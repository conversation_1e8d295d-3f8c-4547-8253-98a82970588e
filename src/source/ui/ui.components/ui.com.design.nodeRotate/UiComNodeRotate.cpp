#include "UiComNodeRotate.h"
#include "core/message/WDMessage.h"

UiComNodeRotate::UiComNodeRotate(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject *parent)
    : QObject(parent)
    , IUiComponent(mainWindow, attrs)
{
    _pRotateDialog      =   new NodeRotateDialog(mWindow().core(), mWindow().widget());
}

UiComNodeRotate::~UiComNodeRotate()
{
    if (_pRotateDialog != nullptr)
    {
        delete _pRotateDialog;
        _pRotateDialog = nullptr;
    }
}


void UiComNodeRotate::onNotice(UiNotice * pNotice)
{
    auto    pCurNode    =   mWindow().core().nodeTree().currentNode();
    int     nType       =   pNotice->type();
    switch (nType)
    {
    case UiNoticeType::UNT_Action:
        {
            UiActionNotice* pActionNotice = static_cast<UiActionNotice*>(pNotice);
            if (pActionNotice->action().is("action.design.node.rotate"))
            {
                if (_pRotateDialog->isHidden())
                    _pRotateDialog->show();
                else
                    _pRotateDialog->activateWindow();
            }
        }
        break;
    default:
        break;
    }
}