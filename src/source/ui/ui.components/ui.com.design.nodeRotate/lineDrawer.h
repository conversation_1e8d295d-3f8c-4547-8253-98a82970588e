#pragma once

#include    "core/scene/WDRenderObject.h"
#include    "core/viewer/primitiveRender/WDTextRender.h"
#include    "core/math/geometric/standardPrimitives/ConeBuilder.h"
#include    "core/geometry/WDMesh.h"

WD_NAMESPACE_BEGIN

// 简单画线，需要给出两个点
class LineDrawer: public WDRenderObject
{
private:
    // 绘制文本
    WDText2DRender          _textRender;
    // 绘制连线使用的材质
    WDMaterialColor::SharedPtr  _lineMat        =   nullptr;
    // 绘制箭头的材质
    WDMaterialColor::SharedPtr  _arrowMat       =   nullptr;
    Color                   _color          =   {128, 64, 0, 255};
    WDMesh                  _lineMesh;
    WDInstance              _lineInst;
    WDInstance              _arrowInst;
    // 线的两端坐标
    std::array<DVec3, 2>    _startAndEnd    =   {DVec3{}, DVec3{}};
    // 线两端的文字
    std::wstring            _startText      =   L"";
    std::wstring            _endText        =   L"";
    // 是否带有箭头
    bool                    _withArrow      =   false;
    // 箭头长度
    float                   _arrowLength    =   9;
    std::shared_ptr<WDMesh> _arrowMesh      =   nullptr;
    FVec3Vector             _arrowBasicPositions;
    FVec3Vector             _arrowBasicNormals;
    // 箭头的pri
    WDPrimitiveSet      _arrowPriSrc;
    bool                    _depthTest      =   false;
    // 是否在场景里定长显示
    bool                    _keepLength     =   false;
    double                  _lineLength     =   30;
    DVec3                   _newEnd         =   {0,0,0};
public:
    LineDrawer(bool withArrow = false);
    /**
     * @brief 设置线条两端坐标
     * @param p1 起点坐标
     * @param p2 终点坐标
    */
    inline void setStartEnd(DVec3 p1, DVec3 p2)
    {
        _startAndEnd[0] = p1;
        _startAndEnd[1] = p2;
    }
    std::array<DVec3, 2> startEnd()
    {
        return _startAndEnd;
    }
    /**
     * @brief 设置线条两端显示文本
     * @param startText 起点文本
     * @param endText 终点文本
    */
    void    setText(const std::string& startText, const std::string& endText)
    {
        //转为wstring
        _startText = stringToWString(startText);
        _endText = stringToWString(endText);
    }
    /**
     * @brief 设置箭头长度
     * @param arrowLen 箭头长度
    */
    void    setArrowLen(float arrowLen)
    {
        _arrowLength = arrowLen;
    }
    /**
     * @brief 设置颜色
     * @param c 颜色
    */
    void    setColor(Color c)
    {
        _color = c;
    }
    /**
     * @brief 设置线的样式
     * @param lineWidth 线宽
     * @param style 线风格：虚线/实线等
     * @param depthTest 是否开启深度测试
    */
    void    setLineStyle(float lineWidth, WDRenderStateLineStipple::Style style, bool depthTest)
    {
        _lineMat    =   WDMaterialColor::MakeShared(Color::white);
        _arrowMat   =   WDMaterialColor::MakeShared(Color::white);

        _lineMat->addStates({
            WDRenderStateLineWidth::MakeShared(lineWidth) // 线宽
            , WDRenderStateLineStipple::Get(style) //虚线样式
            , WDRenderStateDepthTest::MakeShared(depthTest) // 深度测试
            });

        _arrowMat->addStates({WDRenderStateDepthTest::MakeShared(depthTest)});
        _depthTest = depthTest;
    }
    void    setKeepLength(bool keep, double len = 0)
    {
        _keepLength = keep;
        _lineLength = len;
    }
protected:
    virtual void updateAabb(WDContext& context, const WDScene& scene) override;
    virtual void update(WDContext& context, const WDScene&) override;
    virtual void render(WDContext& context, const WDScene&) override;
};


WD_NAMESPACE_END