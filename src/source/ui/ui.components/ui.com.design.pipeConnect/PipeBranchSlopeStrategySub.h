#pragma once

#include "PipeBranchSlopeStrategy.h"

WD_NAMESPACE_BEGIN

/**
* @brief 弯头斜接弯头策略
*/
class PipeBranchSlopeStrategyElboElbo : public PipeBranchSlopeStrategy
{
    WD_DECL_OBJECT(PipeBranchSlopeStrategyElboElbo)
public:
    PipeBranchSlopeStrategyElboElbo();
    ~PipeBranchSlopeStrategyElboElbo();

private:
    virtual bool slopeCalibration(WDNode& slope
        , WDNode& tar
        , const SlopeingPoints& slopePoints
        , const SlopeingPoints& tarPoints) override;
};


/**
* @brief 弯头斜接三通策略
*/
class PipeBranchSlopeStrategyElboTee : public PipeBranchSlopeStrategy
{
    WD_DECL_OBJECT(PipeBranchSlopeStrategyElboTee)
public:
    PipeBranchSlopeStrategyElboTee();
    ~PipeBranchSlopeStrategyElboTee();

private:
    virtual bool slopeCalibration(WDNode& slope
        , WDNode& tar
        , const SlopeingPoints& slopePoints
        , const SlopeingPoints& tarPoints) override;
};


/**
* @brief 三通斜接弯头策略
*/
class PipeBranchSlopeStrategyTeeElbo : public PipeBranchSlopeStrategy
{
    WD_DECL_OBJECT(PipeBranchSlopeStrategyTeeElbo)
public:
    PipeBranchSlopeStrategyTeeElbo();
    ~PipeBranchSlopeStrategyTeeElbo();

private:
    virtual bool slopeCalibration(WDNode& slope
        , WDNode& tar
        , const SlopeingPoints& slopePoints
        , const SlopeingPoints& tarPoints) override;
};


/**
* @brief 三通斜接三通策略
*/
class PipeBranchSlopeStrategyTeeTee : public PipeBranchSlopeStrategy
{
    WD_DECL_OBJECT(PipeBranchSlopeStrategyTeeTee)
public:
    PipeBranchSlopeStrategyTeeTee();
    ~PipeBranchSlopeStrategyTeeTee();

private:
    virtual bool slopeCalibration(WDNode& slope
        , WDNode& tar
        , const SlopeingPoints& slopePoints
        , const SlopeingPoints& tarPoints) override;
};

WD_NAMESPACE_END