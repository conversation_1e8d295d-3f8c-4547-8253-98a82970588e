<?xml version="1.0" encoding="utf-8"?>

<TS language="简体中文">
	<context>
		<name>UiComDesignPipeConnect</name>
		<message>
			<source>PipeBranchConnect</source>
			<translation>分支连接</translation>
		</message>
		<message>
			<source>BranchName</source>
			<translation>分支名称</translation>
		</message>
		<message>
			<source>ConnectObjectType</source>
			<translation>连接对象类型</translation>
		</message>
		<message>
			<source>CurElement</source>
			<translation>当前元素</translation>
		</message>
		<message>
			<source>Apply</source>
			<translation>应用</translation>
		</message>
		<message>
			<source>NodeIsNotPipelineBranch</source>
			<translation>该节点不是管线分支</translation>
		</message>
		<message>
			<source>PickNodeNotMatchConnectType</source>
			<translation>拾取节点对象类型与连接对象类型不匹配</translation>
		</message>
		<message>
			<source>Head</source>
			<translation>头</translation>
		</message>
		<message>
			<source>Tail</source>
			<translation>尾</translation>
		</message>
		<message>
			<source>NOZZ</source>
			<translation>管嘴</translation>
		</message>
		<message>
			<source>TEE</source>
			<translation>三通</translation>
		</message>
		<message>
			<source>OLET</source>
			<translation>支管台</translation>
		</message>
		<message>
			<source>ELBO</source>
			<translation>弯头</translation>
		</message>
		<message>
			<source>REDU</source>
			<translation>异径管</translation>
		</message>
		<message>
			<source>FLAN</source>
			<translation>法兰</translation>
		</message>
		<message>
			<source>Multiway</source>
			<translation>四通</translation>
		</message>
		<message>
			<source>Branch Head</source>
			<translation>分支头</translation>
		</message>
		<message>
			<source>Branch Tail</source>
			<translation>分支尾</translation>
		</message>
		<message>
			<source>First Member</source>
			<translation>分支第一个</translation>
		</message>
		<message>
			<source>Last Member</source>
			<translation>分支最后一个</translation>
		</message>
		<message>
			<source>Name</source>
			<translation>名字</translation>
		</message>
		<message>
			<source>Position</source>
			<translation>位置</translation>
		</message>

		<message>
			<source>PipeComponentConnect</source>
			<translation>管件连接</translation>
		</message>
		<message>
			<source>CurrentSelectNode</source>
			<translation>当前选中节点</translation>
		</message>
		<message>
			<source>ConnectOrder</source>
			<translation>连接顺序</translation>
		</message>
		<message>
			<source>ConnectModel</source>
			<translation>连接模式</translation>
		</message>
		<message>
			<source>FlowDirection</source>
			<translation>流向</translation>
		</message>
		<message>
			<source>Reset</source>
			<translation>重置</translation>
		</message>
		<message>
			<source>Cancel</source>
			<translation>取消</translation>
		</message>
		<message>
			<source>SelectOtherNode</source>
			<translation>当前节点不包含管件数据，请选择其他节点</translation>
		</message>
		<message>
			<source>PipeFittingNotBelongToBranch</source>
			<translation>该管件不属于某个分支</translation>
		</message>
		<message>
			<source>BeforeNotPipeFitting</source>
			<translation>分支下该管件之前没有管件</translation>
		</message>
		<message>
			<source>LaterNotPipeFitting</source>
			<translation>分支下该管件之后没有管件</translation>
		</message>
		<message>
			<source>PipeFittingExitDirection Unreasonable</source>
			<translation>管件出口点方向不合理</translation>
		</message>
		<message>
			<source>BranchDirection Unreasonable</source>
			<translation>分支方向不合理</translation>
		</message>
		<message>
			<source>Connect to Previous</source>
			<translation>连接前一个</translation>
		</message>
		<message>
			<source>Connect to Next</source>
			<translation>连接后一个</translation>
		</message>
		<message>
			<source>Only</source>
			<translation>仅此</translation>
		</message>
		<message>
			<source>and Flow Direction</source>
			<translation>同时流向</translation>
		</message>
		<message>
			<source>and Branch</source>
			<translation>同时分支</translation>
		</message>
		<message>
			<source>and HandWheel Direction</source>
			<translation>同时手轮方向</translation>
		</message>
		<message>
			<source>Disable Cross Branch Connect</source>
			<translation>不允许跨分支连接</translation>
		</message>
		
		<message>
			<source>Head</source>
			<translation>头</translation>
		</message>
		<message>
			<source>Name</source>
			<translation>名称</translation>
		</message>
		<message>
			<source>Ok</source>
			<translation>确认</translation>
		</message>
		<message>
			<source>Cancel</source>
			<translation>取消</translation>
		</message>
		<message>
			<source>CreatePipeline</source>
			<translation>创建管道</translation>
		</message>
		<message>
			<source>Grade</source>
			<translation>等级</translation>
		</message>
		<message>
			<source>CE</source>
			<translation>定位等级</translation>
		</message>
		<message>
			<source>HeatPreser</source>
			<translation>保温</translation>
		</message>
		<message>
			<source>Tracking</source>
			<translation>伴热</translation>
		</message>
		<message>
			<source>PipePositionType</source>
			<translation>位置连接</translation>
		</message>
		<message>
			<source>Position</source>
			<translation>位置</translation>
		</message>
		<message>
			<source>X</source>
			<translation>X</translation>
		</message>
		<message>
			<source>Y</source>
			<translation>Y</translation>
		</message>
		<message>
			<source>Z</source>
			<translation>Z</translation>
		</message>
		<message>
			<source>Direction</source>
			<translation>朝   向</translation>
		</message>
		<message>
			<source>Diameter</source>
			<translation>公称直径</translation>
		</message>
		<message>
			<source>CType</source>
			<translation>连接形式</translation>
		</message>
		<message>
			<source>Pick</source>
			<translation>捕捉</translation>
		</message>
		<message>
			<source>PipeNameType</source>
			<translation>名称连接</translation>
		</message>
		<message>
			<source>Branch_Head</source>
			<translation>分支头</translation>
		</message>
		<message>
			<source>Branch_Tail</source>
			<translation>分支尾</translation>
		</message>
		<message>
			<source>Current node is not BRAN type</source>
			<translation>非分支节点!</translation>
		</message>
		<message>
			<source>Current bran node exist pipe coms,unsurpport bran slope</source>
			<translation>当前分支节点已存在管件，不支持斜接放坡!</translation>
		</message>
	</context>
	
	<context>
		<name>BranchDistanceDialog</name>
		<message>
		  <source>BranchDistance</source>
		  <translation>坐标距离</translation>
		</message>
		<message>
		  <source>Distance</source>
		  <translation>距离</translation>
		</message>
		<message>
		  <source>Ok</source>
		  <translation>确定</translation>
		</message>
		<message>
		  <source>Cancel</source>
		  <translation>取消</translation>
		</message>
		<message>
		  <source>Head</source>
		  <translation>头</translation>
		</message>
		<message>
		  <source>Tail</source>
		  <translation>尾</translation>
		</message>
		<message>
		  <source>CE</source>
		  <translation>当前节点</translation>
		</message>
	</context>

	<context>
		<name>PipeBranchConnectStrategy</name>
		<message>
		  <source>BranchNotPipeFitting</source>
		  <translation>分支没有管件</translation>
		</message>
		<message>
		  <source>BranchHeadFirstPipe</source>
		  <translation>分支头只允许分支下第一个管件</translation>
		</message>
		<message>
		  <source>BranchTailLastPipe</source>
		  <translation>分支尾只允许分支下最后一个管件</translation>
		</message>
		<message>
		  <source>NotConnectSamePipe</source>
		  <translation>请勿重复连接同一个管件</translation>
		</message>
		<message>
		  <source>CurPortConnectOtherPipe</source>
		  <translation>当前连接端口已连接其他管件</translation>
		</message>
		<message>
		  <source>BranchHeadSelectFirst</source>
		  <translation>分支头只能选择第一个</translation>
		</message>
		<message>
		  <source>BranchTailSelectLast</source>
		  <translation>分支尾只能选择最后一个</translation>
		</message>
	  </context>

	<context>
		<name>ErrorUiComDesignPipeConnect</name>
		<message>
			<source>Fail to slope!</source>
			<translation>放坡失败!</translation>
		</message>
		<message>
			<source>Both ends of the pipeComponent can connect!</source>
			<translation>管件两端都不满足放坡!</translation>
		</message>
		<message>
			<source>Both ends of the pipeComponent can not connect!</source>
			<translation>管件两端都满足放坡!</translation>
		</message>
		<message>
			<source>PipeComponent not at HeadTail!</source>
			<translation>管件不在分支头尾!</translation>
		</message>
		<message>
			<source>Branch'head is connected to another object!</source>
			<translation>分支头已连接另外的对象!</translation>
		</message>
		<message>
			<source>Branch'tail is connected to another object!</source>
			<translation>分支尾已连接另外的对象!</translation>
		</message>
		<message>
			<source>Target object is connected other object!</source>
			<translation>目标对象已连接另外的对象!</translation>
		</message>
		<message>
			<source>Flow is not support!</source>
			<translation>流向不支持!</translation>
		</message>
		<message>
			<source>First component are not at either end of the branch!</source>
			<translation>第一个管件不在分支两端!</translation>
		</message>
		<message>
			<source>Second component are not at either end of the branch!</source>
			<translation>第二个管件不在分支两端!</translation>
		</message>
		<message>
			<source>Error from select!</source>
			<translation>错误的选择!</translation>
		</message>
	</context>

	<context>
		<name>ErrorBranchDistance</name>
		<message>
			<source>Current branch is null!</source>
			<translation>当前分支为空!</translation>
		</message>
		<message>
			<source>Current branch'data is null!</source>
			<translation>当前分支业务数据为空!</translation>
		</message>
	</context>

	<context>
		<name>ErrorPromptPipeBranchConnect</name>
		<message>
			<source>NodeIsNotPipelineBranch</source>
			<translation>该节点不是管线分支</translation>
		</message>
		<message>
			<source>Disable Cross Branch Connect</source>
			<translation>不允许跨分支连接</translation>
		</message>
		<message>
			<source>PickNodeNotMatchConnectType</source>
			<translation>拾取节点对象类型与连接对象类型不匹配</translation>
		</message>
		<message>
			<source>Selected branch is null!</source>
			<translation>无法确定当前选中分支</translation>
		</message>
		<message>
			<source>Current type only support at the head or tail of the branch!</source>
			<translation>当前连接类型管件只支持在分支头尾</translation>
		</message>
		<message>
			<source>Current type only support at the head of the branch!</source>
			<translation>当前连接类型管件只支持在分支头</translation>
		</message>
		<message>
			<source>Current type only support at the tail of the branch!</source>
			<translation>当前连接类型管件只支持在分支尾</translation>
		</message>
		<message>
			<source>Connect object is connected to another object!</source>
			<translation>连接对象已连接到其他对象!</translation>
		</message>
		<message>
			<source>Disconnected component only allow connect in branch!</source>
			<translation>非连接管件只允许在分支内连接!</translation>
		</message>
		<message>
			<source>Head connect to head?</source>
			<translation>确认将分支头连接到目标分支头吗?这有可能是不规范的操作</translation>
		</message>
		<message>
			<source>Tail connect to tail?</source>
			<translation>确认将分支尾连接到目标分支尾吗?这有可能是不规范的操作</translation>
		</message>
		<message>
			<source>Not is same branch!</source>
			<translation>目标分支与当前分支相同!</translation>
		</message>
		<message>
			<source>Flow is not support!</source>
			<translation>流向不支持!</translation>
		</message>
	</context>

	<context>
		<name>ErrorPromptPipeComponentConnect</name>
		<message>
			<source>SelectOtherNode</source>
			<translation>当前节点不包含管件数据，请选择其他节点</translation>
		</message>
		<message>
			<source>PipeFittingNotBelongToBranch</source>
			<translation>该管件不属于某个分支</translation>
		</message>
		<message>
			<source>BeforeNotPipeFitting</source>
			<translation>分支下该管件之前没有管件</translation>
		</message>
		<message>
			<source>LaterNotPipeFitting</source>
			<translation>分支下该管件之后没有管件</translation>
		</message>
		<message>
			<source>ConnectTypeNotMatch DataMistake</source>
			<translation>连接类型不匹配，数据错误!</translation>
		</message>
		<message>
			<source>ConnectTypeNotMatch FrontInsertion</source>
			<translation>连接类型不匹配，在距前一个元件出口方向的100mm处插入当前元件!</translation>
		</message>
		<message>
			<source>PipeFittingExitDirection Unreasonable</source>
			<translation>管件出口点方向不合理</translation>
		</message>
		<message>
			<source>BranchDirection Unreasonable</source>
			<translation>分支方向不合理</translation>
		</message>
	</context>

	<context>
		<name>ErrorPipePositionTypeDialog</name>
		<message>
			<source>Input direction is error!</source>
			<translation>输入方向错误!</translation>
		</message>
	</context>
</TS> 