#pragma once

#include "core/WDCore.h"
#include "core/node/WDNode.h"
#include "core/nodeTree/WDNodeTree.h"
#include "core/common/WDMutex.hpp"

WD_NAMESPACE_BEGIN

/**
* @brief 斜接关键点数据
*/
struct SlopeingPoints
{
    // 参与关键点
    std::optional<DKeyPoint> joinPoint   =  std::nullopt;
    // 另一个关键点
    std::optional<DKeyPoint> otherPoint  =  std::nullopt;
    // 斜接方向与流向是否一致(用以判断管件位于分支头尾)
    bool                slopeingDir =   true;
};

/**
* @brief 管线分支斜接策略
*/
class PipeBranchSlopeStrategy : public WDObject
{
    WD_DECL_OBJECT(PipeBranchSlopeStrategy)

public:
    PipeBranchSlopeStrategy();
    virtual ~PipeBranchSlopeStrategy();

public:
    /**
    * @brief 分支斜接
    * @param slope 斜接管件
    * @param tar 目标管件
    */
    bool                        slope(WDNode& slope, WDNode& tar);

public:
    /**
    * @brief 提取错误信息
    */
    inline const std::string&   takeError() const
    {
        return _error;
    }

protected:
    /**
    * @brief 获取斜接关键点数据
    * @param slope 斜接管件
    * @param tar 目标管件
    */
    std::array<SlopeingPoints, 2> slopeKeyPoints(WDNode& slope, WDNode& tar);

    /**
    * @brief 斜接校准
    * @param slope 斜接对象
    * @param tar 目标对象
    * @param slopePoints 斜接对象关键点数据
    * @param tarPoints 目标对象关键点数据
    * @return 是否允许斜接
    */
    virtual bool    slopeCalibration(WDNode& slope
        , WDNode& tar
        , const SlopeingPoints& slopePoints
        , const SlopeingPoints& tarPoints) = 0;

    /**
    * @brief 收集错误信息
    */
    inline void     error(const std::string& info)
    {
        _error = info;
    }

private:
    // 错误信息
    std::string _error;
};


/**
* @brief 管线分支斜接策略管理
*/
class PipeBranchSlopeStrategyMgr
{
    // 分支斜接策略名称索引，后续添加策略需同步更新
    static constexpr const char* ElboSlopeElbo = "ElboSlopeElbo";
    static constexpr const char* ElboSlopeTee = "ElboSlopeTee";
    static constexpr const char* TeeSlopeElbo = "TeeSlopeElbo";
    static constexpr const char* TeeSlopeTee = "TeeSlopeTee";
public:
    /**
    * @brief 分支斜接
    * @param slope 斜接对象
    * @param tar 目标对象
    */
    bool slope(WDNode& slope, WDNode& tar, std::string& error);
    /**
    * @brief 无管件分支 斜接放坡
    */
    void branchSlopeByHeadTailPos(WDNode& branch);

public:
    /**
    * @brief 获取单实例对象
    */
    static PipeBranchSlopeStrategyMgr* GetInstance();
    /**
    * @brief 释放单实例，进程退出时调用
    */
    static void DeleteInstance();

private:
    PipeBranchSlopeStrategyMgr();
    ~PipeBranchSlopeStrategyMgr();
    PipeBranchSlopeStrategyMgr(const PipeBranchSlopeStrategyMgr& right);

private:
    /**
    * @brief 初始化分支斜接策略
    */
    void initStrategy();
    /**
    * @brief 添加分支斜接策略
    */
    bool addStrategy(const std::string& name, PipeBranchSlopeStrategy::SharedPtr strategy);

private:
    static PipeBranchSlopeStrategyMgr*                          _Instance;
    static WDMutex                                              _Mutex;

private:
    // 分支斜接策略map
    std::map<std::string, PipeBranchSlopeStrategy::SharedPtr>   _mapStrategy;
};

WD_NAMESPACE_END
