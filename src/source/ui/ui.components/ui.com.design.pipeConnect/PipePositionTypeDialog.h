#pragma once

#include <QDialog>
#include "ui_PipePositionTypeDialog.h"
#include "core/WDCore.h"
#include "core/node/WDNode.h"

#include "viewer/capturePositioning/WDCapturePositioning.h"
#include "PipeBranchConnectStrategy.h"

class PipePositionTypeDialog : public QDialog
    , public WD::WDCapturePositioningMonitor
{
    Q_OBJECT

public:
    PipePositionTypeDialog(WD::WDCore& core, QWidget *parent = Q_NULLPTR);
    ~PipePositionTypeDialog();

signals:
    /**
    * @brief 确认 按下信号
    */
    void sigOKClicked();

public:
    /**
    * @brief 获取位置
    */
    inline WD::DVec3                position() const
    {
        return WD::DVec3(ui.doubleSpinBoxX->value(), ui.doubleSpinBoxY->value(), ui.doubleSpinBoxZ->value());
    }
    inline WD::DVec3                direction() const
    {
        return _dir;
    }
    /**
    * @brief 获取管径
    */
    inline std::string              bore() const
    {
        return ui.comboBoxDiameter->currentText().toUtf8().data();
    }
    /**
    * @brief 获取连接形式
    */
    inline std::string              cType() const
    {
        return ui.comboBoxCType->currentText().toUtf8().data();
    }
    /**
    * @brief 重置数据
    */
    inline void                     reset()
    {
        // 重置position
        ui.doubleSpinBoxX->setValue(0.0);
        ui.doubleSpinBoxY->setValue(0.0);
        ui.doubleSpinBoxZ->setValue(0.0);
    }

public:
    /**
    * @brief 更新界面数据
    * @param branch 分支对象
    * @param headTail 分支连接头尾类型
    * @brief 更新界面数据
    */
    void update(WD::WDNode& branch, WD::PipeBranchConnectType headTailType);

private slots:
    /**
    * @brief 鼠标获取关键点 按下通知响应
    */
    void slotPickClicked();
    /**
    * @brief 确认 按下通知响应
    */
    void slotOKClicked();
    /**
    * @brief 取消 按下通知响应
    */
    void slotCancelClicked();
    /**
    * @brief 位置 编辑完成通知响应
    */
    void slotPositionEditingFinished();

private:
    virtual void onResult(const WD::WDCapturePositioningResult& result
        , bool& existFlag
        , const WD::WDCapturePositioning& sender) override;
    virtual void onDeactived(const WD::WDCapturePositioning& sender) override;

private:
    /**
    * @brief 初始化公称直径列表
    */
    void        initBoreItems(WD::WDNode::SharedPtr pSPEC);
    /**
    * @brief 初始化连接形式列表，目前直接按现有树结构取节点数据
    */
    void        initCType();
    /**
    * @brief 从SELE节点下获取SPCO节点
    */
    WD::WDNode::SharedPtr pStube(WD::WDNode::SharedPtr pSELE);
    /**
    * @brief 设置界面位置值
    */
    void        setPosition(const WD::Vec3& position);
    /**
    * @brief 设置当前公称直径
    */
    void        setCurBore(const std::string& bore);
    /**
    * @brief 设置当前连接形式
    */
    void        setCurCType(const std::string& cType);
    /**
    * @brief 分支头更新界面
    */
    void        updateHead(WD::WDNode& branch);
    /**
    * @brief 分支尾更新界面
    */
    void        updateTail(WD::WDNode& branch);
    /**
    * @brief 校验输入内容
    */
    bool        checkInput();
    /**
    * @brief 分支头计算方向
    */
    WD::Vec3    caclDirectionH();
    /**
    * @brief 分支尾计算方向
    */
    WD::Vec3    caclDirectionT();

private:
    /**
    * @brief 界面翻译
    */
    void        retranslateUi();

private:
    Ui::PipePositionTypeDialog          ui;
    WD::WDCore&                         _core;

    // 管线分支
    WD::WDNode::WeakPtr                 _pBranch;
    // 管线分支连接方式
    WD::PipeBranchConnectType           _headTailType;
    // 方向
    WD::DVec3                           _dir;
};
