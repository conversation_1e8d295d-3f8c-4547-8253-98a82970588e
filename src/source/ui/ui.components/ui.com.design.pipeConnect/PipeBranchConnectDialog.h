#pragma once

#include <QDialog>
#include "ui_PipeBranchConnectDialog.h"
#include "WDCore.h"
#include "PipeNameTypeDialog.h"
#include "PipePositionTypeDialog.h"
#include "common/WDTDelegate.hpp"
#include "PipeBranchConnectStrategySub.h"
#include "viewer/capturePositioning/WDCapturePositioning.h"

class PipeBranchConnectDialog : public QDialog
    , public WD::WDCapturePositioningMonitor
{
    Q_OBJECT

public:
    using MapStrategy = std::map<WD::BranConObjType, WD::PipeBranchConnectStrategy::SharedPtr>;

public:
    PipeBranchConnectDialog(WD::WDCore& core, QWidget *parent = Q_NULLPTR);
    ~PipeBranchConnectDialog();

public:
    /**
     * @brief 更新分支连接界面
     * @param curNode 使用该节点信息更新界面
     * @return 当前界面处于show状态时，false：界面信息不更新;处于隐藏状态时，false：不显示分支连接界面
    */
    bool updateWidget(WD::WDNode::SharedPtr curNode = nullptr);

protected:
    virtual void showEvent(QShowEvent* event) override;
    virtual void hideEvent(QHideEvent* event)override;

private slots:
    /**
    * @brief 连接对象类型下拉激活槽函数
    */
    void slotConnectObjTypeChanged(int index);
    /**
    * @brief 当前元素按下槽函数
    */
    void slotCEClicked();
    /**
    * @brief 应用按下槽函数
    */
    void slotAppClicked();
    /**
    * @brief 位置窗口OK按下槽函数
    */
    void onPositionOKClicked();

private:
    virtual void onResult(const WD::WDCapturePositioningResult& result
        , bool& existFlag
        , const WD::WDCapturePositioning& sender) override;
    virtual void onDeactived(const WD::WDCapturePositioning& sender) override;
    virtual bool onNodeFilter(WD::WDNode& node, const WD::WDCapturePositioning& sender)override;
private:
    /**
    * @brief 初始化
    */
    void init();
    /**
    * @brief 分支连接
    * @param connectObj 连接对象
    */
    void BranchConnect(WD::WDNode::SharedPtr connectObj = nullptr);
    /**
    * @brief 分支连接
    * @param position 目标位置
    */
    void BranchPosConnect();
    /**
     * @brief 获取连接对象类型
     * @return 
    */
    WD::BranConObjType getConObjType();
    /**
     * @brief 获取连接部位
    */
    WD::PipeBranchConnectType getConPart();
    /**
     * @brief 界面翻译
    */
    void retranslateUi();

private:
    Ui::PipeBranchConnectDialog         ui;
    WD::WDCore&                         _core;
    // 名称类型窗口
    PipeNameTypeDialog*                 _nameTypeDialog;
    // 位置类型窗口
    PipePositionTypeDialog*             _positionTypeDialog;
    // 当前需要进行连接操作的分支节点
    WD::WDNode::WeakPtr                 _pCurBranch;
    // 策略map
    MapStrategy                         _mapStrategy;
};
