#include "UiComDesignPipeConnect.h"
#include "core/WDTranslate.h"
#include "core/nodeTree/WDNodeTree.h"
#include "core/math/DirectionParser.h"
#include "core/viewer/WDViewer.h"
#include "core/message/WDMessage.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/businessModule/design/pipeWork/WDBMDPipeUtils.h"
#include "PipeBranchSlopeStrategy.h"
#include "core/businessModule/WDBMClaimMgr.h"

UiComDesignPipeConnect::UiComDesignPipeConnect(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject*parent)
    : QObject(parent)
    , IUiComponent(mainWindow, attrs)
{
    _pipeBranchConnectDialog    =   new PipeBranchConnectDialog(mainWindow.core(), mWindow().widget());
    _pipeComponentConnectDialog =   new PipeComponentConnectDialog(mWindow().core(), mWindow().widget());
    _branchDistanceDialog       =   new BranchDistanceDialog(mWindow().core(), mWindow().widget());
    
}
UiComDesignPipeConnect::~UiComDesignPipeConnect()
{
    if (_pipeBranchConnectDialog != nullptr)
    {
        delete _pipeBranchConnectDialog;
        _pipeBranchConnectDialog = nullptr;
    }
    if (_pipeComponentConnectDialog != nullptr)
    {
        delete _pipeComponentConnectDialog;
        _pipeComponentConnectDialog = nullptr;
    }
}

void UiComDesignPipeConnect::onNotice(UiNotice * pNotice)
{
    auto& core = mWindow().core();
    int nType = pNotice->type();
    switch (nType)
    {
    case UiNoticeType::UNT_Action:
    {
        UiActionNotice* pActionNotice = static_cast<UiActionNotice*>(pNotice);
        if (pActionNotice->action().is("action.design.pipe.branch.connect"))
        {
            if (_pipeBranchConnectDialog->updateWidget(mWindow().core().nodeTree().currentNode()))
            {
                if (_pipeBranchConnectDialog->isHidden())
                {
                    _pipeBranchConnectDialog->show();
                }
                else
                    _pipeBranchConnectDialog->activateWindow();
            }
            else
            {
                _pipeBranchConnectDialog->accept();
            }
        }
        else if (pActionNotice->action().is("action.design.pipe.component.connect"))
        {
            if (_pipeComponentConnectDialog->updateWidget(mWindow().core().nodeTree().currentNode()))
            {
                if (_pipeComponentConnectDialog->isHidden())
                    _pipeComponentConnectDialog->show();
                else
                    _pipeComponentConnectDialog->activateWindow();
            }
            else
            {
                _pipeComponentConnectDialog->accept();
            }
        }
        else if (pActionNotice->action().is("action.design.pipe.branch.slope"))
        {
            this->actBranchSlope();
            core.needRepaint();
        }
        else if (pActionNotice->action().is("action.design.pipe.branches.slope"))
        {
            this->actBranchesSlope();
            core.needRepaint();
        }
        else if (pActionNotice->action().is("action.design.pipe.branch.headTail.pos"))
        {
            if (!pActionNotice->action().checked())
            {
                auto pBranch = WD::Core().nodeTree().currentNode();
                if (pBranch == nullptr)
                    return;

                if (!pBranch->isType("BRAN"))
                {
                    WD_WARN_T("UiComDesignPipeConnect", "Current node is not BRAN type");
                    return;
                }

                if (_branchDistanceDialog->isHidden())
                    _branchDistanceDialog->show();
                else
                    _branchDistanceDialog->activateWindow();
            }
            else
            {
                _branchDistanceDialog->hide();
            }
        }
    }
    break;
    default:
        break;
    }
}

void UiComDesignPipeConnect::onResult(const WD::WDCapturePositioningResult& result
    , bool& existFlag
    , const WD::WDCapturePositioning& sender)
{
    WDUnused(sender);
    WD::WDNode::SharedPtr pNode = result.node.lock();
    if (pNode == nullptr)
        return ;
    // 申领节点
    bool bCancelMd = false;
    if (!WD::Core().getBMDesign().claimMgr().checkUpdate(pNode
        , {"Position WRT World", "Orientation WRT World"}
        , bCancelMd))
        return ;
    if (bCancelMd)
        return ;

    _slopeComs.push_back(pNode);

    if (_slopeComs.size() == 2)
    {
        //设置捕捉退出
        existFlag = true;
        std::string error;
        if (!WD::PipeBranchSlopeStrategyMgr::GetInstance()->slope(*_slopeComs.front().lock(), *_slopeComs.back().lock(), error))
        {
            std::string errorHead   =   WD::WDTs("ErrorUiComDesignPipeConnect", "Fail to slope!");
            std::string transError  =   WD::WDTs("ErrorUiComDesignPipeConnect", error);
            WD_ERROR(errorHead + "\n" + transError);
        }
    }
}
void UiComDesignPipeConnect::onDeactived(const WD::WDCapturePositioning& sender)
{
    WDUnused(sender);
    _slopeComs.clear();
}

void UiComDesignPipeConnect::actBranchSlope()
{
    auto                pBranch     =   WD::Core().nodeTree().currentNode();
    if (pBranch == nullptr)
        return ;
    // 申领对象
    bool bCancelMd = false;
    if (!WD::Core().getBMDesign().claimMgr().checkUpdate(pBranch, { "Hdirection" , "Tdirection" }, bCancelMd))
        return;
    if (bCancelMd)
        return;

    if (!pBranch->isType("BRAN"))
    {
        WD_WARN_T("UiComDesignPipeConnect", "Current node is not BRAN type");
        return;
    }

    // 判断 管线分支是否有管件 有管件则不进行放坡
    WD::WDNode::Nodes   pipecoms    =   WD::WDBMDPipeUtils::PipeComponents(pBranch);
    if (!pipecoms.empty())
    {
        WD_WARN_T("UiComDesignPipeConnect", "Current bran node exist pipe coms,unsurpport bran slope");
        return;
    }

    // 校验 管线分支头尾 坐标，方向，管径 是否满足放坡条件
    const auto hPos     =   pBranch->getAttribute("Hposition").toDVec3();
    const auto tPos     =   pBranch->getAttribute("Tposition").toDVec3();
    std::string hBore   =   pBranch->getAttribute("Hbore").toString();
    std::string tBore   =   pBranch->getAttribute("Tbore").toString();
    if (hPos == tPos || hBore.empty() || tBore.empty() || hBore != tBore)
        return ;

    // 计算 斜接放坡方向
    WD::DVec3 slopeDir  =   (tPos - hPos).normalized();
    WD::DVec3 tHDir      =   slopeDir;
    WD::DVec3 tTDir      =   -slopeDir;
    // 设置分支头尾方向, 并更新分支连接
    pBranch->setAttribute("Hdirection", tHDir);
    pBranch->setAttribute("Tdirection", tTDir);
    // 更新分支连接
    pBranch->triggerUpdate(true);
}
void UiComDesignPipeConnect::actBranchesSlope()
{
    //激活捕捉工具
    auto& tool = WD::Core().viewer().capturePositioning();
    WD::WDCapturePositioningParam param;
    param.pMonitor = this;
    WD::WDCapturePositioningOption option(WD::WDCapturePositioningType::CPT_Element);
    tool.activate(param, option);
}