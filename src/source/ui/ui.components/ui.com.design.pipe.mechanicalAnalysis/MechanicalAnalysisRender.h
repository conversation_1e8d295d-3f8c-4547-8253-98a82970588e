#pragma once
#include "core/scene/WDRenderObject.h"
#include "MechanicalAnalysisGroupMgr.h"
#include "core/viewer/primitiveRender/WDTextRender.h"
#include "core/viewer/primitiveRender/WDLineRender.h"
#include "core/geometry/standardPrimitives/WDGeometryBox.h"
#include "core/geometry/WDGeometryPolyhedron.h"

WD_NAMESPACE_BEGIN
class MechanicalAnalysisRender : public WDRenderObject
{
public:
    MechanicalAnalysisGroup* pGroup;
    bool bNeedUpdate = false;
    // 是否需要绘制
    bool bPtsNumberVisible = true;
    bool bUnitNumberVisible = true;
    bool bSupportNumberVisible = true;
private:
    // 字体
    WDText2DRender _ptsTextRender;
    WDText2DRender _unitTextRender;
    WDText2DRender _supportTextRender;
    // 边界信息-> 支架点需要绘制边界,且不同截面下的边界大小不同
    WDGeometryBox::SharedPtr _pGeo;
    // 实例
    WDInstances _insts;

    // 几何体材质(phong)带法线
    WDMaterialPhong _material;

    WDRenderStateDepthTest::SharedPtr _pStateDepthTestDisabled;

    WDLineRender _lineRender;
    WDLineRender::Param _lineParam;
public:
    MechanicalAnalysisRender();
    virtual ~MechanicalAnalysisRender();
public:
    void clear()
    {
        _insts.clear();
        _ptsTextRender.reset();
        _unitTextRender.reset();
        _supportTextRender.reset();
        _lineRender.reset();
    }
public:
    void setLines(const FVec3Vector& lines)
    {
        _lineRender.reset();

        _lineRender.addLineSeg(lines, _lineParam);
    }
    virtual void updateAabb(WDContext& context, const WDScene& scene) override;
    virtual void update(WDContext& context, const WDScene& scene) override;
    virtual void render(WDContext& context, const WDScene& scene) override;
};

WD_NAMESPACE_END