#pragma once
#include <QDialog>
#include "ui_AutoNumberConfigDialog.h"
#include "MechanicalAnalysisGroupMgr.h"

WD_NAMESPACE_BEGIN

class WDCore;
class AutoNumberConfigDialog
    : public QDialog
{
    Q_OBJECT
public:
    AutoNumberConfig configs;
private:

    Ui::AutoNumberConfigDialog ui;
    WD::WDCore& _core;
public:
    AutoNumberConfigDialog(WD::WDCore& core, QWidget *parent = nullptr);
    virtual~AutoNumberConfigDialog();
signals:
    // 配置信息改变信号
    void sigConfigChanged();
public slots:
    void slotPushButtonImportClicked();
protected:
    void showEvent(QShowEvent *) override;
    void hideEvent(QHideEvent *) override;
private:
    void saveData();
    void updateDialog();
    void retranslateUi();
};

WD_NAMESPACE_END