#pragma once
#include <QDialog>
#include "ui_ListDialog.h"
#include "MechanicalAnalysisGroupMgr.h"

class ListDialog
    : public QDialog
{
    Q_OBJECT
public:
    enum Type
    {
        Point,
        Unit,
        Constraint,
        Section
    };
    WD::MechanicalAnalysisGroup* pCurrentGroup;
signals:
    void sigSectionNeedUpdate();
    void sigNumberChanged();
    void sigDialogShow();
    void sigDialogHide();
private:
    Ui::ListDialog ui;
public:
    ListDialog(WD::MechanicalAnalysisGroup* pCurrentGroup = nullptr, QWidget *parent = nullptr);
    virtual~ListDialog();
public slots:
    void updateDialog();
private slots:
    void slotPushButtonApplyClicked();
    void slotPushButtonReAutoNumberClicked();
    void slotTableWidgetItemClicked(QTableWidgetItem* item);
protected:
    void showEvent(QShowEvent *) override;
    void hideEvent(QHideEvent *) override;
private:
    void retranslateUi();
};