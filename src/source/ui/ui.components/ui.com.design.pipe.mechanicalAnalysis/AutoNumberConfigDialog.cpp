#include "AutoNumberConfigDialog.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "core/message/WDMessage.h"
#include "core/WDCore.h"

WD_NAMESPACE_USE

AutoNumberConfigDialog::AutoNumberConfigDialog(WD::WDCore& core, QWidget *parent)
    : _core(core), QDialog(parent)
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));

    auto* pValidator = new QRegExpValidator(QRegExp("[a-zA-Z_]*"));
    ui.lineEditPointSuffix->setValidator(pValidator);
    ui.lineEditSupportSuffix->setValidator(pValidator);

    connect(ui.lineEditPointSuffix, &QLineEdit::editingFinished, [&]()
    {
        std::string text = ui.lineEditPointSuffix->text().toLocal8Bit().data();
        for (auto& each : text)
            each = std::toupper(each);
        ui.lineEditPointSuffix->setText(text.c_str());
    });
    connect(ui.lineEditSupportSuffix, &QLineEdit::editingFinished, [&]()
    {
        std::string text = ui.lineEditSupportSuffix->text().toLocal8Bit().data();
        for (auto& each : text)
            each = std::toupper(each);
        ui.lineEditSupportSuffix->setText(text.c_str());
    });
    this->retranslateUi();
    updateDialog();

    connect(ui.pushButtonApply, &QPushButton::clicked, [&]()
    {
        saveData();
        emit sigConfigChanged();
    });
}
AutoNumberConfigDialog::~AutoNumberConfigDialog()
{

}

void AutoNumberConfigDialog::slotPushButtonImportClicked()
{

}

void AutoNumberConfigDialog::showEvent(QShowEvent *)
{
    updateDialog();
}
void AutoNumberConfigDialog::hideEvent(QHideEvent *)
{
}

void AutoNumberConfigDialog::saveData()
{
    configs.nodeTypeConfig.startNumber      =   ui.spinBoxPointNumberStart->value();
    configs.nodeTypeConfig.stepNumber       =   ui.spinBoxPointNumberStep->value();
    configs.nodeTypeConfig.suffix           =   ui.lineEditPointSuffix->text().toLocal8Bit().data();
    
    configs.unitTypeConfig.startNumber      =   ui.spinBoxUnitNumberStart->value();
    configs.unitTypeConfig.stepNumber       =   ui.spinBoxUnitNumberStep->value();
    
    configs.sectionTypeConfig.startNumber   =   ui.spinBoxSectionNumberStart->value();
    configs.sectionTypeConfig.stepNumber    =   ui.spinBoxSectionNumberStep->value();
    
    configs.supportTypeConfig.startNumber   =   ui.spinBoxSupportNumberStart->value();
    configs.supportTypeConfig.stepNumber    =   ui.spinBoxSupportNumberStep->value();
    configs.supportTypeConfig.suffix        =   ui.lineEditSupportSuffix->text().toLocal8Bit().data();
}
void AutoNumberConfigDialog::updateDialog()
{
    ui.spinBoxPointNumberStart->setValue(configs.nodeTypeConfig.startNumber);
    ui.spinBoxPointNumberStep->setValue(configs.nodeTypeConfig.stepNumber);
    ui.lineEditPointSuffix->setText(configs.nodeTypeConfig.suffix.c_str());

    ui.spinBoxUnitNumberStart->setValue(configs.unitTypeConfig.startNumber);
    ui.spinBoxUnitNumberStep->setValue(configs.unitTypeConfig.stepNumber);

    ui.spinBoxSectionNumberStart->setValue(configs.sectionTypeConfig.startNumber);
    ui.spinBoxSectionNumberStep->setValue(configs.sectionTypeConfig.stepNumber);

    ui.spinBoxSupportNumberStart->setValue(configs.supportTypeConfig.startNumber);
    ui.spinBoxSupportNumberStep->setValue(configs.supportTypeConfig.stepNumber);
    ui.lineEditSupportSuffix->setText(configs.supportTypeConfig.suffix.c_str());
}

void AutoNumberConfigDialog::retranslateUi()
{
    Trs("StressCalculationUnitDialog"
        , static_cast<QDialog*>(this)
        , ui.groupBoxPoint
        , ui.groupBoxSection
        , ui.groupBoxSupport
        , ui.groupBoxUnit
        , ui.pushButtonApply
        , ui.labelPointNumberStart
        , ui.labelPointNumberStep
        , ui.labelPointSuffix
        , ui.labelSectionNumberStart
        , ui.labelSectionNumberStep
        , ui.labelSupportNumberStart
        , ui.labelSupportNumberStep
        , ui.labelSupportSuffix
        , ui.labelUnitNumberStart
        , ui.labelUnitNumberStep
    );
}