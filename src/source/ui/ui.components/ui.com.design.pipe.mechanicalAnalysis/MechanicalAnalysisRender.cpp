#include "MechanicalAnalysisRender.h"
#include "core/material/WDDrawHelpter.h"
#include <codecvt>
constexpr static const float boundaryThickness = 10.0;
WD_NAMESPACE_USE
MechanicalAnalysisRender::MechanicalAnalysisRender()
    : WDRenderObject(WDRenderLayer::RL_Scene)
{
    pGroup = nullptr;
   _ptsTextRender.setRenderType(WDText2DRender::RT_BillBoard);
   _unitTextRender.setRenderType(WDText2DRender::RT_BillBoard);
   _supportTextRender.setRenderType(WDText2DRender::RT_BillBoard);
    
    _pGeo = WD::WDGeometryBox::MakeShared(1.0f, 1.0f, 1.0f);

    _material.addState(WDRenderStateCullFace::MakeShared(true));
    _lineParam.lineWidth = 5.0f;
    _lineParam.color = Color::red;
}
MechanicalAnalysisRender::~MechanicalAnalysisRender()
{
}

void MechanicalAnalysisRender::updateAabb(WDContext& , const WDScene& )
{

}
void MechanicalAnalysisRender::update(WDContext&, const WDScene&)
{
    if (!bNeedUpdate)
        return;
    // 先清除已有的数据
    _ptsTextRender.reset();
    _unitTextRender.reset();
    _supportTextRender.reset();
    _insts.clear();
    // 管道组为空,不生成数据
    if (pGroup == nullptr)
        return;

    wchar_t textBuf[1024] = { 0 };
    std::wstring_convert<std::codecvt_utf8_utf16<wchar_t>> con;

    int index = 0;
    const auto& pts = pGroup->pts();
    for (auto& pEach : pts)
    {
        if (pEach == nullptr)
            continue;
        auto pPoint = dynamic_cast<MAPoint*>(pEach);
        if (pPoint == nullptr)
            continue;
        if (pPoint->isSupportPoint || !pPoint->bVisible)
            continue;
        auto pt = FVec3(pPoint->position().data);
        swprintf(textBuf, sizeof(textBuf) / sizeof(textBuf[0]), L"%s", con.from_bytes(pPoint->name()).c_str());
        _ptsTextRender.add(textBuf, pt, Color::purple);
    }

    const auto& units = pGroup->units();
    for (auto& unit : units)
    {
        if (unit == nullptr)
            continue;
        auto pt = FVec3(unit->position().data);
        swprintf(textBuf, sizeof(textBuf) / sizeof(textBuf[0]), L"%s", con.from_bytes(unit->name()).c_str());
        _unitTextRender.add(textBuf, pt, Color::blue);
    }

    const auto& supports = pGroup->supports();
    for (auto& pEach : supports)
    {
        if (pEach == nullptr)
            continue;
        auto pSupport = dynamic_cast<MASupport*>(pEach);
        if (pSupport == nullptr)
            continue;
        auto pt = FVec3(pSupport->position().data);
        auto direction = FVec3(pSupport->posDirection);
        // 设置实例
        FVec3 boundaryPosition = pt;

        _insts.push_back({});

        switch (pSupport->site)
        {
        case MASupport::Front:
            {
                boundaryPosition -= direction * (boundaryThickness / 2);
                _insts.back()._color = Color(65, 205, 82, 150);
            }
            break;
        case MASupport::Middle:
            {
                _insts.back()._color = Color(165, 165, 235, 100);
            }
            break;
        case MASupport::Back:
            {
                boundaryPosition += direction * (boundaryThickness / 2);
                _insts.back()._color = Color(65, 205, 82, 150);
            }
            break;
        default:
            {
                assert(false);
                continue;
            }
        }
        int bore = 50;
        assert (pSupport->parent() != nullptr);
        if (pSupport->parent() != nullptr)
            bore = pSupport->parent()->bore();

        _insts.back()._local = FMat4::Compose(boundaryPosition
            , FMat3::ToQuat(FMat3::MakeRotationUseDirectionZ(FVec3(direction)))
            , FVec3(bore * 2, bore * 2, boundaryThickness));
        _insts.back()._instanceId = uint(++index);
        _insts.back()._instanceAttr = uint(0);
        _insts.back()._user = uint(0);
        swprintf(textBuf, sizeof(textBuf) / sizeof(textBuf[0]), L"%s", con.from_bytes(pSupport->name()).c_str());
        _supportTextRender.add(textBuf, pt, Color::green);
    }
    FVec3Vector lines;
    pGroup->getPipeLines(lines);
    setLines(lines);
    bNeedUpdate = false;
}
void MechanicalAnalysisRender::render(WDContext& context, const WDScene&)
{
    if (bPtsNumberVisible)
        _ptsTextRender.render(context, false);

    if (bUnitNumberVisible)
        _unitTextRender.render(context, false);

    if (bSupportNumberVisible)
    {
        context._rStateStack.push(_pStateDepthTestDisabled);
        {
            WDDrawHelpter::Guard dg(context, _material);
            dg.drawInstance(_insts, *(_pGeo->mesh()), WDMesh::Solid);
        }
        context._rStateStack.pop();
        _supportTextRender.render(context, false);
    }
    _lineRender.render(context);
}