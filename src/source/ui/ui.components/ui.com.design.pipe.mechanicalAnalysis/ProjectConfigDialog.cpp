#include "ProjectConfigDialog.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "core/message/WDMessage.h"
#include "core/WDCore.h"

WD_NAMESPACE_USE

ProjectConfigDialog::ProjectConfigDialog(QWidget *parent)
    : QDialog(parent)
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    this->retranslateUi();

    connect(ui.pushButtonOk, &QPushButton::clicked, this, &ProjectConfigDialog::slotPushButtonOkClicked);

}
ProjectConfigDialog::~ProjectConfigDialog()
{
}

void ProjectConfigDialog::showEvent(QShowEvent *)
{
}
void ProjectConfigDialog::hideEvent(QHideEvent *)
{
}

void ProjectConfigDialog::slotPushButtonOkClicked()
{
    QString projectName = ui.lineEditProjectName->text();
    if (projectName.isEmpty())
    {
        WD_WARN_T("ProjectConfigDialog", "The program name cannot be empty!");
        return;
    }
    QString projectMark = ui.lineEditProjectMark->text();
    if (projectMark.isEmpty())
    {
        WD_WARN_T("ProjectConfigDialog", "The program code cannot be empty!");
        return;
    }
    QString engineeringName = ui.lineEditEngineeringName->text();
    if (engineeringName.isEmpty())
    {
        WD_WARN_T("ProjectConfigDialog", "The project cannot be empty!");
        return;
    }
    QString engineeringMark = ui.lineEditEngineeringMark->text();
    if (engineeringMark.isEmpty())
    {
        WD_WARN_T("ProjectConfigDialog", "The project code be empty!");
        return;
    }
    emit sigProjectConfigOk(projectName, projectMark, engineeringName, engineeringMark);
    this->accept();
}

void ProjectConfigDialog::retranslateUi()
{
    Trs("StressCalculationUnitDialog"
        , static_cast<QDialog*>(this)
        , ui.pushButtonOk
        , ui.labelEngineeringMark
        , ui.labelEngineeringName
        , ui.labelProjectMark
        , ui.labelProjectName
    );
}