#include "MechanicalAnalysisData.h"

WD_NAMESPACE_USE

PointD3::PointD3(DVec3 position) : data(position)
{
}
bool PointD3::operator<(const PointD3& right) const
{
    auto leftV = Convert(data);
    auto rightV = Convert(right.data);

    return leftV < rightV;
}
bool PointD3::operator==(const PointD3& right) const
{
    auto leftV = Convert(data);
    auto rightV = Convert(right.data);

    return leftV == rightV;
}
PointD3 PointD3::operator+(const PointD3& right)
{
    return this->data + right.data;
}
PointD3 PointD3::operator/(const int& s)
{
    return this->data / s;
}
TVec3<int64> PointD3::Convert(const DVec3& pt)
{
    auto val = std::pow(10.0, Digit);
    return TVec3<int64>(static_cast<int64>((round(pt.x * val) / val))
        ,  static_cast<int64>((round(pt.y * val) / val))
        ,  static_cast<int64>((round(pt.z * val) / val)));
}

MechanicalAnalysisDataBase::MechanicalAnalysisDataBase(const std::string& name, WDNode& node, MechanicalAnalysisSection* parent)
    : _name(name), _node(node)
{
    _parent = nullptr;
    setParent(parent);
}
bool    MechanicalAnalysisDataBase::setName(const std::string& newName)
{
    if (_parent != nullptr && _parent->checkName(this->type(), newName))
        return false;

    _name = newName;
    return true;
}
void    MechanicalAnalysisDataBase::setParent(MechanicalAnalysisSection* parent)
{
    if (_parent != nullptr)
    {
        if (parent != nullptr && _parent->number() == parent->number())
            return;
        _parent->removeChild(this);
    }

    _parent = parent;

    if (_parent != nullptr)
        _parent->addChild(this);
}

const char* MechanicalAnalysisSupport::TypeToStr(SuppType type)
{
    switch (type)
    {
    case T_NULL:
        return "NULL";
    case T_ANCH:
        return "ANCH";
    case T_NOZZ:
        return "NOZZ";
    case T_CONN:
        return "CONN";
    case T_RSTN:
        return "RSTN";
    case T_MULR:
        return "MULR";
    case T_ROTR:
        return "ROTR";
    case T_SNUB:
        return "SNUB";
    case T_RSUP:
        return "RSUP";
    case T_CSUP:
        return "CSUP";
    case T_VSUP:
        return "VSUP";
    case T_HANG:
        return "HANG";
    default:
        break;
    }
    return "";
}

bool    MechanicalAnalysisSection::checkName(const MADataType& type, const std::string& name) const
{
    return findChild(type, name) == nullptr;
}

void    MechanicalAnalysisSection::removeChild(MechanicalAnalysisDataBase* pChild)
{
    if (pChild == nullptr)
        return;
    removeChild(pChild->type(), pChild->name());
}
void    MechanicalAnalysisSection::removeChild(const MADataType& type, const std::string& name)
{
    auto itr = _children.find(type);
    if (itr != _children.end())
    {
        for (int i = 0; i < itr->second.size(); ++i)
        {
            auto pData = itr->second[i];
            if (pData == nullptr)
                continue;
            if (pData->name() == name)
            {
                pData->_parent = nullptr;
                itr->second.erase(itr->second.begin() + i);
            }
        }
    }
}
void    MechanicalAnalysisSection::removePoint(const std::string& name)
{
    removeChild(MADataType::MAT_Point, name);
}
void    MechanicalAnalysisSection::removeSupport(const std::string& name)
{
    removeChild(MADataType::MAT_Support, name);
}
void    MechanicalAnalysisSection::removeUnit(const std::string& name)
{
    removeChild(MADataType::MAT_Unit, name);
}
void    MechanicalAnalysisSection::removeAllChild(const std::optional<MADataType>& type)
{
    if (type)
    {
        auto itr = _children.find(type.value());
        if (itr != _children.end())
        {
            auto& datas = itr->second;
            for (auto& each : datas)
            {
                if (each != nullptr)
                    each->_parent = nullptr;
            }
            datas.clear();
        }
        return;
    }

    for (auto& itr : _children)
    {
        for (auto& pData : itr.second)
        {
            if (pData != nullptr)
                pData->_parent = nullptr;
        }
    }
    _children.clear();
}
void    MechanicalAnalysisSection::removeAllPoint()
{
    removeAllChild(MADataType::MAT_Point);
}
void    MechanicalAnalysisSection::removeAllSupport()
{
    removeAllChild(MADataType::MAT_Support);
}
void    MechanicalAnalysisSection::removeAllUnit()
{
    removeAllChild(MADataType::MAT_Unit);
}

bool    MechanicalAnalysisSection::addChild(MechanicalAnalysisDataBase* pChild)
{
    if (pChild == nullptr)
        return false;

    if (!checkName(pChild->type(), pChild->name()))
        return false;

    auto itr = _children.find(pChild->type());
    if (itr == _children.end())
    {
        itr = _children.emplace(pChild->type(), MechanicalAnalysisDatas()).first;
    }
    if (itr == _children.end())
    {
        assert(false);
        return false;
    }
    itr->second.push_back(pChild);
    return true;
}

size_t  MechanicalAnalysisSection::addChildren(const std::vector<MechanicalAnalysisDataBase*>& children)
{
    size_t ret = 0;
    for (auto& pChild : children)
    {
        if (addChild(pChild))
            ++ret;
    }
    return ret;
}

MechanicalAnalysisDataBase* MechanicalAnalysisSection::findChild(const MADataType& type, const std::string& name) const
{
    auto itr = _children.find(type);
    if (itr != _children.end())
    {
        for (auto& pData : itr->second)
        {
            if (pData == nullptr)
                continue;
            if (pData->name() == name)
                return pData;
        }
    }
    return nullptr;
}
MechanicalAnalysisDataBase* MechanicalAnalysisSection::findChild(const MADataType& type, const PointD3& position) const
{
    auto itr = _children.find(type);
    if (itr != _children.end())
    {
        for (auto& pData : itr->second)
        {
            if (pData == nullptr)
                continue;
            if (pData->position() == position)
                return pData;
        }
    }
    return nullptr;
}
