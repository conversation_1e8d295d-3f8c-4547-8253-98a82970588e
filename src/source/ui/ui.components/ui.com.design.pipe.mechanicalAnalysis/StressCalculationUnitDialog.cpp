#include "StressCalculationUnitDialog.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "core/message/WDMessage.h"
#include "core/WDCore.h"
#include "core/businessModule/design/pipeWork/WDBMDPipeUtils.h"
#include "core/viewer/WDViewer.h"
#include <QFileDialog>

WD_NAMESPACE_USE

void CaptureMonitor::onResult(const WD::WDCapturePositioningResult& result, bool& existFlag, const WD::WDCapturePositioning& sender)
{
    WDUnused2(existFlag, sender);
    _dialog.checkNode(result.node.lock());
}

StressCalculationUnitDialog::StressCalculationUnitDialog(WD::WDCore& core, QWidget *parent)
    : _core(core), QDialog(parent)
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    this->retranslateUi();

    _pCaptureMonitor = new CaptureMonitor(*this);
    _pInsertConstraintDialog = new InsertConstraintDialog(nullptr, this);
    _listDialog = new ListDialog(nullptr, this);
    _pProjectConfigDialog = new ProjectConfigDialog(this);

    //任意的图形元素，目前的理解是类似于拾取
    _option.type = WD::WDCapturePositioningType::CPT_Graphics;
    _param.pMonitor = _pCaptureMonitor;
    _option.options = WD::WDCapturePositioningOptions::CPO_Cursor;
    _param.bDisplayCaptureTypeUi = false;
    _param.bDisplayCaptureOptionsUi = false;


    _pAutoNumberConfigDialog = new AutoNumberConfigDialog(_core, this);

    ui.listWidgetGroups->setSelectionMode(QAbstractItemView::ExtendedSelection);
    ui.pushButtonEditMember->setCheckable(true);
    ui.pushButtonEditMember->setChecked(false);
    _pCurrentGroup = nullptr;
    connect(ui.listWidgetGroups
        , &QListWidget::itemSelectionChanged
        , this
        , &StressCalculationUnitDialog::slotListWidgetGroupsItemSelectionChanged);
    connect(ui.listWidgetGroups
        , &QListWidget::itemClicked
        , this
        , &StressCalculationUnitDialog::slotListWidgetGroupsItemSelectionChanged);
    connect(ui.listWidgetMembers
        , &QListWidget::itemClicked
        , this
        , &StressCalculationUnitDialog::slotListWidgetMembersItemClicked);

    _pRender = new WD::MechanicalAnalysisRender;

    ui.checkBoxPointVisible->setChecked(_pRender->bPtsNumberVisible);
    ui.checkBoxSupportVisible->setChecked(_pRender->bSupportNumberVisible);
    ui.checkBoxUnitVisible->setChecked(_pRender->bUnitNumberVisible);

    connect(ui.listWidgetGroups,            &QListWidget::itemChanged,  this,   &StressCalculationUnitDialog::slotListWidgetGroupsItemChanged);
    connect(ui.pushButtonCreateGroup,       &QPushButton::clicked,      this,   &StressCalculationUnitDialog::slotPushButtonCreateGroupClicked);
    connect(ui.pushButtonDeleteGroup,       &QPushButton::clicked,      this,   &StressCalculationUnitDialog::slotPushButtonDeleteGroupClicked);
    connect(ui.pushButtonResetGroup,        &QPushButton::clicked,      this,   &StressCalculationUnitDialog::slotPushButtonResetGroupClicked);
    connect(ui.pushButtonSearchGroup,       &QPushButton::clicked,      this,   &StressCalculationUnitDialog::slotPushButtonSearchGroupClicked);
    connect(ui.pushButtonCopyGroup,         &QPushButton::clicked,      this,   &StressCalculationUnitDialog::slotPushButtonCopyGroupClicked);
    connect(ui.pushButtonEditMember,        &QPushButton::clicked,      this,   &StressCalculationUnitDialog::slotPushButtonEditMemberClicked);
    connect(ui.pushButtonAutoNumberConfig,  &QPushButton::clicked,      this,   &StressCalculationUnitDialog::slotPushButtonAutoNumberConfigClicked);

    connect(ui.lineEditMaterialNumber, &QLineEdit::editingFinished, [&]()
    {
        if (_pCurrentGroup == nullptr)
        {
            WD_WARN_T("StressCalculationUnitDialog", "Please select a pipeline group!");
            return;
        }
        auto text = ui.lineEditMaterialNumber->text();
        if (text.isEmpty())
        {
            WD_WARN_T("StressCalculationUnitDialog", "The material number cannot be empty!");
            ui.lineEditMaterialNumber->setText(_pCurrentGroup->materialNumber.c_str());
            return;
        }
        _pCurrentGroup->materialNumber = text.toUtf8().data();
    });

    connect(ui.pushButtonExit, &QPushButton::clicked, [&]()
    {
        this->close();
    });

    connect(_pAutoNumberConfigDialog, &AutoNumberConfigDialog::sigConfigChanged, this, &StressCalculationUnitDialog::slotAntoNumberUpdate);


    connect(ui.pushButtonInsertConstraint, &QPushButton::clicked, [&]()
    {
        if (_pCurrentGroup == nullptr)
            WD_WARN_T("StressCalculationUnitDialog", "The current pipe group is empty!");

        _pInsertConstraintDialog->pCurrentGroup = _pCurrentGroup;

        if (_pInsertConstraintDialog->isHidden())
        _pInsertConstraintDialog->show();
        else
            _pInsertConstraintDialog->activateWindow();
    });

    connect(_pProjectConfigDialog, &ProjectConfigDialog::sigProjectConfigOk, [&](const QString& projectName
        , const QString& projectMark
        , const QString& engineeringName
        , const QString& engineeringMark )
    {
        assert(_pCurrentGroup != nullptr && "管道组为空!");
        if (_pCurrentGroup == nullptr)
        {
            WD_WARN_T("StressCalculationUnitDialog","Unknown error,export failed!");
            return;
        }

        auto filePathText =  QFileDialog::getSaveFileName(this
            , QString::fromUtf8(WD::WDTs("StressCalculationUnitDialog","Generate Json file").c_str())
            , _jsonPath + ".json"
            , QString::fromUtf8("*.json"));
        if (filePathText.isEmpty())
            return;

        QString suffix(".json");
        if (!filePathText.endsWith(suffix, Qt::CaseInsensitive))
            filePathText += suffix;

        _jsonPath = QFileInfo(filePathText).path() + "/";

        std::string fileName = filePathText.toUtf8().data();
        _groupMgr.projectName = projectName;
        _groupMgr.projectMark = projectMark;
        _groupMgr.engineeringName = engineeringName;
        _groupMgr.engineeringMark = engineeringMark;

        _groupMgr.toJson(*_pCurrentGroup, fileName);

    });
    connect(ui.pushButtonSubmit, &QPushButton::clicked, [&]()
    {
        assert(_pCurrentGroup != nullptr && "管道组为空!");
        if (_pCurrentGroup == nullptr)
        {
            WD_WARN_T("StressCalculationUnitDialog", "Unknown error,export failed!");
            return;
        }
        // 遍历管道组的所有截面,判断是否有截面缺失外径和壁厚
        for (auto& each : _pCurrentGroup->sections())
        {
            auto pSection = each.second;
            if (pSection == nullptr)
                continue;
            auto outSideBore = pSection->outSideBore;
            if (outSideBore < NumLimits<double>::Epsilon)
            {
                char info[1024]     =   { 0 };
                std::string text   =   WD::WDTs("StressCalculationUnitDialog","Pipe group %s section %d Outer diameter is 0 or failed to get!");
                sprintf_s(info, sizeof(info), text.c_str(), _pCurrentGroup->name().c_str(), pSection->number());
                WD_WARN(info);
                return;
            }
            auto thickness = pSection->thickness;
            if (thickness < NumLimits<double>::Epsilon)
            {
                char info[1024] = { 0 };
                std::string text = WD::WDTs("StressCalculationUnitDialog", "Pipe group %s section %d wall thickness is 0 or failed to get!");
                sprintf_s(info, sizeof(info), text.c_str(), _pCurrentGroup->name().c_str(), pSection->number());
                WD_WARN(info);
                return;
            }
        }
        // 遍历管道组的所有截面,判断是否有截面缺失外径和壁厚
        for (auto& pEach : _pCurrentGroup->supports())
        {
            if (pEach == nullptr)
                continue;
            auto pSupport = dynamic_cast<WD::MASupport*>(pEach);
            if (pSupport == nullptr)
                continue;
            if (pSupport->suppType == MASupport::T_NULL)
            {
                char info[1024] = { 0 };
                if (pSupport->attaName.empty())
                {
                    std::string text = WD::WDTs("StressCalculationUnitDialog", "The type of constraint point %s is empty!");
                    sprintf_s(info, sizeof(info), text.c_str(), pSupport->name().c_str());
                }
                else
                {
                    std::string text = WD::WDTs("StressCalculationUnitDialog", "The type of stent point %s is empty!");
                    sprintf_s(info, sizeof(info), text.c_str(), pSupport->name().c_str());
                }
                WD_WARN(info);
                return;
            }
        }
        if (_pProjectConfigDialog->isHidden())
            _pProjectConfigDialog->show();
        else
            _pProjectConfigDialog->activateWindow();

    });

    connect(ui.pushButtonList, &QPushButton::clicked, [&]()
    {
        if (_pCurrentGroup == nullptr)
            WD_WARN_T("StressCalculationUnitDialog","The current pipeline group is empty!");

        _listDialog->pCurrentGroup = _pCurrentGroup;

        if (_listDialog->isHidden())
            _listDialog->show();
        else
            _listDialog->activateWindow();
    });

    connect(ui.checkBoxPointVisible, &QCheckBox::toggled, [&]()
    {
        _pRender->bPtsNumberVisible = ui.checkBoxPointVisible->isChecked();
        _core.needRepaint();
    });
    connect(ui.checkBoxSupportVisible, &QCheckBox::toggled, [&]()
    {
        _pRender->bSupportNumberVisible = ui.checkBoxSupportVisible->isChecked();
        _core.needRepaint();
    });
    connect(ui.checkBoxUnitVisible, &QCheckBox::toggled, [&]()
    {
        _pRender->bUnitNumberVisible = ui.checkBoxUnitVisible->isChecked();
        _core.needRepaint();
    });
    connect(_listDialog, &ListDialog::sigDialogShow, this, &StressCalculationUnitDialog::slotUpdateGroupListWidgetStatu);
    connect(_listDialog, &ListDialog::sigDialogHide, this, &StressCalculationUnitDialog::slotUpdateGroupListWidgetStatu);
    connect(_listDialog, &ListDialog::sigSectionNeedUpdate, [&]()
    {
        _pCurrentGroup = _listDialog->pCurrentGroup;
        if (_pCurrentGroup == nullptr)
        {
            WD_WARN_T("StressCalculationUnitDialog", "The current pipeline group is empty!");
            return;
        }
        if (_groupMgr.reSetSectionNumber(*_pCurrentGroup))
        {
            WD_INFO_T("StressCalculationUnitDialog", "The app setup is successful!");
        }
        _listDialog->updateDialog();
    });
    connect(_listDialog, &ListDialog::sigNumberChanged, [&]()
    {
        assert(_pCurrentGroup != nullptr);
        _pRender->pGroup = _pCurrentGroup;
        _pRender->bNeedUpdate = true;
        _core.needRepaint();
    });
    connect(_pInsertConstraintDialog, &InsertConstraintDialog::sigDialogShow, this, &StressCalculationUnitDialog::slotUpdateGroupListWidgetStatu);
    connect(_pInsertConstraintDialog, &InsertConstraintDialog::sigDialogHide, this, &StressCalculationUnitDialog::slotUpdateGroupListWidgetStatu);
    connect(_pInsertConstraintDialog, &InsertConstraintDialog::sigDataUpdated, [&]()
    {
        if (_pCurrentGroup == nullptr)
            return;
        _pCurrentGroup->updateConstraintedParts();
        _pRender->pGroup = _pCurrentGroup;
        _pRender->bNeedUpdate = true;
        _core.needRepaint();
    });
    connect(ui.listWidgetMembers->model(), &QAbstractItemModel::rowsMoved, [&](const QModelIndex &, int start, int end, const QModelIndex &, int row)
    {
        if (_pCurrentGroup == nullptr)
        {
            assert(false);
            return;
        }
        // 暂不支持多选移动
        if (start != end)
        {
            assert(false);
            return;
        }
        if (start == row)
            return;
        auto& members = _pCurrentGroup->members;
        if (start >= members.size() || row > members.size())
        {
            assert(false);
            return;
        }
        MechanicalAnalysisGroup::Member val = members[start];
        if (row == static_cast<int>(members.size()))
            members.emplace_back(val);
        else
            members.insert(members.begin() + row, val);
        // 如果插入的位置在起始位置前,则起始位置 +1
        if (row < start)
            ++start;
        // 移除起始的数据
        members.erase(members.begin() + start);
    });

}
StressCalculationUnitDialog::~StressCalculationUnitDialog()
{
    if (_pCaptureMonitor != nullptr)
        delete _pCaptureMonitor;
    if (_pAutoNumberConfigDialog != nullptr)
        delete _pAutoNumberConfigDialog;
    if (_pRender != nullptr)
        delete _pRender;
    if (_pInsertConstraintDialog != nullptr)
        delete _pInsertConstraintDialog;
    if (_pProjectConfigDialog != nullptr)
        delete _pProjectConfigDialog;
}

void StressCalculationUnitDialog::showEvent(QShowEvent *)
{
    this->captureDeAtivate();
    groupMemberEditMode(false);
    _pCurrentGroup = nullptr;
    _selectGroups.clear();

    _core.scene().addRenderObject(_pRender);

    ui.listWidgetGroups->blockSignals(true);
    ui.listWidgetGroups->setCurrentItem(nullptr);
    ui.listWidgetGroups->blockSignals(false);

    _core.needRepaint();
}
void StressCalculationUnitDialog::hideEvent(QHideEvent *)
{
    // 取消捕捉
    this->captureDeAtivate();
    groupMemberEditMode(false);
    if (_pCurrentGroup != nullptr)
        _pCurrentGroup->setSelected(false);

    _pCurrentGroup = nullptr;
    ui.pushButtonEditMember->setChecked(false);
    for (auto& each : _selectGroups)
    {
        if (each != nullptr)
            each->setSelected(false);
    }
    _selectGroups.clear();
    _pAutoNumberConfigDialog->hide();
    _pInsertConstraintDialog->hide();
    _pProjectConfigDialog->hide();
    _listDialog->hide();

    _core.scene().removeRenderObject(_pRender);
    ui.listWidgetGroups->blockSignals(true);
    ui.listWidgetGroups->setCurrentItem(nullptr);
    ui.listWidgetGroups->blockSignals(false);
    _core.needRepaint();
}

void StressCalculationUnitDialog::slotAntoNumberUpdate()
{
    if (_pCurrentGroup == nullptr)
        return;
    _groupMgr.updateData(*_pCurrentGroup, _pAutoNumberConfigDialog->configs);
    _pCurrentGroup->updateConstraintedParts();
    _pRender->pGroup = _pCurrentGroup;
    _pRender->bNeedUpdate = true;
    _core.needRepaint();
}
void StressCalculationUnitDialog::slotListWidgetGroupsItemChanged(QListWidgetItem* pItem)
{
    if (pItem == nullptr)
        return;
    assert(_pCurrentGroup != nullptr);
    if (_pCurrentGroup == nullptr)
        return;
    std::string text = pItem->text().toUtf8().data();
    if (text.empty())
    {
        WD_WARN_T("StressCalculationUnitDialog", "The pipeline group name cannot empty!");
        pItem->setText(_pCurrentGroup->name().c_str());
        return;
    }
    std::string value = pItem->data(Qt::UserRole).toString().toUtf8().data();
    if (value == text)
        return;

    _pCurrentGroup = _groupMgr.changeGroup(_pCurrentGroup->name(), text);
    assert(_pCurrentGroup != nullptr);
    if (_pCurrentGroup == nullptr)
        return;
    ui.listWidgetGroups->blockSignals(true);
    pItem->setData(Qt::UserRole, pItem->text());
    ui.listWidgetGroups->blockSignals(false);
}
void StressCalculationUnitDialog::slotListWidgetGroupsItemSelectionChanged()
{
    for (auto& each : _selectGroups)
    {
        if (each != nullptr)
            each->setSelected(false);
    }
    _core.needRepaint();
    _selectGroups.clear();
    auto lists = ui.listWidgetGroups->selectedItems();
    if (!lists.empty())
    {
        for (auto pItem : lists)
        {
            if (pItem == nullptr)
                continue;
            // 获取item的用户数据,即管道组名称
            std::string name = pItem->data(Qt::UserRole).toString().toUtf8().data();
            assert(!name.empty());
            if (name.empty())
                return;
            // 保存选中的管道组
            auto group = _groupMgr.findGroup(name);
            if (group != nullptr)
            {
                group->setSelected(true);
                _selectGroups.emplace_back(group);
            }
        }
        _core.needRepaint();
    }

    if (ui.listWidgetGroups->selectedItems().empty())
    {
        ui.lineEditMaterialNumber->clear();
        ui.listWidgetMembers->clear();
        return;
    }

    auto pItem = ui.listWidgetGroups->currentItem();
    if (pItem == nullptr)
        return;

    // 获取item的用户数据,即管道组名称
    std::string name = pItem->data(Qt::UserRole).toString().toUtf8().data();
    assert(!name.empty());
    if (name.empty())
        return;

    if (_pCurrentGroup != nullptr)
    {
        if (_pCurrentGroup->name() == name)
        {
            _pCurrentGroup->setSelected(true);
            return;
        }
        _pCurrentGroup->setSelected(false);
    }
    _pCurrentGroup = nullptr;

    // 查找获取的管道组
    _pCurrentGroup = _groupMgr.findGroup(name);
    if (_pCurrentGroup == nullptr)
        return;
    updateListWidgetMember(_pCurrentGroup);
    _pRender->pGroup = _pCurrentGroup;
    _pRender->bNeedUpdate = true;
    _core.needRepaint();
}
void StressCalculationUnitDialog::slotListWidgetMembersItemClicked(QListWidgetItem *pItem)
{
    if (pItem == nullptr || _pCurrentGroup == nullptr)
        return;
    _pCurrentGroup->setSelected(false);
    _pCurrentGroup->setSelected(true, pItem->text().toUtf8().data());
    _core.needRepaint();
}
void StressCalculationUnitDialog::slotPushButtonCreateGroupClicked()
{
    auto newName = genNewGroupName();
    assert(!newName.empty());
    if (newName.empty())
        return;
    // 建立新的管道组
    auto pItem = CreateNewGroup(newName);
    if (pItem == nullptr)
    {
        WD_WARN_T("StressCalculationUnitDialog", "Failed to create pipeline group!");
        return;
    }
    for (int i = 0; i < ui.listWidgetGroups->count(); ++i)
    {
        auto each = ui.listWidgetGroups->item(i);
        if (each != nullptr)
            each->setSelected(false);
    }

    ui.listWidgetGroups->addItem(pItem);
    ui.listWidgetGroups->setCurrentItem(pItem);
}
void StressCalculationUnitDialog::slotPushButtonDeleteGroupClicked()
{
    auto lists = ui.listWidgetGroups->selectedItems();
    if (lists.empty())
    {
        WD_WARN_T("StressCalculationUnitDialog","Please select at least one pipeline group!");
        return;
    }
    if (WD_QUESTION_T("StressCalculationUnitDialog", "Specifies whether to delete the currently selected node group?") != 0)
        return;
    ui.listWidgetGroups->blockSignals(true);
    for (auto pItem : lists)
    {
        if (pItem == nullptr)
            continue;
        // 获取item的用户数据,即管道组名称
        std::string name = pItem->data(Qt::UserRole).toString().toUtf8().data();
        assert(!name.empty());
        if (name.empty())
            continue;
        // 删除管道组
        auto ret = _groupMgr.removeGroup(name);
        WDUnused(ret);
        assert(ret);
        // 删除item
        auto row = ui.listWidgetGroups->row(pItem);
        pItem = ui.listWidgetGroups->takeItem(row);
        if (pItem != nullptr)
            delete pItem;
    }
    ui.listWidgetGroups->blockSignals(false);
    _pCurrentGroup = nullptr;
    updateListWidgetMember(_pCurrentGroup);
    _selectGroups.clear();
    _pRender->pGroup = nullptr;
    _pRender->bNeedUpdate = true;
    _core.needRepaint();
}
void StressCalculationUnitDialog::slotPushButtonResetGroupClicked()
{
    if (_selectGroups.empty())
    {
        WD_WARN_T("StressCalculationUnitDialog", "Please select at least one pipeline group!");
        return;
    }
    if (WD_QUESTION_T("StressCalculationUnitDialog", "Specifies whether to reset the currently selected node group?") != 0)
        return;

    for (auto pGroup : _selectGroups)
    {
        if (pGroup == nullptr)
            continue;
        pGroup->clearNumbers();
    }
    _pRender->bNeedUpdate = true;
    _core.needRepaint();
}
void StressCalculationUnitDialog::slotPushButtonSearchGroupClicked()
{
    // 获取当前节点
    auto pNode = _core.nodeTree().currentNode();
    if (pNode == nullptr || (!WDBMDPipeUtils::IsPipeComponent(*pNode) && !pNode->isType("TUBI")))
    {
        WD_WARN_T("StressCalculationUnitDialog" , "Please select a fitting node to find it!");
        return;
    }
    auto groupNames = _groupMgr.containsGroups(pNode);
    if (groupNames.empty())
    {
        WD_INFO_T("StressCalculationUnitDialog", "The current node was not found in the pipeline group!");
        return;
    }
    std::string sText = WD::WDTs("StressCalculationUnitDialog", "Group name:");
    char text[1024] = "";

    // 遍历所有管道组查找当前节点
    for (auto& each : groupNames)
    {
        sprintf(text, "%s %s ", sText.c_str(), each.c_str());
    }
    WD_INFO(text);
}
void StressCalculationUnitDialog::slotPushButtonCopyGroupClicked()
{
    if (_pCurrentGroup == nullptr)
    {
        WD_WARN_T("StressCalculationUnitDialog", "Please select a pipeline group!");
        return;
    }
    // 用管道组的名称生成新的名称
    auto newName = genNewGroupName(_pCurrentGroup->name());
    assert(!newName.empty());
    if (newName.empty())
        return;
    // 建立新的管道组
    auto pItem = CreateNewGroup(newName, _pCurrentGroup->materialNumber);
    if (pItem == nullptr)
    {
        WD_WARN_T("StressCalculationUnitDialog", "Failed to create pipeline group!");
        return;
    }
    for (int i = 0; i < ui.listWidgetGroups->count(); ++i)
    {
        auto each = ui.listWidgetGroups->item(i);
        if (each != nullptr)
            each->setSelected(false);
    }
    ui.listWidgetGroups->insertItem(ui.listWidgetGroups->currentIndex().row() + 1, pItem);
    ui.listWidgetGroups->setCurrentItem(pItem);
}
void StressCalculationUnitDialog::slotPushButtonEditMemberClicked(bool checked)
{
    if (checked)
    {
        if (_pCurrentGroup == nullptr)
        {
            WD_WARN_T("StressCalculationUnitDialog", "Please select a pipeline group!");
            ui.pushButtonEditMember->blockSignals(true);
            ui.pushButtonEditMember->setChecked(false);
            ui.pushButtonEditMember->blockSignals(false);
            return;
        }
        _pCurrentGroup->cleanData();
        updateListWidgetMember(_pCurrentGroup);
        _pRender->clear();
        groupMemberEditMode(true);
        captureAtivate();
        _core.needRepaint();
    }
    else
    {
        captureDeAtivate();
        _pCurrentGroup->cleanData();
        updateListWidgetMember(_pCurrentGroup);
        groupMemberEditMode(false);
        if (!connectCheck(_pCurrentGroup))
        {
            WD_WARN_T("StressCalculationUnitDialog", "Please note that the integrity of the pipeline is incorrect!");
        }
        else
        {
            slotAntoNumberUpdate();
        }
    }
}
void StressCalculationUnitDialog::slotPushButtonAutoNumberConfigClicked()
{
    if (_pAutoNumberConfigDialog->isHidden())
        _pAutoNumberConfigDialog->show();
    else
        _pAutoNumberConfigDialog->activateWindow();
}
void StressCalculationUnitDialog::slotUpdateGroupListWidgetStatu()
{
    ui.listWidgetGroups->setEnabled(_pInsertConstraintDialog->isHidden() && _listDialog->isHidden());
}

void StressCalculationUnitDialog::checkNode(WD::WDNode::SharedPtr pCurrNode)
{
    if (_pCurrentGroup == nullptr)
    {
        WD_WARN_T("StressCalculationUnitDialog", "Please select a pipeline group!");
        return;
    }
    if (pCurrNode == nullptr)
        return;

    if (_pCurrentGroup->contain(pCurrNode))
        _pCurrentGroup->removeMember(pCurrNode);
    else
        _pCurrentGroup->addMember(pCurrNode);

    updateListWidgetMember(_pCurrentGroup);
}
bool StressCalculationUnitDialog::connectCheck(MechanicalAnalysisGroup* group)
{
    assert(group != nullptr);
    if (group == nullptr)
        return false;
    auto& members = group->members;
    if (members.empty() || members.size() == 1)
        return true;
    /**
     * @brief 保存每个成员的连接信息,成员和连接对象均为分支
    */
    struct MemberConnectInfo
    {
        WD::WDNode::SharedPtr pNode;
        std::vector<WD::WDNode*> connectNodes;
        // 标识当前分支是否与其他分支相连
        bool bConnected = false;
    };

    // 添加连接时的最小添加单元为 BRAN
    auto addNode = [](std::vector<WD::WDNode*>& nodes, WD::WDNode& node)
    {
        auto pParent = &node;
        while (pParent != nullptr)
        {
            if (pParent->isType("BRAN"))
            {
                nodes.push_back(pParent);
                return;
            }
            pParent = pParent->parent().get();
        }
    };
    using MemberConnectInfos = std::vector<MemberConnectInfo*>;
    MemberConnectInfos infos;

    // 遍历所有成员收集所有成员的连接分支
    for (auto& each : group->members)
    {
        auto pBranch = each.lock();
        assert(pBranch != nullptr);
        if (pBranch == nullptr)
            continue;
        MemberConnectInfo* info = new MemberConnectInfo;
        if (info == nullptr)
        {
            assert(false);
            continue;
        }
        info->pNode = pBranch;
        WD::WDNode::RecursionHelpter(*pBranch, [&addNode](std::vector<WD::WDNode*>& nodes, WD::WDNode& node)
        {
            auto pConnectVal = node.getAttribute("Connections");
            if (!pConnectVal.valid())
                return;
            auto pConnections = pConnectVal.data<WD::WDBMNodeRefs>();
            if (pConnections == nullptr || pConnections->empty())
                return;
            for (auto& connect : *pConnections)
            {
                auto pNode = connect.refNode();
                if (pNode != nullptr)
                    addNode(nodes, *pNode);
            }
        }, info->connectNodes);

        infos.push_back(info);
    }
    assert(infos.size() >= 2);
    if (infos.size() < 2)
    {
        for (auto& info : infos)
        {
            if (info != nullptr)
                delete info;
            info = nullptr;
        }
        return false;
    }
    // 判断分支是否相连
    MemberConnectInfos checkMembers;
    checkMembers.reserve(infos.size());
    // 这里判断的思路是:
    //  1，以infos中第一个成员为起点,将其放入checkTemp中
    //  2，当checkTemp不为空时,将其中的成员放入checkMembers中
    //  3，遍历checkMembers中的成员,找到所有与其中相连且连接标识为false的成员,暂时存放在checkTemp中
    //  4，遍历结束
    // 重复234过程直至checkTemp为空,如果有任一成员的连接标识为false,则其与其他分支不相连

    MemberConnectInfos checkTemp;
    checkTemp.push_back(infos.front());
    checkTemp.reserve(infos.size());
    while (!checkTemp.empty())
    {
        checkMembers.clear();
        for (auto& each : checkTemp)
            checkMembers.push_back(each);

        checkTemp.clear();
        for (auto& each : checkMembers)
        {
            each->bConnected = true;
            for (auto& info : infos)
            {
                assert(info != nullptr);
                if (info == nullptr)
                    continue;
                if (info->bConnected)
                    continue;
                auto pBranch = info->pNode;
                if (pBranch == nullptr)
                    continue;
                for (auto& eachNode : each->connectNodes)
                {
                    if (eachNode == nullptr)
                        continue;
                    if (eachNode->uuid() == pBranch->uuid())
                    {
                        checkTemp.push_back(info);
                        break;
                    }
                }
            }
        }
    }

    for (auto& each : infos)
    {
        assert(each != nullptr);
        if (each != nullptr && !each->bConnected)
            return false;
    }
    for (auto& info : infos)
    {
        if (info != nullptr)
            delete info;
        info = nullptr;
    }
    return true;
}
void StressCalculationUnitDialog::groupMemberEditMode(bool bEditMode)
{
    bool bEnable = !bEditMode;
    ui.listWidgetGroups->setEnabled(bEnable);
    ui.pushButtonCopyGroup->setEnabled(bEnable);
    ui.pushButtonCreateGroup->setEnabled(bEnable);
    ui.pushButtonDeleteGroup->setEnabled(bEnable);
    ui.pushButtonExit->setEnabled(bEnable);
    ui.pushButtonResetGroup->setEnabled(bEnable);
    ui.pushButtonSearchGroup->setEnabled(bEnable);
    ui.pushButtonSubmit->setEnabled(bEnable);
    ui.lineEditMaterialNumber->setEnabled(bEnable);
    ui.pushButtonInsertConstraint->setEnabled(bEnable);
    ui.pushButtonAutoNumberConfig->setEnabled(bEnable);
    ui.pushButtonList->setEnabled(bEnable);
    if (bEditMode)
        ui.listWidgetMembers->setDragDropMode(QListWidget::InternalMove);
    else
        ui.listWidgetMembers->setDragDropMode(QListWidget::NoDragDrop);
}

void StressCalculationUnitDialog::captureAtivate()
{
    // 激活捕捉
    _core.viewer().capturePositioning().activate(_param, _option);
}
void StressCalculationUnitDialog::captureDeAtivate()
{
    // 关闭捕捉
    _core.viewer().capturePositioning().deativate();
}

QListWidgetItem* StressCalculationUnitDialog::CreateNewGroup(const std::string& name, const std::string& number)
{
    assert(!name.empty());
    if (name.empty())
        return nullptr;

    MechanicalAnalysisGroup* pGroup = _groupMgr.createGroup(name, number);
    if (pGroup == nullptr)
        return nullptr;
    // 将管道组的名称作为用户数据保存在item上
    auto* pItem = new QListWidgetItem(name.c_str());
    QVariant userData;
    userData.setValue(QString::fromUtf8(name.c_str()));
    pItem->setData(Qt::UserRole, userData);
    pItem->setFlags(pItem->flags() | Qt::ItemFlag::ItemIsEditable);
    return pItem;
}
std::string StressCalculationUnitDialog::genNewGroupName(const std::string& name)
{
    if (ui.listWidgetGroups->findItems(name.c_str(), Qt::MatchFlag::MatchExactly | Qt::MatchFlag::MatchFixedString).empty())
        return name;
    char nameStr[64] = { 0 };
    for (int i = 1; ; i++)
    {
        sprintf_s(nameStr, sizeof(nameStr), "%s_%d", name.c_str(), i);
        if (ui.listWidgetGroups->findItems(nameStr, Qt::MatchFlag::MatchExactly | Qt::MatchFlag::MatchFixedString).empty())
            break;
        memset(nameStr, 0, sizeof(nameStr));
    }
    return nameStr;
}
void StressCalculationUnitDialog::updateListWidgetMember(const MechanicalAnalysisGroup* group)
{
    ui.listWidgetMembers->clear();
    ui.lineEditMaterialNumber->clear();
    if (group != nullptr)
    {
        ui.lineEditMaterialNumber->setText(group->materialNumber.c_str());
        for (auto& each : group->members)
        {
            auto pBranch = each.lock();
            assert(pBranch != nullptr);
            if (pBranch == nullptr)
                continue;
            ui.listWidgetMembers->addItem(pBranch->name().c_str());
        }
        _pCurrentGroup->setSelected(true);
    }
}

void StressCalculationUnitDialog::retranslateUi()
{
    Trs("StressCalculationUnitDialog"
        , static_cast<QDialog*>(this)
        , ui.pushButtonCopyGroup
        , ui.pushButtonCreateGroup
        , ui.pushButtonDeleteGroup
        , ui.pushButtonEditMember
        , ui.pushButtonExit
        , ui.pushButtonResetGroup
        , ui.pushButtonSearchGroup
        , ui.pushButtonSubmit
        , ui.pushButtonAutoNumberConfig
        , ui.pushButtonInsertConstraint
        , ui.pushButtonList
        , ui.label
        , ui.label_2
        , ui.label_3
        , ui.checkBoxPointVisible
        , ui.checkBoxSupportVisible
        , ui.checkBoxUnitVisible
	  );
}
