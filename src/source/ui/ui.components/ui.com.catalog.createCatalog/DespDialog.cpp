#include "DespDialog.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "core/message/WDMessage.h"
#include "WDTranslate.h"

DespDialog::DespDialog(WD::WDCore& core, QWidget *parent)
    : QDialog(parent)
    , _core(core)
    , _despMgr(core.getBMCatalog().despMgr())
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    // 默认隐藏行号表头
    ui.tableWidgetDesp->verticalHeader()->setVisible(false);
    // 表格自动用列铺满
    ui.tableWidgetDesp->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
    // 加载设计参数
    _despMgr.load();
    // 初始化设计参数表格
    this->initDespTableWidget();

    // 界面翻译
    this->retranslateUi();
    
    // 绑定事件通知响应
    connect(ui.pushButtonAdd,   &QPushButton::clicked,      this, &DespDialog::slotAddClicked);
    connect(ui.pushButtonRemove,&QPushButton::clicked,      this, &DespDialog::slotRemoveClicked);
    connect(ui.pushButtonClear, &QPushButton::clicked,      this, &DespDialog::slotClearClicked);
    connect(ui.pushButtonSave,  &QPushButton::clicked,      this, &DespDialog::slotSaveClicked);
    connect(ui.tableWidgetDesp, &QTableWidget::itemChanged, this, &DespDialog::slotDespChanged);
}
DespDialog::~DespDialog()
{
}

void DespDialog::showEvent(QShowEvent* evt)
{
    WDUnused(evt);
}
void DespDialog::hideEvent(QHideEvent* evt)
{
    WDUnused(evt);
}

void DespDialog::slotAddClicked(bool)
{
    ui.tableWidgetDesp->setRowCount(ui.tableWidgetDesp->rowCount() + 1);
}
void DespDialog::slotRemoveClicked(bool)
{
    // 删除设计参数
    auto row = ui.tableWidgetDesp->currentRow();
    if (row < 0)
        return ;
    auto pIndex = ui.tableWidgetDesp->item(row, 0);
    if (pIndex != nullptr)
    {
        _despMgr.removeDesp(pIndex->text().toInt());
    }
    // 删除表格当前行
    ui.tableWidgetDesp->removeRow(row);
}
void DespDialog::slotClearClicked(bool)
{
    _despMgr.clear();
    ui.tableWidgetDesp->setRowCount(0);
}
void DespDialog::slotSaveClicked(bool)
{
    _despMgr.save();
    WD_INFO_T("DespDialog", "Success to save!");
}
void DespDialog::slotDespChanged(QTableWidgetItem*)
{
    // 设置设计参数
    auto row = ui.tableWidgetDesp->currentRow();
    if (row < 0)
        return ;
    auto pIndex = ui.tableWidgetDesp->item(row, 0);
    auto pValue = ui.tableWidgetDesp->item(row, 1);
    if (pIndex == nullptr || pValue == nullptr)
        return ;
    _despMgr.setDesp(pIndex->text().toInt(), pValue->text().toUtf8().data());
}

void DespDialog::initDespTableWidget()
{
    const auto& desps = _despMgr.desps();
    if (desps.empty())
        return ;

    ui.tableWidgetDesp->setRowCount((int)desps.size());
    int row = 0;
    for (const auto& desp : desps)
    {
        ui.tableWidgetDesp->setItem(row, 0, new QTableWidgetItem(QString::number(desp.first)));
        ui.tableWidgetDesp->setItem(row, 1, new QTableWidgetItem(QString::fromUtf8(desp.second.c_str())));
        row++;
    }
}
void DespDialog::retranslateUi()
{
    Trs("DespDialog"
        , static_cast<QDialog*>(this)
        , ui.tableWidgetDesp
        , ui.pushButtonAdd
        , ui.pushButtonRemove
        , ui.pushButtonClear
        , ui.pushButtonSave);
}