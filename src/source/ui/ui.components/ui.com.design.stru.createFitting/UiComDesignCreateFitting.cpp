#include "UiComDesignCreateFitting.h"

UiComDesignCreateFitting::UiComDesignCreateFitting(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject *parent)
    : QObject(parent)
    , IUiComponent(mainWindow, attrs)
{
    _pCreateFitting =   new CreateFittingDialog(mWindow().core(), mWindow().widget());
}


UiComDesignCreateFitting::~UiComDesignCreateFitting()
{ 
    if (_pCreateFitting != nullptr)
    {
        delete _pCreateFitting;
        _pCreateFitting = nullptr;
    }
}

void UiComDesignCreateFitting::onNotice(UiNotice * pNotice)
{
    int nType = pNotice->type();
    switch (nType)
    {
    case UiNoticeType::UNT_Action:
    {
        UiActionNotice* pActionNotice = static_cast<UiActionNotice*>(pNotice);
        // 创建钢结构
        if (pActionNotice->action().is("action.architectural.fitting"))
        {
            if (_pCreateFitting->isHidden())
                _pCreateFitting->show();
            else
                _pCreateFitting->activateWindow();
        }
    }
    break;
    default:
        break;
    }
}