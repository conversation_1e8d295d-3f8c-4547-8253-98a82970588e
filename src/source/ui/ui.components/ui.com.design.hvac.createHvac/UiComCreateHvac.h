#pragma once

#include <QObject>
#include "../../wizDesignerApp/UiInterface/UiInterface.h"

#include "HvacMainCreateDialog.h"

class UiComCreateHvac
    : public QObject
	, public IUiComponent
{
    Q_OBJECT
public:
    UiComCreateHvac(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject *parent = nullptr);
    ~UiComCreateHvac();
    /**
    *   @brief 通知事件
    *   @param pNotice 事件对象
    */
    virtual void onNotice(UiNotice* pNotice) override;

private:
    WD::WDCore&             _core;
    // HVAC对话框
    HvacMainCreateDialog*   _pHvacMainCreateDialog;
};

