#pragma once
#include    "Mark3D.h"
#include    "core/viewer/primitiveRender/WDTextRender.h"

WD_NAMESPACE_BEGIN

class MarkGroup : public WDObject
{
    WD_DECL_OBJECT(MarkGroup)
public:
    MarkGroup(WDNode::SharedPtr pNode = nullptr)
        : _node(pNode)
        , _visible(true)
        , _needUpdate(true)
    {
    }
    virtual ~MarkGroup()
    {}
public:
    void setNeedUpdate()
    {
        _needUpdate = true;
    }
    /**
    * @brief 获取对应的分支节点
    */
    inline WDNode::SharedPtr node() const
    {
        return _node.lock();
    }
    /**
    * @brief 设置可见
    */
    inline void setVisible(bool visible)
    {
        _visible = visible;
    }
    /**
    * @brief 获取可见
    */
    inline bool visible() const
    {
        return _visible;
    }
public:
    /**
    * @brief 更新标记
    */
    virtual void update(WDContext& context) = 0;
    /**
    * @brief 绘制标记
    */
    virtual void render(WDContext& context) = 0;
protected:
    WD::WDNode::WeakPtr _node;
    // 标记是否可见
    bool _visible;
    // 标记是否需要更新
    bool _needUpdate;
};
/**
 * @brief 一个分支的所有标记
*/
class BranchMarkGroup: public MarkGroup
{
    WD_DECL_OBJECT(BranchMarkGroup)
public:
    BranchMarkGroup(WDNode::SharedPtr pBranchNode = nullptr)
        : MarkGroup(pBranchNode)
    {
    }
    virtual ~BranchMarkGroup() 
    {}
public:
    /**
     * @brief 更新标记
    */
    virtual void update(WDContext& context) override;
    /**
     * @brief 绘制标记 
    */
    virtual void render(WDContext& context) override;
private:
    WDText3DRender _textRender;
    // 分支首位置标记
    MarkPoint::SharedPtr _hPosMark;
    // 分支尾位置标记
    MarkPoint::SharedPtr _tPosMark;
    // 管件沿着管线方向的距离列表
    std::vector<MarkSegment::SharedPtr> _comLenMarks;
    // 直管长度标记列表
    std::vector<MarkSegment::SharedPtr> _tubiLenMarks;
    // 弯头P0点到各个出口点的距离标记列表
    std::vector<MarkSegment::SharedPtr> _elbowDisMarks;
private:
    // 刷新标记，根据分支节点重新自动生成标记
    void reflushMarks(WDContext& context);
    void addRenderText(WDContext& context
        , const DMat4& transform
        , Mark3D& mark
        , const Color& color = Color::white
        , const int size = 60                                               // 文本尺寸(世界坐标)
        , WDText2DRender::HAlign hAlign = WDText2DRender::HAlign::HA_Center // 水平对齐方式
        , WDText2DRender::VAlign vAlign = WDText2DRender::VAlign::VA_Center // 垂直对齐方式
    );

    void addRenderText(WDContext& context
        , const DVec3& position
        , const std::string& text
        , const Color& color = Color::white
        , const int size = 60                                               // 文本尺寸(世界坐标)
        , WDText2DRender::HAlign hAlign = WDText2DRender::HAlign::HA_Center // 水平对齐方式
        , WDText2DRender::VAlign vAlign = WDText2DRender::VAlign::VA_Center // 垂直对齐方式
    );
};
/**
 * @brief 一个支吊架的所有标记
 *  目前是写死的,没法适配所有的支吊架
*/
class SupportMarkGroup : public MarkGroup
{
    WD_DECL_OBJECT(SupportMarkGroup)
public:
    SupportMarkGroup(WDNode::SharedPtr pZoneNode = nullptr)
        : MarkGroup(pZoneNode)
    {
    }
    virtual ~SupportMarkGroup()
    {}
public:
    /**
     * @brief 更新标记
    */
    virtual void update(WDContext& context) override;
    /**
     * @brief 绘制标记
    */
    virtual void render(WDContext& context) override;
private:
    // 文本绘制对象 3D
    WDText3DRender _textRender;
    // 距离列表
    std::vector<MarkSegment::SharedPtr> _comLenMarks;
private:
    // 刷新标记，根据分支节点重新自动生成标记
    void reflushMarks(WDContext& context);
    //
    void addRenderText(WDContext& context
        , const DMat4& transform
        , Mark3D& mark
        , const Color& color = Color::white
        , const int size = 60                                               // 文本尺寸(世界坐标)
        , WDText2DRender::HAlign hAlign = WDText2DRender::HAlign::HA_Center // 水平对齐方式
        , WDText2DRender::VAlign vAlign = WDText2DRender::VAlign::VA_Center // 垂直对齐方式
    );
    void addRenderText(WDContext& context
        , const DVec3& position
        , const std::string& text
        , const Color& color = Color::white
        , const int size = 60                                               // 文本尺寸(世界坐标)
        , WDText2DRender::HAlign hAlign = WDText2DRender::HAlign::HA_Center // 水平对齐方式
        , WDText2DRender::VAlign vAlign = WDText2DRender::VAlign::VA_Center // 垂直对齐方式
    );
};

WD_NAMESPACE_END
