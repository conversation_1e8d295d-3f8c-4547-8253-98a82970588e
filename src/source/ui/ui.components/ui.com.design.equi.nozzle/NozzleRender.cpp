#include "NozzleRender.h"
#include "core/common/WDContext.h"
#include "core/common/WDConfig.h"
#include "core/material/WDDrawHelpter.h"
#include "core/WDCore.h"
#include "core/businessModule/design/WDBMDesign.h"
#include <codecvt>

WD_NAMESPACE_BEGIN

NozzleRender::NozzleRender(WDCore& app)
    :WDSceneNodeRender(app, "NozzleRender")
{
}

void NozzleRender::onNodeAdd(WDNode::SharedPtr pNode)
{
    if (pNode == nullptr || !pNode->isType("NOZZ"))
        return;

    //保存分支数据以及对应节点
    _nozzleNodeSet.insert(pNode.get());
}
void NozzleRender::onNodeRemove(WDNode::SharedPtr pNode)
{
    if (pNode == nullptr)
        return;

    //取消保存分支数据以及对应节点
    auto fItr = _nozzleNodeSet.find(pNode.get());
    if (fItr != _nozzleNodeSet.end())
    {
        _nozzleNodeSet.erase(fItr);
    }
}
void NozzleRender::onNodeUpdate(WDNode::SharedPtr pNode)
{
    WDUnused(pNode);
}
void NozzleRender::onNodeClear()
{
    _nozzleNodeSet.clear();
}

bool NozzleRender::empty() const
{
    return _nozzleNodeSet.empty();
}
bool NozzleRender::containsRelatives(const WDNode& node) const
{
    for (auto itr = _nozzleNodeSet.begin(); itr != _nozzleNodeSet.end(); ++itr)
    {
        auto pTNode = (*itr);
        if (pTNode == nullptr)
        {
            assert(false && "无效的管嘴节点!");
            continue;
        }
        // 是否与加入节点相同
        if (pTNode == &node)
            return true;
        // 是否直系祖先或后代
        if (node.isAncestor(*pTNode) || pTNode->isAncestor(node))
            return true;
    }
    return false;
}

void NozzleRender::updateAabb(WDContext& , const WDScene&)
{
    DAabb3& aabb = this->aabbRef();
    aabb = DAabb3::Null();

    for (auto itr = _nozzleNodeSet.begin(); itr != _nozzleNodeSet.end(); ++itr)
    {
        auto pNode = (*itr);
        if (pNode == nullptr || !pNode->flags().hasFlag(WDNode::F_Visible))
            continue;

        aabb.unions(pNode->aabb());
    }
}

static std::wstring_convert<std::codecvt_utf8_utf16<wchar_t>> con;
void NozzleRender::update(WDContext& context, const WDScene& scene)
{
    WDUnused(scene);
    WDUnused(context);
    _textRender.reset();
    if (!bVisible)
        return;
    for (auto itr = _nozzleNodeSet.begin(); itr != _nozzleNodeSet.end(); ++itr)
    {
        auto pNode = (*itr);
        if (pNode == nullptr || !pNode->flags().hasFlag(WDNode::F_Visible) || !pNode->isNamed())
            continue;
        auto wString = con.from_bytes(pNode->name());
        _textRender.addFixedDirection(wString
            , FVec3(pNode->globalTranslation())
            , WD::Color::white
            , 60);
    }
}
void NozzleRender::render(WDContext& context, const WDScene& scene)
{
    WDUnused(scene);
    if (!bVisible)
        return;
    _textRender.render(context, false);
}


WD_NAMESPACE_END

