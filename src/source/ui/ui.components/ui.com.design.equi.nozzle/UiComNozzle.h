#pragma once

#include    <QObject>
#include     "../../wizDesignerApp/UiInterface/UiInterface.h"
#include    "NozzleDialog.h"
#include    "NozzleRender.h"

class UiComNozzle
    : public QObject
	, public IUiComponent
{
    Q_OBJECT
private:
    WD::WDCore& _core;

public:
    UiComNozzle(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject *parent = nullptr);
    ~UiComNozzle();
public:
    /**
    *   @brief 通知事件
    *   @param pNotice 事件对象
    */
    virtual void onNotice(UiNotice* pNotice) override;

private:
    NozzleDialog* _pCreateNozzleDialog;
    WD::NozzleRender _nozzleRender;
};
