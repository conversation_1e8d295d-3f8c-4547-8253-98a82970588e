set(TARGET_NAME ui_com_design_equi_nozzle)

set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON) 
find_package(Qt5 COMPONENTS Core Widgets REQUIRED)

set(HEADER_FILES
    "UiComNozzle.h"
	"NozzleDialog.h"
	"NozzleRender.h"
)

set(SOURCE_FILES
    "UiComNozzle.cpp"
	"NozzleDialog.cpp"
	"NozzleRender.cpp"
	"main.cpp"
)

set(FORM_FILES
	"NozzleDialog.ui" 
)

add_library(${TARGET_NAME} SHARED
		${HEADER_FILES}
		${SOURCE_FILES}
		${FORM_FILES}
)

target_link_libraries(${TARGET_NAME} PUBLIC wizDesignerCore ui.commonLib.WeakObject ui.commonLib.custom)
target_link_libraries(${TARGET_NAME} PUBLIC Qt5::Core Qt5::Widgets)
target_include_directories(${TARGET_NAME} PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})

add_custom_command(
	TARGET ${TARGET_NAME}
	POST_BUILD
    COMMAND             ${CMAKE_COMMAND} -E make_directory ./translations
    COMMAND             ${CMAKE_COMMAND} -E copy_directory ./translations ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/ui/components/${TARGET_NAME}/translations
	WORKING_DIRECTORY	${CMAKE_CURRENT_SOURCE_DIR}
    COMMENT             "copy directory translations..."
)