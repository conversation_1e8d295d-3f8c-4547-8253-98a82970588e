#pragma once

#include <QDialog>
#include "ui_DeviceParamSpecializationDialog.h"
#include "NozzInfoEditDialog.h"
#include "core/WDCore.h"
#include "core/node/WDNode.h"
#include "WDDevice.h"
#include "../../ui.commonLibrary/ui.commonLib.property/ObjectPropertyWidget.h"
#include "WDDeviceMgr.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiPositionCaptureHelpter.h"
#include "core/scene/WDRenderObject.h"
#include "core/geometry/standardPrimitives/WDGeometrySphere.h"
#include "common/WDContext.h"
#include "core/material/WDDrawHelpter.h"
#include "core/viewer/WDViewer.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiNodeNameHelpter.h"

class DotRender : public WD::WDRenderObject
{
private:
    WD::FVec3               _pos;
    WD::WDMaterialColor     _matColor;
    WD::WDInstance          _inst;
    WD::WDGeometrySphere::SharedPtr _pPointSphere;
public:
    DotRender() : WDRenderObject(WD::RL_Scene)
    {
        _pPointSphere = WD::WDGeometrySphere::MakeShared(1.0f);
        setColor({ 255, 165, 0 });
        setPosition({ 0, 0, 0 });
        _inst._color = _matColor.color();
    }
public:
    inline void setColor(WD::Color c)
    {
        _matColor.setColor(c);
    }
    inline void setPosition(WD::FVec3 pos)
    {
        _pos = pos;
    }
public:
    virtual void updateAabb(WD::WDContext& , const WD::WDScene& ) override
    {

    }
    inline virtual void update(WD::WDContext& context, const WD::WDScene&) override
    {
        auto unitF = context.camera().pixelU((WD::DVec3)_pos, context._viewer.size()); // 视角拉得越近，这个比例越小
        _inst._local = WD::FMat4::Compose(_pos, WD::FVec3(static_cast<float>(unitF) * 5));
    }
    inline virtual void render(WD::WDContext& context, const WD::WDScene&) override
    {
        WD::WDDrawHelpter::Guard dg(context, _matColor);
        dg.drawInstance({ _inst }, *(_pPointSphere->mesh()), WD::WDMesh::Solid);
    }
};

class DeviceParamSpecializationDialog : public QDialog
{
    Q_OBJECT

public:
    DeviceParamSpecializationDialog(WD::WDCore& app, WD::WDDeviceMgr& deviceMgr, QWidget *parent = Q_NULLPTR);
    ~DeviceParamSpecializationDialog();

protected:
    virtual void showEvent(QShowEvent* evt) override;
    virtual void hideEvent(QHideEvent* evt) override;

private slots:
    /*
    * brief 通用类型切换通知响应
    */
    void slotGTypeChanged(const QString& text);
    /*
    * brief 通用类型切换通知响应
    */
    void slotDescChanged(const QString& text);
    /*
    * brief ok按钮按下通知响应
    */
    void slotOkClicked();
    /*
    * brief cancel按钮按下通知响应
    */
    void slotCancelClicked();
    /**
     * @brief 管嘴下拉框项激活槽函数
     * @param  
    */
    void slotNozzChanged(int);
private:
    /*
    * brief 文本翻译
    */
    void translate();

    /*
    * brief 更新通用类型
    */
    void updateGTypeList();
    /**
     * @brief 更新管嘴下拉项
    */
    void updateNozzComboBox();
    /**
     * @brief 设置坐标捕捉工具的transform
    */
    void setCaptureTransfrom(WD::WDNode::SharedPtr pCurrNode
                            , WD::WDNode::SharedPtr pPrevNode
                            , WD::WDNodeTree& sender);
private:
    Ui::DeviceParamSpecializationDialog   ui;
    WD::WDCore&                     _app;
    WD::WDDeviceMgr&                _deviceMgr;
    // 设备模板样本
    WD::WDDevice::SharedPtr         _pSample;
    // 特化设备模板参数因子
	ObjectPropertyWidget*           _pWidget;
    WD::WDPropertyGroup::SharedPtr  _pGroup;
    UiPositionCaptureHelpter        _positionCaptureHelpter;
    DotRender _dotRender;
    // 节点名称助手
    UiNodeNameHelpter           _nameHelpter;
    //修改嘴信息界面
    NozzInfoEditDialog*         _nozzInfoEditDialog;
};
