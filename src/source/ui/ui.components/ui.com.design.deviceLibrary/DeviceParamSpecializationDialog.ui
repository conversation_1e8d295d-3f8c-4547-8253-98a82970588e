<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DeviceParamSpecializationDialog</class>
 <widget class="QDialog" name="DeviceParamSpecializationDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>653</width>
    <height>442</height>
   </rect>
  </property>
  <property name="focusPolicy">
   <enum>Qt::StrongFocus</enum>
  </property>
  <property name="windowTitle">
   <string>UiComNodeCreateNozzle</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout" stretch="1,1">
   <item>
    <layout class="QVBoxLayout" name="verticalLayout_4" stretch="0,1,0,0,0">
     <property name="sizeConstraint">
      <enum>QLayout::SetDefaultConstraint</enum>
     </property>
     <item>
      <layout class="QFormLayout" name="formLayout">
       <item row="0" column="0">
        <widget class="QLabel" name="labelDeviceName">
         <property name="text">
          <string>name</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QLineEdit" name="lineEditDeviceName">
         <property name="focusPolicy">
          <enum>Qt::StrongFocus</enum>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="labelDeviceGType">
         <property name="text">
          <string>gType</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QComboBox" name="comboBoxGType"/>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="labelDeviceDesc">
         <property name="text">
          <string>desc</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QComboBox" name="comboBoxDesc"/>
       </item>
      </layout>
     </item>
     <item>
      <widget class="QGroupBox" name="groupBoxParamFactors">
       <property name="title">
        <string>params</string>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_3">
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_2"/>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QGroupBox" name="groupBoxSetNozz">
       <property name="title">
        <string>SetNozz</string>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_2">
        <item>
         <widget class="QLabel" name="labelNozz">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="text">
           <string>Nozz</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="comboBoxNozz">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QGroupBox" name="groupBoxCapture">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="title">
        <string>Position</string>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout">
        <item>
         <widget class="QCheckBox" name="checkBoxCapture">
          <property name="focusPolicy">
           <enum>Qt::StrongFocus</enum>
          </property>
          <property name="layoutDirection">
           <enum>Qt::RightToLeft</enum>
          </property>
          <property name="text">
           <string>Capture</string>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_9" stretch="0,1,0">
          <item>
           <widget class="QLabel" name="labelX_2">
            <property name="text">
             <string>X</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QDoubleSpinBox" name="doubleSpinBoxCaptureX">
            <property name="minimum">
             <double>-9999999999.000000000000000</double>
            </property>
            <property name="maximum">
             <double>9999999999.989999771118164</double>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QCheckBox" name="checkBoxPositionX">
            <property name="focusPolicy">
             <enum>Qt::StrongFocus</enum>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_10" stretch="0,1,0">
          <item>
           <widget class="QLabel" name="labelY_2">
            <property name="text">
             <string>Y</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QDoubleSpinBox" name="doubleSpinBoxCaptureY">
            <property name="minimum">
             <double>-9999999999.000000000000000</double>
            </property>
            <property name="maximum">
             <double>9999999999.989999771118164</double>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QCheckBox" name="checkBoxPositionY">
            <property name="focusPolicy">
             <enum>Qt::StrongFocus</enum>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_11" stretch="0,1,0">
          <item>
           <widget class="QLabel" name="labelZ_2">
            <property name="text">
             <string>Z</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QDoubleSpinBox" name="doubleSpinBoxCaptureZ">
            <property name="minimum">
             <double>-9999999999.000000000000000</double>
            </property>
            <property name="maximum">
             <double>9999999999.989999771118164</double>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QCheckBox" name="checkBoxPositionZ">
            <property name="focusPolicy">
             <enum>Qt::StrongFocus</enum>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_6">
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="pushButtonOk">
         <property name="focusPolicy">
          <enum>Qt::StrongFocus</enum>
         </property>
         <property name="text">
          <string>ok</string>
         </property>
         <property name="autoDefault">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="pushButtonCancel">
         <property name="focusPolicy">
          <enum>Qt::StrongFocus</enum>
         </property>
         <property name="text">
          <string>cancel</string>
         </property>
         <property name="autoDefault">
          <bool>false</bool>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QLabel" name="labelIcon">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>0</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
