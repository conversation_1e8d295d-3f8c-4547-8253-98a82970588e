#pragma once

#include    <QObject>
#include     "../../wizDesignerApp/UiInterface/UiInterface.h"
#include    "CreateRevolutionDialog.h"

class UiComCreateRevolution
    : public QObject
	, public IUiComponent
{
    Q_OBJECT
public:
    UiComCreateRevolution(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject *parent = nullptr);
    ~UiComCreateRevolution();
public:
    /**
    *   @brief 通知事件
    *   @param pNotice 事件对象
    */
    virtual void onNotice(UiNotice* pNotice) override;
private:
    CreateRevolutionDialog* _pCreateRevolutionDialog;
};
