#include "RotateOffsetInputDialog.h"
#include "core/WDCore.h"
#include "core/undoRedo/WDUndoStack.h"


RotateOffsetInputDialog::RotateOffsetInputDialog(WD::WDCore& app
    , WD::WDObjectAxisEditor* pEditor
    , QWidget *parent)
    : QDialog(parent)
    , _app(app)
    , _pEditor(pEditor)
{
    _pCommand = nullptr;

    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));

    _type   = AT_None;
    _axis   = WD::DVec3::AxisX();
    _center = WD::DVec3::Zero();

    connect(ui.pushButtonPreview
        , &QPushButton::clicked
        , this
        , &RotateOffsetInputDialog::slotPushButtonPreviewClicked);

    connect(ui.pushButtonOk
        , &QPushButton::clicked
        , this
        , &RotateOffsetInputDialog::slotPushButtonOkClicked);

    connect(ui.pushButtonCancel, &QPushButton::clicked, this, &RotateOffsetInputDialog::close);

    this->retranslateUi();
}

RotateOffsetInputDialog::~RotateOffsetInputDialog()
{
    if (_pCommand != nullptr)
    {
        delete _pCommand;
        _pCommand = nullptr;
    }
}

void RotateOffsetInputDialog::showEvent([[maybe_unused]] QShowEvent* evt)
{
    // 统一将焦点设置到窗体，用于在窗口打开时清除其他控件焦点
    this->setFocus();

    ui.doubleSpinBoxAngle->setValue(0.0);
}

void RotateOffsetInputDialog::hideEvent([[maybe_unused]] QHideEvent* evt)
{
    if (_pCommand != nullptr)
    {
        _pCommand->undo();
        delete _pCommand;
        _pCommand = nullptr;

        _app.needRepaint();
    }
}

void RotateOffsetInputDialog::slotPushButtonPreviewClicked()
{
    if (_pEditor == nullptr)
        return;

    auto pObject = _pEditor->object();
    if (pObject == nullptr)
        return;

    bool bNeedRepaint = false;
    if (_pCommand != nullptr)
    {
        _pCommand->undo();
        delete _pCommand;
        _pCommand = nullptr;
        bNeedRepaint = true;
    }

    double angle = ui.doubleSpinBoxAngle->value();
    if (WD::Abs(angle) > WD::NumLimits<float>::Epsilon)
    {
        _pCommand = WD::WDObjectAxisEditor::Object::MakeRotateCommand(_app
            , pObject
            , _axis, angle, _center
            , false);

        if (_pCommand != nullptr)
        {
            _pCommand->redo();
            bNeedRepaint = true;
        }
    }

    // 触发重绘
    if (bNeedRepaint)
        _app.needRepaint();
}
void RotateOffsetInputDialog::slotPushButtonOkClicked()
{
    if (_pEditor == nullptr)
        return;

    auto pObject = _pEditor->object();
    if (pObject == nullptr)
        return;

    bool bNeedRepaint = false;

    if (_pCommand != nullptr)
    {
        _pCommand->undo();
        delete _pCommand;
        _pCommand = nullptr;
        bNeedRepaint = true;
    }

    double angle = ui.doubleSpinBoxAngle->value();
    if (WD::Abs(angle) > WD::NumLimits<float>::Epsilon)
    {
        _pCommand = WD::WDObjectAxisEditor::Object::MakeRotateCommand(_app
            , pObject
            , _axis, angle, _center
            , true);
        // 发送旋转轴编辑通知
        auto& axisR = _pEditor->getAxisR();
        axisR.mDelegate()(WD::WDEditAxis::EditStatus::ES_End, _axis, WD::real(), angle, axisR);

        if (_pCommand != nullptr)
        {
            // 加入到撤销栈
            _app.undoStack().push(_pCommand);

            _pCommand = nullptr;
            // 这里redo命令执行后，会自动触发needRepaint, 因此后面不需要再重绘了
            bNeedRepaint = false;
        }
    }

    if (bNeedRepaint)
        _app.needRepaint();

    this->accept();
}

void RotateOffsetInputDialog::retranslateUi()
{
    Trs("RotateOffsetInputDialog"
        , static_cast<QDialog*>(this)
        , ui.label
        , ui.pushButtonPreview
        , ui.pushButtonOk
        , ui.pushButtonCancel
    );
}