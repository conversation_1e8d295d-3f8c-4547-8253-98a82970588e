#pragma once

#include <QtWidgets/QDialog>
#include "ui_MoveOffsetInputDialog.h"
#include "core/viewer/objectAxisEditor/WDObjectAxisEditor.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"

class MoveOffsetInputDialog : public QDialog
{
    Q_OBJECT
public:
    enum AxisFlag
    {
        AF_None = 0,
        AF_X    = 1 << 0,
        AF_Y    = 1 << 1,
        AF_Z    = 1 << 2,
    };
    using AxisFlags = WD::WDFlags<AxisFlag, unsigned int>;
public:
    MoveOffsetInputDialog(WD::WDCore& app, WD::WDObjectAxisEditor* pEditor, QWidget *parent = nullptr);
    ~MoveOffsetInputDialog();
public:
    AxisFlags& axisFlags()
    {
        return _flags;
    }

    void setAxisDirection(const WD::DVec3& xDir, const WD::DVec3& yDir, const WD::DVec3& zDir) 
    {
        _xDir = xDir;
        _yDir = yDir;
        _zDir = zDir;
    }
protected:
    virtual	void showEvent(QShowEvent* evt) override;
    virtual	void hideEvent(QHideEvent* evt) override;
private slots:
    void slotPushButtonPreviewClicked();
    void slotPushButtonOkClicked();
private:
    /**
     * @brief 界面翻译
    */
    void retranslateUi();
private:
    Ui::MoveOffsetInputDialog ui;
    WD::WDCore& _app;
    WD::WDObjectAxisEditor* _pEditor;
    AxisFlags _flags;

    WD::DVec3 _xDir;
    WD::DVec3 _yDir;
    WD::DVec3 _zDir;

    WD::WDUndoCommand* _pCommand = nullptr;
};
