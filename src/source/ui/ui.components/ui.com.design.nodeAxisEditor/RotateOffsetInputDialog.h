#pragma once

#include <QtWidgets/QDialog>
#include "ui_RotateOffsetInputDialog.h"
#include "core/viewer/objectAxisEditor/WDObjectAxisEditor.h"
#include "core/WDCore.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"

class RotateOffsetInputDialog : public QDialog
{
    Q_OBJECT
public:
    enum AxisType 
    {
        AT_None = 0,
        AT_X,
        AT_Y,
        AT_Z,
    };
public:
    RotateOffsetInputDialog(WD::WDCore& app, WD::WDObjectAxisEditor* pEditor, QWidget *parent = nullptr);
    ~RotateOffsetInputDialog();
public:
    AxisType& axisType() 
    {
        return _type;
    }
    void setRotateParam(const WD::DVec3& axis, const WD::DVec3& center)
    {
        _axis = axis;
        _center = center;
    }
protected:
    virtual	void showEvent(QShowEvent* evt) override; 
    virtual	void hideEvent(QHideEvent* evt) override;
private slots:
    void slotPushButtonPreviewClicked();
    void slotPushButtonOkClicked();
private:
    /**
     * @brief 界面翻译
    */
    void retranslateUi();
private:
    Ui::RotateOffsetInputDialog ui;
    WD::WDCore& _app;
    WD::WDObjectAxisEditor* _pEditor;
    AxisType _type; 
    WD::DVec3 _axis;
    WD::DVec3 _center;

    WD::WDUndoCommand* _pCommand = nullptr;
};
