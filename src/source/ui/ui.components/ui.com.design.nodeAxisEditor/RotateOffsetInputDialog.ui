<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>RotateOffsetInputDialog</class>
 <widget class="QDialog" name="RotateOffsetInputDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>399</width>
    <height>69</height>
   </rect>
  </property>
  <property name="focusPolicy">
   <enum>Qt::StrongFocus</enum>
  </property>
  <property name="windowTitle">
   <string>Value Input</string>
  </property>
  <layout class="QFormLayout" name="formLayout">
   <item row="0" column="0">
    <widget class="QLabel" name="label">
     <property name="text">
      <string>Angle</string>
     </property>
    </widget>
   </item>
   <item row="0" column="1">
    <widget class="QDoubleSpinBox" name="doubleSpinBoxAngle">
     <property name="minimum">
      <double>-999999.000000000000000</double>
     </property>
     <property name="maximum">
      <double>999999.000000000000000</double>
     </property>
    </widget>
   </item>
   <item row="1" column="0" colspan="2">
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonPreview">
       <property name="text">
        <string>Preview</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonOk">
       <property name="text">
        <string>Ok</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonCancel">
       <property name="text">
        <string>Cancel</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
