#pragma once

#include <QDialog>
#include "ui_EditAxisSettingDialog.h"
#include "viewer/WDViewer.h"
#include "core/viewer/objectAxisEditor/WDObjectAxisEditor.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"


class EditAxisSettingDialog : public QDialog
{
	Q_OBJECT
public:
	EditAxisSettingDialog(WD::WDCore& app, QWidget*	parent	= Q_NULLPTR);
	~EditAxisSettingDialog();
protected:
    /**
     * @brief     重写界面显示
     * @param evt 显示事件
    */
    virtual	void showEvent(QShowEvent* evt) override;
    /**
     * @brief     重写界面显示
     * @param evt 关闭事件
    */
    virtual	void hideEvent(QHideEvent* evt) override;
private:
    /**
     * @brief 界面翻译
    */
    void retranslateUi();
private:
	Ui::EditAxisSettingDialog ui;
	WD::WDCore& _app;
};