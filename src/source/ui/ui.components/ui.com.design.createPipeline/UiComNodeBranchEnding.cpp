#include "UiComNodeBranchEnding.h"
#include "core/WDTranslate.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/ICustomWidgets.h"
#include <QDialog>
#include "../../ui.commonLibrary/ui.commonLib.custom/UiCommon.h"
#include "businessModule/design/pipeWork/WDBMDPipeUtils.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"

UiComNodeBranchEnding::UiComNodeBranchEnding(QWidget *parent)
    : QWidget(parent)
{
    ui.setupUi(this);
	this->layout()->setContentsMargins(0, 0, 0, 0);
    
    // ComboBox设置下拉过滤
    ICustomWidgets::QComboBoxPopupCompletion(ui.comboBoxCType);

    // 界面翻译
    this->retranslateUi();

    _dialog = new QDialog(parent);
    _dialog->setLayout(new QHBoxLayout());
    _dialog->layout()->addWidget(this);
    _dialog->setWindowTitle(this->windowTitle());
    //去掉对话框右上角的问号（帮助按钮）
    _dialog->setWindowFlags(_dialog->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    // 绑定事件通知响应
    connect(ui.pushButtonOK, SIGNAL(clicked()), this, SLOT(slotOKClicked()));
}
UiComNodeBranchEnding::~UiComNodeBranchEnding()
{
}

bool        UiComNodeBranchEnding::updateWidget(WD::WDNode::SharedPtr pBranch, WD::PipeFlowDir inseSequence)
{
    if (pBranch == nullptr || !pBranch->isType("BRAN"))
        return false;

    _pBranch        =   pBranch;
    _inseSequence   =   inseSequence;

    // 更新收管长度
    ui.doubleSpinBoxTLength->setValue(this->getTubeLength());

    // 更新连接方式列表
    this->updateCTypeList(*pBranch);

    _dialog->show();
    return true;
}

void        UiComNodeBranchEnding::slotOKClicked()
{
    auto    pBranch     =   _pBranch.lock();
    if (pBranch == nullptr || !pBranch->isType("BRAN"))
        return ;

    auto    length      =   ui.doubleSpinBoxTLength->value();
    if (_inseSequence == WD::PipeFlowDir::PFD_Forward)
    {
        this->branchTailLengthen(*pBranch, length);
    }
    else if (_inseSequence == WD::PipeFlowDir::PFD_Backward)
    {
        //this->branchHeadLengthen(*pBranch, length);
    }
    // 确认过后隐藏窗口
    _dialog->hide();
}

void        UiComNodeBranchEnding::retranslateUi()
{
    Trs("UiComNodeBranchEnding"
        , static_cast<QWidget*>(this)
        , ui.labelTLength
        , ui.labelCType
        , ui.pushButtonOK
    );
}

WD::WDNode::SharedPtr UiComNodeBranchEnding::tubeSPCO(WD::WDNode::SharedPtr pSELE)
{
    WD::WDNode::SharedPtr pSPCONode = pSELE;
    while (pSPCONode->childCount() != 0)
    {
        pSPCONode = pSPCONode->childAt(0);
        if (pSPCONode == nullptr)
            return nullptr;
    }
    return pSPCONode;
}
void        UiComNodeBranchEnding::updateCTypeList(WD::WDNode& branch)
{
    ui.comboBoxCType->clear();

    // 初始化连接类型集
	WD::WDCxtTsBg("UiComNodeBranchExplict");
    ui.comboBoxCType->addItem(QString(), QString());
    ui.comboBoxCType->addItem(WD::WDCxtTs("Open").c_str(), "OPEN");
    ui.comboBoxCType->addItem(WD::WDCxtTs("ButtWeld").c_str(), "BWD");
    ui.comboBoxCType->addItem(WD::WDCxtTs("ThreadedFemale").c_str(), "SCF");
    ui.comboBoxCType->addItem(WD::WDCxtTs("ThreadedMale").c_str(), "SCM");
    ui.comboBoxCType->addItem(WD::WDCxtTs("SocketweldFemale").c_str(), "SWF");
    ui.comboBoxCType->addItem(WD::WDCxtTs("SocketweldMale").c_str(), "SWM");
    ui.comboBoxCType->addItem(WD::WDCxtTs("RFFlangeFBB").c_str(), "FBB");
    ui.comboBoxCType->addItem(WD::WDCxtTs("RFGasketGBB").c_str(), "GBB");
    ui.comboBoxCType->addItem(WD::WDCxtTs("RFFlangeFBD").c_str(), "FBD");
    WD::WDCxtTsEd();
    
    // 根据管件插入顺序获取界面显示连接类型
    std::string showCType;
    if (!branch.isType("BRAN"))
        return ;
    if (_inseSequence == WD::PipeFlowDir::PFD_Forward)
        showCType = branch.getAttribute("Tconnect").toString();
    else if (_inseSequence == WD::PipeFlowDir::PFD_Backward)
        showCType = branch.getAttribute("Hconnect").toString();

    // 设置当前连接类型item
    for (int i = 0; i < ui.comboBoxCType->count(); ++i)
    {
        auto        curData =   ui.comboBoxCType->itemData(i);
        if (!curData.isValid())
            continue;
        std::string cType   =   curData.value<QString>().toUtf8().data();

        if (cType == showCType)
        {
            ui.comboBoxCType->setCurrentIndex(i);
            break;
        }
    }
}

void        UiComNodeBranchEnding::branchTailLengthen(WD::WDNode& branch, double length)
{
    if (!branch.isType("BRAN"))
        return ;

    // 获取分支原数据
    auto preHConnect    =   branch.getAttribute("Hconnect").toString();
    auto preTBore       =   branch.getAttribute("Tbore").toString();
    auto preTPos        =   branch.getAttribute("Tposition WRT World").toDVec3();
    auto preTDir        =   branch.getAttribute("Tdirection WRT World").toDVec3();

    // 分支尾延长
    WD::Vec3 gPos;
    WD::Vec3 gDir;
    std::string bore;
    auto coms = WD::WDBMDPipeUtils::PipeComponents(WD::WDNode::ToShared(&branch));
    if (coms.empty())
    {
        // 若分支没有管件，则以分支头延长
        gPos = branch.getAttribute("Hposition WRT World").toDVec3();
        gDir = branch.getAttribute("Hdirection WRT World").toDVec3();
        // 分支尾管径取头管径
        bore = branch.getAttribute("Hbore").toString();
    }
    else
    {
        // 若分支有管件，则以分支最后一个管件延长
        auto&   pBack       =   coms.back();
        if (pBack == nullptr || !WD::WDBMDPipeUtils::IsPipeComponent(*pBack))
            return ;
        auto    pLeave      =   pBack->keyPoint(pBack->getAttribute("Leave").toInt());
        if (pLeave == nullptr)
            return ;
        auto    leave       =   pLeave->transformed(pBack->globalTransform());
        gPos = leave.position;
        gDir = leave.direction;
        // 分支尾管径取最后一个管件出口管径
        bore = pLeave->bore();
    }
    auto pBranch = WD::WDNode::ToShared(&branch);
    auto func = [this, pBranch](const std::string& cType
        , const std::string& bore
        , const WD::DVec3& pos
        , const WD::DVec3& dir)
    {
        if (!cType.empty())
            pBranch->setAttribute("Hconnect", cType);

        pBranch->setAttribute("Tbore", bore);
        pBranch->updateModel();

        pBranch->setAttribute("Tposition WRT World", pos);
        pBranch->setAttribute("Tdirection WRT World", dir);

        // 更新分支连接
        pBranch->triggerUpdate(true);
        WD::Core().needRepaint();
    };

    std::string cType;
    // 分支尾连接形式
    auto    cTypeData   =   ui.comboBoxCType->currentData();
    if (cTypeData.isValid())
    {
        auto pTCType = cTypeData.value<QString>();
        cType = pTCType.toUtf8().data();
    }

    // undo/redo
    auto bm = WD::Core().currentBM();
    if (bm != nullptr)
    {
        auto cmd = new WD::WDUndoCommand("branchTailLengthen");
        if(cmd != nullptr)
        {
            cmd->setNoticeAfterRedo([bore, gPos, length, gDir, cType, func](const WD::WDUndoCommand&)
            {
                func(cType, bore, gPos + length * gDir, -gDir);
            });
            cmd->setNoticeAfterUndo([preTBore, preTPos, preTDir, preHConnect, func](const WD::WDUndoCommand&)
            {
                func(preHConnect, preTBore, preTPos, preTDir);
            });
            WD::Core().undoStack().push(cmd);
        }
    }
}

double      UiComNodeBranchEnding::getTubeLength()
{
    auto    pBranch =   _pBranch.lock();
    if (pBranch == nullptr)
        return 0.0;

    // 分支子节点个数
    size_t childCount = pBranch->childCount();
    if (childCount == 0)
        return 0.0;

    // 获取管子节点
    WD::WDNode::SharedPtr pTube = nullptr;
    if (_inseSequence == WD::PipeFlowDir::PFD_Forward)
    {
        pTube = pBranch->childAt(childCount - 1);
    }
    else if (_inseSequence == WD::PipeFlowDir::PFD_Backward)
    {
        pTube = pBranch->childAt(0);
    }
    if (pTube == nullptr)
        return 0.0;

    // 管子长度
    return pTube->getAttribute("ltlength").toDouble();
}