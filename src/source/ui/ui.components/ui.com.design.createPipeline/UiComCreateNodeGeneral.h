#pragma once

#include <QObject>
#include "../../wizDesignerApp/IUIInterface.h"
#include "../../wizDesignerApp/UiQtCommon.h"
#include "UiComNodeGeneralWidget.h"

class UiComCreateNodeGeneral 
    : public QObject
	, public IUIInterface
{
    Q_OBJECT
public:
    UiComCreateNodeGeneral(WD::WDCore& app, QObject *parent = nullptr);
    ~UiComCreateNodeGeneral();
public:
    /**
    *   @brief ��Ӧ����ӿ�
    *   @param  action:�˵���Ŀ,�����²˵����İ�ť
    *   @param  data:�˵�����������,�������ļ�������
    */
    virtual bool    onAction(QAction* action,QActionData* data) override;

    /**
    *   @brief ��ȡ�����UI����
    *   @param  name: һ����������ж�����ڣ�������������,name == nullpr,����Ĭ�ϴ���
    */
    virtual QWidget*getWidget(const char* name) override;
private:
    UiComNodeGeneralWidget* _pDialog;
    WD::WDCore&             _app;
};
