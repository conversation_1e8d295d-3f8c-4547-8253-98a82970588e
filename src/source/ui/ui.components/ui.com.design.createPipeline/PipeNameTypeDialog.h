#pragma once

#include <QDialog>
#include "ui_PipeNameTypeDialog.h"
#include "core/WDCore.h"
#include "core/node/WDNode.h"

class PipeNameTypeDialog : public QDialog
{
    Q_OBJECT

public:
    PipeNameTypeDialog(QWidget *parent = Q_NULLPTR);
    ~PipeNameTypeDialog();

public:
    /**
    * @brief 获取当前节点
    */
    inline WD::WDNode::SharedPtr curNode() const
    {
        return _curNode.lock();
    }

private slots:
    /**
    * @brief 连接对象类型下拉选中通知响应
    */
    void slotNameEditFinished();
    /**
    * @brief 当前元素按下通知响应
    */
    void slotOKClicked();
    /**
    * @brief 应用按下通知响应
    */
    void slotCancelClicked();

private:
    // 界面翻译
    void    retranslateUi();

private:
    Ui::PipeNameTypeDialog      ui;
    // 当前节点
    WD::WDNode::WeakPtr         _curNode;
};
