#pragma once

#include <QDialog>
#include "ui_SelectPipeDialog.h"
#include "core/WDCore.h"
#include "core/node/WDNode.h"
#include "PipeCommon.h"
#include "core/common/WDConfig.h"
#include <QTableWidgetItem>
#include "../../ui.commonLibrary/ui.commonLib.weakObject/WeakObject.h"

class SelectPipeDialog : public QDialog
{
    Q_OBJECT

public:
    SelectPipeDialog(WD::WDCore& core, QWidget *parent = Q_NULLPTR);
    ~SelectPipeDialog();
public:
    /**
     * @brief 用pSELENode下符合tBore的叶子节点更新表格，并返回符合条件的spco节点个数
     * @return 返回刷新的管件的个数
    */
    size_t updateWidget(WD::WDNode::SharedPtr pSELENode, const std::string& tBore, bool bShowDescription);
    /**
     * @brief 获取当前元件等级(SPCO)节点
    */
    WD::WDNode::SharedPtr getSPCONode();
protected:
    /**
     * @brief 重写隐藏事件：用于保存隐藏时的位置
     * @param event 
    */
    virtual void hideEvent(QHideEvent* ev);
    /**
     * @brief 重写显示事件：用于恢复关闭或隐藏的位置
     * @param ev 
    */
    virtual void showEvent(QShowEvent* ev);
private slots:
    /**
     * @brief 确认按钮点击槽函数
    */
    void slotPushBtnOkClicked();
    /**
     * @brief 取消按钮点击槽函数
    */
    void slotPushBtnCancleClicked();
private:
    /**
    * @brief 设置item的用户数据
    * @param item
    * @param pNode
    */
    inline void setItemUData(QTableWidgetItem& item, WD::WDNode::SharedPtr pNode)
    {
        QVariant uData;
        uData.setValue(UiWeakObject(pNode));
        item.setData(Qt::UserRole, uData);
    }
    /**
    * @param item
    * @return item对应的节点指针
    */
    inline WD::WDNode::SharedPtr getItemUData(QTableWidgetItem& item)
    {
        QVariant userData = item.data(Qt::UserRole);
        if (!userData.isValid() || userData.isNull())
            return nullptr;
        auto obj = userData.value<UiWeakObject>();
        return obj.subObject<WD::WDNode>();
    }
    /**
     * @brief 界面翻译
    */
    void retranslateUi();
private:
    Ui::SelectPipeDialog ui;
    WD::WDCore& _core;

    // 当前管件的类型
    std::string _type;
};
