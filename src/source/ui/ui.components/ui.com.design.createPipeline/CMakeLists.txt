set(TARGET_NAME ui_com_design_createPipeline)

set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON) 
find_package(Qt5 COMPONENTS Core Widgets REQUIRED)

set(HEADER_FILES
    "PipeCommon.h"
    "PipeComponentThro.h"
    "UiComNodePipeArriveLeavePoint.h"
    "UiComNodePipeComponent.h"
    "UiComCommonParamsWidget.h"
    "UiComNodeBranchExplict.h"
    "UiComNodeBranchConnect.h"
    "UiComCreatePipeline.h"
    "CreateBranchDialog.h"
    "UiComNodePipeLine.h"
    "UiComNodeBranchEnding.h"
    "PipeBranchRender.h"
    "SelectPipeDialog.h"
    "PipeBranchConnectStrategy.h"
    "PipeBranchConnectStrategySub.h"
    "PipeNameTypeDialog.h"
    "PipePositionTypeDialog.h"
)

set(SOURCE_FILES
    "PipeCommon.cpp"
    "PipeComponentThro.cpp"
    "UiComCommonParamsWidget.cpp"
    "UiComCreatePipeline.cpp"
    "UiComNodeBranchConnect.cpp"
    "UiComNodeBranchExplict.cpp"
    "UiComNodePipeArriveLeavePoint.cpp"
    "CreateBranchDialog.cpp"
    "UiComNodePipeComponent.cpp"
    "UiComNodePipeLine.cpp"
    "UiComNodeBranchEnding.cpp"
    "PipeBranchRender.cpp"
    "SelectPipeDialog.cpp"
    "PipeBranchConnectStrategy.cpp"
    "PipeBranchConnectStrategySub.cpp"
    "PipeNameTypeDialog.cpp"
    "PipePositionTypeDialog.cpp"
	"main.cpp"
)

set(FORM_FILES
    "UiComCommonParamsWidget.ui"
    "UiComNodeBranchConnect.ui"
    "UiComNodeBranchExplict.ui"
    "UiComNodePipeArriveLeavePoint.ui"
    "CreateBranchDialog.ui"
    "UiComNodePipeComponent.ui"
    "UiComNodePipeLine.ui"
    "UiComNodeBranchEnding.ui"
    "SelectPipeDialog.ui"
    "PipeNameTypeDialog.ui"
    "PipePositionTypeDialog.ui"
)

add_library(${TARGET_NAME} SHARED
		${HEADER_FILES}
		${SOURCE_FILES}
		${FORM_FILES}
)

if(MSVC)
target_compile_options(${TARGET_NAME} PRIVATE /bigobj)
endif()

add_compile_definitions(${TARGET_NAME} PRIVATE UI_COM_NODE_CREATEPIPELINE_LIB)

target_link_libraries(${TARGET_NAME} PUBLIC wizDesignerCore ui.commonLib.WeakObject ui.commonLib.custom)
target_link_libraries(${TARGET_NAME} PUBLIC Qt5::Core Qt5::Widgets QScintilla qtpropertybrowser)
target_include_directories(${TARGET_NAME} PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})

add_custom_command(
	TARGET ${TARGET_NAME}
	POST_BUILD
    COMMAND             ${CMAKE_COMMAND} -E make_directory ./translations
    COMMAND             ${CMAKE_COMMAND} -E copy_directory ./translations ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/ui/components/${TARGET_NAME}/translations
	WORKING_DIRECTORY	${CMAKE_CURRENT_SOURCE_DIR}
    COMMENT             "copy directory translations..."
)