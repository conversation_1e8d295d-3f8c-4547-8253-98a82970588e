#include "PipeBranchRender.h"
#include "core/common/WDContext.h"
#include "core/common/WDConfig.h"
#include "core/material/WDDrawHelpter.h"
#include "core/WDCore.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/businessModule/typeMgr/WDBMTypeMgr.h"
#include "core/businessModule/design/pipeWork/WDBMDPipeUtils.h"

WD_NAMESPACE_BEGIN

PipeBranchRender::PipeBranchRender(WDCore& app)
    :WDSceneNodeRender(app, "PipeBranchRender")
{
    _branchDirectionDisplayLinesParam.color = Color(50, 255, 0, 255);
    _branchDirectionDisplayLinesParam.lineWidth = 2.0f;
    _branchDirectionDisplayLinesParam.style = WDLineRender::Style::DashLine;

    _pBranchDirectionDisplayGeom = WDGeometryCone::MakeShared(0.0f, 1.0f, 1.0f);

    _pipelineDirectionArrowMat = WDMaterialPhong::MakeShared();
    _pipelineDirectionArrowMat->setDiffuse(Color(255, 50, 0, 255));


    _branchHTPosArrowMat = WDMaterialPhong::MakeShared();
    _branchHTPosArrowMat->setDiffuse(Color(255, 255, 255, 255));

    auto& cfgItemPipeBranch = app.cfg().get<std::string>("pipeBranch").setDisplayText("Pipe branch");
    // 是否显示坡度
    cfgItemPipeBranch.get<bool>("pipeBranch.slope.visible", false)
        .setDisplayText("Whether to display pipe branch slope")
        .bindValuePtr(&_bShowSlope);
    // 坡度显示范围
    cfgItemPipeBranch.get<DVec2>("pipeBranch.slope.range", DVec2(-0.5001, 0.5001))
        .setDisplayText("Displayed pipe branch slope range")
        .bindValuePtr(&_showSlopeRange);
    // 是否显示分支首尾
    cfgItemPipeBranch.get<bool>("pipeBranch.branch.HTPos", false)
        .setDisplayText("Whether to show the direction of the first and last branches")
        .bindValuePtr(&_bShowBranchHTPos);
}

void PipeBranchRender::onNodeAdd(WDNode::SharedPtr pNode)
{
    this->addBranchData(pNode);
}
void PipeBranchRender::onNodeRemove(WDNode::SharedPtr pNode)
{
    this->removeBranchData(pNode);
}
void PipeBranchRender::onNodeUpdate(WDNode::SharedPtr pNode)
{
    WDUnused(pNode);
}
void PipeBranchRender::onNodeClear()
{
    this->clearBranchData();
}

bool PipeBranchRender::empty() const
{
    return _branchNodeSet.empty();
}
bool PipeBranchRender::containsRelatives(const WDNode& node) const
{
    for (auto itr = _branchNodeSet.begin(); itr != _branchNodeSet.end(); ++itr)
    {
        auto pTNode = (*itr);
        if (pTNode == nullptr)
        {
            assert(false && "无效的分支节点!");
            continue;
        }
        // 是否与加入节点相同
        if (pTNode == &node)
            return true;
        // 是否直系祖先或后代
        if (node.isAncestor(*pTNode) || pTNode->isAncestor(node))
            return true;
    }
    return false;
}

void CheckAddPt(FVec3Vector& pts, const DVec3& pt) 
{
    FVec3 fPt(pt);
    if (pts.empty())
    {
        pts.push_back(fPt);
        return;
    }

    // 与前一个点距离过近，忽略
    if (FVec3::DistanceSq(pts.back(), fPt) <= 0.01f)
        return;

    // 只有一个点 或者 强制加入
    if (pts.size() == 1)
    {
        pts.push_back(fPt);
        return;
    }
    // 两个以及以上的点
    const auto& prevPrevPos = pts[pts.size() - 2];
    const auto& prevPos     = pts[pts.size() - 1];
    // 计算是否共线，如果共线, 并且当前公称直径与前一个公称直径相等，则剔除前一个点
    FVec3 dir1 = (fPt - prevPrevPos).normalized();
    FVec3 dir2 = (fPt - prevPos).normalized();
    if (FVec3::InTheSameDirection(dir1, dir2, 0.01f))
    {
        pts.pop_back();
        pts.push_back(fPt);
    }
    else
    {
        pts.push_back(fPt);
    }
}

void PipeBranchRender::updateAabb(WDContext& , const WDScene&)
{
    DAabb3& aabb = this->aabbRef();
    aabb = DAabb3::Null();

    for (auto itr = _branchNodeSet.begin(); itr != _branchNodeSet.end(); ++itr)
    {
        auto pNode = (*itr);
        if (pNode == nullptr || !pNode->flags().hasFlag(WDNode::F_Visible))
            continue;

        aabb.unions(pNode->aabb());
    }
}
void PipeBranchRender::update(WDContext& context, const WDScene& scene)
{
    WDUnused(scene);
    WDUnused(context);

    //清除流向绘制数据
    _branchDirectionDisplayLines.reset();
    _pipelineDirectionArrowInsts.clear();
    //坡管标签
    _tubiSlopeRender.reset();
    //分支起点终点箭头
    _branchHPosArrowInsts.clear();
    _branchTPosArrowInsts.clear();
    // "∠"符号是否翻转
    std::vector<int> emptyXFlips = {};
    std::vector<int> xFlips = { 0 };
    wchar_t slopeTextBuf[WD_PATH_LENGTH] = { 0 };

    FVec3Vector tPts;
    tPts.reserve(1000);

    WDBMAttrDesc* pADescTubiHPos = nullptr;
    WDBMAttrDesc* pADescTubiTPos = nullptr;
    auto pTDescTubi = app().getBMDesign().typeMgr().get("TUBI");
    if (pTDescTubi != nullptr)
    {
        pADescTubiHPos = pTDescTubi->get("Hposition");
        pADescTubiTPos = pTDescTubi->get("Tposition");
        assert(pADescTubiHPos != nullptr && pADescTubiTPos != nullptr);
    }
    WDBMAttrDesc* pADescBranHPos = nullptr;
    WDBMAttrDesc* pADescBranHDir = nullptr;
    WDBMAttrDesc* pADescBranTPos = nullptr;
    WDBMAttrDesc* pADescBranTDir = nullptr;
    auto pTDescBran = app().getBMDesign().typeMgr().get("BRAN");
    if (pTDescBran != nullptr)
    {
        pADescBranHPos = pTDescBran->get("Hposition WRT World");
        pADescBranHDir = pTDescBran->get("Hdirection WRT World");
        pADescBranTPos = pTDescBran->get("Tposition WRT World");
        pADescBranTDir = pTDescBran->get("Tdirection WRT World");
        assert(pADescBranHPos != nullptr && pADescBranHDir != nullptr && pADescBranTPos != nullptr && pADescBranTDir != nullptr);
    }

    for (auto itr = _branchNodeSet.begin(); itr != _branchNodeSet.end(); ++itr)
    {
        auto pBranchNode = (*itr);
        if (pBranchNode == nullptr || !pBranchNode->flags().hasFlag(WDNode::F_Visible))
            continue;

        // 坡管显示
        if (_bShowSlope)
        {
            auto tubis = WDBMDPipeUtils::Tubings(WDNode::ToShared(pBranchNode));
            for (const auto& pTubi : tubis)
            {
                if (pTubi == nullptr)
                    continue;
                if (!pTubi->isType("TUBI"))
                    continue;
                if (pADescTubiHPos == nullptr || pADescTubiTPos == nullptr)
                    continue;
                const auto hPos = pADescTubiHPos->value(*pTubi).toDVec3();
                const auto tPos = pADescTubiTPos->value(*pTubi).toDVec3();
                if (DVec3::DistanceSq(hPos, tPos) <= NumLimits<float>::Epsilon)
                    continue;

                const auto dir = (tPos - hPos).normalize();
                // 水平距离
                const auto dis = DVec2::Distance(hPos.xy(), tPos.xy());
                if (dis <= NumLimits<float>::Epsilon)
                    continue;
                // 竖直管不带有坡度
                if (DVec3::OnTheSameLine(DVec3::AxisZ(), dir, 0.0001))
                    continue;
                // 高程相同，不带有坡度
                const auto h = tPos.z - hPos.z;
                if (Abs(h) <= 0.001f)
                    continue;

                // 计算坡度值
                const auto slope = h / dis * 100.0;
                // 坡度显示范围 [-0.5,0.5]
                if (slope <= _showSlopeRange[0] || slope >= _showSlopeRange[1])
                    continue;

                if (slope >= 0.001)
                {
                    swprintf_s(slopeTextBuf, WD_PATH_LENGTH, L"∠ %.3lf%%", slope);
                }
                else if (slope <= -0.001)
                {
                    swprintf_s(slopeTextBuf, WD_PATH_LENGTH, L"%.3lf%% ∠", Abs(slope));
                    xFlips[0] = (int)(wcslen(slopeTextBuf) - 1);
                }
                else
                {
                    continue;
                }

                // 计算标签文本朝向
                const auto& right = dir;
                auto front = DVec3::AxisY();
                if (DVec3::InTheSameDirection(right, DVec3::AxisZ(), 0.01))
                    front = DVec3::AxisY();
                else if (DVec3::InTheSameDirection(right, DVec3::AxisNZ(), 0.01))
                    front = DVec3::AxisNY();
                else
                    front = DVec3::Cross(DVec3::AxisZ(), right).normalized();

                auto up = DVec3::Cross(right, front).normalized();

                // 获取直管的外径, 用于计算坡度的绘制位置
                auto dim = 0.0;
                auto rDim = WDBMDPipeUtils::GetPipeComponentDiameter(pTubi->getAttribute("Spref").toNodeRef().refNode());
                if (rDim)
                    dim = rDim.value();

                _tubiSlopeRender.add(
                    slopeTextBuf,
                    FVec3((hPos + tPos) * 0.5 + up * dim * 0.6),
                    FVec3(right),
                    FVec3(up),
                    Color::white,
                    14,
                    WDTextRender::HA_Left,
                    WDTextRender::VA_Bottom,
                    FVec2::Zero(),
                    true,
                    slope < 0.0 ? xFlips : emptyXFlips
                );
            }
        }

        // 分支起点终点显示
        if (_bShowBranchHTPos 
            && pADescBranHPos!= nullptr
            && pADescBranHDir != nullptr
            && pADescBranTPos != nullptr
            && pADescBranTDir != nullptr)
        {
            WDInstance inst;
            
            DVec3 hPos = pADescBranHPos->value(*pBranchNode).toDVec3();
            DVec3 hDir = pADescBranHDir->value(*pBranchNode).toDVec3();
            this->genConeInstance(context, hPos, hDir, inst);
            inst._color = Color::red;
            _branchHPosArrowInsts.push_back(inst);

            DVec3 tPos = pADescBranTPos->value(*pBranchNode).toDVec3();
            DVec3 tDir = pADescBranTDir->value(*pBranchNode).toDVec3();
            this->genConeInstance(context, tPos, tDir, inst);
            inst._color = Color::blue;
            _branchTPosArrowInsts.push_back(inst);
        }

        //分支流向
        if (WDBMDPipeUtils::GetPipelineDirectionDisplayEnabled(*pBranchNode)
            && pADescBranHPos != nullptr
            && pADescBranTPos != nullptr)
        {
            tPts.clear();

            // 加入分支头坐标以及管径
            DVec3 hPos = pADescBranHPos->value(*pBranchNode).toDVec3();
            CheckAddPt(tPts, hPos);

            using APos      = std::pair<FVec3, std::string>;
            APos prevAPos   = APos(FVec3(hPos), pBranchNode->getAttribute("Hbore").toString());

            for (auto pChild : pBranchNode->children())
            {
                if (pChild == nullptr || !WD::WDBMDPipeUtils::IsPipeComponent(*pChild))
                    continue;
                auto pArrivePt = pChild->keyPoint(pChild->getAttribute("Arrive").toInt());
                auto pLeavePt = pChild->keyPoint(pChild->getAttribute("Leave").toInt());
                // 先加入流向的线顶点
                if (pArrivePt == nullptr || pLeavePt == nullptr)
                {
                    auto pos = pChild->globalTranslation();
                    CheckAddPt(tPts, pos);
                }
                else
                {
                    auto aPos   = pArrivePt->transformedPosition(pChild->globalTransform());
                    CheckAddPt(tPts, aPos);

                    auto pos    = pChild->globalTranslation();
                    CheckAddPt(tPts, pos);

                    auto lPos   = pLeavePt->transformedPosition(pChild->globalTransform());
                    CheckAddPt(tPts, lPos);

                    // 如果出入口点不共线，则表示有转折，则需要生成箭头
                    if (!DVec3::OnTheSameLine(pArrivePt->direction, pLeavePt->direction, 0.01))
                    {
                        genArrowInstances(_pipelineDirectionArrowInsts
                            , prevAPos.first
                            , FVec3(pos)
                            , prevAPos.second);

                        prevAPos = APos(FVec3(lPos), pLeavePt->bore());
                    }
                }
            }

            // 加入分支尾坐标
            DVec3 tPos = pADescBranTPos->value(*pBranchNode).toDVec3();
            CheckAddPt(tPts, tPos);
            genArrowInstances(_pipelineDirectionArrowInsts
                , prevAPos.first
                , FVec3(tPos)
                , prevAPos.second);

            if (tPts.size() >= 2)
            {
                //生成流向中线
                _branchDirectionDisplayLines.addLineStrip(tPts, _branchDirectionDisplayLinesParam);
            }

        }
    }
}
void PipeBranchRender::render(WDContext& context, const WDScene& scene)
{
    WDUnused(scene);

    WD::WDRLFlag renderLayer = context._renderLayer;
    if (!renderLayer.hasFlag(RL_Scene))
        return;

    //绘制分支流向的线
    this->_branchDirectionDisplayLines.render(context);
    //绘制分支流向箭头
    if (!this->_pipelineDirectionArrowInsts.empty())
    {
        WDDrawHelpter::Guard dg(context, *_pipelineDirectionArrowMat);
        dg.drawInstance(this->_pipelineDirectionArrowInsts
            , *(_pBranchDirectionDisplayGeom->mesh())
            , WDMesh::Solid);
    }

    if (_bShowBranchHTPos)
    {
        WDDrawHelpter::Guard dg(context, *_branchHTPosArrowMat);
        dg.drawInstance(this->_branchHPosArrowInsts
            , *(_pBranchDirectionDisplayGeom->mesh())
            , WDMesh::Solid);
        dg.drawInstance(this->_branchTPosArrowInsts
            , *(_pBranchDirectionDisplayGeom->mesh())
            , WDMesh::Solid);
    }

    if (_bShowSlope)
    {
        _tubiSlopeRender.render(context, true);
    }
}

void PipeBranchRender::addBranchData(WDNode::SharedPtr pNode)
{
    if (pNode == nullptr || !pNode->isType("BRAN"))
        return;
    //保存分支数据以及对应节点
    _branchNodeSet.insert(pNode.get());
}
void PipeBranchRender::removeBranchData(WDNode::SharedPtr pNode)
{
    if (pNode == nullptr)
        return;

    //取消保存分支数据以及对应节点
    auto fItr = _branchNodeSet.find(pNode.get());
    if (fItr != _branchNodeSet.end())
    {
        _branchNodeSet.erase(fItr);
    }
}
void PipeBranchRender::clearBranchData()
{
    _branchNodeSet.clear();
}

void PipeBranchRender::genArrowInstances(WDInstances& outInsts, const FVec3& pt0, const FVec3& pt1, const std::string& bore)
{
    // 根据管径计算箭头直径
    float arrowDim  = 30.0f;
    bool bBoreOk    = false;
    float tValue    = FromString<float>(bore.c_str(), &bBoreOk);
    if (bBoreOk)
        arrowDim    = tValue * 0.75f;

    // 根据箭头直径计算箭头长度
    float arrowLen = arrowDim * 2.2f;

    // 校验两点之间能否容纳该长度的箭头
    FVec3 vec       = pt1 - pt0;
    float dis      = vec.length();
    if (arrowLen * 1.1f >= dis)
        return ;
    // 箭头的旋转
    FVec3 dir   = FVec3(vec.normalized());
    FQuat rot   = FQuat::FromVectors(FVec3::AxisZ(), dir);
    // 箭头的缩放
    FVec3 scale = FVec3(arrowDim, arrowDim, arrowLen);

    float halfArrowLen  = arrowLen * 0.5f;
    float step          = arrowLen * 4.0f;

    // 至少会有一个箭头
    int cnt             = WD::Max(1, static_cast<int>(dis / step));
    outInsts.reserve(outInsts.size() + cnt);
    for (int i = 0; i < cnt; ++i)
    {
        float stepLen = static_cast<float>(i) * step;

        outInsts.push_back(WDInstance());

        outInsts.back()._color = Color::white;
        FVec3 pos = FVec3(pt0) + dir * (stepLen + halfArrowLen);
        outInsts.back()._local = FMat4::Compose(pos, rot, scale);
    }
}
bool PipeBranchRender::genConeInstance(WDContext& context, const DVec3& pos, const DVec3& dir, WDInstance& outInst)
{
    static constexpr const float arrowDim = 12.0f;
    static constexpr const float arrowLen = 22.0f;
    auto pixelU = static_cast<float>(context.camera().pixelU(pos));

    DVec3 arrowPosition = pos;
    DQuat arrowRotation = DQuat::FromVectors(DVec3::AxisZ(), dir);

    outInst._color = Color::white;
    outInst._instanceAttr = 0;
    outInst._instanceId = 0;
    outInst._user = 0;
    outInst._local = FMat4::Compose(FVec3(arrowPosition)
        , FQuat(arrowRotation)
        , FVec3(arrowDim * pixelU, arrowDim * pixelU, arrowLen * pixelU))
        // 圆锥的原始坐标整体上移0.5
        * FMat4::MakeTranslation(0.0f, 0.0f, -0.5f);
    return true;
}

WD_NAMESPACE_END

