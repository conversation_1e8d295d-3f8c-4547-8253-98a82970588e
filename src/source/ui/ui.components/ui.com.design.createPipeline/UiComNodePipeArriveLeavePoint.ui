<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>UiComNodePipeArriveLeavePoint</class>
 <widget class="QWidget" name="UiComNodePipeArriveLeavePoint">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>386</width>
    <height>142</height>
   </rect>
  </property>
  <property name="focusPolicy">
   <enum>Qt::StrongFocus</enum>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_2">
   <item row="2" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonApply">
       <property name="focusPolicy">
        <enum>Qt::StrongFocus</enum>
       </property>
       <property name="text">
        <string>Ok</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonDismiss">
       <property name="focusPolicy">
        <enum>Qt::StrongFocus</enum>
       </property>
       <property name="text">
        <string>Cancel</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="0" column="0">
    <layout class="QGridLayout" name="gridLayout">
     <item row="0" column="0">
      <widget class="QLabel" name="labelCe">
       <property name="text">
        <string>CurrentNode</string>
       </property>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="QLineEdit" name="lineEditCurrentEle">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>24</height>
        </size>
       </property>
       <property name="focusPolicy">
        <enum>Qt::StrongFocus</enum>
       </property>
      </widget>
     </item>
     <item row="1" column="0">
      <widget class="QLabel" name="labelArrive">
       <property name="text">
        <string>Arrive</string>
       </property>
      </widget>
     </item>
     <item row="2" column="0">
      <widget class="QLabel" name="labelLeave">
       <property name="text">
        <string>Leave</string>
       </property>
      </widget>
     </item>
     <item row="1" column="1">
      <widget class="QComboBox" name="comboBoxArrive">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>24</height>
        </size>
       </property>
      </widget>
     </item>
     <item row="2" column="1">
      <widget class="QComboBox" name="comboBoxLeave">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>24</height>
        </size>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="1" column="0">
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <tabstops>
  <tabstop>lineEditCurrentEle</tabstop>
  <tabstop>comboBoxArrive</tabstop>
  <tabstop>comboBoxLeave</tabstop>
  <tabstop>pushButtonApply</tabstop>
  <tabstop>pushButtonDismiss</tabstop>
 </tabstops>
 <resources/>
 <connections/>
</ui>
