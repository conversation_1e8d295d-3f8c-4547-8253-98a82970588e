#pragma once

#include "core/WDCore.h"
#include "core/node/WDNode.h"
#include "core/graphable/WDGraphableInterface.h"

WD_NAMESPACE_BEGIN
/**
* @brief 管线分支连接方式
*/
enum PipeBranchConnectType
{
    Connect_None = 0,
    // 头连接
    Connect_Head,
    // 尾连接
    Connect_Tail
};
/**
* @brief 设计模块数据对象类型转换为字符串名称
*/
constexpr const char* PipeBranchConnectTypeToName(PipeBranchConnectType type)
{
    switch (type)
    {
    case WD::Connect_Head:     return "Head";
        break;
    case WD::Connect_Tail:     return "Tail";
        break;
    default:
        break;
    }
    return "";
}
/**
* @brief 字符串名称转换到设计模块数据对象类型
*/
PipeBranchConnectType PipeBranchConnectTypeFromName(const char* name);

/**
* @brief 管线分支连接策略
*/
class PipeBranchConnectStrategy : public WDObject
{
    WD_DECL_OBJECT(PipeBranchConnectStrategy)
public:
    PipeBranchConnectStrategy();
    ~PipeBranchConnectStrategy();

public:
    /**
    * @brief 提取错误信息
    */
    std::string takeError();
    /**
    * @brief 连接
    * @param connectType 连接方式
    * @param branch 管线分支
    * @param tarNode 目标节点
    * @return 连接成功返回true，连接失败返回false
    */
    virtual bool connect(PipeBranchConnectType connectType, WDNode& branch, WDNode& tarNode)
    {
        WDUnused(connectType);
        WDUnused(branch);
        WDUnused(tarNode);
        error("未成功调用子类连接实现");
        return false;
    }
    /**
    * @brief 连接
    * @param connectType 连接方式
    * @param branch 管线分支
    * @param position 目标位置
    * @return 连接成功返回true，连接失败返回false
    */
    virtual bool connect(PipeBranchConnectType connectType, WDNode& branch, const WD::DVec3& position)
    {
        WDUnused(connectType);
        WDUnused(branch);
        WDUnused(position);
        error("未成功调用子类连接实现");
        return false;
    }

    /**
    * @brief 收集错误信息
    */
    void error(const std::string& info);

protected:
#if 0
    /**
    * @brief 获取指定SPEC下指定管径的SPCO等级节点
    */
    WDNode::SharedPtr getBoreSPCOFromSPEC(WD::WDNode::SharedPtr pSPEC, const std::string& bore);
#endif
    /**
    * @brief 分支头连接到两通管件
    */
    bool HeadConnectPipeComponent(WDNode& branch, WDNode& component);
    /**
    * @brief 分支尾连接到两通管件
    */
    bool TailConnectPipeComponent(WDNode& branch, WDNode& component);
    /**
    * @brief 设置分支头属性(坐标，朝向)
    * @param pBranch 分支
    * @param pConnectObj 连接对象
    * @param pPoint 连接关键点
    */
    bool setBranchHAttr(WDNode& branch, WDNode::SharedPtr pConnectObj, const WDKeyPoint* point);
    /**
    * @brief 设置分支尾属性(坐标，朝向)
    * @param pBranch 分支
    * @param pConnectObj 连接对象
    * @param pPoint 连接关键点
    */
    bool setBranchTAttr(WDNode& branch, WDNode::SharedPtr pConnectObj, const WDKeyPoint* point);

private:
    // 错误信息
    std::vector<std::string>    _errors;
};

WD_NAMESPACE_END
