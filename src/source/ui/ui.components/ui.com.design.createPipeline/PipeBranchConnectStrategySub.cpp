#include "PipeBranchConnectStrategySub.h"
#include "core/message/WDMessage.h"
#include "core/math/DirectionParser.h"
#include "PipeCommon.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiCommon.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "businessModule/design/pipeWork/WDBMDPipeUtils.h"

WD_NAMESPACE_BEGIN

PipeBranchConnectStrategyNozzle::PipeBranchConnectStrategyNozzle()
{
}
PipeBranchConnectStrategyNozzle::~PipeBranchConnectStrategyNozzle()
{
}

bool PipeBranchConnectStrategyNozzle::connect(PipeBranchConnectType connectType, WDNode& branch, WDNode& tarNode)
{
    if (!branch.isType("BRAN") || !tarNode.isType("NOZZ"))
        return false;

    // 断开已经连接的对象
    DisconnectHRef(tarNode);

    // 连接点属性
    auto    connPt      =   tarNode.keyPoint(1);
    if (connPt == nullptr)
        return false;
    auto    gPos        =   connPt->transformedPosition(tarNode.globalTransform());
    auto    gDir        =   connPt->transformedDirection(tarNode.globalRSTransform());
    auto    bore        =   connPt->bore();
    auto    connType    =   connPt->connType();

    switch (connectType)
    {
    case WD::Connect_None:
        break;
    case WD::Connect_Head:
        {
            // 设置分支头坐标朝向
            branch.setAttribute("Hposition WRT World", gPos);
            branch.setAttribute("Hdirection WRT World", gDir);

            // 设置分支头管径
            branch.setAttribute("Hbore", bore);
            // 更新分支头管子等级引用
            UpdateHStubeByself(branch);

            // 设置分支头连接方式
            branch.setAttribute("Hconnect", connType);
            
            // 原分支头连接的管件 断开连接
            DisconnectHRef(branch);
            // 设置管嘴连接对象
            tarNode.setAttribute("Cref", WD::WDBMNodeRef(WDNode::ToShared(&branch)));
            // 设置头连接对象
            branch.setAttribute("Href", WDBMNodeRef(WDNode::ToShared(&tarNode)));

            //更新分支连接
            branch.triggerUpdate(true);
            return true;
        }
        break;
    case WD::Connect_Tail:
        {
            // 设置分支尾坐标朝向
            branch.setAttribute("Tposition WRT World", gPos);
            branch.setAttribute("Tdirection WRT World", gDir);

            // 设置分支尾管径
            branch.setAttribute("Tbore", bore);

            // 设置分支尾连接方式
            branch.setAttribute("Tconnect", connType);
            
            // 原分支尾连接管件 断开连接
            DisconnectTRef(branch);
            // 设置管嘴连接对象
            tarNode.setAttribute("Cref", WD::WDBMNodeRef(WDNode::ToShared(&branch)));
            // 设置尾连接对象
            branch.setAttribute("Tref", WDBMNodeRef(WDNode::ToShared(&tarNode)));

            //更新分支连接
            branch.triggerUpdate(true);
            return true;
        }
        break;
    default:
        break;
    }
    return false;
}


PipeBranchConnectStrategyTee::PipeBranchConnectStrategyTee()
{
}
PipeBranchConnectStrategyTee::~PipeBranchConnectStrategyTee()
{
}

bool PipeBranchConnectStrategyTee::connect(PipeBranchConnectType connectType, WDNode& branch, WDNode& tarNode)
{
    if (!branch.isType("BRAN"))
        return false;
    
    if (!tarNode.isType("TEE"))
        return false;

    // 断开已经连接的对象
    DisconnectHRef(tarNode);

    // 分叉点属性
    auto    forkPt      =   tarNode.keyPoint(WD::WDBMDPipeUtils::Fork(tarNode));
    if (forkPt == nullptr)
        return false;

    auto    gPos        =   forkPt->transformedPosition(tarNode.globalTransform());
    auto    gDir        =   forkPt->transformedDirection(tarNode.globalRSTransform());
    auto    bore        =   forkPt->bore();
    auto    connType    =   forkPt->connType();

    switch (connectType)
    {
    case WD::Connect_None:
        break;
    case WD::Connect_Head:
        {
            if (&branch == tarNode.parent().get())
            {
                // 同分支做二通管件处理
                return this->HeadConnectPipeComponent(branch, tarNode);
            }
            else
            {
                // 设置分支头坐标朝向
                branch.setAttribute("Hposition WRT World", gPos);
                branch.setAttribute("Hdirection WRT World", gDir);

                // 分支头管径
                branch.setAttribute("Hbore", bore);
                // 更新分支管子等级引用
                UpdateHStubeByself(branch);

                // 分支头连接方式
                branch.setAttribute("Hconnect", connType);
            
                // 原分支头连接管件断开连接
                DisconnectHRef(branch);
                // 设置三通连接对象
                tarNode.setAttribute("Cref", WD::WDBMNodeRef(WDNode::ToShared(&branch)));
                // 设置头连接对象
                branch.setAttribute("Href", WDBMNodeRef(WDNode::ToShared(&tarNode)));

                //更新分支连接
                branch.triggerUpdate(true);
                return true;
            }
        }
        break;
    case WD::Connect_Tail:
        {
            if (&branch == tarNode.parent().get())
            {
                // 同分支做二通管件处理
                return this->TailConnectPipeComponent(branch, tarNode);
            }
            else
            {
                // 设置分支尾坐标朝向
                branch.setAttribute("Tposition WRT World", gPos);
                branch.setAttribute("Tdirection WRT World", gDir);

                // 分支尾管径
                branch.setAttribute("Tbore", bore);

                // 分支尾连接方式
                branch.setAttribute("Tconnect", connType);
            
                // 原分支尾连接管件断开连接
                DisconnectTRef(branch);
                // 设置三通连接对象
                tarNode.setAttribute("Cref", WD::WDBMNodeRef(WDNode::ToShared(&branch)));
                // 设置尾连接对象
                branch.setAttribute("Tref", WDBMNodeRef(WDNode::ToShared(&tarNode)));

                //更新分支连接
                branch.triggerUpdate(true);
                return true;
            }
        }
        break;
    default:
        break;
    }
    return false;
}


PipeBranchConnectStrategyOlet::PipeBranchConnectStrategyOlet()
{
}
PipeBranchConnectStrategyOlet::~PipeBranchConnectStrategyOlet()
{
}

bool PipeBranchConnectStrategyOlet::connect(PipeBranchConnectType connectType, WDNode& branch, WDNode& tarNode)
{
    if (!branch.isType("BRAN"))
        return false;
    
    if (!tarNode.isType("OLET"))
        return false;

    // 断开已经连接的对象
    DisconnectHRef(tarNode);

    // 分叉点属性
    auto    forkPt      =   tarNode.keyPoint(WD::WDBMDPipeUtils::Fork(tarNode));
    if (forkPt == nullptr)
        return false;

    auto    gPos        =   forkPt->transformedPosition(tarNode.globalTransform());
    auto    gDir        =   forkPt->transformedDirection(tarNode.globalRSTransform());
    auto    bore        =   forkPt->bore();
    auto    connType    =   forkPt->connType();

    switch (connectType)
    {
    case WD::Connect_None:
        break;
    case WD::Connect_Head:
        {
            if (&branch == tarNode.parent().get())
            {
                // 同分支做二通管件处理
                return this->HeadConnectPipeComponent(branch, tarNode);
            }
            else
            {
                // 设置分支头坐标朝向
                branch.setAttribute("Hposition WRT World", gPos);
                branch.setAttribute("Hdirection WRT World", gDir);

                // 分支头管径
                branch.setAttribute("Hbore", bore);
                // 更新分支管子等级引用
                UpdateHStubeByself(branch);

                // 分支头连接方式
                branch.setAttribute("Hconnect", connType);
            
                // 原分支头连接管件断开连接
                DisconnectHRef(branch);
                // 设置支管台连接对象
                tarNode.setAttribute("Cref", WD::WDBMNodeRef(WDNode::ToShared(&branch)));
                // 设置头连接对象
                branch.setAttribute("Href", WDBMNodeRef(WDNode::ToShared(&tarNode)));

                //更新分支连接
                branch.triggerUpdate(true);
                return true;
            }
        }
        break;
    case WD::Connect_Tail:
        {
            if (&branch == tarNode.parent().get())
            {
                // 同分支做二通管件处理
                return this->TailConnectPipeComponent(branch, tarNode);
            }
            else
            {
                // 设置分支尾坐标朝向
                branch.setAttribute("Tposition WRT World", gPos);
                branch.setAttribute("Tdirection WRT World", gDir);

                // 分支尾管径
                branch.setAttribute("Tbore", bore);

                // 分支尾连接方式
                branch.setAttribute("Tconnect", connType);
            
                // 原分支尾连接管件断开连接
                DisconnectTRef(branch);
                // 设置支管台连接对象
                tarNode.setAttribute("Cref", WD::WDBMNodeRef(WDNode::ToShared(&branch)));
                // 设置尾连接对象
                branch.setAttribute("Tref", WDBMNodeRef(WDNode::ToShared(&tarNode)));

                //更新分支连接
                branch.triggerUpdate(true);
                return true;
            }
        }
        break;
    default:
        break;
    }
    return false;
}


PipeBranchConnectStrategyElbow::PipeBranchConnectStrategyElbow()
{
}
PipeBranchConnectStrategyElbow::~PipeBranchConnectStrategyElbow()
{
}

bool PipeBranchConnectStrategyElbow::connect(PipeBranchConnectType connectType, WDNode& branch, WDNode& tarNode)
{
    if (!branch.isType("BRAN"))
        return false;

    switch (connectType)
    {
    case WD::Connect_None:
        break;
    case WD::Connect_Head:
        {
            return HeadConnectPipeComponent(branch, tarNode);
        }
        break;
    case WD::Connect_Tail:
        {
            return TailConnectPipeComponent(branch, tarNode);
        }
        break;
    default:
        break;
    }
    return false;
}


PipeBranchConnectStrategyReducer::PipeBranchConnectStrategyReducer()
{
}
PipeBranchConnectStrategyReducer::~PipeBranchConnectStrategyReducer()
{
}

bool PipeBranchConnectStrategyReducer::connect(PipeBranchConnectType connectType, WDNode& branch, WDNode& tarNode)
{
    if (!branch.isType("BRAN"))
        return false;

    switch (connectType)
    {
    case WD::Connect_None:
        break;
    case WD::Connect_Head:
        {
            return HeadConnectPipeComponent(branch, tarNode);
        }
        break;
    case WD::Connect_Tail:
        {
            return TailConnectPipeComponent(branch, tarNode);
        }
        break;
    default:
        break;
    }
    return false;
}


PipeBranchConnectStrategyFlange::PipeBranchConnectStrategyFlange()
{
}
PipeBranchConnectStrategyFlange::~PipeBranchConnectStrategyFlange()
{
}

bool PipeBranchConnectStrategyFlange::connect(PipeBranchConnectType connectType, WDNode& branch, WDNode& tarNode)
{
    if (!branch.isType("BRAN"))
        return false;

    switch (connectType)
    {
    case WD::Connect_None:
        break;
    case WD::Connect_Head:
        {
            return HeadConnectPipeComponent(branch, tarNode);
        }
        break;
    case WD::Connect_Tail:
        {
            return TailConnectPipeComponent(branch, tarNode);
        }
        break;
    default:
        break;
    }
    return false;
}


PipeBranchConnectStrategyMultiway::PipeBranchConnectStrategyMultiway()
{
}
PipeBranchConnectStrategyMultiway::~PipeBranchConnectStrategyMultiway()
{
}

bool PipeBranchConnectStrategyMultiway::connect(PipeBranchConnectType connectType, WDNode& branch, WDNode& tarNode)
{
    if (!branch.isType("BRAN"))
        return false;

    switch (connectType)
    {
    case WD::Connect_None:
        break;
    case WD::Connect_Head:
        {
            return HeadConnectPipeComponent(branch, tarNode);
        }
        break;
    case WD::Connect_Tail:
        {
            return TailConnectPipeComponent(branch, tarNode);
        }
        break;
    default:
        break;
    }
    return false;
}


PipeBranchConnectStrategyBranchHead::PipeBranchConnectStrategyBranchHead()
{
}
PipeBranchConnectStrategyBranchHead::~PipeBranchConnectStrategyBranchHead()
{
}

bool PipeBranchConnectStrategyBranchHead::connect(PipeBranchConnectType connectType, WDNode& branch, WDNode& tarNode)
{
    if (&branch == tarNode.parent().get())
    {
        // 目标分支不能与当前分支相同
        WD_ERROR_T("PipeBranchConnectStrategy", "Not is same branch!");
        return false;
    }

    if (!branch.isType("BRAN"))
        return false;

    // 获取目标分支
    auto    pTarBranch      =   WD::Core().getBMDesign().findParentWithType(tarNode, "TUBI");
    if (pTarBranch == nullptr || !pTarBranch->isType("BRAN"))
        return false;
    // 断开已经连接的对象
    DisconnectHRef(tarNode);

    switch (connectType)
    {
    case WD::Connect_None:
        break;
    case WD::Connect_Head:
        {
            int res = WD_QUESTION_T("PipeBranchConnectStrategy", "Head connect to head?");
            if (res == 0)
            {
                // 坐标朝向
                WD::Vec3 gTarPos = pTarBranch->getAttribute("Hposition WRT World").toDVec3();
                branch.setAttribute("Hposition WRT World", gTarPos);
                WD::Vec3 gTarDir = pTarBranch->getAttribute("Hdirection WRT World").toDVec3();
                branch.setAttribute("Hdirection WRT World", -gTarDir);
                // 头管径
                branch.setAttribute("Hbore", pTarBranch->getAttribute("Hbore"));
                // 更新分支管子等级
                UpdateHStubeByself(branch);

                // 连接方式
                branch.setAttribute("Hconnect", pTarBranch->getAttribute("Hconnect"));

                // 原分支头连接对象断开连接
                DisconnectHRef(branch);
                // 头连接
                branch.setAttribute("Href", WDBMNodeRef(pTarBranch));
                // 目标分支头连接
                pTarBranch->setAttribute("Href", WDBMNodeRef(WDNode::ToShared(&branch)));

                //更新分支连接
                branch.triggerUpdate(true);
                return true;
            }
        }
        break;
    case WD::Connect_Tail:
        {
            // 坐标朝向
            WD::Vec3 gTarPos = pTarBranch->getAttribute("Hposition WRT World").toDVec3();
            branch.setAttribute("Tposition WRT World", gTarPos);
            WD::Vec3 gTarDir = pTarBranch->getAttribute("Hdirection WRT World").toDVec3();
            branch.setAttribute("Tdirection WRT World", -gTarDir);

            // 尾管径
            branch.setAttribute("Tbore", pTarBranch->getAttribute("Hbore"));

            // 连接方式
            branch.setAttribute("Tconnect", pTarBranch->getAttribute("Hconnect"));

            // 原分支尾连接对象断开连接
            DisconnectTRef(branch);
            // 尾连接
            branch.setAttribute("Tref", WDBMNodeRef(pTarBranch));
            // 目标分支头连接
            pTarBranch->setAttribute("Href", WDBMNodeRef(WDNode::ToShared(&branch)));

            //更新分支连接
            branch.triggerUpdate(true);
            return true;
        }
        break;
    default:
        break;
    }
    return false;
}


PipeBranchConnectStrategyBranchTail::PipeBranchConnectStrategyBranchTail()
{
}
PipeBranchConnectStrategyBranchTail::~PipeBranchConnectStrategyBranchTail()
{
}

bool PipeBranchConnectStrategyBranchTail::connect(PipeBranchConnectType connectType, WDNode& branch, WDNode& tarNode)
{
    if (&branch == tarNode.parent().get())
    {
        // 目标分支不能与当前分支相同
        WD_ERROR_T("PipeBranchConnectStrategy", "Not is same branch!");
        return false;
    }

    if (!branch.isType("BRAN"))
        return false;

    // 获取目标分支
    auto    pTarBranch      =   WD::Core().getBMDesign().findParentWithType(tarNode, "TUBI");
    if (pTarBranch == nullptr || !pTarBranch->isType("BRAN"))
        return false;
    // 断开已经连接的对象
    DisconnectTRef(tarNode);

    switch (connectType)
    {
    case WD::Connect_None:
        break;
    case WD::Connect_Head:
        {
            // 坐标朝向
            WD::Vec3 gTarTPos = pTarBranch->getAttribute("Tposition WRT World").toDVec3();
            branch.setAttribute("Hposition WRT World", gTarTPos);
            WD::Vec3 gTarTDir = pTarBranch->getAttribute("Tdirection WRT World").toDVec3();
            branch.setAttribute("Hdirection WRT World", -gTarTDir);

            // 头管径
            branch.setAttribute("Hbore", pTarBranch->getAttribute("Tbore"));
            // 管子等级
            UpdateHStubeByself(branch);

            // 连接方式
            branch.setAttribute("Hconnect", pTarBranch->getAttribute("Tconnect"));

            // 原分支头连接对象断开连接
            DisconnectHRef(branch);
            // 头连接
            branch.setAttribute("Href", WDBMNodeRef(pTarBranch));
            // 目标分支尾连接
            pTarBranch->setAttribute("Tref", WDBMNodeRef(WDNode::ToShared(&branch)));

            //更新分支连接
            branch.triggerUpdate(true);
            return true;
        }
        break;
    case WD::Connect_Tail:
        {
            int res = WD_QUESTION_T("ErrorPromptPipeBranchConnect", "Tail connect to tail?");
            if (res == 0)
            {
                // 坐标朝向
                WD::Vec3 gTarTPos = pTarBranch->getAttribute("Tposition WRT World").toDVec3();
                branch.setAttribute("Tposition WRT World", gTarTPos);
                WD::Vec3 gTarTDir = pTarBranch->getAttribute("Tdirection WRT World").toDVec3();
                branch.setAttribute("Tdirection WRT World", -gTarTDir);

                // 尾管径
                branch.setAttribute("Tbore", pTarBranch->getAttribute("Tbore"));

                // 连接方式
                branch.setAttribute("Tconnect", pTarBranch->getAttribute("Tconnect"));

                // 原分支尾连接对象断开连接
                DisconnectTRef(branch);
                // 尾连接
                branch.setAttribute("Tref", WDBMNodeRef(pTarBranch));
                // 目标分支尾连接
                pTarBranch->setAttribute("Tref", WDBMNodeRef(WDNode::ToShared(&branch)));

                //更新分支连接
                branch.triggerUpdate(true);
                return true;
            }
        }
        break;
    default:
        break;
    }
    return false;
}


PipeBranchConnectStrategyFirstMember::PipeBranchConnectStrategyFirstMember()
{
}
PipeBranchConnectStrategyFirstMember::~PipeBranchConnectStrategyFirstMember()
{
}

bool PipeBranchConnectStrategyFirstMember::connect(PipeBranchConnectType connectType, WDNode& branch, WDNode& tarNode)
{
    WDUnused(tarNode);
    if (!branch.isType("BRAN"))
        return false;

    switch (connectType)
    {
    case WD::Connect_None:
        break;
    case WD::Connect_Head:
        {
            // 分支下管件列表
            WDNode::Nodes   coms        = WDBMDPipeUtils::PipeComponents(WDNode::ToShared(&branch));
            if (coms.empty())
                return false;
            // 第一个管件
            auto&           pFront      =   coms.front();
            if (pFront == nullptr || !WD::WDBMDPipeUtils::IsPipeComponent(*pFront))
                return false;

            // 设置分支头坐标朝向
            auto    pArrive =   pFront->keyPoint(pFront->getAttribute("Arrive").toInt());
            if (pArrive == nullptr)
                return false;
            auto    gPos    =   pArrive->transformedPosition(pFront->globalTransform());
            branch.setAttribute("Hposition WRT World", gPos);
            auto    gDir    =   pArrive->transformedDirection(pFront->globalRSTransform());
            branch.setAttribute("Hdirection WRT World", -gDir);

            // 头管径
            branch.setAttribute("Hbore", pArrive->bore());
            // 更新头管子等级引用
            UpdateHStubeByself(branch);

            // 分支头连接方式
            branch.setAttribute("Hconnect", pArrive->connType());

            // 原分支头连接对象断开连接
            DisconnectHRef(branch);
            // 分支头连接置空
            branch.setAttribute("Href", WDBMNodeRef(nullptr));

            //更新分支连接
            branch.triggerUpdate(true);
            return true;
        }
        break;
    case WD::Connect_Tail:
        {
            // 获取分支下管件列表
            WDNode::Nodes coms = WDBMDPipeUtils::PipeComponents(WDNode::ToShared(&branch));
            if (coms.empty())
            {
                this->error(WD::WDTs("PipeBranchConnectStrategy", "BranchNotPipeFitting"));
                return false;
            }
            else
            {
                this->error(WD::WDTs("PipeBranchConnectStrategy", "BranchHeadSelectFirst"));
                return false;
            }
        }
        break;
    default:
        break;
    }
    return false;
}


PipeBranchConnectStrategyLastMember::PipeBranchConnectStrategyLastMember()
{
}
PipeBranchConnectStrategyLastMember::~PipeBranchConnectStrategyLastMember()
{
}

bool PipeBranchConnectStrategyLastMember::connect(PipeBranchConnectType connectType, WDNode& branch, WDNode& tarNode)
{
    WDUnused(tarNode);
    if (!branch.isType("BRAN"))
        return false;

    switch (connectType)
    {
    case WD::Connect_None:
        break;
    case WD::Connect_Head:
        {
            // 获取分支下管件列表
            WDNode::Nodes coms = WDBMDPipeUtils::PipeComponents(WDNode::ToShared(&branch));
            if (coms.empty())
            {
                this->error(WD::WDTs("PipeBranchConnectStrategy", "BranchNotPipeFitting"));
                return false;
            }
            else
            {
                this->error(WD::WDTs("PipeBranchConnectStrategy", "BranchTailSelectLast"));
                return false;
            }
        }
        break;
    case WD::Connect_Tail:
        {
            // 获取分支下管件列表
            WDNode::Nodes   coms        = WDBMDPipeUtils::PipeComponents(WDNode::ToShared(&branch));
            if (coms.empty())
                return false;
            // 获取第一个管件等级引用
            auto&           pBack       =   coms.back();
            if (pBack == nullptr || !WD::WDBMDPipeUtils::IsPipeComponent(*pBack))
                return false;

            // 设置分支尾坐标朝向
            auto    pLeave =   pBack->keyPoint(pBack->getAttribute("Leave").toInt());
            if (pLeave == nullptr)
                return false;
            auto    gPos    =   pLeave->transformedPosition(pBack->globalTransform());
            branch.setAttribute("Tposition WRT World", gPos);
            auto    gDir    =   pLeave->transformedDirection(pBack->globalRSTransform());
            branch.setAttribute("Tdirection WRT World", -gDir);

            // 尾管径
            branch.setAttribute("Tbore", pLeave->bore());

            // 分支尾连接方式
            branch.setAttribute("Tconnect", pLeave->connType());

            // 原分支尾连接对象断开连接
            DisconnectTRef(branch);
            // 分支尾连接置空
            branch.setAttribute("Tref", WDBMNodeRef(nullptr));

            //更新分支连接
            branch.triggerUpdate(true);
            return true;
        }
        break;
    default:
        break;
    }
    return false;
}


PipeBranchConnectStrategyName::PipeBranchConnectStrategyName()
{
}
PipeBranchConnectStrategyName::~PipeBranchConnectStrategyName()
{
}

bool PipeBranchConnectStrategyName::connect(PipeBranchConnectType connectType, WDNode& branch, WDNode& tarNode)
{
    WDUnused(tarNode);
    if (!branch.isType("BRAN"))
        return false;

    switch (connectType)
    {
    case WD::Connect_None:
        break;
    case WD::Connect_Head:
        {
        }
        break;
    case WD::Connect_Tail:
        {
        }
        break;
    default:
        break;
    }
    return false;
}


PipeBranchConnectStrategyPosition::PipeBranchConnectStrategyPosition()
{
}
PipeBranchConnectStrategyPosition::~PipeBranchConnectStrategyPosition()
{
}

bool PipeBranchConnectStrategyPosition::connect(PipeBranchConnectType connectType, WDNode& branch, const WD::DVec3& position)
{
    if (!branch.isType("BRAN"))
        return false;

    switch (connectType)
    {
    case WD::Connect_None:
        break;
    case WD::Connect_Head:
        {
            // 断开连接
            DisconnectHRef(branch);
            // 头连接对象 属性(连接对象 置空
            branch.setAttribute("Href", WDBMNodeRef(nullptr));
            // 设置分支头位置
            branch.setAttribute("Hposition WRT World", position);
            // 设置分支头方向为第一个管件入口点方向
            //this->autoBranchHDirection(branch);
            //更新分支连接
            branch.triggerUpdate(true);
        }
        break;
    case WD::Connect_Tail:
        {
            // 断开连接
            DisconnectTRef(branch);
            // 尾连接对象 属性(连接对象 置空
            branch.setAttribute("Tref", WDBMNodeRef(nullptr));
            // 设置分支尾位置
            branch.setAttribute("Tposition WRT World", position);
            // 设置分支尾方向为最后个管件出口点方向
            //this->autoBranchTDirection(branch);
            //更新分支连接
            branch.triggerUpdate(true);
        }
        break;
    default:
        break;
    }
    return false;
}
void PipeBranchConnectStrategyPosition::autoBranchHDirection(WD::WDNode& branch)
{
    WD::WDNode::Nodes       coms        = WDBMDPipeUtils::PipeComponents(WD::WDNode::ToShared(&branch));
    if (coms.empty())
        return ;
    // 分支第一个管件
    WD::WDNode::SharedPtr   pFront      =   coms.front();
    if (pFront == nullptr || !WD::WDBMDPipeUtils::IsPipeComponent(*pFront))
        return ;

    // 设置分支头方向为分支第一个管件入口点方向
    auto                    pArrivePt   =   pFront->keyPoint(pFront->getAttribute("Arrive").toInt());
    if (pArrivePt == nullptr)
        return ;
    WD::Vec3                arriveDir   =   pArrivePt->transformedDirection(pFront->globalTransform());
    branch.setAttribute("Hdirection WRT World", -arriveDir);
}
void PipeBranchConnectStrategyPosition::autoBranchTDirection(WD::WDNode& branch)
{
    WD::WDNode::Nodes       coms        = WDBMDPipeUtils::PipeComponents(WD::WDNode::ToShared(&branch));
    if (coms.empty())
        return ;
    // 分支最后一个管件
    WD::WDNode::SharedPtr   pBack       =   coms.back();
    if (pBack == nullptr || !WD::WDBMDPipeUtils::IsPipeComponent(*pBack))
        return ;
    // 设置分支尾方向为分支最后一个管件出口点方向
    auto                    pLeavePt    =   pBack->keyPoint(pBack->getAttribute("Leave").toInt());
    if (pLeavePt == nullptr)
        return;
    WD::Vec3                leaveDir    =   pLeavePt->transformedDirection(pBack->globalTransform());
    branch.setAttribute("Tdirection WRT World", -leaveDir);
}

WD_NAMESPACE_END