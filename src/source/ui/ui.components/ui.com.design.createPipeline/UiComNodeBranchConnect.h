#pragma once

#include <QWidget>
#include "ui_UiComNodeBranchConnect.h"
#include "viewer/capturePositioning/WDCapturePositioning.h"
#include "WDCore.h"

class UiComNodeBranchConnect : public QWidget
    , public WD::WDCapturePositioningMonitor
{
    Q_OBJECT

public:
    /**
    * @brief 连接节点属性
    */
    struct ConnAttr
    {
        // 节点
        WD::WDNode::SharedPtr   pConn   =   nullptr;
        // 位置
        WD::DVec3               pos;
        // 朝向（默认朝向X方向、一个有效的方向）
        WD::DVec3               dir = WD::DVec3(1,0,0);
        // 管径
        std::string             bore;
        // 连接方式
        std::string             connectType;

    };

    // 连接对象类型
    enum ConnectObjType
    {
        // 管嘴
        COT_Nozz = 0,
        // 三通
        COT_Tee,
        // 支管台
        COT_Olet,
        // 分支头
        COT_BranchHead,
        // 分支尾
        COT_BranchTail,
    };

private:
    // 分支头
    static constexpr const char* Branch_Head = "Branch_Head";
    // 分支尾
    static constexpr const char* Branch_Tail = "Branch_Tail";

public:
    UiComNodeBranchConnect(QWidget *parent = Q_NULLPTR);
    ~UiComNodeBranchConnect();

protected:
    virtual void showEvent(QShowEvent* evt) override;
    virtual void hideEvent(QHideEvent* evt) override;

public:
    /**
    * @brief 获取连接属性
    */
    ConnAttr    connAttr() const;
    bool        checkUpdate() const;
    /**
    * @brief 设置连接节点的连接对象节点
    * @param cRefNode 连接对象节点
    */
    void        setConnectCRefNode(WD::WDNode::SharedPtr cRefNode);

    /**
    * @brief 禁用拾取
    */
    inline void inactivePick()
    {
        ui.checkBoxPick->setChecked(false);
    }

    /**
     * @brief 获取连接对象类型
     * @return 当前的连接对象类型
    */
    inline ConnectObjType getConnectObjType()const
    {
        return _conObjType;
    }
    /**
     * @brief 获取连接的节点属性名
     * @return 属性名称
    */
    std::string getAttrName()const;
    /**
     * @brief 重置该界面
    */
    void reset()
    {
        // 连接点置空
        _pConnect.reset();
        // 清空节点名称的显示
        ui.lineEditName->clear();
    }
public:
    /**
    * @brief 设置group空间title
    */
    inline void setGroupName(const std::string& text)
    {
        ui.groupBox->setTitle(QString::fromUtf8(text.c_str()));
    }
    /**
    * @brief 设置连接label
    */
    inline void setConnectToLabel(const std::string& text)
    {
        ui.connectToLabel->setText(QString::fromUtf8(text.c_str()));
    }


signals:
    /**
    * @brief 启用拾取通知
    */
    void signalActivePick();

private slots:
    /**
    * @brief 拾取状态checkBox状态更改事件通知响应
    * @param state checkBox状态
    */
    void slotCheckBoxPickClicked();
    /**
    * @brief 拾取节点类型更改事件通知响应
    */
    void slotConnToComboBoxCurrentIndexChanged(int index);

private:
    virtual void    onResult(const WD::WDCapturePositioningResult& result
        , bool& existFlag
        , const WD::WDCapturePositioning& sender) override;
    virtual void    onDeactived(const WD::WDCapturePositioning& sender) override;
    virtual bool    onNodeFilter(WD::WDNode& node, const WD::WDCapturePositioning& sender) override;
private:
    /**
    * @brief 校验连接对象 并 更新数据
    */
    bool            checkAndUpdate(WD::WDNode& obj);

    /**
    * @brief 界面翻译
    */
    void            retranslateUi();

private:
    Ui::UiComNodeBranchConnect      ui;
    // 当前连接节点
    WD::WDNode::WeakPtr             _pConnect;
    // 连接对象类型
    ConnectObjType                  _conObjType;
};
