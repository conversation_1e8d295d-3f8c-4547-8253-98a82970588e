<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>EditCatalogDialog</class>
 <widget class="QDialog" name="EditCatalogDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>793</width>
    <height>780</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>EditCatalogDialog</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_6">
   <item row="0" column="0">
    <layout class="QVBoxLayout" name="verticalLayout_2">
     <item>
      <layout class="QVBoxLayout" name="verticalLayoutLabel"/>
     </item>
     <item>
      <layout class="QGridLayout" name="gridLayout_5" rowstretch="0,0" columnstretch="1,1" columnminimumwidth="1,1">
       <item row="0" column="0">
        <widget class="QGroupBox" name="groupBoxModelRef">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="title">
          <string>ModelReference</string>
         </property>
         <layout class="QGridLayout" name="gridLayout_2" rowstretch="0,0">
          <property name="leftMargin">
           <number>9</number>
          </property>
          <property name="topMargin">
           <number>9</number>
          </property>
          <property name="rightMargin">
           <number>9</number>
          </property>
          <property name="bottomMargin">
           <number>9</number>
          </property>
          <property name="spacing">
           <number>6</number>
          </property>
          <item row="0" column="0">
           <layout class="QVBoxLayout" name="verticalLayoutModelRefW"/>
          </item>
          <item row="1" column="0">
           <layout class="QHBoxLayout" name="horizontalLayout_2">
            <property name="rightMargin">
             <number>9</number>
            </property>
            <property name="bottomMargin">
             <number>9</number>
            </property>
            <item>
             <spacer name="horizontalSpacer_2">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QPushButton" name="pushButtonCE">
              <property name="focusPolicy">
               <enum>Qt::StrongFocus</enum>
              </property>
              <property name="text">
               <string>CE</string>
              </property>
              <property name="autoDefault">
               <bool>false</bool>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QGroupBox" name="groupBoxComObjTab">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="title">
          <string>ComPars</string>
         </property>
         <layout class="QGridLayout" name="gridLayout">
          <property name="leftMargin">
           <number>9</number>
          </property>
          <property name="topMargin">
           <number>9</number>
          </property>
          <property name="rightMargin">
           <number>9</number>
          </property>
          <property name="bottomMargin">
           <number>9</number>
          </property>
          <property name="spacing">
           <number>6</number>
          </property>
          <item row="0" column="0">
           <layout class="QVBoxLayout" name="verticalLayoutComParsW"/>
          </item>
         </layout>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QGroupBox" name="groupBoxModelData">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="title">
          <string>ModelData</string>
         </property>
         <layout class="QGridLayout" name="gridLayout_4">
          <item row="0" column="0">
           <layout class="QVBoxLayout" name="verticalLayoutModelData">
            <property name="sizeConstraint">
             <enum>QLayout::SetDefaultConstraint</enum>
            </property>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QGroupBox" name="groupBoxDataAttrs">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="title">
          <string>DataAttrs</string>
         </property>
         <layout class="QGridLayout" name="gridLayout_3">
          <item row="0" column="0">
           <layout class="QVBoxLayout" name="verticalLayoutAttrsW">
            <property name="sizeConstraint">
             <enum>QLayout::SetDefaultConstraint</enum>
            </property>
           </layout>
          </item>
          <item row="1" column="0">
           <layout class="QHBoxLayout" name="horizontalLayout_3">
            <item>
             <spacer name="horizontalSpacer">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QPushButton" name="pushButtonAttrApply">
              <property name="focusPolicy">
               <enum>Qt::StrongFocus</enum>
              </property>
              <property name="text">
               <string>Apply</string>
              </property>
              <property name="autoDefault">
               <bool>false</bool>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QVBoxLayout" name="verticalLayoutVecP">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="sizeConstraint">
        <enum>QLayout::SetDefaultConstraint</enum>
       </property>
      </layout>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
