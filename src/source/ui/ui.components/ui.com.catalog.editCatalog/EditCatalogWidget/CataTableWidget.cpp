#include "CataTableWidget.h"
#include "EditComCommon.h"
#include "core/WDCore.h"
#include "core/nodeTree/WDNodeTree.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "core/message/WDMessage.h"
#include "WDRapidxml.h"
#include <QFileDialog>


static constexpr const char* Info = "xml version='1.0' encoding='utf-8'";
static constexpr const char* ObjRoot = "Root";
static constexpr const char* Objects = "Objects";
static constexpr const char* Object = "Object";
static constexpr const char* objType = "Type";
static constexpr const char* objName = "Name";
static constexpr const char* gTypeName = "GType";

//获取目录下元件参数个数最大值
static int ParamsCount(WD::WDNode::SharedPtr pCate)
{
    WD::WDNode::Nodes atomObjectNodes = cataObjectNodes(pCate);
    int count = 0;
    for (auto node : atomObjectNodes)
    {
        if (node == nullptr)
            continue;
        size_t paramsCount = node->getAttribute("Param").toStringVector().size();
        count = std::max(count, static_cast<int>(paramsCount));
    }

    return count;
}

CataTableWidget::CataTableWidget(WD::WDBMCatalog& cataMgr, QWidget* parent)
    :QWidget(parent)
    , _cataMgr(cataMgr)
{
    ui.setupUi(this);
    _pCateNode.reset();
    _pObjNode.reset();

    //界面翻译
    this->retranslateUi();
    // 绑定事件通知响应
    connect(ui.pushButtonAdd, SIGNAL(clicked()), this, SLOT(slotAddClicked()));
    connect(ui.pushButtonDelete, SIGNAL(clicked()), this, SLOT(slotDeleteClicked()));
    connect(ui.pushButtonApp, SIGNAL(clicked()), this, SLOT(slotAppClicked()));
    connect(ui.pushButtonImport, SIGNAL(clicked()), this, SLOT(slotImportObjects()));
    connect(ui.pushButtonExport, SIGNAL(clicked()), this, SLOT(slotExportObjects()));
    connect(ui.tableWidget
        , SIGNAL(currentItemChanged(QTableWidgetItem*, QTableWidgetItem*))
        , this
        , SLOT(slotTableWidgetCurrentItemChanged(QTableWidgetItem*, QTableWidgetItem*)));
}

CataTableWidget::~CataTableWidget()
{
    _pCateNode.reset();
    _pObjNode.reset();
}

void CataTableWidget::updateWidget(WD::WDNode::SharedPtr pCateNode, WD::WDNode::SharedPtr pCataNode)
{
    _pCateNode = pCateNode;
    _pObjNode = pCataNode;

    //清空表格
    ui.tableWidget->blockSignals(true);
    while (ui.tableWidget->rowCount() != 0)
    {
        int rowIndex = ui.tableWidget->rowCount() - 1;
        for (int i = 0; i < ui.tableWidget->columnCount(); ++i)
        {
            QTableWidgetItem* pItem = ui.tableWidget->takeItem(rowIndex, i);
            if (pItem != nullptr)
                delete pItem;
        }
        ui.tableWidget->removeRow(rowIndex);
    }
    ui.tableWidget->blockSignals(false);

    if (pCateNode == nullptr || pCataNode == nullptr)
        return;

    //获取目录下元件的参数个数最大值
    int paramCount = ParamsCount(pCateNode);

    ui.tableWidget->blockSignals(true);
    ui.tableWidget->setColumnCount(paramCount + static_cast<int>(ColumeNmae::CATAGTYPE) + 1);
    QStringList hHeaderLabels;
    hHeaderLabels << QString::fromUtf8(WD::WDTs("EditCatalogDialog", "Type").c_str());
    hHeaderLabels << QString::fromUtf8(WD::WDTs("EditCatalogDialog", "Name").c_str());
    hHeaderLabels << QString::fromUtf8(WD::WDTs("EditCatalogDialog", "GType").c_str());

    //设置表头
    char tmpBuf[1024] = { 0 };
    for (int i = 0; i < paramCount; ++i)
    {
        sprintf_s(tmpBuf, sizeof(tmpBuf) / sizeof(tmpBuf[0]), "%s%d", WD::WDTs("EditCatalogDialog", "Argument").c_str(), i + 1);
        hHeaderLabels << QString::fromUtf8(tmpBuf);
    }
    ui.tableWidget->setHorizontalHeaderLabels(hHeaderLabels);

    //元件表不展示第0列（类型）
    ui.tableWidget->hideColumn(CATATYPE);

    //获取目录下的所有元件对象
    WD::WDNode::Nodes   cataNodes = cataObjectNodes(pCateNode);
    QTableWidgetItem* pCurrentItem = nullptr;
    for (int i = 0; i < static_cast<int>(cataNodes.size()); ++i)
    {
        auto pNode = cataNodes.at(i);
        if(pNode == nullptr)
            continue;
        this->insertRow(*pNode, i, paramCount);
        //获取与输入元件匹配的项
        if (pCurrentItem == nullptr && pNode == pCataNode)
        {
            pCurrentItem = ui.tableWidget->item(i, 0);
        }
    }
    //设置当前选中项
    if (pCurrentItem != nullptr)
    {
        ui.tableWidget->setCurrentItem(pCurrentItem);
    }

    ui.tableWidget->blockSignals(false);
 }

void CataTableWidget::slotAddClicked()
{
    auto pCateNode = _pCateNode.lock();
    if (pCateNode == nullptr)
        return;

    //获取目录下最后一个元件的参数值,用作新增元件的默认值
    WD::WDNode::SharedPtr preCataNode = nullptr;
    const auto& prevObjNodes = cataObjectNodes(pCateNode);
    if (!prevObjNodes.empty())
    {
        preCataNode = prevObjNodes.back();
        if (preCataNode == nullptr)
        {
            return;
        }
    }

    //创建元件节点
    WD::WDNode::SharedPtr pNewComNode = preCataNode->cloneT<WD::WDNode>();
    if(pNewComNode == nullptr)
        return;

    //设置新元件的名称（名称不能同名）
    char nameBuf[1024] = { 0 };
    sprintf_s(nameBuf, sizeof(nameBuf) / sizeof(nameBuf[0]), "%s%s", pCateNode->name().c_str(), WD::WDUuid::Create().toString().c_str());
    pNewComNode->setName(nameBuf);

    //设置新元件的父对象
    pNewComNode->setParent(pCateNode);

    //添加行
    int row = ui.tableWidget->rowCount();
    this->insertRow(*pNewComNode, row, ui.tableWidget->columnCount());
    //设置当前行
    ui.tableWidget->setCurrentCell(row, 0);
}

void CataTableWidget::slotDeleteClicked()
{
    QList<QTableWidgetItem*> selections = ui.tableWidget->selectedItems();
    auto colCount = ui.tableWidget->columnCount();
    if (selections.size() <= 0)
    {
        return;
    }
    else
    {
        for (int i = 0; i < selections.size(); i += colCount)
        {
            QTableWidgetItem* item = selections.at(i);
            if(item == nullptr)
                continue;
            int rows = item->row();
            this->deleteSingleRow(rows);
        }
    }
}

void CataTableWidget::slotAppClicked()
{
    auto pCateNode = _pCateNode.lock();
    if (pCateNode == nullptr)
        return;
    auto pCataNode = _pObjNode.lock();
    int rowCount = ui.tableWidget->rowCount();
    auto cataNodes = cataObjectNodes(pCateNode);
    assert(rowCount == cataNodes.size());
    int minRowCount = WD::Min<int>(rowCount, static_cast<int>(cataNodes.size()));

    bool bUpdate = false;
    for (int i = 0; i < minRowCount; ++i)
    {
        auto pNode = cataNodes[i];
        if (pNode == nullptr)
            continue;

        if (ui.tableWidget->columnCount() <= 0)
            continue;

        //更新名称
        QTableWidgetItem* pNameItem = ui.tableWidget->item(i, CATANAME);
        if (pNameItem != nullptr)
        {
            std::string name = pNameItem->text().toUtf8().data();
            if (pNode->name() != name)
            {
                pNode->setName(name);
                //判断当前对象有值更新才需要发送update信号
                if (pNode == pCataNode)
                {
                    bUpdate = true;
                }
            }
        }

        //更新GType
        QTableWidgetItem* pGTypeItem = ui.tableWidget->item(i, CATAGTYPE);
        if (pNameItem != nullptr)
        {
            pNode->setAttribute("Gtype", WD::WDBMWord(pGTypeItem->text().toUtf8().data()));
        }

        //更新参数
        WD::StringVector values = pNode->getAttribute("Param").toStringVector();
        for (int j = 0; j < values.size(); ++j)
        {
            //从第4列开始为参数
            QTableWidgetItem* pItem = ui.tableWidget->item(i, j + CATAGTYPE + 1);
            if (pItem == nullptr)
                continue;
            std::string value = pItem->text().toUtf8().data();
            if (values[j] != value)
            {
                values[j] = value;
                //判断当前对象有值更新才需要发送update信号
                if (pNode == pCataNode)
                {
                    bUpdate = true;
                }
            }
        }
        pNode->setAttribute("Param", values);
    }

    //发送update信号
    if (bUpdate)
        emit sigDataChanged();
}

void CataTableWidget::slotImportObjects()
{
    std::string fileName = QFileDialog::getOpenFileName(this
        , QString::fromUtf8(WD::WDTs("EditCatalogDialog", "ImportCataSheet").c_str())
        , ""
        , "(*.xlsx);;(*.xml)").toUtf8().toStdString();

    if (fileName.empty())
        return;
    std::string extName = WD::FileExtension(fileName);
    bool nRet = false;
    if (extName == "xlsx")
    {
        nRet = updateTableWidgetByExcel(fileName);
    }
    else
    {
        nRet = updateTableWidgetByXML(fileName);
    }

    if(nRet == true)
    {
        emit sigDataChanged();
        WD_INFO_T("EditCatalogDialog", "ImportSucceed");
    }

    return;
}

void CataTableWidget::slotExportObjects()
{
    QString fiterSelect;
    auto fName = QFileDialog::getSaveFileName(this
        , QString::fromUtf8(WD::WDTs("EditCatalogDialog", "ExportFile").c_str())
        , ""
        , "(*.xlsx);;(*.xml)", &fiterSelect);
    if (fName.isEmpty())
        return;

    bool nRet = false;

    // linux返回的路径不带后缀
    QString suffix;
    if (fiterSelect == "(*.xlsx)")
    {
        suffix = ".xlsx";
        if (!fName.endsWith(suffix, Qt::CaseInsensitive))
            fName += suffix;
        nRet = exportExcelByTableWidget(fName.toUtf8().toStdString());
    }
    else if (fiterSelect == "(*.xml)")
    {
        suffix = ".xml";
        if (!fName.endsWith(suffix, Qt::CaseInsensitive))
            fName += suffix;
        nRet = exportXMLByTableWidget(fName.toUtf8().toStdString());
    }
    else
        return;

    if (nRet)
    {
        WD_INFO_T("EditCatalogDialog", "ExportSucceed");
    }
    else
    {
        WD_WARN_T("EditCatalogDialog", "ExportFail");
    }
    return;
}

void CataTableWidget::slotTableWidgetCurrentItemChanged(QTableWidgetItem* pCurrent, QTableWidgetItem*)
{
    if (pCurrent == nullptr)
        return;
    //获取当前项对应的节点
    auto pCateNode = _pCateNode.lock();
    auto row       = ui.tableWidget->row(pCurrent);
    auto cataNodes = cataObjectNodes(pCateNode);
    if (cataNodes.size() <= row)
        return;
    _pObjNode = cataNodes[row];

    auto pCataNode = _pObjNode.lock();
    if(pCataNode ==  nullptr)
        return;

    //元件表当前选中对象同步到节点树上
    WD::Core().getBMCatalog().core().nodeTree().setCurrentNode(pCataNode);
    emit sigCurrentCataObjectChanged(pCataNode);
}

void CataTableWidget::slotParamsCountChanged(int num)
{
    auto item = ui.tableWidget->currentItem();
    if (item == nullptr)
        return;
    int row = ui.tableWidget->row(item);
    auto pCataNode = _pObjNode.lock();
    if (pCataNode == nullptr)
        return;
    auto pCateNode = _pCateNode.lock();
    if (pCateNode == nullptr)
        return;
    auto cataNodes = cataObjectNodes(pCateNode);
    if (cataNodes.size() <= row)
        return;

    if (num >= 0)
    {
        //添加 n 个参数
        for (int i = 0; i < num; ++i)
        {
            for (int j = 0; j < cataNodes.size(); ++j)
            {
                //除当前元件以外元件才添加参数
                if (row != j)
                {
                    WD::WDNode::SharedPtr node = cataNodes[j];
                    if (node == nullptr)
                        continue;
                    auto params = node->getAttribute("Param").toStringVector();
                    params.push_back("");
                    node->setAttribute("Param", params);
                }
            }
        }
    }
    else if (num < 0)
    {
        //减少 n 个参数
        num = -num;
        for (int i = 0; i < num; ++i)
        {
            for (int j = 0; j < cataNodes.size(); ++j)
            {
                //除当前元件以外元件才减少参数
                if (row != j)
                {
                    WD::WDNode::SharedPtr node = cataNodes[j];
                    if (node == nullptr)
                        continue;
                    auto params = node->getAttribute("Param").toStringVector();
                    if (params.empty())
                        continue;
                    params.pop_back();
                    node->setAttribute("Param", params);
                }
            }
        }
    }
}

void CataTableWidget::insertRow(const WD::WDNode& cataNode, int rowIndex, int cCount)
{
    ui.tableWidget->insertRow(rowIndex);

    QTableWidgetItem* pTypeItem = new QTableWidgetItem(QString::fromUtf8(cataNode.type().data()));
    ui.tableWidget->setItem(rowIndex, CATATYPE, pTypeItem);
    pTypeItem->setTextAlignment(Qt::AlignHCenter);

    QTableWidgetItem* pNameItem = new QTableWidgetItem(QString::fromUtf8(cataNode.name().c_str()));
    ui.tableWidget->setItem(rowIndex, CATANAME, pNameItem);
    pNameItem->setTextAlignment(Qt::AlignHCenter);

    QTableWidgetItem* pGTypeItem = new QTableWidgetItem(QString::fromUtf8(cataNode.getAttribute("Gtype").toWord().c_str()));
    ui.tableWidget->setItem(rowIndex, CATAGTYPE, pGTypeItem);
    pGTypeItem->setTextAlignment(Qt::AlignHCenter);

    const auto values = cataNode.getAttribute("Param").toStringVector();
    for (int i = 0; i < cCount; ++i)
    {
        QString text = "";
        if (i < values.size())
            text = QString::fromUtf8(values[i].c_str());

        //创建行Item
        QTableWidgetItem* pItem = new QTableWidgetItem(text);
        pItem->setTextAlignment(Qt::AlignHCenter);
        ui.tableWidget->setItem(rowIndex, i + 3, pItem);
    }
}

void CataTableWidget::deleteSingleRow(int row)
{
    auto pCateNode = _pCateNode.lock();
    if (pCateNode == nullptr)
        return;

    //拿到当前所有子元件
    auto cataNodes = cataObjectNodes(pCateNode);
    if (row >= cataNodes.size())
        return;

    //删除对应元件
    auto pCataNode = _cataMgr.findNode(cataNodes[row]->name(), cataNodes[row]->type());
    if (pCataNode == nullptr)
        return;

    auto pBMBase = pCataNode->getBMBase();
    if (pBMBase == nullptr)
    {
        assert(false && "BMBase is null!");
        return;
    }
    pBMBase->destroy(pCataNode);

    //删除行
    for (int i = 0; i < ui.tableWidget->columnCount(); ++i)
    {
        QTableWidgetItem* pItem = ui.tableWidget->takeItem(row, i);
        if (pItem != nullptr)
            delete pItem;
    }
    ui.tableWidget->blockSignals(true);
    ui.tableWidget->removeRow(row);
    ui.tableWidget->setCurrentItem(nullptr);
    ui.tableWidget->blockSignals(false);

    if (ui.tableWidget->rowCount() <= 0 || ui.tableWidget->colorCount() <= 0)
        return;
    int currRow = row;
    if (currRow >= ui.tableWidget->rowCount())
    {
        currRow = 0;
    }
    ui.tableWidget->setCurrentItem(ui.tableWidget->item(currRow, 0));
}

bool CataTableWidget::updateTableWidgetByExcel(std::string& filePath)
{

    // 获取Excel数据
    QTableWidget2Excel* pExcel = new QTableWidget2Excel();
    if (pExcel == nullptr)
        return false;
    QTableWidget2Excel::ExcelData excelData;
    bool flag = pExcel->getExcelData(filePath, excelData);
    if (!flag)
    {
        WD_WARN_T("ErrorPromptSCOMsTab", "ImportFail");
        delete pExcel;
        return false;
    }
    delete pExcel;
    if (excelData.size() <= 0)
    {
        assert(false);
        return false;
    }

    //excel所有元件的参数个数必须与当前目录下元件的参数个数一致
    //转换导入的元件信息 QTableWidget2Excel::ExcelData->ArrayCataData
    ArrayCataData cataDatas;
    int excelRow = static_cast<int>(excelData.size());
    for(int i = 0; i < excelRow; ++i)
    {
        auto excelRowData = excelData[i];
        auto excelCol = static_cast<int>(excelRowData.size());
        if(excelCol != ui.tableWidget->columnCount())
        {
            WD_WARN_T("EditCatalogDialog", "ImportFail, the number of parameters is not equal!");
            return false;
        }
        WD::StringVector params;
        std::string type = "", name = "", gtype = "";
        for(int j = 0; j < excelCol; ++j)
        {
            if(j == CATATYPE)
                type = excelRowData[j];
            else if(j == CATANAME)
                name = excelRowData[j];
            else if(j == CATAGTYPE)
                gtype = excelRowData[j];
            else
                params.push_back(excelRowData[j]);
        }
        cataDatas.emplace_back(type, name, gtype, params);
    }

    //处理Excel中获取的数据   
    return processImportData(cataDatas);
}

bool CataTableWidget::getCataDataByXML(const std::string& fileName, ArrayCataData& catamDatas)
{
    // 获取XML数据
    WD::WDFileReader file(fileName);
    if (file.isBad())
    {
        return false;
    }
    file.readAll();
    size_t length = file.length();
    if (length == 0)
        return false;

    //解析XML文件
    WD::XMLDoc doc;
    doc.parse<0>((char*)file.data());
    WD::XMLNode* pXmlNodeRoot = doc.first_node(ObjRoot);
    if (pXmlNodeRoot == nullptr)
        return false;
    WD::XMLNode* pXmlNodeObjs = pXmlNodeRoot->first_node(Objects);
    if (pXmlNodeObjs == nullptr)
        return false;
    WD::XMLNode* pXmlNodeObj = pXmlNodeObjs->first_node(Object);
    for (; pXmlNodeObj != nullptr; pXmlNodeObj = pXmlNodeObj->next_sibling())
    {
        WD::XMLAttr* pAttrType = pXmlNodeObj->first_attribute(objType);
        if (pAttrType == nullptr)
            continue;
        WD::XMLAttr* pAttrName = pAttrType->next_attribute();
        if (pAttrName == nullptr)
            continue;
        WD::XMLAttr* pAttrGType = pAttrName->next_attribute();
        if (pAttrGType == nullptr)
            continue;

        WD::StringVector values;
        WD::XMLAttr* pAttrParam = pAttrGType->next_attribute();
        for (; pAttrParam; pAttrParam = pAttrParam->next_attribute())
        {
            values.push_back(pAttrParam->value());
        }
        //XML文件中元件的参数个数必须与当前目录下元件的参数个数相同
        if (values.size() != (ui.tableWidget->columnCount() - CATAGTYPE - 1))
            return false;
        catamDatas.emplace_back(pAttrType->value(), pAttrName->value(), pAttrGType->value(), values);
    }
    return true;
}

bool CataTableWidget::updateTableWidgetByXML(const std::string& filePath)
{    
    //从XML文件中获取元件表信息
    ArrayCataData cataDatas;
    if(!this->getCataDataByXML(filePath, cataDatas))
        return false;

    //处理XML中获取的数据
    return processImportData(cataDatas);
}

bool CataTableWidget::exportExcelByTableWidget(const std::string& filePath)
{
    QTableWidget2Excel* pExcel = new QTableWidget2Excel();
    if (pExcel == nullptr)
        return false;

    bool flag = pExcel->exportTableWidgetToExcel(filePath, ui.tableWidget);

    delete pExcel;
    return flag;
}

bool CataTableWidget::exportXMLByTableWidget(const std::string& fileName)
{
    if (fileName.empty())
    {
        assert(false && "fileName is empty!");
        return false;
    }
    int row = ui.tableWidget->rowCount();
    int column = ui.tableWidget->columnCount();
    if (row <= 0 || column <= 0)
    {
        return false;
    }

    //将tableWidget数据写到XML中
    WD::XMLDoc doc;
    WD::XMLNode* pXmlInfo = doc.allocate_node(rapidxml::node_pi, Info);
    doc.append_node(pXmlInfo);
    WD::XMLNode* pXmlRoot = doc.allocate_node(rapidxml::node_element, ObjRoot);
    doc.append_node(pXmlRoot);
    WD::XMLNode* pXmlObjects = doc.allocate_node(rapidxml::node_element, Objects);
    pXmlRoot->append_node(pXmlObjects);
    for (int i = 0; i < row; ++i)
    {
        // 元件
        WD::XMLNode* pXmlObject = doc.allocate_node(rapidxml::node_element, Object);
        pXmlObjects->append_node(pXmlObject);

        auto typeItem = ui.tableWidget->item(i, 0);
        if (typeItem == nullptr)
            continue;
        WD::XMLAttr* pAttrType = doc.allocate_attribute(objType, doc.allocate_string(typeItem->text().toUtf8().toStdString().c_str()));
        pXmlObject->append_attribute(pAttrType);

        auto nameItem = ui.tableWidget->item(i, 1);
        if (nameItem == nullptr)
            continue;
        WD::XMLAttr* pAttrName = doc.allocate_attribute(objName, doc.allocate_string(nameItem->text().toUtf8().toStdString().c_str()));
        pXmlObject->append_attribute(pAttrName);

        auto gtypeItem = ui.tableWidget->item(i, 2);
        if (gtypeItem == nullptr)
            continue;
        WD::XMLAttr* pAttrGType = doc.allocate_attribute(gTypeName, doc.allocate_string(gtypeItem->text().toUtf8().toStdString().c_str()));
        pXmlObject->append_attribute(pAttrGType);


        for (int j = CATAGTYPE + 1; j < column; ++j)
        {
            auto item = ui.tableWidget->item(i, j);
            if (item == nullptr)
                continue;
            std::string paramName = "Param" + std::to_string(j - CATAGTYPE - 1);
            WD::XMLAttr* pAttrParam = doc.allocate_attribute(doc.allocate_string(paramName.c_str())
                , doc.allocate_string(item->text().toUtf8().toStdString().c_str()));
            pXmlObject->append_attribute(pAttrParam);
        }
    }
    std::string outString;
    rapidxml::print(std::back_inserter(outString), doc, 0);

    FILE* fp = fopen(fileName.c_str(), "w");
    if (fp == nullptr)
    {
        return false;
    }

    fwrite(outString.data(), 1, outString.size(), fp);
    fclose(fp);
    fp = nullptr;

    return true;  
}

bool CataTableWidget::processImportData(ArrayCataData& catamDatas)
{
    //在当前目录下查找同名的元件
    //名称和类型一致的的元件，将excel中的数据更新到元件上
    //名称相同，类型不相同，表格不添加该元件（避免与名称互斥冲突）
    auto pCateNode = _pCateNode.lock();
    if (pCateNode == nullptr)
    {
        assert(false);
        return false;
    }
    auto pBMBase = pCateNode->getBMBase();
    if (pBMBase == nullptr)
    {
        assert(false);
        return false;
    }
    const auto& cataNods = pCateNode->children();
    for (auto cataNodeIt = cataNods.rbegin(); cataNodeIt != cataNods.rend(); ++cataNodeIt)
    {
        auto cataNode = cataNodeIt->get();
        if (cataNode == nullptr)
            continue;

        int  xlslRow = static_cast<int>(catamDatas.size());
        for (int i = xlslRow - 1; i >= 0; --i)
        {
            auto excelCata = catamDatas[i];
            if (excelCata.name.compare(cataNode->name()) == 0)
            {
                //名称和类型相同，更新参数的值，然后从EXcel的表中移除
                if (excelCata.type.compare(cataNode->type()) == 0)
                {
                    // 更新元件的参数值
                    cataNode->setAttribute("Param", excelCata.paramValues);
                    // 更新元件的 GType
                    cataNode->setAttribute("Gtype", WD::WDBMWord(excelCata.gtype));
                }
                catamDatas.erase(catamDatas.begin() + i);
            }
        }
    }

    //不同名的直接新建
    WD::WDNode::SharedPtr currenNode = nullptr;
    for (auto cataAttr : catamDatas)
    {
        auto pNewCataNode = pBMBase->create(pCateNode, cataAttr.type, cataAttr.name);
        if (pNewCataNode == nullptr)
            continue;
        //设置节点通用类型
        pNewCataNode->setAttribute("Gtype", WD::WDBMWord(cataAttr.gtype));
        //添加新建元件的参数值
        pNewCataNode->setAttribute("Param", cataAttr.paramValues);

        currenNode = pNewCataNode;
    }

    if (currenNode == nullptr)
    {
        this->updateWidget(pCateNode, _pObjNode.lock());
    }
    else
    {
        this->updateWidget(pCateNode, currenNode);
    }
    return true;;
}

void CataTableWidget::retranslateUi()
{
    Trs("EditCatalogDialog"
        , ui.pushButtonAdd
        , ui.pushButtonDelete
        , ui.pushButtonApp
        , ui.pushButtonImport
        , ui.pushButtonExport
    );
}