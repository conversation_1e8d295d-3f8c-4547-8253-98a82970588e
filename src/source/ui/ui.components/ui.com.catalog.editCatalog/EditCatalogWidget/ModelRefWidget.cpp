#include "ModelRefWidget.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "core/businessModule/WDBMBase.h"
#include "core/nodeTree/WDNodeTree.h"


ModelRefWidget::ModelRefWidget(WD::WDBMCatalog& catalogMgr, QWidget* parent)
    :QWidget(parent)
    , _catalogMgr(catalogMgr)
{
    ui.setupUi(this);
    QStringList labels;
    labels << QString::fromUtf8(WD::WDTs("EditCatalogDialog", "Desc").c_str());
    labels << QString::fromUtf8(WD::WDTs("EditCatalogDialog", "Value").c_str());
    ui.treeWidget->setHeaderLabels(labels);

    connect(ui.treeWidget, SIGNAL(currentItemChanged(QTreeWidgetItem*, QTreeWidgetItem*))
        , SLOT(slotCurrentItemChanged(QTreeWidgetItem*, QTreeWidgetItem*)));
    connect(ui.treeWidget, &QTreeWidget::itemClicked, this, &ModelRefWidget::slotCurrentItemClicked);
}
void ModelRefWidget::updateWidget(WD::WDNode::SharedPtr pCataNode)
{
    auto preItemName = getCurrentItemName();
    ui.treeWidget->clear();
    _pCataNode = pCataNode;
    if (pCataNode == nullptr)
        return;
    auto pBMBase = pCataNode->getBMBase();
    if (pBMBase == nullptr)
        return ;

    auto it = _map.find(std::string(pCataNode->type()));
    if(it == _map.end())
        return;


    // 目录
    auto pAtomParent = pCataNode->parent();
    if (pAtomParent != nullptr)
    {
        QTreeWidgetItem* pItem = new QTreeWidgetItem();

        pItem->setText(0, QString::fromUtf8(WD::WDTs("EditCatalogDialog", "Category").c_str()));
        pItem->setData(0, Qt::UserRole, QString::fromUtf8("Category"));
        pItem->setText(1, QString::fromUtf8(pAtomParent->name().c_str()));

        ui.treeWidget->addTopLevelItem(pItem);
        if(preItemName == "Category")
        {
            ui.treeWidget->setCurrentItem(pItem);
        }
    }
    // 元件
    {
        QTreeWidgetItem* pItem = new QTreeWidgetItem();

        pItem->setText(0, QString::fromUtf8(WD::WDTs("EditCatalogDialog", "Component").c_str()));
        pItem->setData(0, Qt::UserRole, QString::fromUtf8("Component"));
        pItem->setText(1, QString::fromUtf8(pCataNode->name().c_str()));

        ui.treeWidget->addTopLevelItem(pItem);
        if (preItemName == "Component")
        {
            ui.treeWidget->setCurrentItem(pItem);
        }
    }

    for(const auto& attrName: it->second)
    {
        QTreeWidgetItem* pItem = new QTreeWidgetItem();

        std::string nodeName = getRefNodeName(*pCataNode, attrName);

        pItem->setText(0, QString::fromUtf8(pBMBase->trA(attrName).c_str()));
        pItem->setData(0, Qt::UserRole, QString::fromUtf8(attrName.c_str()));
        pItem->setText(1, QString::fromUtf8(nodeName.c_str()));


        ui.treeWidget->addTopLevelItem(pItem);
        if (preItemName == attrName)
        {
            ui.treeWidget->setCurrentItem(pItem);
        }
    }
    if ((ui.treeWidget->currentItem() == nullptr)  && (ui.treeWidget->topLevelItemCount() > 0))
    {
        ui.treeWidget->setCurrentItem(ui.treeWidget->topLevelItem(0));
    }
}
void ModelRefWidget::add(const char* type, const std::initializer_list< std::vector<std::string> >& attrsNames)
{
    std::vector<std::string> rVector;
    for (const auto& item : attrsNames)
    {
        rVector.insert(rVector.end(), item.begin(), item.end());
    }
    this->add(type, rVector);
}
const std::string ModelRefWidget::getCurrentItemName()
{
    auto item = ui.treeWidget->currentItem();
    if(item != nullptr)
    {
        std::string itemName = item->data(0, Qt::UserRole).toString().toUtf8().data();
        return itemName;
    }
    return "";
}
void ModelRefWidget::slotCurrentItemChanged(QTreeWidgetItem* currentItem, QTreeWidgetItem* preItem)
{
    WDUnused(preItem);
    if (currentItem != nullptr)
    {
        std::string itemName = currentItem->data(0, Qt::UserRole).toString().toUtf8().data();
        emit sigCurrentItemChanged(itemName);
    }
}

void ModelRefWidget::slotCurrentItemClicked(QTreeWidgetItem* item, int column)
{
    WDUnused(column);
    if(item == nullptr)
        return;
    auto pCataNode  = _pCataNode.lock();
    if(pCataNode == nullptr)
        return;

    //当前激活项同步到节点树上   
    std::string itemName = item->data(0, Qt::UserRole).toString().toUtf8().data();
    if(itemName == "Category")
    {
        auto pCateNode = pCataNode->parent();
        if(pCateNode == nullptr)
            return;
        _catalogMgr.core().nodeTree().setCurrentNode(pCateNode);
    }
    else if(itemName == "Component")
    {
        _catalogMgr.core().nodeTree().setCurrentNode(pCataNode);
    }
    else if(itemName == "Blrfarray")
    {
        auto attrValue = pCataNode->getAttribute(itemName);
        auto pBTSEV = attrValue.data<WD::WDBMNodeRefs>();
        if(pBTSEV == nullptr)
            return;
        if(pBTSEV->size()<= 0)
            return;
        auto pBTSENode = pBTSEV->at(0).refNode();
        if(pBTSENode == nullptr)
            return;
        _catalogMgr.core().nodeTree().setCurrentNode(pBTSENode);
    }
    else
    {
        auto attrValue = pCataNode->getAttribute(itemName);
        auto pNodeRef = attrValue.data<WD::WDBMNodeRef>();
        if (pNodeRef == nullptr)
            return;
        auto pNodeRefNode = pNodeRef->refNode();
        if (pNodeRefNode == nullptr)
            return;
        _catalogMgr.core().nodeTree().setCurrentNode(pNodeRefNode);
    }
}

std::string ModelRefWidget::getRefNodeName(WD::WDNode& cataNode, const std::string_view& refAttrName)
{
    auto value  = cataNode.getAttribute(refAttrName);
    WD::WDBMAttrValueType vType = value.type();
    switch (vType)
    {
    case WD::WDBMAttrValueType::T_Bool:
        break;
    case WD::WDBMAttrValueType::T_Int:
        break;
    case WD::WDBMAttrValueType::T_Double:
        break;
    case WD::WDBMAttrValueType::T_String:
        {
            auto p = value.data<std::string>();
            if (p != nullptr)
                return *p;
        }
        break;
    case WD::WDBMAttrValueType::T_Word:
        break;
    case WD::WDBMAttrValueType::T_DVec2:
        break;
    case WD::WDBMAttrValueType::T_DVec3:
        break;
    case WD::WDBMAttrValueType::T_DQuat:
        break;
    case WD::WDBMAttrValueType::T_Color:
        break;
    case WD::WDBMAttrValueType::T_Uuid:
        break;
    case WD::WDBMAttrValueType::T_LevelRange:
        break;
    case WD::WDBMAttrValueType::T_NodeRef:
        {
            auto p = value.data<WD::WDBMNodeRef>();
            if (p != nullptr && p->refNode() != nullptr)
                return p->refNode()->name();
        }
        break;
    case WD::WDBMAttrValueType::T_IntVector:
        break;
    case WD::WDBMAttrValueType::T_DoubleVector:
        break;
    case WD::WDBMAttrValueType::T_StringVector:
        break;
    case WD::WDBMAttrValueType::T_NodeRefs:
        {
            auto p = value.data<WD::WDBMNodeRefs>();
            if (p != nullptr && !p->empty() && p->front().refNode() != nullptr)
                return p->at(0).refNode()->name();
        }
        break;
    case WD::WDBMAttrValueType::T_GeometryRef:
        break;
    default:
        break;
    }
    return "";
}
