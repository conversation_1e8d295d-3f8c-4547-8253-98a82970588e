#pragma once

#include "core/node/WDNode.h"
#include "core/businessModule/catalog/WDBMCatalog.h"

/**
* @brief 获取 目录 节点下的所有元件对象节点
*/
WD::WDNode::Nodes cataObjectNodes(WD::WDNode::SharedPtr pCateNode);
/**
 * @brief 获取 目录 下的第一个元件节点
 * @param pCateNode 目录节点
 * @return 第一个元件节点
*/
WD::WDNode::SharedPtr firstCataObjectCategory(WD::WDNode::SharedPtr pCateNode);
/**
* @brief 判断父节点中是否包含指定名称子节点
* @param pParent 父节点
* @param name 子节点名称
* @return 包含返回true，不包含返回false
*/
bool contain(WD::WDNode::SharedPtr pParent, const std::string& name);
/**
 * @brief 通过index获取Dkey的值
 * @param index 索引号
 * @return DKey
*/
std::string getDKeyByindex(int index);
/**
 * @brief 向上递归获取目录类型节点
 * @param pNode 任意节点
 * @return 目录类型节点指针或者null
*/
WD::WDNode::SharedPtr findCategoryNode(WD::WDNode::SharedPtr pNode);
/**
 * @brief 查找匹配的DATA Node
 * @param cataNode  元件节点
 * @param dKey  数据键
 * @return 与dKey匹配的DATA节点
*/
WD::WDNode::SharedPtr findDATANodeByDKey(const WD::WDNode& cataNode, const std::string& dKey);
/**
 * @brief 包含PA+索引号的TEXT节点
 * @param cataNode 元件节点
 * @param index 索引号
 * @return 匹配的TEXT 节点
*/
WD::WDNode::SharedPtr findTEXTNodeByIndex(const WD::WDNode& cataNode, int index);
/**
 * @brief 创建DATA节点。  node:元件节点 index：第几个参数  param：参数的描述
 * @param cataNode 元件节点
 * @param index 参数索引号
 * @param paramDesc 参数的描述
 * @return 新建的DATA节点
*/
WD::WDNode::SharedPtr creatDATANode(const WD::WDNode& cataNode, int index, const std::string& paramDesc);
/**
 * @brief 创建TEXT节点
 * @param cataNode 元件节点
 * @param index 参数索引号
 * @param paramDesc 
 * @return 新建的TEXT文本
*/
WD::WDNode::SharedPtr creatTEXTNode(const WD::WDNode& cataNode, int index, const std::string& paramDesc);


/**
 * @brief 是否元件节点
*/
inline bool IsComponentNode(const WD::WDNode& node)
{
    return node.isAnyOfType("SCOM", "SFIT", "SPRF", "JOIN");
}
/**
 * @brief 是否负型节点
*/
inline bool IsNegativeGMNode(const WD::WDNode& node) 
{
    return node.isAnyOfType("NSBO", "NLSN", "NSCO", "NSSP"
        , "NLCY", "NSCY", "NSSL", "NSCT"
        , "NSRT", "NLPY", "NSDS", "NSEX"
        , "NSRE");
}