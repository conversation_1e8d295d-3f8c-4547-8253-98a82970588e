#pragma once

#include "ui_ModelRefWidget.h"
#include <QWidget>
#include "core/node/WDNode.h"
#include "core/businessModule/catalog/WDBMCatalog.h"
#include "core/nodeTree/WDNodeTree.h"
#include "core/WDCore.h"

class ModelRefWidget :public QWidget
{
    Q_OBJECT
public:
    ModelRefWidget(WD::WDBMCatalog& catalogMgr, QWidget* parent);
    ~ModelRefWidget(){};
signals:
    void sigCurrentItemChanged(const std::string itemName);
public:
    /**
     * @brief 更新窗口
     * @param pAtomNode 元件节点 
    */
    void updateWidget(WD::WDNode::SharedPtr pAtomNode);
    /**
     * @brief 添加支持的类型以及需要显示的对应属性
    */
    inline void add(const char* type, const std::vector<std::string>& attrNames)
    {
        _map[type] = attrNames;
    }
    /**
     * @brief 添加支持的类型以及需要显示的对应属性
    */
    void add(const char* type, const std::initializer_list< std::vector<std::string> >& attrsNames);
    /**
     * @brief 获取当前项的名称
     * @return 
    */
    const std::string getCurrentItemName();
    /**
     * @brief 
     * @param cataNode 
     * @return 
    */
    std::vector<std::string> queryAttrVecByNode(const WD::WDNode& cataNode)
    {
        std::vector<std::string> attrVect = {};
        auto type = cataNode.type();
        auto it  = _map.find(type.data());
        if(it != _map.end()) 
            return it->second;
        return attrVect;
    }
private slots:
    /**
     * @brief 树当前项改变槽函数
    */
    void slotCurrentItemChanged(QTreeWidgetItem*, QTreeWidgetItem*);
    /**
     * @brief 树当前被点击槽函数
     * @param  
    */
    void slotCurrentItemClicked(QTreeWidgetItem* item, int column);
private:
    /**
     * @brief 获取引用的节点名称
     * @param cataNode 元件节点
     * @param refAttrName NodeRef类型的属性名称
     * @return 
    */
    std::string getRefNodeName(WD::WDNode& cataNode, const std::string_view& refAttrName);
private:
    //ui界面
    Ui::ModelRefWidget ui;
    //元件节点
    WD::WDNode::WeakPtr _pCataNode;
    // 元件库管理
    WD::WDBMCatalog&    _catalogMgr;
    // 类型属性配置表  
    std::map<std::string, std::vector<std::string> > _map;
};



