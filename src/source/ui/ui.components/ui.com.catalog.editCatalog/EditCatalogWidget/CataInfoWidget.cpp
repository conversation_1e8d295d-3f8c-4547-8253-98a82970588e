#include "CataInfoWidget.h"
#include "core/WDCore.h"
#include "core/nodeTree/WDNodeTree.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "core/businessModule/catalog/WDBMCatalog.h"
#include <QMessageBox>

CataInfoWidget::CataInfoWidget(WD::WDCore& app, QWidget* parent)
    : QWidget(parent)
{
    ui.setupUi(this);
    //界面翻译
    retranslateUi();

    _pWidget = new NodeAttributeWidget(app.getBMCatalog(), NodeAttributeWidget::F_AddCEButtonToNodeRef);
    //绑定表头翻译的回调函数
    _pWidget->trFunction() = [](const std::string& key)->std::string
    {
        return WD::WDTs("EditCatalogDialog", key);
    };
    //移除F_AutoApplyToNode标志，使修改不立即应用到节点，点击应用按钮后修改才生效
    _pWidget->flags().removeFlag(NodeAttributeWidget::F_AutoApplyToNode);
    ui.horizontalLayout->addWidget(_pWidget->getWidget());

    //属性窗口CE的回调函数
    _pWidget->clickedCEBtnFunction() = std::bind(&CataInfoWidget::clickedCEBtnNode, this, std::placeholders::_1, std::placeholders::_2);
    
    connect(ui.pushButtonApply, SIGNAL(clicked()), this, SLOT(slotApply()));
}
CataInfoWidget::~CataInfoWidget()
{
    _pCataNode.reset();
    if(_pWidget != nullptr)
    {
        delete _pWidget;
        _pWidget = nullptr;
    }
}
void CataInfoWidget::updateWidget(WD::WDNode::SharedPtr pNode)
{
    _pCataNode = pNode;
    if(pNode ==nullptr)
        return;

    //设置索引值
    QString refs = QString("%1(%2)").arg(QString::fromUtf8(pNode->name().c_str()), QString::fromUtf8(pNode->type().data()));
    ui.labelRefLabel->setText(refs);

    //刷新元件属性窗口
    auto attrs = _group.query(*pNode);
    _pWidget->initWidget(attrs);
    _pWidget->setNode(pNode);
}
void CataInfoWidget::slotApply()
{
    if(_pWidget == nullptr)
        return;
    _pWidget->apply();

    //元件信息界面更新信号
    auto pNode = _pCataNode.lock();
    emit signalUpdate(pNode);
}
void CataInfoWidget::retranslateUi()
{
    Trs("EditCatalogDialog"
        , ui.labelRefs
        , ui.pushButtonApply
    );
}
WD::WDNode::SharedPtr CataInfoWidget::clickedCEBtnNode(WD::WDNode& node, const WD::WDBMAttrDesc& attr)
{
    //获取节点树上当前节点
    auto pCurrNode = WD::Core().nodeTree().currentNode();
    if (pCurrNode == nullptr)
        return nullptr;

    //判断当前节点与修改的属性是否匹配
    if ((attr.name() == "Ptref"         && pCurrNode->isType("PTSE"))
        || (attr.name() == "Pstref"     && pCurrNode->isType("PTSS"))
        || (attr.name() == "Gmref"      && pCurrNode->isType("GMSE"))
        || (attr.name() == "Gstref"     && pCurrNode->isType("GMSS"))
        || (attr.name() == "Ngmref"     && pCurrNode->isType("NGMS"))
        || (attr.name() == "Dtref"      && pCurrNode->isType("DTSE"))
        || (attr.name() == "Blrfarray"  && pCurrNode->isType("BTSE")))
    {
       return pCurrNode;
    }
    
    auto pBMBase = node.getBMBase();
    if (pBMBase == nullptr)
        return nullptr;

    QString text= WD::WDTs("EditCatalogDialog", "Please select").c_str() ;
    text += QString::fromUtf8(pBMBase->trA(attr.name()).c_str());
    text += WD::WDTs("EditCatalogDialog", "Node!").c_str();
    QMessageBox::warning(nullptr, WD::WDTs("EditCatalogDialog", "Warn").c_str(), text);
    return nullptr;
}