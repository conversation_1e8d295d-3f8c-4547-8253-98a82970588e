#pragma once
#include "ui_CataInfoWidget.h"
#include <QWidget>
#include "../../ui.commonLibrary/ui.commonLib.attributte/NodeAttributeWidget.h"
#include "EditCatalogWidget/SetWidgetBaseAndAttrsCvrt.h"

class CataInfoWidget :public QWidget
{
    Q_OBJECT
public:
    CataInfoWidget(WD::WDCore& app, QWidget *parent);
    ~CataInfoWidget();
public:
    /**
     * @brief 更新界面
     * @param pNode 元件节点
    */
    void updateWidget(WD::WDNode::SharedPtr pNode);
    /**
     * @brief 获取元件需要展示的属性组
    */
    inline Node2AttrGroup& group()
    {
        return _group;
    }
signals:
    /**
     * @brief 修改元件信息的更新信号
    */
    void signalUpdate(WD::WDNode::SharedPtr& pObjNode);
private slots:
    /**
     * @brief 应用按钮槽函数
    */
    void slotApply();
private:
    /**
     * @brief 界面文本翻译
    */
    void retranslateUi();
    /**
     * @brief CE按钮获取有效的节点
     * @param node 元件节点 
     * @param attr 元件节点的某一引用属性
     * @return 点击CE获取的节点与属性匹配则返回该节点， 不匹配则返回nullptr
    */
    WD::WDNode::SharedPtr clickedCEBtnNode(WD::WDNode& node, const WD::WDBMAttrDesc& attr);
private:
    //界面
    Ui::CataInfoWidget ui;
    //节点转属性组
    Node2AttrGroup _group;
    //属性窗口
    NodeAttributeWidget* _pWidget;
    //当前界面展示的元件
    WD::WDNode::WeakPtr _pCataNode;
};