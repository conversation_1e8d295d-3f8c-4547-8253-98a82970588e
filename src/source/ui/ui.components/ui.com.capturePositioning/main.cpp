#include "UiComCapturePositioning.h"

IUiComponent* Create(IMainWindow& mainWindow, const UiComponentAttributes& attrs)
{
    return new UiComCapturePositioning(mainWindow, attrs, mainWindow.widget());
}

void Destroy(IUiComponent* component)
{
    delete component;
}

UI_EXTENSION_EXPORT void GetUiComponentInfor(UiComponentInfor& infor)
{
    infor.createFunction = Create;
    infor.destroyFunction = Destroy;
}