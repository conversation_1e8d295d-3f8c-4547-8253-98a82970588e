#pragma once

#include <QDialog>
#include "ui_CurveEquationDialog.h"
#include "core/math/Math.hpp"
#include "core/WDCore.h"

class CurveEquationDialog : public QDialog
{
	Q_OBJECT
private:
    // 取值范围 -_limitRange ~ _limitRange
    const uint _limitRange = 250000;
    const uint _maxPointCnt = 1000;
public:
    CurveEquationDialog(WD::WDCore& app
		, QWidget *parent = Q_NULLPTR);
	virtual ~CurveEquationDialog();

protected:
    virtual void showEvent(QShowEvent* evt) override;
    virtual void hideEvent(QHideEvent* evt) override;
signals:
    void sigEquationApply(WD::DVec3Vector points);
private slots:
    /**
    * @brief 方程1应用按钮
    */
	void slotEquation1ApplyButtonClicked();
    /**
    * @brief 方程2应用按钮
    */
    void slotEquation2ApplyButtonClicked();
    /**
    * @brief 方程3应用按钮
    */
    void slotEquation3ApplyButtonClicked();
    /**
    * @brief 取消按键槽函数
    */
	void slotCancelButtonClicked();
private:
    /**
    * @brief 初始化界面
    */
    void initDialog();
    /**
    * @brief 界面文本翻译
    */
    void retranslateUi();

private:
	Ui::CurveEquationDialog  ui;
    WD::WDCore& _core;
};

