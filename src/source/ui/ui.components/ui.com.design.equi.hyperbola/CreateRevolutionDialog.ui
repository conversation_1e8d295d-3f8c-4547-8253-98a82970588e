<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CreateRevolutionDialog</class>
 <widget class="QDialog" name="CreateRevolutionDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>578</width>
    <height>578</height>
   </rect>
  </property>
  <property name="focusPolicy">
   <enum>Qt::StrongFocus</enum>
  </property>
  <property name="windowTitle">
   <string>Create Revolution</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QGroupBox" name="groupBoxAxes">
     <property name="title">
      <string>Axes</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <widget class="QPushButton" name="pushButtonRotationLine">
        <property name="focusPolicy">
         <enum>Qt::StrongFocus</enum>
        </property>
        <property name="text">
         <string>Rotation Line</string>
        </property>
        <property name="autoDefault">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="pushButtonFlip">
        <property name="focusPolicy">
         <enum>Qt::StrongFocus</enum>
        </property>
        <property name="text">
         <string>Flip</string>
        </property>
        <property name="autoDefault">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="pushButtonPointOnPlane">
        <property name="focusPolicy">
         <enum>Qt::StrongFocus</enum>
        </property>
        <property name="text">
         <string>Point on Plane</string>
        </property>
        <property name="autoDefault">
         <bool>false</bool>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBoxSettings">
     <property name="title">
      <string>Settings</string>
     </property>
     <layout class="QFormLayout" name="formLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="labelName">
        <property name="text">
         <string>Name</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QLineEdit" name="lineEditName"/>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="labelAngle">
        <property name="text">
         <string>Angle</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QDoubleSpinBox" name="doubleSpinBoxAngle">
        <property name="maximum">
         <double>360.000000000000000</double>
        </property>
        <property name="value">
         <double>90.000000000000000</double>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="labelObstruction">
        <property name="text">
         <string>Obstruction</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QComboBox" name="comboBoxObstruction"/>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="labelNegative">
        <property name="text">
         <string>Negative</string>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="QCheckBox" name="checkBoxNegative">
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBoxVertices">
     <property name="title">
      <string>Vertices</string>
     </property>
     <layout class="QFormLayout" name="formLayout_2">
      <item row="0" column="0">
       <widget class="QLabel" name="labelMethod">
        <property name="text">
         <string>Method</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QComboBox" name="comboBoxMethod"/>
      </item>
      <item row="1" column="0" colspan="2">
       <layout class="QHBoxLayout" name="horizontalLayout_2">
        <item>
         <widget class="QTableWidget" name="tableWidgetVertices"/>
        </item>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_2">
          <item>
           <spacer name="verticalSpacer">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="pushButtonAdd">
            <property name="focusPolicy">
             <enum>Qt::StrongFocus</enum>
            </property>
            <property name="text">
             <string>Add</string>
            </property>
            <property name="autoDefault">
             <bool>false</bool>
            </property>
            <property name="default">
             <bool>false</bool>
            </property>
            <property name="flat">
             <bool>false</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="pushButtonImport">
            <property name="text">
             <string>Import</string>
            </property>
            <property name="autoDefault">
             <bool>false</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="pushButtonExport">
            <property name="text">
             <string>Export</string>
            </property>
            <property name="autoDefault">
             <bool>false</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="pushButtonDelete">
            <property name="focusPolicy">
             <enum>Qt::StrongFocus</enum>
            </property>
            <property name="text">
             <string>Delete</string>
            </property>
            <property name="autoDefault">
             <bool>false</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="pushButtonClear">
            <property name="focusPolicy">
             <enum>Qt::StrongFocus</enum>
            </property>
            <property name="text">
             <string>Clear</string>
            </property>
            <property name="autoDefault">
             <bool>false</bool>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="verticalSpacer_2">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_4" stretch="1,0,0">
     <item>
      <spacer name="horizontalSpacer_4">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonOK">
       <property name="focusPolicy">
        <enum>Qt::StrongFocus</enum>
       </property>
       <property name="text">
        <string>Ok</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonCancel">
       <property name="focusPolicy">
        <enum>Qt::StrongFocus</enum>
       </property>
       <property name="text">
        <string>Cancel</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <tabstops>
  <tabstop>pushButtonRotationLine</tabstop>
  <tabstop>pushButtonFlip</tabstop>
  <tabstop>pushButtonPointOnPlane</tabstop>
  <tabstop>lineEditName</tabstop>
  <tabstop>doubleSpinBoxAngle</tabstop>
  <tabstop>comboBoxObstruction</tabstop>
  <tabstop>checkBoxNegative</tabstop>
  <tabstop>comboBoxMethod</tabstop>
  <tabstop>tableWidgetVertices</tabstop>
  <tabstop>pushButtonAdd</tabstop>
  <tabstop>pushButtonImport</tabstop>
  <tabstop>pushButtonExport</tabstop>
  <tabstop>pushButtonDelete</tabstop>
  <tabstop>pushButtonClear</tabstop>
  <tabstop>pushButtonOK</tabstop>
  <tabstop>pushButtonCancel</tabstop>
 </tabstops>
 <resources/>
 <connections/>
</ui>
