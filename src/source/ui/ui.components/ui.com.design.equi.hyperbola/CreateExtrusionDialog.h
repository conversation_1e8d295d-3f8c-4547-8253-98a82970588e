#pragma once

#include <QDialog>
#include "ui_CreateExtrusionDialog.h"
#include "core/WDCore.h"
#include "core/node/WDNode.h"
#include "core/viewer/capturePositioning/WDCapturePositioning.h"
#include "core/scene/WDScene.h"
#include "core/geometry/standardPrimitives/WDGeometrySphere.h"
#include "core/material/WDMaterial.h"
#include "core/viewer/primitiveRender/WDTextRender.h"
#include "core/scene/WDRenderObject.h"
#include "core/geometry/WDGeometryPolyhedron.h"

#include "CurveEquationDialog.h"

#include "../../ui.commonLibrary/ui.commonLib.custom/UiPositionCaptureHelpter.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiLoopVerticesDefineHelpter.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiPositionInputDialog.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiPolarCoordInputDialog.h"
#include "../../ui.commonLibrary//ui.commonLib.custom/UiNodeNameHelpter.h"

class CreateExtrusionDialog;

WD_NAMESPACE_BEGIN

class ExtrVertexRenderObject : public WD::WDRenderObject
{
private:
    // 
    CreateExtrusionDialog& _d;
    // 绘制顶点的几何体
    WDGeometrySphere::SharedPtr _pGeomSphere;
    // 几何体材质(phong)带法线
    WDMaterialPhong             _material;
    // 实例
    WDInstances                 _insts;

    // 连接线几何体
    WDGeometryPolyhedron::SharedPtr _pGeomLines;
    // 连接线材质(纯色)
    WDMaterialColor             _materialLines;

    WDRenderStateDepthTest::SharedPtr _pStateDepthTestDisabled;
    // 字体
    WDText2DRender _textRender;
public:
    ExtrVertexRenderObject(CreateExtrusionDialog& d);
    ~ExtrVertexRenderObject();;
protected:
    virtual void updateAabb(WDContext& context, const WDScene& scene) override;
    virtual void update(WD::WDContext& context, const WDScene&) override;
    virtual void render(WD::WDContext& context, const WDScene&) override;
};

WD_NAMESPACE_END

class CreateExtrusionDialog : public QDialog
    , public WD::WDCapturePositioningMonitor
{
    Q_OBJECT
public:
    /**
     * @brief 定义Loop顶点的方式
    */
    enum Method
    {
        // 默认(直接向表格中添加世界坐标系的点)
        M_Default,
        // 捕捉 Capture
        M_Capture,
        // 输入坐标 Input Coordinate
        M_InputCoord,
        // 极坐标(方向 + 距离) Polar Coordinate
        M_PolarCoord,
        // 曲线
        M_Curve,
    };
public:
    CreateExtrusionDialog(WD::WDCore& app, QWidget *parent = Q_NULLPTR);
    ~CreateExtrusionDialog();
public:
    // 创建顶点的方法
    enum CreateMethods
    {
        // 未选中任何方法
        Default = 0,
        // 输入坐标
        InputCoordinates,
        // 捕捉方式
        Catch,
        // 输入方向+数值方式
        DirectionNumber
    };
protected:
    virtual void showEvent(QShowEvent* evt) override;
    virtual void hideEvent(QHideEvent* evt) override;
private slots:

    // 负实体状态切换
    void    slotCheckBoxNegativeStateChanged(int state);
    // 创建顶点方式改变槽函数
    void    slotComboBoxMethodActived(int index);

    // 添加按钮按下槽函数
    void    slotPushButtonAddClicked();
    // 导入表格顶点数据槽函数
    void    slotPushButtonImportClicked();
    // 导出表格顶点数据槽函数
    void    slotPushButtonExportClicked();
    // 删除按钮按下槽函数
    void    slotPushButtonDeleteClicked();
    // 清除
    void    slotPushButtonClearClicked();
    // 确定
    void    slotPushButtonOKClicked();
    // 插入曲线顶点
    void    slotCurveEquationDialogApply(const WD::DVec3Vector& points);
private:
    // 获取当前顶点描述的拉升体的世界坐标位置旋转矩阵
    std::optional<WD::DMat4> globalTRMat() const;
    // 设置绘制对象激活状态
    void activeRenderObject(bool bActive);
    // 更新拉伸体坐标系浏览轴显隐
    void updateAxisVisible();
    // 根据模型树当前选中节点获取管嘴可挂载的父节点
    WD::WDNode::SharedPtr getParentNode();
private:
    // 模型树当前节点改变
    void onNodeTreeCurrentNodeChanged(WD::WDNode::SharedPtr pCurrNode
        , WD::WDNode::SharedPtr pPrevNode
        , WD::WDNodeTree& sender);
    // 界面翻译
    void retranslateUi();
private:
    Ui::CreateExtrusionDialog            ui;
    WD::WDCore&                         _app;
    //
    UiPositionCaptureHelpter            _positionCaptureHelpter;
    //
    UiLoopVerticesEditHelpter           _loopVerticesEditHelpter;
    // 
    UiPositionInputDialog*              _pPositionInputDialog;
    //
    UiPolarCoordInputDialog*            _pPolarCoordInputDialog;
    // 顶点绘制对象
    WD::ExtrVertexRenderObject          _renderObject;
    // 节点名称助手
    UiNodeNameHelpter                   _nameHelpter;

    CurveEquationDialog*                _pCurveEquationDialog;
    friend class WD::ExtrVertexRenderObject;
};
