#include "UiComDesignHyperbola.h"

UiComDesignHyperbola::UiComDesignHyperbola(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject *parent)
    : QObject(parent)
    , IUiComponent(mainWindow, attrs)
{
    _pCreateExtrusionDialog     = new CreateExtrusionDialog(mainWindow.core(), mWindow().widget());
    _pCreateRevolutionDialog    = new CreateRevolutionDialog(mainWindow.core(), mWindow().widget());
}


UiComDesignHyperbola::~UiComDesignHyperbola()
{
    if (_pCreateExtrusionDialog != nullptr)
    {
        delete _pCreateExtrusionDialog;
        _pCreateExtrusionDialog = nullptr;
    }
    if (_pCreateRevolutionDialog != nullptr)
    {
        delete _pCreateRevolutionDialog;
        _pCreateRevolutionDialog = nullptr;
    }
}

void UiComDesignHyperbola::onNotice(UiNotice * pNotice)
{
    int nType = pNotice->type();
    switch (nType)
    {
    case UiNoticeType::UNT_Action:
    {
        UiActionNotice* pActionNotice = static_cast<UiActionNotice*>(pNotice);
        // 创建
        if (pActionNotice->action().is("action.design.create.extrusion.hyperbola"))
        {
            if (_pCreateExtrusionDialog->isHidden())
                _pCreateExtrusionDialog->show();
            else
                _pCreateExtrusionDialog->activateWindow();
        }
        else if (pActionNotice->action().is("action.design.create.revolution.hyperbola"))
        {
            if (_pCreateRevolutionDialog->isHidden())
                _pCreateRevolutionDialog->show();
            else
                _pCreateRevolutionDialog->activateWindow();
        }
    }
    break;
    default:
        break;
    }
}