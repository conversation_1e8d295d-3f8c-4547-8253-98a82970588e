#include "UiComNodeDelete.h"
#include "core/nodeTree/WDNodeTree.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/WDTranslate.h"
#include "core/extension/WDPluginToolSet.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiCommon.h"
#include "core/extension/WDPluginFormat.h"
#include "core/extension/WDExtensionMgr.h"
#include <qfiledialog.h>
#include "businessModule/WDBMPermissionMgr.h"

UiComNodeDelete::UiComNodeDelete(IMainWindow& mainWindow
    , const UiComponentAttributes& attrs
    , QObject *parent)
    : QObject(parent)
    , IUiComponent(mainWindow, attrs)
{
    _pDeleteDialog = new NodeDeleteDialog(mWindow().core(), mWindow().widget());
}
UiComNodeDelete::~UiComNodeDelete()
{
    if (_pDeleteDialog != nullptr)
    {
        delete _pDeleteDialog;
        _pDeleteDialog = nullptr;
    }
}

void UiComNodeDelete::onNotice(UiNotice * pNotice)
{
    int nType = pNotice->type();
    switch (nType)
    {
    case UiNoticeType::UNT_AllReady:
        {

        }
        break;
    case UiNoticeType::UNT_ReadyUnload:
        {
        }
        break;
    case UiNoticeType::UNT_Action:
        {
            UiActionNotice* pActionNotice = static_cast<UiActionNotice*>(pNotice);
            //节点删除
            if (pActionNotice->action().is("action.node.delete"))
            {
                if (_pDeleteDialog->isHidden())
                    _pDeleteDialog->show();
                else
                    _pDeleteDialog->activateWindow();
            }
        }
        break;
    default:
        break;
    }
}
