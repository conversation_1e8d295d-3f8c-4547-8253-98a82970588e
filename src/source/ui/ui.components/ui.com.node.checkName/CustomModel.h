#pragma once

#include <QAbstractListModel>
#include "core/node/WDNode.h"

/**
 * @brief 查找结果的
*/
class FindResultModel
    : public QAbstractListModel
{
    Q_OBJECT
public:
    FindResultModel();
    ~FindResultModel();
public:
    /**
     * @brief 设置模型管理的数据
     * @param names 
    */
    void setData(const std::vector< std::string>& names);
    inline std::string getData(const int& index)const 
    {
        if(index < 0 || index >= _names.size())
        {
            return "";
        }
        return _names[index];
    }
private:
    virtual QVariant data(const QModelIndex& index, int role = Qt::DisplayRole) const override;
    virtual int rowCount(const QModelIndex& parent = QModelIndex()) const override;
private:
    // 节点重复的名称集
    std::vector<std::string>    _names;
};


/**
 * @brief 名称重复的节点组视图的Model
 * 用于管理同一名称的节点组（数据）
*/
class RepeatNodeModel 
    : public QAbstractListModel
    , public WD::WDNodeObserver
{
    Q_OBJECT

public:
    RepeatNodeModel();
    ~RepeatNodeModel();
public:
    /**
     * @brief 设置列表数据
     * @param nodes
    */
    void setData(const std::vector<WD::WDNode::WeakPtr>& nodes);
    /**
    * @brief 获取index对应的节点
    */
    inline WD::WDNode::SharedPtr getNode(int index)
    {
        if (index < 0 || index > _nodes.size())
            return nullptr;
        return _nodes[index].lock();
    }
    /**
     * @brief 清空数据源 并从节点的观察者中移除
    */
    void clear();
private:
    virtual int rowCount(const QModelIndex& parent = QModelIndex()) const override;
    virtual QVariant data(const QModelIndex& index, int role = Qt::DisplayRole) const override;

    /**
     * @brief 节点被编辑通知
     * @param pNode 发送者
    */
    virtual void onNodeAttributeValueChanged(const std::string_view& name
        , const WD::WDBMAttrValue& currValue
        , const WD::WDBMAttrValue& prevValue
        , WD::WDNode::SharedPtr pNode)override;
    /**
     * @brief 节点析构通知
     * @param pNode 
    */
    virtual void onNodeDestroyBefore(WD::WDNode::SharedPtr pNode) override;

private:
    // 同一名称的节点组
    std::vector<WD::WDNode::WeakPtr>    _nodes;
};