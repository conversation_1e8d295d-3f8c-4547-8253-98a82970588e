#include "CustomModel.h"


FindResultModel::FindResultModel()
{
    _names.clear();
}

FindResultModel::~FindResultModel()
{
    _names.clear();
}

void FindResultModel::setData(const std::vector< std::string>& names)
{
    beginResetModel();
    _names.clear();
    _names = names;
    endResetModel();
}

QVariant FindResultModel::data(const QModelIndex& index, int role) const
{
    if(!index.isValid())
        return QVariant();
    if (role == Qt::DisplayRole)
    {
        return QString::fromUtf8(_names[index.row()].c_str());
    }
    return QVariant();
}
int FindResultModel::rowCount(const QModelIndex& parent) const
{
    Q_UNUSED(parent);
    return static_cast<int>(_names.size());
}


RepeatNodeModel::RepeatNodeModel()
{}

RepeatNodeModel::~RepeatNodeModel()
{
    this->clear();
}

void RepeatNodeModel::setData(const std::vector<WD::WDNode::WeakPtr>& nodes)
{
    beginResetModel();
    // 1.清空数据并 移除观察者
    this->clear();
    // 2. 重新更新列表
    for (auto pNode : nodes)
    {
        auto pShrNode = pNode.lock();
        if (pShrNode == nullptr)
            continue;
        // 当前节点为顶层节点，将当前model添加到节点的观察者中
        pShrNode->observers() += this;
        _nodes.push_back(pNode);
    }
    endResetModel();
}
void RepeatNodeModel::clear()
{
    for (auto node : _nodes)
    {
        if (node.expired())
            continue;
        node.lock()->observers() -= this;
    }
    _nodes.clear();
}

int RepeatNodeModel::rowCount(const QModelIndex& parent) const
{
    Q_UNUSED(parent);
    return static_cast<int>(_nodes.size());
}

QVariant RepeatNodeModel::data(const QModelIndex& index, int role) const
{
    if (!index.isValid() || index.row() >= _nodes.size())
        return QVariant();

    if (role == Qt::DisplayRole)
    {
        auto pNode = _nodes[index.row()].lock();
        if (pNode != nullptr)
            return QString::fromUtf8(pNode->name().c_str());
    }
    return QVariant();
}

void RepeatNodeModel::onNodeAttributeValueChanged(const std::string_view& name
    , const WD::WDBMAttrValue& currValue
    , const WD::WDBMAttrValue& prevValue
    , WD::WDNode::SharedPtr pNode)
{
    Q_UNUSED(name);
    Q_UNUSED(currValue);
    Q_UNUSED(prevValue);
    Q_UNUSED(pNode);

    // 强制更新界面
    beginResetModel();
    endResetModel();
}
void RepeatNodeModel::onNodeDestroyBefore(WD::WDNode::SharedPtr pNode)
{
    // 重搜索结果中移除pNode或pNode的子孙节点
    if (pNode == nullptr)
        return;
    beginResetModel();
    for (auto it = _nodes.begin(); it != _nodes.end(); )
    {
        // 如果节点树删除的节点是结果表单里的节点或是表单节点的祖宗节点
        auto pSNode = (*it).lock();
        if (pSNode == nullptr)
        {
            it = _nodes.erase(it);
        }
        else if (pNode == pSNode)
        {
            pNode->observers() -= this;
            it = _nodes.erase(it);
        }
        else
        {
            ++it;
        }
    }
    endResetModel();
}