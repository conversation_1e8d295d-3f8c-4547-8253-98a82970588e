#include "UiComNodeCheckName.h"

UiComNodeCheckName::UiComNodeCheckName(IMainWindow& mainWindow, const UiComponentAttributes& attrs)
    :IUiComponent(mainWindow, attrs)
    , _core(mainWindow.core())
{
    _pNodeCheckNameDialog = new NodeCheckNameDialog(_core, mainWindow.widget());
}
UiComNodeCheckName::~UiComNodeCheckName()
{
    if (_pNodeCheckNameDialog != nullptr)
    {
        delete _pNodeCheckNameDialog;
        _pNodeCheckNameDialog = nullptr;
    }
}

void UiComNodeCheckName::onNotice(UiNotice* pNotice)
{
    auto nType = pNotice->type();
    switch(nType)
    {
    case UiNoticeType::UNT_Action:
        {
            UiActionNotice* pActionNotice = static_cast<UiActionNotice*>(pNotice);
            if (pActionNotice->action().is("action.node.checkName") )
            {
                if (_pNodeCheckNameDialog->isHidden())
                    _pNodeCheckNameDialog->show();
                else
                    _pNodeCheckNameDialog->activateWindow();
            }
        }
        break;
    }
}
