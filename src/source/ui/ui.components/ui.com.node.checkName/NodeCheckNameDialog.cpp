#include "NodeCheckNameDialog.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/nodeTree/WDNodeTree.h"
#include "core/businessModule/typeMgr/WDBMTypeMgr.h"
#include "core/node/WDNode.h"
#include "core/message/WDMessage.h"

NodeCheckNameDialog::NodeCheckNameDialog(WD::WDCore& core, QWidget* parent)
    :QDialog(parent)
    , _core(core)
{
    ui.setupUi(this);
    _datas.clear();
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));

    _findResultModel = new FindResultModel();
    _findResultView = new FindResultView(this);
    _findResultView->setModel(_findResultModel);
    ui.verticalLayoutFindResult->addWidget(_findResultView);
    // 重复节点
    _repeatNodeModel = new RepeatNodeModel();
    _repeatNodeView = new RepeatNodeView(_core, this);
    _repeatNodeView->setModel(_repeatNodeModel);
    ui.verticalLayoutRepeatNode->addWidget(_repeatNodeView);
    retranslateUi();

    connect(ui.pushBtnCheck, &QPushButton::clicked, this, &NodeCheckNameDialog::slotBtnCheckClicked);
    connect(ui.pushBtnResetIllegalName, &QPushButton::clicked, this, &NodeCheckNameDialog::slotBtnResetIllegalNameClicked);
    // 更新重复节点
    connect(_findResultView, &FindResultView::sigCurrentChanged, this, [=](const QString& text){
        if(text.isEmpty())
        {
            _repeatNodeModel->setData(std::vector<WD::WDNode::WeakPtr>());
            return;
        }
        auto it =  _datas.find(text.toUtf8().toStdString());
        if(it == _datas.end())
        {
            _repeatNodeModel->setData(std::vector<WD::WDNode::WeakPtr>());
        }
        else
        {
            _repeatNodeModel->setData(it->second);
        }
    });
}

NodeCheckNameDialog::~NodeCheckNameDialog()
{
    _datas.clear();
    if(_findResultModel != nullptr)
    {
        delete _findResultModel;
        _findResultModel = nullptr;
    }
    if(_findResultView != nullptr)
    {
        delete _findResultView;
        _findResultView = nullptr;
    }
    if (_repeatNodeModel != nullptr)
    {
        delete _repeatNodeModel;
        _repeatNodeModel = nullptr;
    }
    if (_repeatNodeView != nullptr)
    {
        delete _repeatNodeView;
        _repeatNodeView = nullptr;
    }
}

void NodeCheckNameDialog::slotBtnCheckClicked()
{
    // 清空旧数据
    _datas.clear();

    // 获取名称重复的节点
    auto pBm = _core.currentBM();
    if (pBm == nullptr)
        return;
    auto pRoot = pBm->root();
    if (pRoot == nullptr)
        return;

    // 收集树上所有名称对应的节点集
    collectName2Nodes(pRoot);

    // 去除名称唯一的节点
    for (auto it = _datas.begin(); it != _datas.end(); )
    {
        if (it->second.size() <= 1)
        {
                it = _datas.erase(it);
        }
        else
        {
            ++it;
        }
    }

    // 更新查找结果列表
    std::vector<std::string> names;
    names.reserve(_datas.size());
    for (auto it = _datas.begin(); it != _datas.end();)
    {
        names.push_back(it->first);
        ++it;
    }
    // 更新查找结果
    _findResultModel->setData(names);
    _repeatNodeModel->setData({});
}

void NodeCheckNameDialog::slotBtnResetIllegalNameClicked()
{
    auto pBm = _core.currentBM();
    if(pBm == nullptr)
        return;

    auto pRoot = pBm->root();
    if(pRoot == nullptr)
        return;

    // 递归整棵树，重置非法名称（2025.3.3-新命名规范非法名称）
    WD::WDNode::RecursionHelpter(*pRoot, [pBm](WD::WDNode& node)
        {
            if (!pBm->nameValid(node.srcName()))
            {
                node.setAttribute("Name", std::string(""));
            }
        });

    // 触发重名检查，更新当前重名信息
    slotBtnCheckClicked();

    WD_INFO_T("NodeCheckNameDialog", "Reset illegal name success");
}

void NodeCheckNameDialog::collectName2Nodes(const WD::WDNode::SharedPtr topNode)
{
    if (topNode == nullptr)
        return;
    _datas[topNode->name()].push_back(topNode);

    // 递归处理子节点
    for (auto pNode : topNode->children())
    {
        collectName2Nodes(pNode);
    }
}
void NodeCheckNameDialog::retranslateUi()
{
    Trs("NodeCheckNameDialog"
        , static_cast<QDialog*>(this)
        , ui.pushBtnCheck
        , ui.groupBoxFindResult
        , ui.groupBoxRepeatNode
        , ui.pushBtnResetIllegalName
    );
}
