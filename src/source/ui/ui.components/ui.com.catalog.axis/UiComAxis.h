#pragma once

#include <QObject>
#include "../../wizDesignerApp/UiInterface/UiInterface.h"
#include "core/common/WDConfig.h"
#include "core/scene/WDRenderObject.h"
#include "core/viewer/primitiveRender/WDTextRender.h"
#include "core/geometry/standardPrimitives/WDGeometrySphere.h"
#include "core/geometry/WDGeometryPolyhedron.h"

class UiComAxis;
WD_NAMESPACE_BEGIN
/**
 * @brief 原点坐标轴绘制对象
*/
class OriginAxisRenderObject: public WDRenderObject
{
private :
    UiComAxis&                          _d;
    // 线宽
    WDRenderStateLineWidth::SharedPtr   _pStateLineWidth;
    // 禁用深度测试
    WDRenderStateDepthTest::SharedPtr   _pStateDepthTestFalse;
    // 虚线绘制
    WDRenderStateLineStipple::SharedPtr _pStateDashLine;

    // 轴材质
    WDMaterialColor::SharedPtr          _pMaterialAxis;
    // 轴几何体数据
    WDGeometryPolyhedron::SharedPtr     _pGeomAxis;

    // 单位点几何体
    WDGeometrySphere::SharedPtr         _pGeomPoint;
    // 单位点材质
    WDMaterialPhong                     _materialPoint;

    // 箭头材质
    WDMaterialPhong::SharedPtr          _pMaterialArrow;
    // 箭头几何体数据
    WDGeometry::SharedPtr               _pGeomArrow;
    // 箭头变换数据
    std::array<FMat4, 3>                _arrowsTransform;

    // 2d文本对象
    WDText2DRender                      _textRender;

    // 轴顶点
    FVec3                               _moveAxis[6];
    // 箭头顶点
    std::vector<FVec3>                  _axisAr;

    // 轴线宽
    float                               _lineWidth;
    // 箭头地面直径和高度
    float                               _arrowBottomDiameter;
    float                               _arrowHeight;

public:
    OriginAxisRenderObject(UiComAxis& d);
protected:
    virtual void updateAabb(WDContext& context, const WDScene& scene) override;
    virtual void update(WDContext& context, const WDScene&) override;
    virtual void render(WDContext& context, const WDScene&) override;

private:
    /**
     * @brief 添加点
     * @param context 上下文
     * @param insts 实例
     * @param pos 坐标
    */
    void addPointToInstance(WDContext& context, WDInstances& insts, const DVec3& pos);
};
WD_NAMESPACE_END

class UiComAxis
    : public QObject
	, public IUiComponent
{
    Q_OBJECT

private:
    friend class WD::OriginAxisRenderObject;

private:
    WD::WDCore&                             _core;
    // 原点坐标轴绘制
    WD::OriginAxisRenderObject              _renderObject;
    // 原点坐标轴矩阵
    WD::DMat4                               _transform;
    // 坐标轴单轴长度
    float                                   _axisLength;
    // 轴线透明度0-255
    int                                     _axisAlpha;
    // 单位长度
    float                                   _perLength;
    // 单位点半径
    float                                   _pointRadio;
    // 单位点透明度0-255
    int                                     _pointAlpha;
public:
    UiComAxis(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject *parent = nullptr);
    ~UiComAxis();

public:
    /**
    * @brief 获取轴的位置
    * @return 轴的位置
    */
    inline WD::DVec3 position() const
    {
        return _transform.extractTranslation();
    }
    /**
    * @brief 获取X轴方向
    * @return X轴方向
    */
    inline WD::DVec3 axisX() const
    {
        WD::DMat3 t     = WD::DMat4::ToMat3(_transform);
        WD::DVec3 axis  = WD::DVec3::Normalize(t * WD::DVec3::AxisX());
        return axis;
    }
    /**
    * @brief 获取Y轴方向
    * @return Y轴方向
    */
    inline WD::DVec3 axisY() const
    {
        WD::DMat3 t     = WD::DMat4::ToMat3(_transform);
        WD::DVec3 axis  = WD::DVec3::Normalize(t * WD::DVec3::AxisY());
        return axis;
    }
    /**
    * @brief 获取Z轴方向
    * @return Z轴方向
    */
    inline WD::DVec3 axisZ() const
    {
        WD::DMat3 t     = WD::DMat4::ToMat3(_transform);
        WD::DVec3 axis  = WD::DVec3::Normalize(t * WD::DVec3::AxisZ());
        return axis;
    }

protected:
    /**
    *   @brief 通知事件
    *   @param pNotice 事件对象
    */
    virtual void onNotice(UiNotice* pNotice) override;

private:
    /**
     * @brief 配置项(显示坐标轴)值更改通知响应
     * @param item 配置项
    */
    void onCfgAxisVisibleValueChanged(const WD::WDConfigItem& item);
    /**
     * @brief 配置项(单轴长度)值更改通知响应
     * @param item 配置项
    */
    void onCfgAxisLengthValueChanged(const WD::WDConfigItem& item);
    /**
     * @brief 配置项(轴线透明度)值更改通知响应
     * @param item 配置项
    */
    void onCfgAxisAlphaValueChanged(const WD::WDConfigItem& item);
    /**
     * @brief 配置项(单位长度)值更改通知响应
     * @param item 配置项
    */
    void onCfgAxisPerLengthValueChanged(const WD::WDConfigItem& item);
    /**
     * @brief 配置项(单位点半径)值更改通知响应
     * @param item 配置项
    */
    void onCfgAxisPointRadioValueChanged(const WD::WDConfigItem& item);
    /**
     * @brief 配置项(单位点透明度)值更改通知响应
     * @param item 配置项
    */
    void onCfgAxisPointAlphaValueChanged(const WD::WDConfigItem& item);
};

