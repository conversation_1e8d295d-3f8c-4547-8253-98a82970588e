<?xml version="1.0" encoding="utf-8"?>

<TS language="简体中文">
	<context>
		<name>MembersDialog</name>
		<message>
			<source>Members</source>
			<translation>节点导航</translation>
		</message>
		<message>
			<source>Control</source>
			<translation>控制</translation>
		</message>
		<message>
			<source>GoTo</source>
			<translation>转至</translation>
		</message>
		<message>
			<source>DrawList</source>
			<translation>列表</translation>
		</message>
		<message>
			<source>Owner</source>
			<translation>父对象</translation>
		</message>
		<message>
			<source>First</source>
			<translation>第一个</translation>
		</message>
		<message>
			<source>Last</source>
			<translation>最后一个</translation>
		</message>
		<message>
			<source>First member</source>
			<translation>第一个成员</translation>
		</message>
		<message>
			<source>Last member</source>
			<translation>最后一个成员</translation>
		</message>
		<message>
			<source>Old CE</source>
			<translation>上一次对象</translation>
		</message>
		<message>
			<source>Reference...</source>
			<translation>对象引用</translation>
		</message>
		<message>
			<source>The list is exhausted for</source>
			<translation>列表已用尽</translation>
		</message>
		<message>
			<source>The command Before,	Next are illegal at WORLD level</source>
			<translation>前一个、后一个命令在根节点层次是非法的</translation>
		</message>
		<message>
			<source>No child nodes</source>
			<translation>没有子节点</translation>
		</message>
		<message>
			<source>the content cannot be found</source>
			<translation>内容无法找到</translation>
		</message>
	</context>

	<context>
		<name>ReferenceDialog</name>
		<message>
			<source>Reference</source>
			<translation>对象引用</translation>
		</message>
	</context>
</TS>