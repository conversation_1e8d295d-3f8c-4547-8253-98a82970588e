<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ReferenceDialogClass</class>
 <widget class="QDialog" name="ReferenceDialogClass">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>214</width>
    <height>192</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Reference</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <widget class="QListWidget" name="listWidget">
     <property name="styleSheet">
      <string notr="true">QListWidget::item{border-bottom: 1px solid lightgray;}
QListWidget::item::selected{background-color:lightgray;}</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
