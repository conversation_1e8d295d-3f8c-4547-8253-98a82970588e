#include "ReferenceDialog.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "core/businessModule/WDBMBase.h"

ReferenceDialog::ReferenceDialog(QWidget *parent)
    : QDialog(parent)
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    this->retranslateUi();

    connect(ui.listWidget, &QListWidget::currentItemChanged, this, [this]() 
        {
            auto pItem = ui.listWidget->currentItem();
            if (pItem == nullptr)
                return;
            auto pNode = getItemUData(*pItem);
            if (pNode == nullptr)
                return;
            emit noticeMemberDialog(pNode);
        });
}

ReferenceDialog::~ReferenceDialog()
{
}


void ReferenceDialog::updateWidget(WD::WDNode::SharedPtr pNode)
{
    ui.listWidget->clear();
    auto refAttrs = getNodeElement(pNode);
    fillWidget(refAttrs, pNode);
}


std::vector<WD::WDBMAttrDesc*> ReferenceDialog::getNodeElement(WD::WDNode::SharedPtr pNode)
{
    std::vector<WD::WDBMAttrDesc*> rRefAttrs;
    auto pTypeDesc = pNode->getTypeDesc();

    if (pTypeDesc == nullptr)
        return rRefAttrs;

    const auto& attrs = pTypeDesc->attrDescs();
    rRefAttrs.reserve(attrs.size());

    for (const auto& pAttr : attrs) 
    {
        if (pAttr == nullptr)
            continue;
        if (pAttr->type() != WD::WDBMAttrValueType::T_NodeRef)
            continue;
        rRefAttrs.push_back(pAttr);
    }
    return rRefAttrs;
}

void ReferenceDialog::fillWidget(const std::vector<WD::WDBMAttrDesc*>& rRefAttrs, WD::WDNode::SharedPtr pNode)
{

    auto pBMBase = pNode->getBMBase();
    if (pBMBase == nullptr)
    {
        assert(false && "pBMBase == nullptr");
        return;
    }

    for (const auto& pAttr : rRefAttrs)
    {
        if (pAttr == nullptr)
            continue;

        if (pAttr->flags().hasFlag(WD::WDBMAttrDesc::F_Hidden))
            continue;

        auto item = getNodeRefAttrValue(*pNode, *pAttr);

        const auto& attrName = item.first;
        auto pTNode = item.second;
        if (pTNode == nullptr)
            continue;
        const std::string& attrValue = pTNode->name();
        QString text = QString("%1  /%2").arg(QString::fromUtf8(pBMBase->trA(attrName).c_str())).arg(QString::fromUtf8(attrValue.c_str()));
        QListWidgetItem* pWidgetItem = new QListWidgetItem(text);
        setItemUData(*pWidgetItem, pTNode);
        ui.listWidget->addItem(pWidgetItem);
    }
   
}

std::pair<std::string, WD::WDNode::SharedPtr> ReferenceDialog::getNodeRefAttrValue(const WD::WDNode& node, const WD::WDBMAttrDesc& refAttr)
{
    std::pair<std::string, WD::WDNode::SharedPtr> ret;

    ret.first = refAttr.name();

    auto rValue = node.getAttribute(refAttr.name());
    auto pRValue = rValue.data<WD::WDBMNodeRef>();
    if (pRValue == nullptr)
        return ret;
    ret.second = pRValue->refNode();
    
    return ret;
}


void ReferenceDialog::setItemUData(QListWidgetItem& item, WD::WDNode::SharedPtr pNode)
{
    QVariant userData;
    userData.setValue(UiWeakObject(pNode));
    item.setData(Qt::UserRole, userData);
}

WD::WDNode::SharedPtr ReferenceDialog::getItemUData(const QListWidgetItem& item)
{
    QVariant userData = item.data(Qt::UserRole);
    if (!userData.isValid() || userData.isNull())
        return nullptr;
    auto obj = userData.value<UiWeakObject>();
    return obj.subObject< WD::WDNode>();
}


void ReferenceDialog::retranslateUi()
{
    Trs("ReferenceDialog"
        , static_cast<QWidget*>(this));
}