set(TARGET_NAME ui_com_clip_plane)

set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON) 
find_package(Qt5 COMPONENTS Core Widgets Xml REQUIRED)

set(HEADER_FILES
	"QtClipPlane.h"
)

set(SOURCE_FILES
	"QtClipPlane.cpp"
	"main.cpp"
)

set(FORM_FILES
	"QtClipPlane.ui"
)

add_library(${TARGET_NAME} SHARED
		${HEADER_FILES}
		${SOURCE_FILES}
		${FORM_FILES}
)

target_compile_definitions(${TARGET_NAME} PRIVATE
	-DUI_COM_CLIP_PLANE_LIB
)

target_link_libraries(${TARGET_NAME} PUBLIC wizDesignerCore QtnRibbon)
target_link_libraries(${TARGET_NAME} PUBLIC Qt5::Core Qt5::Widgets Qt5::Xml)
target_include_directories(${TARGET_NAME} PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
