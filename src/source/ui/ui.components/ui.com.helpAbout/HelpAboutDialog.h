#pragma once

#include <QDialog>
#include "ui_HelpAboutDialog.h"
#include "core/node/WDNode.h"
#include "core/WDCore.h"
#include "UpdatedMessageDialog.h"

WD_NAMESPACE_USE

class HelpAboutDialog : public QDialog
{
	Q_OBJECT

public:
	HelpAboutDialog(IMainWindow& mainWindow, QWidget *parent = Q_NULLPTR);
	~HelpAboutDialog();
private slots:
	/**
	 * @brief 确定按键点击响应槽函数
	*/
	void slotPushButtonOkClicked();
private:
	/**
	* @brief 界面文本翻译
	*/
	void retranslateUi();
protected:
	virtual void showEvent(QShowEvent* event) override;
	virtual void hideEvent(QHideEvent* event) override;

private:
	Ui::HelpAboutDialog  ui;
	UpdatedMessageDialog* _pUpdatedMsgUi;
	WD::WDCore& _app;
};

