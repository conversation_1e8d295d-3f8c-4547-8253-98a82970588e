#pragma once

#include    <QObject>
#include     "../../wizDesignerApp/UiInterface/UiInterface.h"
#include    "CreateSectionDialog.h"

class UiComDesignCreateSection
    : public QObject
    , public IUiComponent
{
    Q_OBJECT

public:
    UiComDesignCreateSection(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject *parent = nullptr);
    ~UiComDesignCreateSection();

public:
    /**
    *   @brief 通知事件
    *   @param pNotice 事件对象
    */
    virtual void onNotice(UiNotice* pNotice) override;

private:
    CreateSectionDialog*             _pCreateSection;
};


