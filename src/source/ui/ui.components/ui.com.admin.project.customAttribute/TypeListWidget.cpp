#include "TypeListWidget.h"
#include "core/businessModule/WDBMBase.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "core/message/WDMessage.h"

TypeListWidget::TypeListWidget(WD::WDCore& core, QWidget *parent)
    : _core(core)
    , QWidget(parent)
{
    ui.setupUi(this);
    retranslateUi();
    // 设置选择模式为单选
    ui.tableWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    // 设置选择行为为整行选择
    ui.tableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    // 表头可拉动
    ui.tableWidget->horizontalHeader()->setSectionResizeMode(QHeaderView::Interactive);
    // 表头最后一列拉伸至表尾
    ui.tableWidget->horizontalHeader()->setStretchLastSection(true);

    connect(ui.tableWidget
        , &QTableWidget::currentItemChanged
        , this
        , [=](QTableWidgetItem* current, QTableWidgetItem* previous) {
        WDUnused(previous);
        if (current == nullptr)
        {
            assert(false);
            return;
        }
        _typeIndex = current->row();
        emit sigCurrentTypeIndexChanged(current->row());
    });
    connect(ui.lineEditSearch, &QLineEdit::textChanged, this, &TypeListWidget::slotLineEditeSearchTextChanged);
}
TypeListWidget::~TypeListWidget()
{
}

void TypeListWidget::slotLineEditeSearchTextChanged(const QString& text)
{
    if (text.isEmpty())
    {
        for (int i = 0; i < ui.tableWidget->rowCount(); i++)
            ui.tableWidget->setRowHidden(i, false);
        return;
    }

    // 先将所有行隐藏
    for (int i = 0; i < ui.tableWidget->rowCount(); i++)
        ui.tableWidget->setRowHidden(i, true);

    // 获取文本与搜索字符串匹配的单元格
    auto list = ui.tableWidget->findItems(text, Qt::MatchFlag::MatchContains);
    if(list.isEmpty())
    {
        ui.tableWidget->setCurrentCell(-1, 0);
        return;
    }

    // 显示文本匹配的行
    for (auto& each : list)
    {
        if (each != nullptr)
            ui.tableWidget->setRowHidden(each->row(), false);
    }
    // 设置当前行为第0行
    if(ui.tableWidget->rowCount() > 0)
        ui.tableWidget->setCurrentCell(list.front()->row(), 0);
}

void TypeListWidget::updateWidget(std::string modelName, const std::vector<std::string>& types) 
{
    ui.tableWidget->blockSignals(true);

    // 清空表格
    ui.tableWidget->clearContents();
    auto pBmBase = _core.getBMBase(modelName);
    if(pBmBase == nullptr)
    {
        WD_WARN_T("TypeListWidget", "get bmObj failure, please check model type");
        return;
    }

    // 设置行数
    ui.tableWidget->setRowCount(static_cast<int>(types.size()));

    // 初始化表格
    for(auto i = 0; i < types.size(); ++i)
    {
        const auto& typeName = types[i];
        const auto&  translateName = pBmBase->trT(typeName);
        QTableWidgetItem* nameItem = new QTableWidgetItem(QString::fromUtf8(typeName.c_str()));
        QTableWidgetItem* translateItem = new QTableWidgetItem(QString::fromUtf8(translateName.c_str()));

        ui.tableWidget->setItem(i, 0, nameItem);
        ui.tableWidget->setItem(i, 1, translateItem);
        nameItem->setFlags(Qt::ItemFlag::NoItemFlags | Qt::ItemFlag::ItemIsSelectable | Qt::ItemFlag::ItemIsEnabled);
        translateItem->setFlags(Qt::ItemFlag::NoItemFlags | Qt::ItemFlag::ItemIsSelectable | Qt::ItemFlag::ItemIsEnabled);
    }
    ui.tableWidget->blockSignals(false);

    if(ui.tableWidget->rowCount() > 0 && ui.tableWidget->columnCount() > 0)
    {
        ui.tableWidget->setCurrentCell(0, 0);
    }

    auto searchStr = ui.lineEditSearch->text();
    if(!searchStr.isEmpty())
        slotLineEditeSearchTextChanged(searchStr);
}
void TypeListWidget::retranslateUi()
{
    Trs("TypeListWidget"
    , static_cast<QWidget*>(this)
    , ui.labelSearch
    , ui.tableWidget
    );
}
