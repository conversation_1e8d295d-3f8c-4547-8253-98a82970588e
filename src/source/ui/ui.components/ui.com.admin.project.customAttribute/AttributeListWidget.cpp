#include "AttributeListWidget.h"
#include "core/businessModule/WDBMBase.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "core/message/WDMessage.h"

AttributeListWidget::AttributeListWidget(WD::WDCore& core, QWidget *parent)
    : _core(core)
    , QWidget(parent)
{
    ui.setupUi(this);
    retranslateUi();

    // 设置选择模式为单选
    ui.tableWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    // 设置选择行为为整行选择
    ui.tableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    // 表头可拉动
    ui.tableWidget->horizontalHeader()->setSectionResizeMode(QHeaderView::Interactive);
    // 表头最后一列拉伸至表尾
    ui.tableWidget->horizontalHeader()->setStretchLastSection(true);

    ui.tableWidget->setColumnCount(2);

    connect(ui.tableWidget, &QTableWidget::currentCellChanged, this, [this](int currentRow, int currentColumn, int previousRow, int previousColumn){
        WDUnused(currentColumn);
        WDUnused(previousRow);
        WDUnused(previousColumn);
        emit sigCurrentAttrDescChanged(currentRow);
    });
    connect(ui.lineEditSearch, &QLineEdit::textChanged, this, &AttributeListWidget::slotLineEditSearchTextChanged);
}
AttributeListWidget::~AttributeListWidget()
{
}
void AttributeListWidget::updateWidget(const std::string& modelName, const Attributes& attributs)
{
    int oldRow = ui.tableWidget->currentRow();
    int oldRowCount = ui.tableWidget->rowCount();
    ui.tableWidget->blockSignals(true);
    ui.tableWidget->setRowCount(0);
    ui.tableWidget->blockSignals(false);

    if(attributs.size() == 0)
        emit clearAttrDescInfo();
    auto pBmBase = _core.getBMBase(modelName);
    if (pBmBase == nullptr)
    {
        WD_WARN_T("AttributeListWidget", "get bmObj failure, please check model type");
        return;
    }
    ui.tableWidget->blockSignals(true);

    // 设置行号
    ui.tableWidget->setRowCount(static_cast<int>(attributs.size()));
    // 初始化表格
    for (int i = 0; i < static_cast<int>(attributs.size()); ++i)
    {
        const auto& attrDesc = attributs[i];

        const auto& attrName = attrDesc.name();
        const auto& translateName = pBmBase->trT(attrName);
        QTableWidgetItem* nameItem = new QTableWidgetItem(QString::fromUtf8(attrName.c_str()));
        QTableWidgetItem* translateItem = new QTableWidgetItem(QString::fromUtf8(translateName.c_str()));

        ui.tableWidget->setItem(i, 0, nameItem);
        ui.tableWidget->setItem(i, 1, translateItem);
    }
    ui.tableWidget->blockSignals(false);

    auto newRowCount = ui.tableWidget->rowCount();
    if (newRowCount > 0 && ui.tableWidget->columnCount() > 0)
    {
        if(oldRowCount < newRowCount)
        {
            // 新增了1行， 设置最后1行为当前行
            ui.tableWidget->setCurrentCell(newRowCount - 1, 0);
        }
        else if(oldRowCount == newRowCount)
        {
            //行数未发生改变
            ui.tableWidget->setCurrentCell(oldRow, 0);
        }
    }
    auto searchStr = ui.lineEditSearch->text();
    if(!searchStr.isEmpty())
        slotLineEditSearchTextChanged(searchStr);
}
void AttributeListWidget::updateRowItem(const std::string& modelName, int row, const WD::WDBMAttrDesc& attrDesc)
{
    auto pBmBase = _core.getBMBase(modelName);
    if (pBmBase == nullptr)
    {
        WD_WARN_T("AttributeListWidget", "get bmObj failure, please check model type");
        return;
    }

    auto pNameItem = ui.tableWidget->item(row, 0);
    if(pNameItem == nullptr)
        return;

    auto pTranslateItem = ui.tableWidget->item(row, 1);
    if (pTranslateItem == nullptr)
        return;
    // 获取属性的名称
    const auto& attrName = attrDesc.name();

    // 设置index行各单元格的文本
    pNameItem->setText(QString::fromUtf8(attrName.c_str()));
    pTranslateItem->setText(QString::fromUtf8(pBmBase->trT(attrName).c_str()));
}
void AttributeListWidget::slotLineEditSearchTextChanged(const QString& text)
{
    if (text.isEmpty())
    {
        for (int i = 0; i < ui.tableWidget->rowCount(); i++)
            ui.tableWidget->setRowHidden(i, false);
        return;
    }

    // 先将所有行隐藏
    for (int i = 0; i < ui.tableWidget->rowCount(); i++)
        ui.tableWidget->setRowHidden(i, true);

    // 获取文本与搜索字符串匹配的单元格
    auto list = ui.tableWidget->findItems(text, Qt::MatchFlag::MatchContains);
    if (list.isEmpty())
    {
        ui.tableWidget->setCurrentCell(-1, 0);
        return;
    }

    // 显示文本匹配的行
    for (auto& each : list)
    {
        if (each != nullptr)
            ui.tableWidget->setRowHidden(each->row(), false);
    }
    // 设置当前行为第0行
    if (ui.tableWidget->rowCount() > 0)
        ui.tableWidget->setCurrentCell(list.front()->row(), 0);
}
void AttributeListWidget::clear()const
{
    ui.tableWidget->setRowCount(0);
}
void AttributeListWidget::retranslateUi()
{
    Trs("AttributeListWidget"
        , static_cast<QWidget*>(this)
        , ui.labelSearch
        , ui.tableWidget
    );
}