#pragma once

#include "core/WDCore.h"
#include "ui_ProCustomAttrDialog.h"
#include "core/businessModule/typeMgr/WDBMAttrDesc.h"
#include "core/businessModule/typeMgr/WDBMTypeDesc.h"
#include "core/businessModule/WDBMProjectMgr.h"
#include "TypeListWidget.h"
#include "AttributeListWidget.h"
#include "AttributeInfoWidget.h"
#include "TypeInfo.h"
#include "../../wizDesignerApp/UiInterface/ICollaboration.h"
#include <QDialog>
class ProCustomAttrDialog : public QDialog
{
    Q_OBJECT
public:
    ProCustomAttrDialog(WD::WDCore& core, ICollaboration& collaboration, QWidget* parent = nullptr);
    ~ProCustomAttrDialog();
protected:
    virtual void showEvent(QShowEvent* e)override;
    virtual void hideEvent(QHideEvent* e)override;
private slots:
    /**
     * @brief 导入UDA.TXT文件
    */
    void slotPushButtonImportClicked();
private:
    /**
     * @brief 初始化，模块下拉项
    */
    void initModelComboBox();
    /**
     * @brief 使用模块注册的类型初始化模块的TypeInfos
     * @param modelName 
    */
    void initModelType(std::string modelName);
    /**
     * @brief 加载UDA文件
     * @param fileName 
     * @param outTypes 
     * @return 
    */
    bool loadUDAConfigFileFromData(const char* pData, TypeInfos& outTypes);
    /**
     * @brief 保存UDA文件
     * @param fileName 
     * @param outTypes 
     * @return 
    */
    std::string saveUDAConfigFileToData(const TypeInfos& outTypes);
    /**
     * @brief 刷新类型窗口
     * @param types 
    */
    void reflushTypeTable(const TypeInfos& types);
    /**
     * @brief 导入UDA文件
     * @param fileNames 
     * @return 
    */
    std::map<std::string, std::vector<std::string> > importUDAConfigFile(const QStringList& fileNames);
    /**
     * @brief 将导入的udaData合并到modelTypeInfo
     * @param modelTypeInfo 模块的类型信息
     * @param udaData 从UDA文件解析的类型和属性集数据
     * @param modelTypeInfo 模块的类型信息
    */
    void mergeImportUdaData(TypeInfos& modelTypeInfo, const std::map<std::string, std::vector<std::string> >& udaData);
    /**
     * @brief 界面翻译
    */
    void retranslateUi();
private:
    Ui::ProCustomAttrDialog ui;
    WD::WDCore& _core;
    // 网络Api对象
    ICollaboration& _collaboration;
    // 项目列表
    std::vector<WD::WDBMProject*> _projects;
    // 模块和类型信息的映射关系
    std::map<std::string, TypeInfos> _types;
    // 当前类型索引
    int _currTypeIndex;
    // 当前属性索引
    int _currAttrIndex;
    // 类型窗口
    TypeListWidget* _pTypeListWidget;
    // 属性窗口
    AttributeListWidget* _pAttributeListWidget;
    // 属性信息界面
    AttributeInfoWidget* _pAttributeInfoWidget;


    /**
     * @brief 文件信息，提交到服务时使用
     */
    struct FileInfo 
    {
        std::string fileType;
        std::string savedFileName;
    public:
        FileInfo(const std::string& fType = "", const std::string& sFName = "")
            : fileType(fType)
            , savedFileName(sFName)
        {

        }
    };
    std::map<std::string, FileInfo> _fileInfoMap;
};
