#pragma once

#include <QWidget>
#include "core/WDCore.h"
#include "core/businessModule/typeMgr/WDBMAttrDesc.h"
#include "ui_AttributeInfoWidget.h"

class AttributeInfoWidget : public QWidget
{
    Q_OBJECT

public:
    AttributeInfoWidget(WD::WDCore& core, QWidget *parent = Q_NULLPTR);
    ~AttributeInfoWidget();
public: 
    /**
     * @brief 通过WD::WDBMAttrDesc对象更新窗口显示
     * @param attrDesc 
    */
    void updateWidget(WD::WDBMAttrDesc attrDesc);
    /**
     * @brief 获取当前信息的WD::WDBMAttrDesc对象
    */
    bool getAttrDescObj(WD::WDBMAttrDesc& attrDesc);
    /**
     * @brief 清空界面
    */
    void clear()const;
private:
    /**
     * @brief 初始化属性类型下拉项
    */
    void initAttrTypeComboBox();
    /**
     * @brief 初始化值类型下拉项
    */
    void initValueTypeComboBox();
    /**
     * @brief 设置界面的各子控件是否可以编辑
     * @param flag true:可以编辑 false:不可以编辑,默认值
    */
    void setSubWidgetEnabled(bool flag = false);
    /**
     * @brief 界面翻译
    */
    void retranslateUi(); 
private:
    Ui::AttributeInfoWidget ui;
    WD::WDCore& _core;
};
