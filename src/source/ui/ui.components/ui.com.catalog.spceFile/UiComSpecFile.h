#pragma once
#include <QObject>
#include "../../wizDesignerApp/UiInterface/UiInterface.h"
#include "core/common/WDTimestamp.hpp"
#include "core/extension/WDPluginFormat.h"
#include "SpecFileImportDialog.h"

class UiComSpecFile
    : public QObject
    , public IUiComponent
{
    Q_OBJECT

public:
    UiComSpecFile(IMainWindow& mainWindow, const UiComponentAttributes& attrs);
    ~UiComSpecFile();
public:
    /**
    *   @brief 通知事件
    *   @param pNotice 事件对象
    */
    virtual void onNotice(UiNotice* pNotice) override;

private:
    // 记录导入开始的时间
    double _startTime = 0.0;
    WD::WDTimestamp _tms;
    SpecFileImportDialog* _specFileDialog;
};
