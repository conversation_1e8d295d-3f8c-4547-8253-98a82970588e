#pragma once
#include <QWidget>
#include <QList>
#include "ui_UiNodeCoordinate.h"
#include "../../wizDesignerApp/UiInterface/UiInterface.h"
#include "core/graphable/WDGraphableInterface.h"

class UiNodeCoordinate
	: public QDialog
	, public IUiComponent
{
	Q_OBJECT

public:
	UiNodeCoordinate(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QWidget* parent = nullptr);
    ~UiNodeCoordinate();

    /**
    *   @brief 通知事件
    *   @param pNotice 事件对象
    */
    virtual void onNotice(UiNotice* pNotice) override;
	/**
	*   @brief 获取组件中UI对象
	*   @param  name: 一个组件可能有多个窗口，根据名称区分,name == nullpr || "",返回默认窗口
	*/
	virtual QWidget* getWidget(const char* name) override;

private slots:
    /**
    * @brief CE通知响应
    */
	void            slotCEClicked();

private:
    /**
    * @brief 获取节点附带的点集数据
    */
    WD::WDKeyPoints getNodePoints(WD::WDNode::SharedPtr pNode);
    /**
    * @brief 表格中添加关键点数据
    * @param row 行序号
    * @param position 位置
    * @param direction 朝向
    */
    void            addItemToTableWidgetKeyPoint(int row, WD::WDKeyPoint& keyPoint, const WD::DMat4& transform);
protected:
    /**
     * @brief     重写界面显示
     * @param evt 显示事件
    */
    virtual	void	showEvent(QShowEvent* evt) override;
private:
    // 翻译
    void retranslateUi();
private:
    Ui::UiNodeCoordinate ui;
};
