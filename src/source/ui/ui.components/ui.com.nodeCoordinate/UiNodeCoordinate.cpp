#include "UiNodeCoordinate.h"
#include "WDTranslate.h"
#include "core/nodeTree/WDNodeTree.h"
#include "core/graphable/WDGraphableInterface.h"
#include <QDialog>
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "core/businessModule/WDBDBase.h"

UiNodeCoordinate::UiNodeCoordinate(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QWidget* parent)
    : QDialog(parent)
	, IUiComponent(mainWindow, attrs)
{
    ui.setupUi(this);
    
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));

    // 隐藏行号
    ui.tableWidgetKeyPoint->verticalHeader()->setHidden(true);
    // 节点关键点坐标表头
    ui.tableWidgetKeyPoint->setColumnCount(7);
    QStringList headerLabels;
    headerLabels 
        << "Number"
        << "Position.X"
        << "Position.Y"
        << "Position.Z"
        << "Orientation.X"
        << "Orientation.Y"
        << "Orientation.Z";
    ui.tableWidgetKeyPoint->setHorizontalHeaderLabels(headerLabels);

    // 绑定通知事件响应
    connect(ui.pushButtonCE, SIGNAL(clicked()), this, SLOT(slotCEClicked()));

    retranslateUi();
}
UiNodeCoordinate::~UiNodeCoordinate()
{
}

void UiNodeCoordinate::onNotice(UiNotice * pNotice)
{
    int nType = pNotice->type();
    switch (nType)
    {
    case UiNoticeType::UNT_Action:
    {
        UiActionNotice* pActionNotice = static_cast<UiActionNotice*>(pNotice);
        if (pActionNotice->action().is("action.tool.node.coordinate.visible"))
        {
            if (this->isHidden())
                this->show();
            else
                this->activateWindow();
        }
    }
    break;
    default:
        break;
    }
}
QWidget * UiNodeCoordinate::getWidget(const char * name)
{
    WDUnused(name);
	return  nullptr;
}

void UiNodeCoordinate::slotCEClicked()
{
    WD::WDNode::SharedPtr                      pCurNode = mWindow().core().nodeTree().currentNode();
    if (pCurNode == nullptr)
        return ;
    // 清空节点关键点坐标
    ui.tableWidgetKeyPoint->clearContents();

    WD::WDKeyPoints keyPoints = this->getNodePoints(pCurNode);
    if (keyPoints.empty())
        return ;

    // 关键点列表
    int pointCount      =   static_cast<int>(keyPoints.size());
    ui.tableWidgetKeyPoint->setRowCount(pointCount);
    const auto& gTrans =    pCurNode->globalTransform();
    for (int row = 0; row < pointCount; ++row)
    {
        this->addItemToTableWidgetKeyPoint(row, keyPoints[row], gTrans);
    }
}
WD::WDKeyPoints UiNodeCoordinate::getNodePoints(WD::WDNode::SharedPtr pNode)
{
    WD::WDKeyPoints rKeyPoints;
    if (pNode == nullptr)
        return rKeyPoints;
    //P0点
    {
        rKeyPoints.push_back(WD::WDKeyPoint::P0());
    }
    //其他关键点
    if (pNode->getBDBase() == nullptr)
        return rKeyPoints;

    auto pGraphable = pNode->getBDBase()->graphableSupporter();
    if (pGraphable == nullptr)
        return rKeyPoints;

    auto pKeyPoints = pGraphable->gKeyPoints();
    if (pKeyPoints == nullptr || pKeyPoints->empty())
        return rKeyPoints;

    for (const auto& keyPoint : (*pKeyPoints))
    {
        rKeyPoints.push_back(keyPoint);
    }

    return rKeyPoints;
}
void UiNodeCoordinate::addItemToTableWidgetKeyPoint(int row, WD::WDKeyPoint& keyPoint, const WD::DMat4& transform)
{
    // 序号
    QString numberStr = QString("P") + QString::number(keyPoint.numb());
    ui.tableWidgetKeyPoint->setItem(row, 0, new QTableWidgetItem(numberStr));
    // 位置
    const auto pos = keyPoint.transformedPosition(transform);
    ui.tableWidgetKeyPoint->setItem(row, 1, new QTableWidgetItem(QString::number(pos.x, 'f', 2)));
    ui.tableWidgetKeyPoint->setItem(row, 2, new QTableWidgetItem(QString::number(pos.y, 'f', 2)));
    ui.tableWidgetKeyPoint->setItem(row, 3, new QTableWidgetItem(QString::number(pos.z, 'f', 2)));
    // 朝向
    const auto dir = keyPoint.transformedDirection(transform);
    ui.tableWidgetKeyPoint->setItem(row, 4, new QTableWidgetItem(QString::number(dir.x, 'f', 2)));
    ui.tableWidgetKeyPoint->setItem(row, 5, new QTableWidgetItem(QString::number(dir.y, 'f', 2)));
    ui.tableWidgetKeyPoint->setItem(row, 6, new QTableWidgetItem(QString::number(dir.z, 'f', 2)));
}

void UiNodeCoordinate::showEvent(QShowEvent* evt)
{
    // 统一将焦点设置到窗体，用于在窗口打开时清除其他控件焦点
    this->setFocus();
    WDUnused(evt);
}

void UiNodeCoordinate::retranslateUi()
{
    Trs("UiNodeCoordinate"
        , static_cast<QWidget*>(this)
        , ui.pushButtonCE
        , ui.tableWidgetKeyPoint);
}