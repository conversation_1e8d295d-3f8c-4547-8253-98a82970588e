#pragma once
#include "core/WDCore.h"
#include "core/businessModule/design/WDBMDesign.h"

#include "core/viewer/primitiveRender/WDTextRender.h"
#include "core/scene/WDRenderObject.h"
#include "core/geometry/standardPrimitives/WDGeometrySphere.h"
#include "core/geometry/WDGeometryPolyhedron.h"

WD_NAMESPACE_BEGIN

/**
 * @brief 创建墙的类型
*/
enum CreateWallType
{
    // 直墙
    StructuralWall = 0,
    // 用户自定义墙
    GroundWall,
    // 环墙
    Wall,
    // 
    Other
};

/**
* @brief 绘制起点和终点的绘制类
*/
class PointStartEndRenderObject : public WDRenderObject
{
private:
    // 创建墙的类型标志
    int _flag;
    // 保存顶点的数组
    std::vector<WD::FVec3> _verData;
    // 绘制顶点的几何体
    WDGeometrySphere::SharedPtr _pGeomSphere;
    // 几何体材质(phong)带法线
    WDMaterialPhong _material;
    // 实例
    WDInstances _insts;
    // 辅助轴线几何体
    WDGeometryPolyhedron::SharedPtr _pAshaftLines;
    // 连接线材质(纯色)
    WDMaterialColor _materialLines;

    WDRenderStateDepthTest::SharedPtr _pStateDepthTestDisabled;

    // 字体
    WDText2DRender _textRender;
public:
    PointStartEndRenderObject();
    ~PointStartEndRenderObject() {};
public:
    /**
    *   @brief 添加绘制点
    */
    void addVertex(DVec3& Pos, int index = -1);
    /**
    *   @brief 修改绘制点
    */
    void modifyVertex(int index, DVec3& pos);
    /**
    *   @brief 移除绘制点
    */
    void removeVertex(int index);
    /**
     * @brief 清空顶点数组
    */
    inline void clearVertex()
    {
        _verData.clear();
    }  
    /**
     * @brief 设置创建墙的类型标志
    */
    inline void setFlag(int flag)
    {
        _flag = flag;
    }
protected:
    virtual void updateAabb(WDContext& context, const WDScene& scene) override;
    virtual void update(WD::WDContext& context, const WD::WDScene&) override;
    virtual void render(WD::WDContext& context, const WD::WDScene&) override;
};

/**
* @brief 绘制圆弧的绘制类
*/
class PointRoundRenderObject : public WDRenderObject
{
public:
    enum RingWallType
    {
        // 弧
        Arc,
        // 墙
        Round
    };
private:
    RingWallType  _flag;
    // 保存顶点的数组
    WD::FVec3Vector _verData;
    // 几何体材质(phong)带法线
    WDMaterialPhong _material;
    // 辅助轴线几何体
    WDGeometryPolyhedron::SharedPtr _pAshaftLines;
    // 连接线材质(纯色)
    WDMaterialColor _materialLines;

    WDRenderStateDepthTest::SharedPtr _pStateDepthTestDisabled;
public:
    PointRoundRenderObject(const RingWallType& flag = Arc);
    ~PointRoundRenderObject() {};
public:
    void setFlag(const RingWallType& flag)
    {
        _flag = flag;
    }
    /**
    *   @brief 设置绘制点
    */
    void setVertexs(const FVec3Vector& Pos);
    /**
    *   @brief 添加绘制点
    */
    void addVertex(const DVec3& Pos);
    /**
    *   @brief 移除绘制点
    */
    void removeVertex(int index);
    /**
    * @brief 清空顶点数组
    */
    inline void clearVertex()
    {
        _verData.clear();
    }  
protected:
    virtual void updateAabb(WDContext& context, const WDScene& scene) override;
    virtual void update(WD::WDContext& context, const WD::WDScene&) override;
    virtual void render(WD::WDContext& context, const WD::WDScene&) override;
};
using RingWallType = PointRoundRenderObject::RingWallType;
/**
 * @brief 世界坐标与Wrt节点相对坐标转换
*/
class WrtTransition
{
private:
    // 当前选中Wrt节点
    WDNode::WeakPtr _pWrtNode;
public:
    WrtTransition()
    {}
    ~WrtTransition()
    {}
public:
    /**
     * @brief Wrt节点是否为空
     * @return true 为空, false 不为空
    */
    bool empty() const
    {
        return _pWrtNode.expired();
    }
    /**
    *   @brief 设置当前操作节点
    */
    inline void setCurrentWrtNode(WDNode::SharedPtr pNode)
    {
        _pWrtNode = pNode;
    }
    /**
    *   @brief 设置当前操作节点
    */
    inline WDNode::SharedPtr currentWrtNode()
    {
        return  _pWrtNode.lock();
    }
    /**
    * @brief 顶点数据从世界坐标转换到Wrt节点相对坐标
    * @param globalVertex 需要转换的,世界坐标下的顶点数据
    */
    void globalConvertToWrt(WD::DVec3& globalVertex);
    /**
    * @brief 顶点数据从Wrt节点相对坐标转换到世界坐标
    * @param wrtVertex 需要转换的,Wrt节点坐标下的坐标
    */
    void wrtConvertToGlobal(WD::DVec3& wrtVertex);
};

WD_NAMESPACE_END

