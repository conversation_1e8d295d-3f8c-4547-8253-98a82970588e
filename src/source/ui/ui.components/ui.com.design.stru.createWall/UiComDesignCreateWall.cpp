#include "UiComDesignCreateWall.h"

UiComDesignCreateWall::UiComDesignCreateWall(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject *parent)
    : QObject(parent)
    , IUiComponent(mainWindow, attrs)
{
    _pCreateWall            = new CreateWallDialog(mWindow().core(), mWindow().widget());
}


UiComDesignCreateWall::~UiComDesignCreateWall()
{
    if (_pCreateWall != nullptr)
    {
        delete _pCreateWall;
        _pCreateWall = nullptr;
    }
}

void UiComDesignCreateWall::onNotice(UiNotice * pNotice)
{
    int nType = pNotice->type();
    switch (nType)
    {
    case UiNoticeType::UNT_Action:
    {
        UiActionNotice* pActionNotice = static_cast<UiActionNotice*>(pNotice);
        // 创建墙
        if (pActionNotice->action().is("action.architectural.wall"))
        {
            if (_pCreateWall->isHidden())
                _pCreateWall->show();
            else
                _pCreateWall->activateWindow();
        }
    }
    break;
    default:
        break;
    }
}