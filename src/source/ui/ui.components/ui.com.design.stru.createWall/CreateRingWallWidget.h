#pragma once

#include "ui_CreateRingWallWidget.h"
#include "core/WDCore.h"
#include "CreateRingWallDefineExplicitlyWidget.h"

class CreateRingWallMethodsMgr
{
public:
    virtual ~CreateRingWallMethodsMgr()
    {
        mgr.clear();
    }
public:
    inline void addMethod(Methods type, std::shared_ptr<CreateRingWallMethodsBase> method)
    {
        if (mgr.find(type) == mgr.end())
            mgr[type] = method;
        else
            assert(false);
    }
public:
    std::map<Methods, std::shared_ptr<CreateRingWallMethodsBase>> mgr;
};


class CreateRingWallWidget : public QWidget
{
	Q_OBJECT

public:
	CreateRingWallWidget(WD::WDCore& app, WD::WrtTransition& wrtTransition, QWidget *parent = Q_NULLPTR);
	~CreateRingWallWidget();
private:
    void init();
signals:
    void widgetChange(QSize size);
public slots:
    void comboBoxCurrentIndexChanged(int);
public:

	void clearViewData();

    void openRender();

    WD::WDNode::SharedPtr createRingWall(WD::WDNode& parent, WD::WDNode::SharedPtr pSpco, WallSpecificationDialog& spec);

    void updateView(WD::WDNode* pCurrentNode, WD::WDNode* pPrevNode);
private:
	// 初始化数据
    void initData();
	// 界面翻译
	void retranslateUi();
private:
	Ui::CreateRingWallWidget ui;
    WD::WDCore& _core;
    
    CreateRingWallMethodsMgr _methodsMgr;
    std::shared_ptr<CreateRingWallMethodsBase> _method;
	WD::WrtTransition& _wrtTransition;
};

