#pragma once
#include "CreateRingWallMethodsBase.h"
#include "ui_CreateRingWallDefineExplicitlyWidget.h"

class CreateRingWallDefineExplicitlyWidget : public QWidget, public CreateRingWallMethodsBase
{
	Q_OBJECT

public:
    CreateRingWallDefineExplicitlyWidget(WD::WDCore& app
        , WD::WrtTransition& wrtTransition
        , const WD::RingWallType& type = WD::RingWallType::Arc
        , QWidget *parent = Q_NULLPTR);
	~CreateRingWallDefineExplicitlyWidget();
private:
	/**
	 * @brief 当前选中的更改偏移的模式
	*/
	enum PositionMode
	{
        // 圆中心点
        Origin = 0,
        // 半径
        Radius,
        // 起点
        StartAngle,
        // 终点
        EndAngle,
	};
    void showEvent(QShowEvent* event) override;
    void hideEvent(QHideEvent* event) override;
public:
	/**
	 * @brief 开启绘制
	*/
	virtual void openRender() override;
    /**
    * @brief 创建环墙
    */
    virtual WD::WDNode::SharedPtr createRingWall(WD::WDNode& parent, WD::WDNode::SharedPtr pSpco, WallSpecificationDialog& wallSpec) override;
    /**
    * @brief 更新数据
    */
    virtual void updateView(WD::WDNode* pCurrentNode, WD::WDNode* pPrevNode) override;
    /**
    * @brief 清理数据,移除自定义绘制对象
    */
    virtual void clearViewData() override;
    virtual void setCreateType(const WD::RingWallType& type) override;
private:
    bool createArcWall(WD::WDNode& wallNode);
    bool createRoundWall(WD::WDNode& wallNode);
    //  更新显示的轴
    void updateViewAxis();
    //  更新显示的值 bUpdateAxis:是否更新显示轴，默认更新
    void updateViewData(const int& viewMode, bool bUpdateAxis = true);
private:
    //  获取界面显示的数据
    WD::DVec3 getViewData();
    WD::DVec3 getCurvePos();
	/**
	* @brief 设置当前数据
	* @param startPoint 起点坐标
	*/
	void setViewData(const WD::DVec3& point = WD::DVec3::Zero());
	// 初始化数据
    void initData();
	// 界面翻译
	void retranslateUi();
private:
	Ui::CreateRingWallDefineExplicitlyWidget ui;
    //  圆心点
    WD::DVec3 _origin;
    //  半径
    float _radius;
    //  起点角度
    float _startAngle;
    //  终点角度
    float _endAngle;
};

