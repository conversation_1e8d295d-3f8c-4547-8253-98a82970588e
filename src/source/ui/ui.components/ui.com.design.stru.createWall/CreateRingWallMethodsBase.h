#pragma once
#include "CreateWallCommon.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiPositionCaptureHelpter.h"

class WallSpecificationDialog;
class CreateRingWallMethodsBase
{
public:
    // 创建方式
    enum CreationMethods
    {
        // 详细的配置环墙的属性
        DefineExplicitly,
        // 三点画弧
        Torough3Points
    };
public:
    CreateRingWallMethodsBase(WD::WDCore& app
        , WD::WrtTransition& wrtTransition
        , const WD::RingWallType& type = WD::RingWallType::Arc);
    virtual ~CreateRingWallMethodsBase();
public:
    /**
     * @brief 开启绘制
    */
    virtual void openRender() = 0;
    /**
     * @brief 创建环墙
    */
    virtual WD::WDNode::SharedPtr createRingWall(WD::WDNode& parent, WD::WDNode::SharedPtr pSpco, WallSpecificationDialog& spec) = 0;
    /**
     * @brief 更新数据
    */
    virtual void updateView(WD::WDNode* pCurrentNode, WD::WDNode* pPrevNode) = 0;
    /**
    * @brief 清理数据,移除自定义绘制对象
    */
    virtual void clearViewData() = 0;
    /**
     * @brief 设置创建类型
    */
    virtual void setCreateType(const WD::RingWallType& type) = 0;

    void init(const WD::RingWallType& type)
    {
        this->openRender();
        this->setCreateType(type);
    }
protected:
    WD::WDCore& _core;
    WD::WrtTransition& _wrtTransition;
    WD::RingWallType _type;
    // 捕捉助手
    UiPositionCaptureHelpter _positionCaptureHelpter;
    WD::PointStartEndRenderObject _renderPoint;
    WD::PointRoundRenderObject _renderRound;
};
using Methods = CreateRingWallMethodsBase::CreationMethods;

class CreateRingWallTorough3Points : public CreateRingWallMethodsBase
{
public:
    CreateRingWallTorough3Points(WD::WDCore& app
        , WD::WrtTransition& wrtTransition
        , const WD::RingWallType& type = WD::RingWallType::Arc);
public:
    /**
    * @brief 开启绘制
    */
    virtual void openRender() override;
    /**
    * @brief 创建环墙
    */
    virtual WD::WDNode::SharedPtr createRingWall(WD::WDNode& parent, WD::WDNode::SharedPtr pSpco, WallSpecificationDialog& spec) override;
    /**
    * @brief 更新数据
    */
    virtual void updateView(WD::WDNode*, WD::WDNode*) override
    {
    }
    /**
    * @brief 清理数据,移除自定义绘制对象
    */
    virtual void clearViewData() override;
    /**
    * @brief 设置创建类型
    */
    virtual void setCreateType(const WD::RingWallType& type) override;
private:
    void addPoint(WD::DVec3 point);
    bool createArcWall(WD::WDNode& wallNode);
    bool createRoundWall(WD::WDNode& wallNode);
    // 更新绘制选中的三点构成的弧或者圆
    void updateViewArc();
private:
    WD::DVec3Vector _points;
};