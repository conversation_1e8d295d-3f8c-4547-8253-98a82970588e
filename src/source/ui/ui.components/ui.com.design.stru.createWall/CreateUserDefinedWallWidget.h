#pragma once

#include <QWidget>
#include <QStandardItemModel>

#include "ui_CreateUserDefinedWallWidget.h"
#include "core/WDCore.h"
#include "core/viewer/capturePositioning/WDCapturePositioning.h"
#include "../../ui.commonLibrary//ui.commonLib.custom/UiNodeNameHelpter.h"

#include "CreateWallCommon.h"

class CreateUserDefinedWallWidget : public QWidget
	, public WD::WDCapturePositioningMonitor
{
	Q_OBJECT

public:
	CreateUserDefinedWallWidget(WD::WDCore& app, WD::WrtTransition& wrtTransition, QWidget *parent = Q_NULLPTR);
	~CreateUserDefinedWallWidget();
private:
	/**
	 * @brief 创建墙的方式
	*/
	enum CreateMethods
	{
		// 输入坐标
		InputCoordinates = 0,
		// 捕捉方式
		Catch,
		// 输入方向+数值方式
		DirectionNumber
	};
public:
	/**
	 * @brief 创建用户自定义墙
	*/
	WD::WDNode::SharedPtr createUserDefinedWall(WD::WDNode& parent, WD::WDNode::SharedPtr pSpco);
	/**
	 * @brief 清空表格,移除自定义绘制对象
	*/
	void clearTableView();
    /**
    * @brief 清空表格
    */
    void clearData();
	/**
	 * @brief 开启绘制
	*/
	void openRender();
	/**
	 * @brief 根据Wrt节点改变,更新表格数据
	*/
	void updateTableWidget(WD::WDNode* pCurrentNode, WD::WDNode* pPrevNode);
private slots:
	/**
	 * @brief 创建墙的方式改变响应槽函数
	*/
	void slotComboBoxCreateOptionChanged(int index);
	/**
	 * @brief 添加一行数据响应槽函数
	*/
	void slotPushButtonAddClicked();
	/**
	 * @brief 删除一行数据响应槽函数
	*/
	void slotPushButtonDeleteClicked();
	/**
	 * @brief 单元格内容改变响应槽函数
	 * @param  item 改变内容的Item
	*/
	void slotItemChanged(QTableWidgetItem* item);
private:
	/**
	* @brief 在表格上添加一行数据
	* @param point 坐标点
	*/
	void addRow(const WD::DVec3& point, int index = -1);
	/**
	 * @brief 获取tableView表格所有顶点数据
	 * @return 顶点集合
	*/
	std::vector<WD::DVec3> getVertices();
	/**
	* @brief 获取一行的坐标
	* @param rowIndex   行索引
	* @param point      坐标
	*/
	WD::DVec3 getRowData(int rowIndex);
	/**
	* @brief 计算点在前三个点所形成平面上的投影(世界坐标)
	* @param vertex 要求投影的顶点数据
	* @return 投影
	*/
	WD::DVec3 getProject(WD::DVec3& vertex);
	/**
	* @brief 校验前三个顶点数据是否能成面
	* @return true为能成面 false为不能
	*/
	bool isIsTriangle();
	/**
	* @brief 获取默认拉伸方向(世界坐标)
	* @return 默认拉伸方向向量
	*/
	WD::DVec3 getDirection();
	/**
	* @brief 给定三个顶点计算拉伸方向
	* @param v1 顶点一(世界坐标)
	* @param v2 顶点二(世界坐标)
	* @param v3 顶点三(世界坐标)
	* @return 三点形成平面相对父属设备节点旋转
	*/
	WD::DMat4 getLocalExtrDirction(WD::WDNode::SharedPtr pParentNode
		, const WD::DVec3& v1
		, const WD::DVec3& v2
		, const WD::DVec3& v3);
	/**
	* @brief 定位捕捉结果通知,带退出标志,可以决定此次结果完成后是否自动退出捕捉
	* @param result 捕捉的结果数据
	* @param existFlag 退出标志
	*   每次通知结果后，都可以由子类重新指定该值以决定捕捉是否自动退出
	*   默认情况 existFlag值为false,即通知一次结果后不会退出，继续捕捉且继续通知结果
	*   如果重写时，将existFlag的值修改为true，则此次结果通知完成后，将自动退出捕捉状态
	* @param sender 发送者
	*/
	virtual void onResult(const WD::WDCapturePositioningResult& result
		, bool& existFlag
		, const WD::WDCapturePositioning& sender) override;
private:
	/**
	 * @brief 是否符合创建用户自定义墙的条件
	 * @return true 符合, false 不符合
	*/
	bool isAccordCreateCondition();
	/**
	* @brief 开启捕捉
	*/
	void openCapturePosition();
	/**
	* @brief 关闭捕捉
	*/
	void closeCapturePosition();
	/**
	*   @brief 添加自定义绘制对象
	*/
	void addRenderObject();
	/**
	*   @brief 移除自定义绘制对象
	*/
	void removeRenderObject();
	/**
	* @brief 模型树当前节点改变
	*/
	void onNodeTreeCurrentNodeChanged(WD::WDNode::SharedPtr pCurrNode
		, WD::WDNode::SharedPtr pPrevNode
		, WD::WDNodeTree& sender);
	// 初始化界面
	void initWidget();
	// 界面翻译
	void retranslateUi();
private:
	Ui::CreateUserDefinedWallWidget ui;
    WD::WDCore&					   _core;
	WD::WrtTransition&			   _wrtTransition;
	WD::PointStartEndRenderObject  _renderObj;
	// 节点名称助手
	UiNodeNameHelpter				_nameHelpter;
};

