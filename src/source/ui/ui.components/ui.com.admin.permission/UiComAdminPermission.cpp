#include "UiComAdminPermission.h"

UiComAdminPermission::UiComAdminPermission(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject *parent)
    : QObject(parent)
    , IUiComponent(mainWindow, attrs)
    , _core(mainWindow.core())
{
    _pAccrDialog = new AccreditDialog(_core, mainWindow.collaboration(), mWindow().widget());
    _pInacDialog = new InaccreditDialog(_core, mainWindow.collaboration(), mWindow().widget());

    // 绑定事件
    connect(_pAccrDialog, &AccreditDialog::sigUpdateData, _pInacDialog, &InaccreditDialog::slotUpdateData);
}
UiComAdminPermission::~UiComAdminPermission()
{
    if (_pAccrDialog != nullptr)
    {
        _pAccrDialog->deleteLater();
    }
    if (_pInacDialog != nullptr)
    {
        _pInacDialog->deleteLater();
    }
}

void UiComAdminPermission::onNotice(UiNotice * pNotice)
{
    int nType = pNotice->type();
    switch (nType)
    {
    case UiNoticeType::UNT_Action:
        {
            auto pANotice = static_cast<UiActionNotice*>(pNotice);
            if (pANotice->action().is("action.permission.accredit"))
            {
                if (_pAccrDialog->isHidden())
                    _pAccrDialog->show();
                else
                    _pAccrDialog->activateWindow();
            }
            else if (pANotice->action().is("action.permission.inaccredit"))
            {
                if (_pInacDialog->isHidden())
                    _pInacDialog->show();
                else
                    _pInacDialog->activateWindow();
            }
        }
        break;
    default:
        break;
    }
}