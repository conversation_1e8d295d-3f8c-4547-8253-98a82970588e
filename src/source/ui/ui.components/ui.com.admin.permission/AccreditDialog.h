#pragma once

#include <QDialog>
#include "ui_AccreditDialog.h"

#include "WDCore.h"
#include "../../wizDesignerApp/UiInterface/ICollaboration.h"

class AccreditDialog : public QDialog
{
    Q_OBJECT
public:
    AccreditDialog(WD::WDCore& core, ICollaboration& collaboration, QWidget *parent = Q_NULLPTR);
    ~AccreditDialog();
protected:
    virtual void showEvent(QShowEvent* evt) override;
    virtual void hideEvent(QHideEvent* evt) override;
signals:
    void sigUpdateData();
private slots:
    void slotOkClicked();
    void slotFilterTextChanged(const QString& text);
private:
    /**
     * @brief 初始化专业列表
    */
    void initSpecList();
    // 界面翻译
    void retranslateUi();
private:
    Ui::AccreditDialog  ui;
    WD::WDCore&         _core;
    ICollaboration&     _collaboration;
};
