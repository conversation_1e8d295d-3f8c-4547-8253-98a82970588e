#include    "UiComNodeTree.h"
#include    <QKeyEvent>
#include    "WDTranslate.h"
#include    "businessModule/catalog/WDBMCatalog.h"

UiComNodeTree::UiComNodeTree(IMainWindow& mainWindow, const UiComponentAttributes& attrs)
    : QObject(nullptr)
    , IUiComponent(mainWindow, attrs)
{
    _pNodeTreeWidget = new NodeTreeWidget(mainWindow);
}
UiComNodeTree::~UiComNodeTree()
{
    if (_pNodeTreeWidget != nullptr)
    {
        delete _pNodeTreeWidget;
        _pNodeTreeWidget = nullptr;
    }
}

void UiComNodeTree::onNotice(UiNotice* pNotice)
{
    int nType = pNotice->type();
    switch (nType)
    {
    case UiNoticeType::UNT_AllReady:
    {
        auto pAction = mWindow().queryAction("action.display.node.tree");
        auto pDock = mWindow().getDock(_pNodeTreeWidget);
        if (pAction != nullptr && pDock != nullptr)
        {
            connect(pDock, &QDockWidget::visibilityChanged, this, [=](bool visible)
                {
                    pAction->setChecked(visible);
                });
        }
    }
    break;
    case UiNoticeType::UNT_Action:
        {
            UiActionNotice* pActionNotice = static_cast<UiActionNotice*>(pNotice);
            if (pActionNotice->action().is("action.display.node.tree"))
            {
                bool bChecked = pActionNotice->action().checked();
                if (bChecked)
                    mWindow().showDock(_pNodeTreeWidget);
                else
                    mWindow().hideDock(_pNodeTreeWidget);
            }
}
        break;
    default:
        break;
    }
}
QWidget* UiComNodeTree::getWidget(const char* name)
{
    if (name == nullptr || strlen(name) == 0)
    {
        return _pNodeTreeWidget;
    }
    return  nullptr;
}