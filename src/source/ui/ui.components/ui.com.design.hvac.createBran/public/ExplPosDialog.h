#pragma once

#include <QDialog>
#include "ui_ExplPosDialog.h"
#include "core/WDCore.h"
#include "core/math/Math.hpp"

class ExplPosDialog : public QDialog
{
    Q_OBJECT

public:
    ExplPosDialog(WD::WDCore& core, QWidget* parent = nullptr);
    ~ExplPosDialog();

signals:
    void sigOkClicked(WD::Vec3& pos);

public:
    // 界面翻译
    void retranslateUi(const std::string& ctxStr);

protected:
    virtual void showEvent(QShowEvent* evt) override;
    virtual void hideEvent(QHideEvent* evt) override;

private slots:
    /**
     * @brief ok按钮按下通知响应事件
    */
    void slotOnOkClicked();
    /**
     * @brief cancel按钮按下通知响应事件
    */
    void slotOnCancelClicked();

private:
    Ui::ExplPosDialog   ui;
    WD::WDCore&         _core;
    // 初始值
    WD::DVec3           _prevPos;
};
