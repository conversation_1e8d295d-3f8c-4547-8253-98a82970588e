#include "UiComCreateBran.h"

UiComCreateBran::UiComCreateBran(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject *parent)
    : QObject(parent)
    , IUiComponent(mainWindow, attrs)
    , _core(mainWindow.core())
{
    _pBranCreateDialog = new BranCreateDialog(mWindow().core(), mWindow().widget());
}
UiComCreateBran::~UiComCreateBran()
{
    if (_pBranCreateDialog != nullptr)
    {
        _pBranCreateDialog->deleteLater();
    }
}

void UiComCreateBran::onNotice(UiNotice * pNotice)
{
    int nType = pNotice->type();
    switch (nType)
    {
    case UiNoticeType::UNT_Action:
    {
        UiActionNotice* pActionNotice = static_cast<UiActionNotice*>(pNotice);
        // 主体
        if (pActionNotice->action().is("action.design.hvac.createBran"))
        {
            if (_pBranCreateDialog->isHidden())
                _pBranCreateDialog->show();
            else
                _pBranCreateDialog->activateWindow();
        }
    }
    break;
    default:
        break;
    }
}