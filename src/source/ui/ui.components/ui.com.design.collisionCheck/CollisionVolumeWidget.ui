<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CollisionVolumeWidget</class>
 <widget class="QWidget" name="CollisionVolumeWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>641</width>
    <height>265</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string/>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="1,1">
     <item>
      <widget class="QGroupBox" name="groupBoxFrom">
       <property name="title">
        <string>From</string>
       </property>
       <layout class="QGridLayout" name="gridLayout" columnstretch="0,1,0">
        <item row="1" column="0">
         <widget class="QLabel" name="labelMinX">
          <property name="text">
           <string>X</string>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QDoubleSpinBox" name="doubleSpinBoxMinX">
          <property name="minimum">
           <double>-999999999.000000000000000</double>
          </property>
          <property name="maximum">
           <double>999999999.990000009536743</double>
          </property>
         </widget>
        </item>
        <item row="1" column="2">
         <widget class="QCheckBox" name="checkBoxLockMinX">
          <property name="focusPolicy">
           <enum>Qt::StrongFocus</enum>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item row="2" column="0">
         <widget class="QLabel" name="labelMinY">
          <property name="text">
           <string>Y</string>
          </property>
         </widget>
        </item>
        <item row="2" column="1">
         <widget class="QDoubleSpinBox" name="doubleSpinBoxMinY">
          <property name="minimum">
           <double>-999999999.000000000000000</double>
          </property>
          <property name="maximum">
           <double>999999999.990000009536743</double>
          </property>
         </widget>
        </item>
        <item row="2" column="2">
         <widget class="QCheckBox" name="checkBoxLockMinY">
          <property name="focusPolicy">
           <enum>Qt::StrongFocus</enum>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item row="3" column="0">
         <widget class="QLabel" name="labelMinZ">
          <property name="text">
           <string>Z</string>
          </property>
         </widget>
        </item>
        <item row="3" column="1">
         <widget class="QDoubleSpinBox" name="doubleSpinBoxMinZ">
          <property name="minimum">
           <double>-999999999.000000000000000</double>
          </property>
          <property name="maximum">
           <double>999999999.990000009536743</double>
          </property>
         </widget>
        </item>
        <item row="3" column="2">
         <widget class="QCheckBox" name="checkBoxLockMinZ">
          <property name="focusPolicy">
           <enum>Qt::StrongFocus</enum>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item row="0" column="1" colspan="2">
         <widget class="QCheckBox" name="checkBoxCaptureMin">
          <property name="focusPolicy">
           <enum>Qt::StrongFocus</enum>
          </property>
          <property name="layoutDirection">
           <enum>Qt::RightToLeft</enum>
          </property>
          <property name="text">
           <string>Capture</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QGroupBox" name="groupBoxTo">
       <property name="title">
        <string>To</string>
       </property>
       <layout class="QGridLayout" name="gridLayout_2" columnstretch="0,1,0">
        <item row="1" column="0">
         <widget class="QLabel" name="labelMaxX">
          <property name="text">
           <string>X</string>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QDoubleSpinBox" name="doubleSpinBoxMaxX">
          <property name="minimum">
           <double>-999999999.000000000000000</double>
          </property>
          <property name="maximum">
           <double>999999999.990000009536743</double>
          </property>
         </widget>
        </item>
        <item row="1" column="2">
         <widget class="QCheckBox" name="checkBoxLockMaxX">
          <property name="focusPolicy">
           <enum>Qt::StrongFocus</enum>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item row="2" column="0">
         <widget class="QLabel" name="labelMaxY">
          <property name="text">
           <string>Y</string>
          </property>
         </widget>
        </item>
        <item row="2" column="1">
         <widget class="QDoubleSpinBox" name="doubleSpinBoxMaxY">
          <property name="minimum">
           <double>-999999999.000000000000000</double>
          </property>
          <property name="maximum">
           <double>999999999.990000009536743</double>
          </property>
         </widget>
        </item>
        <item row="2" column="2">
         <widget class="QCheckBox" name="checkBoxLockMaxY">
          <property name="focusPolicy">
           <enum>Qt::StrongFocus</enum>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item row="3" column="0">
         <widget class="QLabel" name="labelMaxZ">
          <property name="text">
           <string>Z</string>
          </property>
         </widget>
        </item>
        <item row="3" column="1">
         <widget class="QDoubleSpinBox" name="doubleSpinBoxMaxZ">
          <property name="minimum">
           <double>-999999999.000000000000000</double>
          </property>
          <property name="maximum">
           <double>999999999.990000009536743</double>
          </property>
         </widget>
        </item>
        <item row="3" column="2">
         <widget class="QCheckBox" name="checkBoxLockMaxZ">
          <property name="focusPolicy">
           <enum>Qt::StrongFocus</enum>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item row="0" column="1" colspan="2">
         <widget class="QCheckBox" name="checkBoxCaptureMax">
          <property name="focusPolicy">
           <enum>Qt::StrongFocus</enum>
          </property>
          <property name="layoutDirection">
           <enum>Qt::RightToLeft</enum>
          </property>
          <property name="text">
           <string>Capture</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,0,0,0">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="labelVolume">
       <property name="text">
        <string>Volume</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="comboBoxVolume"/>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonClear">
       <property name="focusPolicy">
        <enum>Qt::StrongFocus</enum>
       </property>
       <property name="text">
        <string>Clear</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="0,0,0,0">
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QCheckBox" name="checkBoxDisplay">
       <property name="focusPolicy">
        <enum>Qt::StrongFocus</enum>
       </property>
       <property name="text">
        <string>Display</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="labelColor">
       <property name="minimumSize">
        <size>
         <width>100</width>
         <height>0</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="focusPolicy">
        <enum>Qt::StrongFocus</enum>
       </property>
       <property name="frameShape">
        <enum>QFrame::Box</enum>
       </property>
       <property name="text">
        <string/>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonColor">
       <property name="focusPolicy">
        <enum>Qt::StrongFocus</enum>
       </property>
       <property name="text">
        <string>Color</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>160</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <tabstops>
  <tabstop>checkBoxCaptureMin</tabstop>
  <tabstop>doubleSpinBoxMinX</tabstop>
  <tabstop>checkBoxLockMinX</tabstop>
  <tabstop>doubleSpinBoxMinY</tabstop>
  <tabstop>checkBoxLockMinY</tabstop>
  <tabstop>doubleSpinBoxMinZ</tabstop>
  <tabstop>checkBoxLockMinZ</tabstop>
  <tabstop>checkBoxCaptureMax</tabstop>
  <tabstop>doubleSpinBoxMaxX</tabstop>
  <tabstop>checkBoxLockMaxX</tabstop>
  <tabstop>doubleSpinBoxMaxY</tabstop>
  <tabstop>checkBoxLockMaxY</tabstop>
  <tabstop>doubleSpinBoxMaxZ</tabstop>
  <tabstop>checkBoxLockMaxZ</tabstop>
  <tabstop>comboBoxVolume</tabstop>
  <tabstop>pushButtonClear</tabstop>
  <tabstop>checkBoxDisplay</tabstop>
  <tabstop>pushButtonColor</tabstop>
 </tabstops>
 <resources/>
 <connections/>
</ui>
