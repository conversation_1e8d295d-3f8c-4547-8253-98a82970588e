#include    "CollisionReportConfiguration.h"
#include    <QDir>
#include    <QFileDialog>
#include    "core/message/WDMessage.h"
#include    "core/WDTranslate.h"
#include    "WDRapidxml.h"

CollisionReportConfiguration::CollisionReportConfiguration(WD::WDCore& core, const QString& configFile, QWidget *parent)
    : QDialog(parent), _app(core)
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    this->initWidget();
    this->widgetTextTranslate();
    this->loadConfigFromXML(configFile);
    ui.lineEditConfigurationFile->setText(configFile);

    connect(ui.tableWidget, SIGNAL(currentCellChanged(int, int, int, int)), this, SLOT(slotCellClicked(int, int, int, int)));
    connect(ui.pushButtonReload, SIGNAL(clicked()), this, SLOT(slotReloadConfigFromXML()));
    connect(ui.pushButtonUpdate
        , SIGNAL(clicked())
        , this
        , SLOT(slotUpdateData()));
    connect(ui.pushButtonSaveCurSet
        , SIGNAL(clicked())
        , this
        , SLOT(slotSaveData()));
}

CollisionReportConfiguration::~CollisionReportConfiguration()
{

}

void CollisionReportConfiguration::setNextIndex()
{
    //QList<QTableWidgetItem*>item = ui.tableWidget->findItems(QString::fromUtf8("Index"), Qt::MatchExactly);
    //if (item.count() != 1)
    //    return;
    //int index = ui.tableWidget->item(item.at(0)->row(), 1)->text().toInt() + 1;
    int index = ui.tableWidget->item(3, 1)->text().toInt() + 1;
    if (index > ui.tableWidget->item(1, 1)->text().toInt())
    {
        WD_INFO_T("CollisionReportConfiguration", "UpdateFail3");
        // 点击确认后,下一流水号重置到最小
        index = ui.tableWidget->item(0, 1)->text().toInt();
    }
    ui.tableWidget->item(3, 1)->setText(QString::number(index));
    QStringList selData = this->getTableUserData(3);
    selData.replace(1, QString::number(index));
    this->setTableUserData(3, selData);
}

void CollisionReportConfiguration::initWidget()
{
    // 单击选择行
    ui.tableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    // 设置所有单元格不可编辑
    ui.tableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    // 设置不可以多选行
    ui.tableWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    // 初始化表格
    ui.tableWidget->setColumnCount(2);
    // 根据内容设置列宽
    ui.tableWidget->horizontalHeader()->setSectionResizeMode(QHeaderView::ResizeToContents);
}

void CollisionReportConfiguration::widgetTextTranslate()
{
    WD::WDCxtTsBg("CollisionReportConfiguration");
    this->setWindowTitle(QString::fromUtf8(WD::WDCxtTs("Titile").c_str()));
    ui.labelConfigurationFile->setText(QString::fromUtf8(WD::WDCxtTs("ConfigurationFile").c_str()));
    ui.pushButtonReload->setText(QString::fromUtf8(WD::WDCxtTs("Reload").c_str()));
    ui.pushButtonSaveCurSet->setText(QString::fromUtf8(WD::WDCxtTs("SaveCurSet").c_str()));
    ui.pushButtonUpdate->setText(QString::fromUtf8(WD::WDCxtTs("Update").c_str()));

    ui.groupBoxItemEdit->setTitle(QString::fromUtf8(WD::WDCxtTs("ItemEdit").c_str()));
    ui.labelParameterName->setText(QString::fromUtf8(WD::WDCxtTs("ParameterName").c_str()));
    ui.labelParameterNumber->setText(QString::fromUtf8(WD::WDCxtTs("ParameterNumber").c_str()));
    //ui.labelParameterDescribe->setText(QString::fromUtf8(WD::WDCxtTs("ParameterDescribe").c_str()));

    QStringList lstHeader;
    lstHeader << QString::fromUtf8(WD::WDCxtTs("ParameterName").c_str())
        << QString::fromUtf8(WD::WDCxtTs("ParameterNumber").c_str());
        //<< QString::fromUtf8(WD::WDCxtTs("ParameterDescribe").c_str());
    ui.tableWidget->setHorizontalHeaderLabels(lstHeader);
    WD::WDCxtTsEd();
}

bool CollisionReportConfiguration::loadConfigFromXML(const QString & configFile)
{
    FILE* pFile = fopen(configFile.toLocal8Bit().data(), "rb");
    if (pFile == nullptr)
        return false;
    _configFile = configFile;
    fseek(pFile, 0, SEEK_END);
    size_t  nLen = ftell(pFile);
    fseek(pFile, 0, SEEK_SET);
    char*   buf = new char[nLen + 1];
    memset(buf, 0, nLen + 1);
    fread(buf, nLen, 1, pFile);
    fclose(pFile);
    auto result = analysisConfigXML(buf);
    delete[] buf;
    return result;
}

bool CollisionReportConfiguration::analysisConfigXML(char * xml)
{
    try
    {
        WD::XMLDoc doc;
        WD::XMLNode* rootNode = nullptr;
        doc.parse<0>((char*)xml);
        rootNode = doc.first_node();
        if (rootNode == nullptr)
            return false;

        WD::XMLNode* ParameterNode = rootNode->first_node("Parameter");
        while (ParameterNode != nullptr)
        {
            auto pName = ParameterNode->first_node("Name");
            if (pName == nullptr)
            {
                ParameterNode = ParameterNode->next_sibling("Parameter");
                continue;
            }
            QString strName = QString::fromUtf8(pName->value()); 
            auto pValue = ParameterNode->first_node("Value");
            if (pValue == nullptr)
            {
                ParameterNode = ParameterNode->next_sibling("Parameter");
                continue;
            }
            QString strValue = QString::fromUtf8(pValue->value());
            QStringList lstData({ strName ,strValue });
            this->addOneRowToTableWidget(lstData);

            ParameterNode = ParameterNode->next_sibling("Parameter");
        }
        return true;
    }
    catch (std::runtime_error err)
    {
        assert(false && err.what());
        return  false;
    }
}

void CollisionReportConfiguration::addOneRowToTableWidget(const QStringList & lstMsg)
{
    // 获取当前行数
    int count = ui.tableWidget->rowCount();
    // 在最后一行后面插入一行
    ui.tableWidget->insertRow(count);
    for (int i = 0; i < lstMsg.size(); ++i)
    {
        QTableWidgetItem* item = new QTableWidgetItem(lstMsg.at(i));
        item->setTextAlignment(Qt::AlignCenter);
        ui.tableWidget->setItem(count, i, item);
    }
    this->setTableUserData(count, lstMsg);
}

void CollisionReportConfiguration::setTableUserData(const int & row, const QStringList & lstData)
{
    if (row < 0 || lstData.isEmpty())
        return;
    // 获取当前选中行的第一列
    QTableWidgetItem* curItem = ui.tableWidget->item(row, 0);
    // 每行第一列设置用户数据
    curItem->setData(Qt::UserRole, QVariant::fromValue(lstData));
}

QStringList CollisionReportConfiguration::getTableUserData(const int & row) const
{
    if (row < 0)
        return QStringList();
    // 获取当前选中行的第一列
    QTableWidgetItem* curItem = ui.tableWidget->item(row, 0);
    if (curItem == nullptr)
        return QStringList();
    return curItem->data(Qt::UserRole).value<QStringList>();
}

QString CollisionReportConfiguration::findRowValue(int row) const
{
    QStringList lstRow = this->getTableUserData(row);
    if (lstRow.count() != 2)
        return QString();
    return lstRow.at(1);
}

bool CollisionReportConfiguration::cheakUpdate(int row, const QString& number) const
{
    bool state = false;
    switch (row)
    {
    case 0:
    {
        // 若修改流水号最小值大于流水号最大值,为负值或不是纯数字,则修改失败
        if (number.toInt() > ui.tableWidget->item(1, 1)->text().toInt() || !number.contains(QRegExp("^\\d+$")) || number.toInt() < 0)
            state = true;
    }
    break;
    case 1:
    {
        // 若修改流水号最大值,小于最小值,为负值或不是纯数字,则修改失败
        if (number.toInt() <= ui.tableWidget->item(0, 1)->text().toInt() || !number.contains(QRegExp("^\\d+$")) || number.toInt() < 0)
            state = true;
    }
    break;
    case 2:
    {
        // 若下一流水号宽度小于1或不是纯数字,则修改失败
        if (number.toInt() < 1 || !number.contains(QRegExp("^\\d+$")))
            state = true;
    }
    break;
    case 3:
    {
        // 若修改下一流水号大于流水号最大值,为负值或不是纯数字,则修改失败
        if ((number.toInt() > ui.tableWidget->item(1, 1)->text().toInt()) || !number.contains(QRegExp("^\\d+$")) || number.toInt() < 0)
            state = true;
    }
    break;
    default:
        break;
    }
    return state;
}

void CollisionReportConfiguration::slotCellClicked(int curRow, int curCol, int preRow, int preCol)
{
    WDUnused3(curCol, preRow, preCol);
    QStringList lstRow = this->getTableUserData(curRow);
    if (lstRow.count() != 2)
        return;
    ui.lineEditParameterName->setText(lstRow.at(0));
    ui.lineEditParameterNumber->setText(lstRow.at(1));
}

void CollisionReportConfiguration::slotReloadConfigFromXML()
{
    // 打开文件对话框，选择xml文件
    QString path = QFileDialog::getOpenFileName(
        this,                                                                                // 指定父窗口
        QString::fromUtf8(WD::WDTs("CollisionReportConfiguration", "selectXml").c_str()),    // 打开文件对话框的标题
        ".",                                                                                 // 打开目录,"." 表示当前目录
        tr("xml(*.xml);;")                                                                   // 设置文件过滤器,有多个条件时中间以两个;;隔开
    );
    if (path.isEmpty())
    {
        return;
    }
    int count = ui.tableWidget->rowCount();
    // 清空表格
    for (int i = 0;i < count;++i)
    {
        ui.tableWidget->removeRow(0);
    }
    // 解析xml文件,更新界面
    this->loadConfigFromXML(path);
    ui.lineEditConfigurationFile->setText(path);
    ui.lineEditParameterName->clear();
    ui.lineEditParameterNumber->clear();
    ui.tableWidget->setCurrentItem(nullptr);
}

void CollisionReportConfiguration::slotUpdateData()
{
    QTableWidgetItem* curSelect = ui.tableWidget->currentItem();
    if (curSelect == nullptr)
        return;
    // 拿到编辑栏内容
    QString name = ui.lineEditParameterName->text();
    QString number = ui.lineEditParameterNumber->text();
    if (name.isEmpty() || number.isEmpty())
    {
        WD_WARN_T("CollisionReportConfiguration", "UpdateFail1");
        return;
    }
    int index = curSelect->row();
    if (index == 0 || index == 1 || index == 2 || index == 3)
    if (this->cheakUpdate(index, number))
    {
        WD_WARN_T("CollisionReportConfiguration", "UpdateFail2");
        return;
    }
    QStringList lstCurData;
    lstCurData << name << number;
    QList<QTableWidgetItem*>lstSelects = ui.tableWidget->selectedItems();
    if (lstSelects.count() != 2)
        return;
    for (int i = 0;i < lstSelects.count();++i)
    {
        lstSelects.at(i)->setText(lstCurData.at(i));
    }
    this->setTableUserData(curSelect->row(), lstCurData);
    // 若流水号最小值改变,下一流水号对应更改
    if (curSelect->row() != 0)
        return;
    QTableWidgetItem* nextNum = ui.tableWidget->item(3, 1);
    if (nextNum == nullptr)
        return;
    int newNum = number.toInt() + 1;
    // 若下一流水号大于流水号最大值,则重置
    if (newNum > ui.tableWidget->item(1, 1)->text().toInt())
    {
        WD_INFO_T("CollisionReportConfiguration", "UpdateFail3");
        // 点击确认后,下一流水号重置到最小
        newNum = ui.tableWidget->item(0, 1)->text().toInt();
    }
    nextNum->setText(QString::number(newNum));
    QStringList lstNextData;
    lstNextData << ui.tableWidget->item(3, 0)->text() << QString::number(newNum);
    this->setTableUserData(3, lstNextData);
}

void CollisionReportConfiguration::slotSaveData()
{
    // 重写xml保存至对应路径下
    WD::WDXmlCreator cXml("xml version='1.0' encoding='utf-8' standalone='no'");
    cXml.root("root");
    for (int i = 0;i < ui.tableWidget->rowCount();++i)
    {
        QStringList lstData = this->getTableUserData(i);
        WD::WDXmlCreatorNode node = cXml.root().append("Parameter");
        node.append("Name", lstData.at(0).toUtf8().toStdString().c_str());
        node.append("Value", lstData.at(1).toUtf8().toStdString().c_str());
    }
    cXml.toFile(_configFile.toLocal8Bit().data());
}

void CollisionReportConfiguration::closeEvent(QCloseEvent * event)
{
    WDUnused(event);
    ui.lineEditParameterName->clear();
    ui.lineEditParameterNumber->clear();
    ui.tableWidget->setCurrentItem(nullptr);
}

void CollisionReportConfiguration::showEvent(QShowEvent* event)
{
    // 统一将焦点设置到窗体，用于在窗口打开时清除其他控件焦点
    this->setFocus();
    WDUnused(event);
}

