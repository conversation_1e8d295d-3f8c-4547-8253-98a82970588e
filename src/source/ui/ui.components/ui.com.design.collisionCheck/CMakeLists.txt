set(TARGET_NAME ui_com_design_collisionCheck)

set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON) 
find_package(Qt5 COMPONENTS Core Widgets REQUIRED)

#find_package(Bullet REQUIRED)

set(HEADER_FILES
	"CollisionCheckCommon.h"
	"UiComCollisionCheck.h"
	"CollisionCheckForm.h"
	"CollisionReportConfiguration.h"
    "UiHeaderView.h"
    "CollisionCheckDialog.h"
    "CollisionRenderObject.h"
    "CollisionOptionWidget.h"
	"CollisionVolumeWidget.h"
	"CollisionObjectSettingWidget.h"
	"CollisionResultTableWidget.h"
)

set(SOURCE_FILES
	"CollisionCheckCommon.cpp"
	"UiComCollisionCheck.cpp"
	"CollisionCheckForm.cpp"
	"CollisionReportConfiguration.cpp"
	"UiHeaderView.cpp"
    "CollisionCheckDialog.cpp"
	"CollisionRenderObject.cpp"
	"CollisionOptionWidget.cpp"
	"CollisionVolumeWidget.cpp"
	"CollisionObjectSettingWidget.cpp"
	"CollisionResultTableWidget.cpp"
	"main.cpp"
)

set(FORM_FILES
	"CollisionCheckForm.ui"
	"CollisionReportConfiguration.ui"
    "CollisionCheckDialog.ui"
	"CollisionOptionWidget.ui"
	"CollisionVolumeWidget.ui"
	"CollisionObjectSettingWidget.ui"
	"CollisionResultTableWidget.ui"
)

add_library(${TARGET_NAME} SHARED
		${HEADER_FILES}
		${SOURCE_FILES}
		${FORM_FILES}
)

target_link_libraries(${TARGET_NAME} PUBLIC wizDesignerCore util.rapidxml)
target_link_libraries(${TARGET_NAME} PUBLIC collision)
target_link_libraries(${TARGET_NAME} PUBLIC ui.commonLib.custom ui.commonLib.word)
target_link_libraries(${TARGET_NAME} PUBLIC Qt5::Core Qt5::Widgets)

target_include_directories(${TARGET_NAME} PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})

add_custom_command(
	TARGET ${TARGET_NAME}
	POST_BUILD
    COMMAND             ${CMAKE_COMMAND} -E make_directory ./translations
    COMMAND             ${CMAKE_COMMAND} -E copy_directory ./translations ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/ui/components/${TARGET_NAME}/translations
	WORKING_DIRECTORY	${CMAKE_CURRENT_SOURCE_DIR}
    COMMENT             "copy directory translations..."
)