#pragma once

#include "ui_CollisionOptionWidget.h"

#include <QWidget>
#include <QButtonGroup>
#include "CollisionCheckCommon.h"

/**
 * @brief 碰撞选项界面
*/
class CollisionOptionWidget : public QWidget
{
    Q_OBJECT
public:
    /**
     * @brief 主体,客体，碰撞点的绘制标志
    */
    enum RFlag 
    {
        // 无
        RF_None = 0,
        // 主体
        RF_Subject = 1 << 0,
        // 客体
        RF_Object = 1 << 1,
        // 碰撞点
        RF_ClashPosition = 1 << 2,
    };
    using RFlags = WD::WDFlags<RFlag, unsigned int>;
public:
    CollisionOptionWidget(WD::WDCore& app, QWidget *parent = Q_NULLPTR);
    ~CollisionOptionWidget();
signals:
    /**
     * @brief 主客体显示颜色改变信号
    */
    void sigColorChanged();
    /**
     * @brief 碰撞被碰组点击信号
    */
    void sigGroupClashObstructionChecked(bool checked);
public:
    /**
     * @brief 准备选项数据
     *  将界面上设置的数据缓存下来，方便快速进行选项过滤、误差计算等...
    */
    void prepareOptionsData();
    /**
     * @brief 获取碰撞的误差选项数据
    */
    inline const CsTolerances& tolerances() const
    {
        return _tolerances;
    }
    /**
     * @brief 指定碰撞主体，准备过滤对象
     *  过滤对象将缓存到当前对象,当调用filter(...)方法时，将使用过滤对象计算主客体是否被过滤掉
     * @param sub 碰撞主体对象
    */
    void prepareFilter(const WD::WDNode& sub);
    /**
     * @brief 指定碰撞客体，执行过滤
     *  需要用到主体准备的过滤对象
     * @param obj 碰撞主体对象
     * @param obj 碰撞客体对象
     * @return 主体客体的碰撞是否被过滤掉
    */
    bool filter(const WD::WDNode& sub, const WD::WDNode& obj) const;
    /**
     * @brief 获取碰撞主体高亮颜色
    */
    WD::Color subjectColor() const;
    /**
     * @brief 获取碰撞客体高亮颜色
    */
    WD::Color objectColor() const;
    /**
     * @brief 获取碰撞对象绘制标志
    */
    RFlags rFlags() const;
    /**
     * @brief 结果列表中是否忽略触碰项
     * @return 忽略返回true, 否则返回false
    */
    bool ignoreTouch() const;
private:
    /**
     * @brief 初始化碰撞公差(Tolerances)相关界面
    */
    void initTolerancesUi();
    /**
     * @brief 初始化碰撞选项(Clash Options)相关界面
    */
    void initClashOptionsUi();
    /**
     * @brief 初始化碰撞显示(Presentation)相关界面
    */
    void initPresentationUi();
    /**
     * @brief 初始化碰撞颜色(Clash Colours)相关界面
    */
    void initClashColoursUi();
    /**
    *   @brief 初始化碰撞过滤(Clashes Ignored Within)相关界面
    */
    void initClashIgnoredWithinUi();
private:
    // 给Label设置颜色
    void setLabelColor(QLabel& label, const QColor& color);
    // 获取给Label设置的颜色
    QColor getLabelColor(const QLabel& label) const;

    inline void setUData(const QObject& object, const QString& uData)
    {
        _uDataMap[&object] = uData;
    }
    inline QString getUData(const QObject& object) const
    {
        auto fItr = _uDataMap.find(&object);
        if (fItr == _uDataMap.end())
            return "";
        return fItr->second;
    }
private:
    /**
     * @brief 界面翻译
    */
    void retranslateUi();
private:
    Ui::CollisionOptionWidget ui;
    WD::WDCore& _app;

    /*********** 辅助对象 ************/
    // 管理控件的用户数据
    std::map<const QObject*, QString> _uDataMap;

    /************ 碰撞误差相关数据 ***********/
    CsTolerances  _tolerances;

    /************ 碰撞选项相关数据 ***********/
    /**
     * @brief 包含和忽略的枚举类型
     *  主要用在 碰撞选项(Clash Options)
    */
    enum InIgType
    {
        // 未知
        Unknown = 0,
        // 包含
        Included,
        // 忽略
        Ignored,
        // 忽略相邻的
        IgnoredAdjacent,
        // 带有等级的忽略
        WithSpecIgnored
    };

    /************ 碰撞过滤相关数据 ***********/
    // 管理碰撞过滤的按钮互斥组
    std::vector<QButtonGroup*> _buttonGroups;
    // 碰撞过滤的所有按钮
    std::vector<QCheckBox*> _ignoredWithinButtons;
    // 勾选的过滤类型列表
    std::set<QString> _ignoredWithinTypes;
    /**
     * @brief 过滤对象
    */
    struct FilterObject 
    {
    public:
        // 准备过滤对象时使用的主体对象
        const WD::WDNode* pSubject = nullptr;
        // 主体节点是否是分支的子孙节点,并结合界面选项决定是否需要保留分支节点，来做额外过滤
        const WD::WDNode* pBranchNode = nullptr;
        // 所有要过滤的父节点
        std::vector<const WD::WDNode*> filterParents;
        //
        InIgType inIgType;
    };
    FilterObject _filterObject;
};
