#pragma once

#include <QObject>
#include <QHeaderView>
#include <QPainter>
#include <QToolButton>

//#include "WDCore.h"
//#include "node/WDNode.h"

/**
* @brief 自定义表头
*/
class UiHeaderView : public QHeaderView
{
	Q_OBJECT
signals:
    /**
     * @brief 下拉项被点击信号
     * @param col 列
     * @param id 下拉项Id
    */
    void sigItemClicked(int col, const QString& text);
public:
    /**
    * @brief 默认水平方向
    */
    UiHeaderView(Qt::Orientation orientation = Qt::Horizontal, QWidget *parent = nullptr);
    ~UiHeaderView();
public:
    /**
    * @brief 给某列添加下拉列表
    */
    void addItems(int col, const QStringList& items);
    /**
     * @brief 清除某列的下拉列表
    */
    void clearItems(int col);
protected:
    // 自定义头部列宽
    virtual QSize sectionSizeFromContents(int logicalIndex) const override;
    // 自定义头部,主要实现这个函数
    virtual void paintSection(QPainter* painter, const QRect &rect, int logicalIndex) const override;
private:
    struct ColData
    {
        QToolButton* pToolButton = nullptr;
        QMenu* pMenu = nullptr;
    };
    std::map<int, ColData> _mapToolbtn;
};
