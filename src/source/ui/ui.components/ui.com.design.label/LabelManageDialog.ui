<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>LabelManageDialog</class>
 <widget class="QDialog" name="LabelManageDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>467</width>
    <height>399</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>LabelManageDialog</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_3">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_4">
     <item>
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <item>
          <widget class="QPushButton" name="pushButtonAddLabel">
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string>添加标签</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QTreeWidget" name="treeWidget">
         <property name="focusPolicy">
          <enum>Qt::NoFocus</enum>
         </property>
         <column>
          <property name="text">
           <string notr="true">1</string>
          </property>
         </column>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="0,1">
         <item>
          <widget class="QPushButton" name="pushButtonCE">
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string>CE</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEditCE">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QTreeWidget" name="treeWidgetLabel">
         <property name="focusPolicy">
          <enum>Qt::NoFocus</enum>
         </property>
         <column>
          <property name="text">
           <string notr="true">1</string>
          </property>
         </column>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonApply">
       <property name="focusPolicy">
        <enum>Qt::NoFocus</enum>
       </property>
       <property name="text">
        <string>设置标签</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
