#include "LabelManageDialog.h"


void LabelManageDialog::slotOkClicked()
{
    auto pCurr = _nodeCEWidget->node();
    if (pCurr == nullptr)
    {
        WD_WARN(localTranslate("NonCurrentNode"));
        return ;
    }

    auto item1 = ui.treeWidget->currentItem();
    auto item2 = ui.treeWidgetLabel->currentItem();
    if (item1 == nullptr || item2 == nullptr)
    {
        WD_WARN(localTranslate("NonLabelOrAttrSelect"));
        return ;
    }
    auto attrName = item2->text(0).toStdString();
    auto preLabelAttr = pCurr->getAttribute(attrName);
    if (!preLabelAttr.valid())
    {
        auto preLabelName = preLabelAttr.toString();
        // ��ѯ�ñ�ǩ
        auto preLabel = _core.nodeLabelMgr().find(preLabelName);
        if (preLabel != nullptr)
        {
            preLabel->remove(pCurr->uuid(), attrName);
        }
    }

    auto label = _core.nodeLabelMgr().find(item1->text(0).toStdString());
    if (label == nullptr)
    {
        showLabels();
        WD_WARN(localTranslate("TargeLabelIsNotValid"));
        return ;
    }

    auto labeledObj = WD::LabeledObject::Create(pCurr, attrName);
    if (labeledObj == nullptr)
    {
        WD_WARN(localTranslate("NodeDoNotHasThisLabel"));
        showNodeLabels(*pCurr);
        return ;
    }
    if (!label->add(labeledObj))
    {
        WD_WARN(localTranslate("NodeLabeledByThisLabel"));
        return ;
    }
    showNodeLabels(*pCurr);
}