<?xml version="1.0" encoding="utf-8"?>

<TS language="简体中文">
	
	<context>
		<name>SupportCreateDialog</name>
		<message>
			<source>CE</source>
			<translation>当前节点</translation>
		</message>
		<message>
			<source>Atta</source>
			<translation>支架点</translation>
		</message>

		<message>
			<source>Root</source>
			<translation>生根点</translation>
		</message>
		<message>
			<source>Pivot</source>
			<translation>支吊架点</translation>
		</message>
		<message>
			<source>Pick up</source>
			<translation>拾取</translation>
		</message>
		<message>
			<source>X</source>
			<translation>X</translation>
		</message>
		<message>
			<source>Y</source>
			<translation>Y</translation>
		</message>
		<message>
			<source>Z</source>
			<translation>Z</translation>
		</message>

		<message>
			<source>Set Point</source>
			<translation>设置点</translation>
		</message>
		<message>
			<source>Prev</source>
			<translation>前一个</translation>
		</message>
		<message>
			<source>Next</source>
			<translation>后一个</translation>
		</message>
		<message>
			<source>Spread</source>
			<translation>展开缩略图</translation>
		</message>
		<message>
			<source>Reflush</source>
			<translation>更新模板库</translation>
		</message>

		<message>
			<source>Name</source>
			<translation>名称</translation>
		</message>
		<message>
			<source>Index</source>
			<translation>流水号</translation>
		</message>
		<message>
			<source>Auto</source>
			<translation>自动</translation>
		</message>
		
		<message>
			<source>Recommended</source>
			<translation>推荐生根点</translation>
		</message>
		<message>
			<source>Select</source>
			<translation>选点</translation>
		</message>

		<message>
			<source>Select Library</source>
			<translation>选择支吊架库</translation>
		</message>
		
		<message>
			<source>Parent</source>
			<translation>挂载节点</translation>
		</message>

		<message>
			<source>Create</source>
			<translation>创建</translation>
		</message>
		<message>
			<source>Cancel</source>
			<translation>取消</translation>
		</message>

		<message>
			<source>Ok</source>
			<translation>确认</translation>
		</message>
	</context>
	<context>
		<name>ErrorSupportCreateDialog</name>
		<message>
			<source>Node is not atta!</source>
			<translation>当前节点不是附点节点，操作无效</translation>
		</message>
		<message>
			<source>Fail to setParent!</source>
			<translation>节点挂载失败</translation>
		</message>
		<message>
			<source>Node invalid!</source>
			<translation>节点不合法</translation>
		</message>
		<message>
			<source>Height is invalid!</source>
			<translation>支架点与生根点之间的距离不合法</translation>
		</message>
	</context>

	<context>
		<name>AttaCreateDialog</name>
		<message>
			<source>AttaCreateDialog</source>
			<translation>创建支架点</translation>
		</message>
		<message>
			<source>Component description</source>
			<translation>管件描述</translation>
		</message>
		<message>
			<source>PBOR</source>
			<translation>公称直径</translation>
		</message>
		<message>
			<source>STYP</source>
			<translation>型号</translation>
		</message>
		<message>
			<source>SHOP</source>
			<translation>工厂预制</translation>
		</message>
		<message>
			<source>TRUE</source>
			<translation>是</translation>
		</message>
		<message>
			<source>FALS</source>
			<translation>否</translation>
		</message>

		<message>
			<source>Spool</source>
			<translation>端面距离</translation>
		</message>
		<message>
			<source>Distance</source>
			<translation>中心距离</translation>
		</message>
		<message>
			<source>Params</source>
			<translation>参数</translation>
		</message>
		<message>
			<source>InsertionSequence</source>
			<translation>插入顺序</translation>
		</message>
		<message>
			<source>Front</source>
			<translation>向前</translation>
		</message>
		<message>
			<source>Back</source>
			<translation>向后</translation>
		</message>
		<message>
			<source>Create</source>
			<translation>创建</translation>
		</message>
		<message>
			<source>Change</source>
			<translation>变换</translation>
		</message>
		<message>
			<source>Apply</source>
			<translation>应用</translation>
		</message>
		<message>
			<source>Component description</source>
			<translation>管件描述</translation>
		</message>
		<message>
			<source>Grade</source>
			<translation>等级</translation>
		</message>
		<message>
			<source>CE</source>
			<translation>定位等级</translation>
		</message>
	</context>
	<context>
		<name>ErrorAttaCreateDialog</name>
		<message>
			<source>You cannot operate the current node!</source>
			<translation>您不能操作当前节点!</translation>
		</message>
		<message>
			<source>Spco is null!</source>
			<translation>等级引用为空！</translation>
		</message>
		<message>
			<source>Parent invalid!</source>
			<translation>父节点无效！</translation>
		</message>
		<message>
			<source>Name is exists!</source>
			<translation>名称已存在！</translation>
		</message>
		<message>
			<source>ConnectTypeNotMatch DataMistake</source>
			<translation>连接类型不匹配，数据错误!</translation>
		</message>
		<message>
			<source>ConnectTypeNotMatch FrontInsertion</source>
			<translation>连接类型不匹配，在距前一个元件出口方向的100mm处插入当前元件!</translation>
		</message>
	</context>
	
	<context>
		<name>SupportLibraryWidget</name>
		<message>
			<source>SupportLibrary</source>
			<translation>支吊架库</translation>
		</message>
		<message>
			<source>SnapShoot</source>
			<translation>快照</translation>
		</message>
		<message>
			<source>Add</source>
			<translation>导入支吊架</translation>
		</message>
		<message>
			<source>Edit</source>
			<translation>支吊架参数</translation>
		</message>
		<message>
			<source>Remove</source>
			<translation>移除支吊架</translation>
		</message>
		<message>
			<source>Name</source>
			<translation>命名</translation>
		</message>
		<message>
			<source>Use</source>
			<translation>特化</translation>
		</message>
		<message>
			<source>Save</source>
			<translation>保存</translation>
		</message>
		<message>
			<source>Load</source>
			<translation>读取</translation>
		</message>
	</context>
	<context>
		<name>ErrorSupportLibraryWidget</name>
		<message>
			<source>Current node cannot null!</source>
			<translation>当前节点为空！</translation>
		</message>
		<message>
			<source>Current node cannot add in support library!</source>
			<translation>当前选中节点不符合标准！</translation>
		</message>
		<message>
			<source>Fail to add in support library!</source>
			<translation>支吊架模板添加失败！</translation>
		</message>
		<message>
			<source>Unselect support!</source>
			<translation>未选中支吊架模板！</translation>
		</message>
		<message>
			<source>Fail to remove!</source>
			<translation>删除失败！</translation>
		</message>
		<message>
			<source>Confirm Save!</source>
			<translation>确认是否保存</translation>
		</message>
		<message>
			<source>Select Support</source>
			<translation>选择支吊架模板</translation>
		</message>
		<message>
			<source>succeed</source>
			<translation>保存截图成功</translation>
		</message>
		<message>
			<source>fail</source>
			<translation>保存截图失败</translation>
		</message>
	</context>

	<context>
		<name>SupportParamEdit</name>
		<message>
			<source>SupportParamEdit</source>
			<translation>支吊架参数</translation>
		</message>
		<message>
			<source>SupportStruct</source>
			<translation>支吊架结构</translation>
		</message>
		<message>
			<source>StructParams</source>
			<translation>结构参数</translation>
		</message>
		<message>
			<source>ParamFactors</source>
			<translation>支吊架参数</translation>
		</message>
		<message>
			<source>FactorType</source>
			<translation>类型</translation>
		</message>
		<message>
			<source>FactorName</source>
			<translation>名称</translation>
		</message>
		<message>
			<source>Add</source>
			<translation>添加</translation>
		</message>
		<message>
			<source>Remove</source>
			<translation>移除</translation>
		</message>
		<message>
			<source>UpdateParamFactor</source>
			<translation>保存</translation>
		</message>
		<message>
			<source>Name</source>
			<translation>支吊架名称</translation>
		</message>
		<message>
			<source>Spec</source>
			<translation>支吊架等级</translation>
		</message>
		<message>
			<source>GType</source>
			<translation>通用类型</translation>
		</message>
		<message>
			<source>MType</source>
			<translation>型号</translation>
		</message>
		<message>
			<source>Double</source>
			<translation>数据</translation>
		</message>
		<message>
			<source>Position</source>
			<translation>位置</translation>
		</message>
		<message>
			<source>Direction</source>
			<translation>朝向</translation>
		</message>
		<message>
			<source>StructParamName</source>
			<translation>名称</translation>
		</message>
		<message>
			<source>Default</source>
			<translation>值</translation>
		</message>
		<message>
			<source>Expression</source>
			<translation>表达式</translation>
		</message>
		<message>
			<source>FactorDefault</source>
			<translation>值</translation>
		</message>
		<message>
			<source>FactorDescription</source>
			<translation>描述</translation>
		</message>
		<message>
			<source>UpdateStructParams</source>
			<translation>保存</translation>
		</message>
		<message>
			<source>Legend</source>
			<translation>图例</translation>
		</message>
		<message>
			<source>Path</source>
			<translation>文件名</translation>
		</message>
		<message>
			<source>Ok</source>
			<translation>确认</translation>
		</message>
	</context>
	<context>
		<name>ErrorSupportParamEdit</name>
		<message>
			<source>Only the png in the default directory takes effect</source>
			<translation>仅默认目录下.png生效</translation>
		</message>
		<message>
			<source>GType cannot be empty!</source>
			<translation>通用类型不能为空</translation>
		</message>
		<message>
			<source>Desc cannot be empty!</source>
			<translation>型号不能为空</translation>
		</message>
		<message>
			<source>Support is null!</source>
			<translation>支吊架模板为空！</translation>
		</message>
		<message>
			<source>Row's data abnormal!</source>
			<translation>行数据异常！</translation>
		</message>
		<message>
			<source>ParamFactor name repeat!</source>
			<translation>参数名称重复！</translation>
		</message>
		<message>
			<source>Fail to add ParamFactor</source>
			<translation>参数添加失败</translation>
		</message>
		<message>
			<source>Under the gType has same desc!</source>
			<translation>通用类型下已有相同型号支吊架模板</translation>
		</message>
		<message>
			<source>Select legend</source>
			<translation>选择图例文件</translation>
		</message>
		<message>
			<source>Support's equi is null!</source>
			<translation>支吊架模板中的根节点为空</translation>
		</message>
		<message>
			<source>Support's name repeat!</source>
			<translation >支吊架名称重复</translation>
		</message>
		<message>
			<source>NameIsNotNull</source>
			<translation >支吊架名称不能为空!</translation>
		</message>
		<message>
			<source>Spec cannot be empty!</source>
			<translation >支吊架等级为空!</translation>
		</message>
		<message>
			<source>mType cannot be empty!</source>
			<translation >支吊架型号为空!</translation>
		</message>
	</context>

	<context>
		<name>SpreadDialog</name>
		<message>
			<source>Microsoft Accor black</source>
			<translation>微软雅黑</translation>
		</message>
	</context>
</TS>