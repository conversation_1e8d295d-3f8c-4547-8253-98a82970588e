#include "Common.h"
#include "core/businessModule/design/pipeWork/WDBMDPipeUtils.h"
#include "core/businessModule/WDBDBase.h"

WD_NAMESPACE_BEGIN

WD::WDNode::SharedPtr   GetPipeSpec(WDNode& node)
{
    if (node.isType("PIPE"))
    {
        return node.getAttribute("Pspec").toNodeRef().refNode();
    }
    else if (node.isType("BRAN"))
    {
        return node.getAttribute("Pspec").toNodeRef().refNode();
    }
    else if (WDBMDPipeUtils::IsPipeComponent(node))
    {
        // 获取管件等级spco
        auto    pSpco = node.getAttribute("Spref").toNodeRef().refNode();
        // 循环遍历父节点获取所属spec节点
        auto    pSpec = pSpco;
        while (pSpec != nullptr)
        {
            if (pSpec->isType("SPEC"))
                return pSpec;

            pSpec = pSpec->parent();
        }
    }

    return nullptr;
}


/**
 * @brief 计算圆盘面与三角形交点,如果有交点，将返回最近点
 * @param triPtA 三角形顶点a
 * @param triPtB 三角形顶点b
 * @param triPtC 三角形顶点c
 * @param center 圆盘中心点
 * @param direction 圆盘所在平面的方向
 * @param radius 圆盘半径
 * @return 如果有交点，将返回最近的交点，否则返回std::nullopt
*/
std::optional<DVec3> CalcTrangleIntersect(const DVec3& triPtA
	, const DVec3& triPtB
	, const DVec3& triPtC
	, const DVec3& center
	, const DVec3& direction
	, double radius)
{
	// 分别计算点到平面的距离以及三个点至少有一个与其他点不在同一侧，证明有可能相交
	auto vA = triPtA - center;
	auto vB = triPtB - center;
	auto vC = triPtC - center;
	auto dtA = DVec3::Dot(direction, vA);
	auto dtB = DVec3::Dot(direction, vB);
	auto dtC = DVec3::Dot(direction, vC);
	// A,B,C三个点均在圆面上
	if (Abs(dtA) <= 0.00001 && Abs(dtB) <= 0.00001 && Abs(dtC) <= 0.00001)
	{
		// 根据三条边计算三条边上的最近点
		auto nearPtA = DSegment3::ClosestPointToPoint(center, triPtA, triPtB);
		auto nearPtB = DSegment3::ClosestPointToPoint(center, triPtA, triPtC);
		auto nearPtC = DSegment3::ClosestPointToPoint(center, triPtB, triPtC);
		// 分别计算三个距离
		auto disA = DVec3::Distance(center, nearPtA);
		auto disB = DVec3::Distance(center, nearPtB);
		auto disC = DVec3::Distance(center, nearPtC);
		// 最小距离则为最近点
		auto minDis = disA;
		auto nearPt = nearPtA;
		if (disB < minDis)
		{
			minDis = disB;
			nearPt = nearPtB;
		}
		if (disC < minDis)
		{
			minDis = disC;
			nearPt = nearPtC;
		}
		// 保证最近点在圆的半径范围内
		if (minDis > radius)
			return std::nullopt;
		return nearPt;
	}
	// A,B两个点在圆面上
	else if (Abs(dtA) <= 0.00001 && Abs(dtB) <= 0.00001)
	{
		// 计算最近点
		auto nearPt = DSegment3::ClosestPointToPoint(center, triPtA, triPtB);
		auto dis = DVec3::Distance(center, nearPt);
		if (dis > radius)
			return std::nullopt;
		return nearPt;
	}
	// A,C两个点在圆面上
	else if (Abs(dtA) <= 0.00001 && Abs(dtC) <= 0.00001)
	{
		// 计算最近点
		auto nearPt = DSegment3::ClosestPointToPoint(center, triPtA, triPtC);
		auto dis = DVec3::Distance(center, nearPt);
		if (dis > radius)
			return std::nullopt;
		return nearPt;
	}
	// B,C两个点在圆面上
	else if (Abs(dtB) <= 0.00001 && Abs(dtC) <= 0.00001)
	{
		// 计算最近点
		auto nearPt = DSegment3::ClosestPointToPoint(center, triPtB, triPtC);
		auto dis = DVec3::Distance(center, nearPt);
		if (dis > radius)
			return std::nullopt;
		return nearPt;
	}
	// 以下剩余两种情况:
	// 1.三个点有一个点在圆面上
	// 2.三个点均不在圆面上
	int tD = 0;
	tD += dtA < 0.0 ? -1 : 1;
	tD += dtB < 0.0 ? -1 : 1;
	tD += dtC < 0.0 ? -1 : 1;
	// 三个点均在同一侧，不可能相交
	if (tD == -3 || tD == 3)
		return std::nullopt;
	// 分别用三条边线计算与圆盘所在平面的交点
	DPlane cirPlane(direction.normalized(), center);
	auto insRetA = cirPlane.intersect(DSegment3(triPtA, triPtB));
	auto insRetB = cirPlane.intersect(DSegment3(triPtA, triPtC));
	auto insRetC = cirPlane.intersect(DSegment3(triPtB, triPtC));
	// 理论上总是能算出两个交点(这两个交点有重合的可能，也就是说明三角面的某个顶点在圆面上)
	auto& pt0 = insRetA.second;
	auto& pt1 = insRetB.second;
	if (insRetA.first && insRetB.first)
	{
		pt0 = insRetA.second;
		pt1 = insRetB.second;
	}
	else if (insRetA.first && insRetC.first)
	{
		pt0 = insRetA.second;
		pt1 = insRetC.second;
	}
	else if (insRetB.first && insRetC.first)
	{
		pt0 = insRetB.second;
		pt1 = insRetC.second;
	}
	else
	{
		assert(false && "注意:出现了意料之外的数据，需要调试!!");
		return std::nullopt;
	}
	// 两点重合
	if (DVec3::DistanceSq(pt0, pt1) <= NumLimits<double>::Epsilon)
	{
		auto dis = DVec3::Distance(center, pt0);
		if (dis > radius)
			return std::nullopt;
		return pt0;
	}
	// 计算最近点
	auto nearPt = DSegment3::ClosestPointToPoint(center, pt0, pt1);
	auto dis = DVec3::Distance(center, nearPt);
	if (dis > radius)
		return std::nullopt;
	return nearPt;
}
void CalcIntersect(IntersectDatas& result, WDNode::SharedPtr pNode, const DVec3& center, const DVec3& normal, double radius)
{
    IntersectData tData;
    // 最小距离
    double minDist = WD::NumLimits<double>::Max;
    // 最小距离点
    DVec3 minDistPt;
    if (pNode == nullptr)
        return ;
    auto    pBase   =   pNode->getBDBase();
    if (pBase == nullptr)
        return ;
    auto    pGraph  =   pBase->graphableSupporter();
    if (pGraph == nullptr)
        return ;
    auto    gGeos   =   pGraph->gGeometries(WDGraphableInterface::GGT_Basic);
    if (gGeos.empty())
        return ;
    auto&   trans   =   pNode->globalTransform();
    for (auto& pGeo : gGeos)
    {
        auto        transMat    =   trans * pGeo->transform();
        auto        mesh        =   pGeo->mesh();
        if (mesh == nullptr)
            continue;
        auto&       allPos      =   mesh->positions();
        if (allPos.empty())
            continue;
        DVec3Vector poss;
        poss.reserve(allPos.size());
        for (const auto& pos: allPos)
            poss.push_back(transMat * DVec3(pos));
        auto&       pris    =   mesh->primitiveSets(WDMesh::PSType::Solid);

        for (const auto& pri : pris)
        {
            if (pri.empty())
                continue;
            switch (pri.primitiveType())
            {
            case WDPrimitiveSet::PT_Triangles:
                {
                    WDPrimitiveSet::DrawType type = pri.drawType();
                    switch (type)
                    {
                    case WD::WDPrimitiveSet::DrawType::DT_Array:
                        {
                            auto&   priData =   pri.drawArrayData();
                            uint    nStart  =   priData.first;
                            uint    nEnd    =   priData.first + priData.second;
    
                            for (size_t i = nStart; i < nEnd; i += 3)
                            {
                                auto res = CalcTrangleIntersect(poss[i + 0], poss[i + 1], poss[i + 2], center, normal, radius);
                                if (res)
                                {
                                    auto& intersectPt = res.value();
                                    auto tDist = DVec3::Distance(intersectPt, center);
                                    if (tDist < minDist)
                                    {
                                        minDistPt   =   intersectPt;
                                        minDist     =   tDist;
                                    }
                                }
                            }
                        } 
                        break;
                    case WD::WDPrimitiveSet::DrawType::DT_ElementByte:
                        {
                            const auto& indices = pri.drawElementByteData();
                            for (size_t i = 0; i < indices.size(); i += 3) 
                            {
                                auto res = CalcTrangleIntersect(poss[indices[i + 0]], poss[indices[i + 1]], poss[indices[i + 2]], center, normal, radius);
                                if (res)
                                {
                                    auto& intersectPt = res.value();
                                    auto tDist = DVec3::Distance(intersectPt, center);
                                    if (tDist < minDist)
                                    {
                                        minDistPt   =   intersectPt;
                                        minDist     =   tDist;
                                    }
                                }
                            }
                        }
                        break;
                    case WD::WDPrimitiveSet::DrawType::DT_ElementUShort:
                        {
                            const auto& indices = pri.drawElementUShortData();
                            for (size_t i = 0; i < indices.size(); i += 3)
                            {
                                auto res = CalcTrangleIntersect(poss[indices[i + 0]], poss[indices[i + 1]], poss[indices[i + 2]], center, normal, radius);
                                if (res)
                                {
                                    auto& intersectPt = res.value();
                                    auto tDist = DVec3::Distance(intersectPt, center);
                                    if (tDist < minDist)
                                    {
                                        minDistPt   =   intersectPt;
                                        minDist     =   tDist;
                                    }
                                }
                            }
                        }
                        break;
                    case WD::WDPrimitiveSet::DrawType::DT_ElementUInt:
                        {
                            const auto& indices = pri.drawElementUIntData();
                            for (size_t i = 0; i < indices.size(); i += 3)
                            {
                                auto res = CalcTrangleIntersect(poss[indices[i + 0]], poss[indices[i + 1]], poss[indices[i + 2]], center, normal, radius);
                                if (res)
                                {
                                    auto& intersectPt = res.value();
                                    auto tDist = DVec3::Distance(intersectPt, center);
                                    if (tDist < minDist)
                                    {
                                        minDistPt   =   intersectPt;
                                        minDist     =   tDist;
                                    }
                                }
                            }
                        }
                        break;
                    default:
                        break;
                    }
                }
            break;
            case WDPrimitiveSet::PT_TriangleStrip:
                {
                    auto&   priData =   pri.drawArrayData();
                    uint    nStart  =   priData.first;
                    uint    nCount  =   priData.first + priData.second;
    
                    DVec3   pt0 =   poss[nStart + 0];
                    DVec3   pt1 =   poss[nStart + 1];
    
                    for (uint i = nStart + 2; i < nCount; ++i)
                    {
                        const auto&  pt2 = poss[i];
                        auto res = CalcTrangleIntersect(pt0, pt1, pt2, center, normal, radius);
                        if (res)
                        {
                            auto& intersectPt = res.value();
                            auto tDist = DVec3::Distance(intersectPt, center);
                            if (tDist < minDist)
                            {
                                minDistPt   =   intersectPt;
                                minDist     =   tDist;
                            }
                        }
                        pt0 = pt1;
                        pt1 = pt2;
                    }
                }
            break;
            case WDPrimitiveSet::PT_TriangleFan:
                {
                    auto&   priData =   pri.drawArrayData();
                    uint    nStart  =   priData.first;
                    uint    nCount  =   priData.first + priData.second;
    
                    const   DVec3&  pt0 =   poss[nStart + 0];
                    DVec3   pt1 =   poss[nStart + 1];
    
                    for (uint i = nStart + 2; i < nCount; ++i)
                    {
                        const DVec3& pt2    =   poss[i];
                        auto res = CalcTrangleIntersect(pt0, pt1, pt2, center, normal, radius);
                        if (res)
                        {
                            auto& intersectPt = res.value();
                            auto tDist = DVec3::Distance(intersectPt, center);
                            if (tDist < minDist)
                            {
                                minDistPt   =   intersectPt;
                                minDist     =   tDist;
                            }
                        }
                        pt1 = pt2;
                    }
                }
            break;
            default:
            break;
            }
        }
    }
    if (minDist < WD::NumLimits<double>::Max)
    {
        tData.pNode = pNode;
        tData.pts.push_back(minDistPt);
        result.emplace_back(tData);
    }
}

WD_NAMESPACE_END
