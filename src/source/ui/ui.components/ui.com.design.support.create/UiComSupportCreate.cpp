#include "UiComSupportCreate.h"
#include "Common.h"

UiComSupportCreate::UiComSupportCreate(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject *parent)
    : QObject(parent)
    , IUiComponent(mainWindow, attrs)
    , _core(mainWindow.core())
{
    _supportMgr         =   new WD::WDSupportMgr(_core);

    _readed             =   false;
    
    _pSuppCreateDialog  =   nullptr;
    _pAttaCreateDialog  =   nullptr;
    _widgetSupportEdit  =   nullptr;
}
UiComSupportCreate::~UiComSupportCreate()
{
    if (_supportMgr != nullptr)
    {
        delete _supportMgr;
        _supportMgr = nullptr;
    }
}

void UiComSupportCreate::onNotice(UiNotice * pNotice)
{
    int nType = pNotice->type();
    switch (nType)
    {
    case UiNoticeType::UNT_AllReady:
    {
        _pSuppCreateDialog = new SupportCreateDialog(mWindow().core(), *_supportMgr, mWindow().widget());
        _pAttaCreateDialog = new AttaCreateDialog(mWindow().core(), mWindow().widget());
        _widgetSupportEdit = new SupportLibraryWidget(_core, *_supportMgr, mWindow().widget());
    }
    break;
    case UiNoticeType::UNT_ReadyUnload:
    {
        if (_pSuppCreateDialog != nullptr)
        {
            delete _pSuppCreateDialog;
            _pSuppCreateDialog = nullptr;
        }
        if (_pAttaCreateDialog != nullptr)
        {
            delete _pAttaCreateDialog;
            _pAttaCreateDialog = nullptr;
        }
        if (_widgetSupportEdit != nullptr)
        {
            delete _widgetSupportEdit;
            _widgetSupportEdit = nullptr;
        }
    }
    break;
    case UiNoticeType::UNT_Action:
    {
        UiActionNotice* pActionNotice = static_cast<UiActionNotice*>(pNotice);
        // 支吊架建模
        if (pActionNotice->action().is("action.design.support.create"))
        {
            if (!_readed)
            {
                // 默认加载设备库
                std::string path = _core.dataDirPath() + std::string(WD::Library_File_Name);
                _supportMgr->read(path.c_str());

                _readed = true;
            }
            if (_pSuppCreateDialog->isHidden())
                _pSuppCreateDialog->show();
            else
                _pSuppCreateDialog->activateWindow();
        }
        // 创建附点
        else if (pActionNotice->action().is("action.design.atta.create"))
        {
            if (_pAttaCreateDialog->isHidden())
                _pAttaCreateDialog->show();
            else
                _pAttaCreateDialog->activateWindow();
        }
        // 支吊架模板库
        else if (pActionNotice->action().is("action.design.supportLibrary.support.edit"))
        {
            if (!_readed)
            {
                // 默认加载设备库
                std::string path = _core.dataDirPath() + std::string(WD::Library_File_Name);
                _supportMgr->read(path.c_str());

                _readed = true;
            }
            if (_widgetSupportEdit->isHidden())
                _widgetSupportEdit->show();
            else
                _widgetSupportEdit->activateWindow();
        }
    }
    break;
    default:
        break;
    }
}