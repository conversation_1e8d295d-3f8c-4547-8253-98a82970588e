#include "SupportLibraryWidget.h"
#include "core/nodeTree/WDNodeTree.h"
#include "core/message/WDMessage.h"
#include "core/WDTranslate.h"
#include <QFileDialog>
#include "../../ui.commonLibrary/ui.commonLib.weakObject/WeakObject.h"
#include "../Common.h"
#include "core/viewer/WDViewer.h"
#include "core/businessModule/design/WDBMDesign.h"

/**
 * @brief 节点是否支持导入
 * @param node 
 * @return 
*/
static bool SupportImport(WD::WDNode& node)
{
    return node.isAnyOfType("HANG", "EQUI", "STRU");
}

SupportLibraryWidget::SupportLibraryWidget(WD::WDCore& app, WD::WDSupportMgr& supportMgr, QWidget *parent)
    : QDialog(parent)
    , _supportMgr(supportMgr)
    , _app(app)
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    // 文本翻译
    this->translate();

    // 创建设备模板参数编辑窗口
    _widgetParamsEdit = new SupportParamEdit(app, _supportMgr, parent);

    // 绑定事件通知响应
    connect(ui.pushButtonAdd,   &QPushButton::clicked,                          this, &SupportLibraryWidget::slotAddClicked);
    connect(ui.pushButtonEdit,  &QPushButton::clicked,                          this, &SupportLibraryWidget::slotEditClicked);
    connect(ui.pushButtonRemove,&QPushButton::clicked,                          this, &SupportLibraryWidget::slotRemoveClicked);
    connect(ui.pushButtonSave,  &QPushButton::clicked,                          this, &SupportLibraryWidget::slotSaveClicked);
    connect(ui.pushButtonLoad,  &QPushButton::clicked,                          this, &SupportLibraryWidget::slotLoadClicked);
    connect(_widgetParamsEdit,  &SupportParamEdit::sigSupportParamEditFinished,   this, &SupportLibraryWidget::slotSupportParamEditFinished);
}
SupportLibraryWidget::~SupportLibraryWidget()
{
    if (_widgetParamsEdit != nullptr)
    {
        _widgetParamsEdit->deleteLater();
    }
}

void SupportLibraryWidget::showEvent(QShowEvent* evt)
{
    // 更新加载设备库
    this->updateSupportList();

    // 统一将焦点设置到窗体，用于在窗口打开时清除其他控件焦点
    this->setFocus();
    WDUnused(evt);
}
void SupportLibraryWidget::hideEvent(QHideEvent* evt)
{
    WDUnused(evt);
}

WD::Aabb3 SquareAabb(const WD::Aabb3& aabb)
{
    WD::Aabb3 result;
    auto size = aabb.size().length();
    auto radius = size * 0.5;
    result.min = aabb.center() - WD::DVec3(radius);
    result.max = aabb.center() + WD::DVec3(radius);

    return result;
}
void SupportLibraryWidget::slotAddClicked()
{
    // 获取当前节点
    auto pCurNode = _app.nodeTree().currentNode();
    if (pCurNode == nullptr)
    {
        WD_ERROR_T("ErrorSupportLibraryWidget", "Current node cannot null!");
        return ;
    }

    // 判断当前节点是否能够加入到设备库
    if (!SupportImport(*pCurNode))
    {
        WD_ERROR_T("ErrorSupportLibraryWidget", "Current node cannot add in support library!");
        return ;
    }
    // 截图
    auto legend = pCurNode->name() + ".png";
    if (ui.checkBoxSnopShoot->isChecked())
    {
        // 记录节点的标记状态后取消选中状态
        std::map<WD::WDNode::SharedPtr, WD::WDNode::Flags> mapNodeSrcFlags;
        WD::WDNode::RecursionHelpterR(*pCurNode, [&mapNodeSrcFlags](WD::WDNode& node)
            {
                auto flags = node.flags();
                mapNodeSrcFlags[WD::WDNode::ToShared(&node)] = flags;
                node.setFlags(flags.setFlag(WD::WDNode::Flag::F_Selected, false));
                return false;
            });
        pCurNode->update(true);
        _app.needRepaint();

        // 截图
        // 获取包围盒
        auto image = _app.viewer().getSnapShotImage(SquareAabb(pCurNode->aabb()), WD::DVec3::AxisX(), WD::DVec3::AxisZ());
        if (image.data().empty())
            return ;

        //垂直翻转图像（一些接口如glReadPixels等，读取顺序为从下往上，因此需要调整图像）
        image.verticalFlip();
        //选择文件保存路径
        //保存到图片
        QString inf;
        auto fileName = WD::WDSupport::LegendPath() + legend;
        image.save(QString(fileName.c_str()).toLocal8Bit().data())? inf = "succeed": inf="fail";

        WD_INFO_T("ErrorSupportLibraryWidget", inf.toUtf8().toStdString());

        // 恢复节点的标记状态
        for (auto& var: mapNodeSrcFlags)
        {
            auto pNode = var.first;
            if (pNode == nullptr)
                continue;
            pNode->setFlags(var.second);
        }
        pCurNode->update(true);
        _app.needRepaint();
    }
    // 加入设备模板库
    WD::WDSupport::SharedPtr pSupport = _supportMgr.addSupport(pCurNode);
    if (pSupport == nullptr)
    {
        WD_ERROR_T("ErrorSupportLibraryWidget", "Fail to add in support library!");
        return ;
    }
    pSupport->setLegend(legend);
    
    // 更新设备列表
    this->updateSupportList();
}
void SupportLibraryWidget::slotEditClicked()
{
    // 获取当前选中设备模板
    auto pSupport = this->getSeleSupport();
    if (pSupport == nullptr)
    {
        WD_ERROR_T("ErrorSupportLibraryWidget", "Unselect support!");
        return ;
    }

    _widgetParamsEdit->setSupport(pSupport);
    _widgetParamsEdit->show();
}
void SupportLibraryWidget::slotRemoveClicked()
{
    // 获取当前选中设备模板
    auto pSupport = this->getSeleSupport();
    if (pSupport == nullptr)
    {
        WD_ERROR_T("ErrorSupportLibraryWidget", "Unselect support!");
        return ;
    }
    if (!_supportMgr.removeSupport(pSupport))
    {
        WD_ERROR_T("ErrorSupportLibraryWidget", "Fail to remove!");
        return ;
    }
    
    // 更新设备列表
    this->updateSupportList();
}
void SupportLibraryWidget::slotSaveClicked()
{
    // 提示保存
    int res = WD_QUESTION_T("ErrorSupportLibraryWidget", "Confirm Save!");
    if (res != 0)
        return ;

    std::string path = _app.dataDirPath() + std::string(WD::Library_File_Name);
    _supportMgr.write(path.c_str());
}
void SupportLibraryWidget::slotLoadClicked()
{
    // 先保存现有设备模板
    auto supports = _supportMgr.supports();

    QString fileName = QFileDialog::getOpenFileName(this
        , QString::fromUtf8(WD::WDTs("ErrorSupportLibraryWidget", "Select Support").c_str())
        , ""
        , "file(*.xml)");

    if (fileName.isEmpty())
        return ;

    //!TODO: 后续将读写xml单独成插件，读写文件返回objs统一管理
    _supportMgr.read(fileName.toUtf8().data());
    auto tempSupports = _supportMgr.supports();
    _supportMgr.clear();
    _supportMgr.addSupports(supports);
    _supportMgr.addSupports(tempSupports);

    // 更新设备列表
    this->updateSupportList();
}
void SupportLibraryWidget::slotSupportParamEditFinished()
{
    // 刷新设备列表
    this->updateSupportList();
}

void SupportLibraryWidget::translate()
{
    this->setWindowTitle(QString::fromUtf8(WD::WDTs("SupportLibraryWidget", "SupportLibrary").data()));

    ui.checkBoxSnopShoot->setText(QString::fromUtf8(WD::WDTs("SupportLibraryWidget", "SnapShoot").data()));
    ui.pushButtonAdd->setText(QString::fromUtf8(WD::WDTs("SupportLibraryWidget", "Add").data()));
    ui.pushButtonEdit->setText(QString::fromUtf8(WD::WDTs("SupportLibraryWidget", "Edit").data()));
    ui.pushButtonRemove->setText(QString::fromUtf8(WD::WDTs("SupportLibraryWidget", "Remove").data()));
    ui.pushButtonSave->setText(QString::fromUtf8(WD::WDTs("SupportLibraryWidget", "Save").data()));
    ui.pushButtonLoad->setText(QString::fromUtf8(WD::WDTs("SupportLibraryWidget", "Load").data()));
}
void SupportLibraryWidget::updateSupportList()
{
    // 清空设备列表
    ui.listWidgetSupport->clear();

    for (auto& pSupport : _supportMgr.supports())
    {
        if (pSupport == nullptr)
            continue;

        QVariant    userData;
        userData.setValue(UiWeakObject(pSupport));
        auto        pItem   =   new QListWidgetItem(QString::fromUtf8(pSupport->name().c_str()));
        pItem->setData(Qt::UserRole, userData);
        ui.listWidgetSupport->addItem(pItem);
    }
}

WD::WDSupport::SharedPtr SupportLibraryWidget::getSeleSupport()
{
    auto    pCurItem    =   ui.listWidgetSupport->currentItem();
    if (pCurItem == nullptr)
        return nullptr;
    auto    userData    =   pCurItem->data(Qt::UserRole);
    if (!userData.isValid())
        return nullptr;

    return userData.value<UiWeakObject>().subObject<WD::WDSupport>();
}