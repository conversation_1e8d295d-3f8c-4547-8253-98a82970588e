#include "SupportParamEdit.h"
#include "core/message/WDMessage.h"
#include <QCheckBox>
#include <QFileDialog>
#include "../../ui.commonLibrary/ui.commonLib.weakObject/WeakObject.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/ICustomWidgets.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/businessModule/design/equipment/WDBMDEquiUtils.h"
#include "core/businessModule/design/pipeWork/WDBMDPipeUtils.h"

ReadOnlyDelegate::ReadOnlyDelegate(QObject *parent)
    : QStyledItemDelegate(parent)
{}
QWidget* ReadOnlyDelegate::createEditor(QWidget *parent, const QStyleOptionViewItem &option, const QModelIndex &index) const
{  
    WDUnused3(parent, option, index);
    // 总是返回nullptr，表示没有编辑器  
    return nullptr;  
}

SupportParamEdit::SupportParamEdit(WD::WDCore& core, WD::WDSupportMgr& supportMgr, QWidget *parent)
    : QDialog(parent)
    , _core(core)
    , _supportMgr(supportMgr)
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));

    // 界面居中
    auto&   pParentGeo  =   parent->geometry();
    int     width       =   this->width();
    int     height      =   this->height();
    int     left        =   pParentGeo.left() - (width - pParentGeo.width()) / 2;
    int     top         =   pParentGeo.top() - (height - pParentGeo.height()) / 2;
    this->setGeometry(QRect(left, top, width, height));

    // 文本翻译
    this->translate();

    // 下拉框过滤
    ICustomWidgets::QComboBoxPopupCompletion(ui.comboBoxSpec);
    ICustomWidgets::QComboBoxPopupCompletion(ui.comboBoxGType);

    // 绑定事件通知响应
    connect(ui.pushButtoAddFactor,              &QPushButton::clicked,              this, &SupportParamEdit::slotAddFactor);
    connect(ui.pushButtonRemoveFactor,          &QPushButton::clicked,              this, &SupportParamEdit::slotRemoveFactor);
    connect(ui.pushButtonUpdateParamFactor,     &QPushButton::clicked,              this, &SupportParamEdit::slotUpdateParamFactor);
    connect(ui.treeWidgetSupportStruct,         &QTreeWidget::itemSelectionChanged, this, &SupportParamEdit::slotSupportStructItemChanged);
    connect(ui.pushButtonUpdateStructParams,    &QPushButton::clicked,              this, &SupportParamEdit::slotUpdateStructParams);
    connect(ui.pushButtonOk,                    &QPushButton::clicked,              this, &SupportParamEdit::slotOKClicked);
    connect(ui.pushButtonPath,                  &QPushButton::clicked,              this, &SupportParamEdit::slotPathClicked);
}
SupportParamEdit::~SupportParamEdit()
{
    _pSupport.reset();
}

void SupportParamEdit::showEvent(QShowEvent* evt)
{
    WDUnused(evt);

    auto pSupport = _pSupport.lock();
    if (pSupport == nullptr)
    {
        //!TODO: 提示支吊架模板为空
        return ;
    }

    // 初始化树名称
    this->initStructTree();
    // 初始化表头
    this->initStructParamTable();
    this->initParamFactorTable();

    // 显示支吊架模板名称
    ui.lineEditName->setText(QString::fromUtf8(pSupport->name().c_str()));
    
    // 更新支吊架等级
    this->updateSpecList();
    // 更新通用类型
    this->updateGTypeList();
    // 更新描述
    this->updateDesc();

    // 更新支吊架参数因子
    this->updateSupportFactors();
    // 更新支吊架结构树
    this->updateSupportStruct();
    // 更新图例
    this->updateLegend();
}
void SupportParamEdit::hideEvent(QHideEvent* evt)
{
    WDUnused(evt);
}

void SupportParamEdit::slotAddFactor()
{
    auto        pSupport     =   _pSupport.lock();
    if (pSupport == nullptr)
    {
        WD_ERROR_T("ErrorSupportParamEdit", "Support is null!");
        return ;
    }
    
    // 名称(TODO: 需新增命名规则，如A后应该是B等等
    QString     name        =   ui.lineEditFactorName->text();

    // 添加项
    int         rowCount    =   ui.tableWidgetParamFactors->rowCount();
    ui.tableWidgetParamFactors->insertRow(rowCount);
    ui.tableWidgetParamFactors->setItem(rowCount, 0, new QTableWidgetItem(name));
    ui.tableWidgetParamFactors->setItem(rowCount, 1, new QTableWidgetItem(""));
    ui.tableWidgetParamFactors->setItem(rowCount, 2, new QTableWidgetItem(""));
}
void SupportParamEdit::slotRemoveFactor()
{
    ui.tableWidgetParamFactors->removeRow(ui.tableWidgetParamFactors->currentRow());
}
void SupportParamEdit::slotUpdateParamFactor()
{
    auto pSupport = _pSupport.lock();
    if (pSupport == nullptr)
    {
        WD_ERROR_T("ErrorSupportParamEdit", "Support is null!");
        return ;
    }

    // 清空当前支吊架模板的参数因子
    pSupport->clearFactors();
    
    // 收集参数因子数据
    struct FactorData
    {
        // 描述
        std::string desciption;
        // 值
        std::string value;
    };
    std::unordered_map<std::string, FactorData> factorDatas;
    for (int i = 0; i < ui.tableWidgetParamFactors->rowCount(); ++i)
    {
        // 名称
        auto        pNameItem   =   ui.tableWidgetParamFactors->item(i, 0);
        // 描述
        auto        pDescItem   =   ui.tableWidgetParamFactors->item(i, 1);
        // 默认值
        auto        pValueItem  =   ui.tableWidgetParamFactors->item(i, 2);
        if (pNameItem == nullptr || pDescItem == nullptr || pValueItem == nullptr)
        {
            WD_WARN(WD::ToString(i) + WD::WDTs("ErrorSupportParamEdit", "Row's data abnormal!"));
            continue;
        }

        std::string name        =   pNameItem->text().toUtf8().data();
        std::string desc        =   pDescItem->text().toUtf8().data();
        std::string value       =   pValueItem->text().toUtf8().data();

        // 收集数据
        if (factorDatas.find(name) != factorDatas.end())
        {
            WD_ERROR(name + WD::WDTs("ErrorSupportParamEdit", "ParamFactor name repeat!"));
            return ;
        }
        factorDatas[name] = {desc, value};
    }
    // 重新添加参数因子
    for (auto& factorData : factorDatas)
    {
        // 添加参数因子
        std::string                 name    =   factorData.first;
        std::string                 desc    =   factorData.second.desciption;
        std::string                 value   =   factorData.second.value;
        if (!pSupport->setFactor(name, desc, value))
        {
            WD_WARN(name + WD::WDTs("ErrorSupportParamEdit", "ParamFactor's name is same"));
            continue ;
        }
    }
}
void SupportParamEdit::slotUpdateStructParams()
{
    auto    pSupport     =   _pSupport.lock();
    if (pSupport == nullptr)
    {
        WD_ERROR_T("ErrorSupportParamEdit", "Support is null!");
        return ;
    }
    auto    pCurNode    =   _pCurNode.lock();
    if (pCurNode == nullptr)
        return ;
    // 移除相关节点的参数
    pSupport->undefineRefNodeParam(*pCurNode);
        
    // 位置
    std::string posX;
    std::string posY;
    std::string posZ;
    auto        pItemX  =   ui.tableWidgetStructParams->item(0, 2);
    if (pItemX != nullptr)
    {
        posX = pItemX->text().toUtf8().data();
    }
    auto        pItemY  =   ui.tableWidgetStructParams->item(1, 2);
    if (pItemY != nullptr)
    {
        posY = pItemY->text().toUtf8().data();
    }
    auto        pItemZ  =   ui.tableWidgetStructParams->item(2, 2);
    if (pItemZ != nullptr)
    {
        posZ = pItemZ->text().toUtf8().data();
    }
    pSupport->defineTRSParam(*pCurNode, pCurNode->name(), posX, posY, posZ);

    //!TODO: 朝向
    
    // 参数
    for (int i = 3; i < ui.tableWidgetStructParams->rowCount(); ++i)
    {
        auto    pItemName   =   ui.tableWidgetStructParams->item(i, 0);
        auto    pItemExpr   =   ui.tableWidgetStructParams->item(i, 2);
        if (pItemName == nullptr || pItemExpr == nullptr)
            continue;

        std::string attrName;
        auto        userData    =   pItemName->data(Qt::UserRole);
        if (userData.isValid())
        {
            attrName = userData.toString().toUtf8().data();
        }
        auto        userData1   =   pItemName->data(Qt::UserRole + 1);
        if (userData1.isValid())
        {
            attrName += userData1.toString().toUtf8().data();
        }
        std::string expr        =   pItemExpr->text().toUtf8().data();
        if (!expr.empty())
        {
            pSupport->defineShapeParam(*pCurNode, attrName, expr);
        }
    }
}
void SupportParamEdit::slotSupportStructItemChanged()
{
    auto    pItem       =   ui.treeWidgetSupportStruct->currentItem();
    if (pItem == nullptr)
        return ;

    // 更新支吊架参数列表
    auto    userData    =   pItem->data(0, Qt::UserRole);
    if (!userData.isValid())
        return ;
    auto    pNode       =   userData.value<UiWeakObject>().subObject<WD::WDNode>();
    _pCurNode = pNode;
    this->updateStructParams(pNode);
}
/**
 * @brief 校验支吊架类别
 * @param mgr 支吊架模板管理
 * @param spec 等级
 * @param gType 通用类型
 * @param mType 型号
*/
static bool CheckSupport(WD::WDSupportMgr& mgr, std::string_view spec, std::string_view gType, std::string_view mType, WD::WDSupport::SharedPtr pSupport = nullptr)
{
    auto& supports = mgr.supports();
    for (auto& support : supports)
    {
        if (support == nullptr || support == pSupport)
            continue;

        if (support->spec() == spec && support->gType() == gType && support->mType() == mType)
        {
            return false;
        }
    }
    return true;
}
void SupportParamEdit::slotOKClicked()
{
    WD::WDSupport::SharedPtr pSupport = _pSupport.lock();
    if (pSupport == nullptr)
    {
        WD_ERROR_T("ErrorSupportParamEdit", "Support is null!");
        return ;
    }

    // 获取支吊架模板名称
    std::string name = ui.lineEditName->text().toUtf8().data();
    if (name.empty())
    {
        WD_ERROR_T("ErrorSupportParamEdit", "NameIsNotNull");
        return;
    }
    for (auto& pTSupport : _supportMgr.supports())
    {
        if (pTSupport == nullptr || pTSupport == pSupport)
            continue;

        if (pTSupport->name() == name)
        {
            WD_ERROR_T("ErrorSupportParamEdit", "Support's name repeat!");
            return ;
        }
    }
    
    // 获取支吊架模板界面上类别
    std::string spec    =   ui.comboBoxSpec->currentText().toUtf8().data();
    if (spec.empty())
    {
        WD_ERROR_T("ErrorSupportParamEdit", "Spec cannot be empty!");
        return ;
    }
    std::string gType   =   ui.comboBoxGType->currentText().toUtf8().data();
    if (gType.empty())
    {
        WD_ERROR_T("ErrorSupportParamEdit", "GType cannot be empty!");
        return ;
    }
    std::string mType   =   ui.lineEditMType->text().toUtf8().data();
    if (mType.empty())
    {
        WD_ERROR_T("ErrorSupportParamEdit", "mType cannot be empty!");
        return ;
    }
    if (!CheckSupport(_supportMgr, spec, gType, mType, pSupport))
    {
        WD_ERROR_T("ErrorSupportParamEdit", "Under the gType has same desc!");
        return ;
    }

    // 获取图例文件名称
    std::string legend  =   ui.lineEditPath->text().toUtf8().data();

    pSupport->setName(name);
    pSupport->setSpec(spec);
    pSupport->setGType(gType);
    pSupport->setMType(mType);
    pSupport->setLegend(legend);
    emit sigSupportParamEditFinished();
    this->accept();
}
void SupportParamEdit::slotPathClicked()
{
    // 提示 仅默认目录下.png生效
    WD_INFO_T("ErrorSupportParamEdit", "Only the png in the default directory takes effect");

    QString fileName = QFileDialog::getOpenFileName(this
        , QString::fromUtf8(WD::WDTs("ErrorSupportParamEdit", "Select legend").c_str())
        , QString::fromUtf8(WD::WDSupport::LegendPath().c_str())
        , "file(*.png)");

    if (fileName.isEmpty())
        return ;

    // 显示图例文件名称(不含路径)
    std::string legend = WD::FileName(fileName.toLocal8Bit().data());
    ui.lineEditPath->setText(QString::fromUtf8(legend.c_str()));

    // 显示图例
    QPixmap pixmap(fileName);
    pixmap = pixmap.scaledToWidth(ui.labelLegend->width(), Qt::SmoothTransformation);
    ui.labelLegend->setPixmap(pixmap);
    ui.labelLegend->setAlignment(Qt::AlignCenter);
}

void SupportParamEdit::initStructTree()
{
    auto pSupport = _pSupport.lock();
    if (pSupport == nullptr)
    {
        WD_ERROR_T("ErrorSupportParamEdit", "Support is null!");
        return ;
    }

    // 设置水平表头标签
    QStringList horizontalHeaderLabels;
    horizontalHeaderLabels 
        << QString::fromUtf8(pSupport->name().c_str());
    ui.treeWidgetSupportStruct->setHeaderLabels(horizontalHeaderLabels);
}
void SupportParamEdit::initStructParamTable()
{
    // 设置水平表头标签
    QStringList horizontalHeaderLabels;
    horizontalHeaderLabels 
        << QString::fromUtf8(WD::WDTs("SupportParamEdit", "StructParamName").data()) 
        << QString::fromUtf8(WD::WDTs("SupportParamEdit", "Default").data())
        << QString::fromUtf8(WD::WDTs("SupportParamEdit", "Expression").data());
    ui.tableWidgetStructParams->setHorizontalHeaderLabels(horizontalHeaderLabels);
    ui.tableWidgetStructParams->setColumnCount(horizontalHeaderLabels.size());
    ReadOnlyDelegate* tDelegate = new ReadOnlyDelegate(this);  
    ui.tableWidgetStructParams->setItemDelegateForColumn(1, tDelegate); // 设置第2列的委托为只读委托

}
void SupportParamEdit::initParamFactorTable()
{
    // 设置水平表头标签
    QStringList horizontalHeaderLabels;
    horizontalHeaderLabels 
        << QString::fromUtf8(WD::WDTs("SupportParamEdit", "FactorName").data())
        << QString::fromUtf8(WD::WDTs("SupportParamEdit", "FactorDescription").data())
        << QString::fromUtf8(WD::WDTs("SupportParamEdit", "FactorDefault").data());
    ui.tableWidgetParamFactors->setHorizontalHeaderLabels(horizontalHeaderLabels);
    ui.tableWidgetParamFactors->setColumnCount(horizontalHeaderLabels.size());
}

bool            SupportParamEdit::enableSupportParamRef(WD::WDNode* pNode)
{
    if (pNode == nullptr)
        return false;
    
    return WD::WDBMDEquiUtils::IsEquiPrimitive(*pNode)
        || WD::WDBMDPipeUtils::IsPipeComponent(*pNode)
        || pNode->isAnyOfType("NOZZ", "SUBE", "SUBS", "FRMW", "SBFR", "SCTN");
}
void            SupportParamEdit::addItem(WD::WDNode* pParentNode, QTreeWidgetItem* pParentItem)
{
    if (pParentNode == nullptr || pParentItem == nullptr)
        return ;
    
    for (size_t i = 0; i < pParentNode->childCount(); ++i)
    {
        auto pChild = pParentNode->childAt(i);
        if (pChild == nullptr)
            continue;

        if (!this->enableSupportParamRef(pChild.get()))
            continue;

        QTreeWidgetItem* pItem = new QTreeWidgetItem();
        pItem->setText(0, QString::fromUtf8(pChild->name().c_str()));
        QVariant userData;
        userData.setValue(UiWeakObject(pChild));
        pItem->setData(0, Qt::UserRole, userData);
        pParentItem->addChild(pItem);

        // 添加子项
        this->addItem(pChild.get(), pItem);
    }
}

void SupportParamEdit::updateSupportStruct()
{
    // 清空支吊架结构树
    ui.treeWidgetSupportStruct->clear();
    this->initStructTree();

    // 获取支吊架节点
    auto    pSupport =   _pSupport.lock();
    if (pSupport == nullptr)
    {
        WD_ERROR_T("ErrorSupportParamEdit", "Support is null!");
        return ;
    }
    auto    pEqui   =   pSupport->node();
    if (pEqui == nullptr)
    {
        WD_ERROR_T("ErrorSupportParamEdit", "Support's equi is null!");
        return ;
    }

    // 添加顶层节点
    for (size_t i = 0; i < pEqui->childCount(); ++i)
    {
        auto pChild = pEqui->childAt(i);
        if (pChild == nullptr)
            continue;

        if (!this->enableSupportParamRef(pChild.get()))
            continue;

        // 添加项
        auto        pItem   =   new QTreeWidgetItem();
        pItem->setText(0, QString::fromUtf8(pChild->name().c_str()));
        QVariant    userData;
        userData.setValue(UiWeakObject(pChild));
        pItem->setData(0, Qt::UserRole, userData);
        ui.treeWidgetSupportStruct->addTopLevelItem(pItem);

        // 添加子项
        this->addItem(pChild.get(), pItem);
    }

    // 默认选中第一个item
    auto pFirstItem = ui.treeWidgetSupportStruct->topLevelItem(0);
    if (pFirstItem != nullptr)
    {
        ui.treeWidgetSupportStruct->setCurrentItem(pFirstItem);
    }
}
void stdPrisPropertyToGroup(WD::WDPropertyGroup& group, WD::WDNode& node)
{
    if (node.isAnyOfType("BOX", "NBOX"))
    {
        group.addPropertyFloat("Xlength", node.getAttribute("Xlength").toDouble());
        group.addPropertyFloat("Ylength", node.getAttribute("Ylength").toDouble());
        group.addPropertyFloat("Zlength", node.getAttribute("Zlength").toDouble());
    }
    else if (node.isAnyOfType("CYLI", "NCYL"))
    {
        group.addPropertyFloat("Diameter", node.getAttribute("Diameter").toDouble());
        group.addPropertyFloat("Height", node.getAttribute("Height").toDouble());
    }
    else if (node.isAnyOfType("CONE", "NCON"))
    {
        group.addPropertyFloat("Dtop", node.getAttribute("Dtop").toDouble());
        group.addPropertyFloat("Dbottom", node.getAttribute("Dbottom").toDouble());
        group.addPropertyFloat("Height", node.getAttribute("Height").toDouble());
    }
    else if (node.isAnyOfType("SNOU", "NSNO"))
    {
        group.addPropertyFloat("Dtop", node.getAttribute("Dtop").toDouble());
        group.addPropertyFloat("Dbottom", node.getAttribute("Dbottom").toDouble());
        group.addPropertyFloat("Height", node.getAttribute("Height").toDouble());
        group.addPropertyFloat("Xoffset", node.getAttribute("Xoffset").toDouble());
        group.addPropertyFloat("Yoffset", node.getAttribute("Yoffset").toDouble());
    }
    else if (node.isAnyOfType("PYRA", "NPYR"))
    {
        group.addPropertyFloat("Xtop", node.getAttribute("Xtop").toDouble());
        group.addPropertyFloat("Ytop", node.getAttribute("Ytop").toDouble());
        group.addPropertyFloat("Xbottom", node.getAttribute("Xbottom").toDouble());
        group.addPropertyFloat("Ybottom", node.getAttribute("Ybottom").toDouble());
        group.addPropertyFloat("Height", node.getAttribute("Height").toDouble());
        group.addPropertyFloat("Xoffset", node.getAttribute("Xoffset").toDouble());
        group.addPropertyFloat("Yoffset", node.getAttribute("Yoffset").toDouble());
    }
    else if (node.isAnyOfType("CTOR", "NCTO"))
    {
        group.addPropertyFloat("Rinside", node.getAttribute("Rinside").toDouble());
        group.addPropertyFloat("Routside", node.getAttribute("Routside").toDouble());
        group.addPropertyFloat("Angle", node.getAttribute("Angle").toDouble());
    }
    else if (node.isAnyOfType("RTOR", "NRTO"))
    {
        group.addPropertyFloat("Rinside", node.getAttribute("Rinside").toDouble());
        group.addPropertyFloat("Routside", node.getAttribute("Routside").toDouble());
        group.addPropertyFloat("Height", node.getAttribute("Height").toDouble());
        group.addPropertyFloat("Angle", node.getAttribute("Angle").toDouble());
    }
    else if (node.isAnyOfType("DISH", "NDIS"))
    {
        group.addPropertyFloat("Diameter", node.getAttribute("Diameter").toDouble());
        group.addPropertyFloat("Radius", node.getAttribute("Radius").toDouble());
        group.addPropertyFloat("Height", node.getAttribute("Height").toDouble());
    }
    else if (node.isAnyOfType("SLCY", "NSLC"))
    {
        group.addPropertyFloat("Diameter", node.getAttribute("Diameter").toDouble());
        group.addPropertyFloat("Height", node.getAttribute("Height").toDouble());
        group.addPropertyFloat("Xtshear", node.getAttribute("Xtshear").toDouble());
        group.addPropertyFloat("Ytshear", node.getAttribute("Ytshear").toDouble());
        group.addPropertyFloat("Xbshear", node.getAttribute("Xbshear").toDouble());
        group.addPropertyFloat("Ybshear", node.getAttribute("Ybshear").toDouble());
    }
    else if (node.isAnyOfType("SPHE", "NSPH"))
    {
        group.addPropertyFloat("Diameter", node.getAttribute("Diameter").toDouble());
    }
    else if (node.isAnyOfType("ELPS", "NELP"))
    {
        group.addPropertyFloat("xDiameter", node.getAttribute("xDiameter").toDouble());
        group.addPropertyFloat("yDiameter", node.getAttribute("yDiameter").toDouble());
        group.addPropertyFloat("zDiameter", node.getAttribute("zDiameter").toDouble());
    }
}
void SupportParamEdit::updateStructParams(WD::WDNode::SharedPtr pNode)
{
    // 清空结构参数表
    ui.tableWidgetStructParams->setRowCount(0);
    this->initStructParamTable();

    auto pSupport = _pSupport.lock();
    if (pSupport == nullptr)
    {
        WD_ERROR_T("ErrorSupportParamEdit", "Support is null!");
        return ;
    }
    if (pNode == nullptr)
        return ;

    // 相对支吊架头坐标的位置
    auto        posName =   QString::fromUtf8(WD::WDTs("SupportParamEdit", "Position").data());
    WD::Vec3    pos     =   pNode->globalTranslation();
    QString posX;
    QString posY;
    QString posZ;
    auto pTRS = dynamic_cast<WD::WDSupportTRSParam*>(pSupport->queryTRSParam(*pNode.get()).get());
    if (pTRS != nullptr)
    {
        posX = QString::fromUtf8(pTRS->posExpressionX().c_str());
        posY = QString::fromUtf8(pTRS->posExpressionY().c_str());
        posZ = QString::fromUtf8(pTRS->posExpressionZ().c_str());
    }
    auto row = ui.tableWidgetStructParams->rowCount();
    ui.tableWidgetStructParams->insertRow(row);
    auto pXNameItem = new QTableWidgetItem(posName + ".x");
    pXNameItem->setFlags(pXNameItem->flags() & ~Qt::ItemIsEditable);
    ui.tableWidgetStructParams->setItem(row, 0, pXNameItem);
    ui.tableWidgetStructParams->setItem(row, 1, new QTableWidgetItem(QString::fromUtf8(WD::ToString(pos.x).c_str())));
    ui.tableWidgetStructParams->setItem(row, 2, new QTableWidgetItem(posX));
    row = ui.tableWidgetStructParams->rowCount();
    ui.tableWidgetStructParams->insertRow(row);
    auto pYNameItem = new QTableWidgetItem(posName + ".y");
    pYNameItem->setFlags(pYNameItem->flags() & ~Qt::ItemIsEditable);
    ui.tableWidgetStructParams->setItem(row, 0, pYNameItem);
    ui.tableWidgetStructParams->setItem(row, 1, new QTableWidgetItem(QString::fromUtf8(WD::ToString(pos.y).c_str())));
    ui.tableWidgetStructParams->setItem(row, 2, new QTableWidgetItem(posY));
    row = ui.tableWidgetStructParams->rowCount();
    ui.tableWidgetStructParams->insertRow(row);
    auto pZNameItem = new QTableWidgetItem(posName + ".z");
    pZNameItem->setFlags(pZNameItem->flags() & ~Qt::ItemIsEditable);
    ui.tableWidgetStructParams->setItem(row, 0, pZNameItem);
    ui.tableWidgetStructParams->setItem(row, 1, new QTableWidgetItem(QString::fromUtf8(WD::ToString(pos.z).c_str())));
    ui.tableWidgetStructParams->setItem(row, 2, new QTableWidgetItem(posZ));

    //!TODO: 朝向
    
    // 参数属性组
    WD::WDPropertyGroup paramGroup;
    if (WD::WDBMDEquiUtils::IsEquiPrimitive(*pNode))
    {
        WD::WDPropertyGroup group;
        stdPrisPropertyToGroup(group, *pNode);
        paramGroup.copy(&group);
    }
    else if (pNode->isType("SCTN"))
    {
        WD::WDPropertyGroup group;
        this->sctnPropertyToGroup(group, *pNode);
        paramGroup.copy(&group);
    }
    else
    {
        auto pHeight = pNode->getAttribute("Height").data<double>();
        if (pHeight != nullptr)
        {
            paramGroup.addPropertyDouble("Height", *pHeight);
        }
    }
    auto&   desiMgr =   WD::Core().getBMDesign();
    auto&   pPtys   =   paramGroup.propertys();
    for (int i = 0; i < pPtys.size(); ++i)
    {
        auto&   pPty        =   pPtys.at((size_t)i);
        if (pPty == nullptr)
            continue;
        auto&   ptyName     =   pPty->srcName();
        // 针对DVec3的数据单独处理
        if (pPty->type() == WD::WDPropertyDataType::PDT_DVec3)
        {
            auto pTPty = pPty->toPtr<WD::WDPropertyDVec3>();
            if (pTPty != nullptr)
            {
                auto    sName       =   QString::fromUtf8(desiMgr.trA(ptyName).c_str());
                auto&   sValue      =   pTPty->getValue();

                std::string sExtrX = ".x";
                QString sExprX;
                auto pShapeX = dynamic_cast<WD::WDSupportShapeParam*>(pSupport->queryShapeParam(*pNode.get(), ptyName, sExtrX).get());
                if (pShapeX != nullptr)
                    sExprX = QString::fromUtf8(pShapeX->expression().c_str());
                auto row1 = ui.tableWidgetStructParams->rowCount();
                ui.tableWidgetStructParams->insertRow(row1);
                auto pNameItemX = new QTableWidgetItem(sName + QString::fromUtf8(sExtrX.c_str()));
                // item项设置参数名称
                pNameItemX->setData(Qt::UserRole, QString::fromUtf8(ptyName.c_str()));
                pNameItemX->setData(Qt::UserRole + 1, QString::fromUtf8(sExtrX.c_str()));
                pNameItemX->setFlags(pNameItemX->flags() & ~Qt::ItemIsEditable);
                ui.tableWidgetStructParams->setItem(row1, 0, pNameItemX);
                ui.tableWidgetStructParams->setItem(row1, 1, new QTableWidgetItem(QString::fromUtf8(WD::ToString(sValue.x).c_str())));
                ui.tableWidgetStructParams->setItem(row1, 2, new QTableWidgetItem(sExprX));

                std::string sExtrY = ".y";
                QString sExprY;
                auto pShapeY = dynamic_cast<WD::WDSupportShapeParam*>(pSupport->queryShapeParam(*pNode.get(), ptyName, sExtrY).get());
                if (pShapeY != nullptr)
                    sExprY = QString::fromUtf8(pShapeY->expression().c_str());
                row1 = ui.tableWidgetStructParams->rowCount();
                ui.tableWidgetStructParams->insertRow(row1);
                auto pNameItemY = new QTableWidgetItem(sName + QString::fromUtf8(sExtrY.c_str()));
                // item项设置参数名称
                pNameItemY->setData(Qt::UserRole, QString::fromUtf8(ptyName.c_str()));
                pNameItemY->setData(Qt::UserRole + 1, QString::fromUtf8(sExtrY.c_str()));
                pNameItemY->setFlags(pNameItemY->flags() & ~Qt::ItemIsEditable);
                ui.tableWidgetStructParams->setItem(row1, 0, pNameItemY);
                ui.tableWidgetStructParams->setItem(row1, 1, new QTableWidgetItem(QString::fromUtf8(WD::ToString(sValue.y).c_str())));
                ui.tableWidgetStructParams->setItem(row1, 2, new QTableWidgetItem(sExprY));

                std::string sExtrZ = ".z";
                QString sExprZ;
                auto pShapeZ = dynamic_cast<WD::WDSupportShapeParam*>(pSupport->queryShapeParam(*pNode.get(), ptyName, sExtrZ).get());
                if (pShapeZ != nullptr)
                    sExprZ = QString::fromUtf8(pShapeZ->expression().c_str());
                row1 = ui.tableWidgetStructParams->rowCount();
                ui.tableWidgetStructParams->insertRow(row1);
                auto pNameItemZ = new QTableWidgetItem(sName + QString::fromUtf8(sExtrZ.c_str()));
                // item项设置参数名称
                pNameItemZ->setData(Qt::UserRole, QString::fromUtf8(ptyName.c_str()));
                pNameItemZ->setData(Qt::UserRole + 1, QString::fromUtf8(sExtrZ.c_str()));
                pNameItemZ->setFlags(pNameItemZ->flags() & ~Qt::ItemIsEditable);
                ui.tableWidgetStructParams->setItem(row1, 0, pNameItemZ);
                ui.tableWidgetStructParams->setItem(row1, 1, new QTableWidgetItem(QString::fromUtf8(WD::ToString(sValue.z).c_str())));
                ui.tableWidgetStructParams->setItem(row1, 2, new QTableWidgetItem(sExprZ));
            }
        }
        else
        {
            auto    sName       =   QString::fromUtf8(desiMgr.trA(ptyName).c_str());
            auto    sDefault    =   QString::fromUtf8(pPty->valueToString().c_str());
            QString sExpr;
            auto pShape = dynamic_cast<WD::WDSupportShapeParam*>(pSupport->queryShapeParam(*pNode.get(), ptyName).get());
            if (pShape != nullptr)
                sExpr = QString::fromUtf8(pShape->expression().c_str());
            auto row1 = ui.tableWidgetStructParams->rowCount();
            ui.tableWidgetStructParams->insertRow(row1);
            auto pNameItem = new QTableWidgetItem(sName);
            // item项设置参数名称
            pNameItem->setData(Qt::UserRole, QString::fromUtf8(ptyName.c_str()));
            pNameItem->setFlags(pNameItem->flags() & ~Qt::ItemIsEditable);
            ui.tableWidgetStructParams->setItem(row1, 0, pNameItem);
            ui.tableWidgetStructParams->setItem(row1, 1, new QTableWidgetItem(sDefault));
            ui.tableWidgetStructParams->setItem(row1, 2, new QTableWidgetItem(sExpr));
        }
    }
}
void SupportParamEdit::updateSupportFactors()
{
    // 清空参数因子表
    ui.tableWidgetParamFactors->setRowCount(0);
    this->initParamFactorTable();

    // 获取当前支吊架模板
    auto pSupport = _pSupport.lock();
    if (pSupport == nullptr)
    {
        WD_ERROR_T("ErrorSupportParamEdit", "Support is null!");
        return ;
    }

    // 填入表格信息
    auto&   factors =   pSupport->factors();
    auto    row     =   factors.size();
    ui.tableWidgetParamFactors->setRowCount((int)row);
    for (size_t i = 0; i < row; ++i)
    {
        auto& factor = factors.at(i);

        // 名称
        auto    pName   =   new QTableWidgetItem(QString::fromUtf8(factor.name().c_str()));
        ui.tableWidgetParamFactors->setItem((int)i, 0, pName);
        // 描述
        auto    pDesc   =   new QTableWidgetItem(QString::fromUtf8(factor.description().c_str()));
        ui.tableWidgetParamFactors->setItem((int)i, 1, pDesc);
        // 值
        auto    pValue  =   new QTableWidgetItem(QString::fromUtf8(factor.value().c_str()));
        ui.tableWidgetParamFactors->setItem((int)i, 2, pValue);
    }
}
void SupportParamEdit::updateSpecList()
{
    // 收集spec
    std::set<std::string> specs;
    for (auto& pSupport : _supportMgr.supports())
    {
        if (pSupport == nullptr)
            continue;

        specs.insert(pSupport->spec());
    }

    // 更新spec下拉项
    ui.comboBoxSpec->clear();
    for (auto& spec : specs)
    {
        if (spec.empty())
            continue;
        auto text = QString::fromUtf8(spec.c_str());
        ui.comboBoxSpec->addItem(text);
    }

    // 设置当前spec
    auto pSupport = _pSupport.lock();
    if (pSupport != nullptr)
    {
        ui.comboBoxSpec->setCurrentText(QString::fromUtf8(pSupport->spec().c_str()));
    }
}
void SupportParamEdit::updateGTypeList()
{
    // 收集gType
    std::set<std::string> gTypes;
    for (auto& pSupport : _supportMgr.supports())
    {
        if (pSupport == nullptr)
            continue;

        gTypes.insert(pSupport->gType());
    }

    // 更新gType下拉项
    ui.comboBoxGType->clear();
    for (auto& gType : gTypes)
    {
        if (gType.empty())
            continue;
        auto text = QString::fromUtf8(gType.c_str());
        ui.comboBoxGType->addItem(text);
    }

    // 设置当前gType
    auto pSupport = _pSupport.lock();
    if (pSupport != nullptr)
    {
        ui.comboBoxGType->setCurrentText(QString::fromUtf8(pSupport->gType().c_str()));
    }
}
void SupportParamEdit::updateDesc()
{
    auto pSupport = _pSupport.lock();
    if (pSupport == nullptr)
    {
        WD_ERROR_T("ErrorSupportParamEdit", "Support is null!");
        return ;
    }

    ui.lineEditMType->setText(QString::fromUtf8(pSupport->mType().c_str()));
}
void SupportParamEdit::updateLegend()
{
    auto pSupport = _pSupport.lock();
    if (pSupport == nullptr)
    {
        WD_ERROR_T("ErrorSupportParamEdit", "Support is null!");
        return ;
    }
    // 显示图例文件名称(不含路径)
    std::string legend = pSupport->legend();
    ui.lineEditPath->setText(QString::fromUtf8(legend.c_str()));

    // 显示图例
    QPixmap pixmap(QString::fromUtf8((WD::WDSupport::LegendPath() + legend).c_str()));
    pixmap = pixmap.scaledToWidth(ui.labelLegend->width(), Qt::SmoothTransformation);
    ui.labelLegend->setPixmap(pixmap);
    ui.labelLegend->setAlignment(Qt::AlignCenter);
}


void SupportParamEdit::sctnPropertyToGroup(WD::WDPropertyGroup& group, WD::WDNode& node)
{
    if (node.isType("SCTN"))
    {
        const auto posStart = node.getAttribute("Posstart").toDVec3();
        const auto posEnd = node.getAttribute("Posend").toDVec3();

        group.addPropertyDVec3("Posstart WRT World", posStart);
        group.addPropertyDVec3("Posend WRT World", posEnd);
    }
}

void SupportParamEdit::translate()
{
    this->setWindowTitle(QString::fromUtf8(WD::WDTs("SupportParamEdit", "SupportParamEdit").data()));
    
    ui.labelName->setText(QString::fromUtf8(WD::WDTs("SupportParamEdit", "Name").data()));
    ui.labelSpec->setText(QString::fromUtf8(WD::WDTs("SupportParamEdit", "Spec").data()));
    ui.labelGType->setText(QString::fromUtf8(WD::WDTs("SupportParamEdit", "GType").data()));
    ui.labelMType->setText(QString::fromUtf8(WD::WDTs("SupportParamEdit", "MType").data()));
    ui.groupBoxSupportStruct->setTitle(QString::fromUtf8(WD::WDTs("SupportParamEdit", "SupportStruct").data()));
    ui.groupBoxStructParams->setTitle(QString::fromUtf8(WD::WDTs("SupportParamEdit", "StructParams").data()));
    ui.groupBoxParamFactors->setTitle(QString::fromUtf8(WD::WDTs("SupportParamEdit", "ParamFactors").data()));
    ui.pushButtoAddFactor->setText(QString::fromUtf8(WD::WDTs("SupportParamEdit", "Add").data()));
    ui.pushButtonRemoveFactor->setText(QString::fromUtf8(WD::WDTs("SupportParamEdit", "Remove").data()));
    ui.pushButtonUpdateParamFactor->setText(QString::fromUtf8(WD::WDTs("SupportParamEdit", "UpdateParamFactor").data()));
    ui.pushButtonUpdateStructParams->setText(QString::fromUtf8(WD::WDTs("SupportParamEdit", "UpdateStructParams").data()));
    ui.groupBoxLegend->setTitle(QString::fromUtf8(WD::WDTs("SupportParamEdit", "Legend").data()));
    ui.labelPath->setText(QString::fromUtf8(WD::WDTs("SupportParamEdit", "Path").data()));
    ui.pushButtonOk->setText(QString::fromUtf8(WD::WDTs("SupportParamEdit", "Ok").data()));
}