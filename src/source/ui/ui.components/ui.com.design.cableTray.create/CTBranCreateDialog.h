#pragma once

#include <QDialog>
#include <QStackedWidget>

#include "ui_CTBranCreateDialog.h"

#include "CTBranCreateDialog/CTBranExplCreateWidget.h"
#include "public/specListWidget.h"

#include "core/WDCore.h"
#include "node/WDNode.h"
#include "../../ui.commonLibrary//ui.commonLib.custom/UiNodeNameHelpter.h"

class CTBranCreateDialog : public QDialog
{
    Q_OBJECT

public:
    // 头尾连接形式
    enum HeadTail
    {
        None = 0,
        // 绝对坐标
        Explict
    };
    Q_ENUM(HeadTail)

public:
    CTBranCreateDialog(WD::WDCore& core, QWidget *parent = Q_NULLPTR);
    ~CTBranCreateDialog();

protected:
    virtual void showEvent(QShowEvent* evt) override;
    virtual void hideEvent(QHideEvent* evt) override;

private slots:
    /**
    * @brief 确认绑定事件响应
    */
    void slotOkButtonClicked();
    /**
    * @brief 取消按钮事件响应
    */
    void slotCancelButtonClicked();
    /**
    * @brief 头尾连接形式 切换 事件响应
    */
    void slotheadTailComIndexChanged(int index);
    /**
    * @brief 是否开启分支尾设置通知响应
    */
    void slotTailableCheckBoxChanged(int state);

    /**
    * @brief 专业等级节点 切换 通知响应
    * @param SPEC 切换后 专业等级节点
    */
    void slotSpecChanged(WD::WDNode::SharedPtr SPEC);
    /**
    * @brief 点拾取分支头尾坐标通知响应
    */
    void slotPickPoint();

    //模型树当前节点改变
    void onCurrentNodeChanged(WD::WDNode::SharedPtr pCurrNode
        , WD::WDNode::SharedPtr pPrevNode
        , WD::WDNodeTree& sender);
    
private:
    /**
    * @brief 更新分支尾窗口显隐
    */
    void                    updateTailWidgetVisible();

    /**
    * @brief 设置当前选中等级
    */
    void                    setCurrentSpec();
    // 根据模型树当前选中节点获取分支可挂载的父节点
    WD::WDNode::SharedPtr   getParent() const;

private:
    /**
    * @brief 获取头坐标
    */
    WD::DVec3   hPos();
    /**
    * @brief 获取头方向
    */
    WD::DVec3   hDir();
    /**
    * @brief 获取头管径
    */
    std::string hBore();
    /**
    * @brief 获取头连接方式
    */
    std::string hConnect();

    /**
    * @brief 获取尾坐标
    */
    WD::DVec3   tPos();
    /**
    * @brief 获取尾方向
    */
    WD::DVec3   tDir();
    /**
    * @brief 获取尾管径
    */
    std::string tBore();
    /**
    * @brief 获取尾连接方式
    */
    std::string tConnect();
    
private:
    /**
    * @brief 准备桥架分支数据
    */
    bool prepareCtBranData(WD::WDNode& ctBran);
    // 创建分支节点
    void createCTBranch();
    /**
     * @brief 自动生成父名/Bnum的默认名称
    */
    void genNewName();
    // 界面翻译
    void retranslateUi();

private:
    Ui::CTBranCreateDialog  ui;
    WD::WDCore&             _core;
    
    // 专业等级节点窗口
    SpecListWidget*         _pSpecListWidget;

    // 头尾绝对坐标窗口
    CTBranExplCreateWidget* _pHeadExplictWidget;
    CTBranExplCreateWidget* _pTailExplictWidget;
    QStackedWidget*         _pStackedWids;

    // 头尾连接类型
    HeadTail                _headTail;

    // 是否开启分支尾设置
    bool                    _tailable;

    //窗口高度
    int                     _mWindowHeight;

    // 节点名称助手
    UiNodeNameHelpter           _nameHelpter;
};
