#pragma once

#include    <QObject>
#include     "../../wizDesignerApp/UiInterface/UiInterface.h"
#include    "ColorManagementDialog.h"

class UIComPipeColorManagement
    : public QObject
    , public IUiComponent
{
    Q_OBJECT

public:
    UIComPipeColorManagement(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject *parent = nullptr);
    ~UIComPipeColorManagement();

public:
    /**
    *   @brief 通知事件
    *   @param pNotice 事件对象
    */
    virtual void onNotice(UiNotice* pNotice) override;
private:
    ColorManagementDialog* _pPipeColorManagementDlg;
};
