<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>PositionRelativelyDialog</class>
 <widget class="QWidget" name="PositionRelativelyDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>364</width>
    <height>253</height>
   </rect>
  </property>
  <property name="focusPolicy">
   <enum>Qt::StrongFocus</enum>
  </property>
  <property name="windowTitle">
   <string>Pisiton relatively</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QGridLayout" name="gridLayout_2">
     <item row="0" column="0">
      <widget class="QLabel" name="labelCurrentNode">
       <property name="text">
        <string>Current node</string>
       </property>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="QLineEdit" name="lineEditCE">
       <property name="focusPolicy">
        <enum>Qt::StrongFocus</enum>
       </property>
      </widget>
     </item>
     <item row="2" column="0">
      <widget class="QLabel" name="labelWRT">
       <property name="text">
        <string>WRT</string>
       </property>
      </widget>
     </item>
     <item row="2" column="1">
      <widget class="QLineEdit" name="lineEditWRT">
       <property name="focusPolicy">
        <enum>Qt::StrongFocus</enum>
       </property>
      </widget>
     </item>
     <item row="2" column="2">
      <widget class="QCheckBox" name="checkBoxWRT">
       <property name="focusPolicy">
        <enum>Qt::StrongFocus</enum>
       </property>
       <property name="text">
        <string/>
       </property>
      </widget>
     </item>
     <item row="1" column="0" colspan="3">
      <widget class="QGroupBox" name="groupBoxOffset">
       <property name="title">
        <string>Offset</string>
       </property>
       <property name="checkable">
        <bool>false</bool>
       </property>
       <property name="checked">
        <bool>false</bool>
       </property>
       <layout class="QGridLayout" name="gridLayout" columnstretch="0,1,0">
        <item row="1" column="0">
         <widget class="QLabel" name="labelPositionX">
          <property name="text">
           <string>X</string>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QDoubleSpinBox" name="doubleSpinBoxPositionX">
          <property name="minimum">
           <double>-9999999999.000000000000000</double>
          </property>
          <property name="maximum">
           <double>9999999999.000000000000000</double>
          </property>
         </widget>
        </item>
        <item row="1" column="2">
         <widget class="QCheckBox" name="checkBoxPositionX">
          <property name="focusPolicy">
           <enum>Qt::StrongFocus</enum>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item row="2" column="0">
         <widget class="QLabel" name="labelPositionY">
          <property name="text">
           <string>Y</string>
          </property>
         </widget>
        </item>
        <item row="2" column="1">
         <widget class="QDoubleSpinBox" name="doubleSpinBoxPositionY">
          <property name="minimum">
           <double>-9999999999.000000000000000</double>
          </property>
          <property name="maximum">
           <double>9999999999.000000000000000</double>
          </property>
         </widget>
        </item>
        <item row="2" column="2">
         <widget class="QCheckBox" name="checkBoxPositionY">
          <property name="focusPolicy">
           <enum>Qt::StrongFocus</enum>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item row="3" column="0">
         <widget class="QLabel" name="labelPositionZ">
          <property name="text">
           <string>Z</string>
          </property>
         </widget>
        </item>
        <item row="3" column="1">
         <widget class="QDoubleSpinBox" name="doubleSpinBoxPositionZ">
          <property name="minimum">
           <double>-9999999999.000000000000000</double>
          </property>
          <property name="maximum">
           <double>9999999999.000000000000000</double>
          </property>
         </widget>
        </item>
        <item row="3" column="2">
         <widget class="QCheckBox" name="checkBoxPositionZ">
          <property name="focusPolicy">
           <enum>Qt::StrongFocus</enum>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item row="0" column="0" colspan="3">
         <widget class="QCheckBox" name="checkBoxCapture">
          <property name="focusPolicy">
           <enum>Qt::StrongFocus</enum>
          </property>
          <property name="layoutDirection">
           <enum>Qt::RightToLeft</enum>
          </property>
          <property name="text">
           <string>Capture</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item row="0" column="2">
      <layout class="QHBoxLayout" name="horizontalLayout_2">
       <property name="spacing">
        <number>0</number>
       </property>
       <item>
        <widget class="QPushButton" name="pushButtonRefresh">
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="focusPolicy">
          <enum>Qt::StrongFocus</enum>
         </property>
         <property name="text">
          <string>CE</string>
         </property>
         <property name="autoDefault">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="pushButtonMenu">
         <property name="maximumSize">
          <size>
           <width>20</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="focusPolicy">
          <enum>Qt::StrongFocus</enum>
         </property>
         <property name="text">
          <string>PushButton</string>
         </property>
         <property name="autoDefault">
          <bool>false</bool>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonOk">
       <property name="focusPolicy">
        <enum>Qt::StrongFocus</enum>
       </property>
       <property name="text">
        <string>Ok</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonCancel">
       <property name="focusPolicy">
        <enum>Qt::StrongFocus</enum>
       </property>
       <property name="text">
        <string>Cancel</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <tabstops>
  <tabstop>lineEditCE</tabstop>
  <tabstop>pushButtonRefresh</tabstop>
  <tabstop>pushButtonMenu</tabstop>
  <tabstop>checkBoxCapture</tabstop>
  <tabstop>doubleSpinBoxPositionX</tabstop>
  <tabstop>checkBoxPositionX</tabstop>
  <tabstop>doubleSpinBoxPositionY</tabstop>
  <tabstop>checkBoxPositionY</tabstop>
  <tabstop>doubleSpinBoxPositionZ</tabstop>
  <tabstop>checkBoxPositionZ</tabstop>
  <tabstop>lineEditWRT</tabstop>
  <tabstop>checkBoxWRT</tabstop>
  <tabstop>pushButtonOk</tabstop>
  <tabstop>pushButtonCancel</tabstop>
 </tabstops>
 <resources/>
 <connections/>
</ui>
