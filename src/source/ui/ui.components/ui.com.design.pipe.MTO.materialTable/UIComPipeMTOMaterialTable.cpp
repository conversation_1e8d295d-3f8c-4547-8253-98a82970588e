#include "UIComPipeMTOMaterialTable.h"

UIComPipeMTOMaterialTable::UIComPipeMTOMaterialTable(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject *parent)
    : QObject(parent)
    , IUiComponent(mainWindow, attrs)
{
    _pMTOMaterialTableDialog = new WD::MTOMaterialTableDialog(mWindow().core(), mWindow().widget());
}


UIComPipeMTOMaterialTable::~UIComPipeMTOMaterialTable()
{
    if (_pMTOMaterialTableDialog != nullptr)
    {
        delete _pMTOMaterialTableDialog;
        _pMTOMaterialTableDialog = nullptr;
    }
}

void UIComPipeMTOMaterialTable::onNotice(UiNotice * pNotice)
{
    int nType = pNotice->type();
    switch (nType)
    {
    case UiNoticeType::UNT_Action:
        {
            UiActionNotice* pActionNotice = static_cast<UiActionNotice*>(pNotice);

            if (pActionNotice->action().is("action.design.pipe.MTO.materialTable"))
            {
                if (_pMTOMaterialTableDialog->isHidden())
                    _pMTOMaterialTableDialog->show();
                else
                    _pMTOMaterialTableDialog->activateWindow();
            }
        }
        break;
    default:
        break;
    }
}