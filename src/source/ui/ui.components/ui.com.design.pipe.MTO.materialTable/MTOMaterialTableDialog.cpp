#include "MTOMaterialTableDialog.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "core/message/WDMessage.h"
#include "core/WDCore.h"
#include "core/nodeTree/WDNodeTree.h"
#include "../../ui.commonLibrary/ui.commonLib.weakObject/WeakObject.h"
#include <qfiledialog.h>
#include "../../ui.commonLibrary/ui.commonLib.custom/UiCommon.h"

WD_NAMESPACE_USE
/**
 * @brief 保存数据为CSV类型文件
 * @param filePath 
 * @param mtoData 
 * @return 
*/
static bool SaveCSVData(const QString filePath, const std::vector<StringVector>& mtoData)
{
    if (mtoData.empty())
        return false;
    auto colCnt = mtoData.front().size();
    StringVector colDatas;
    colDatas.reserve(colCnt);
    StringVector allDatas;
    // 特殊字符,在这种字符前面加一个双引号
    const std::string specialChars = "\"";
    for (auto& eachRow : mtoData)
    {
        assert(eachRow.size() == colCnt);
        colDatas.clear();
        for (auto& eachData : eachRow)
        {
            colDatas.push_back(eachData);
            auto& text = colDatas.back();
            for (auto& specialChar : specialChars)
            {
                if (text.find(specialChar) != std::string::npos)
                {
                    for (int i = 0; i < text.size(); ++i)
                    {
                        if (text[i] == specialChar)
                        {
                            text.insert(text.begin() + i, '\"');
                            ++i;
                        }
                    }
                }
            }
            text.insert(text.begin(), '\"');
            text.push_back('\"');
        }
        allDatas.push_back(StringConcat(colDatas, ","));
    }
    std::string text = StringConcat(allDatas, "\n");
    QFile file(filePath);
    if (file.open(QIODevice::WriteOnly | QIODevice::Truncate))
    {
        file.write(QString(text.c_str()).toLocal8Bit());
        file.close();
        return true;
    }
    return false;
}

MTOMaterialTableDialog::MTOMaterialTableDialog(WD::WDCore& core, QWidget *parent)
    : _core(core), QDialog(parent), _helper(core)
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));

    _flags[MTOMaterialTableHelper::ST_BRAN] = {MaterialFlag::MF_UnitName,
        MaterialFlag::MF_PipeSpec,
        MaterialFlag::MF_ComsType,
        MaterialFlag::MF_ScomCode,
        MaterialFlag::MF_MainBore,
        MaterialFlag::MF_SubBore,
        MaterialFlag::MF_Count,
        MaterialFlag::MF_BoltDiameter,
        MaterialFlag::MF_BoltLength,
        MaterialFlag::MF_InsulationSpec,
        MaterialFlag::MF_UniqueCode,
        MaterialFlag::MF_DetailedDesc};

    _flags[MTOMaterialTableHelper::ST_PIPE] = {MaterialFlag::MF_UnitName,
        MaterialFlag::MF_ComsType,
        MaterialFlag::MF_Code,
        MaterialFlag::MF_ScomCodeWXHH,
        MaterialFlag::MF_MaterialCode,
        MaterialFlag::MF_DetailedDesc,
        MaterialFlag::MF_MaterialDesc,
        MaterialFlag::MF_Specifications,
        MaterialFlag::MF_Count,
        MaterialFlag::MF_Unit,
        MaterialFlag::MF_SingleWeight,
        MaterialFlag::MF_TotalWeight,};


    initDialog();
    this->retranslateUi();
    _helper.filterType.insert("ATTA");
    _helper.filterType.insert("INST");
    ui.listWidget->setSelectionMode(QAbstractItemView::SelectionMode::ExtendedSelection);

    QObject::connect(ui.comboBoxGenerateMethod
        , QOverload<int>::of(&QComboBox::currentIndexChanged)
        , this
        , &MTOMaterialTableDialog::slotComboBoxGenerateMethodCurrentIndexChanged);
    QObject::connect(ui.comboBoxGenerateTable
        , QOverload<int>::of(&QComboBox::currentIndexChanged)
        , this
        , &MTOMaterialTableDialog::slotComboBoxGenerateTableCurrentIndexChanged);

    QObject::connect(ui.pushButtonConfig
        , &QPushButton::clicked
        , this
        , &MTOMaterialTableDialog::slotPushButtonConfigClicked);
    QObject::connect(ui.pushButtonAddCurrent
        , &QPushButton::clicked
        , this
        , &MTOMaterialTableDialog::slotPushButtonAddCurrentClicked);
    QObject::connect(ui.pushButtonAddCurrentMember
        , &QPushButton::clicked
        , this
        , &MTOMaterialTableDialog::slotPushButtonAddCurrentMemberClicked);
    QObject::connect(ui.pushButtonRemoveChoosed
        , &QPushButton::clicked
        , this
        , &MTOMaterialTableDialog::slotPushButtonRemoveChoosedClicked);
    QObject::connect(ui.pushButtonRemoveAll
        , &QPushButton::clicked
        , this
        , &MTOMaterialTableDialog::slotPushButtonRemoveAllClicked);
    QObject::connect(ui.pushButtonGenerateMaterialTable
        , &QPushButton::clicked
        , this
        , &MTOMaterialTableDialog::slotPushButtonGenerateMaterialTableClicked);
    QObject::connect(ui.pushButtonATTAMaterialSummary
        , &QPushButton::clicked
        , this
        , &MTOMaterialTableDialog::slotPushButtonATTAMaterialSummaryClicked);

    QObject::connect(ui.toolButtonPath
        , &QToolButton::clicked
        , this
        , &MTOMaterialTableDialog::slotToolButtonPathClicked);
}
MTOMaterialTableDialog::~MTOMaterialTableDialog()
{

}

void MTOMaterialTableDialog::slotComboBoxGenerateMethodCurrentIndexChanged()
{
    updateDialog();
}
void MTOMaterialTableDialog::slotComboBoxGenerateTableCurrentIndexChanged()
{
    _helper.type = StatisticsType(ui.comboBoxGenerateTable->currentData().toInt());
}

void MTOMaterialTableDialog::slotPushButtonConfigClicked()
{
    WD_INFO_T("MTOMaterialTableDialog", "The function is under development");
    return;
}
void MTOMaterialTableDialog::slotPushButtonAddCurrentClicked()
{
    auto pCurrentNode = _core.nodeTree().currentNode();
    if (pCurrentNode == nullptr)
    {
        WD_WARN_T("MTOMaterialTableDialog", "Current node is Null!");
        return;
    }
    addNodeToList(pCurrentNode);
}
void MTOMaterialTableDialog::slotPushButtonAddCurrentMemberClicked()
{
    auto pCurrentNode = _core.nodeTree().currentNode();
    if (pCurrentNode == nullptr)
    {
        WD_WARN_T("MTOMaterialTableDialog", "Current node is Null!");
        return;
    }
    for (auto& pChild : pCurrentNode->children())
    {
        if (pChild == nullptr)
            continue;
        addNodeToList(pChild);
    }
}
void MTOMaterialTableDialog::slotPushButtonRemoveChoosedClicked()
{
    auto selects = ui.listWidget->selectedItems();
    if (selects.empty())
        return;
    if (WD_QUESTION_T("MTOMaterialTableDialog", "Sure remove choose node?") != 0)
        return;
    for (auto pItem : selects)
    {
        if (pItem == nullptr)
        {
            assert(false);
            continue;
        }
        ui.listWidget->removeItemWidget(pItem);
        delete pItem;
        pItem = nullptr;
    }

}
void MTOMaterialTableDialog::slotPushButtonRemoveAllClicked()
{
    if (ui.listWidget->count() == 0)
        return;
    if (WD_QUESTION_T("MTOMaterialTableDialog", "Sure remove all node?") != 0)
        return;
    ui.listWidget->clear();
}
void MTOMaterialTableDialog::slotPushButtonGenerateMaterialTableClicked()
{
    auto filePath = ui.lineEditPath->text();
    if (filePath.isEmpty())
    {
        WD_WARN_T("MTOMaterialTableDialog", "File path is empty!");
        return;
    }
    if (ui.listWidget->count() == 0)
    {
        WD_WARN_T("MTOMaterialTableDialog", "Table is empty!");
        return;
    }
    WDNode::Nodes nodes;
    for (int index = 0; index < ui.listWidget->count(); ++index)
    {
        auto pItem = ui.listWidget->item(index);
        if (pItem == nullptr)
        {
            assert(false);
            continue;
        }
        auto pNode = pItem->data(Qt::UserRole).value<UiWeakObject>().subObject<WDNode>();
        if (pNode == nullptr)
        {
            assert(false);
            continue;
        }
        nodes.push_back(pNode);
    }
    auto itr = _flags.find(_helper.type);
    if (itr == _flags.end())
    {
        WD_WARN_T("MTOMaterialTableDialog", "Table Generate Failed!");
        return;
    }
    _helper.flags = itr->second;
    // 处理材料数据
    switch (_helper.type)
    {
    case StatisticsType::ST_BRAN:
        handleBranData(_helper.exec(nodes));
        break;
    case StatisticsType::ST_PIPE:
        handlePipeData(_helper.exec(nodes));
        break;
    default:
        break;
    }

}
void MTOMaterialTableDialog::slotPushButtonATTAMaterialSummaryClicked()
{
    auto filePath = ui.lineEditPath->text();
    if (filePath.isEmpty())
    {
        WD_WARN_T("MTOMaterialTableDialog", "File path is empty!");
        return;
    }
    if (ui.listWidget->count() == 0)
    {
        WD_WARN_T("MTOMaterialTableDialog", "Table is empty!");
        return;
    }
    WDNode::Nodes nodes;
    for (int index = 0; index < ui.listWidget->count(); ++index)
    {
        auto pItem = ui.listWidget->item(index);
        if (pItem == nullptr)
        {
            assert(false);
            continue;
        }
        auto pNode = pItem->data(Qt::UserRole).value<UiWeakObject>().subObject<WDNode>();
        if (pNode == nullptr)
        {
            assert(false);
            continue;
        }
        nodes.push_back(pNode);
    }
    auto materialData = _helper.execATTA(nodes);
    if (SaveCSVData(ui.lineEditPath->text(), materialData))
        WD_INFO_T("MTOMaterialTableDialog", "Table Generate Success!");
    else
        WD_WARN_T("MTOMaterialTableDialog", "Table Generate Failed!");
}

void MTOMaterialTableDialog::slotToolButtonPathClicked()
{
    auto fileName = QFileDialog::getSaveFileName(this
        , QString::fromUtf8(WD::WDTs("MTOMaterialTableDialog", "SavePath").c_str())
        , ""
        , "*.csv");
    if (fileName.isEmpty())
        return;

    QString suffix(".csv");
    if (!fileName.endsWith(suffix, Qt::CaseInsensitive))
        fileName += suffix;

    ui.lineEditPath->setText(fileName);
}

void MTOMaterialTableDialog::showEvent(QShowEvent *)
{
}
void MTOMaterialTableDialog::hideEvent(QHideEvent *)
{
}

void MTOMaterialTableDialog::handleBranData(const MTOMaterialTableData& mtoData)
{
    std::vector<StringVector> allData;
    if (!mtoData.empty())
    {
        for (const auto& materialData : mtoData)
        {
            int index = 0;
            if (allData.size() <= index)
                allData.resize(materialData.second.size() + 1);

            allData[index].emplace_back(WDTs("MTOMaterialTableDialog", MTOMaterialTableHelper::FlagToStr(materialData.first, _helper.type)));

            for (auto& subData : materialData.second)
            {
                ++index;
                assert(allData.size() > index);
                if (allData.size() <= index)
                    allData.resize(index + 1);
                allData[index].emplace_back(subData);
            }
        }
    }
    if (!allData.empty() && SaveCSVData(ui.lineEditPath->text(), allData))
        WD_INFO_T("MTOMaterialTableDialog", "Table Generate Success!");
    else
        WD_WARN_T("MTOMaterialTableDialog", "Table Generate Failed!");
}

struct MaterialMergeKey
{
private:
    std::string _scomCode;
    std::string _specifications;
public:
    MaterialMergeKey(const std::string& scomCode, const std::string& specifications) : _scomCode(scomCode), _specifications(specifications)
    {

    }
public:
    bool operator<(const MaterialMergeKey& right) const
    {
        if ((_scomCode == right._scomCode) && (_specifications == right._specifications))
            return false;

        if (_scomCode < right._scomCode)
            return true;

        return (_scomCode == right._scomCode) && (_specifications < right._specifications);
    }
};
void MTOMaterialTableDialog::handlePipeData(const MTOMaterialTableData& mtoData)
{
    if (!mtoData.empty())
    {
        /// pipe的材料表分为pipemto和mto两个表
        std::vector<StringVector> allData;
        // 先生成 pipemto 表
        QFileInfo path(ui.lineEditPath->text());
        {
            auto checkPipeMtoFlag = [] (MaterialFlag flag) ->bool
            {
                // pipemto不包含元件编码和材料编码
                if (flag == MaterialFlag::MF_ScomCodeWXHH || flag == MaterialFlag::MF_MaterialCode)
                    return false;
                return true;
            };
            for (const auto& materialData : mtoData)
            {
                if (!checkPipeMtoFlag(materialData.first))
                    continue;

                int index = 0;
                if (allData.size() <= index)
                    allData.resize(materialData.second.size() + 1);
                allData[index].emplace_back(WDTs("MTOMaterialTableDialog", MTOMaterialTableHelper::FlagToStr(materialData.first, _helper.type)));

                for (auto& subData : materialData.second)
                {
                    ++index;
                    assert(allData.size() > index);
                    if (allData.size() <= index)
                        allData.resize(index + 1);
                    allData[index].emplace_back(subData);
                }
            }
        }
        char filePath[1024] ={ 0 };
        sprintf(filePath, "%s\\%s-pipemto.csv", path.path().toUtf8().data(), path.baseName().toUtf8().data());
        try
        {
            std::sort(allData.begin() + 1, allData.end(), [](const StringVector& left, const StringVector& right) -> bool
            {
                if (left.size() != right.size())
                    return left.size() < right.size();
                for (int i = 0; i < left.size(); ++i)
                {
                    if (left[i] != right[i])
                        return left[i] < right[i];
                }
                return false;
            });
        }
        catch (...)
        {
            assert(false);
        }
        if (allData.empty() || !SaveCSVData(QString::fromUtf8(filePath), allData))
        {
            WD_WARN_T("MTOMaterialTableDialog", "Table Generate Failed!");
            return;
        }
        // 再生成 mto 表 mto表是对pipemto内容的汇总,这里记录每种数据的下标,用于合并不同pipe的数据
        {
            auto checkMtoFlag = [] (MaterialFlag flag) ->bool
            {
                // 汇总表不需要名称和编码的数据
                if (flag == MaterialFlag::MF_UnitName || flag == MaterialFlag::MF_Code)
                    return false;
                return true;
            };

            allData.clear();
            // 先获取所有数据
            for (const auto& materialData : mtoData)
            {
                if (!checkMtoFlag(materialData.first))
                    continue;
                // 这里用元件编码作为唯一标识来合并数据
                auto itr = mtoData.find(MaterialFlag::MF_ScomCodeWXHH);
                if (itr == mtoData.end())
                    continue;

                int index = 0;
                if (allData.size() <= index)
                    allData.resize(materialData.second.size() + 1);
                allData[index].emplace_back(WDTs("MTOMaterialTableDialog", MTOMaterialTableHelper::FlagToStr(materialData.first, _helper.type)));

                for (auto& subData : materialData.second)
                {
                    ++index;
                    assert(allData.size() > index);
                    if (allData.size() <= index)
                        allData.resize(index + 1);
                    allData[index].emplace_back(subData);
                }
            }

            auto dataSize = mtoData.size();
            // 元件编码数据下标
            int scomCodeIndex = 0;
            for (const auto& materialData : mtoData)
            {
                if (!checkMtoFlag(materialData.first))
                    continue;
                if (materialData.first == MaterialFlag::MF_ScomCodeWXHH)
                    break;
                ++scomCodeIndex;
            }
            // 规格数据下标
            int specIndex = 0;
            for (const auto& materialData : mtoData)
            {
                if (!checkMtoFlag(materialData.first))
                    continue;
                if (materialData.first == MaterialFlag::MF_Specifications)
                    break;
                ++specIndex;
            }

            if (scomCodeIndex < dataSize && specIndex < dataSize)
            {
                // 汇总数据
                auto mergeData = [&mtoData, &checkMtoFlag] (StringVector& to, StringVector& from)
                {
                    if (to.size() != from.size())
                    {
                        assert(false);
                        return;
                    }
                    int dataIndex = 0;
                    for (auto& each : mtoData)
                    {
                        auto flag = each.first;
                        if (!checkMtoFlag(flag))
                            continue;
                        switch (flag)
                        {
                        case WD::MTOMaterialTableHelper::MF_Count:
                        case WD::MTOMaterialTableHelper::MF_BoltLength:
                        case WD::MTOMaterialTableHelper::MF_SingleWeight:
                        case WD::MTOMaterialTableHelper::MF_TotalWeight:
                            {
                                bool bToOk = false;
                                double toVal = FromString<double>(to[dataIndex], &bToOk);
                                bool bFromOk = false;
                                double fromVal = FromString<double>(from[dataIndex], &bFromOk);

                                assert(bToOk && bFromOk);
                                if (bToOk && bFromOk)
                                    to[dataIndex] = DoubleValueToString(toVal + fromVal);
                            }
                        default:
                            break;
                        }
                        ++dataIndex;
                    }
                };
                std::map<MaterialMergeKey, int> indexs;
                for (int i = 0; i < allData.size(); ++i)
                {
                    auto& subData = allData[i];
                    assert(subData.size() <= dataSize);
                    if (subData.size() > dataSize || subData.empty())
                    {
                        allData.erase(allData.begin() + i);
                        --i;
                        continue;
                    }
                    // 元件编码和规格都相同的需要合并
                    const auto& scomData = subData[scomCodeIndex];
                    const auto& specData = subData[specIndex];
                    auto key = MaterialMergeKey(scomData, specData);
                    if (auto itr = indexs.find(key); itr != indexs.end())
                    {
                        if (itr->second < allData.size())
                        {
                            mergeData(allData[itr->second], allData[i]);
                            allData.erase(allData.begin() + i);
                            --i;
                        }
                        else
                        {
                            assert(false);
                            itr->second = i;
                        }
                    }
                    else
                    {
                        indexs[key] = i;
                    }
                }

                assert(!allData.empty());
                if (!allData.empty())
                {
                    sprintf(filePath, "%s\\%s-mto.csv", path.path().toUtf8().data(), path.baseName().toUtf8().data());
                    try
                    {
                        std::sort(allData.begin() + 1, allData.end(), [](const StringVector& left, const StringVector& right) -> bool
                        {
                            if (left.size() != right.size())
                                return left.size() < right.size();
                            for (int i = 0; i < left.size(); ++i)
                            {
                                if (left[i] != right[i])
                                    return left[i] < right[i];
                            }
                            return false;
                        });
                    }
                    catch (...)
                    {
                        assert(false);
                    }
                    SaveCSVData(QString::fromUtf8(filePath), allData);
                }
            }
        }
    }

    WD_INFO_T("MTOMaterialTableDialog", "Table Generate Success!");
}

bool MTOMaterialTableDialog::contains(WD::WDNode::SharedPtr pNode)
{
    if (pNode == nullptr)
        return false;
    for (int index = 0; index < ui.listWidget->count(); ++index)
    {
        auto pItem = ui.listWidget->item(index);
        if (pItem == nullptr)
        {
            assert(false);
            continue;
        }
        auto pItemNode = pItem->data(Qt::UserRole).value<UiWeakObject>().subObject<WDNode>();
        if (pItemNode == nullptr)
        {
            assert(false);
            continue;
        }
        if (pItemNode->uuid() == pNode->uuid())
            return true;
    }
    return false;
}
void MTOMaterialTableDialog::addNodeToList(WD::WDNode::SharedPtr pNode)
{
    if (contains(pNode))
        return;
    auto item = new QListWidgetItem(QString::fromUtf8(pNode->name().c_str()));
    QVariant userData;
    userData.setValue(UiWeakObject(pNode));
    item->setData(Qt::UserRole, userData);
    ui.listWidget->addItem(item);
}

void MTOMaterialTableDialog::updateDialog()
{
    ui.comboBoxGenerateTable->blockSignals(true);
    ui.comboBoxGenerateTable->clear();
    ui.comboBoxGenerateTable->blockSignals(false);
    switch (StatisticsMethod(ui.comboBoxGenerateMethod->currentData().toInt()))
    {
    case WD::MTOMaterialTableDialog::SM_WXHH:
        {
            ui.comboBoxGenerateTable->addItem("StatisticsMaterialByPIPE", StatisticsType::ST_PIPE);
            ui.pushButtonATTAMaterialSummary->setEnabled(false);
        }
        break;
    case WD::MTOMaterialTableDialog::SM_HDY:
        {
            ui.comboBoxGenerateTable->addItem("StatisticsMaterialByBRAN", StatisticsType::ST_BRAN);
            ui.pushButtonATTAMaterialSummary->setEnabled(true);
        }
        break;
    default:
        break;
    }

    Trs("MTOMaterialTableDialog", ui.comboBoxGenerateTable);

    _helper.type = StatisticsType(ui.comboBoxGenerateTable->currentData().toInt());
}

void MTOMaterialTableDialog::initDialog()
{
    ui.comboBoxGenerateMethod->addItem("HDY",   StatisticsMethod::SM_HDY);
    ui.comboBoxGenerateMethod->addItem("WXHH",  StatisticsMethod::SM_WXHH);
    ui.comboBoxGenerateMethod->setCurrentIndex(0);
    updateDialog();
}
void MTOMaterialTableDialog::retranslateUi()
{
    Trs("MTOMaterialTableDialog"
        , static_cast<QDialog*>(this)
        , ui.pushButtonConfig
        , ui.pushButtonAddCurrent
        , ui.pushButtonAddCurrentMember
        , ui.pushButtonRemoveAll
        , ui.pushButtonRemoveChoosed
        , ui.pushButtonGenerateMaterialTable
        , ui.comboBoxGenerateMethod
        , ui.labelGenerateMethod
        , ui.labelGenerateTable
        , ui.labelTablePath
        , ui.pushButtonATTAMaterialSummary
    );
}