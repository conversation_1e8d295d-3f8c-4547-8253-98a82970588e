<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MTOMaterialTableDialog</class>
 <widget class="QDialog" name="MTOMaterialTableDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>425</width>
    <height>286</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MTOMaterialTable</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_3">
   <item row="0" column="0">
    <layout class="QGridLayout" name="gridLayout_2" rowstretch="0,0,1,0" columnstretch="0,1,0,0">
     <item row="0" column="0">
      <widget class="QLabel" name="labelGenerateMethod">
       <property name="text">
        <string>GenerateMethod</string>
       </property>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="QComboBox" name="comboBoxGenerateMethod"/>
     </item>
     <item row="0" column="2" rowspan="2" colspan="2">
      <widget class="QPushButton" name="pushButtonConfig">
       <property name="focusPolicy">
        <enum>Qt::ClickFocus</enum>
       </property>
       <property name="text">
        <string>Config</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item row="1" column="0">
      <widget class="QLabel" name="labelGenerateTable">
       <property name="text">
        <string>GenerateTable</string>
       </property>
      </widget>
     </item>
     <item row="1" column="1">
      <widget class="QComboBox" name="comboBoxGenerateTable"/>
     </item>
     <item row="2" column="0" colspan="2">
      <widget class="QListWidget" name="listWidget"/>
     </item>
     <item row="2" column="2" colspan="2">
      <layout class="QGridLayout" name="gridLayout" rowstretch="1,0,0,0,0">
       <item row="0" column="0">
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="1" column="0">
        <widget class="QPushButton" name="pushButtonAddCurrent">
         <property name="focusPolicy">
          <enum>Qt::ClickFocus</enum>
         </property>
         <property name="text">
          <string>AddCurrent</string>
         </property>
         <property name="autoDefault">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QPushButton" name="pushButtonAddCurrentMember">
         <property name="focusPolicy">
          <enum>Qt::ClickFocus</enum>
         </property>
         <property name="text">
          <string>AddCurrentMember</string>
         </property>
         <property name="autoDefault">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="3" column="0">
        <widget class="QPushButton" name="pushButtonRemoveChoosed">
         <property name="focusPolicy">
          <enum>Qt::ClickFocus</enum>
         </property>
         <property name="text">
          <string>RemoveChoosed</string>
         </property>
         <property name="autoDefault">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="4" column="0">
        <widget class="QPushButton" name="pushButtonRemoveAll">
         <property name="focusPolicy">
          <enum>Qt::ClickFocus</enum>
         </property>
         <property name="text">
          <string>RemoveAll</string>
         </property>
         <property name="autoDefault">
          <bool>false</bool>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item row="3" column="0">
      <widget class="QLabel" name="labelTablePath">
       <property name="text">
        <string>TablePath</string>
       </property>
      </widget>
     </item>
     <item row="3" column="1" colspan="2">
      <widget class="QLineEdit" name="lineEditPath"/>
     </item>
     <item row="3" column="3">
      <widget class="QToolButton" name="toolButtonPath">
       <property name="text">
        <string>...</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="1" column="0" alignment="Qt::AlignRight">
    <widget class="QSplitter" name="splitter">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <widget class="QPushButton" name="pushButtonATTAMaterialSummary">
      <property name="focusPolicy">
       <enum>Qt::ClickFocus</enum>
      </property>
      <property name="text">
       <string>ATTAMaterialSummary</string>
      </property>
      <property name="autoDefault">
       <bool>false</bool>
      </property>
     </widget>
     <widget class="QPushButton" name="pushButtonGenerateMaterialTable">
      <property name="focusPolicy">
       <enum>Qt::ClickFocus</enum>
      </property>
      <property name="text">
       <string>GenerateMaterialTable</string>
      </property>
      <property name="autoDefault">
       <bool>false</bool>
      </property>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
