#pragma once
#include "core/WDCore.h"
#include "core/node/WDNode.h"
#include <QString>
#include "log/WDLoggerPort.h"

class MTOMaterialTableHelperPrivate;

WD_NAMESPACE_BEGIN
/**
 * @brief MTO材料统计帮助 注意: 统计结果类型均为 utf-8 编码的字符串
*/
class MTOMaterialTableHelper
{
public:
    enum MaterialFlag
    {
        // 统计单元(分支/管道)名称
        MF_UnitName         = 1 << 0,
        // 管道名称
        MF_PipeSpec         = 1 << 1,
        // 元件种类
        MF_ComsType         = 1 << 2,
        // 编码
        MF_Code             = 1 << 3,
        // 元件编码
        MF_ScomCode         = 1 << 4,
        // 元件编码
        MF_ScomCodeWXHH     = 1 << 5,
        // 材料编码
        MF_MaterialCode     = 1 << 6,
        // 详细描述
        MF_DetailedDesc     = 1 << 7,
        // 材料描述
        MF_MaterialDesc     = 1 << 8,
        // 主管径
        MF_MainBore         = 1 << 9,
        // 次管径
        MF_SubBore          = 1 << 10,
        // 规格
        MF_Specifications   = 1 << 11,
        // 数量/长度
        MF_Count            = 1 << 12,
        // 单位
        MF_Unit             = 1 << 13,
        // 螺栓直径
        MF_BoltDiameter     = 1 << 14,
        // 螺栓长度
        MF_BoltLength       = 1 << 15,
        // 保温等级
        MF_InsulationSpec   = 1 << 16,
        // 唯一码
        MF_UniqueCode       = 1 << 17,
        // 单重
        MF_SingleWeight     = 1 << 18,
        // 总重
        MF_TotalWeight      = 1 << 19,
    };
public:
    using MTOMaterialTableData =  std::map<MaterialFlag, StringVector>;

    enum StatisticsType
    {
        ST_PIPE,
        ST_BRAN
    };
    using MaterialFlags = WDFlags<MaterialFlag, uint>;
    /**
     * @brief 获取标志对应的字符串
    */
    static const char* FlagToStr(const MaterialFlag& type, const StatisticsType& sType = ST_BRAN);
    /**
     * @brief 获取统计类型对应的类型字符串
    */
    static const char* GetTypeStr(const StatisticsType& type);
public:
    // 统计标志
    MaterialFlags   flags;
    // 统计类型(目前只支持分支)
    StatisticsType  type = ST_BRAN;
    // 需要过滤的类型,被过滤的类型MTO材料表中不会统计
    std::set<std::string> filterType;
    friend class MTOMaterialTableHelperPrivate;
public:
    MTOMaterialTableHelper(WD::WDCore& core);
    virtual~MTOMaterialTableHelper();
public:
    /**
     * @brief 生成MTO材料表
    */
    MTOMaterialTableData exec(const WDNode::Nodes& nodes);
    /**
     * @brief 生成ATTA材料汇总表
     * @return 
    */
    std::vector<StringVector> execATTA(const WDNode::Nodes& nodes);
private:
    WDCore& _core;
};

using MaterialFlag = MTOMaterialTableHelper::MaterialFlag;
using StatisticsType = MTOMaterialTableHelper::StatisticsType;
using MTOMaterialTableData = MTOMaterialTableHelper::MTOMaterialTableData;

WD_NAMESPACE_END