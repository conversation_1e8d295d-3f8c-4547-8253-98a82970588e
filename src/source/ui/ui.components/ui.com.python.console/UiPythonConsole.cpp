#include "UiPythonConsole.h"
#include "WDCore.h"
#include "WDPython.h"
#include "Qsci/qscilexerpython.h"
#include "Qsci/qsciapis.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"

#include <QKeyEvent>
UiPythonConsole::UiPythonConsole(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QWidget *parent)
    : QWidget(parent)
    , IUiComponent(mainWindow, attrs)
{
    ui.setupUi(this);
	this->setWindowFlags(Qt::CustomizeWindowHint);
    _printWidget = new QsciScintilla();
    ui.verticalLayoutPrint->addWidget(_printWidget);

    //只读且隐藏光标
    _printWidget->setReadOnly(true); 
    _printWidget->setCaretWidth(0);

    _printWidget->setWrapMode(QsciScintilla::WrapMode::WrapWord);

    //设置换行方式为 Unix 即遇到 "\n" 则换行
    _printWidget->setEolMode(QsciScintilla::EolMode::EolUnix);

    _printWidget->setAutoCompletionSource(QsciScintilla::AcsDocument);
    //设置字体
    QFont font = ui.lineEdit->font();
    _printWidget->setFont(font);
    _printWidget->setMarginsFont(font);
    QFontMetrics fontmetrics = QFontMetrics(font);
    //设置左侧行号栏宽度等
    _printWidget->setMarginWidth(0, fontmetrics.horizontalAdvance("00000"));
    _printWidget->setMarginLineNumbers(0, true);
    _printWidget->setBraceMatching(QsciScintilla::SloppyBraceMatch);
    _printWidget->setTabWidth(4);
    //设置括号等自动补全
    _printWidget->setAutoIndent(true);
    //初始设置 python 解析器
    auto    lex =   new QsciLexerPython(this);
    lex->setDefaultPaper(QColor(37,37,37));

    _printWidget->setIndentationGuidesBackgroundColor(QColor(37,37,37));

    _printWidget->setLexer(lex);
    //设置自动补全
    _printWidget->setCaretLineVisible(true);
    //设置光标所在行背景色
    _printWidget->setCaretLineBackgroundColor(Qt::lightGray);
    //设置编码为UTF-8
    _printWidget->SendScintilla(QsciScintilla::SCI_SETCODEPAGE, QsciScintilla::SC_CP_UTF8);
    //折叠样式
    _printWidget->setFolding(QsciScintilla::BoxedTreeFoldStyle);
    //折叠栏颜色
    _printWidget->setFoldMarginColors(Qt::gray, Qt::lightGray);

    _printWidget->setMarginsBackgroundColor(QColor(37,37,37));


    this->setWindowTitle(QString::fromUtf8(WD::WDTs("UiPythonConsole", "Titile").c_str()));

#ifdef WD_USE_PYTHON
    auto& wizPy = mWindow().core().wizDesignerPython();
    wizPy.noticePythonError() += {this, &UiPythonConsole::onPythonError};
    auto& wizPyOS = mWindow().core().wizDesignerPythonOStream();
    wizPyOS.noticeStdOut() += {this, &UiPythonConsole::onPyOS_std_out};
    wizPyOS.noticeStdError() += {this, &UiPythonConsole::onPyOS_std_error};
    wizPy.exec(
        "print(sys.version + '\\n' + 'WIZDesigner_' + core.version())"
    );
    ui.lineEdit->installEventFilter(this);
#endif

    connect(ui.lineEdit, SIGNAL(returnPressed()), this, SLOT(onLineEditReturnPressed()));
}

UiPythonConsole::~UiPythonConsole()
{
#ifdef WD_USE_PYTHON
    auto& wizPyOS = mWindow().core().wizDesignerPythonOStream();
    wizPyOS.noticeStdOut() -= {this, &UiPythonConsole::onPyOS_std_out};
    wizPyOS.noticeStdError() -= {this, &UiPythonConsole::onPyOS_std_error};
    auto& wizPy = mWindow().core().wizDesignerPython();
    wizPy.noticePythonError() -= {this, &UiPythonConsole::onPythonError};
#endif
}

void UiPythonConsole::onNotice(UiNotice * pNotice)
{
    int nType = pNotice->type();
    switch (nType)
    {
    case UiNoticeType::UNT_AllReady:
    {
        auto pAction = mWindow().queryAction("action.display.console.visible");
        auto pDock = mWindow().getDock(this);
        if (pAction != nullptr && pDock != nullptr)
        {
            connect(pDock, &QDockWidget::visibilityChanged, this, [=](bool visible)
                {
                    pAction->setChecked(visible);
                });
        }
    }
    break;
    case UiNoticeType::UNT_Action:
    {
        UiActionNotice* pActionNotice = static_cast<UiActionNotice*>(pNotice);
        if (pActionNotice->action().is("action.display.console.visible"))
        {
            bool bChecked = pActionNotice->action().checked();
            if (bChecked)
                mWindow().showDock(this);
            else
                mWindow().hideDock(this);
        }
    }
    break;
    default:
        break;
    }
}

QWidget * UiPythonConsole::getWidget(const char * name)
{
	if (name == nullptr || strlen(name) == 0)
	{
		return this;
	}
	return  nullptr;
}

void UiPythonConsole::onPyOS_std_out(const std::string& str)
{
    _printWidget->append(str.c_str());
    _printWidget->setCursorPosition(_printWidget->lines(), 0);

}
void UiPythonConsole::onPyOS_std_error(const std::string& str)
{
    _printWidget->append(str.c_str());
    _printWidget->setCursorPosition(_printWidget->lines(), 0);
}
void UiPythonConsole::onPythonError(const std::string& errStr)
{
    _printWidget->append(errStr.c_str());
    _printWidget->append("\n");
    _printWidget->setCursorPosition(_printWidget->lines(), 0);
}

bool UiPythonConsole::eventFilter(QObject* obj, QEvent* event)
{
    if(obj == ui.lineEdit)
    {
        if(event->type() == QEvent::KeyPress)
        {
            QKeyEvent* p = static_cast<QKeyEvent*>(event);
            if(p->key() == Qt::Key_Up)
            {
                keyUpPress();
                return true;
            }
            if(p->key() == Qt::Key_Down)
            {
                keyDownPress();
                return true;
            }
        }
    }
    return QWidget::eventFilter(obj,event);
}

void UiPythonConsole::keyUpPress()
{
    if(_recored.isEmpty())
        return;
    if ( ui.lineEdit->text().isEmpty() )
    {
        ui.lineEdit->setText(_recored.back());
        return;
    }
    if ( !_recored.contains(ui.lineEdit->text()) )
    {
        ui.lineEdit->setText(_recored.back());
        return;
    }
    int index = _recored.indexOf(ui.lineEdit->text());
    if(index == 0)
        return;
    ui.lineEdit->setText(_recored.at(index - 1));
}

void UiPythonConsole::keyDownPress()
{
    if(_recored.isEmpty())
        return;
    if ( ui.lineEdit->text().isEmpty() )
        return;
    if ( !_recored.contains(ui.lineEdit->text()) )
        return;
    int index = _recored.indexOf(ui.lineEdit->text());
    if(index == _recored.size() - 1)
        return;
    ui.lineEdit->setText(_recored.at(index + 1));
}

void UiPythonConsole::onLineEditReturnPressed()
{
#ifdef WD_USE_PYTHON
    QString cmd = ui.lineEdit->text();
    while(cmd.front() == ' ')
    {
        cmd.remove(0,1);
    }
    auto& wizPy = mWindow().core().wizDesignerPython();
    QString utf8Cmd = cmd.toUtf8();
    _printWidget->append(">>> " + cmd.toUtf8() + "\n");
    wizPy.exec(cmd.toUtf8().data());
    if ( !ui.lineEdit->text().isEmpty() )
    {
        if ( _recored.size() == 0 )
        {
            _recored << ui.lineEdit->text();
        }
        else
        {
            QString t = ui.lineEdit->text();
            for ( int i = 0; i < _recored.size(); ++i )
            {
                if ( _recored.at(i) == t )
                {
                    _recored.removeAt(i);
                    i--;
                }
            }
            _recored << ui.lineEdit->text();
        }
    }
#endif
    ui.lineEdit->clear();
}