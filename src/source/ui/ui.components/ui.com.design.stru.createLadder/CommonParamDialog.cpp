#include "CommonParamDialog.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "core/message/WDMessage.h"

WD_NAMESPACE_USE

CommonParamDialog::CommonParamDialog(QWidget *parent)
    :  QDialog(parent)
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));

    _params = LadderHandrailCommon::BalustradeParams();
    _maxPostPitch = 1500.0;
    _handrailPostInsert = 76.0;
    _kickplateDepth = 100.0;
    _kickplateThickness = 10.0;
    _floorplateThickness = 25.0;

    this->initDialog();
    this->retranslateUi();

    QObject::connect(ui.pushButtonReset,    &QPushButton::clicked, this, &CommonParamDialog::slotPushbuttonResetClicked);
    QObject::connect(ui.pushButtonConfirm,  &QPushButton::clicked, this, &CommonParamDialog::slotPushbuttonConfirmClicked);
}


void    CommonParamDialog::showEvent(QShowEvent *)
{
    // 统一将焦点设置到窗体，用于在窗口打开时清除其他控件焦点
    this->setFocus();
    this->initDialog();
}

void    CommonParamDialog::hideEvent(QHideEvent *)
{
}

void    CommonParamDialog::slotPushbuttonResetClicked()
{
    _params = LadderHandrailCommon::BalustradeParams();
    _maxPostPitch = 1500.0;
    _handrailPostInsert = 76.0;
    _kickplateDepth = 100.0;
    _kickplateThickness = 10.0;
    _floorplateThickness = 25.0;
    initDialog();
}
void    CommonParamDialog::slotPushbuttonConfirmClicked()
{
    _params.columnHigh      = ui.doubleSpinBoxColumnHeight->value();
    _params.handrailCentres = ui.doubleSpinBoxHandrailCentres->value();
    _params.handrailDiam    = ui.doubleSpinBoxHandrailDiameter->value();
    _maxPostPitch           = ui.doubleSpinBoxMaxPostPitch->value();
    _handrailPostInsert     = ui.doubleSpinBoxHandrailPostInsert->value();
    _kickplateDepth         = ui.doubleSpinBoxKickplateDepth->value();
    _kickplateThickness     = ui.doubleSpinBoxKickplateThickness->value();
    _floorplateThickness    = ui.doubleSpinBoxFloorplateThickness->value();
    this->accept();
}
void    CommonParamDialog::initDialog()
{
    ui.doubleSpinBoxColumnHeight->setValue(_params.columnHigh);
    ui.doubleSpinBoxHandrailCentres->setValue(_params.handrailCentres);
    ui.doubleSpinBoxHandrailDiameter->setValue(_params.handrailDiam);
    ui.doubleSpinBoxMaxPostPitch->setValue(_maxPostPitch);
    ui.doubleSpinBoxHandrailPostInsert->setValue(_handrailPostInsert);
    ui.doubleSpinBoxKickplateDepth->setValue(_kickplateDepth);
    ui.doubleSpinBoxKickplateThickness->setValue(_kickplateThickness);
    ui.doubleSpinBoxFloorplateThickness->setValue(_floorplateThickness);
}
void    CommonParamDialog::retranslateUi()
{
    Trs("CreateLadderDialog"
        , static_cast<QDialog*>(this)
        , ui.labelColumnHeight
        , ui.labelFloorplateThickness
        , ui.labelHandrailCentres
        , ui.labelHandrailDiameter
        , ui.labelHandrailPostInsert
        , ui.labelKickplateDepth
        , ui.labelKickplateThickness
        , ui.labelMaxPostPitch
        
        , ui.pushButtonReset
        , ui.pushButtonConfirm
	  );
}