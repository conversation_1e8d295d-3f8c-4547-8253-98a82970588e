#include "CreatePlatformDialog.h"
#include "core/nodeTree/WDNodeTree.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/message/WDMessage.h"
#include "PlatformCreater/CircularPlatformCreater.h"
#include "businessModule/WDBMPermissionMgr.h"
#include "core/businessModule/WDBMClaimMgr.h"
#include "core/undoRedo/WDUndoStack.h"

WD_NAMESPACE_USE

CreatePlatformDialog::CreatePlatformDialog(WD::WDCore& core, WD::CommonParamDialog& commonParamDialog, QWidget *parent)
    :   _core(core), _captureHelper(core), _commonParamDialog(commonParamDialog), QDialog(parent)
    , _nameHelpter(_core.getBMDesign())
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    this->resize(QSize(300, 500));

    ui.comboBoxType->addItem("CircularPlatform",    static_cast<int>(CreateType::CT_CircularPlatform));
    //ui.comboBoxType->addItem("RectangularPlatform", static_cast<int>(CreateType::CT_RectangularPlatform));

    this->retranslateUi();
    ui.comboBoxType->setCurrentIndex(0);
    _currentCreater = nullptr;
    this->updateDialog(CreateType(ui.comboBoxType->currentData().toInt()));

    connect(ui.pushButtonConfirm,   &QPushButton::clicked, this, &CreatePlatformDialog::slotPushButtonConfirmClicked);
    connect(ui.pushButtonCancle,    &QPushButton::clicked, this, &CreatePlatformDialog::slotPushButtonCancleClicked);

    connect(ui.comboBoxType,        QOverload<int>::of(& QComboBox::currentIndexChanged)
        , this, &CreatePlatformDialog::slotComboBoxTypeCurrentIndexChanged);
    connect(ui.comboBoxCreateMethod,    QOverload<int>::of(& QComboBox::currentIndexChanged)
        , this, &CreatePlatformDialog::slotComboBoxCreateMethodCurrentIndexChanged);

    _captureHelper.setCheckBoxCapture(ui.checkBoxSnap);
    _captureHelper.setCheckBoxXYZ(ui.checkBoxX, ui.checkBoxY, ui.checkBoxZ);
    _captureHelper.setDoubleSpinBoxXYZ(ui.doubleSpinBoxX, ui.doubleSpinBoxY, ui.doubleSpinBoxZ);

    _nameHelpter.setLineEdit(ui.lineEditName);
}

CreatePlatformDialog::~CreatePlatformDialog()
{
}

void    CreatePlatformDialog::showEvent(QShowEvent *)
{
    // 统一将焦点设置到窗体，用于在窗口打开时清除其他控件焦点
    this->setFocus();
    slotComboBoxCreateMethodCurrentIndexChanged(ui.comboBoxCreateMethod->currentData().toInt());
    _nameHelpter.resetName();

    if (_currentCreater != nullptr)
        _currentCreater->initParam();
    _captureHelper.setPosition(WD::DVec3::Zero(), true);
}

void    CreatePlatformDialog::hideEvent(QHideEvent *)
{
}

void    CreatePlatformDialog::slotPushButtonConfirmClicked()
{
    auto pCurNode = _core.nodeTree().currentNode();
    if (pCurNode == nullptr)
    {
        WD_WARN_T("CreateLadderDialog", "Current Node Is Empty!");
        return;
    }
    auto pParent = _core.getBMDesign().findParentWithType(*pCurNode, "STRU");
    if (pParent == nullptr)
    {
        WD_WARN_T("CreateLadderDialog", "Current Node Not Support Create Platform!");
        return;
    }
    //  权限校验
    if (!_core.getBMDesign().permissionMgr().check(*pParent))
    {
        WD_WARN_T("CreateLadderDialog", "You cannot operate the current node!");
        return;
    }
    // 申领对象
    if (!_core.getBMDesign().claimMgr().checkAdd(pParent))
        return;
    if (_nameHelpter.exists()) 
    {
        WD_WARN_T("CreateLadderDialog", "Name Is Exist!");
        return;
    }
    std::string name = _nameHelpter.name();
    std::string errorStr;
    if (_currentCreater == nullptr)
    {
        assert(false);
        auto type = CreateType(ui.comboBoxType->currentData().toInt());
        auto itr = _creaters.find(type);
        if (itr != _creaters.end())
        {
            _currentCreater = itr->second;
            if (_currentCreater == nullptr)
            {
                assert(false);
                return;
            }
        }
    }
    std::string error;
    auto pNewNode = _currentCreater->create(*pParent, name, _captureHelper.position(), errorStr);
    if (pNewNode == nullptr)
    {
        WD_ERROR(errorStr);
        return;
    }
    _core.nodeTree().setCurrentNode(pNewNode);

    auto cmdCreatedNode = _core.getBMDesign().makeCreatedCommand({pNewNode});
    auto cmdAddToScene = _core.getBMDesign().makeSceneAddCommand({pNewNode});
    if (cmdAddToScene != nullptr && cmdCreatedNode != nullptr)
    {
        _core.undoStack().beginMarco("CreateLadder");
        _core.undoStack().push(cmdCreatedNode);
        _core.undoStack().push(cmdAddToScene);
        _core.undoStack().endMarco();
    }
    
    _nameHelpter.resetName();
}
void    CreatePlatformDialog::slotPushButtonCancleClicked()
{
    this->reject();
}
void    CreatePlatformDialog::slotComboBoxTypeCurrentIndexChanged(int)
{
    this->updateDialog(CreateType(ui.comboBoxType->currentData().toInt()));
}
void    CreatePlatformDialog::slotComboBoxCreateMethodCurrentIndexChanged(int)
{
    if (_currentCreater == nullptr)
    {
        assert(false);
        auto type = CreateType(ui.comboBoxType->currentData().toInt());
        auto itr = _creaters.find(type);
        if (itr != _creaters.end())
        {
            _currentCreater = itr->second;
            if (_currentCreater == nullptr)
            {
                assert(false);
                return;
            }
        }
    }
    _currentCreater->setCreateType(ui.comboBoxCreateMethod->currentData().toInt());
}

void    CreatePlatformDialog::updateDialog(const CreateType& type)
{
    auto pLayout = ui.groupBoxParam->layout();
    if (pLayout == nullptr)
    {
        pLayout = new QGridLayout();
        ui.groupBoxParam->setLayout(pLayout);
    }
    if (_currentCreater != nullptr)
    {
        auto pWidget = _currentCreater->getWidget();
        assert(pWidget != nullptr);
        if (pWidget != nullptr)
        {
            pLayout->removeWidget(pWidget);
            pWidget->setParent(nullptr);
        }
    }

    auto itr = _creaters.find(type);
    if (itr == _creaters.end())
    {
        auto ret = _creaters.emplace(type, nullptr);
        if (!ret.second)
        {
            assert(false);
            return;
        }
        itr = ret.first;
    }
    ui.comboBoxCreateMethod->blockSignals(true);
    ui.comboBoxCreateMethod->clear();
    ui.comboBoxCreateMethod->blockSignals(false);
    auto pCreater = itr->second;
    if (pCreater == nullptr)
    {
        switch (type)
        {
        case CreateType::CT_CircularPlatform:
            pCreater = new WD::CircularPlatformCreater(_core, _commonParamDialog, _dicts);
            break;
        case CreateType::CT_RectangularPlatform:

            break;
            break;
        default:
            {
                _currentCreater = nullptr;
                WD_WARN_T("CreateLadderDialog", "Platform type not currently supported!");
                return;
            }
            break;
        }
        itr->second = pCreater;
    }
    // 使用创建器初始化创建方式下拉框,初始化失败说明该创建器创建方式不可编辑并会使下拉框隐藏,这里同步下拉框label的状态
    ui.labelCreateMethod->setVisible(pCreater->initCreateMethodComboBox(*ui.comboBoxCreateMethod));
    _currentCreater = pCreater;
    pLayout->addWidget(pCreater->getWidget());
}

void    CreatePlatformDialog::retranslateUi()
{
    Trs("CreateLadderDialog"
        , static_cast<QDialog*>(this)
        , ui.labelType
        , ui.labelName
        , ui.labelCreateMethod

        , ui.groupBoxParam
        , ui.groupBoxPosition

        , ui.comboBoxType
        , ui.comboBoxCreateMethod

        , ui.labelSnap

        , ui.pushButtonConfirm
        , ui.pushButtonCancle
	  );
}