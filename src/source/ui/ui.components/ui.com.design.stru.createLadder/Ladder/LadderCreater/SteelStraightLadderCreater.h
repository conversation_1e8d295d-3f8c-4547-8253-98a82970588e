#pragma once
#include "core/WDCore.h"
#include "core/node/WDNode.h"
#include "../../CreaterBase.h"
WD_NAMESPACE_BEGIN
/**
* @brief 钢直梯生成器
*/
class SteelStraightLadderCreater : public CreaterBase
{
public:
    //创建方式 -> 钢直梯有三种创建方式 : 1,A step ladder、A front exit ladder、A side exit ladder
    // 目前先实现 A front exit ladder(前登式直梯)
    enum class CreateMethod
    {
        CM_StepLadder,
        //前登式直梯
        CM_FrontExitLadder,
        CM_SideExitLadder,
    };
    CreateMethod type = CreateMethod::CM_FrontExitLadder;
private:
    // 高度最大值,钢直梯最大高度不能大于 9144, 这里暂时写死 9144
    constexpr static const int ladderMaxHeight = 9144;
    // 不可修改的固定参数
    // 横杆长度
    double crossBarLength       =   450.0;
    // 最优横杆高度
    double bestCrossBarHeight   =   230.0;
    // 钢直梯的部分参数会跟据高度是否大于临界值而改变
    // 钢直梯的高度临界值,当高度大于和小于此值时部分参数会不同
    constexpr static const double ladderCriticalVal = 2300.0;

    // 立柱与顶部护栏之间的间距
    double disColumnTGuardrail  =   100.0;
    // 顶部方环的内径,暂时不知道从何获取
    double tRtorRin             =   0.1;
    // 顶部护栏的垫片BOX参数
    double tBoxX                =   114.0;
    double tBoxY                =   76.0;

    // 护圈参数
    struct RetainerParam
    {
        // 护圈的高度最小值     ->  当需要设置护圈的部分的高度小于此值时,只生成一个护圈
        double minHeight    =   410.0;
        // 护圈的最佳值        ->  当需要设置护圈的部分的高度大于 minGHeight 时, 用超过部分 / 此值,结果向上取整 +1 得到护圈数量
        double bestHeight   =   760.0;

        double thickness    =   6.0;
        double width        =   65.0;
        double angle        =   45.0;
    };
    RetainerParam rParam;

    struct SSLParamVariables
    {
        // 侧梁厚度
        double beamThickness        =   0.0;
        // 第一根横杆离楼梯原点高度(即底杆高度)
        double bottomBarHeight      =   0.0;
        // 侧梁宽度
        double beamWidth            =   0.0;
        // 底部BOX的长度
        double bBoxLength           =   0.0;
        // 立柱参数,顶部的护栏
        struct GuardrailParams
        {
            // 左右两侧立柱的间距
            double disLeftRight     =   0.0;
            // 立柱与顶部弯梁之间的间距(x轴
            double disColumnTBeamX  =   0.0;
            // 立柱与顶部弯梁之间的间距(y轴)
            double disColumnTBeamY  =   0.0;
            // 弯梁在最上面一阶横杆上面冒头出来的高度
            double beamOverHeight   =   0.0;
            // 护栏处斜BOX的倾斜角度(抬高角度)
            double angle            =   0.0;
            // 顶部护栏的厚度
            double tThickness       =   0.0;
            // 顶部护栏的延伸长度
            double tLength          =   0.0;

        };
        GuardrailParams gParams;
    };
    inline SSLParamVariables getSSLParam(const double& ladderHeight)
    {
        SSLParamVariables params;
        // 大于等级临界值时的参数
        if (ladderHeight >= ladderCriticalVal)
        {
            params.beamThickness    =   8.0;
            params.bottomBarHeight  =   230.0;
            params.beamWidth        =   70.0;
            params.bBoxLength       =   70.0;


            params.gParams.disLeftRight     = 1050.0;
            params.gParams.disColumnTBeamX  = 216.0;
            params.gParams.disColumnTBeamY  = 311.0;
            params.gParams.beamOverHeight   = 79.0;
            params.gParams.angle            = 36.0;
            params.gParams.tThickness       = 6.0;
            params.gParams.tLength          = 670.50;
        }
        // 低于临界值时的参数
        else
        {
            params.beamThickness            = 10.0;
            params.bottomBarHeight          = 150.0;
            params.beamWidth                = 65.0;
            params.bBoxLength               = 65.0;

            params.gParams.disLeftRight     = 976.0;
            params.gParams.disColumnTBeamX  = 178.0;
            params.gParams.disColumnTBeamY  = 276.0;
            params.gParams.beamOverHeight   = 90.0;
            params.gParams.angle            = 45.0;
            params.gParams.tThickness       = 8.0;
            params.gParams.tLength          = 455;
        }
        return params;
    }
private:
    static constexpr const char* ladderHeightStr        = "LadderHeight";
    static constexpr const char* orientationStr         = "Orientation";
    static constexpr const char* crossbarDiameterStr    = "CrossbarDiameter";
public:
    SteelStraightLadderCreater(WDCore& core, WD::CommonParamDialog& commonParamDialog, Dicts& dicts);
public:
    virtual WD::WDNode::SharedPtr create(WD::WDNode& parentNode
        , const std::string& name
        , const DVec3& position
        , std::string& outError) override;
    virtual bool initCreateMethodComboBox(QComboBox& comboBox) override;
public:
    virtual int createType() override;
    virtual void setCreateType(int cType) override;
private:
    // 前登式直梯创建
    WD::WDNode::SharedPtr createFrontExitLadder(WD::WDNode& parentNode
        , const std::string& name
        , const DVec3& position
        , std::string& outError);
};

WD_NAMESPACE_END