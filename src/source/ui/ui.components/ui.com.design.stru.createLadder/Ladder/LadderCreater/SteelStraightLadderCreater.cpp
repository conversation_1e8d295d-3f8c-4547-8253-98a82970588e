#include "SteelStraightLadderCreater.h"
#include <QHeaderView>
#include <QLineEdit>
#include <QDoubleSpinBox>
#include <QComboBox>
#include "core/math/DirectionParser.h"
#include "core/businessModule/typeMgr/WDBMAttrValue.h"
#include "core/WDTranslate.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "core/businessModule/design/WDBMDesign.h"

WD_NAMESPACE_USE

SteelStraightLadderCreater::SteelStraightLadderCreater(WDCore& core, WD::CommonParamDialog& commonParamDialog, Dicts& dicts)
    : CreaterBase(core, commonParamDialog, dicts)
{
    _params.reserve(3);
    // 楼梯高度
    _params.emplace_back(geneDoubleAttrDesc(ladderHeightStr, 0, 0.0));
    // 朝向字符串
    _params.emplace_back(geneStringAttrDesc(orientationStr, "Y", "[-a-zA-Z0-9\\.\\s]*"));
    // 横杆直径
    _params.emplace_back(geneDoubleAttrDesc(crossbarDiameterStr, 20.0));
    this->initWidget();
}

WD::WDNode::SharedPtr SteelStraightLadderCreater::create(WD::WDNode& parentNode
    , const std::string& name
    , const DVec3& position
    , std::string& outError)
{
    switch (type)
    {
        // 目前只支持前登式直梯
    case CreateMethod::CM_FrontExitLadder:
        return createFrontExitLadder(parentNode, name, position, outError);
    default:
        break;
    }
    assert(false);
    return nullptr;
}
bool SteelStraightLadderCreater::initCreateMethodComboBox(QComboBox& comboBox)
{
    comboBox.blockSignals(true);
    comboBox.clear();
    comboBox.addItem(QString::fromUtf8(WD::WDTs("CreateLadderDialog", "FrontExitLadder").c_str())
        , int(CreateMethod::CM_FrontExitLadder));

    comboBox.setCurrentIndex(0);
    for (int idx = 0; idx < comboBox.count(); ++idx)
    {
        if (comboBox.itemData(idx).toInt() == this->createType())
        {
            comboBox.setCurrentIndex(idx);
            break;
        }
    }
    comboBox.setVisible(true);
    comboBox.blockSignals(false);
    return true;
}
int SteelStraightLadderCreater::createType()
{
    return static_cast<int>(this->type);
}
void SteelStraightLadderCreater::setCreateType(int cType)
{
    auto newType = CreateMethod(cType);
    if (this->type == newType)
        return;
    this->type = newType;
}

WD::WDNode::SharedPtr SteelStraightLadderCreater::createFrontExitLadder(WD::WDNode& parentNode
    , const std::string& name
    , const DVec3& position
    , std::string& outError)
{
    // 楼梯高度
    const double ladderHeight = this->getValue(ladderHeightStr).toDouble();
    if (ladderHeight == 0.0)
    {
        outError = WDTs("CreateLadderDialog", ladderHeightStr) + WDTs("CreateLadderDialog", "Data cannot be 0!");
        return nullptr;
    }
    if (ladderHeight > ladderMaxHeight)
    {
        char info[1024] = { 0 };
        sprintf_s(info, sizeof(info), "%dmm", ladderMaxHeight);
        outError = WDTs("CreateLadderDialog", "Steel straight ladder cannot long than") + info;
        return nullptr;
    }
    // 横杆直径
    const double& crossBarDiameter = this->getValue(crossbarDiameterStr).toDouble();
    if (crossBarDiameter == 0.0)
    {
        outError = WDTs("CreateLadderDialog", crossbarDiameterStr) + WDTs("CreateLadderDialog", "Data cannot be 0!");
        return nullptr;
    }
    // 朝向
    auto oriStr = this->getValue(orientationStr).toString();
    DVec3 dir = DVec3::Zero();
    if (!DirectionParserENU::Direction(oriStr, dir))
    {
        if (!DirectionParserXYZ::Direction(oriStr, dir))
        {
            outError = WDTs("CreateLadderDialog", "Orientation string input error!");
            return nullptr;
        }
    }
    // 直梯参数
    auto sslParam = getSSLParam(ladderHeight);

    if (ladderHeight < sslParam.bottomBarHeight)
    {
        outError = WDTs("CreateLadderDialog", "The total height of the ladder is less than the best step height, create failed!");
        return nullptr;
    }

    // 1.计算横杆个数 -> 横杆个数 = 楼梯高度 / 最优横杆高度 四舍五入,取整
    int crossBarCnt = static_cast<int>(LadderHandrailCommon::DoubleChangeDecimalPlaces(double(ladderHeight / bestCrossBarHeight), 0));
    if (crossBarCnt <= 0)
    {
        outError = WDTs("CreateLadderDialog", "The total height of the ladder is less than the best step height, create failed!");
        return nullptr;
    }
    // 2.计算实际横杆间距 -> 实际横杆间距 = (楼梯高度 - 底杆高度) / (横杆个数 - 1) 保留两位小数、四舍五入
    double realCrossBarSpacing = LadderHandrailCommon::DoubleChangeDecimalPlaces((ladderHeight - sslParam.bottomBarHeight) / (crossBarCnt == 1 ? 1 : crossBarCnt - 1));

    WD::WDNode::SharedPtr pRootNode = LadderHandrailCommon::CreateNode(_core, parentNode, "STRU");
    if (pRootNode == nullptr)
    {
        assert(false);
        return nullptr;
    }
    pRootNode->setAttribute("Name", name);
    const auto& bParam = _commonParamDialog.balustradeParams();
    // 这里记录立柱上的球面高度
    std::array<double, 2> highs = {bParam.columnHigh - bParam.handrailCentres, bParam.columnHigh};
    // 钢直梯大于临界值和小于临界值时侧梁和上部的护栏样式不同,这里分开处理
    if (ladderHeight < ladderCriticalVal)
    {
        const DVec3 start = DVec3::Zero();
        const auto& gParams = sslParam.gParams;
        // 绘制横杆
        {
            auto pSubsStepNode = LadderHandrailCommon::CreateNode(_core, *pRootNode, "SUBS");
            if (pSubsStepNode == nullptr)
            {
                assert(false);
                return nullptr;
            }
            // 所有的圆柱体旋转是一致的
            auto mat = DMat3::MakeRotationXY(DVec3::AxisZ(), DVec3::AxisY());
            for (int i = 0; i < crossBarCnt; ++i)
            {
                auto high = i * realCrossBarSpacing + sslParam.bottomBarHeight;
                auto center = start + DVec3::AxisNY() * sslParam.beamWidth / 2 + DVec3::AxisZ() * high;
                LadderHandrailCommon::CreateCYLI(_core, *pSubsStepNode, crossBarLength, crossBarDiameter, center, mat.toQuat());
            }
        }
        // 记录顶部方环3的内径
        double rtor3Rin;
        // 绘制左右弯梁
        {
            auto createBeam = [&] (WD::WDNode& root, const DVec3& axisY) ->bool
            {
                // 弯梁下有四个BOX + 四个方环

                // box1
                auto boxSize = DVec3(sslParam.bBoxLength, sslParam.beamWidth, sslParam.beamThickness);
                auto boxPosition = DVec3::AxisZ() * boxSize.z / 2;
                LadderHandrailCommon::CreateBOX(_core, root, boxSize.x, boxSize.y, boxSize.z, boxPosition);
                // box2
                boxSize = DVec3(sslParam.beamThickness, sslParam.beamWidth, ladderHeight + gParams.beamOverHeight);
                boxPosition = DVec3::AxisNX() * (sslParam.bBoxLength + sslParam.beamThickness) / 2 + DVec3::AxisZ() * boxSize.z / 2;
                LadderHandrailCommon::CreateBOX(_core, root, boxSize.x, boxSize.y, boxSize.z, boxPosition);

                auto mat = DMat3::MakeRotation((90 - gParams.angle), DVec3::AxisY());
                // rtor1
                auto rtorPosition = DVec3::AxisX() * (-sslParam.bBoxLength / 2 + sslParam.beamThickness)
                    + DVec3::AxisZ() * (ladderHeight + gParams.beamOverHeight);
                auto rMat = DMat3::MakeRotationXZ(mat * DVec3::AxisNX(), DVec3::AxisNY());
                LadderHandrailCommon::CreateRTOR(_core, root, sslParam.beamThickness, sslParam.beamThickness * 2, sslParam.beamWidth, gParams.angle
                    , rtorPosition, rMat.toQuat());
                // 这里计算斜BOX的各种参数
                double offset = (gParams.disLeftRight - crossBarLength) / 2 - gParams.disColumnTBeamX- sslParam.beamThickness / 2 - sslParam.beamThickness;
                double angleCosVal = std::cos(WD::DegToRad(gParams.angle));
                double angleTanVal = std::tan(WD::DegToRad(gParams.angle));
                double rAngleSinVal = std::sin(WD::DegToRad(90 - gParams.angle));
                double rAngleCosVal = std::cos(WD::DegToRad(90 - gParams.angle));
                // 这里根据方环和box的位置算出实际上BOX所占空间的宽度
                double realBoxWidth = (offset + sslParam.beamThickness * 2 * rAngleCosVal - sslParam.beamThickness
                    - (sslParam.beamThickness - sslParam.beamThickness * rAngleCosVal));
                // 这里算出方环和BOX实际的高度
                double boxHigh = realBoxWidth / angleTanVal + sslParam.beamThickness * angleCosVal + sslParam.beamThickness * rAngleSinVal * 2;
                // box3
                boxSize = DVec3(sslParam.beamThickness, sslParam.beamWidth, realBoxWidth / angleCosVal);
                boxPosition = DVec3::AxisX() * ((offset - sslParam.bBoxLength) / 2)
                    + DVec3::AxisZ() * (ladderHeight + gParams.beamOverHeight + boxHigh / 2);
                assert(gParams.angle > 0 && gParams.angle < 90);
                LadderHandrailCommon::CreateBOX(_core, root, boxSize.x, boxSize.y, boxSize.z, boxPosition, mat.toQuat());

                // rtor2
                rtorPosition = DVec3::AxisX() * (-sslParam.bBoxLength / 2 + offset - sslParam.beamThickness )
                    + DVec3::AxisZ() * (ladderHeight + gParams.beamOverHeight + boxHigh);
                rMat = DMat3::MakeRotationXZ(mat * DVec3::AxisX(), DVec3::AxisNY());
                LadderHandrailCommon::CreateRTOR(_core, root, sslParam.beamThickness, sslParam.beamThickness * 2, sslParam.beamWidth, gParams.angle
                    , rtorPosition, rMat.toQuat());

                rtor3Rin = sslParam.beamWidth / 2 + sslParam.beamThickness;
                // box4
                boxSize = DVec3(sslParam.beamThickness, sslParam.beamWidth
                    , bParam.columnHigh - boxHigh - gParams.beamOverHeight - (rtor3Rin + sslParam.beamWidth / 2));
                boxPosition = DVec3::AxisX() * (offset - (sslParam.bBoxLength - sslParam.beamThickness) / 2)
                    + DVec3::AxisZ() * (ladderHeight + gParams.beamOverHeight + boxHigh + boxSize.z / 2);
                LadderHandrailCommon::CreateBOX(_core, root, boxSize.x, boxSize.y, boxSize.z, boxPosition);

                // rtor3
                rtorPosition = DVec3::AxisX() * (-sslParam.bBoxLength / 2 + offset + sslParam.beamThickness / 2)
                    + axisY * (sslParam.beamWidth + sslParam.beamThickness)
                    + DVec3::AxisZ() * (ladderHeight + bParam.columnHigh - (rtor3Rin + sslParam.beamWidth / 2));
                rMat = DMat3::MakeRotationXY(-axisY, DVec3::AxisZ());
                LadderHandrailCommon::CreateRTOR(_core, root, rtor3Rin, sslParam.beamWidth + rtor3Rin, sslParam.beamThickness, 90, rtorPosition, rMat.toQuat());

                // rtor4
                rtorPosition = DVec3::AxisX() * (-sslParam.bBoxLength / 2 + offset + sslParam.beamThickness / 2)
                    + axisY * (sslParam.beamWidth + sslParam.beamThickness)
                    + DVec3::AxisZ() * (ladderHeight + bParam.columnHigh);
                rMat = DMat3::MakeRotationYZ(axisY, DVec3::AxisX());
                LadderHandrailCommon::CreateRTOR(_core, root, tRtorRin, sslParam.beamWidth / 2, sslParam.beamThickness, 180, rtorPosition, rMat.toQuat());

                return true;
            };

            // 左弯梁
            auto pLeftBeamNode = LadderHandrailCommon::CreateNode(_core, *pRootNode, "SUBS");
            if (pLeftBeamNode == nullptr)
            {
                assert(false);
                return nullptr;
            }
            // 右弯梁  -> 为左弯梁的镜像
            auto pRightBeamNode = LadderHandrailCommon::CreateNode(_core, *pRootNode, "SUBS");
            if (pRightBeamNode == nullptr)
            {
                assert(false);
                return nullptr;
            }
            createBeam(*pLeftBeamNode, DVec3::AxisY());
            createBeam(*pRightBeamNode, DVec3::AxisNY());
            auto mat = DMat3::MakeRotationXZ(DVec3::AxisNX(), DVec3::AxisZ());
            pLeftBeamNode->setAttribute("Position WRT Owner",     start + DVec3::AxisX() *  ((sslParam.bBoxLength + crossBarLength) / 2 + sslParam.beamThickness));
            pRightBeamNode->setAttribute("Position WRT Owner",    start + DVec3::AxisNX() * ((sslParam.bBoxLength + crossBarLength) / 2 + sslParam.beamThickness));
            pRightBeamNode->setAttribute("Orientation WRT Owner", mat.toQuat());
        }
        // 绘制护栏
        {
            auto createConnectPart = [&] (WD::WDNode& root, const DVec3& axisX)
            {
                // 立柱的垫片BOX
                LadderHandrailCommon::CreateBOX(_core, root, tBoxX, tBoxY, sslParam.beamThickness, DVec3(0, 0, sslParam.beamThickness / 2));
                auto cyliHeight = gParams.disColumnTBeamX - gParams.tThickness - sslParam.beamThickness / 2;
                auto cyliPosition = axisX * cyliHeight / 2 + DVec3::AxisZ() * highs[0];
                auto rMat = DMat3::MakeRotationYZ(DVec3::AxisY(), axisX);
                LadderHandrailCommon::CreateCYLI(_core, root, cyliHeight, bParam.ballDiam / 2, cyliPosition, rMat.toQuat());

                auto boxPosition = axisX * (cyliHeight + gParams.tThickness / 2)
                    + DVec3::AxisY() * (-(gParams.disColumnTBeamY  + sslParam.beamWidth - bParam.ballDiam / 2) / 2)
                    + DVec3::AxisZ() * highs[0];
                LadderHandrailCommon::CreateBOX(_core, root, gParams.tThickness, gParams.disColumnTBeamY + bParam.ballDiam / 2 + sslParam.beamWidth, sslParam.beamWidth, boxPosition);

                cyliPosition = axisX * disColumnTGuardrail / 2 + DVec3::AxisZ() * highs[1];
                LadderHandrailCommon::CreateCYLI(_core, root, disColumnTGuardrail, bParam.ballDiam / 2, cyliPosition, rMat.toQuat());

                auto length = gParams.disColumnTBeamY - rtor3Rin + bParam.ballDiam / 4 - sslParam.beamWidth / 2;
                boxPosition = axisX * (disColumnTGuardrail + gParams.tThickness / 2)
                    + DVec3::AxisY() * (-(length - bParam.ballDiam / 2) / 2)
                    + DVec3::AxisZ() * highs[1];
                LadderHandrailCommon::CreateBOX(_core, root, gParams.tThickness, length, sslParam.beamWidth, boxPosition);

                auto rtorPosition = axisX * (disColumnTGuardrail + gParams.tThickness / 2)
                    + DVec3::AxisY() * bParam.ballDiam / 4
                    + DVec3::AxisZ() * highs[1];
                rMat = DMat3::MakeRotationYZ(DVec3::AxisY(), axisX);
                LadderHandrailCommon::CreateRTOR(_core, root, tRtorRin, sslParam.beamWidth / 2, gParams.tThickness, 180, rtorPosition, rMat.toQuat());

            };

            // 左立柱
            auto pLeftGuardrailNode = LadderHandrailCommon::CreatePost(_core, *pRootNode, DVec3::Zero(), _commonParamDialog.balustradeParams());
            if (pLeftGuardrailNode == nullptr)
            {
                assert(false);
                return nullptr;
            }
            // 右立柱
            auto pRightGuardrailNode = LadderHandrailCommon::CreatePost(_core, *pRootNode, DVec3::Zero(), _commonParamDialog.balustradeParams());
            if (pRightGuardrailNode == nullptr)
            {
                assert(false);
                return nullptr;
            }
            createConnectPart(*pLeftGuardrailNode,  DVec3::AxisNX());
            createConnectPart(*pRightGuardrailNode, DVec3::AxisX());
            // 绘制立柱
            DVec3 gPosition = start + DVec3::AxisX() * gParams.disLeftRight / 2
                + DVec3::AxisY() * gParams.disColumnTBeamY
                + DVec3::AxisZ() * ladderHeight;
            pLeftGuardrailNode->setAttribute("Position WRT Owner",    gPosition);
            gPosition = gPosition + DVec3::AxisNX() * gParams.disLeftRight;
            pRightGuardrailNode->setAttribute("Position WRT Owner",   gPosition);
        }
        // 绘制护圈
        {
            auto pTopGuardrailNode = LadderHandrailCommon::CreateNode(_core, *pRootNode, "SUBS");
            if (pTopGuardrailNode == nullptr)
            {
                assert(false);
                return nullptr;
            }
            const auto cyliHeight = gParams.disColumnTBeamX - gParams.tThickness - sslParam.beamThickness / 2 - disColumnTGuardrail;
            const auto cyliDiam = bParam.ballDiam / 2;
            DVec3 cyliPosition = start
                + DVec3::AxisX() * (gParams.disLeftRight/ 2 - disColumnTGuardrail - gParams.tThickness - cyliHeight / 2)
                + DVec3::AxisY() * (rtor3Rin + sslParam.beamWidth / 2 - cyliDiam)
                + DVec3::AxisZ() * (ladderHeight + bParam.columnHigh);
            auto rMat = Mat3::MakeRotationYZ(DVec3::AxisY(), DVec3::AxisX());
            LadderHandrailCommon::CreateCYLI(_core, *pTopGuardrailNode, cyliHeight, cyliDiam, cyliPosition, rMat.toQuat());

            cyliPosition = cyliPosition + DVec3::AxisX() * (-gParams.disLeftRight + (disColumnTGuardrail + gParams.tThickness) * 2 + cyliHeight);
            LadderHandrailCommon::CreateCYLI(_core, *pTopGuardrailNode, cyliHeight, cyliDiam, cyliPosition, rMat.toQuat());


            auto boxPosition = start
                + DVec3::AxisX() * (gParams.disLeftRight/ 2 - disColumnTGuardrail - gParams.tThickness / 2)
                + DVec3::AxisY() * (rtor3Rin + sslParam.beamWidth / 2 - gParams.tLength / 2)
                + DVec3::AxisZ() * (ladderHeight + bParam.columnHigh);
            LadderHandrailCommon::CreateBOX(_core, *pTopGuardrailNode, gParams.tThickness, gParams.tLength, sslParam.beamWidth, boxPosition);
            auto width = gParams.disLeftRight - disColumnTGuardrail * 2 - gParams.tThickness;
            boxPosition = boxPosition + DVec3::AxisX() * -width;
            LadderHandrailCommon::CreateBOX(_core, *pTopGuardrailNode, gParams.tThickness, gParams.tLength, sslParam.beamWidth, boxPosition);

            rMat = DMat3::MakeRotationXZ(DVec3::AxisX(), DVec3::AxisNZ());
            LadderHandrailCommon::CreateRTOR(_core, *pTopGuardrailNode, (width - gParams.tThickness) / 2, (width + gParams.tThickness) / 2, sslParam.beamWidth, 180
                , boxPosition + DVec3::AxisX() * width / 2 + DVec3::AxisY() * (-gParams.tLength / 2), rMat.toQuat());
        }
    }
    else
    {
        const DVec3 start = DVec3::Zero();
        const auto& gParams = sslParam.gParams;
        // 绘制横杆
        {
            auto pSubsStepNode = LadderHandrailCommon::CreateNode(_core, *pRootNode, "SUBS");
            if (pSubsStepNode == nullptr)
            {
                assert(false);
                return nullptr;
            }
            // 所有的圆柱体旋转是一致的
            auto mat = DMat3::MakeRotationXY(DVec3::AxisZ(), DVec3::AxisY());
            for (int i = 0; i < crossBarCnt; ++i)
            {
                auto high = i * realCrossBarSpacing + sslParam.bottomBarHeight;
                auto center = start + DVec3::AxisZ() * high;
                if (LadderHandrailCommon::CreateCYLI(_core, *pSubsStepNode, crossBarLength, crossBarDiameter, center, mat.toQuat()) == nullptr)
                {
                    assert(false);
                }
            }
        }
        double offset = 0.0;
        // 绘制左右弯梁
        {
            auto createBeam = [&] (WD::WDNode& root, const DVec3& axisY)
            {
                // 弯梁下有4个BOX 和 2偏心方台
                // 下方的两个box
                auto boxSize = DVec3(sslParam.beamThickness, sslParam.beamWidth, ladderHeight + gParams.beamOverHeight);
                auto boxPosition = DVec3::AxisZ() * boxSize.z / 2;
                LadderHandrailCommon::CreateBOX(_core, root, boxSize.x, boxSize.y, boxSize.z, boxPosition);

                boxSize = DVec3(sslParam.beamWidth, sslParam.beamThickness, ladderHeight + gParams.beamOverHeight);
                boxPosition = boxPosition + DVec3::AxisX() * (sslParam.beamWidth - sslParam.beamThickness) / 2
                    + axisY * (sslParam.beamWidth - sslParam.beamThickness) / 2;
                LadderHandrailCommon::CreateBOX(_core, root, boxSize.x, boxSize.y, boxSize.z, boxPosition);
                // 中间的两个棱台体 
                offset = (gParams.disLeftRight - crossBarLength) / 2 - gParams.disColumnTBeamX -  sslParam.beamThickness / 2;
                double high = offset / std::tan(WD::DegToRad(gParams.angle));
                auto pyraPosition = DVec3::AxisX() * offset / 2 + DVec3::AxisZ() * (ladderHeight + gParams.beamOverHeight + high / 2);
                LadderHandrailCommon::CreatePYRA(_core, root, sslParam.beamThickness, sslParam.beamWidth, sslParam.beamThickness, sslParam.beamWidth
                    , high, offset, 0, pyraPosition);

                pyraPosition = pyraPosition + DVec3::AxisX() * (sslParam.beamWidth - sslParam.beamThickness) / 2
                    + axisY * (sslParam.beamWidth - sslParam.beamThickness) / 2;
                LadderHandrailCommon::CreatePYRA(_core, root, sslParam.beamWidth, sslParam.beamThickness, sslParam.beamWidth, sslParam.beamThickness
                    , high, offset, 0, pyraPosition);

                // 上方的两个box
                boxSize = DVec3(sslParam.beamThickness, sslParam.beamWidth, bParam.columnHigh - gParams.beamOverHeight - high + rParam.width / 2);
                boxPosition = DVec3::AxisX() * offset + DVec3::AxisZ() * (ladderHeight + gParams.beamOverHeight + high +  boxSize.z / 2);
                LadderHandrailCommon::CreateBOX(_core, root, boxSize.x, boxSize.y, boxSize.z, boxPosition);

                boxSize = DVec3(sslParam.beamWidth, sslParam.beamThickness, boxSize.z);
                boxPosition = boxPosition + DVec3::AxisX() * (sslParam.beamWidth - sslParam.beamThickness) / 2
                    + axisY * (sslParam.beamWidth - sslParam.beamThickness) / 2;
                LadderHandrailCommon::CreateBOX(_core, root, boxSize.x, boxSize.y, boxSize.z, boxPosition);

            };

            // 左弯梁
            auto pLeftBeamNode = LadderHandrailCommon::CreateNode(_core, *pRootNode, "SUBS");
            if (pLeftBeamNode == nullptr)
            {
                assert(false);
                return nullptr;
            }
            // 右弯梁  -> 为左弯梁的镜像
            auto pRightBeamNode = LadderHandrailCommon::CreateNode(_core, *pRootNode, "SUBS");
            if (pRightBeamNode == nullptr)
            {
                assert(false);
                return nullptr;
            }
            createBeam(*pLeftBeamNode, DVec3::AxisY());
            createBeam(*pRightBeamNode, DVec3::AxisNY());
            auto mat = DMat3::MakeRotationXZ(DVec3::AxisNX(), DVec3::AxisZ());
            pLeftBeamNode->setAttribute("Position WRT Owner",     start + DVec3::AxisX()  * (crossBarLength + sslParam.beamThickness) / 2);
            pRightBeamNode->setAttribute("Position WRT Owner",    start + DVec3::AxisNX() * (crossBarLength + sslParam.beamThickness) / 2);
            pRightBeamNode->setAttribute("Orientation WRT Owner", mat.toQuat());
        }
        // 绘制护栏
        {
            auto createConnectPart = [&] (WD::WDNode& root, const DVec3& axisX)
            {
                LadderHandrailCommon::CreateBOX(_core, root, tBoxX, tBoxY, sslParam.beamThickness, DVec3(0, 0, sslParam.beamThickness / 2));

                auto cyliPosition = axisX * disColumnTGuardrail / 2 + DVec3::AxisZ() * highs[0];
                auto rMat = DMat3::MakeRotationYZ(DVec3::AxisY(), axisX);
                LadderHandrailCommon::CreateCYLI(_core, root, disColumnTGuardrail, bParam.ballDiam / 2, cyliPosition, rMat.toQuat());

                cyliPosition.z = highs[1];
                LadderHandrailCommon::CreateCYLI(_core, root, disColumnTGuardrail, bParam.ballDiam / 2, cyliPosition, rMat.toQuat());

                auto boxPosition = axisX * (disColumnTGuardrail + gParams.tThickness / 2)
                    + DVec3::AxisY() * -(gParams.tLength / 2 - bParam.ballDiam / 4)
                    + DVec3::AxisZ() * highs[0];
                LadderHandrailCommon::CreateBOX(_core, root, gParams.tThickness, gParams.tLength, rParam.width, boxPosition);

                boxPosition.y = boxPosition.y + rParam.width / 4;
                boxPosition.z = highs[1];
                LadderHandrailCommon::CreateBOX(_core, root, gParams.tThickness, gParams.tLength - rParam.width / 2, rParam.width, boxPosition);

                auto rtorPosition = axisX * (disColumnTGuardrail + gParams.tThickness / 2)
                    + DVec3::AxisY() * bParam.ballDiam / 4
                    + DVec3::AxisZ() * highs[0];

                rMat = DMat3::MakeRotationYZ(DVec3::AxisY(), axisX);
                LadderHandrailCommon::CreateRTOR(_core, root, tRtorRin, rParam.width / 2, gParams.tThickness, 180, rtorPosition, rMat.toQuat());

                rtorPosition.z = highs[1];
                LadderHandrailCommon::CreateRTOR(_core, root, tRtorRin, rParam.width / 2, gParams.tThickness, 180, rtorPosition, rMat.toQuat());

                auto cyliHeight =  gParams.disColumnTBeamX - disColumnTGuardrail - gParams.tThickness - sslParam.beamThickness / 2;
                cyliPosition = axisX * (disColumnTGuardrail + gParams.tThickness + cyliHeight / 2)
                    + DVec3::AxisY() * -(gParams.disColumnTBeamY + sslParam.beamThickness / 2)
                    + DVec3::AxisZ() * highs[0];
                LadderHandrailCommon::CreateCYLI(_core, root, cyliHeight, bParam.ballDiam / 2, cyliPosition, rMat.toQuat());

                cyliPosition.z = highs[1];
                LadderHandrailCommon::CreateCYLI(_core, root, cyliHeight, bParam.ballDiam / 2, cyliPosition, rMat.toQuat());
            };

            auto pLeftGuardrailNode = LadderHandrailCommon::CreatePost(_core, *pRootNode, DVec3::Zero(), _commonParamDialog.balustradeParams());
            if (pLeftGuardrailNode == nullptr)
            {
                assert(false);
                return nullptr;
            }
            auto pRightGuardrailNode = LadderHandrailCommon::CreatePost(_core, *pRootNode, DVec3::Zero(), _commonParamDialog.balustradeParams());
            if (pRightGuardrailNode == nullptr)
            {
                assert(false);
                return nullptr;
            }
            createConnectPart(*pLeftGuardrailNode,  DVec3::AxisNX());
            createConnectPart(*pRightGuardrailNode, DVec3::AxisX());
            // 绘制立柱
            DVec3 leftPos = start + DVec3::AxisX() * gParams.disLeftRight / 2
                + DVec3::AxisY() * gParams.disColumnTBeamY
                + DVec3::AxisZ() * ladderHeight;
            pLeftGuardrailNode->setAttribute("Position WRT Owner",    leftPos);
            pRightGuardrailNode->setAttribute("Position WRT Owner",   leftPos + DVec3::AxisNX() * gParams.disLeftRight);
        }
        // 绘制护圈
        {
            auto pTopGuardrailNode = LadderHandrailCommon::CreateNode(_core, *pRootNode, "SUBS");
            if (pTopGuardrailNode == nullptr)
            {
                assert(false);
                return nullptr;
            }
            // 当直梯高度大于临界值时,需要根据高度计算护圈的个数

            // 计算需要护圈的部分的高度
            const double height = ladderHeight - ladderCriticalVal;
            int gCnt = 1;
            if (height > rParam.minHeight)
                gCnt += static_cast<int>(LadderHandrailCommon::DoubleChangeDecimalPlaces((height - rParam.minHeight) / rParam.bestHeight, 0
                , LadderHandrailCommon::ChangeType::CT_RoundUp));

            assert(gCnt > 0);
            double realGHeight = (gCnt > 1) ? height / double(gCnt - 1) : height;
            // 这里提前计算部分参数,避免在循环中重复计算同一个值

            // 垫片BOX的部分参数
            const double gaskBoxXOffset = crossBarLength / 2 + sslParam.beamThickness + rParam.thickness / 2;
            const double gaskBoxYOffset = -sslParam.beamThickness / 2;
            const double gaskBoxLength  = sslParam.beamWidth - sslParam.beamThickness;
            // 斜BOX的部分参数
            const double oBoxWOffset    = (gParams.disLeftRight - crossBarLength) / 2
                - disColumnTGuardrail - sslParam.beamThickness - gParams.tThickness - rParam.thickness;
            const double oBoxLength     = oBoxWOffset / (std::sin(WD::DegToRad(rParam.angle)));
            const double oBoxHOffset    = oBoxWOffset / (std::tan(WD::DegToRad(rParam.angle)));
            const DMat3  oMatL          = DMat3::MakeRotation(rParam.angle, DVec3::AxisZ());
            const DMat3  oMatR          = DMat3::MakeRotation(-rParam.angle, DVec3::AxisZ());
            //延伸BOX的部分参数
            const double eBoxLength     = gParams.tLength - bParam.ballDiam / 4
                - gParams.disColumnTBeamY - sslParam.beamWidth / 2 - oBoxHOffset -  rParam.width / 2;
            // 护圈的部分参数
            const double routSide       = (gParams.disLeftRight) / 2 - disColumnTGuardrail;
            const double rinSide        = routSide - gParams.tThickness;
            const DMat3 rMat            = DMat3::MakeRotationXZ(DVec3::AxisNX(), DVec3::AxisZ());

            // 顶部的护圈
            if (highs.size() == 2)
            {
                DVec3 rPosition = start + DVec3::AxisY() * -(oBoxHOffset + eBoxLength + rParam.width / 2)
                    + DVec3::AxisZ() * (ladderHeight + highs[1]);
                LadderHandrailCommon::CreateRTOR(_core, *pTopGuardrailNode, rinSide, routSide, rParam.width, 180, rPosition, rMat.toQuat());
            }
            // 创建护圈
            for (int i = 0; i < gCnt; ++i)
            {
                double gHeight = i * realGHeight + ladderCriticalVal + rParam.width / 2;
                // 垫片BOX左
                DVec3 boxPosition = start + DVec3(gaskBoxXOffset, gaskBoxYOffset, gHeight);
                LadderHandrailCommon::CreateBOX(_core, *pTopGuardrailNode, rParam.thickness, gaskBoxLength, rParam.width, boxPosition);
                // 垫片BOX右
                boxPosition.x = boxPosition.x - gaskBoxXOffset * 2;
                LadderHandrailCommon::CreateBOX(_core, *pTopGuardrailNode, rParam.thickness, gaskBoxLength, rParam.width, boxPosition);

                // 斜BOX左
                DVec3 oBoxPosition = start + DVec3::AxisX() * (oBoxWOffset / 2 + gaskBoxXOffset + rParam.thickness / 2)
                    + DVec3::AxisY() * -(oBoxHOffset + sslParam.beamWidth) / 2
                    + DVec3::AxisZ() * gHeight;
                LadderHandrailCommon::CreateBOX(_core, *pTopGuardrailNode, rParam.thickness, oBoxLength, rParam.width, oBoxPosition, oMatL.toQuat());
                // 斜BOX右
                oBoxPosition.x = oBoxPosition.x - (crossBarLength + oBoxWOffset + (sslParam.beamThickness + rParam.thickness) * 2);
                LadderHandrailCommon::CreateBOX(_core, *pTopGuardrailNode, rParam.thickness, oBoxLength, rParam.width, oBoxPosition, oMatR.toQuat());

                // 延伸BOX左
                DVec3 eBoxPosition = start + DVec3::AxisX() * (gaskBoxXOffset + oBoxWOffset + rParam.thickness)
                    + DVec3::AxisY() * -(oBoxHOffset + (eBoxLength + sslParam.beamWidth) / 2)
                    + DVec3::AxisZ() * gHeight;
                LadderHandrailCommon::CreateBOX(_core, *pTopGuardrailNode, rParam.thickness, eBoxLength, rParam.width, eBoxPosition);
                // 延伸BOX右
                eBoxPosition.x = eBoxPosition.x - (crossBarLength + (sslParam.beamThickness + rParam.thickness + oBoxWOffset) * 2 + rParam.thickness);
                LadderHandrailCommon::CreateBOX(_core, *pTopGuardrailNode, rParam.thickness, eBoxLength, rParam.width, eBoxPosition);

                // 护圈
                DVec3 rPosition = start + DVec3::AxisY() * -(oBoxHOffset + eBoxLength + sslParam.beamWidth / 2)
                    + DVec3::AxisZ() * gHeight;
                LadderHandrailCommon::CreateRTOR(_core, *pTopGuardrailNode, rinSide, routSide, rParam.width, 180, rPosition, rMat.toQuat());
            }
            // 护圈的三根BOX侧梁
            const double boxHeight = height + bParam.columnHigh + rParam.width / 2;
            auto boxPosition = start + DVec3::AxisX() * (gParams.disLeftRight / 2 - disColumnTGuardrail - rParam.thickness - rParam.thickness / 2)
                + DVec3::AxisY() * -(oBoxHOffset + eBoxLength + sslParam.beamWidth / 2)
                + DVec3::AxisZ() * (ladderCriticalVal + boxHeight / 2);
            LadderHandrailCommon::CreateBOX(_core, *pTopGuardrailNode, rParam.thickness, rParam.width, boxHeight, boxPosition);
            boxPosition.x = boxPosition.x - rinSide * 2 + rParam.thickness;
            LadderHandrailCommon::CreateBOX(_core, *pTopGuardrailNode, rParam.thickness, rParam.width, boxHeight, boxPosition);

            boxPosition = start + DVec3::AxisY() * -(oBoxHOffset + eBoxLength + rinSide - rParam.thickness / 2 + rParam.width / 2)
                + DVec3::AxisZ() * (ladderCriticalVal + boxHeight / 2);

            LadderHandrailCommon::CreateBOX(_core, *pTopGuardrailNode, rParam.width, rParam.thickness, boxHeight, boxPosition);
        }
    }

    pRootNode->setAttribute("Position WRT World", position);
    DQuat ori = DQuat::FromVectors(DVec3::AxisY(), dir);
    pRootNode->setAttribute("Orientation WRT World", ori);
    pRootNode->triggerUpdate(true);
    return pRootNode;
}