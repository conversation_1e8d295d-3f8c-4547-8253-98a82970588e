<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CreateStructureDialog</class>
 <widget class="QDialog" name="CreateStructureDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>399</width>
    <height>103</height>
   </rect>
  </property>
  <property name="focusPolicy">
   <enum>Qt::StrongFocus</enum>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <widget class="QLabel" name="labelName">
     <property name="text">
      <string>Name</string>
     </property>
    </widget>
   </item>
   <item row="0" column="1" colspan="3">
    <widget class="QLineEdit" name="lineEditName"/>
   </item>
   <item row="1" column="0">
    <widget class="QLabel" name="labelPurpose">
     <property name="text">
      <string>Purpose</string>
     </property>
    </widget>
   </item>
   <item row="1" column="1" colspan="3">
    <widget class="QComboBox" name="comboBoxPurpose"/>
   </item>
   <item row="2" column="1">
    <spacer name="horizontalSpacer">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>136</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="2" column="2">
    <widget class="QPushButton" name="pushButtonOk">
     <property name="focusPolicy">
      <enum>Qt::StrongFocus</enum>
     </property>
     <property name="text">
      <string>Ok</string>
     </property>
     <property name="autoDefault">
      <bool>false</bool>
     </property>
    </widget>
   </item>
   <item row="2" column="3">
    <widget class="QPushButton" name="pushButtonCancel">
     <property name="focusPolicy">
      <enum>Qt::StrongFocus</enum>
     </property>
     <property name="text">
      <string>Cancel</string>
     </property>
     <property name="autoDefault">
      <bool>false</bool>
     </property>
    </widget>
   </item>
   <item row="3" column="1">
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>0</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
