#pragma once

#include <QDialog>

#include "ui_CreateStructureDialog.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "nodeTree/WDNodeTree.h"
#include "../../ui.commonLibrary//ui.commonLib.custom/UiNodeNameHelpter.h"

class CreateStructureDialog : public QDialog
{
	Q_OBJECT

public:
    CreateStructureDialog(const std::string& title
        , const std::string& type
        , WD::WDCore& app
		, QWidget *parent = Q_NULLPTR);
	~CreateStructureDialog();

protected:
    virtual void showEvent(QShowEvent* evt) override;
    virtual void hideEvent(QHideEvent* evt) override;

private slots:
    /**
    * @brief 确定按键槽函数
    */
	void slotOkButtonClicked();
    /**
    * @brief 取消按键槽函数
    */
	void slotCancelButtonClicked();

private:
    /**
    * @brief 模型树当前节点改变
    */
    void onNodeTreeCurrentNodeChanged(WD::WDNode::SharedPtr pCurrNode, WD::WDNode::SharedPtr pPrevNode, WD::WDNodeTree& sender);
    /**
    * @brief 初始化下拉框内容
    */
    void initComboBox();
    /**
    * @brief 创建节点
    */
    WD::WDNode::SharedPtr createStructuresNode();
    /**
    * @brief 根据模型树当前选中节点获取管嘴可挂载的父节点
    */
    WD::WDNode::SharedPtr getParentNode() const;
    /**
    * @brief 界面文本翻译
    */
    void retranslateUi();

private:
	Ui::CreateStructureDialog  ui;
    WD::WDCore& _core;
    // 创建类型
    std::string _type;

    // 节点名称助手
    UiNodeNameHelpter _nameHelpter;
};

