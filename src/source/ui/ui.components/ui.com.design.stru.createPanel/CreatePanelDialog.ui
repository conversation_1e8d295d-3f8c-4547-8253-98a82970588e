<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CreatePanelDialog</class>
 <widget class="QDialog" name="CreatePanelDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>584</width>
    <height>515</height>
   </rect>
  </property>
  <property name="focusPolicy">
   <enum>Qt::StrongFocus</enum>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,1">
     <item>
      <widget class="QLabel" name="labelNodeName">
       <property name="text">
        <string>NodeName</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEditNodeName"/>
     </item>
    </layout>
   </item>
   <item row="1" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="0,1">
     <item>
      <widget class="QLabel" name="labelDescription">
       <property name="text">
        <string>Description</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEditDescription"/>
     </item>
    </layout>
   </item>
   <item row="2" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="0,1,0,1">
     <item>
      <widget class="QLabel" name="labelThickness">
       <property name="text">
        <string>Thickness</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QDoubleSpinBox" name="doubleSpinBoxThickness">
       <property name="minimum">
        <double>-100000000000000005366162204393472.000000000000000</double>
       </property>
       <property name="maximum">
        <double>999999999999999993220948674361627976461708441944064.000000000000000</double>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="labelJustification">
       <property name="text">
        <string>Justification</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="comboBoxJustification"/>
     </item>
    </layout>
   </item>
   <item row="3" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_5">
     <item>
      <widget class="QLabel" name="labelType">
       <property name="text">
        <string>CreateMethods</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="comboBoxType"/>
     </item>
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item row="4" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_7">
     <item>
      <widget class="QTableWidget" name="tableWidget"/>
     </item>
     <item>
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <spacer name="verticalSpacer_2">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="pushButtonAdd">
         <property name="focusPolicy">
          <enum>Qt::StrongFocus</enum>
         </property>
         <property name="text">
          <string>Add</string>
         </property>
         <property name="autoDefault">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="pushButtonImport">
         <property name="focusPolicy">
          <enum>Qt::StrongFocus</enum>
         </property>
         <property name="text">
          <string>Import</string>
         </property>
         <property name="autoDefault">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="pushButtonExport">
         <property name="focusPolicy">
          <enum>Qt::StrongFocus</enum>
         </property>
         <property name="text">
          <string>Export</string>
         </property>
         <property name="autoDefault">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="pushButtonDelete">
         <property name="focusPolicy">
          <enum>Qt::StrongFocus</enum>
         </property>
         <property name="text">
          <string>Delete</string>
         </property>
         <property name="autoDefault">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="pushButtonClear">
         <property name="focusPolicy">
          <enum>Qt::StrongFocus</enum>
         </property>
         <property name="text">
          <string>Clear</string>
         </property>
         <property name="autoDefault">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer_3">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item row="5" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_6">
     <item>
      <widget class="QLabel" name="labelWRT">
       <property name="text">
        <string>WRT</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEditWRT"/>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonChange">
       <property name="focusPolicy">
        <enum>Qt::StrongFocus</enum>
       </property>
       <property name="text">
        <string>CE</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="6" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_4">
     <item>
      <layout class="QVBoxLayout" name="verticalLayoutWrt"/>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="labelWarning">
       <property name="text">
        <string>TextLabel</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonOk">
       <property name="focusPolicy">
        <enum>Qt::StrongFocus</enum>
       </property>
       <property name="text">
        <string>Ok</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonCancel">
       <property name="focusPolicy">
        <enum>Qt::StrongFocus</enum>
       </property>
       <property name="text">
        <string>Cancel</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
