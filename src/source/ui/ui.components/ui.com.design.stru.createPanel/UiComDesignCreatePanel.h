#pragma once

#include    <QObject>
#include     "../../wizDesignerApp/UiInterface/UiInterface.h"
#include    "CreatePanelDialog.h"

class UiComDesignCreatePanel
    : public QObject
    , public IUiComponent
{
    Q_OBJECT

public:
    UiComDesignCreatePanel(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject *parent = nullptr);
    ~UiComDesignCreatePanel();

public:
    /**
    *   @brief 通知事件
    *   @param pNotice 事件对象
    */
    virtual void onNotice(UiNotice* pNotice) override;

private:
    CreatePanelDialog*      _pCreatePanelDialog;
    CreatePanelDialog*      _pCreateFloorslab;
};


