#include "NodeListDialog.h"
#include <QAction>
#include <QHeaderView>
#include <QFileDialog>
#include "core/viewer/WDViewer.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "../../ui.commonLibrary/ui.commonLib.weakObject/WeakObject.h"
#include "core/businessModule/WDBMNodeEditUtils.h"
#include "core/undoRedo/WDUndoStack.h"

void BYCommand::performCommand()
{
    if (!processArgs())
        return;

    // 将列表中的节点转为弱指针组
    std::vector<WD::WDNode::WeakPtr> nodes;
    WD::WDNode::Nodes operNodes;
    operNodes.reserve(_nodeList.nodes().size());
    nodes.reserve(_nodeList.nodes().size());
    for (auto pNode : _nodeList.nodes())
    {
        if (pNode == nullptr)
            continue;
        operNodes.emplace_back(pNode);
        nodes.emplace_back(operNodes.back());
    }
    // 校审操作节点列表
    if (!WD::WDBMNodeEditor::NodeMRCheckUpdate(operNodes))
        return ;

    // 使用支持undo/redo的命令移动列表中的节点及其子孙节点
    auto pCmd = new WD::WDBMNodeMoveUndoCommand(nodes, _movePosition);
    _core.undoStack().push(pCmd);
}
bool BYCommand::processArgs()
{
    // 解析命令参数
    // 判断参数个数 个数不匹配则报错
    // 正确参数情况
    // X 100
    // X 100 Y 100
    // X 100 Y 100 Z 100
    auto count = _args.size();

    // 参数个数为偶数，非偶数则参数个数不正确
    if (count % 2 != 0)
    {
        WD_ERROR_T("Command", "An operation was attempted on the element,but due to an error,no operation was performed!");
        return false;
    }

    //判断参数的规则是否正确
    //1、下标为偶数的字符串为
    //X x -X -x
    //Y y -Y -y
    //Z z -Z -z
    std::regex optFormat("^(X|x|-X|-x|Y|y|-Y|-y|Z|z|-Z|-z|E|e|W|w|N|n|S|s|U|u|D|d)$");
    for (int i = 0; i < _args.size(); i += 2)
    {
        // 选项
        std::string opt = _args[i];
        if (!std::regex_match(opt, optFormat))
        {
            WD_ERROR_T("Command", "No action was performed because:Syntax error!");
            return false;
        }
    }
    // 2、下标为奇数的字符串是否为数字
    std::regex valueFormat("^-?\\d+(\\.\\d+)?$");
    for (int i = 1; i < _args.size(); i += 2)
    {
        // 选项
        std::string opt = _args[i];
        if (!std::regex_match(opt, valueFormat))
        {
            WD_ERROR_T("Command", "No action was performed because:Syntax error!");
            return false;
        }
    }

    // 循环从参数数组中每次获取2个参数，第一个为方向 ，第二个为移动距离
    double x = 0, y = 0, z = 0;
    for (int i = 0; i < _args.size(); i += 2)
    {
        // 选项
        std::string opt = _args[i];
        // 参数
        std::string value = _args[i + 1];

        // 如果是X方向
        if (std::regex_match(opt, std::regex("^(X|x|E|e)$")))
        {
            x += std::stod(value);
        }
        else if (std::regex_match(opt, std::regex("^(-X|-x|W|w)$")))
        {
            x -= std::stod(value);
        }
        else if (std::regex_match(opt, std::regex("^(Y|y|N|n)$")))
        {
            y += std::stod(value);
        }
        else if (std::regex_match(opt, std::regex("^(-Y|-y|S|s)$")))
        {
            y -= std::stod(value);
        }
        else if (std::regex_match(opt, std::regex("^(Z|z|U|u)$")))
        {
            z += std::stod(value);
        }
        else if (std::regex_match(opt, std::regex("^(-Z|-z|D|d)$")))
        {
            z -= std::stod(value);
        }
    }
    WD::Vec3 pos(x, y, z);
    _movePosition = pos;
    return true;
}

void ADDCommaand::performCommand()
{
    if (!processArgs())
        return;

    for (auto pNode : _nodeList.nodes())
    {
        if (pNode == nullptr)
            continue;
        _core.scene().add(pNode);
    }
    _core.needRepaint();
}
bool ADDCommaand::processArgs()
{
    auto count = _args.size();

    if (count <= 0)
    {
        return false;
    }
    if (count != 1)
    {
        return false;
    }

    auto opt = _args[0];
    if (opt.empty())
        return false;
    std::transform(opt.begin(), opt.end(), opt.begin(), [](unsigned char c) {return std::toupper(c); });
    if (opt.compare("CE") != 0)
        return false;
    return true;
}

void REMOVECommand::performCommand()
{
    if (!processArgs())
        return;

    for (auto pNode : _nodeList.nodes())
    {
        if (pNode == nullptr)
            continue;
        _core.scene().remove(pNode);
    }
    _core.needRepaint();
}
bool REMOVECommand::processArgs()
{
    auto count = _args.size();

    if (count <= 0)
    {
        return false;
    }
    if (count != 1)
    {
        return false;
    }

    auto opt = _args[0];
    if (opt.empty())
        return false;
    std::transform(opt.begin(), opt.end(), opt.begin(), [](unsigned char c) {return std::toupper(c); });
    if (opt.compare("CE") != 0)
        return false;
    return true;
}
ConvertCommand::ConvertCommand(WD::WDCore& core, std::vector<std::string> args, const WD::NodeList& nodeList)
{
    if (args.size() <= 0)
        return;
    // 通过命令行第一个参数区分命令类型
    std::string commandType = args[0];
    // 移除第一个参数
    args.erase(args.begin(), args.begin() + 1);

    if (commandType.empty())
        return;
    std::transform(commandType.begin(), commandType.end(), commandType.begin(), [](unsigned char c) {return std::toupper(c); });

    if (commandType.compare("BY") == 0)
    {
        BYCommand byCom(core, args, nodeList);
        byCom.performCommand();
    }
    else if (commandType.compare("ADD") == 0)
    {
        ADDCommaand addCom(core, args, nodeList);
        addCom.performCommand();
    }
    else if (commandType.compare("REMOVE") == 0 || commandType.compare("REM") == 0)
    {
        REMOVECommand removeCom(core, args, nodeList);
        removeCom.performCommand();
    }
    else
    {
        WD_WARN_T("Command", "Command is illegality!");
    }
}

NodeListDialog::NodeListDialog(WD::WDCore& core, QWidget* parent)
    : QDialog(parent)
    , _core(core)
    , _createListWidget(core.nodeListMgr(), this)
    , _selectionDialog(core, this)
    , _ImportScriptDialog(core, this)
    , _addFlag(false)
{
    ui.setupUi(this);
    // 去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    // 界面翻译
    retranslateUi();
    // 隐藏表头
    ui.tableWidget->horizontalHeader()->setVisible(false);
    //设置行选中
    ui.tableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    //设置列表不可编辑
    ui.tableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    //自适应列宽
    ui.tableWidget->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);

    //初始化"添加"和"删除"按钮的下拉菜单
    pushBtnAddMenuInit();
    pushBtnRemoveMenuInit();
    // 设置菜单项状态
    updateAddActionState();

    connect(ui.pushBtnAdd, &QPushButton::clicked, this, &NodeListDialog::slotAddPushBtn);
    connect(ui.pushBtnRemove, &QPushButton::clicked, this, &NodeListDialog::slotRemovePushBtn);
    connect(ui.pushBtnAction, &QPushButton::clicked, this, &NodeListDialog::slotActionPushBtnClicked);
    connect(ui.comboBox, QOverload<const QString&>::of(&QComboBox::currentIndexChanged), this, &NodeListDialog::slotCurrentListIndexChanged);
    connect(ui.tableWidget, &QTableWidget::itemPressed, this, &NodeListDialog::slotItemPressed);
    connect(ui.checkBox, &QCheckBox::stateChanged, this, &NodeListDialog::slotHighLightCheckBoxStateChanged);
    connect(&_selectionDialog, &SelectionDialog::sigNodesChanged, this, &NodeListDialog::slotNodesChanged);

    //添加节点树当前item改变通知
    _core.nodeTree().noticeCurrentNodeChanged() += {this, & NodeListDialog::onCurrentNodeChanged};
    // 列表管理对象添加该观察者
    _core.nodeListMgr().observers() += this;
}
NodeListDialog::~NodeListDialog()
{
    //移除节点树当前item改变通知
    _core.nodeTree().noticeCurrentNodeChanged() -= {this, & NodeListDialog::onCurrentNodeChanged};
    // 列表管理对象移除该观察者
    _core.nodeListMgr().observers() -= this;

    //将当前界面从所有的NodeList的观察者中移除
    auto listsName = _core.nodeListMgr().getAllName();
    for(auto listIt : listsName)
    {
        auto pNodeList =_core.nodeListMgr().find(listIt);
        if (pNodeList != nullptr)
        {
            if (pNodeList->observers().contains(this))
                pNodeList->observers() -= this;
        }
    }
    if(_pAddBtnMenu != nullptr)
    {
        _pAddBtnMenu->deleteLater();
    }
    if(_pRemoveBynMenu != nullptr)
    {
        _pRemoveBynMenu->deleteLater();
    }
}

void NodeListDialog::onAddAfter(WD::WDNode::SharedPtr pNode, WD::NodeList& sender)
{
    if (pNode == nullptr)
        return;
    auto pCurrlist = _core.nodeListMgr().current();
    if (pCurrlist == nullptr)
        return;
    if (pCurrlist == &sender)
    {
        addTableWidgetItem(*pNode);
    }
}
void NodeListDialog::onRemoveAfter(WD::WDNode::SharedPtr pNode, WD::NodeList& sender)
{
    if (pNode == nullptr)
        return;
    auto pCurrlist = _core.nodeListMgr().current();
    if (pCurrlist == nullptr)
        return;
    if (pCurrlist == &sender)
    {
        // 节点被移除后，检查表格是否存在该节点的项，存在则移除
        for(int i = 0; i < ui.tableWidget->rowCount(); ++i)
        {
            auto pItem = ui.tableWidget->item(i, NODENNAME);
            if(pItem == nullptr)
                continue;
            auto pItemNode = getItemUData(*pItem);
            if(pItemNode == pNode.get())
            {
                removeTableWidgetItem(*pItem, pNode.get());
                return;
            }
        }
    }
}
void NodeListDialog::onClearBefore(WD::NodeList& sender)
{
    auto pCurrlist = _core.nodeListMgr().current();
    if (pCurrlist == nullptr)
        return;
    if (pCurrlist != &sender)
        return;

    const auto&nodes = sender.nodes();
    for(auto pNode : nodes)
    {
        if(pNode == nullptr)
            continue;
        setNodeHighLight(*pNode, false);
    }
    _core.needRepaint();
}
void NodeListDialog::onClearAfter(WD::NodeList& sender)
{
    auto pCurrlist = _core.nodeListMgr().current();
    if (pCurrlist == nullptr)
        return;
    if (pCurrlist != &sender)
        return;

    clearTableWidget();
}

void NodeListDialog::onCreatAfter(WD::NodeList* pNodeList)
{
    if(pNodeList == nullptr)
        return;
    // 将该界面添加到列表对象中
    pNodeList->observers() += this;

    ui.comboBox->addItem(QString::fromUtf8(pNodeList->name().c_str()));
    // 列表可能被清空了，需要更新菜单项的状态
    updateAddActionState();
}
void NodeListDialog::onDeleteBefore(WD::NodeList* pNodeList)
{
    if(pNodeList == nullptr)
        return;
    // 将该界面添加到列表对象中
    pNodeList->observers() -= this;

    // 移除与pNodeList名称相同的项
    for(int i =  0; i < ui.comboBox->count(); ++i)
    {
        auto text = ui.comboBox->itemText(i);
        if(pNodeList->name().compare(text.toUtf8().toStdString()) == 0)
        {
            ui.comboBox->removeItem(i);
            break;
        }
    }
    // 列表可能被清空了，需要更新菜单项的状态
    updateAddActionState();
}
void NodeListDialog::onCurrentNodeListChanged(WD::NodeList* preNodeList, WD::NodeList* currNodeList)
{
    // 取消前一个列表高亮状态
    if(preNodeList != nullptr)
    {
        for( auto pNode : preNodeList->nodes())
        {
            if(pNode == nullptr)
                continue;
            setNodeHighLight(*pNode, false);
        }
    }
    // 设置列表下拉菜单当前项
    if(currNodeList != nullptr)
    {
        for( int i = 0; i < ui.comboBox->count(); ++i)
        {
            auto text = ui.comboBox->itemText(i).toUtf8().toStdString();
            if(currNodeList->name().compare(text) == 0)
            {
                ui.comboBox->setCurrentIndex(i);
                break;
            }
        }
    }
    updateTableWidget(currNodeList);
    _core.needRepaint();
}

void NodeListDialog::slotAddPushBtn()
{
    _pAddBtnMenu->exec(ui.pushBtnAdd->mapToGlobal(QPoint(0, ui.pushBtnAdd->height())));
}
void NodeListDialog::slotRemovePushBtn()
{
    _pRemoveBynMenu->exec(ui.pushBtnRemove->mapToGlobal(QPoint(0, ui.pushBtnRemove->height())));
}
void NodeListDialog::slotActionPushBtnClicked()
{
    auto pCurrentList = _core.nodeListMgr().current();
    if (pCurrentList == nullptr)
        return;
    // 执行相关命令
    QString commandStr = ui.lineEdit->text();
    QStringList argsList = commandStr.split(" ");
    std::vector<std::string> args;
    for (auto arg : argsList)
    {
        if (arg.compare("") == 0)
            continue;
        args.push_back(arg.toUtf8().toStdString());
    }

    ConvertCommand convertCom(_core, args, *pCurrentList);
    _core.needRepaint();
}
void NodeListDialog::slotCurrentListIndexChanged(const QString& currentListName)
{
    _core.nodeListMgr().setCurrentByName(currentListName.toUtf8().toStdString());
}
void NodeListDialog::slotItemPressed(QTableWidgetItem* item)
{
    if (item == nullptr)
        return;
    auto pNode = getItemUData(*item);
    _core.nodeTree().setCurrentNode(WD::WDNode::ToShared(pNode));
    _core.needRepaint();
}
void NodeListDialog::slotHighLightCheckBoxStateChanged(int state)
{
    // 改变当前列表包含节点的高亮状态
    auto pNodeList = _core.nodeListMgr().current();
    if (pNodeList == nullptr)
        return;
    const auto& nodes = pNodeList->nodes();
    if (nodes.empty())
        return;
    switch (state)
    {
    case Qt::Unchecked:
        {
            for (auto it = nodes.begin(); it != nodes.end(); ++it)
            {
                auto pNode = (*it);
                if (pNode == nullptr)
                    continue;
                setNodeHighLight(*pNode, false);
            }
            _core.sceneForceUpdate();
            _core.needRepaint();
        }
        break;
    case Qt::Checked:
        {
            for (auto it = nodes.begin(); it != nodes.end(); ++it)
            {
                auto pNode = (*it);
                if (pNode == nullptr)
                    continue;
                setNodeHighLight(*pNode, true);
            }
            _core.sceneForceUpdate();
            _core.needRepaint();
        }
        break;
    case Qt::PartiallyChecked:
        break;
    }
}
void NodeListDialog::slotNodesChanged()
{
    auto pNodeList = _core.nodeListMgr().current();
    if (pNodeList == nullptr)
        return;

    const auto& nodes = _selectionDialog.getNodes();
    if (nodes.size() <= 0)
        return;

    // 添加
    if (_addFlag)
    {
        for (auto pWeakNode : nodes)
        {
            auto pShareNode = pWeakNode.lock();
            if(pShareNode == nullptr)
                continue;
            pNodeList->add(pShareNode);
        }
    }
    // 移除
    else
    {
        for (auto pWeakNode : nodes)
        {
            auto pShareNode = pWeakNode.lock();
            if (pShareNode == nullptr)
                continue;
            pNodeList->remove(pShareNode);
        }
    }
}
void NodeListDialog::slotAddBtnActionList()
{
    _createListWidget.show();
}
void NodeListDialog::slotRemoveBtnActionList()
{
    auto pNodeList = _core.nodeListMgr().current();
    if (pNodeList == nullptr)
        return;

    // 列表管理移除当前列表
    _core.nodeListMgr().remove(pNodeList->name());
}
void NodeListDialog::slotRemoveBtnActionAll()
{
    // 清空表格
    clearTableWidget();
    WD::NodeList* nodelist = _core.nodeListMgr().current();
    if (nodelist == nullptr)
        return;

    // 清空当前列表
    nodelist->clear();
}

void NodeListDialog::pushBtnAddMenuInit()
{
    WD::WDCxtTsBg("NodeListDialog");
    _pAddBtnMenu = new QMenu();
    QAction* pActionCE = new QAction;
    pActionCE->setText(QString::fromUtf8(WD::WDCxtTs("CE").c_str()));
    _pAddBtnMenu->addAction(pActionCE);
    connect(pActionCE, &QAction::triggered, this,[this]()
    {
        _addFlag = true;
        actionCETriggered();
    });

    QAction* pActionCEMembers = new QAction;
    pActionCEMembers->setText(QString::fromUtf8(WD::WDCxtTs("CE Members").c_str()));
    _pAddBtnMenu->addAction(pActionCEMembers);
    connect(pActionCEMembers, &QAction::triggered, this, [this]()
    {
        _addFlag = true;
        actionCEMembersTriggered();
    });

    // 模型点选功能还未实现，先屏蔽该选项
    //QAction* pActionIdentified = new QAction;
    //pActionIdentified->setText(QString::fromUtf8(WD::WDCxtTs("Identified").c_str()));
    //_pAddBtnMenu->addAction(pActionIdentified);
    //connect(pActionIdentified, &QAction::triggered, this, [this]()
    //{
    //    this->_addFlag = true;
    //    actionIdentifiedTriggered();
    //});

    QAction* pActionModelBoxSelect = new QAction;
    pActionModelBoxSelect->setText(QString::fromUtf8(WD::WDCxtTs("ModelBoxSelect").c_str()));
    _pAddBtnMenu->addAction(pActionModelBoxSelect);
    connect(pActionModelBoxSelect, &QAction::triggered, this, [this]()
    {
        _addFlag = true;
        actionModelBoxSelectdTriggered();
    });

    QAction* pActionSelection = new QAction;
    pActionSelection->setText(QString::fromUtf8(WD::WDCxtTs("Selection").c_str()));
    _pAddBtnMenu->addAction(pActionSelection);
    connect(pActionSelection, &QAction::triggered, this, [this]()
    {
        _addFlag = true;
        _selectionDialog.setWindowTitle(QString::fromUtf8(WD::WDTs("NodeListDialog", "Add Selection").c_str()));
        _selectionDialog.show();
    });

    QAction* pActionList = new QAction;
    pActionList->setText(QString::fromUtf8(WD::WDCxtTs("List").c_str()));
    _pAddBtnMenu->addAction(pActionList);
    connect(pActionList, SIGNAL(triggered()), this, SLOT(slotAddBtnActionList()));

    QAction* pActionImport = new QAction;
    pActionImport->setText(QString::fromUtf8(WD::WDCxtTs("Import").c_str()));
    _pAddBtnMenu->addAction(pActionImport);
    connect(pActionImport, &QAction::triggered, this, [this]()
    {
        _addFlag = true;
        actionImportScriptTriggered();
    });
    WD::WDCxtTsEd();
}
void NodeListDialog::pushBtnRemoveMenuInit()
{
    WD::WDCxtTsBg("NodeListDialog");
    _pRemoveBynMenu = new QMenu();
    QAction* pActionCE = new QAction;
    pActionCE->setText(QString::fromUtf8(WD::WDCxtTs("CE").c_str()));
    _pRemoveBynMenu->addAction(pActionCE);
    connect(pActionCE, &QAction::triggered, this, [this]()
    {
        _addFlag = false;
        actionCETriggered();
    });

    QAction* pActionCEMembers = new QAction;
    pActionCEMembers->setText(QString::fromUtf8(WD::WDCxtTs("CE Members").c_str()));
    _pRemoveBynMenu->addAction(pActionCEMembers);
    connect(pActionCEMembers, &QAction::triggered, this, [this]()
    {
        _addFlag = false;
        actionCEMembersTriggered();
    });


    // 模型点选功能还未实现，先屏蔽该选项
    //QAction* pActionIdentified = new QAction;
    //pActionIdentified->setText(QString::fromUtf8(WD::WDCxtTs("Identified").c_str()));
    //_pRemoveBynMenu->addAction(pActionIdentified);
    //connect(pActionIdentified, &QAction::triggered, this, [this]()
    //{
    //    _addFlag = false;
    //    actionIdentifiedTriggered();
    //});   
     
    QAction* pActionModelBoxSelect = new QAction;
    pActionModelBoxSelect->setText(QString::fromUtf8(WD::WDCxtTs("ModelBoxSelect").c_str()));
    _pRemoveBynMenu->addAction(pActionModelBoxSelect);
    connect(pActionModelBoxSelect, &QAction::triggered, this, [this]()
    {
        _addFlag = false;
        actionModelBoxSelectdTriggered();
    });


    QAction* pActionAll = new QAction;
    pActionAll->setText(QString::fromUtf8(WD::WDCxtTs("ALL").c_str()));
    _pRemoveBynMenu->addAction(pActionAll);
    connect(pActionAll, SIGNAL(triggered()), this, SLOT(slotRemoveBtnActionAll()));

    QAction* pActionSelection = new QAction;
    pActionSelection->setText(QString::fromUtf8(WD::WDCxtTs("Selection").c_str()));
    _pRemoveBynMenu->addAction(pActionSelection);
    connect(pActionSelection, &QAction::triggered, this, [this]()
    {
        _addFlag = false;
        _selectionDialog.setWindowTitle(QString::fromUtf8(WD::WDTs("NodeListDialog", "Remove Selection").c_str()));
        _selectionDialog.show();
    });

    QAction* pActionList = new QAction;
    pActionList->setText(QString::fromUtf8(WD::WDCxtTs("List").c_str()));
    _pRemoveBynMenu->addAction(pActionList);
    connect(pActionList, SIGNAL(triggered()), this, SLOT(slotRemoveBtnActionList()));
    WD::WDCxtTsEd();
}
void NodeListDialog::actionCETriggered()
{
    //获取节点树当前节点
    auto pNode = _core.nodeTree().currentNode();
    if (pNode != nullptr)
    {
        auto pNodeList = _core.nodeListMgr().current();
        if (pNodeList == nullptr)
            return;
        if (_addFlag)
        {
            pNodeList->add(pNode);           
        }
        else
        {
            pNodeList->remove(pNode);
        }
        _core.sceneForceUpdate();
    }
    else
    {
        WD_WARN_T("NodeListDialog", "please Select Node!");
    }
}
void NodeListDialog::actionCEMembersTriggered()
{
    // 获取当前列表对象
    auto pNodeList = _core.nodeListMgr().current();
    if (pNodeList == nullptr)
        return;

    //获取节点树当前节点
    auto pNode = _core.nodeTree().currentNode();
    if (pNode == nullptr)
    {
        WD_WARN_T("NodeListDialog", "please Select Node!");
        return;
    }

    // 向当前列表对象添加当前节点的子节点
    if (_addFlag)
    {
        pNodeList->add(pNode->children());
    }
    else
    {
        pNodeList->remove(pNode->children());
    }
    _core.sceneForceUpdate();
}
void NodeListDialog::actionIdentifiedTriggered()
{}
void NodeListDialog::actionModelBoxSelectdTriggered()
{
    // 获取当前列表对象
    auto pNodeList = _core.nodeListMgr().current();
    if (pNodeList == nullptr)
        return;

    // 获取场景中选中的节点
    auto sNodes = _core.scene().selectedNodes();
    if (sNodes.size() <= 0)
        return;
    // 将场景中框选的节点集添加或移除
    if (_addFlag)
    {
        pNodeList->add(sNodes);
    }
    else
    {
        pNodeList->remove(sNodes);
    }
    _core.sceneForceUpdate();
}
void NodeListDialog::actionImportScriptTriggered()
{
    // 获取当前列表对象
    auto pNodeList = _core.nodeListMgr().current();
    if (pNodeList == nullptr)
        return;

    // 节点树上搜索包含脚本中名称的节点
    QString fileName = QFileDialog::getOpenFileName(nullptr
        , QString::fromUtf8(WD::WDTs("NodeListDialog", "Import").c_str())
        , ""
        , tr("TXT (*.txt)"));
    if (fileName.isEmpty())
        return;

    const auto nodes = _ImportScriptDialog.updateWidget(fileName);
    _ImportScriptDialog.show();
    if (_addFlag)
    {
        pNodeList->add(nodes);
    }
    _core.sceneForceUpdate();
}
void NodeListDialog::updateAddActionState()
{
    //无列表
    if (ui.comboBox->count() <= 0)
    {
        for (auto pAction : _pAddBtnMenu->actions())
        {
            if(pAction == nullptr)
                continue;
            WD::WDCxtTsBg("NodeListDialog");

            //添加按钮的list选项使能，其余失能
            if(pAction->text() != QString::fromUtf8(WD::WDCxtTs("List").c_str()))
            {
                pAction->setDisabled(true);
            }
            else
            {
                pAction->setDisabled(false);
            }
            WD::WDCxtTsEd();
        }
        //移除按钮都失能
        for (auto pAction : _pRemoveBynMenu->actions())
        {
            if(pAction == nullptr)
                continue;
            pAction->setDisabled(true);
        }
    }
    else
    {
        for (auto pAction : _pAddBtnMenu->actions())
        {
            if(pAction == nullptr)
                continue;
            pAction->setDisabled(false);
        }
        for (auto pAction : _pRemoveBynMenu->actions())
        {
            if (pAction == nullptr)
                continue;
            pAction->setDisabled(false);
        }
    }
}
void NodeListDialog::addTableWidgetItem(WD::WDNode& node)
{
    QTableWidgetItem* item = new QTableWidgetItem(QString::fromUtf8(node.name().c_str()));
    setItemUData(*item, &node);
    //获取行数
    int row = ui.tableWidget->rowCount();
    //在最后插入一行
    ui.tableWidget->insertRow(row);
    //listWidget添加当前节点item
    ui.tableWidget->setItem(row, NODENNAME, item);

    // 高亮被勾选，则节点添加高亮
    auto state = ui.checkBox->checkState();
    if(state == Qt::Checked)
    {
        setNodeHighLight(node, true);
    }
}
void NodeListDialog::removeTableWidgetItem(QTableWidgetItem& item, WD::WDNode* pNode)
{
    // 表格移除项
    ui.tableWidget->removeRow(item.row());

    // 从当前界面移除的节点都要取消高亮效果
    setNodeHighLight(*pNode, false);
}
void NodeListDialog::onCurrentNodeChanged(WD::WDNode::SharedPtr pCurrNode
    , WD::WDNode::SharedPtr pPrevNode
    , WD::WDNodeTree& sender)
{
    WDUnused(sender);
    WDUnused(pPrevNode);

    // 节点树未选中任何节点，则列表表格也不选中任何节点
    if (pCurrNode == nullptr)
    {
        ui.tableWidget->setCurrentCell(-1, NODENNAME);
        return;
    }
    WD::NodeList* nodelist = _core.nodeListMgr().current();
    // nodelist为空，不需要同步列表节点的选中状态
    if (nodelist == nullptr)
        return;

    bool bexist = nodelist->exist(pCurrNode);
    // 在列表不包含节点树的当前节点，则列表的当前行为-1
    if (!bexist)
    {
        ui.tableWidget->setCurrentCell(-1, NODENNAME);
    }
    // 遍历所有项，将data与当前节点相同的项设为当前项
    else
    {
        for (int i = 0; i < ui.tableWidget->rowCount(); ++i)
        {
            QTableWidgetItem* item = ui.tableWidget->item(i, NODENNAME);
            if (item == nullptr)
                continue;
            auto pNode = getItemUData(*item);
            if (pNode == pCurrNode.get())
            {
                ui.tableWidget->setCurrentItem(item);
                return;
            }
        }
    }
}
void NodeListDialog::setNodeHighLight(WD::WDNode& node, bool highlightFlag)
{
    WD::WDNode::RecursionHelpter(node, [this, &highlightFlag](WD::WDNode& node)
    {
        auto flag = node.flags();
        if (highlightFlag)
        {
            flag.addFlag(WD::WDNode::Flag::F_Highlight);
        }
        else
        {
            flag.removeFlag(WD::WDNode::Flag::F_Highlight);
        }
        node.setFlags(flag);
    });
}
void NodeListDialog::updateTableWidget(const WD::NodeList* pList)
{
    clearTableWidget();

    if (pList == nullptr)
        return;

    // 根据NodeList初始化列表GUI
    const auto& nodes = pList->nodes();
    for (auto it = nodes.begin(); it != nodes.end(); ++it)
    {
        auto pNode = *it;
        if (pNode == nullptr)
            continue;
        addTableWidgetItem(*pNode);
    }

    // 设置列表GUI当前项
    auto pCurrentNode = _core.nodeTree().currentNode();
    onCurrentNodeChanged(pCurrentNode, nullptr, _core.nodeTree());
}
void NodeListDialog::clearTableWidget()
{
    auto model = ui.tableWidget->model();
    model->removeRows(0, model->rowCount());
    ui.tableWidget->viewport()->update();
}
void NodeListDialog::retranslateUi()
{
    Trs("NodeListDialog"
    , static_cast<QDialog*>(this)
    , ui.pushBtnAdd
    , ui.pushBtnRemove
    , ui.labelList
    , ui.checkBox
    , ui.pushBtnAction);
}