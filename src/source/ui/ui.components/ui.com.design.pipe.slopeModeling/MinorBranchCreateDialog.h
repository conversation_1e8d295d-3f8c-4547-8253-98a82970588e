#pragma once

#include <QDialog>
#include "ui_MinorBranchCreateDialog.h"
#include "PipeSlopeModelingCommon.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiCESelectHelpter.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiPositionCaptureHelpter.h"
#include "../../ui.commonLibrary//ui.commonLib.custom/UiNodeNameHelpter.h"

class MinorBranchCreateDialog: public QDialog
{
    Q_OBJECT
public:
    MinorBranchCreateDialog(PipeSlopeModelingCommon& common, QWidget*parent = Q_NULLPTR);
    ~MinorBranchCreateDialog();
protected:
    virtual void showEvent(QShowEvent* evt) override;
    virtual void hideEvent(QHideEvent* evt) override;
private slots:
    void slotPushButtonOkClicked();
    void slotPushButtonCancelClicked();
    void slotComboBoxBoreCurrentIndexChanged(int index);
private:
    // 更新管径界面
    void updateBores();
    // 根据模型树当前选中节点获取管线可挂载的父节点
    WD::WDNode::SharedPtr getParentNode() const;
    // 创建分支节点
    bool createBranchNode();
    /**
     * @brief 根据支管的起点坐标,在主管上创建用于连接支管的三通管件
     * @param pMainBranchNode 主管分支节点(三通的父节点)
     * @param minorBranchHPos 支管的起点坐标
     * @param minorBranchSlope 支管的坡度
     * @param outMinorBranchHDir 输出的支管的头方向
    */
    WD::WDNode::SharedPtr createTee(WD::WDNode::SharedPtr pMainBranchNode
        , const WD::DVec3& minorBranchHPos
        , double minorBranchSlope
        , WD::DVec3& outMinorBranchHDir) ;
    // 根据主管与支管计算连接
    bool calcAutoConnection(WD::WDNode& branchNode) ;
private:
    //模型树当前节点改变
    void onNodeTreeCurrentNodeChanged(WD::WDNode::SharedPtr pCurrNode
        , WD::WDNode::SharedPtr pPrevNode
        , WD::WDNodeTree& sender);
    // 界面翻译
    void retranslateUi();
private:
    Ui::MinorBranchCreateDialog ui;
    PipeSlopeModelingCommon&    _common;
    // 保存上一次选择的管径
    std::string                 _bore;
    //
    UiCESelectHelpter           _helpterMainBranch;
    // 
    UiPositionCaptureHelpter    _helpterPos;
    // 节点名称助手
    UiNodeNameHelpter           _nameHelpter;
};
