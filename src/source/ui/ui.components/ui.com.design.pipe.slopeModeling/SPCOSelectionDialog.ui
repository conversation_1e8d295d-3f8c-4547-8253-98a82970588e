<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SPCOSelectionDialog</class>
 <widget class="QDialog" name="SPCOSelectionDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>304</width>
    <height>313</height>
   </rect>
  </property>
  <property name="focusPolicy">
   <enum>Qt::StrongFocus</enum>
  </property>
  <property name="windowTitle">
   <string>Spec Selection</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <item>
    <widget class="QGroupBox" name="groupBoxPSpec">
     <property name="title">
      <string>PSpec</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout">
      <item>
       <widget class="QListWidget" name="listWidgetPSpec"/>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,1,0">
        <item>
         <widget class="QLabel" name="labelISpec">
          <property name="text">
           <string>ISpec</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="comboBoxISpec"/>
        </item>
        <item>
         <widget class="QCheckBox" name="checkBoxInsu">
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="0,1,0">
        <item>
         <widget class="QLabel" name="labelTSpec">
          <property name="text">
           <string>TSpec</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="comboBoxTSpec"/>
        </item>
        <item>
         <widget class="QCheckBox" name="checkBoxTrac">
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonOk">
       <property name="text">
        <string>Ok</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonCancel">
       <property name="text">
        <string>Cancel</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
