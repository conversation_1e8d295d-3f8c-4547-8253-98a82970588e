#include "SlopeEditDialog.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"

SlopeEditDialog::SlopeEditDialog(PipeSlopeModelingCommon& common,QWidget *parent)
    : QDialog(parent)
    , _common(common)
{
    ui.setupUi(this);

    retranslateUi();
}
SlopeEditDialog::~SlopeEditDialog()
{

}
void SlopeEditDialog::retranslateUi()
{
    WD::WDCxtTsBg("SlopeEditDialog");

    auto tr = [](const char* str) -> QString
        {
            return QString::fromUtf8(WD::WDCxtTs(str).c_str());
        };

    WD::WDCxtTsEd();
}