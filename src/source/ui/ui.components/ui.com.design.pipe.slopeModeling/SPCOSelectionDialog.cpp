#include "SPCOSelectionDialog.h"
#include "../../ui.commonLibrary/ui.commonLib.weakObject/WeakObject.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/ICustomWidgets.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiCommon.h"

SPCOSelectionDialog::SPCOSelectionDialog(WD::WDCore& core, PipeSlopeModelingCommon& common, QWidget *parent)
    : QDialog(parent)
    , _common(common)
    , _core(core)
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    retranslateUi();

    // 保温和伴热设置下拉过滤
    ICustomWidgets::QComboBoxPopupCompletion(ui.comboBoxISpec);
    ICustomWidgets::QComboBoxPopupCompletion(ui.comboBoxTSpec);

    connect(ui.pushButtonOk
        , SIGNAL(clicked())
        , this
        , SLOT(slotPushButtonOkClicked()));

    connect(ui.pushButtonCancel
        , SIGNAL(clicked())
        , this
        , SLOT(slotPushButtonCancelClicked()));

}
SPCOSelectionDialog::~SPCOSelectionDialog()
{

}

void SPCOSelectionDialog::updateDefaultSpecToCommon()
{
    // 更新管线等级
    this->updatePSpecList();
    // 更新保温等级
    this->updateISpecList();
    // 更新伴热等级
    this->updateTSpecList();
}

void SPCOSelectionDialog::showEvent(QShowEvent* evt)
{
    // 统一将焦点设置到窗体，用于在窗口打开时清除其他控件焦点
    this->setFocus();
    WDUnused(evt);

    if (_common.iSpec() != nullptr)
    {
        ui.checkBoxInsu->setChecked(true);
    }
    else
    {
        ui.checkBoxInsu->setChecked(false);
    }
    if (_common.tSpec() != nullptr)
    {
        ui.checkBoxTrac->setChecked(true);
    }
    else
    {
        ui.checkBoxTrac->setChecked(false);
    }

    // 更新管线等级
    this->updatePSpecList();
    // 更新保温等级
    this->updateISpecList();
    // 更新伴热等级
    this->updateTSpecList();
}
void SPCOSelectionDialog::hideEvent(QHideEvent* evt)
{
    WDUnused(evt);
}

bool SPCOSelectionDialog::updatePSpecList()
{
    // 清空专业等级列表
    ui.listWidgetPSpec->clear();

    // 获取专业等级列表
    WD::WDNode::Nodes nodes = WD::GetSPECByTEXTAttrSText(_core.getBMCatalog(), Pipe_SPEC_Stext);
    if (nodes.empty())
        return false;

    // 遍历添加SPEC列表项
    auto pSrcPSpec               = _common.pSpec();
    QListWidgetItem* pCurrentItem   = nullptr;
    for (const auto& pNode : nodes)
    {
        if (pNode == nullptr)
            continue;
        QString name = QString::fromUtf8(pNode->name().c_str());
        // 添加列表项
        QListWidgetItem* pItem = new QListWidgetItem(name);
        //QWeakObject
        QVariant userData;
        userData.setValue(UiWeakObject(pNode));
        pItem->setData(Qt::UserRole, userData);
        ui.listWidgetPSpec->addItem(pItem);

        if (pSrcPSpec == nullptr)
        {
            pSrcPSpec = pNode;
            pCurrentItem = pItem;
        }
        else if (pSrcPSpec == pNode)
        {
            pCurrentItem = pItem;
        }
    }

    assert(pSrcPSpec != nullptr);
    assert(pCurrentItem != nullptr);
    _common.setPSpec(pSrcPSpec);
    ui.listWidgetPSpec->setCurrentItem(pCurrentItem);

    return true;
}
bool SPCOSelectionDialog::updateISpecList()
{
    ui.comboBoxISpec->clear();

    // 获取专业等级列表
    WD::WDNode::Nodes nodes = WD::GetSPECByTEXTAttrSText(_core.getBMCatalog(), Insu_SPEC_Stext);
    WD::WDNode::Nodes nodes1 = WD::GetSPECByTEXTAttrSText(_core.getBMCatalog(), Insu_SPEC_Stext1);
    nodes.insert(nodes.end(), nodes1.begin(), nodes1.end());
    if (nodes.empty())
        return false;

    // 遍历添加SPEC列表项
    auto pSrcISpec      = _common.iSpec();
    int currentIndex    = -1;
    for (int i = 0; i < nodes.size(); ++i)
    {
        auto pNode = nodes[i];
        if (pNode == nullptr)
            continue;
        QString name = QString::fromUtf8(pNode->name().c_str());
        //QWeakObject
        QVariant userData;
        userData.setValue(UiWeakObject(pNode));
        ui.comboBoxISpec->addItem(name, userData);
        if (pSrcISpec == nullptr)
        {
            pSrcISpec = pNode;
            currentIndex = i;
        }
        else if (pSrcISpec == pNode)
        {
            currentIndex = i;
        }
    }
    assert(currentIndex >= 0 && currentIndex < ui.comboBoxISpec->count());
    assert(pSrcISpec != nullptr);
    if (ui.checkBoxInsu->isChecked())
        _common.setISpec(pSrcISpec);
    ui.comboBoxISpec->setCurrentIndex(currentIndex);
    return true;
}
bool SPCOSelectionDialog::updateTSpecList()
{
    ui.comboBoxTSpec->clear();

    // 获取专业等级列表
    WD::WDNode::Nodes nodes = WD::GetSPECByTEXTAttrSText(_core.getBMCatalog(), Trac_SPEC_Stext);
    if (nodes.empty())
        return false;

    // 遍历添加SPEC列表项
    auto pSrcTSpec = _common.tSpec();
    int currentIndex = -1;
    for (int i = 0; i < nodes.size(); ++i)
    {
        auto pNode = nodes[i];
        if (pNode == nullptr)
            continue;
        QString name = QString::fromUtf8(pNode->name().c_str());
        //QWeakObject
        QVariant userData;
        userData.setValue(UiWeakObject(pNode));
        ui.comboBoxTSpec->addItem(name, userData);
        if (pSrcTSpec == nullptr)
        {
            pSrcTSpec = pNode;
            currentIndex = i;
        }
        else if (pSrcTSpec == pNode)
        {
            currentIndex = i;
        }
    }
    assert(currentIndex >= 0 && currentIndex < ui.comboBoxTSpec->count());
    assert(pSrcTSpec != nullptr);
    if (ui.checkBoxTrac->isChecked())
        _common.setTSpec(pSrcTSpec);
    ui.comboBoxTSpec->setCurrentIndex(currentIndex);
    return true;
}

void SPCOSelectionDialog::slotPushButtonOkClicked()
{
    // 等级
    {
        auto pSPECItem = ui.listWidgetPSpec->currentItem();
        if (pSPECItem != nullptr)
        {
            auto uData = pSPECItem->data(Qt::UserRole);
            UiWeakObject weakObject = uData.value<UiWeakObject>();
            WD::WDNode::SharedPtr pNode = WD::WDNode::SharedCast(weakObject.object());
            _common.setPSpec(pNode);
        }
    }
    // 保温等级
    if (ui.checkBoxInsu->isChecked())
    {
        auto uData = ui.comboBoxISpec->currentData();
        UiWeakObject weakObject = uData.value<UiWeakObject>();
        WD::WDNode::SharedPtr pNode = WD::WDNode::SharedCast(weakObject.object());
        _common.setISpec(pNode);
    }
    else
    {
        _common.setISpec(nullptr);
    }
    // 伴热等级
    if (ui.checkBoxTrac->isChecked())
    {
        auto uData = ui.comboBoxTSpec->currentData();
        UiWeakObject weakObject = uData.value<UiWeakObject>();
        WD::WDNode::SharedPtr pNode = WD::WDNode::SharedCast(weakObject.object());
        _common.setTSpec(pNode);
    }
    else
    {
        _common.setTSpec(nullptr);
    }
}
void SPCOSelectionDialog::slotPushButtonCancelClicked()
{
    this->reject();
}

void SPCOSelectionDialog::retranslateUi()
{
    WD::WDCxtTsBg("SPCOSelectionDialog");

    auto tr = [](const char* str) -> QString
        {
            return QString::fromUtf8(WD::WDCxtTs(str).c_str());
        };

    this->setWindowTitle(tr("Spec Selection"));
    ui.groupBoxPSpec->setTitle(tr("PSpec"));
    ui.labelISpec->setText(tr("ISpec"));
    ui.labelTSpec->setText(tr("TSpec"));

    ui.pushButtonOk->setText(tr("Ok"));
    ui.pushButtonCancel->setText(tr("Cancel"));

    WD::WDCxtTsEd();
}