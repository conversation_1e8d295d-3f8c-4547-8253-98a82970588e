<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MinorBranchCreateDialog</class>
 <widget class="QDialog" name="MinorBranchCreateDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>303</width>
    <height>307</height>
   </rect>
  </property>
  <property name="focusPolicy">
   <enum>Qt::StrongFocus</enum>
  </property>
  <property name="windowTitle">
   <string>Create Minor Branch</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QFormLayout" name="formLayout">
     <item row="0" column="0">
      <widget class="QLabel" name="labelMainBranch">
       <property name="text">
        <string>Main Branch</string>
       </property>
      </widget>
     </item>
     <item row="0" column="1">
      <layout class="QHBoxLayout" name="horizontalLayout_2">
       <item>
        <widget class="QLineEdit" name="lineEditMainPipeObject"/>
       </item>
       <item>
        <widget class="QPushButton" name="pushButtonCE">
         <property name="maximumSize">
          <size>
           <width>1000</width>
           <height>20</height>
          </size>
         </property>
         <property name="text">
          <string>CE</string>
         </property>
         <property name="autoDefault">
          <bool>false</bool>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item row="1" column="0">
      <widget class="QLabel" name="labelName">
       <property name="text">
        <string>Name</string>
       </property>
      </widget>
     </item>
     <item row="1" column="1">
      <widget class="QLineEdit" name="lineEditName"/>
     </item>
     <item row="2" column="0">
      <widget class="QLabel" name="labelBore">
       <property name="text">
        <string>Bore</string>
       </property>
      </widget>
     </item>
     <item row="2" column="1">
      <widget class="QComboBox" name="comboBoxBore"/>
     </item>
     <item row="3" column="0">
      <widget class="QLabel" name="labelSlope">
       <property name="text">
        <string>Slope</string>
       </property>
      </widget>
     </item>
     <item row="3" column="1">
      <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="1,0">
       <item>
        <widget class="QDoubleSpinBox" name="doubleSpinBoxSlope">
         <property name="decimals">
          <number>3</number>
         </property>
         <property name="minimum">
          <double>-999999999.000000000000000</double>
         </property>
         <property name="maximum">
          <double>999999999.000000000000000</double>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="label">
         <property name="text">
          <string>%</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBoxStartPosition">
     <property name="title">
      <string>Start Position</string>
     </property>
     <property name="checkable">
      <bool>false</bool>
     </property>
     <property name="checked">
      <bool>false</bool>
     </property>
     <layout class="QGridLayout" name="gridLayout" columnstretch="0,1,0" columnminimumwidth="0,0,0">
      <item row="1" column="0">
       <widget class="QLabel" name="label_4">
        <property name="text">
         <string>x:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QDoubleSpinBox" name="doubleSpinBoxPositionX">
        <property name="minimum">
         <double>-9999999999.000000000000000</double>
        </property>
        <property name="maximum">
         <double>9999999999.000000000000000</double>
        </property>
       </widget>
      </item>
      <item row="1" column="2">
       <widget class="QCheckBox" name="checkBoxPositionX">
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="label_5">
        <property name="text">
         <string>y:</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QDoubleSpinBox" name="doubleSpinBoxPositionY">
        <property name="minimum">
         <double>-9999999999.000000000000000</double>
        </property>
        <property name="maximum">
         <double>9999999999.000000000000000</double>
        </property>
       </widget>
      </item>
      <item row="2" column="2">
       <widget class="QCheckBox" name="checkBoxPositionY">
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="label_6">
        <property name="text">
         <string>z:</string>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="QDoubleSpinBox" name="doubleSpinBoxPositionZ">
        <property name="minimum">
         <double>-9999999999.000000000000000</double>
        </property>
        <property name="maximum">
         <double>9999999999.000000000000000</double>
        </property>
       </widget>
      </item>
      <item row="3" column="2">
       <widget class="QCheckBox" name="checkBoxPositionZ">
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item row="0" column="0" colspan="3">
       <widget class="QCheckBox" name="checkBoxCapture">
        <property name="layoutDirection">
         <enum>Qt::RightToLeft</enum>
        </property>
        <property name="text">
         <string>Capture</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonOk">
       <property name="text">
        <string>Ok</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonCancel">
       <property name="text">
        <string>Cancen</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <tabstops>
  <tabstop>lineEditMainPipeObject</tabstop>
  <tabstop>pushButtonCE</tabstop>
  <tabstop>lineEditName</tabstop>
  <tabstop>comboBoxBore</tabstop>
  <tabstop>doubleSpinBoxSlope</tabstop>
  <tabstop>checkBoxCapture</tabstop>
  <tabstop>doubleSpinBoxPositionX</tabstop>
  <tabstop>checkBoxPositionX</tabstop>
  <tabstop>doubleSpinBoxPositionY</tabstop>
  <tabstop>checkBoxPositionY</tabstop>
  <tabstop>doubleSpinBoxPositionZ</tabstop>
  <tabstop>checkBoxPositionZ</tabstop>
  <tabstop>pushButtonOk</tabstop>
  <tabstop>pushButtonCancel</tabstop>
 </tabstops>
 <resources/>
 <connections/>
</ui>
