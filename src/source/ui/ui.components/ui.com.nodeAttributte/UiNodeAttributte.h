#pragma once
#include <QWidget>
#include <QList>
#include "ui_UiNodeAttributte.h"
#include "../../wizDesignerApp/UiInterface/UiInterface.h"
#include "../../ui.commonLibrary/ui.commonLib.attributte/NodeAttributeWidget.h"


class UiNodeAttributteContainer
	: public QWidget
	, public IUiComponent
    , public WD::WDNodeObserver
{
	Q_OBJECT

private:
    struct AttrWidget
    {
        // 只读界面
        NodeAttributeWidget* rWidget = nullptr;
        // 读写界面
        NodeAttributeWidget* rwWidget = nullptr;
    };

private:
    /// 存放语句记录
    QList<QString> _recored;
public:
	UiNodeAttributteContainer(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QWidget *parent = Q_NULLPTR);
    ~UiNodeAttributteContainer();

    /**
    *   @brief 通知事件
    *   @param pNotice 事件对象
    */
    virtual void onNotice(UiNotice* pNotice) override;
	/**
	*   @brief 获取组件中UI对象
	*   @param  name: 一个组件可能有多个窗口，根据名称区分,name == nullpr || "",返回默认窗口
	*/
	virtual QWidget* getWidget(const char* name) override;

public:
    // 节点更新之后通知
    virtual void onNodeUpdateAfter(WD::WDNode::SharedPtr pNode) override;
    // 节点属性改变通知
    virtual void onNodeAttributeValueChanged(const std::string_view& name
        , const WD::WDBMAttrValue& currValue
        , const WD::WDBMAttrValue& prevValue
        , WD::WDNode::SharedPtr pNode);
private:
	// 节点改变通知
	void onNodeChange(WD::WDNode::SharedPtr pCurrNode, WD::WDNode::SharedPtr pPrevNode, WD::WDNodeTree& sender);
private:
    // 翻译
    void retranslateUi();
private:
    Ui::NodeAttributeWidget               ui;
    std::map<std::string, AttrWidget> _typeAttrWidgets;
    //保存前一个窗口
    NodeAttributeWidget*  _pPreWidget;
};
