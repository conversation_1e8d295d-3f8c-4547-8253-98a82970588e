#include "UiComNodeCopy.h"
#include "core/message/WDMessage.h"

UiComNodeCopy::UiComNodeCopy(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject *parent)
    : QObject(parent)
    , IUiComponent(mainWindow, attrs)
{
    _pRotateCopyDialog  =   new RotateCopyDialog(mWindow().core(), mWindow().widget());
    _pOffSetCopyDialog  =   new OffSetCopyDialog(mWindow().core(), mWindow().widget());
    _pMirrorCopyDialog  =   new MirrorCopyDialog(mWindow().core(), mWindow().widget());
}

UiComNodeCopy::~UiComNodeCopy()
{
    if (_pRotateCopyDialog != nullptr)
    {
        delete _pRotateCopyDialog;
        _pRotateCopyDialog = nullptr;
    }
    if (_pOffSetCopyDialog != nullptr)
    {
        delete _pOffSetCopyDialog;
        _pOffSetCopyDialog = nullptr;
    }
    if (_pMirrorCopyDialog != nullptr)
    {
        delete _pMirrorCopyDialog;
        _pMirrorCopyDialog = nullptr;
    }
}


void UiComNodeCopy::onNotice(UiNotice * pNotice)
{
    auto    pCurNode    =   mWindow().core().nodeTree().currentNode();
    int     nType       =   pNotice->type();
    switch (nType)
    {
    case UiNoticeType::UNT_Action:
        {
            UiActionNotice* pActionNotice = static_cast<UiActionNotice*>(pNotice);
            if (pActionNotice->action().is("action.design.node.rotateCopy"))
            {
                if (_pRotateCopyDialog->isHidden())
                    _pRotateCopyDialog->show();
                else
                    _pRotateCopyDialog->activateWindow();
            }
            else if (pActionNotice->action().is("action.design.node.offSetCopy"))
            {
                if (_pOffSetCopyDialog->isHidden())
                    _pOffSetCopyDialog->show();
                else
                    _pOffSetCopyDialog->activateWindow();
            }
            else if (pActionNotice->action().is("action.design.node.mirrorCopy"))
            {
                if (_pMirrorCopyDialog->isHidden())
                    _pMirrorCopyDialog->show();
                else
                    _pMirrorCopyDialog->activateWindow();
            }
        }
        break;
    default:
        break;
    }
}