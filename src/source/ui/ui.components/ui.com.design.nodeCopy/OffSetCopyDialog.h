#pragma once

#include <QDialog>
#include "core/WDCore.h"
#include "core/WDTranslate.h"
#include "core/nodeTree/WDNodeTree.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiCESelectHelpter.h"
#include "ui_OffSetCopyDialog.h"
#include "lineDrawer.h"
#include "viewer/WDViewer.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiListSelectHelpter.h"

class OffSetCopyDialog : public QDialog
{
    Q_OBJECT
public:
    OffSetCopyDialog(WD::WDCore& core, QWidget *parent = Q_NULLPTR);
    ~OffSetCopyDialog();
private:
    using   SharedNodes =   std::vector<WD::WDNode::SharedPtr>;
    using   Nodes       =   UiListSelectHelpter::Nodes;
protected:
    virtual void showEvent(QShowEvent* evt) override;
    virtual void hideEvent(QHideEvent* evt) override;
    virtual void reject() override;
private slots:
    /**
    * @brief 确定按键槽函数
    */
    void slotOkButtonClicked();
    /**
    * @brief 取消按键槽函数
    */
    void slotCancelButtonClicked()
    {
        reject();
    }
private:
    WD::WDScene::SharedPtr scene()
    {
        return _core.viewer().scene();
    }
    void showAxis()
    {
        if (destNode() == nullptr)
        {
            hideAxis();
            return ;
        }
        WD::DMat4 axisMat = WD::DMat4::Compose(destNode()->globalTranslation()
                                            , destNode()->globalRotation()
                                            , {1, 1, 1});
        // 获取浏览轴对象,并设置轴名称
        _core.viewer().browseAxisMgr().get("OffSetCopy").setTransform(axisMat);
    }
    void hideAxis()
    {
        _core.viewer().browseAxisMgr().put("OffSetCopy");
    }
    UiListSelectHelpter::Nodes targetNodes()
    {
        return _listSelectWidget->getSelectedNodes();
    }
    WD::WDNode::SharedPtr destNode()
    {
        return _nodeDestCEWidget->node();
    }
    /**
    * @brief 界面翻译
    */
    void retranslateUi();
private:
    Ui::OffSetCopyDialog               ui;
    WD::WDCore&                 _core;

    UiCESelectHelpter*          _nodeDestCEWidget;
    UiListSelectHelpter*        _listSelectWidget;
};
