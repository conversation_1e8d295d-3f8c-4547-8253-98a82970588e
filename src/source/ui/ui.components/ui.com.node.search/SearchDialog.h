#pragma once

#include "ui_SearchDialog.h"
#include "core/node/WDNode.h"
#include "core/nodeTree/WDNodeTree.h"
#include "core/scene/WDRenderObject.h"
#include "core/viewer/primitiveRender/WDLineRender.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiPositionCaptureHelpter.h"
#include <QDialog>
#include <QCloseEvent>
#include "ResultDialog.h"
class SearchDialog;

WD_NAMESPACE_BEGIN
class FilterBoxRenderObject : public WDRenderObject
{
public:
    FilterBoxRenderObject(SearchDialog& d);
    ~FilterBoxRenderObject();
protected:
    virtual void updateAabb(WDContext& context, const WDScene& scene) override;
    virtual void update(WDContext&, const WDScene&) override;
    virtual void render(WDContext& context, const WDScene&) override;
public:
    WDLineRender line;
private:
    SearchDialog& _d;
};
WD_NAMESPACE_END

class SearchDialog : public QDialog
{
    Q_OBJECT

public:
    SearchDialog(WD::WDCore& core, std::string bmModel, QWidget* parent = nullptr);
    ~SearchDialog();
signals:
    /**
     * @brief 搜索按钮点击信号
    */
    void sigBtnSearchClick();
public:
    /**
     * @brief 获取当前搜索结果包含的属性集
     * @return 
    */
    inline const std::set<std::string>& expression()const
    {
        return _attrList;
    }
    /**
     * @brief 获取搜索结果包含的所有节点
     * @return 搜索结果节点集
    */
    inline const std::vector<WD::WDNode::WeakPtr> resultNodes() const
    {
        return _nodesSet;
    }
protected:
    /**
     * @brief 重写显示事件
     * @param evt 
    */
    virtual void showEvent(QShowEvent* evt) override;
    /**
     * @brief 重写关闭事件
     * @param event 
    */
    virtual void closeEvent(QCloseEvent* event)override;
    /**
     * @brief 获取开始坐标
     *  填的两个坐标里，获取分量最小的坐标
    */
    WD::DVec3 getStartPost();
    /**
     * @brief 获取结束坐标
     *  填的两个坐标里，获取分量最大的坐标
    */
    WD::DVec3 getEndPost();
private:
    /**
     * @brief 界面重画包围盒
    */
    void renderAabb();
    /**
     * @brief 获取包围盒
     * @return 包围盒
    */
    WD::DAabb3 getAabb();
    /**
    * @brief 移除包围盒
    */
    void removeAabb();
    /**
    * @brief 展示拾取结果
    * @param point 拾取的点
    */
    void showCaptureResult(WD::DVec3 point);
    /**
     * @brief 过滤 node 下名称为name，类型属于typeSet的节点
     * @param name 包含的名称
     * @param typeSet 节点类型集
     * @param node 在此节点下过滤符合条件的节点
    */
    void filterByNode(const QString& name, const std::set<QString>& typeSet, const WD::WDNode::SharedPtr node);
    /**
     * @brief 从node开始向下递归获取AABB包含的所有节点
    */
    void getAabb3ContainsNode(WD::WDNode& node, const WD::DAabb3& aabb, const std::set<QString>& typeSet, const QString& name);
    /**
     * @brief 通过AABB包围盒 过滤名称为name，类型属于typeSet的节点
    */
    void filterByAABB(const QString& name, const std::set<QString>& typeSet, const WD::DAabb3& aabb);
    /**
     * @brief 设置搜索对象相关控件使能或失能
     * @param flag true:使能搜索对象相关控件 false:失能
    */
    void setObjectEnable(bool flag);
    /**
     * @brief 设置搜索范围相关控件使能或失能
     * @param flag true:使能搜索范围相关控件 false:失能
    */
    void setRangeEnable(bool flag);
    /**
     * @brief 界面翻译
    */
    void retranslateUi();
private slots:
    /**
     * @brief 搜索按钮槽函数
    */
    void slotSearchBtn();
    /**
     * @brief 当前对象按钮 槽函数
    */
    void slotCEBtn();
    /**
     * @brief 搜索对象被勾选槽函数
    */
    void slotSearchObjectRadioBtn();
    /**
     * @brief 搜索范围复选框点击槽函数
    */
    void slotSearchRangeRadioBtn();
    /**
     * @brief 捕捉开始坐标
     * @param  状态
    */
    void slotCaptureStartPos(int state);
    /**
     * @brief 捕捉结束坐标
     * @param  状态
    */
    void slotCaptureEndPos(int state);
    /**
     * @brief 改变坐标分量，实时绘制线框
    */
    void slotLocationChange(double);
private:
    Ui::SearchDialog ui;
    WD::WDCore&      _core;
    // 当前所在的业务模块名称
    std::string      _bmModel;
    // 开始位置
    WD::FVec3        _startPos = WD::FVec3();
    // 借宿位置
    WD::FVec3        _endPos = WD::FVec3();
    WD::WDNode::WeakPtr _pCurrent;
    // 绘制对象
    WD::FilterBoxRenderObject _renderObject;
    // 捕捉辅助工具
    UiPositionCaptureHelpter  _positionCaptureHelpter;
    // 结果包含的属性集搜索
    std::set<std::string>   _attrList;
    // 搜索结果Nodes
    std::vector<WD::WDNode::WeakPtr>    _nodesSet;

    friend class WD::FilterBoxRenderObject;
};