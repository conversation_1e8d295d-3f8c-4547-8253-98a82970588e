#include "SearchDialog.h"
#include "core/viewer/WDViewer.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/businessModule/typeMgr/WDBMTypeMgr.h"
#include "core/businessModule/typeMgr/WDBMTypeDesc.h"
#include "core/message/WDMessage.h"
#include <QButtonGroup>
#include <QCompleter>
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"


WD_NAMESPACE_BEGIN
FilterBoxRenderObject::FilterBoxRenderObject(SearchDialog& d)
    : WDRenderObject(RL_Scene)
    , _d(d)
{
}
FilterBoxRenderObject::~FilterBoxRenderObject()
{
}

void FilterBoxRenderObject::updateAabb(WDContext& , const WDScene& )
{
    // 更新包围盒
    DAabb3& aabb = this->aabbRef();
    aabb = DAabb3::Null();
    aabb.expandByPoint(DVec3(_d.getStartPost()));
    aabb.expandByPoint(DVec3(_d.getEndPost()));
}
void FilterBoxRenderObject::update(WDContext& context, const WDScene&)
{
    WDUnused(context);
    line.reset();

    FAabb3 tAabb(FVec3(_d.getStartPost()), FVec3(_d.getEndPost()));
    FVec3 cs[8];
    tAabb.corners(cs);
    /*
    *  顶点索引
       1-----2
      /|    /|
     / |   / |
    5-----4  |
    |  0--|--3
    | /   | /
    |/    |/
    6-----7
    */
    line.addLineLoop({ cs[0], cs[1] , cs[2], cs[3] });
    line.addLineLoop({ cs[4], cs[5] , cs[6], cs[7] });
    line.addLineSeg({ cs[1], cs[5], cs[2], cs[4], cs[3], cs[7], cs[0], cs[6] });
}
void FilterBoxRenderObject::render(WDContext& context, const WDScene&)
{
    WDUnused(context);
    WD::WDRLFlag renderLayer = context._renderLayer;
    if (!renderLayer.hasFlag(RL_Scene))
        return;

    line.render(context);
}
WD_NAMESPACE_END

SearchDialog::SearchDialog(WD::WDCore& core, std::string bmModel, QWidget* parent)
    : QDialog(parent)
    , _core(core)
    , _bmModel(bmModel)
    , _renderObject(*this)
    , _positionCaptureHelpter(core)
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    retranslateUi();
    // 位置捕捉助手
    _positionCaptureHelpter.setCaptureTimes(UiPositionCaptureHelpter::CaptureTimes::CT_Repeat);
    auto param = _positionCaptureHelpter.captureParam();
    param.bShowResultCoord = true;
    _positionCaptureHelpter.setCaptureParam(param);
    connect(&_positionCaptureHelpter
        , &UiPositionCaptureHelpter::sigPositionChanged
        , this
        , [=](const WD::DVec3& currPosition, const WD::DVec3& prevPosition, const WD::DMat4& transform)
    {
        WDUnused(prevPosition);
        WDUnused(transform);
        // 添加捕捉的点
        showCaptureResult(currPosition);
    });
    QStringList typeLists;
    //获取 类型管理对象
    auto pBMBase = _core.getBMBase(_bmModel);
    if (pBMBase != nullptr)
    {
        const auto& typeMgr = pBMBase->typeMgr();
        const auto& typeDescs = typeMgr.registedTypes();
        for (auto pTypeDesc : typeDescs)
        {
            if (pTypeDesc == nullptr)
                continue;
            typeLists.push_back(QString::fromUtf8(pTypeDesc->name().c_str()));
        }
        QCompleter* compelter = new QCompleter(typeLists, this);
        compelter->setCaseSensitivity(Qt::CaseInsensitive);
        ui.lineEdit_type->setCompleter(compelter);
    }
    //设置按钮组
    QButtonGroup group = QButtonGroup(this);
    group.addButton(ui.radioButton_searchedObject, 0);
    group.addButton(ui.radioButton_searchedRange, 1);
    setRangeEnable(false);

    //设置坐标分量范围
    ui.doubleSpinBox_startX->setRange(std::numeric_limits<float>::lowest(), std::numeric_limits<float>::max());
    ui.doubleSpinBox_startY->setRange(std::numeric_limits<float>::lowest(), std::numeric_limits<float>::max());
    ui.doubleSpinBox_startZ->setRange(std::numeric_limits<float>::lowest(), std::numeric_limits<float>::max());
    ui.doubleSpinBox_endX->setRange(std::numeric_limits<float>::lowest(), std::numeric_limits<float>::max());
    ui.doubleSpinBox_endY->setRange(std::numeric_limits<float>::lowest(), std::numeric_limits<float>::max());
    ui.doubleSpinBox_endZ->setRange(std::numeric_limits<float>::lowest(), std::numeric_limits<float>::max());

    connect(ui.pushButton_ce, SIGNAL(clicked()), this, SLOT(slotCEBtn()));
    connect(ui.pushButton_search, SIGNAL(clicked()), this, SLOT(slotSearchBtn()));
    connect(ui.radioButton_searchedObject, SIGNAL(pressed()), this, SLOT(slotSearchObjectRadioBtn()));
    connect(ui.radioButton_searchedRange, SIGNAL(pressed()), this, SLOT(slotSearchRangeRadioBtn()));
    //坐标分量改变时，实时更新视图中的线框
    connect(ui.doubleSpinBox_startX, SIGNAL(valueChanged(double)), this, SLOT(slotLocationChange(double)));
    connect(ui.doubleSpinBox_startY, SIGNAL(valueChanged(double)), this, SLOT(slotLocationChange(double)));
    connect(ui.doubleSpinBox_startZ, SIGNAL(valueChanged(double)), this, SLOT(slotLocationChange(double)));
    connect(ui.doubleSpinBox_endX, SIGNAL(valueChanged(double)), this, SLOT(slotLocationChange(double)));
    connect(ui.doubleSpinBox_endY, SIGNAL(valueChanged(double)), this, SLOT(slotLocationChange(double)));
    connect(ui.doubleSpinBox_endZ, SIGNAL(valueChanged(double)), this, SLOT(slotLocationChange(double)));
    connect(ui.checkBox_captureMin, SIGNAL(stateChanged(int)), this, SLOT(slotCaptureStartPos(int)));
    connect(ui.checkBox_captureMax, SIGNAL(stateChanged(int)), this, SLOT(slotCaptureEndPos(int)));
}
SearchDialog::~SearchDialog()
{
}
void SearchDialog::slotSearchBtn()
{
    //清空前一次的搜索结果
    _attrList.clear();
    _nodesSet.clear();

    //获取 类型管理对象
    auto pBMBase = _core.getBMBase(_bmModel);
    if(pBMBase == nullptr)
    {
        assert(false && "业务对象为空");
        return;
    }
    const auto& typeMgr = pBMBase->typeMgr();

    //获取包含名
    QString nameChip = ui.lineEdit_name->text();
    //获取对象类型
    QString typeStr = ui.lineEdit_type->text();
    std::set<QString> typeSet;
    if(typeStr.isEmpty())
    {
        const auto& typeVec = typeMgr.registedTypes();
        for (auto it = typeVec.begin(); it != typeVec.end(); ++it)
        {
            auto pTypeDesc = *it;
            if (pTypeDesc == nullptr)
                continue;
            typeSet.insert(QString::fromUtf8(pTypeDesc->name().c_str()));
            for (auto pAttr : pTypeDesc->attrDescs())
            {
                if (pAttr == nullptr)
                    continue;
                _attrList.insert(pAttr->name());
            }
        }
    }
    else
    {
        QStringList typeList = ui.lineEdit_type->text().split(" ");
        for (auto fItr = typeList.begin(); fItr != typeList.end(); ++fItr)
        {
            auto typeName = (*fItr).toUpper();
            std::string typeNameStd = typeName.toUtf8().data();
            auto pTypeDesc = typeMgr.get(typeNameStd);
            if (pTypeDesc == nullptr)
            {
                assert(false);
                continue;
            }
            typeSet.insert(typeName);
            const auto& attrDescs = pTypeDesc->attrDescs();
            // 获取所有的 属性名
            for (auto pAttr : attrDescs)
            {
                if (pAttr == nullptr)
                    continue;
                _attrList.insert(pAttr->name());
            }
        }
    }

    //选择被搜索对象
    if (ui.radioButton_searchedObject->isChecked())
    {
        if (_pCurrent.lock() == nullptr)
        {
            // 获取节点名称， 从节点树上搜索对应的节点
            QString ceName = ui.lineEdit_ceName->text();
            auto rootNodeCount  = _core.nodeTree().topLevelNodeCount();
            for(size_t i = 0; i < rootNodeCount; ++i)
            {
                _core.nodeTree().fuzzyFindNode(ceName.toUtf8().toStdString(), _core.nodeTree().topLevelNode(i));
            }
        }

        filterByNode(nameChip, typeSet, _pCurrent.lock());
    }
    //选择被搜索范围
    if (ui.radioButton_searchedRange->isChecked())
    {
        filterByAABB(nameChip, typeSet, getAabb());
    }

    emit sigBtnSearchClick();
}
void SearchDialog::slotCEBtn()
{
    auto ceNode = _core.nodeTree().currentNode();
    if (ceNode == nullptr)
    {
        WD_WARN_T("SearchDialog", "nodeTree current node is null, please select search hierarchy node");
        return;
    }
    _pCurrent = ceNode;
    QString nodeName = ceNode->name().c_str();
    ui.lineEdit_ceName->setText(nodeName);
}
void SearchDialog::slotSearchObjectRadioBtn()
{
    //移除包围盒
    removeAabb();
    //退出捕获状态
    _positionCaptureHelpter.exit(false);
    setObjectEnable(true);
    setRangeEnable(false);
}
void SearchDialog::slotSearchRangeRadioBtn()
{
    setRangeEnable(true);
    setObjectEnable(false);
    if (ui.checkBox_captureMin->isChecked() || ui.checkBox_captureMax->isChecked())
    {
        _positionCaptureHelpter.activeCapture();
    }
    renderAabb();
}
void SearchDialog::slotLocationChange(double value)
{
    WDUnused(value);
    renderAabb();
}
WD::DAabb3 SearchDialog::getAabb()
{
    //将float型改为double型三维坐标创建包围盒
    WD::DAabb3 aabb(getStartPost(), getEndPost());
    return aabb;
}
void SearchDialog::removeAabb()
{
    _renderObject.line.reset();
    // 移除绘制对象
    _core.scene().removeRenderObject(&_renderObject);
    _core.needRepaint();
}
WD::DVec3 SearchDialog::getStartPost()
{
    WD::DVec3 start;
    start.x = ui.doubleSpinBox_startX->value();
    start.y = ui.doubleSpinBox_startY->value();
    start.z = ui.doubleSpinBox_startZ->value();

    WD::DVec3 end;
    end.x = ui.doubleSpinBox_endX->value();
    end.y = ui.doubleSpinBox_endY->value();
    end.z = ui.doubleSpinBox_endZ->value();

    //startPos是小的， _endPos存大的
    return WD::DVec3::Min(start, end);
}
WD::DVec3 SearchDialog::getEndPost()
{
    WD::DVec3 start;
    start.x = ui.doubleSpinBox_startX->value();
    start.y = ui.doubleSpinBox_startY->value();
    start.z = ui.doubleSpinBox_startZ->value();

    WD::DVec3 end;
    end.x = ui.doubleSpinBox_endX->value();
    end.y = ui.doubleSpinBox_endY->value();
    end.z = ui.doubleSpinBox_endZ->value();

    //endPos是大的
    return WD::DVec3::Max(start, end);
}
void SearchDialog::showCaptureResult(WD::DVec3 point)
{
    if (ui.checkBox_captureMin->isChecked())
    {
        ui.doubleSpinBox_startX->setValue(point.x);
        ui.doubleSpinBox_startY->setValue(point.y);
        ui.doubleSpinBox_startZ->setValue(point.z);
    }
    else if (ui.checkBox_captureMax->isChecked()) {
        ui.doubleSpinBox_endX->setValue(point.x);
        ui.doubleSpinBox_endY->setValue(point.y);
        ui.doubleSpinBox_endZ->setValue(point.z);
    }
}
void SearchDialog::renderAabb()
{
    // 添加绘制对象
    _core.scene().addRenderObject(&_renderObject);
    _core.needRepaint();
}
void SearchDialog::slotCaptureStartPos(int state)
{
    if (state == Qt::Checked)
    {
        ui.checkBox_captureMax->setChecked(false);
        _positionCaptureHelpter.activeCapture();
    }
    else {
        //退出捕获状态
        _positionCaptureHelpter.exit(false);
    }
}
void SearchDialog::slotCaptureEndPos(int state)
{
    if (state == Qt::Checked)
    {
        ui.checkBox_captureMin->setChecked(false);
        _positionCaptureHelpter.activeCapture();
    }
    else {
        //退出捕获状态
        _positionCaptureHelpter.exit(false);
    }
}

void SearchDialog::showEvent(QShowEvent* evt)
{
    // 统一将焦点设置到窗体，用于在窗口打开时清除其他控件焦点
    this->setFocus();
    WDUnused(evt);
    if (ui.radioButton_searchedRange->isChecked())
    {
        if(ui.checkBox_captureMin->isChecked() || ui.checkBox_captureMax->isChecked())
        {
            _positionCaptureHelpter.activeCapture();
        }
        renderAabb();

        _core.needRepaint();
    }
}
void SearchDialog::closeEvent(QCloseEvent* event)
{
    removeAabb();
    event->accept();
    //退出捕获状态
    _positionCaptureHelpter.exit(true);
}

void SearchDialog::filterByNode(const QString& name, const std::set<QString>& typeSet, const WD::WDNode::SharedPtr pNode)
{
    // 搜索的层级节点为空，则不通过节点树层次搜索
    if(pNode == nullptr)
    {
        return;
    }
    //递归子孙节点，获取类型匹配的节点
    WD::WDNode::RecursionHelpter(*pNode, [&typeSet, &name, this](WD::WDNode& node)
    {
        QString nodeName = node.name().c_str();
        if (nodeName.contains(name))
        {
            QString nodeType = QString::fromUtf8(node.type().data());
            auto itr = typeSet.find(nodeType);
            // 收集名称和类型都匹配的节点
            if (itr != typeSet.end())
            {
                _nodesSet.push_back(WD::WDNode::ToShared(&node));
            }
        }
    });
}
void SearchDialog::getAabb3ContainsNode(WD::WDNode& node, const WD::DAabb3& aabb, const std::set<QString>& typeSet, const QString& name)
{
    //如果两个盒子不相交，则不必再遍历子节点
    if (!aabb.intersects(node.aabb()))
        return;

    //如果aabb包含节点的包围盒，递归该节点
    if (aabb.contains(node.aabb()))
    {
        filterByNode(name, typeSet, WD::WDNode::ToShared(&node));
        return;
    }
    //不包含仅相交继续遍历子节点
    for (auto child : node.children())
    {
        if (child == nullptr)
            continue;
        getAabb3ContainsNode(*child, aabb, typeSet, name);
    }
}
void SearchDialog::filterByAABB(const QString& name, const std::set<QString>& typeSet, const WD::DAabb3& aabb)
{
    //根节点开始遍历所有的子节点
    auto pRoot = _core.getBMDesign().root();
    if (pRoot != nullptr)
    {
        getAabb3ContainsNode(*pRoot, aabb, typeSet, name);
    }
}
void SearchDialog::setObjectEnable(bool flag)
{
    ui.lineEdit_ceName->setEnabled(flag);
    ui.pushButton_ce->setEnabled(flag);
}
void SearchDialog::setRangeEnable(bool flag)
{
    ui.doubleSpinBox_startX->setEnabled(flag);
    ui.doubleSpinBox_startY->setEnabled(flag);
    ui.doubleSpinBox_startZ->setEnabled(flag);
    ui.doubleSpinBox_endX->setEnabled(flag);
    ui.doubleSpinBox_endY->setEnabled(flag);
    ui.doubleSpinBox_endZ->setEnabled(flag);
    ui.checkBox_captureMin->setEnabled(flag);
    ui.checkBox_captureMax->setEnabled(flag);
}
void SearchDialog::retranslateUi()
{
    Trs("SearchDialog"
        ,static_cast<QDialog*>(this)
        , ui.pushButton_search
        , ui.label_name
        , ui.label_type
        , ui.radioButton_searchedObject
        , ui.pushButton_ce
        , ui.radioButton_searchedRange
        , ui.checkBox_captureMin
        , ui.checkBox_captureMax
    );
}