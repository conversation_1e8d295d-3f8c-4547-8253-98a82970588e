#include "ResultDialog.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/businessModule/catalog/WDBMCatalog.h"
#include "core/businessModule/typeMgr/WDBMTypeMgr.h"
#include "core/nodeTree/WDNodeTree.h"
#include "core/viewer/WDViewer.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "../../ui.commonLibrary/ui.commonLib.excel/QTableWidget2Excel.h"
#include <QMenu>
#include <QFileDialog>
#include <QHeaderView>
#include <QTableWidgetItem>
#include <QMouseEvent>
#include <QCommonStyle>
#include <QLineEdit>
#include <QModelIndexList>
#include "core/message/WDMessage.h"
#include "core/businessModule/WDBMClaimMgr.h"

ResultDialog::ResultDialog(WD::WDCore& core, const std::string model, QWidget* parent)
    :QDialog(parent)
    , _core(core)
    , _bmModel(model)
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    retranslateUi();

    // 创建 model 和 view
    _model = new QStandardItemModel();
    ui.tableWidget->setModel(_model);

    _delegate = new ResultDelegate(0, ui.tableWidget);
    ui.tableWidget->setItemDelegate(_delegate);

    _modifyMenu = new QMenu(this);
    QAction* forwardAct = _modifyMenu->addAction(WD::WDTs("ResultDialog", "Forward").c_str());
    connect(forwardAct, &QAction::triggered, this, &ResultDialog::slotCopyForward);
    QAction* backAct = _modifyMenu->addAction(WD::WDTs("ResultDialog", "Back").c_str());
    connect(backAct, &QAction::triggered, this, &ResultDialog::slotCopyBack);

    _columnSetupDialog = new ColumnSetupDialog(core, _bmModel, this);
    _columnSetupDialog->hide();

    ui.tableWidget->horizontalHeader()->setStretchLastSection(true);
    ui.tableWidget->horizontalHeader()->setSectionResizeMode(QHeaderView::Interactive);
    ui.tableWidget->horizontalHeader()->setSelectionMode(QAbstractItemView::SingleSelection);
    ui.tableWidget->horizontalHeader()->setSelectionBehavior(QAbstractItemView::SelectItems);
  
    connect(_delegate, &ResultDelegate::textChanged, this, &ResultDialog::slotLineEditTextChanged);
    connect(ui.tableWidget->selectionModel(), &QItemSelectionModel::currentChanged, this, &ResultDialog::slotItemChanged);
    connect(_columnSetupDialog, &ColumnSetupDialog::sigUpdateWidget, this, &ResultDialog::updateWidget);
    connect(ui.tableWidget, &SearchResultWidget::sigOpenColumnSetupDialog,[this]()
    {
        _columnSetupDialog->show();
    });
    connect(ui.tableWidget, &SearchResultWidget::sigClearWidget,[this]()
    {
        int rowCount = _model->rowCount();
        _model->removeRows(0, rowCount);
        _model->setRowCount(0);
        this->clear();
    });
    connect(ui.tableWidget, &SearchResultWidget::sigExportExcel,[this]()
    {
        QString fileName = QFileDialog::getSaveFileName(nullptr, QString::fromUtf8(WD::WDTs("ResultDialog", "Save Search Reault").c_str()), "", "Excel File(*.xlsx)");

        if(fileName.isEmpty())
            return;

        QString suffix(".xlsx");
        if (!fileName.endsWith(suffix, Qt::CaseInsensitive))
            fileName += suffix;

        QTableWidget2Excel* pExcel = new QTableWidget2Excel();
        if (pExcel == nullptr)
        {
            assert(false && "pExcel为空");
            return;
        }
            
        auto attrs = _columnSetupDialog->getAttr();

        QTableWidget2Excel::ExcelData sheetData;
        //获取表头数据
        {
            std::vector<std::string> headData;
            headData.reserve(attrs.size());
            for(auto attr : attrs)
            {
                headData.emplace_back(attr.second);
            }
            sheetData.emplace_back(headData);
        }

        // 将未隐藏的行数据导出,不包含0行，从第1行开始
        for(int i = 1; i < _model->rowCount(); ++i)
        {
            if(!ui.tableWidget->isRowHidden(i))
            {
                int index  = i - 1;
                if(index >= _nods.size())
                    continue;
                auto pNode  = _nods[index].lock();
                if(pNode == nullptr)
                    continue;

                std::vector<std::string> rowData;
                rowData.reserve(attrs.size());
                for (auto attr : attrs)
                {
                    std::string value = WD::WDBMAttrValue::ToString(pNode->getAttribute(attr.first));
                    rowData.emplace_back(value);
                }
                sheetData.emplace_back(rowData);
            }
        }

        auto ret = pExcel->exportExcelByTwoArray(fileName, sheetData);
        delete pExcel;
        pExcel = nullptr;

        if(ret)
        {
            WD_INFO_T("ResultDialog", "export succeed");
        }
        else
        {
            WD_WARN_T("ResultDialog", "export failure");
        }
    });

    connect(ui.tableWidget, &SearchResultWidget::sigRemoveCurrentRow,[this]()
    {
        auto pSelectModel = ui.tableWidget->selectionModel();
        if (pSelectModel == nullptr)
        {
            assert(false && "表格的选择模型为空");
            return;
        }
        // 移除当前选中的行,移除会影响行号，先获取左右的行号，再从后向前移除
        const auto& selectModelIndexs = pSelectModel->selectedIndexes();
        std::set<int> rowsIndexSet;
        for (const QModelIndex& modelIndex : selectModelIndexs)
        {
            int row = modelIndex.row();
            rowsIndexSet.insert(row);
        }
        for(auto it  = rowsIndexSet.rbegin(); it != rowsIndexSet.rend(); ++it)
        {
            int index = *it - 1;
            if(index < 0 || index >= _nods.size())
                continue;
            // 移除观察者
            auto pNode = _nods[index].lock();
            if(pNode != nullptr)
            {
                pNode->observers() -= this;
            }
            _nods.erase(_nods.begin() + index);
            _model->removeRow(*it);
        }
    });
    connect(ui.tableWidget, &SearchResultWidget::sigNavigateTo,[this]()
    {
        int row = ui.tableWidget->currentIndex().row() - 1;
        if(row < 0 || row >= _nods.size())
            return;
        _core.nodeTree().setCurrentNode(_nods[row].lock());
    });
    connect(ui.tableWidget, &SearchResultWidget::sigLoad3DView,[this]()
    {
        auto pSelectModel = ui.tableWidget->selectionModel();
        if(pSelectModel== nullptr)
        {
            assert(false && "表格的选择模型为空");
            return;
        }
        auto selectModelIndexs = pSelectModel->selectedIndexes();
        for(QModelIndex modelIndex : selectModelIndexs)
        {
            int index = modelIndex.row() - 1;
            if(index < 0 || modelIndex.row() - 1 >= _nods.size())
                continue;
            _core.scene().add(_nods[index].lock());
        }
        _core.needRepaint();
    });
    connect(ui.tableWidget, &SearchResultWidget::sigModifyFinish, [this](const QModelIndex& modelIndex, const QString& oldValue, const QString& newValue)
    {
        auto nRet = modifyValue(modelIndex, newValue);
        if(!nRet)
        {
            // 还原旧值
            // 获取当前行
            auto row = modelIndex.row();
            // 获取当前列
            auto col = modelIndex.column();

            auto pTypeItem = _model->item(row, col);
            if (pTypeItem == nullptr)
                return;
            pTypeItem->setText(oldValue);
        }
    });
}
ResultDialog::~ResultDialog()
{
    this->clear();
    if (_delegate != nullptr)
    {
        delete _delegate;
        _delegate = nullptr;
    }
    if (_model != nullptr)
    {
        delete _model;
        _model = nullptr;
    }
    if (ui.tableWidget != nullptr)
    {
        delete ui.tableWidget;
        ui.tableWidget = nullptr;
    }
    if (_columnSetupDialog != nullptr)
    {
        delete _columnSetupDialog;
        _columnSetupDialog = nullptr;
    }
    if (_modifyMenu != nullptr)
    {
        delete _modifyMenu;
        _modifyMenu = nullptr;
    }    
}

bool ContainsAttr(const WD::WDNode& node, const std::string_view& attrName)
{
    auto p = node.getTypeDesc();
    if(p == nullptr)
        return false;
    return p->contains(attrName);   
}
void ResultDialog::upDateData(const std::vector<WD::WDNode::WeakPtr>& nodes, const std::set<std::string>& expression)
{
    this->clear();
    _nods = nodes;
    _columnSetupDialog->updateComboBox(expression);
    updateWidget();
}
bool ResultDialog::modifyValue(const QModelIndex& modelIndex, const QVariant& value)
{
    // 获取编辑的文本 
    if(!modelIndex.isValid())
    {
        assert(false);
        return false;
    }

    //获取属性名和节点
    auto row     = modelIndex.row();
    // 实际第0行为模糊搜索行，无对应的节点,无需修改
    if(row == 0)
        return true;
    row -= 1;
    auto column = modelIndex.column();
    auto attrs = _columnSetupDialog->getAttr();
    
    if(column < 0 || column>= attrs.size())
        return false;
    auto attr = attrs[column];
    if(row < 0 || row >= _nods.size())
        return false;
    auto pNode = _nods[row].lock();
    if(pNode == nullptr)
    {
        assert(false && "modify value,but node is null");
        return false;
    }
    auto pBm = pNode->getBMBase();
    if (pBm == nullptr)
        return false;
    
    // 修改节点属性的值
    auto pNodeDesc = pNode->getTypeDesc();
    if(pNodeDesc == nullptr)
    {
        assert(false);
        return false;
    }
    
    // 获取属性名称
    auto attrName = attr.first;
    // 申领对象
    bool bCancelMd = false;
    if (!pBm->claimMgr().checkUpdate(pNode, { attrName }, bCancelMd))
        return false;
    if (bCancelMd)
        return false;

    std::string newValue = value.toString().toUtf8().data();
	if(attrName == "Name")
    {
        auto oldName = pNode->name();
        // 判断新名称是否有效
        auto nameIsValid = pBm->nameValid(std::string_view(newValue));
        if(nameIsValid)
        {
            // 判断名称是否发生改变
            if(newValue == oldName)
            {
                return true;
            }
            // 判断是否重名
            auto nameIsExist = pBm->nameExists(std::string_view(newValue));
            if (nameIsExist)
            {
                auto info = WD::WDTs("ResultDialog", "item");
                info.append("(");
                info.append(std::to_string(row + 1));
                info.append(", ");
                info.append(std::to_string(column + 1));
                info.append(WD::WDTs("ResultDialog", "modify name failure, name exited"));
                WD_WARN(info);
                return false;
            }
            else
            {
                pNode->setAttribute("Name", newValue);
                pNode->update(true);
                return true;
            }
        }
        else
        {
            auto info = WD::WDTs("ResultDialog", "item");
            info.append("(");
            info.append(std::to_string(row + 1));
            info.append(", ");
            info.append(std::to_string(column + 1));
            info.append(WD::WDTs("ResultDialog", "modify name failure, name illegal"));
            info.append(")");
            WD_WARN(info);
            return false;
        }
    }
    else
    {
        bool nRet = false;
        auto pTypeAttr = pNodeDesc->get(std::string_view(attrName));
        if (pTypeAttr == nullptr)
        {
            //assert(false && "AttributeType对象指针为空");
            return false;
        }

        // 将QString转换为属性值对应的类型
        auto type = pTypeAttr->type();
        WD::WDBMAttrValue typeAttrValid;
        if(type == WD::WDBMAttrValueType::T_NodeRef)
        {
            // 节点树查找对应的节点
            auto p = _core.getBMCatalog().findNode(newValue);
            if (p == nullptr)
            {
                // 元件模块未找到，设计模块查找
                p = _core.getBMDesign().findNode(newValue);
            }
            if(p == nullptr)
            {
                // 提示修改失败！
                WD_WARN_T("ResultDialog", "Modify value failure");
                return false;
            }

            typeAttrValid = WD::WDBMAttrValue(WD::WDBMNodeRef(p));
        }
        else
        {
            typeAttrValid = WD::WDBMAttrValue::FromString(type, std::string_view(newValue));
        }

        if (!typeAttrValid.valid())
        {
            return false;
        }
        nRet = pNode->setAttribute(attr.first, typeAttrValid);
        pNode->update(true);
        if (!nRet)
        {
            auto info = WD::WDTs("ResultDialog", "item");
            info.append("(");
            info.append(std::to_string(row + 1));
            info.append(", ");
            info.append(std::to_string(column + 1));
            info.append(WD::WDTs("ResultDialog", "modify value error"));
            WD_WARN(info);
        }
        return nRet;
    }
    return false;
}
void ResultDialog::updateWidget()
{
    auto& attrs = _columnSetupDialog->getAttr();
    int columnCount = static_cast<int>(attrs.size());

    _model->clear();
    // 默认为2列
    _model->setColumnCount(columnCount);
    // 行数为节点个数
    _model->setRowCount(static_cast<int>(_nods.size()) + 1);

    // 设置表头 和过滤行
    for (int i = 0; i < columnCount; ++i)
    {
        // 表头
        std::string HeadingStr = attrs[i].second.c_str();
        QString HeadText = QString::fromUtf8(HeadingStr.c_str());
        _model->setHeaderData(i, Qt::Horizontal, HeadText);
        // 过滤行
        _model->setItem(0, i, new QStandardItem());
    }


    // 设置单元格数据同步设置垂直表头
    _model->setHeaderData(0, Qt::Vertical, " ");
    for (int i = 0; i < _nods.size(); ++i)
    {
        auto pNode = _nods[i].lock();
        if (pNode == nullptr)
            continue;
        // 将当前界面添加到该节点的观察者中
        pNode->observers() += this;

        for (int j = 0; j < columnCount; ++j)
        {
            std::string_view attrName(attrs[j].first);
            //不支持的属性不可编辑
            if (!ContainsAttr(*pNode, attrName))
            {
                _model->setItem(i+1, j, new QStandardItem());
                auto pItem = _model->item(i+1, j);
                if (pItem == nullptr)
                    continue;
                pItem->setIcon(QIcon(":/warning.png"));
                pItem->setEditable(false);
                continue;
            }
            auto value = pNode->getAttribute(attrName);
            std::string valueStr = "";
            if(value.type() == WD::WDBMAttrValue::Type::T_NodeRef)
            {
                auto p = value.toNodeRef().refNode();
                if(p != nullptr)
                    valueStr = p->name();
            }
            else
            {
                valueStr = WD::WDBMAttrValue::ToString(value);
            }
            auto index = _model->index(i + 1, j, QModelIndex());
            _model->setData(index, valueStr.c_str());
            // Type 列不可编辑
            if (j == 1)
            {
                auto pTypeItem = _model->item(i + 1, j);
                if (pTypeItem == nullptr)
                    continue;
                pTypeItem->setEditable(false);
            }
        }
        _model->setHeaderData(i+1, Qt::Vertical, QVariant(i+1));
    }
}
void ResultDialog::filterColumn(int column, const QString& text)
{
    for (int row = 1; row < _model->rowCount(); ++row)
    {
        auto pItem = _model->item(row, column);
        if(pItem == nullptr)
            return;
        bool match = pItem->text().contains(text, Qt::CaseInsensitive);
        if(match)
        {
            //// 匹配当前列的搜索条件， 显示
            //ui.tableWidget->setRowHidden(row, !match);

            // 不匹配当前列的搜索条件，判断其他列的是否满足搜索条件 满足则显示
            for(int col = 0; col < _model->columnCount(); ++col)
            {
                if(column == col)
                    continue;
                auto pFilterItem = _model->item(0, col);
                if(pFilterItem == nullptr)
                    continue;
                auto filterText = pFilterItem->text();
                if(filterText.isEmpty())
                    continue;
                auto pBrotherItem = _model->item(row, col);
                if(pBrotherItem == nullptr)
                    continue;
                auto brotherText = pBrotherItem->text();
                match &= brotherText.contains(filterText, Qt::CaseInsensitive);
                if(!match)
                    break;
            }
            // 匹配当前列的搜索条件， 显示
            ui.tableWidget->setRowHidden(row, !match);
        }
        else
        {
            // 隐藏
            ui.tableWidget->setRowHidden(row, !match);
        }
    }
}
void ResultDialog::clear()
{
    //将原数据清空， 并移除所有观察者
    for (auto pWeakNode : _nods)
    {
        auto pNode = pWeakNode.lock();
        if (pNode == nullptr)
            continue;
        // 将当前界面从节点的观察者中移除
        pNode->observers() -= this;
    }
    _nods.clear();
}
void ResultDialog::retranslateUi()
{
    Trs("ResultDialog"  
        , static_cast<QDialog*>(this)
    );
}
void ResultDialog::slotCopyForward()
{
    auto pSelectedModel = ui.tableWidget->selectionModel();
    if (pSelectedModel == nullptr)
    {
        assert(false && "表格的选择模型为空");
        return;
    }
    auto selectedModelIndexList = pSelectedModel->selectedIndexes();

    auto lastModelIndex = selectedModelIndexList.last();
    auto pLastItem = _model->item(lastModelIndex.row(), lastModelIndex.column());
    if (pLastItem == nullptr)
        return;
    QVariant value = pLastItem->text();

    // 向前拷贝
    for (int i = 0; i < selectedModelIndexList.size()-1; ++i)
    {
        auto modelIndex = selectedModelIndexList.at(i);
        if(modifyValue(modelIndex, value))
        {
            auto pItem = _model->item(modelIndex.row(), modelIndex.column());
            if (pItem == nullptr)
                continue;
            pItem->setText(value.toString());
        }
    }
}
void ResultDialog::slotCopyBack()
{
    auto pSelectedModel = ui.tableWidget->selectionModel();
    if (pSelectedModel == nullptr)
    {
        assert(false && "表格的选择模型为空");
        return;
    }
    auto selectedModelIndexList = pSelectedModel->selectedIndexes();
    auto firstModelIndex = selectedModelIndexList.first();
    auto pFirstItem = _model->item(firstModelIndex.row(), firstModelIndex.column());
    if (pFirstItem == nullptr)
        return;
    QVariant value = pFirstItem->text();

    // 向后拷贝
    for (int i = 1; i < selectedModelIndexList.size(); ++i)
    {
        auto modelIndex = selectedModelIndexList.at(i);
        if(modifyValue(modelIndex, value))
        {
            auto pItem = _model->item(modelIndex.row(), modelIndex.column());
            if(pItem == nullptr)
                continue;
            pItem->setText(value.toString());
        }
    }
}
void ResultDialog::slotLineEditTextChanged(const QString& text)
{
    auto modelIndex = ui.tableWidget->currentIndex();
    if(!modelIndex.isValid())
    {
        return;
    }
    int column = modelIndex.column();
    filterColumn(column, text);
}
void ResultDialog::slotItemChanged(const QModelIndex& current, const QModelIndex& previous)
{
    WDUnused(current);
    if(!previous.isValid())
    {
        return;
    }
    // 为第0行，不需要修改值
    int row = previous.row();
    if(row <= 0)
        return;
    int col = previous.column();
    // 非法列和第2列不检测
    if(col < 0 || col == 1)
        return;

    // 获取对应的节点，修改值
    auto pItem = _model->item(row, col);
    if(pItem == nullptr)
    {
        assert(false && "前一item为空");
        return;
    }
    QVariant newValue  = pItem->text();
    // 获取节点的属性值
    int index = row - 1;
    if (index < 0 || index >= _nods.size())
    {
        //assert(false && "数组下标越界!");
        return;
    }
    // 设置为节点该属性值
    auto pNode = _nods[index].lock();
    if (pNode == nullptr)
        return;
    // 获取节点该列对应的属性值
    const auto& attrs = _columnSetupDialog->getAttr();
    if (col >= attrs.size())
        return;
    auto attrIt = attrs[col];
    auto value = pNode->getAttribute(std::string_view(attrIt.first));
    auto valueStr = WD::WDBMAttrValue::ToString(value);

    // 单元格当前文本与属性值不相等，表示进行了修改
    if(valueStr.compare(newValue.toString().toUtf8()) == 0)
        return;
    if (!modifyValue(previous, newValue))
    {
        pItem->setText(QString::fromUtf8(valueStr.c_str()));
    }
}
void ResultDialog::resizeEvent(QResizeEvent* event)
{
    QSize newSize = event->size();
    ui.tableWidget->setGeometry(0, 0, newSize.width(), newSize.height());
    QWidget::resizeEvent(event);
}
void ResultDialog::contextMenuEvent(QContextMenuEvent* event)
{
    //  前两列不可以批量复制
    if(_columnSetupDialog->getAttr().size() <= 2)
    {
        return;
    }
    auto pSelectedModel = ui.tableWidget->selectionModel();
    if(pSelectedModel == nullptr)
    {
        assert(false && "表格的选择模型为空");
        return;
    }
    auto selectedModelIndexList = pSelectedModel->selectedIndexes();
    if(selectedModelIndexList.size() <= 1)
        return;
    for(int i = 0; i < selectedModelIndexList.size()-1 ; ++i)
    {
        int currentColumn = selectedModelIndexList.at(i).column();
        int nextColumn = selectedModelIndexList.at(i + 1).column();
        if (currentColumn <= 1 || nextColumn <= 1)
        {
            return;
        }

        if(currentColumn != nextColumn)
        {
            WD_WARN_T("ResultDialog", "batch copy need select the same colume");
            return;
        }
    }
        _modifyMenu->exec(event->globalPos());
}
void ResultDialog::onNodeDestroyBefore(WD::WDNode::SharedPtr pNode)
{
    if(pNode == nullptr)
        return;
    for(auto it = _nods.begin(); it != _nods.end();)
    {
        auto pShareNode = it->lock();
        if(pShareNode == nullptr)
        {
            it = _nods.erase(it);
            continue;
        }

        if(pShareNode == pNode)
        {
            // 将当前界面从节点的观察者中移除
            pNode->observers() -= this;
            // 将节点从原始数据组中移除
            it = _nods.erase(it);
            break;
        }
        else
        {
            ++it;
        }
    }
    // 将节点从当前界面中移除后，更新界面
    updateWidget();
}