#include "SearchResultWidget.h"
#include "WDTranslate.h"
#include <QHeaderView>

SearchResultWidget::SearchResultWidget(QWidget* parent)
    : QTableView(parent)
{
    // 启用双击编辑
    setEditTriggers(QAbstractItemView::DoubleClicked | QAbstractItemView::EditKeyPressed);
    initMenue();
    // 重写水平表头的contextMenueEvent方法
    horizontalHeader()->setContextMenuPolicy(Qt::CustomContextMenu);
    connect(horizontalHeader(), &QWidget::customContextMenuRequested, [=](const QPoint &pos){
        _horizontalMenu->exec(horizontalHeader()->mapToGlobal(pos));
    });
    verticalHeader()->setContextMenuPolicy(Qt::CustomContextMenu);
    connect(verticalHeader(), &QWidget::customContextMenuRequested, [=](const QPoint &pos){
        _vertiacalMenu->exec(verticalHeader()->mapToGlobal(pos));
    });
}
SearchResultWidget::~SearchResultWidget()
{
    if (_horizontalMenu != nullptr)
    {
        delete _horizontalMenu;
        _horizontalMenu = nullptr;
    }
    if (_vertiacalMenu != nullptr)
    {
        delete _vertiacalMenu;
        _vertiacalMenu = nullptr;
    }
}
void SearchResultWidget::initMenue()
{
    _horizontalMenu = new QMenu(this);
    QAction* columnSetupAct = _horizontalMenu->addAction(WD::WDTs("ResultDialog", "Column Setup").c_str());
    connect(columnSetupAct, &QAction::triggered, [&]() 
    {
        emit sigOpenColumnSetupDialog();
    });
    QAction* clearResultAct = _horizontalMenu->addAction(WD::WDTs("ResultDialog", "Clear Results").c_str());
    connect(clearResultAct, &QAction::triggered, [&]() 
    {
        emit sigClearWidget();
    });
    QAction* exportAct = _horizontalMenu->addAction(WD::WDTs("ResultDialog", "Export to excel").c_str());
    connect(exportAct, &QAction::triggered, [&]() 
    {
        emit sigExportExcel();
    });

    _vertiacalMenu = new QMenu(this);
    QAction* removeSelectedRowsAct = _vertiacalMenu->addAction(WD::WDTs("ResultDialog", "Remove Selected Rows").c_str());
    connect(removeSelectedRowsAct, &QAction::triggered, [&]()
    {
        emit sigRemoveCurrentRow();
    });
    QAction* navigateToAct = _vertiacalMenu->addAction(WD::WDTs("ResultDialog", "Navigate To").c_str());
    connect(navigateToAct, &QAction::triggered, [&]() 
    {
        emit sigNavigateTo();
    });
    QAction* viewAct = _vertiacalMenu->addAction(WD::WDTs("ResultDialog", "3D View").c_str());
    connect(viewAct, &QAction::triggered, [&]()
    {
        emit sigLoad3DView();
    });
}
void SearchResultWidget::keyPressEvent(QKeyEvent* event)
{
    if(event->key() == Qt::Key_Enter || event->key() == Qt::Key_Return)
    {
        auto current = currentIndex();
        if(current.isValid() && state() == QAbstractItemView::EditingState)
        {
            // 获取编辑前的值
            auto oldValue = model()->data(current, Qt::DisplayRole).toString();
            QWidget* editer = indexWidget(current);
            if (editer == nullptr)
            {
                assert(false && "索引获取窗口部件为空");
                return;
            }

            QLineEdit* lineEdit = qobject_cast<QLineEdit*>(editer);
            if (lineEdit == nullptr)
            {
                assert(false && "行编辑器为空");
                return;
            }

            auto newValue = lineEdit->text();
            // 提交数据
            commitData(editer);

            //触发修改完成信号
            emit sigModifyFinish(current, oldValue, newValue);
        }
    }
    QTableView::keyPressEvent(event);
}

ExpComboBox::ExpComboBox(QWidget* parent)
    :QComboBox(parent)
{}
ExpComboBox::~ExpComboBox()
{}
void ExpComboBox::keyPressEvent(QKeyEvent* event)
{
    if (event->key() == Qt::Key_Enter || event->key() == Qt::Key_Return)
    {
        emit sigReturnKeyEvent();
    }
    else
    {
        QComboBox::keyPressEvent(event);
    }
}

QWidget* ResultDelegate::createEditor(QWidget* parent, const QStyleOptionViewItem& option, const QModelIndex& index)const
{
    if (index.row() == editableRow)
    {
        QLineEdit* editor = new QLineEdit(parent);
        connect(editor, &QLineEdit::textChanged, this, &ResultDelegate::slotLineEditTextChanged);

        return editor;
    }
    else
    {
        return QStyledItemDelegate::createEditor(parent, option, index);
    }
}
void ResultDelegate::setEditorData(QWidget* editor, const QModelIndex& index) const
{
    if (index.row() == editableRow)
    {
        QString value = index.model()->data(index, Qt::EditRole).toString();
        QLineEdit* lineEdit = static_cast<QLineEdit*>(editor);
        lineEdit->setText(value);
    }
    else
    {
        QStyledItemDelegate::setEditorData(editor, index);
    }
}
void ResultDelegate::setModelData(QWidget* editor, QAbstractItemModel* model, const QModelIndex& index)const
{
    if (index.row() == editableRow)
    {
        QLineEdit* lineEdit = static_cast<QLineEdit*>(editor);
        model->setData(index, lineEdit->text(), Qt::EditRole);
    }
    else
    {
        QStyledItemDelegate::setModelData(editor, model, index);
    }
}
void ResultDelegate::slotLineEditTextChanged(const QString& text)
{
    emit textChanged(text);
}
