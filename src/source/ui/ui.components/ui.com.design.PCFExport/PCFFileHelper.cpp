#include "PCFFileHelper.h"
#include <qfileinfo.h>
#include "core/businessModule/WDBDBase.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/businessModule/design/pipeWork/WDBMDPipeUtils.h"
#include "core/businessModule/catalog/modelBuilder/WDBMCModelBuilder.h"
#include "core/math/DirectionParser.h"

std::string NodeTypeMapping(const WD::WDNode& node, PCFFileHelper::GenerateType type, std::string& errorStr)
{
    if (node.isType("PIPE"))
        return "PIPELINE-REFERENCE";

    // 分支只有在以分支为单位生成文件时才会记录其信息
    if (node.isType("BRAN"))
    {
        if (type == PCFFileHelper::GenerateType::GT_BRAN)
            return "PIPELINE-REFERENCE";

        return "";
    }
    if (node.isType("TUBI"))
        return "PIPE";

    if (!WD::WDBMDPipeUtils::IsPipeComponent(node))
        return "";

    if (node.isType("GASK"))
        return "GASKET";
    if (node.isType("FLAN"))
        return "FLANGE";
    if (node.isType("FBLI"))
        return "FLANGE-BLIND";
    if (node.isType("ELBO"))
        return "ELBOW";
    if (node.isType("VALV"))
        return "VALVE";
    if (node.isType("UNIO"))
        return "UNION";
    if (node.isType("INST"))
        return "INSTRUMENT";
    if (node.isType("ATTA"))
        return "SUPPORT";

    // 大小头有同心大小头和偏心大小头之分,映射的名称也不相同
    if (node.isType("REDU"))
    {
        auto pArrivePoint   = node.keyPoint(node.getAttribute("Arrive").toInt());
        auto pLeavePoint    = node.keyPoint(node.getAttribute("Leave").toInt());
        if (pArrivePoint == nullptr || pLeavePoint == nullptr)
        {
            errorStr = "node data error!";
            return "";
        }
        auto dir = WD::DVec3::Normalize(pLeavePoint->position - pArrivePoint->position);
        if (WD::DVec3::OnTheSameLine(dir, pArrivePoint->direction))
            return "REDUCER-CONCENTRIC";
        return "REDUCER-ECCENTRIC";
    }

    return node.type().data();
}
/**
 * @brief ENU全称朝向标签类型
*/
class AxisLabelNameENUFullName
{
public:
    static constexpr const char* X() 
    {
        return "EAST";
    }
    static constexpr const char* NX() 
    {
        return "WEST";
    }
    static constexpr const char* Y() 
    {
        return "NORTH";
    }
    static constexpr const char* NY() 
    {
        return "SOUTH";
    }
    static constexpr const char* Z() 
    {
        return "UP";
    }
    static constexpr const char* NZ() 
    {
        return "DOWN";
    }
    /**
    * @brief 轴可能是的字符串
    */
    static constexpr const char* AxisWord()
    {
        return "(EAST|WEST|NORTH|SOUTH|UP|DOWN)";
    }
};


std::string PCFFileHelper::GetGenerateTypeStr(GenerateType type)
{
    switch (type)
    {
    case PCFFileHelper::GenerateType::GT_BRAN:
        return "BRAN";
    case PCFFileHelper::GenerateType::GT_PIPE:
        return "PIPE";
    default:
        break;
    }
    return "";
}

PCFFileHelper::PCFFileHelper(WD::WDCore& core) : _core(core)
{
    type = GenerateType::GT_PIPE;
}

std::string PCFFileHelper::exportFile(WD::WDNode::SharedPtr pCurrentNode, const QString& dirPath)
{
    return exportFile(WD::WDNode::Nodes{pCurrentNode}, dirPath);
}

std::string PCFFileHelper::exportFile(WD::WDNode::Nodes nodes, const QString& dirPath)
{
    if (dirPath.isEmpty())
        return "dir path is empty!";
    if (nodes.empty())
        return "node list is empty!";

    struct FileData
    {
        QString fileName;
        WD::StringVector datas;
    };
    std::vector<FileData> fileDatas;

    auto typeStr = GetGenerateTypeStr(type);
    int unNamedIdx = 0;
    for (auto& pNode : nodes)
    {
        if (pNode == nullptr)
            continue;
        if (!pNode->isType(typeStr))
        {
            assert(false && "节点类型不匹配");
            continue;
        }
        std::string errorStr;

        QString fileName;
        if (pNode->isNamed())
        {
            auto tName = QString::fromUtf8(pNode->name().c_str());
            tName.replace("/", "_");
            tName.replace("\\", "_");

            fileName = dirPath + QString("/%1.pcf").arg(tName);
        }
        else
        {
            if (unNamedIdx == 0)
                fileName = dirPath + QString("/%1.pcf").arg(QString("unnamed"));
            else
                fileName = dirPath + QString("/%1_%2.pcf").arg(QString("unnamed")).arg(QString::number(unNamedIdx));
            ++unNamedIdx;
        }
        auto datas = generateData(*pNode, errorStr);
        if (!errorStr.empty())
            return errorStr;
        fileDatas.emplace_back(FileData{fileName, datas});
    }


    for (auto& eachFile : fileDatas)
    {
        const auto& fileName = eachFile.fileName;
        const auto& datas = eachFile.datas;
        // 生成文件
        QFile file(fileName);
        if (file.open(QFile::WriteOnly))
        {
            for (auto& each : datas)
                file.write((each + "\r\n").c_str());

            file.close();
        }
    }

    return "export success!";
}

// 管道等级
static constexpr const char* PipingSpec             = "PIPING-SPEC";
// 保温等级
static constexpr const char* InsulationSpec         = "INSULATION-SPEC";
// 涂漆等级
static constexpr const char* PaintingSpec           = "PAINTING-SPEC";
// 伴热等级
static constexpr const char* TracingSpec            = "TRACING-SPEC";

// 端点
static constexpr const char* EndPoint               = "END-POINT";
// 符号键
static constexpr const char* Skey                   = "SKEY";
// 重量
static constexpr const char* Weight                 = "WEIGHT";
// 材料代码
static constexpr const char* ItemCode               = "ITEM-CODE";
// 材料描述
static constexpr const char* ItemDescription        = "ITEM-DESCRIPTION";
// 材料描述
static constexpr const char* MaterialDescription    = "DESCRIPTION";
// 弯头/三通中心点
static constexpr const char* CentrePoint            = "CENTRE-POINT";
// 阀柄方向
static constexpr const char* SpindleDirection       = "SPINDLE-DIRECTION";
// 分支坐标(三通/支管台)
static constexpr const char* Branch1Point           = "BRANCH1-POINT";

// 支架点坐标+公称直径
static constexpr const char* CoOrdsStr              = "CO-ORDS";
// 支架类型
static constexpr const char* SupportTypeStr         = "SUPPORT-TYPE";
// 支架方向
static constexpr const char* SupportDirectionStr    = "SUPPORT-DIRECTION";

WD::StringVector PCFFileHelper::generateData(WD::WDNode& node, std::string& errorStr)
{
    WD::StringVector datas;
    // 获取头部信息
    generateFileHeader(datas);

    using MaterialData = std::unordered_map<std::string, std::string>;
    MaterialData materialData;
    // 获取组件信息
    WD::WDNode::RecursionHelpterR(node, [&datas, &materialData] (WD::WDCore& core
        , GenerateType type
        , std::string& errorStr
        , WD::WDNode& node)
    {
        auto mappingType = NodeTypeMapping(node, type, errorStr);
        // 错误字符串不为空,中断导出
        if (!errorStr.empty())
            return true;
        // 映射类型名称为空,不记录其信息
        if (mappingType.empty())
            return false;

        char text[10240] = {};
        if (node.type() == GetGenerateTypeStr(type))
        {
            sprintf(text, "%s   %s", mappingType.c_str(), node.name().c_str());
            datas.emplace_back(std::string(text));

            // 管道等级
            auto pPspecNode = node.getAttribute("Pspec").toNodeRef().refNode();
            if (pPspecNode != nullptr)
                sprintf(text, "    %s   %s", PipingSpec, pPspecNode->name().c_str());
            else
                sprintf(text, "    %s", PipingSpec);
            datas.emplace_back(std::string(text));
            // 保温等级
            auto pIspecNode = node.getAttribute("Ispec").toNodeRef().refNode();
            if (pIspecNode != nullptr)
                sprintf(text, "    %s   %s", InsulationSpec, pIspecNode->name().c_str());
            else
                sprintf(text, "    %s", InsulationSpec);
            datas.emplace_back(std::string(text));
            // 涂漆等级
            auto pPtspecNode = node.getAttribute("Ptspec").toNodeRef().refNode();
            if (pPtspecNode != nullptr)
                sprintf(text, "    %s   %s", PaintingSpec, pPtspecNode->name().c_str());
            else
                sprintf(text, "    %s", PaintingSpec);
            datas.emplace_back(std::string(text));
            // 伴热等级
            auto pTspecNode = node.getAttribute("Tspec").toNodeRef().refNode();
            if (pTspecNode != nullptr)
                sprintf(text, "    %s   %s", TracingSpec, pTspecNode->name().c_str());
            else
                sprintf(text, "    %s", TracingSpec);
            datas.emplace_back(std::string(text));
            return false;
        }

        // 提前准备好需要用到的数据
        auto pSpcoNode = node.getAttribute("Spref").toNodeRef().refNode();
        // cmpref节点用于获取重量数据
        WD::WDNode::SharedPtr pCmprefNode;
        // detref节点用于获取材料代码和材料描述
        WD::WDNode::SharedPtr pDetrefNode;
        // 重量数据和材料描述都需要进行表达式解析
        WD::WDBMCModelBuilder::CAttrs cAttrs;
        if (pSpcoNode != nullptr)
        {
            auto modelHelper = WD::WDBMCModelBuilder(core);
            auto gVars = WD::WDBMGVars();
            auto pBase = node.getBDBase();
            if (pBase != nullptr)
                pBase->collectGVars(core, gVars);

            pCmprefNode = pSpcoNode->getAttribute("Cmpref").toNodeRef().refNode();
            pDetrefNode = pSpcoNode->getAttribute("Detref").toNodeRef().refNode();
            cAttrs = modelHelper.buildAttrs(pSpcoNode->getAttribute("Catref").toNodeRef().refNode(), gVars, pSpcoNode, {pDetrefNode, pCmprefNode});
        }
        // 支架比较特殊,这里单独处理
        if (node.isType("ATTA"))
        {
            // 校验ATTA是否需要输出
            auto pSpecNode = pSpcoNode;
            while (pSpecNode != nullptr)
            {
                if (pSpecNode->isType("SPEC"))
                    break;
                pSpecNode = pSpecNode->parent();
            }
            if (pSpecNode == nullptr)
                return false;
            auto purpose = pSpecNode->getAttribute("Purpose").convertToString();
            auto attype = node.getAttribute("Attype").convertToString();
            // 当SPEC的purpose != PENI 同时 ATTYPE 值为 unset/HANG 时需要输出ATTA的信息
            if (purpose == "PENI" || (!attype.empty() && _stricmp(attype.c_str(), "Unset") != 0 && _stricmp(attype.c_str(), "HANG") != 0))
                return false;

            datas.emplace_back(mappingType);
            // 坐标XYZ信息+公称直径
            auto bore = WD::WDBMDPipeUtils::GetPipeComponentBore(pSpcoNode);
            WD::DVec3 pos = node.getAttribute("Position WRT World").toDVec3();
            if (bore)
            {
                sprintf(text, "    %s    %.3lf %.3lf %.3lf %d"
                    , CoOrdsStr
                    , pos.x, pos.y, pos.z
                    , bore.value());
            }
            else
            {
                assert(false);
                sprintf(text, "    %s    %.3lf %.3lf %.3lf"
                    , CoOrdsStr
                    , pos.x, pos.y, pos.z);
            }
            datas.emplace_back(std::string(text));
            // 支架方向,暂时写死为UP
            std::string supportDir = "UP";
            sprintf(text, "    %s    %s", SupportDirectionStr, supportDir.c_str());
            datas.emplace_back(std::string(text));
            // 符号键
            std::string skeyStr;
            if (pDetrefNode != nullptr)
                skeyStr = pDetrefNode->getAttribute("Skey").convertToString();
            sprintf(text, "    %s    %s", Skey, skeyStr.c_str());
            datas.emplace_back(std::string(text));

            // 重量
            bool bOk = false;
            double doubleVar = 0.0;
            if (pCmprefNode != nullptr)
                doubleVar = cAttrs.getAttribute(pCmprefNode, "Cweight").convertToDouble(&bOk);
            if (bOk)
                sprintf(text, "    %s   %.3lf", Weight, doubleVar);
            else
                sprintf(text, "    %s", Weight);
            datas.emplace_back(std::string(text));
            // 材料代码
            std::string itemCodeStr;
            if (pDetrefNode != nullptr)
                itemCodeStr = pDetrefNode->name();
            if (itemCodeStr.empty())
                sprintf(text, "    %s", ItemCode);
            else
                sprintf(text, "    %s   %s", ItemCode, itemCodeStr.c_str());
            datas.emplace_back(std::string(text));
            std::string key = std::string(text + 4);

            // 材料描述
            char value[1024] = { 0 };
            std::string itemDescStr;
            if (pDetrefNode != nullptr)
                itemDescStr = cAttrs.getAttribute(pDetrefNode, "Rtext").convertToString();
            if (itemDescStr.empty())
            {
                sprintf(text, "    %s", ItemDescription);
                sprintf(value, "    %s", MaterialDescription);
            }
            else
            {
                sprintf(text, "    %s   %s", ItemDescription, itemDescStr.c_str());
                sprintf(value, "    %s   %s", MaterialDescription, itemDescStr.c_str());
            }
            datas.emplace_back(std::string(text));
            auto itr = materialData.find(key);
            if (itr == materialData.end())
            {
                materialData.emplace(key, std::string(value));
            }
            // 支架类型 值固定为 Pipe Support
            sprintf(text, "    %s   Pipe Support", SupportTypeStr);
            datas.emplace_back(std::string(text));
            // 支架方向,暂不输出
            return false;
        }

        datas.emplace_back(mappingType);
        if (WD::WDBMDPipeUtils::IsPipeComponent(node))
        {
            // 起始点
            auto pArrivePoint = node.keyPoint(node.getAttribute("Arrive").toInt());
            if (pArrivePoint == nullptr)
            {
                errorStr = "node data error!";
                return true;
            }
            auto posS = pArrivePoint->transformedPosition(node.globalTransform());
            sprintf(text, "    %s    %.3lf %.3lf %.3lf %s %s"
                , EndPoint
                , posS.x, posS.y, posS.z
                , pArrivePoint->bore().c_str()
                , pArrivePoint->connType().c_str());
            datas.emplace_back(std::string(text));
            // 结束点
            auto pLeavePoint = node.keyPoint(node.getAttribute("Leave").toInt());
            if (pLeavePoint == nullptr)
            {
                errorStr = "node data error!";
                return true;
            }
            auto posE = pLeavePoint->transformedPosition(node.globalTransform());
            sprintf(text, "    %s    %.3lf %.3lf %.3lf %s %s"
                , EndPoint
                , posE.x, posE.y, posE.z
                , pLeavePoint->bore().c_str()
                , pLeavePoint->connType().c_str());
            datas.emplace_back(std::string(text));


            // 弯头/三通中心点
            if (node.isAnyOfType("TEE", "ELBO"))
            {
                auto cPos = node.getAttribute("Position WRT World").toDVec3();
                sprintf(text, "    %s   %.3lf %.3lf %.3lf"
                    , CentrePoint
                    , cPos.x, cPos.y, cPos.z);
                datas.emplace_back(std::string(text));
            }
            // 分支坐标(三通/支管台)
            if (node.isAnyOfType("TEE", "OLET"))
            {
                auto pForkPoint = node.keyPoint(WD::WDBMDPipeUtils::Fork(node));
                if (pForkPoint == nullptr)
                {
                    errorStr = "node data error!";
                    return true;
                }
                auto fPos = pForkPoint->transformedPosition(node.globalTransform());
                sprintf(text, "    %s   %.3lf %.3lf %.3lf %s %s"
                    , Branch1Point
                    , fPos.x, fPos.y, fPos.z
                    , pForkPoint->bore().c_str()
                    , pForkPoint->connType().c_str());
                datas.emplace_back(std::string(text));
            }
            // 阀柄方向
            if (node.isType("VALV"))
            {
                std::string dirStr;
                // 阀门手轮朝向就是p3方向
                auto pPoint = node.keyPoint(3);
                if (pPoint != nullptr)
                {
                    auto direction = pPoint->transformedDirection(node.globalTransform());
                    dirStr = WD::TDirectionParser<double, AxisLabelNameENUFullName>::OutputStringByDirection(direction, 3);
                }
                if (dirStr.empty())
                    sprintf(text, "    %s", SpindleDirection);
                else
                    sprintf(text, "    %s   %s"
                    , SpindleDirection
                    , dirStr.c_str());
                datas.emplace_back(std::string(text));
            }
            // 符号键
            std::string skeyStr;
            if (pDetrefNode != nullptr)
                skeyStr = pDetrefNode->getAttribute("Skey").convertToString();
            sprintf(text, "    %s    %s", Skey, skeyStr.c_str());
            datas.emplace_back(std::string(text));

            // 焊缝不需要输出后续的信息
            if (node.isType("WELD"))
                return false;
        }
        else if (node.isType("TUBI"))
        {
            auto hPos = node.getAttribute("Hposition WRT World").toDVec3();
            auto tPos = node.getAttribute("Tposition WRT World").toDVec3();
            auto bore = WD::WDBMDPipeUtils::GetPipeComponentBore(pSpcoNode);
            if (bore)
            {
                // 直管段头
                sprintf(text, "    %s    %.3lf %.3lf %.3lf %d"
                    , EndPoint
                    , hPos.x, hPos.y, hPos.z
                    , bore.value());
                datas.emplace_back(std::string(text));
                // 直管段尾
                sprintf(text, "    %s    %.3lf %.3lf %.3lf %d"
                    , EndPoint
                    , tPos.x, tPos.y, tPos.z
                    , bore.value());
                datas.emplace_back(std::string(text));
            }
            else
            {
                assert(false);
                // 直管段头
                sprintf(text, "    %s    %.3lf %.3lf %.3lf"
                    , EndPoint
                    , hPos.x, hPos.y, hPos.z);
                datas.emplace_back(std::string(text));
                // 直管段尾
                sprintf(text, "    %s    %.3lf %.3lf %.3lf"
                    , EndPoint
                    , tPos.x, tPos.y, tPos.z);
                datas.emplace_back(std::string(text));
            }
        }
        else
        {
            datas.pop_back();
            return false;
        }
        std::string pSpecStr;
        std::string iSpecStr;
        std::string ptSpecStr;
        std::string tSpecStr;
        auto pBranch = node.parent();
        if (pBranch != nullptr && pBranch->isType("BRAN"))
        {
            // 获取管道等级名称
            auto pPspecNode = pBranch->getAttribute("Pspec").toNodeRef().refNode();
            if (pPspecNode != nullptr)
                pSpecStr = pPspecNode->name();

            // 获取保温等级名称
            auto pIspecNode = pBranch->getAttribute("Ispec").toNodeRef().refNode();
            if (pIspecNode != nullptr)
                iSpecStr = pIspecNode->name();

            // 获取涂漆等级名称
            auto pPtspecNode = pBranch->getAttribute("Ptspec").toNodeRef().refNode();
            if (pPtspecNode != nullptr)
                ptSpecStr = pPtspecNode->name();

            // 获取伴热等级名称
            auto pTspecNode = pBranch->getAttribute("Tspec").toNodeRef().refNode();
            if (pTspecNode != nullptr)
                tSpecStr = pTspecNode->name();
        }
        // 管道等级
        if (pSpecStr.empty())
            sprintf(text, "    %s", PipingSpec);
        else
            sprintf(text, "    %s   %s", PipingSpec, pSpecStr.c_str());
        datas.emplace_back(std::string(text));
        // 保温等级
        if (iSpecStr.empty())
            sprintf(text, "    %s", InsulationSpec);
        else
            sprintf(text, "    %s   %s", InsulationSpec, iSpecStr.c_str());
        datas.emplace_back(std::string(text));
        // 涂漆等级
        if (ptSpecStr.empty())
            sprintf(text, "    %s", PaintingSpec);
        else
            sprintf(text, "    %s   %s", PaintingSpec, ptSpecStr.c_str());
        datas.emplace_back(std::string(text));
        // 伴热等级
        if (tSpecStr.empty())
            sprintf(text, "    %s", TracingSpec);
        else
            sprintf(text, "    %s   %s", TracingSpec, tSpecStr.c_str());
        datas.emplace_back(std::string(text));
        // 重量
        std::string weightAttributeName;
        if (node.isType("TUBI"))
            weightAttributeName = "UWEI";
        else
            weightAttributeName = "Cweight";
        bool bOk = false;
        double doubleVar = 0.0;
        if (pCmprefNode != nullptr)
            doubleVar = cAttrs.getAttribute(pCmprefNode, weightAttributeName).convertToDouble(&bOk);
        if (bOk)
            sprintf(text, "    %s   %.3lf", Weight, doubleVar);
        else
            sprintf(text, "    %s", Weight);
        datas.emplace_back(std::string(text));

        // 材料代码
        std::string itemCodeStr;
        if (pDetrefNode != nullptr)
            itemCodeStr = pDetrefNode->name();
        if (itemCodeStr.empty())
            sprintf(text, "    %s", ItemCode);
        else
            sprintf(text, "    %s   %s", ItemCode, itemCodeStr.c_str());
        datas.emplace_back(std::string(text));
        std::string key = std::string(text + 4);

        // 材料描述
        char value[1024] = { 0 };
        std::string itemDescStr;
        if (pDetrefNode != nullptr)
            itemDescStr = cAttrs.getAttribute(pDetrefNode, "Rtext").convertToString();
        if (itemDescStr.empty())
        {
            sprintf(text, "    %s", ItemDescription);
            sprintf(value, "    %s", MaterialDescription);
        }
        else
        {
            sprintf(text, "    %s   %s", ItemDescription, itemDescStr.c_str());
            sprintf(value, "    %s   %s", MaterialDescription, itemDescStr.c_str());
        }
        datas.emplace_back(std::string(text));
        auto itr = materialData.find(key);
        if (itr == materialData.end())
        {
            materialData.emplace(key, std::string(value));
        }

        return false;
    }, _core, type, errorStr);
    if (!errorStr.empty())
        return datas;

    // 输出材料信息
    {
        datas.emplace_back("MATERIALS");
        datas.reserve(datas.size() + 2 * materialData.size());

        for (auto& eachData : materialData)
        {
            datas.emplace_back(eachData.first);
            datas.emplace_back(eachData.second);
        }
    }

    return datas;
}

std::string PCFFileHelper::generateFileHeader(WD::StringVector& datas)
{
    datas.emplace_back("ISOGEN-FILES   ISOGEN.FLS");
    datas.emplace_back("UNITS-BORE   MM");
    datas.emplace_back("UNITS-CO-ORDS   MM");
    datas.emplace_back("UNITS-BOLT-LENGTH   MM");
    datas.emplace_back("UNITS-BOLT-DIA   MM");
    datas.emplace_back("UNITS-WEIGHT   KGS");

    return "";
}