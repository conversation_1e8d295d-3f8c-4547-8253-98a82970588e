<?xml version="1.0" encoding="utf-8"?>

<TS language="简体中文">
	<context>
		<name>UiComMeasureDistance</name>
		<message>
			<source>Distance measure</source>
			<translation>距离测量</translation>
		</message>
		<message>
			<source>Display capture coordinate</source>
			<translation>显示捕捉坐标点</translation>
		</message>
		<message>
			<source>Nearest point distance</source>
			<translation>最近点测量</translation>
		</message>
		<message>
			<source>Result display</source>
			<translation>显示结果</translation>
		</message>
		<message>
			<source>Close</source>
			<translation>关闭</translation>
		</message>
		<message>
			<source>Object</source>
			<translation>对象</translation>
		</message>
		<message>
			<source>KeyPoint</source>
			<translation>关键点</translation>
		</message>
		<message>
			<source>PLine</source>
			<translation>PLine线</translation>
		</message>
		<message>
			<source>Distance</source>
			<translation>距离</translation>
		</message>
		<message>
			<source>Penetrating</source>
			<translation>嵌入</translation>
		</message>
		<message>
			<source>XDistance</source>
			<translation>X方向距离</translation>
		</message>
		<message>
			<source>YDistance</source>
			<translation>Y方向距离</translation>
		</message>
		<message>
			<source>ZDistance</source>
			<translation>Z方向距离</translation>
		</message>
		<message>
			<source>Direction</source>
			<translation>测量朝向</translation>
		</message>
		<message>
			<source>the measurement failed. the two point measured can't the same node</source>
			<translation>测量失败,最近点测量的两个点不能来自同一个节点!</translation>
		</message>
	</context>
</TS>