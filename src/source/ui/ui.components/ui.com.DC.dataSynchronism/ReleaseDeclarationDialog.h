#pragma once

#include "ui_ReleaseDeclarationDialog.h"
#include "core/WDCore.h"
#include "core/node/WDNode.h"
#include "../../wizDesignerApp/UiInterface/ICollaboration.h"

class ReleaseDeclarationDialog : public QDialog
{
    Q_OBJECT
public:
    ReleaseDeclarationDialog(WD::WDCore& core, ICollaboration& collaboration, QWidget *parent = Q_NULLPTR);
    ~ReleaseDeclarationDialog();

protected:
    /**
     * @brief 显示界面前需要获取最新的当前用户申领的对象名字，所以这里重载了该事件
    */
    void showEvent(QShowEvent* event) override;

private slots:
    /**
     * @brief 点击确认后，将释放已选对象的权限
    */
    void slotPushButtonReleaseClicked();
    /**
    * @brief 点击确认后，将释放所有对象的权限
    */
    void slotPushButtonReleaseAllClicked();
    /**
     * @brief 只是关闭对话框
    */
    void slotPushButtonCancelClicked();

private:
    /**
    * @brief 刷新申明列表，将当前用户所申领的对象名字刷新到界面中
    */
    void refreshDeclarationList() const;
    /**
     * @brief 释放申明
     * @param nodes 需要释放申明的节点
    */
    void releaseDeclaration(const WD::WDNode::Nodes& nodes) const;
private:
    /**
     * @brief 检查当前签入的节点中是否由和服务器数据不一致的节点，如果有就弹窗提示，由用户选择是否提交到服务器
     * @return 是否执行签出，true-签出 false-不签出
    */
    bool checkCommitData() const;
    /**
     * @brief 设置按钮是否可用
     * @param enable 是否可用，true-可用 false-不可用
    */
    void setButtonEnable(bool enable);

    void setItemUData(QListWidgetItem& item, WD::WDNode::SharedPtr pNode) const;
    WD::WDNode::SharedPtr getItemUData(const QListWidgetItem& item) const;
private:
    // 界面翻译
    void retranslateUi();

private:
    Ui::ReleaseDeclarationDialog ui;
    WD::WDCore&  _core;
    ICollaboration& _collaboration;
};
