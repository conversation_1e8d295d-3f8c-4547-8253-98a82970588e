#pragma once

#include    "../../wizDesignerApp/UiInterface/UiInterface.h"
#include    "WDCore.h"
#include    "ObjectDeclarationDialog.h"
#include    "ReleaseDeclarationDialog.h"
#include    "core/common/WDConfig.h"
#include    <QTimer>
#include    <QThread>
#include    "../../wizDesignerApp/UiInterface/ICollaboration.h"

class HTCheckLoginThread;
class UiComDCDataSynchronism
	: public QObject
	, public IUiComponent
{
	Q_OBJECT
public:
    UiComDCDataSynchronism(IMainWindow& mainWindow, const UiComponentAttributes& attrs);
	virtual ~UiComDCDataSynchronism();
public:
    /**
    *   @brief 通知事件
    *   @param pNotice 事件对象
    */
    virtual void onNotice(UiNotice* pNotice) override;

signals:
    void    sigDownloadProjectEnd(bool bSuccess);
    void    sigCommitProjectEnd(bool bSuccess);
    void    sigInitProjectEnd(bool bSuccess);

private slots:
    void    slotDownloadProjectEnd(bool bSuccess);
    void    slotCommitProjectEnd(bool bSuccess);
    void    slotInitProjectEnd(bool bSuccess);
    void    slotQuitSys(const QString& msg);

private:
    /**
     * @brief 轮询通知 响应
    */
    void    slotRepeatSave();

private:
    /**
     * @brief 配置项(自动保存)值更改通知响应
     * @param item 配置项
    */
    void    onCfgAutoSaveValueChanged(const WD::WDConfigItem& item);
    /**
     * @brief 配置项(自动保存时间间隔)值更改通知响应
     * @param item 配置项
    */
    void    onCfgAutoSaveSpacingValueChanged(const WD::WDConfigItem& item);
    /**
     * @brief 提交项目到服务器
    */
    void    commitLocalProjectToServer();
    /**
     * @brief 更新数据到本地
    */
    void    downloadProjectFromServer();
    /**
     * @brief 把本地项目数据添加到申领的新增列表中
     */
    void    createAddLocal();
private:
    WD::WDCore& _core;
    ObjectDeclarationDialog* _pObjDeclDialog;
    ReleaseDeclarationDialog* _pRelDeclDialog;

    // 轮询保存timer
    QTimer* _timer;
    bool    _autoSave;
    int     _autoSaveSpacing;
    HTCheckLoginThread* _pCheckLoginThread;

    bool _bDownloadSuccess = false;
};

/**
 * @brief 同账号登录检测线程
*/
class HTCheckLoginThread : public QThread
{
    Q_OBJECT
public:
    HTCheckLoginThread()
        : _quit(true)
    {}
    ~HTCheckLoginThread()
    {}

public:
    // TODO: NEWDC
#if 0
    inline std::function<INetworkApi::ReplyCheckLogin(const int)>& applyFunc()
    {
        return _applyFunc;
    }
#endif

public:
    inline void start()
    {
        _quit = false;
        QThread::start();
    }
    inline void quit()
    {
        _quit = true;
        QThread::quit();
    }

signals:
    /**
     * @brief 当前账户已在其它设备登录的信号，msg:打印信息
    */
    void sigHasLoggedIn(const QString& msg);

private:
    void run() override
    {
#if 0
        auto lastReq = QDateTime::currentDateTime();
        while (!_quit)
        {
            auto loginInfo = _applyFunc(INetworkApi::LT_Login);
            if (!loginInfo.canLogin)
            {
                // 该账户已经在其他设备上登陆了，所以就退出当前程序
                _quit = true;
                emit sigHasLoggedIn(loginInfo.errMsg);
            }

            auto nowReq = QDateTime::currentDateTime();
            auto delay = lastReq.msecsTo(nowReq);
            // 每隔20秒执行一次
            qint64 gapTime = 20000 - delay;
            if (gapTime > 0)
                msleep(gapTime);
            lastReq = nowReq;
        }
#endif
    }

private:
    // TODO: NEWDC
    //std::function<INetworkApi::ReplyCheckLogin(const int)>   _applyFunc;
    // 退出标志
    bool                    _quit;
};