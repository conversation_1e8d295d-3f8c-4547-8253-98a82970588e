#pragma once

#include <QDialog>
#include "ui_UiAtomComCoCoTableWidget.h"
#include "core/node/WDNode.h"

class UiAtomComCoCoTableWidget : public QDialog
{
    Q_OBJECT

public:
    UiAtomComCoCoTableWidget(QWidget *parent = Q_NULLPTR);
    ~UiAtomComCoCoTableWidget();

public:
    bool    updateWidget(WD::WDNode::SharedPtr node);
    void    initWidget();
public slots:
    /**
    * @brief 导入按钮按下的槽函数
    */
    void    onImporButtonClicked();

    /**
    * @brief 导出按钮按下的槽函数
    */
    void    onExportButtonClicked();

    /**
    * @brief 确定按钮按下的槽函数
    */
    void    onOkButtonClicked();
private:
    Ui::UiAtomComCoCoTableWidget    ui;
    // 保存CCTA节点
    WD::WDNode::WeakPtr             _pCcta;
};
