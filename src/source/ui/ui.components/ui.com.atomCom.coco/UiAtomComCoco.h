#pragma once
#include <QWidget>
#include "ui_UiAtomComCoco.h"
#include "../../wizDesignerApp/UiInterface/UiInterface.h"
#include "UiAtomComCocoCreate.h"


class UiAtomComCoco
	: public QWidget
	, public IUiComponent
{
	Q_OBJECT

public:
	UiAtomComCoco(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QWidget *parent = nullptr);
    ~UiAtomComCoco();
public:
    /**
    *   @brief 通知事件
    *   @param pNotice 事件对象
    */
    virtual void onNotice(UiNotice* pNotice) override;

private:
    Ui::UiAtomComCoco               ui;
    UiAtomComCocoCreate*            _cocoCreateWidget;
    
};
