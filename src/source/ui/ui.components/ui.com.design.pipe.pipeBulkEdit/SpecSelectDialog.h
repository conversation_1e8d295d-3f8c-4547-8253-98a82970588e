#pragma once
#include <QDialog>
#include "ui_SpecSelectDialog.h"
#include "core/node/WDNode.h"
namespace WD
{
    class WDCore;
}

class SpecSelectDialog
    : public QDialog
{
    Q_OBJECT
public:
    enum HandleType
    {
        ComsOnly,
        Branch,
        Pipe
    };
public:
    HandleType type = ComsOnly;
public:
    SpecSelectDialog(WD::WDCore& core, QWidget *parent = nullptr);
    virtual~SpecSelectDialog();
public slots:
    void slotPushButtonApplyClicked();
    void slotPushButtonDismissClicked();
public:
    WD::WDNode::SharedPtr pSpec() const
    {
        return _pSpec.lock();
    }
    void setPSpec(WD::WDNode::SharedPtr pSpec);
protected:
    void showEvent(QShowEvent *) override;
    void hideEvent(QHideEvent *) override;
private:
    void retranslateUi();
private:
    Ui::SpecSelectDialog ui;
    WD::WDCore& _core;
    WD::WDNode::WeakPtr _pSpec;
    WD::WDNode::WeakPtr _pDefaultSpec;
};