#include "UiComCreateAxisNet.h"
#include "core/nodeTree/WDNodeTree.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/viewer/WDViewer.h"
#include "core/scene/WDScene.h"
#include "businessModule/WDBMPermissionMgr.h"
#include "../../wizDesignerApp/UiInterface/ICollaboration.h"

UiComCreateAxisNet::UiComCreateAxisNet(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject *parent)
    : QObject(parent)
    , IUiComponent(mainWindow, attrs)
{
    auto& core = mWindow().core();
    _pCreateRectangularGridDialog = new CreateRectangularGridDialog(core, mWindow().widget());
    _pCreateRectangularGridDialog->bIsAdmin = !mWindow().collaboration().actived() || core.getBMDesign().permissionMgr().isAdmin() || 
        (core.getBMDesign().permissionMgr().role() == WD::WDBMPermissionMgr::U_SiteMamager);
}

UiComCreateAxisNet::~UiComCreateAxisNet()
{
    if (_pCreateRectangularGridDialog != nullptr)
    {
        delete _pCreateRectangularGridDialog;
        _pCreateRectangularGridDialog = nullptr;
    }
}

void UiComCreateAxisNet::onNotice(UiNotice* pNotice) 
{
    int nType = pNotice->type();
    switch (nType)
    {
        case UiNoticeType::UNT_Action:
        {
            UiActionNotice* pActionNotice = static_cast<UiActionNotice*>(pNotice);
            // 创建自定义辅助轴网
            if (pActionNotice->action().is("action.architectural.axisNet"))
            {
                if (_pCreateRectangularGridDialog->isHidden())
                    _pCreateRectangularGridDialog->show();
                else
                    _pCreateRectangularGridDialog->activateWindow();
            }
        }
        break;
    case UiNoticeType::UNT_AllReady:
        {   
        }
        break;
    default:
        break;
    }
}
