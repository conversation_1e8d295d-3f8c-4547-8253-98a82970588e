#include "JointDialog.h"
#include "core/message/WDMessage.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "../../ui.commonLibrary/ui.commonLib.weakObject/WeakObject.h"

JointDialog::JointDialog(WD::WDCore& core, QDialog *parent)
    : QDialog(parent)
    , _core(core)
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    // 连接点类型和形状初始化
    _jointShape =   JointShape::JS_Rectangular;
    _jointType  =   JointType::JT_Leave;
    this->initJointShapeCombo();
    ui.radioButtonLeave->setChecked(true);

    connect(ui.comboBoxShape
        , QOverload<int>::of(&QComboBox::currentIndexChanged)
        , this
        , &JointDialog::slotOnJointShapeChanged);
    connect(ui.radioButtonArrive,   &QRadioButton::toggled,  this, &JointDialog::slotOnJointTypeChanged);
    connect(ui.radioButtonLeave,    &QRadioButton::toggled,  this, &JointDialog::slotOnJointTypeChanged);
    connect(ui.radioButtonThird,    &QRadioButton::toggled,  this, &JointDialog::slotOnJointTypeChanged);
    connect(ui.pushButtonOk,        &QRadioButton::clicked, this, &JointDialog::slotOnOkClicked);
    connect(ui.pushButtonCancel,    &QRadioButton::clicked, this, &JointDialog::slotOnCancelClicked);
}

JointDialog::~JointDialog()
{
}

void JointDialog::retranslateUi(const std::string& ctxStr)
{
    Trs(ctxStr.c_str()
        , static_cast<QDialog*>(this)
        , ui.labelShape
        , ui.comboBoxShape
        , ui.groupBoxJoint
        , ui.radioButtonArrive
        , ui.radioButtonLeave
        , ui.radioButtonThird
        , ui.labelChooseJoint
        , ui.pushButtonOk
        , ui.pushButtonCancel
    );
}

void JointDialog::showEvent(QShowEvent* evt)
{
    // 统一将焦点设置到窗体，用于在窗口打开时清除其他控件焦点
    this->setFocus();
    WDUnused(evt);
    _pJoint.reset();
    this->updateJointList();
}

void JointDialog::hideEvent(QHideEvent* evt)
{
    WDUnused(evt);
}

void JointDialog::slotOnJointShapeChanged(int index)
{
    WDUnused(index);
    auto    userData    =   ui.comboBoxShape->currentData();
    if (!userData.isValid())
        return ;

    _jointShape = (JointShape)userData.value<int>();
}

void JointDialog::slotOnJointTypeChanged()
{
    auto pSender = static_cast<QRadioButton*>(QObject::sender());
    if (pSender == ui.radioButtonArrive)
    {
        _jointType = JointType::JT_Arrive;
    }
    else if (pSender == ui.radioButtonLeave)
    {
        _jointType = JointType::JT_Leave;
    }
    else if (pSender == ui.radioButtonThird)
    {
        _jointType = JointType::JT_Third;
    }
}

void JointDialog::slotOnOkClicked()
{
    // 获取当前选中连接点
    auto    pItem       =   ui.listWidgetJoint->currentItem();
    if (pItem == nullptr)
    {
        WD_ERROR_T("ErrorJointDialog", "Must choose a item!");
        return ;
    }
    auto    userData    =   pItem->data(Qt::UserRole);
    if (!userData.isValid())
        return ;
    _pJoint = userData.value<UiWeakObject>().subObject<WD::WDNode>();

    emit sigJointChoosed(_jointType);
    this->accept();
}

void JointDialog::slotOnCancelClicked()
{
    this->reject();
}

void JointDialog::initJointShapeCombo()
{
    ui.comboBoxShape->addItem(JointShapeToName(JointShape::JS_Rectangular), JointShape::JS_Rectangular);
    ui.comboBoxShape->addItem(JointShapeToName(JointShape::JS_Circular), JointShape::JS_Circular);
    ui.comboBoxShape->addItem(JointShapeToName(JointShape::JS_FlatOval), JointShape::JS_FlatOval);

    // 默认选中第一个
    ui.comboBoxShape->setCurrentIndex(0);
}

void JointDialog::updateJointList()
{
    // 清除列表
    ui.listWidgetJoint->clear();
    auto    pCata   =   HvacCata();
    auto    pJoints =   GetJoints(pCata, _jointShape);
    for (const auto& pJoint : pJoints)
    {
        if (pJoint == nullptr)
            continue;

        // 获取描述
        auto desc = GetJointDesc(*pJoint);

        // 添加项
        auto        pItem       =   new QListWidgetItem(ui.listWidgetJoint);
        pItem->setText(QString::fromUtf8(desc.c_str()));
        QVariant    userData;
        userData.setValue(UiWeakObject(pJoint));
        pItem->setData(Qt::UserRole, userData);
        ui.listWidgetJoint->addItem(pItem);
    }
}