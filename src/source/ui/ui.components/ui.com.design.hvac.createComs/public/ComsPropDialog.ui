<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ComsPropDialog</class>
 <widget class="QDialog" name="ComsPropDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>324</width>
    <height>228</height>
   </rect>
  </property>
  <property name="focusPolicy">
   <enum>Qt::StrongFocus</enum>
  </property>
  <property name="windowTitle">
   <string>Component</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout" stretch="1,0,0,0,0,0">
   <item>
    <layout class="QVBoxLayout" name="verticalLayout_2"/>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <widget class="QComboBox" name="comboBoxALType"/>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonTrans">
       <property name="text">
        <string>Swap</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QFormLayout" name="formLayout">
     <item row="0" column="0">
      <widget class="QLabel" name="labelArrive">
       <property name="text">
        <string>Arrive</string>
       </property>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="QLineEdit" name="lineEditArrive"/>
     </item>
     <item row="1" column="0">
      <widget class="QLabel" name="labelLeave">
       <property name="text">
        <string>Leave</string>
       </property>
      </widget>
     </item>
     <item row="1" column="1">
      <widget class="QLineEdit" name="lineEditLeave"/>
     </item>
     <item row="2" column="0">
      <widget class="QLabel" name="labelBranch">
       <property name="text">
        <string>BranchPort</string>
       </property>
      </widget>
     </item>
     <item row="2" column="1">
      <widget class="QLineEdit" name="lineEditBranch"/>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_3">
     <item>
      <widget class="QPushButton" name="pushButtonPrev">
       <property name="text">
        <string>Previous</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonChoose">
       <property name="text">
        <string>Choose</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_4">
     <item>
      <widget class="QLabel" name="labelDir">
       <property name="text">
        <string>Direction</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEditDir"/>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonOk">
       <property name="text">
        <string>Ok</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonCancel">
       <property name="text">
        <string>Cancel</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
