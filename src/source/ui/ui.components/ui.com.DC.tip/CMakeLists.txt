set(TARGET_NAME ui_com_DC_tip)

set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON) 
find_package(Qt5 COMPONENTS Core Widgets WebSockets WebEngineWidgets REQUIRED)

set(HEADER_FILES
    "Common.h"
    "UiComDCTip.h"
)

set(SOURCE_FILES
    "Common.cpp"
    "UiComDCTip.cpp"
	"main.cpp"
)

set(FORM_FILES
)

add_library(${TARGET_NAME} SHARED
		${HEADER_FILES}
		${SOURCE_FILES}
		${FORM_FILES}
)
add_compile_definitions(${TARGET_NAME} PRIVATE UI_COM_DC_TIP_LIB)

target_link_libraries(${TARGET_NAME} PUBLIC wizDesignerCore util.rapidjson ui.commonLib.WeakObject ui.commonLib.custom ui.commonLib.property)
target_link_libraries(${TARGET_NAME} PUBLIC Qt5::Core Qt5::Widgets Qt5::WebSockets Qt5::WebEngineWidgets QScintilla qtpropertybrowser)
target_include_directories(${TARGET_NAME} PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})

add_custom_command(
	TARGET ${TARGET_NAME}
	POST_BUILD
    COMMAND             ${CMAKE_COMMAND} -E make_directory ./translations
    COMMAND             ${CMAKE_COMMAND} -E copy_directory ./translations ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/ui/components/${TARGET_NAME}/translations
	WORKING_DIRECTORY	${CMAKE_CURRENT_SOURCE_DIR}
    COMMENT             "copy directory translations..."
)

DeployQt(${TARGET_NAME})