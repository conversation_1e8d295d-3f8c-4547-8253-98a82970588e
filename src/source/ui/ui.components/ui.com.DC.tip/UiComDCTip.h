#pragma once

#include <QObject>
#include "../../wizDesignerApp/UiInterface/UiInterface.h"

#include <QWebEngineView>
#include <QWebEnginePage>
#include <QPushButton>
#include <QWebEngineCertificateError>
#include "../../wizDesignerApp/UiInterface/IRepeatThread.h"

class WDWebEnginePage : public QWebEnginePage
{
    Q_OBJECT

public:
    WDWebEnginePage(QObject* parent = nullptr)
        : QWebEnginePage(parent)
    {}

public:
    inline virtual bool certificateError(const QWebEngineCertificateError&) override
    {
        return true;
    }
};

class UiComDCTip
    : public QObject
	, public IUiComponent
    , public IRepeatListener
{
    Q_OBJECT
public:
    UiComDCTip(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject *parent = nullptr);
    ~UiComDCTip();

    virtual QWidget*    getWidget(const char* name) override;
    virtual void        onNotice(UiNotice* pNotice) override;

private slots:
    /**
     * @brief 获取通知接口调用完成 响应
    */
    void    slotGetMessageFinished();
    void    slotBtnSubmitReviewClicked(bool);

private:
    /**
     * @brief 获取通知 响应
    */
    virtual void onRepeat(const std::string& key) override;

private:
    /**
     * @brief 提交校审
     * @param nodes 校审节点
    */
    void    submitReview(const WD::WDNode::Nodes& nodes);

private:
    WD::WDCore&         _core;
    IRepeatThread&      _repeatThread;
    // 前端页面弹窗
    QWebEngineView*     _view;
    QWidget*            _viewWidget;
    QPushButton*        _submitBtn;
    WD::WDNode::WeakPtr _curNode;
};

