<?xml version="1.0" encoding="utf-8"?>

<TS language="简体中文">
	<context>
		<name>UiComDesignCableTrayBranConnCommon</name>
		<message>
			<source>PBOR</source>
			<translation>公称直径</translation>
		</message>
		<message>
			<source>Nominal diameter</source>
			<translation>公称直径</translation>
		</message>
		<message>
			<source>ANGL</source>
			<translation>角度</translation>
		</message>
		<message>
			<source>Radius</source>
			<translation>半径</translation>
		</message>
		<message>
			<source>HEIG</source>
			<translation>高度</translation>
		</message>
	</context>

	<context>
		<name>BranConnDialog</name>
		<message>
			<source>You cannot operate the current node!</source>
			<translation>您不能操作当前节点!</translation>
		</message>
		<message>
			<source>BranchConnection</source>
			<translation>分支连接</translation>
		</message>
		<message>
			<source>BranchName</source>
			<translation>分支名称</translation>
		</message>
		<message>
			<source>ConnectionType</source>
			<translation>连接类型</translation>
		</message>
		<message>
			<source>CurrentElement</source>
			<translation>当前元素</translation>
		</message>
		<message>
			<source>Apply</source>
			<translation>应用</translation>
		</message>
		<message>
			<source>Head</source>
			<translation>头</translation>
		</message>
		<message>
			<source>Tail</source>
			<translation>尾</translation>
		</message>
		<message>
			<source>Tee</source>
			<translation>三通</translation>
		</message>
		<message>
			<source>Multiway</source>
			<translation>四通</translation>
		</message>
		<message>
			<source>BranchHead</source>
			<translation>分支头</translation>
		</message>
		<message>
			<source>BranchTail</source>
			<translation>分支尾</translation>
		</message>
		<message>
			<source>FirstMember</source>
			<translation>分支第一个</translation>
		</message>
		<message>
			<source>LastMember</source>
			<translation>分支最后一个</translation>
		</message>
		<message>
			<source>Name</source>
			<translation>名字</translation>
		</message>
		<message>
			<source>Failed to invoke the subclass connection implementation</source>
			<translation>未成功调用子类连接实现</translation>
		</message>
	</context>
	<context>
		<name>ErrorBranConnDialog</name>
		<message>
			<source>PickNodeNotMatchConnectType</source>
			<translation>拾取节点对象类型与连接对象类型不匹配</translation>
		</message>
		<message>
			<source>Current type only support at the head of the branch!</source>
			<translation>当前连接类型管件只支持在分支头</translation>
		</message>
		<message>
			<source>Current type only support at the tail of the branch!</source>
			<translation>当前连接类型管件只支持在分支尾</translation>
		</message>
		<message>
			<source>Connect object is connected to another object!</source>
			<translation>连接对象已连接到其他对象!</translation>
		</message>
		<message>
			<source>Head connect to head?</source>
			<translation>确认将分支头连接到目标分支头吗?这有可能是不规范的操作</translation>
		</message>
		<message>
			<source>Tail connect to tail?</source>
			<translation>确认将分支尾连接到目标分支尾吗?这有可能是不规范的操作</translation>
		</message>
		<message>
			<source>Not is same branch!</source>
			<translation>目标分支与当前分支相同!</translation>
		</message>
		<message>
			<source>Flow is not support!</source>
			<translation>流向不支持!</translation>
		</message>
		<message>
			<source>Fail to find bran node!</source>
			<translation>未查到分支节点!</translation>
		</message>
		<message>
			<source>CurPortConnectOtherPipe</source>
			<translation>当前端口已连接到其他对象!</translation>
		</message>
		<message>
			<source>BranchNotPipeFitting</source>
			<translation>连接失败</translation>
		</message>
		<message>
			<source>BranchHeadSelectFirst</source>
			<translation>连接失败</translation>
		</message>
		<message>
			<source>BranchTailSelectLast</source>
			<translation>连接失败</translation>
		</message>
	</context>
	
	<context>
		<name>ErrorUiComBranConn</name>
		<message>
			<source>Fail to find bran node!</source>
			<translation>未查到分支节点!</translation>
		</message>
	</context>

	<context>
		<name>NameTypeDialog</name>
		<message>
			<source>NodeName</source>
			<translation>节点名称</translation>
		</message>
		<message>
			<source>Name</source>
			<translation>名称</translation>
		</message>
		<message>
			<source>Confirm</source>
			<translation>确定</translation>
		</message>
		<message>
			<source>Cancel</source>
			<translation>取消</translation>
		</message>
	</context>
</TS>