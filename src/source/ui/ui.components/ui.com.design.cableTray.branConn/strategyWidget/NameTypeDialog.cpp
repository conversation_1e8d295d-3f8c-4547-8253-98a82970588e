#include "NameTypeDialog.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "nodeTree/WDNodeTree.h"

NameTypeDialog::NameTypeDialog(WD::WDCore& core, QWidget *parent)
    : QDialog(parent)
    , _core(core)
{
    ui.setupUi(this);
    
    // 绑定事件通知响应
    connect(ui.lineEditName,    &QLineEdit::editingFinished,this, &NameTypeDialog::slotNameEditFinished);
    connect(ui.pushButtonOK,    &QPushButton::clicked,      this, &NameTypeDialog::slotOKClicked);
    connect(ui.pushButtonCancel,&QPushButton::clicked,      this, &NameTypeDialog::slotCancelClicked);
}
NameTypeDialog::~NameTypeDialog()
{
}

void NameTypeDialog::retranslateUi(const std::string& cxt)
{
    WDUnused(cxt);
    Trs("NameTypeDialog"
        , static_cast<QDialog*>(this)
        , ui.label

        , ui.pushButtonOK
        , ui.pushButtonCancel);
}

void NameTypeDialog::slotNameEditFinished()
{
    _pCur = _core.nodeTree().findNode(ui.lineEditName->text().toUtf8().data());
    if (_pCur.lock() == nullptr)
    {
        ui.lineEditName->clear();
    }
}
void NameTypeDialog::slotOKClicked()
{
    this->accept();
}
void NameTypeDialog::slotCancelClicked()
{
    this->reject();
}

void NameTypeDialog::showEvent(QShowEvent* event)
{
    // 统一将焦点设置到窗体，用于在窗口打开时清除其他控件焦点
    this->setFocus();
    WDUnused(event);
}
