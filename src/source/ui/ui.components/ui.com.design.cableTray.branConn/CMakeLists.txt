set(TARGET_NAME ui_com_design_cableTray_branConn)

set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON) 
find_package(Qt5 COMPONENTS Core Widgets REQUIRED)

# 递归搜索 头文件，源文件，ui文件
file(GLOB_RECURSE HEADER_FILES "${CMAKE_CURRENT_SOURCE_DIR}/*.h"
                               "${CMAKE_CURRENT_SOURCE_DIR}/*.hpp")
file(GLOB_RECURSE SOURCE_FILES "${CMAKE_CURRENT_SOURCE_DIR}/*.cpp")
file(GLOB_RECURSE FORM_FILES   "${CMAKE_CURRENT_SOURCE_DIR}/*.ui")

# 需要排除的文件
set(EXCLUDE_FILES
)

# 将需要排除的文件转换为绝对路径，方便对其进行排除
set(EXCLUDE_ABS_PATHS "")
foreach(EXCLUDE_FILE ${EXCLUDE_FILES})
    get_filename_component(EXCLUDE_ABS_PATH "${EXCLUDE_FILE}" ABSOLUTE)
    list(APPEND EXCLUDE_ABS_PATHS ${EXCLUDE_ABS_PATH})
endforeach()

# 排除不需要的文件
list(REMOVE_ITEM HEADER_FILES ${EXCLUDE_ABS_PATHS})
list(REMOVE_ITEM SOURCE_FILES ${EXCLUDE_ABS_PATHS})
list(REMOVE_ITEM FORM_FILES   ${EXCLUDE_ABS_PATHS})

add_library(${TARGET_NAME} SHARED
        ${HEADER_FILES}
        ${SOURCE_FILES}
        ${FORM_FILES}
)

target_link_libraries(${TARGET_NAME} PUBLIC wizDesignerCore ui.commonLib.custom ui.commonLib.WeakObject)
target_link_libraries(${TARGET_NAME} PUBLIC Qt5::Core Qt5::Widgets qtpropertybrowser)
target_include_directories(${TARGET_NAME} PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})

add_custom_command(
    TARGET ${TARGET_NAME}
    POST_BUILD
    COMMAND             ${CMAKE_COMMAND} -E make_directory ./translations
    COMMAND             ${CMAKE_COMMAND} -E copy_directory ./translations ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/ui/components/${TARGET_NAME}/translations
    WORKING_DIRECTORY    ${CMAKE_CURRENT_SOURCE_DIR}
    COMMENT             "copy directory translations..."
)