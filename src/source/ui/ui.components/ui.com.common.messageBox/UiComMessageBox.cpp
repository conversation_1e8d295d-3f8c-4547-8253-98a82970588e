#include "UiComMessageBox.h"
#include "core/message/WDMessage.h"
#include "core/WDTranslate.h"
#include <QApplication>
#include <QFile>
#include <QDesktopServices>
#include <QUrl>
#include <QMessageBox>

UiComMessageBox::UiComMessageBox(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject* parent)
    : QObject(parent)
    , IUiComponent(mainWindow, attrs)
{
	mWindow().core().message().noticeMessage() = std::bind(&UiComMessageBox::moreActionDesiMessageBox
		, this
		, std::placeholders::_1
		, std::placeholders::_2
		, std::placeholders::_3
		, std::placeholders::_4
		, std::placeholders::_5
		, std::placeholders::_6
		, std::placeholders::_7);

}

UiComMessageBox::~UiComMessageBox()
{
	mWindow().core().message().noticeMessage() = {};
}

void UiComMessageBox::onNotice(UiNotice * pNotice)
{
    int nType = pNotice->type();
    switch (nType)
    {
    case UiNoticeType::UNT_Action:
    {
    }
    break;
    default:
        break;
    }
}

int UiComMessageBox::moreActionDesiMessageBox(const std::string& text,
	WD::WDMessage::MessageType type
	, const std::string& title
	, const std::string& button0
	, const std::string& button1
	, const std::string& button2
	, WD::WDMessage::MessageLevel level)
{
	// 超出等级限制则不弹窗
	int		limitLevel = WD::Core().message().limitLevel();
	if (level < limitLevel)
	{
		// !TODO: 提示消息等级超出等级限制
		return -1;
	}

	WD::WDCxtTsBg("UiComMessageBox");	
	QString	tText = QString::fromUtf8(WD::WDCxtTs(text).c_str());
	QString	tTitle = QString::fromUtf8(WD::WDCxtTs(title).c_str());
	QString	tButton0 = QString::fromUtf8(WD::WDCxtTs(button0).c_str());
	QString	tButton1 = QString::fromUtf8(WD::WDCxtTs(button1).c_str());
	QString	tButton2 = QString::fromUtf8(WD::WDCxtTs(button2).c_str());
	WD::WDCxtTsEd();

	int	res = -1;
	if (text.size() <= TextCriticalSize)
	{
		switch (type)
		{
		case WD::WDMessage::MT_Info:
			{
				if (level < 0)
					return false;
				res = QMessageBox::information(nullptr, tTitle, tText, tButton0);
			}
			break;
		case WD::WDMessage::MT_Question:
			{
				if (!button1.empty() && !button2.empty())
				{
					res = QMessageBox::question(nullptr, tTitle, tText, tButton0, tButton1, tButton2);
				}
				else if (!button1.empty() && button2.empty())
				{
					res = QMessageBox::question(nullptr, tTitle, tText, tButton0, tButton1);
				}
				else
				{
					res = QMessageBox::question(nullptr, tTitle, tText, tButton0);
				}
			}
			break;
		case WD::WDMessage::MT_Warn:
			{
				if (level < 1)
					return false;
				res = QMessageBox::warning(nullptr, tTitle, tText, tButton0);
			}
			break;
		case WD::WDMessage::MT_Error:
			{
				if (level < 2)
					return false;
				res = QMessageBox::critical(nullptr, tTitle, tText, tButton0);
			}
			break;
		default:
			break;
		}
	}
	else
	{
		switch (type)
		{
		case WD::WDMessage::MT_Info:
			{
				if (level < 0)
					return false;
			}
			break;
		case WD::WDMessage::MT_Warn:
			{
				if (level < 1)
					return false;
			}
			break;
		case WD::WDMessage::MT_Error:
			{
				if (level < 2)
					return false;
			}
			break;
		default:
			break;
		}
		auto messageBoxDialog = MessageBoxDialog(mWindow().core(), mWindow().widget());
		messageBoxDialog.moreActionDesiMessageBox(tText, tTitle, tButton0, tButton1, tButton2);
		if (messageBoxDialog.exec() == QDialog::Accepted)
			res = messageBoxDialog.ret;
	}

	return res;
}