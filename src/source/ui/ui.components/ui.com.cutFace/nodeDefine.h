#include    "../../../utilLib/util.isoSymbol/WDSvgReader.h"
#include    "core/businessModule/design/WDBDDesign.h"
#include    "core/businessModule/typeMgr/WDBMTypeRegistHelpter.h"
#include    "core/businessModule/serialize/WDBMAttrReaderWriter.h"
#include    "core/businessModule/design/levelCommon/WDBMDCommSub.h"
#include    "core/math/geometric/curve/Curves.h"
#include    "core/WDCore.h"
#include    "core/viewer/WDViewer.h"

WD_NAMESPACE_BEGIN

class Integrate : public WDBDDesign
    , public WDSelectionInterface
    , public WDGraphableInterface
    , public WDCollisibleInterface
{
    WDBD_DECL(Integrate, INTEGRATE)
protected:
    // ������
    WDGeometryPolyhedron::SharedPtr _pGeom  = nullptr;
public:
    Integrate()
    {
        _pGeom = WDGeometryPolyhedron::MakeShared(nullptr);
        noticeGGeometriesAdd({ _pGeom });
    }
    ~Integrate()
    {
    }
public:
    virtual const WDSelectionInterface* selectionSupporter() const override final
    {
        return this;
    }
    virtual bool pickup(const DMat4& transformMatrix
        , const WDPickupParam& param
        , PickupResult& outResult) const override final
    {
        if (_pGeom == nullptr)
            return false;
        return _pGeom->pickup(transformMatrix, param, outResult);
    }
    virtual FrameSelectResult frameSelect(const DMat4& transformMatrix
        , const WDFrameSelectParam& param) const override final
    {
        if (_pGeom == nullptr)
            return FrameSelectResult::FSR_NoData;
        return _pGeom->frameSelect(transformMatrix, param);
    }
    virtual WDGraphableInterface* graphableSupporter() override final
    {
        return this;
    }
    virtual DAabb3 gAabb() override final
    {
        if (_pGeom == nullptr)
            return DAabb3::Null();
        return _pGeom->aabb();
    }
    virtual WDGeometries gGeometries(GGeomType gGeomsType)  override final
    {
        WDUnused(gGeomsType);
        return {_pGeom};
    }
    virtual const WDKeyPoints* gKeyPoints() override final
    {
        return nullptr;
    }
    virtual WDCollisibleInterface* collisibleSupporter() override final
    {
        return nullptr;
    }
    virtual WDCollisions cCollisions() override final
    {
        return {};
    }
    virtual bool hasHoles() const override final
    {
        return false;
    }
public:
    static void  RegistAttribute(WDBMTypeDesc& des)
    {
        WDBDDesign::RegistAttribute(des);
        des.setParentTypes({"WORL"});
    }
    virtual void copy(const WDBDBase& src) override
    {
        if (this == &src)
            return;

        const TThis* pSrc = dynamic_cast<const TThis*>(&src);
        if (pSrc == nullptr)
            return;
        WDBDDesign::copy(src);
    }
protected:
    virtual void read(WDBMAttrReader& ar) override
    {
        WDBDDesign::read(ar);
    }
    virtual void write(WDBMAttrWriter& aw) const override
    {
        WDBDDesign::write(aw);
    }
public:
    virtual void onModelUpdate(WDCore& core, WDNode& node) override final
    {
        WDBDDesign::onModelUpdate(core, node);
    }
};


void AllRegist(WDCore& core);

WD_NAMESPACE_END

