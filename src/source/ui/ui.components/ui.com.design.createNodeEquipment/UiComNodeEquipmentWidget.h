#pragma once

#include <QDialog>
#include "ui_UiComNodeEquipmentWidget.h"
#include "core/WDCore.h"
#include "core/nodeTree/WDNodeTree.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/node/WDNode.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiPositionCaptureHelpter.h"
#include "core/scene/WDRenderObject.h"
#include "core/viewer/primitiveRender/WDTextRender.h"
#include "core/geometry/standardPrimitives/WDGeometrySphere.h"
#include "common/WDContext.h"
#include "core/viewer/WDViewer.h"
#include "core/material/WDDrawHelpter.h"
#include "../../ui.commonLibrary//ui.commonLib.custom/UiNodeNameHelpter.h"


class DotRender : public WD::WDRenderObject
{
private:
    WD::FVec3               _pos;
    WD::WDMaterialColor     _matColor;
    WD::WDInstance          _inst;
    WD::WDGeometrySphere::SharedPtr _pPointSphere;
public:
    DotRender() : WDRenderObject(WD::WDRenderLayer::RL_Scene)
    {
        _pPointSphere = WD::WDGeometrySphere::MakeShared(1.0f);
        setColor({ 255, 165, 0 });
        setPosition({ 0, 0, 0 });
        _inst._color = _matColor.color();
    }
public:
    inline void setColor(WD::Color c)
    {
        _matColor.setColor(c);
    }
    inline void setPosition(WD::FVec3 pos)
    {
        _pos = pos;
    }
public:
    virtual void updateAabb(WD::WDContext& , const WD::WDScene&) override
    {

    }
    virtual void update(WD::WDContext& context, const WD::WDScene&) override
    {
        auto unitF = context.camera().pixelU((WD::DVec3)_pos, context._viewer.size()); // 视角拉得越近，这个比例越小
        _inst._local = WD::FMat4::Compose(_pos, WD::FVec3(static_cast<float>(unitF) * 5));
    }
    virtual void render(WD::WDContext& context, const WD::WDScene&) override
    {
        WD::WDDrawHelpter::Guard dg(context, _matColor);
        dg.drawInstance({ _inst }, *(_pPointSphere->mesh()), WD::WDMesh::Solid);
    }
};

class UiComNodeEquipmentWidget : public QDialog
{
    Q_OBJECT
public:
    UiComNodeEquipmentWidget(WD::WDCore& app, const std::string& type, QWidget *parent = Q_NULLPTR);
    ~UiComNodeEquipmentWidget();
protected:
    virtual void showEvent(QShowEvent* evt) override;
    virtual void hideEvent(QHideEvent* e) override;
private slots:
    //确定按键槽函数
    void onOkButtonClicked();
    //取消按键槽函数
    void onCancelButtonClicked();
signals:
    /**
    * @brief 需要重新分析合法父节点并进行相关设置的信号
    * 目前，发送信号的事件有：窗口显示、节点树当前节点改变
    */
    void sigNeedReanalyzeParent();
private:
    // 根据模型树当前选中节点获取管嘴可挂载的父节点
    WD::WDNode::SharedPtr getParentNode() const;
    //创建设备/子设备节点
    bool createNode();
    // 设置坐标捕捉工具的transform
    void setCaptureTransform();
private:
    //模型树当前节点改变
    void onNodeTreeCurrentNodeChanged(WD::WDNode::SharedPtr pCurrNode
        , WD::WDNode::SharedPtr pPrevNode
        , WD::WDNodeTree& sender);
    /**
     * @brief 界面翻译
    */
    void retranslateUi();
private:
    Ui::UiComNodeEquipmentWidget ui;
    WD::WDCore& _app;
    // 创建类型
    std::string _type;
    // 坐标拾取
    UiPositionCaptureHelpter    _positionCaptureHelpter;
    // 拾取标记
    DotRender   _dotRender;
    WD::WDNode::WeakPtr         _parent;
    // 节点名称助手
    UiNodeNameHelpter           _nameHelpter;
};
