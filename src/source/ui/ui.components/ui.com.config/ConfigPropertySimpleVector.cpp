#include "ConfigPropertySimpleVector.h"


SimpleVectorType::SimpleVectorType(ConfigPropertyWidget& ownWidget
    , QtGroupPropertyManager* pGroupMgr
    , QtAbstractPropertyManager* pManager
    , WD::WDConfig::Item* pItem)
    : PropertyBaseType(ownWidget, pItem)
    , _pGroupMgr(pGroupMgr)
    , _pManager(pManager)
{
    _vectorSize = 0;
}

SimpleVectorType::~SimpleVectorType()
{
}

bool SimpleVectorType::updateValueFromWDPty()
{
    if (_pItem == nullptr)
        return false;
    if (_pManager == nullptr)
        return false;

    //获取名称
    const std::string& itemText  = _pItem->displayText();
    QString tItemText            = QString::fromUtf8(itemText.data());
    //创建根属性栏
    _pRootProperty = _pGroupMgr->addProperty(tItemText);

    WD::WDConfig::Item::ValueType type = _pItem->valueType();
    
    switch (type)
    {
    case WD::WDConfig::Item::ValueType::VT_None:
        break;
    case WD::WDConfig::Item::ValueType::VT_IntVector:
    {
        QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pManager);
        ////记录数组长度
        auto values = *(_pItem->value<std::vector<int>>());
        _vectorSize = values.size();
        //循环创建属性栏
        for (size_t i = 0; i < _vectorSize; i++)
        {
            //创建子属性栏
            QString name    = tItemText + QString::number(i);
            QtProperty* p   = pTMgr->addProperty(name);
            //设置子属性栏值
            int value       = values.at(i);
            pTMgr->setValue(p, value);
            //添加父子关系
            _pRootProperty->addSubProperty(p);
            //存入容器
            _pItemVector.push_back(p);
        }
    }
    break;
    case WD::WDConfig::Item::ValueType::VT_DoubleVector:
    {
        QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pManager);
        ////记录数组长度
        auto values = *(_pItem->value<std::vector<double>>());
        _vectorSize = values.size();
        //循环创建属性栏
        for (size_t i = 0; i < _vectorSize; i++)
        {
            //创建子属性栏
            QString name    = tItemText + QString::number(i);
            QtProperty* p   = pTMgr->addProperty(name);
            //设置子属性栏值
            double value    = values.at(i);
            pTMgr->setValue(p, value);
            //添加父子关系
            _pRootProperty->addSubProperty(p);
            //存入容器
            _pItemVector.push_back(p);
        }
    }
    break;
    case WD::WDConfig::Item::ValueType::VT_StringVector:
    {
        QtStringPropertyManager* pTMgr = static_cast<QtStringPropertyManager*>(_pManager);
        ////记录数组长度
        auto values = *(_pItem->value<std::vector<std::string>>());
        _vectorSize = values.size();
        //循环创建属性栏
        for (size_t i = 0; i < _vectorSize; i++)
        {
            //创建子属性栏
            QString name    = tItemText + QString::number(i);
            QtProperty* p   = pTMgr->addProperty(name);
            //设置子属性栏值
            std::string value = values.at(i);
            QString qStrValue = QString::fromUtf8(value.data());
            pTMgr->setValue(p, qStrValue);
            //添加父子关系
            _pRootProperty->addSubProperty(p);
            //存入容器
            _pItemVector.push_back(p);
        }
    }
    break;
    default:
        break;
    }
    

    return true;
}

bool SimpleVectorType::applyValue()
{
    if (_pRootProperty == nullptr)
        return false;
    if (_pManager == nullptr)
        return false;
    if (_pGroupMgr == nullptr)
        return false;
    if (_pItemVector.empty())
        return false;


    WD::WDConfig::Item::ValueType type = _pItem->valueType();
    switch (type)
    {
    case WD::WDConfig::Item::ValueType::VT_None:
        break;
    case WD::WDConfig::Item::ValueType::VT_IntVector:
    {
        QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);

        std::vector<int> valueVector;
        valueVector.reserve(_pItemVector.size());
        //循环获取值
        for (size_t i = 0; i < _vectorSize; i++)
        {
            QtProperty* pSubPty = _pItemVector.at(i);
            bool value          = pTMgr->value(pSubPty).toBool();

            valueVector.push_back(value ? 1 : 0);
        }
        _pItem->setValue(valueVector);
    }
    break;
    case WD::WDConfig::Item::ValueType::VT_DoubleVector:
    {
        QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pManager);

        std::vector<double> valueVector;
        valueVector.reserve(_pItemVector.size());
        //循环获取值
        for (size_t i = 0; i < _vectorSize; i++)
        {
            QtProperty* pSubPty = _pItemVector.at(i);
            double value = pTMgr->value(pSubPty);
            valueVector.push_back(value);
        }
        _pItem->setValue(valueVector);
    }
    break;
    case WD::WDConfig::Item::ValueType::VT_StringVector:
    {
        QtStringPropertyManager* pTMgr = static_cast<QtStringPropertyManager*>(_pManager);

        std::vector<std::string> valueVector;
        valueVector.reserve(_pItemVector.size());
        //循环获取值
        for (size_t i = 0; i < _vectorSize; i++)
        {
            QtProperty* pSubPty = _pItemVector.at(i);
            QString qStrValue   = pTMgr->value(pSubPty);
            std::string value   = qStrValue.toUtf8().data();
            valueVector.push_back(value);
        }
        _pItem->setValue(valueVector);
    }
    break;
    default:
        break;
    }


    return true;
}

bool SimpleVectorType::contains(QtProperty * pItem) const
{
    if (pItem == _pRootProperty)
        return true;
    for (auto var : _pItemVector)
    {
        if (pItem == var)
            return true;
    }
    return false;
}

QtProperty * SimpleVectorType::getRootProperty()
{
    return _pRootProperty;
}

bool SimpleVectorType::updateShowP(QtProperty * pItem)
{
    WDUnused(pItem);
    return true;
}


