#pragma once
#include "ConfigPropertyBaseType.h"

class Mat4Type : public PropertyBaseType
{
private:
    //��������
    QtProperty* _pRootProperty;
    // 4X4 16����������
    std::array<QtProperty* , 4>     _subProperty;
    std::array<QtProperty* , 16>    _subSubProperty;
    QtGroupPropertyManager*         _pGroupMgr;
    QtStringPropertyManager*        _pStringMgr;
    QtDoublePropertyManager*        _pDoubleMgr;
    QString                         _valueStr;
public:
    Mat4Type(ConfigPropertyWidget& ownWidget
    , QtGroupPropertyManager* pGroupMgr
    , QtStringPropertyManager* pStringMgr
    , QtDoublePropertyManager* pDoubleMgr
    , WD::WDConfig::Item* pPtr);
    ~Mat4Type();
public:
    /**
    * @brief WDProperty��ֵ������ʾ����
    * @return true ���³ɹ� false ����ʧ��
    */
    virtual bool updateValueFromWDPty() override;
    /**
    * @brief �������ֵӦ�õ������WDProperty������
    * @return true Ӧ�óɹ� false Ӧ��ʧ��
    */
    virtual bool applyValue() override;
    /**
    * @brief �Ƿ����������������
    * @param pPty ����������
    * @return true ���� false ������
    */
    virtual bool contains(QtProperty* pPty) const override;
    /**
    * @brief ���ظ�����������
    */
    virtual QtProperty* getRootProperty() override;
protected:
    /**
    * @brief ��������QtProperty��ʾ
    */
    virtual bool updateShowP(QtProperty* pPty) override;
private:

    /**
    * @brief ��ʼ������
    * @return ���ظ�������
    */
    QtProperty* initCreate();
    /**
    * @brief ���������޸���������
    * @param index ����ֵ
    * @return true �޸ĳɹ� false �޸�ʧ��
    */
    bool updateSubProperty(int index);
    /**
    * @brief ���������޸ĸ�������
    * @param index ����ֵ
    * @return true �޸ĳɹ� false �޸�ʧ��
    */
    bool updateParentProperty(int index);
};
//Mat3����
class Mat3Type : public PropertyBaseType
{
private:
    //��������
    QtProperty* _pRootProperty;
    // 3X3 9����������
    std::array<QtProperty* , 3>     _subProperty;
    std::array<QtProperty* , 9>    _subSubProperty;
    QtGroupPropertyManager*         _pGroupMgr;
    QtStringPropertyManager*        _pStringMgr;
    QtDoublePropertyManager*        _pDoubleMgr;
    QString                         _valueStr;
public:
    Mat3Type(ConfigPropertyWidget& ownWidget
    , QtGroupPropertyManager* pGroupMgr
    , QtStringPropertyManager* pStringMgr
    , QtDoublePropertyManager* pDoubleMgr
    , WD::WDConfig::Item* pPtr);
    ~Mat3Type();
public:
    /**
    * @brief WDProperty��ֵ������ʾ����
    * @return true ���³ɹ� false ����ʧ��
    */
    virtual bool updateValueFromWDPty() override;
    /**
    * @brief �������ֵӦ�õ������WDProperty������
    * @return true Ӧ�óɹ� false Ӧ��ʧ��
    */
    virtual bool applyValue() override;
    /**
    * @brief �Ƿ����������������
    * @param pPty ����������
    * @return true ���� false ������
    */
    virtual bool contains(QtProperty* pPty) const override;
    /**
    * @brief ���ظ�����������
    */
    virtual QtProperty* getRootProperty() override;
protected:
    /**
    * @brief ��������QtProperty��ʾ
    */
    virtual bool updateShowP(QtProperty* pPty) override;
private:

    /**
    * @brief ��ʼ������
    * @return ���ظ�������
    */
    QtProperty* initCreate();
    /**
    * @brief ���������޸���������
    * @param index ����ֵ
    * @return true �޸ĳɹ� false �޸�ʧ��
    */
    bool updateSubProperty(int index);
        /**
    * @brief ���������޸ĸ�������
    * @param index ����ֵ
    * @return true �޸ĳɹ� false �޸�ʧ��
    */
    bool updateParentProperty(int index);
};


//Mat2����
class Mat2Type : public PropertyBaseType
{
private:
    //��������
    QtProperty* _pRootProperty;
    // 3X3 9����������
    std::array<QtProperty* , 2>     _subProperty;
    std::array<QtProperty* , 4>    _subSubProperty;
    QtGroupPropertyManager*         _pGroupMgr;
    QtStringPropertyManager*        _pStringMgr;
    QtDoublePropertyManager*        _pDoubleMgr;
    QString                         _valueStr;
public:
    Mat2Type(ConfigPropertyWidget& ownWidget
    , QtGroupPropertyManager* pGroupMgr
    , QtStringPropertyManager* pStringMgr
    , QtDoublePropertyManager* pDoubleMgr
    , WD::WDConfig::Item* pPtr);
    ~Mat2Type();
public:
    /**
    * @brief WDProperty��ֵ������ʾ����
    * @return true ���³ɹ� false ����ʧ��
    */
    virtual bool updateValueFromWDPty() override;
    /**
    * @brief �������ֵӦ�õ������WDProperty������
    * @return true Ӧ�óɹ� false Ӧ��ʧ��
    */
    virtual bool applyValue() override;
    /**
    * @brief �Ƿ����������������
    * @param pPty ����������
    * @return true ���� false ������
    */
    virtual bool contains(QtProperty* pPty) const override;
    /**
    * @brief ���ظ�����������
    */
    virtual QtProperty* getRootProperty() override;
protected:
    /**
    * @brief ��������QtProperty��ʾ
    */
    virtual bool updateShowP(QtProperty* pPty) override;
private:

    /**
    * @brief ��ʼ������
    * @return ���ظ�������
    */
    QtProperty* initCreate();
    /**
    * @brief ���������޸���������
    * @param index ����ֵ
    * @return true �޸ĳɹ� false �޸�ʧ��
    */
    bool updateSubProperty(int index);
        /**
    * @brief ���������޸ĸ�������
    * @param index ����ֵ
    * @return true �޸ĳɹ� false �޸�ʧ��
    */
    bool updateParentProperty(int index);
};
