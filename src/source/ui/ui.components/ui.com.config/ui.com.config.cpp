#include    "ui.com.config.h"
#include    "WDTranslate.h"
#include    "WDCore.h"
#include    "core/businessModule/design/WDBMDesign.h"
#include    "core/viewer/WDViewer.h"
#include    "core/scene/WDScene.h"

UIComConfig::UIComConfig(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject *parent)
    : QObject(parent)
    , IUiComponent(mainWindow, attrs)
{
	_pWidget = new ConfigPropertyWidget(mainWindow.core(), mainWindow.collaboration());

    _pWidget->layout()->setContentsMargins(2, 2, 2, 2);
    _pWidget->layout()->setSpacing(2);
    // 模块字符串
    std::string modelName = mainWindow.moduleType();
    std::string tanslateName = modelName + ":Ui:Config";
    std::string value = WD::WDTs(tanslateName, "Config");
    _pWidget->setWindowTitle(QString::fromUtf8(value.c_str()));

    WD::WDCore& app = mainWindow.core();
    // 获取app中的孔洞配置默认值
    const auto& holesDrawn = app.holesDrawn();
    //孔洞开关设置为默认值
    auto& cfgHolesDrawn = app.cfg().get<bool>("HolesDrawn", false);
    cfgHolesDrawn.setDisplayText("Hole switch");
    cfgHolesDrawn.setValue(holesDrawn.first);
    cfgHolesDrawn.bindFunction({ this, &UIComConfig::onCfgHolesDrawnValueChanged });
    //角度公差设置为默认值
    auto& cfgArcTolerance = app.cfg().get<float>("ArcTolerance", 10.0f);
    cfgArcTolerance.setDisplayText("Angular tolerance");
    cfgArcTolerance.setValue(holesDrawn.second);
    cfgArcTolerance.bindFunction({ this, &UIComConfig::onCfgArcToleranceValueChanged });
}
UIComConfig::~UIComConfig()
{
    if (_pWidget != nullptr)
    {
        delete _pWidget;
        _pWidget = nullptr;
    }

    auto& cfgHolesDrawn = mWindow().core().cfg().get<bool>("HolesDrawn", false);
    cfgHolesDrawn.unbindFunction({ this, &UIComConfig::onCfgHolesDrawnValueChanged });

    auto& cfgArcTolerance = mWindow().core().cfg().get<float>("ArcTolerance", 10.0f);
    cfgArcTolerance.unbindFunction({ this, &UIComConfig::onCfgArcToleranceValueChanged });
}

void UIComConfig::onNotice(UiNotice * pNotice)
{

    int nType = pNotice->type();
    switch (nType)
    {
    case UiNoticeType::UNT_AllReady:
    {
        // 加载配置项
        WD::WDCore& app = mWindow().core();


        _pWidget->update(app.cfg().items());

        // 绑定按钮与停靠窗口
        auto pAction = mWindow().queryAction("action.display.configure");
        UiDockOptions options;
        options.allowAreas = Qt::DockWidgetArea::LeftDockWidgetArea | Qt::DockWidgetArea::RightDockWidgetArea;
        options.initArea = Qt::DockWidgetArea::RightDockWidgetArea;
        options.bToggleViewAction = true;
        auto pDock = mWindow().addDock(_pWidget, options);
        if (pAction != nullptr && pDock != nullptr)
        {
            connect(pDock, &QDockWidget::visibilityChanged, this, [=](bool visible)
                {
                    pAction->setChecked(visible);
                });
        }
        mWindow().hideDock(_pWidget);
    }

    break;
    case UiNoticeType::UNT_Action:
    {
        UiActionNotice* pActionNotice = static_cast<UiActionNotice*>(pNotice);
        if (pActionNotice->action().is("action.display.configure"))
        {
            bool bChecked = pActionNotice->action().checked();
            if (bChecked)
                mWindow().showDock(_pWidget);
            else
                mWindow().hideDock(_pWidget);
        }
    }
    break;
    default:
        break;
    }
}

void UIComConfig::onCfgHolesDrawnValueChanged(const WD::WDConfigItem& item)
{
    auto pValue = item.value<bool>();
    if (pValue == nullptr)
        return ;
    const bool& value = *pValue;

    // 这里只处理设计模块的更新
    auto moduleType = mWindow().moduleType();
    if (moduleType != "Design")
        return ;

    WD::WDCore& app = this->mWindow().core();
    // 获取app中的配置值
    WD::WDCore::HolesDrawn& holesDrawn = app.holesDrawn();
    if (value == holesDrawn.first)
        return ;
    // 修改app中的配置值
    holesDrawn.first = value;
    
    bool holesOnlyScene = true;
    if (holesOnlyScene)
    {
        // 只给场景中已添加的节点做掏洞处理
        app.scene().holesDrawnChanged();
    }
    else
    {
        // 从设计模块根节点开始, 触发更新, 对所有节点进行掏洞处理
        app.getBMDesign().root()->update(true);
    }
}
void UIComConfig::onCfgArcToleranceValueChanged(const WD::WDConfigItem& item)
{
    auto pValue = item.value<float>();
    if (pValue == nullptr)
        return ;
    const float& value = *pValue;

    // 这里只处理设计模块的更新
    auto moduleType = mWindow().moduleType();
    if (moduleType != "Design")
        return ;

    WD::WDCore& app = this->mWindow().core();

    // 获取app中的配置值
    WD::WDCore::HolesDrawn& holesDrawn = app.holesDrawn();
    // 值未改变，不触发更新
    if (holesDrawn.second == value)
        return ;

    // 修改app中的配置值
    holesDrawn.second = value;

    // 未开启孔洞时，不触发更新
    if (!holesDrawn.first)
        return ;

    bool holesOnlyScene = true;
    if (holesOnlyScene)
    {
        // 只给场景中已添加的节点做掏洞处理
        app.scene().holesDrawnChanged();
    }
    else
    {
        // 从设计模块根节点开始, 触发更新, 对所有节点进行掏洞处理
        if (app.getBMDesign().root() != nullptr)
        {
            app.getBMDesign().root()->update(true);
        }
    }
}


