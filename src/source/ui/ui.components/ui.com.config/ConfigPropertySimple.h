#pragma once
#include "ConfigPropertyBaseType.h"
class SimpleType : public PropertyBaseType
{
public:
    //属性栏保存的值
    QtProperty*                  _pPproperty;
    QtAbstractPropertyManager*   _pManager;
public:
    SimpleType(ConfigPropertyWidget& ownWidget, QtAbstractPropertyManager* pManager, WD::WDConfig::Item* pItem);
    ~SimpleType();
public:
    virtual bool updateValueFromWDPty() override;
    virtual bool applyValue() override;
    virtual bool contains(QtProperty* pPty) const override;
    virtual QtProperty* getRootProperty() override;
protected:
    virtual bool updateShowP(QtProperty* pPty) override;
private:
    
    //创建QtProperty对象
    QtProperty* initCreate();
};