#pragma once
#include "ConfigPropertyBaseType.h"

class ComplicateBaseType
{
protected:
    //�����WDPropertyָ��
    ConfigPropertyWidget&           _ownWidget;
    QString&                        _name;
    WD::WDPropertyDataType         _type;
public:
    ComplicateBaseType(ConfigPropertyWidget& ownWidget,QString& name, WD::WDPropertyDataType type);
    virtual ~ComplicateBaseType();
public:
    /**
    * @brief �Ƿ����������������
    * @param pPty ����������
    * @return true ���� false ������
    */
    virtual bool contains(QtProperty* pPty) const = 0;
    /**
    * @brief �������õĳ�ʼֵ������ʾ����
    * @return true ���³ɹ� false ����ʧ��
    */
    virtual bool updateValue() = 0;
    /**
    * @brief ��������������ֵ���޸ĸ��±������������ֵ
    * @param pPty ����ֵ���ĵ�����������
    * @return true ���³ɹ� false ����ʧ��
    */
    bool updateShow(QtProperty* pPty);
    /**
    * @brief ���ظ�����������
    */
    virtual QtProperty* getRootProperty() = 0;
    /**
    * @brief ���ظ����͵��ַ���ֵ
    */
    virtual std::string getValue() = 0;
protected:
    /**
    * @brief ��������QtProperty��ʾ
    *   ����ʵ��
    */
    virtual bool updateShowP(QtProperty* pPty) = 0;

    /**
    * @brief �ж�������ַ����Ƿ�Ϸ�
    * @param rootValue Ҫ�и��QString�ַ��� 
    * @param size      Ҫ�зֵ��Ӵ�����
    * @return true �Ϸ� false ���Ϸ�
    */
    bool isStringValid(QString rootValue, int size);
};

class ComplicateVec4Type : public ComplicateBaseType
{
private:
    //�����������ֵ
    QtProperty*                  _pRootProperty;
    QtStringPropertyManager*     _pStringMgr;
    QtAbstractPropertyManager*   _pManager;
    std::string&                 _valueStr;
    std::array<QtProperty*, 4>   _pSubProperty;
public:
    ComplicateVec4Type(ConfigPropertyWidget& ownWidget
        , QString& name
        , WD::WDPropertyDataType type
        , QtStringPropertyManager* pStringMgr
        , QtAbstractPropertyManager* pManager
        , std::string& valueStr);
    ~ComplicateVec4Type();
public:
    /**
    * @brief �Ƿ����������������
    * @param pPty ����������
    * @return true ���� false ������
    */
    virtual bool contains(QtProperty* pPty) const override;
    /**
    * @brief �������õĳ�ʼֵ������ʾ����
    * @return true ���³ɹ� false ����ʧ��
    */
    virtual bool updateValue() override;
    /**
    * @brief ���ظ�����������
    */
    virtual QtProperty* getRootProperty() override;
    /**
    * @brief ���ظ����͵��ַ���ֵ
    */
    virtual std::string getValue()override;
protected:
    /**
    * @brief ��������QtProperty��ʾ
    *   ����ʵ��
    */
    virtual bool updateShowP(QtProperty* pPty) override;
private:
    //����QtProperty����
    QtProperty* initCreate();
    /**
    * @brief �����������£�ͬ����������������ʾ
    * @param rootValue ��������ֵ
    * @return true ���³ɹ� false ����ʧ��
    */
    bool updateSubProperty(QString rootValue);
    /**
    * @brief �����������£�ͬ�����¸�������
    * @return true ���³ɹ� false ����ʧ��
    */
    bool updateRootProperty();

};

class ComplicateVec3Type : public ComplicateBaseType
{
private:
    //�����������ֵ
    QtProperty*                  _pRootProperty;
    QtStringPropertyManager*     _pStringMgr;
    QtAbstractPropertyManager*   _pManager;
    std::string&                 _valueStr;
    std::array<QtProperty*, 3>   _pSubProperty;
public:
    ComplicateVec3Type(ConfigPropertyWidget& ownWidget
        , QString& name
        , WD::WDPropertyDataType type
        , QtStringPropertyManager* pStringMgr
        , QtAbstractPropertyManager* pManager
        , std::string& valueStr);
    ~ComplicateVec3Type();
public:
    /**
    * @brief �Ƿ����������������
    * @param pPty ����������
    * @return true ���� false ������
    */
    virtual bool contains(QtProperty* pPty) const override;
    /**
    * @brief �������õĳ�ʼֵ������ʾ����
    * @return true ���³ɹ� false ����ʧ��
    */
    virtual bool updateValue() override;
    /**
    * @brief ���ظ�����������
    */
    virtual QtProperty* getRootProperty() override;
    /**
    * @brief ���ظ����͵��ַ���ֵ
    */
    virtual std::string getValue()override;
protected:
    /**
    * @brief ��������QtProperty��ʾ
    *   ����ʵ��
    */
    virtual bool updateShowP(QtProperty* pPty) override;
private:
    //����QtProperty����
    QtProperty* initCreate();
    /**
    * @brief �����������£�ͬ����������������ʾ
    * @param rootValue ��������ֵ
    * @return true ���³ɹ� false ����ʧ��
    */
    bool updateSubProperty(QString rootValue);
    /**
    * @brief �����������£�ͬ�����¸�������
    * @return true ���³ɹ� false ����ʧ��
    */
    bool updateRootProperty();

};

class ComplicateVec2Type : public ComplicateBaseType
{
private:
    //�����������ֵ
    QtProperty*                  _pRootProperty;
    QtStringPropertyManager*     _pStringMgr;
    QtAbstractPropertyManager*   _pManager;
    std::string&                 _valueStr;
    std::array<QtProperty*, 2>   _pSubProperty;
public:
    ComplicateVec2Type(ConfigPropertyWidget& ownWidget
        , QString& name
        , WD::WDPropertyDataType type
        , QtStringPropertyManager* pStringMgr
        , QtAbstractPropertyManager* pManager
        , std::string& valueStr);
    ~ComplicateVec2Type();
public:
    /**
    * @brief �Ƿ����������������
    * @param pPty ����������
    * @return true ���� false ������
    */
    virtual bool contains(QtProperty* pPty) const override;
    /**
    * @brief �������õĳ�ʼֵ������ʾ����
    * @return true ���³ɹ� false ����ʧ��
    */
    virtual bool updateValue() override;
    /**
    * @brief ���ظ�����������
    */
    virtual QtProperty* getRootProperty() override;
    /**
    * @brief ���ظ����͵��ַ���ֵ
    */
    virtual std::string getValue()override;
protected:
    /**
    * @brief ��������QtProperty��ʾ
    *   ����ʵ��
    */
    virtual bool updateShowP(QtProperty* pPty) override;
private:
    //����QtProperty����
    QtProperty* initCreate();
    /**
    * @brief �����������£�ͬ����������������ʾ
    * @param rootValue ��������ֵ
    * @return true ���³ɹ� false ����ʧ��
    */
    bool updateSubProperty(QString rootValue);
    /**
    * @brief �����������£�ͬ�����¸�������
    * @return true ���³ɹ� false ����ʧ��
    */
    bool updateRootProperty();

};


class ComplicateQuatType : public ComplicateBaseType
{
private:
    //�����������ֵ
    QtProperty*                     _pRootProperty;
    QtStringPropertyManager*        _pStringMgr;
    QtDoublePropertyManager*        _pDoubleMgr;
    std::string&                    _valueStr;

    QString                         _rootValue;
    std::array<QtProperty*, 2>      _pSubProperty;
    std::array<QString, 2>          _subValues;
    std::array<QtProperty*, 6>      _pSubSubProperty;
    std::array<double, 6>           _subSubValues;
public:
    ComplicateQuatType(ConfigPropertyWidget& ownWidget
        , QString& name
        , WD::WDPropertyDataType type
        , QtStringPropertyManager* pStringMgr
        , QtDoublePropertyManager* pDoubleMgr
        , std::string& valueStr);
    ~ComplicateQuatType();
public:
    /**
    * @brief �Ƿ����������������
    * @param pPty ����������
    * @return true ���� false ������
    */
    virtual bool contains(QtProperty* pPty) const override;
    /**
    * @brief �������õĳ�ʼֵ������ʾ����
    * @return true ���³ɹ� false ����ʧ��
    */
    virtual bool updateValue() override;
    /**
    * @brief ���ظ�����������
    */
    virtual QtProperty* getRootProperty() override;
    /**
    * @brief ���ظ����͵��ַ���ֵ
    */
    virtual std::string getValue()override;
protected:
    /**
    * @brief ��������QtProperty��ʾ
    *   ����ʵ��
    */
    virtual bool updateShowP(QtProperty* pPty) override;
private:
    //��Ԫ��ת������ת�Ľṹ��
    struct RotateData
    {
        double nx;
        double ny;
        double nz;
        double dx;
        double dy;
        double dz;
    
        double& operator[](int index)
        {
            ///if(index > 5 || index < 0)
            ///    return 0;
            double* p = (double*)this;
            return p[index];
        }
    public:
        ///��up��Right��xyzת��Ϊquat
        WD::TQuat<double> rotation(RotateData& data) const
        {
            WD::DVec3 vUp = WD::DVec3(data.nx,data.ny,data.nz);
            WD::DVec3 vRight = WD::DVec3(data.dx,data.dy,data.dz);
            WD::DVec3 vFront = WD::DVec3::Cross(vUp, vRight).normalized();
    
            WD::DMat4 rMat;
            rMat[0] = WD::TVec4<double>(vRight, 0.0);
            rMat[1] = WD::TVec4<double>(vFront, 0.0);
            rMat[2] = WD::TVec4<double>(vUp, 0.0);
            rMat[3] = WD::TVec4<double>(WD::TVec3<double>(0.0), 1.0);
    
            WD::TQuat<double> quat = rMat.extractRotationQuat();
            return quat;
        }
        ///��ȡUp��Right��xyz
        void setRottion(WD::TQuat<double> quat,RotateData& data)
        {
            WD::TMat4<double> rMat = WD::TMat4<double>::FromQuat(quat);
            
            WD::TVec3<double> vRight = WD::TVec3<double>(rMat[0][0], rMat[0][1], rMat[0][2]);
            WD::TVec3<double> vFront = WD::TVec3<double>(rMat[1][0], rMat[1][1], rMat[1][2]);
            WD::TVec3<double> vUp    = WD::TVec3<double>(rMat[2][0], rMat[2][1], rMat[2][2]);
            
            data.nx = vUp.x;
            data.ny = vUp.y;
            data.nz = vUp.z;
            
            data.dx = vRight.x;
            data.dy = vRight.y;
            data.dz = vRight.z;      
        }

        std::string toString()
        {
            QStringList valueList({QString::number(nx,'f',3)
                ,QString::number(ny,'f',3)
                ,QString::number(nz,'f',3)
                ,QString::number(dx,'f',3)
                ,QString::number(dy,'f',3)
                ,QString::number(dz,'f',3)});
            QString str = valueList.join(" ");
            return str.toUtf8().data();
        }

        void fromQString(const QString& str)
        {
            QStringList values = str.split(" ");
            if (values.size() != 6)
                return;
            nx = values[0].toDouble();
            ny = values[1].toDouble();
            nz = values[2].toDouble();
    
            dx = values[3].toDouble();
            dy = values[4].toDouble();
            dz = values[5].toDouble();
        }
    };
    //����QtProperty����
    QtProperty* initCreate();

};

class ComplicateMat4Type : public ComplicateBaseType
{
private:

    //��������
    QtProperty* _pRootProperty;
    // 4X4 16����������
    std::array<QtProperty* , 4>     _subProperty;
    std::array<QtProperty* , 16>    _subSubProperty;
    QtGroupPropertyManager*         _pGroupMgr;
    QtStringPropertyManager*        _pStringMgr;
    QtDoublePropertyManager*        _pDoubleMgr;
    QString                         _rootValue;
    std::string&                    _valueStr;

public:
    ComplicateMat4Type(ConfigPropertyWidget& ownWidget
        , QString& name
        , WD::WDPropertyDataType type
        , QtGroupPropertyManager* pGroupMgr
        , QtStringPropertyManager* pStringMgr
        , QtDoublePropertyManager* pDoubleMgr
        , std::string& valueStr);
    ~ComplicateMat4Type();
public:
    /**
    * @brief �Ƿ����������������
    * @param pPty ����������
    * @return true ���� false ������
    */
    virtual bool contains(QtProperty* pPty) const override;
    /**
    * @brief �������õĳ�ʼֵ������ʾ����
    * @return true ���³ɹ� false ����ʧ��
    */
    virtual bool updateValue() override;
    /**
    * @brief ���ظ�����������
    */
    virtual QtProperty* getRootProperty() override;
    /**
    * @brief ���ظ����͵��ַ���ֵ
    */
    virtual std::string getValue()override;
protected:
    /**
    * @brief ��������QtProperty��ʾ
    *   ����ʵ��
    */
    virtual bool updateShowP(QtProperty* pPty) override;
private:    
    //����QtProperty����
    QtProperty* initCreate();
    /**
    * @brief ���������޸���������
    * @param index ����ֵ
    * @return true �޸ĳɹ� false �޸�ʧ��
    */
    bool updateSubProperty(int index);
    /**
    * @brief ���������޸ĸ�������
    * @param index ����ֵ
    * @return true �޸ĳɹ� false �޸�ʧ��
    */
    bool updateParentProperty(int index);
};

class ComplicateMat3Type : public ComplicateBaseType
{
private:

    //��������
    QtProperty* _pRootProperty;
    // 4X4 16����������
    std::array<QtProperty* , 3>     _subProperty;
    std::array<QtProperty* , 9>    _subSubProperty;
    QtGroupPropertyManager*         _pGroupMgr;
    QtStringPropertyManager*        _pStringMgr;
    QtDoublePropertyManager*        _pDoubleMgr;
    QString                         _rootValue;
    std::string&                    _valueStr;

public:
    ComplicateMat3Type(ConfigPropertyWidget& ownWidget
        , QString& name
        , WD::WDPropertyDataType type
        , QtGroupPropertyManager* pGroupMgr
        , QtStringPropertyManager* pStringMgr
        , QtDoublePropertyManager* pDoubleMgr
        , std::string& valueStr);
    ~ComplicateMat3Type();
public:
    /**
    * @brief �Ƿ����������������
    * @param pPty ����������
    * @return true ���� false ������
    */
    virtual bool contains(QtProperty* pPty) const override;
    /**
    * @brief �������õĳ�ʼֵ������ʾ����
    * @return true ���³ɹ� false ����ʧ��
    */
    virtual bool updateValue() override;
    /**
    * @brief ���ظ�����������
    */
    virtual QtProperty* getRootProperty() override;
    /**
    * @brief ���ظ����͵��ַ���ֵ
    */
    virtual std::string getValue()override;
protected:
    /**
    * @brief ��������QtProperty��ʾ
    *   ����ʵ��
    */
    virtual bool updateShowP(QtProperty* pPty) override;
private:    
    //����QtProperty����
    QtProperty* initCreate();
    /**
    * @brief ���������޸���������
    * @param index ����ֵ
    * @return true �޸ĳɹ� false �޸�ʧ��
    */
    bool updateSubProperty(int index);
    /**
    * @brief ���������޸ĸ�������
    * @param index ����ֵ
    * @return true �޸ĳɹ� false �޸�ʧ��
    */
    bool updateParentProperty(int index);
};


class ComplicateMat2Type : public ComplicateBaseType
{
private:

    //��������
    QtProperty* _pRootProperty;
    // 4X4 16����������
    std::array<QtProperty* , 2>     _subProperty;
    std::array<QtProperty* , 4>    _subSubProperty;
    QtGroupPropertyManager*         _pGroupMgr;
    QtStringPropertyManager*        _pStringMgr;
    QtDoublePropertyManager*        _pDoubleMgr;
    QString                         _rootValue;
    std::string&                    _valueStr;

public:
    ComplicateMat2Type(ConfigPropertyWidget& ownWidget
        , QString& name
        , WD::WDPropertyDataType type
        , QtGroupPropertyManager* pGroupMgr
        , QtStringPropertyManager* pStringMgr
        , QtDoublePropertyManager* pDoubleMgr
        , std::string& valueStr);
    ~ComplicateMat2Type();
public:
    /**
    * @brief �Ƿ����������������
    * @param pPty ����������
    * @return true ���� false ������
    */
    virtual bool contains(QtProperty* pPty) const override;
    /**
    * @brief �������õĳ�ʼֵ������ʾ����
    * @return true ���³ɹ� false ����ʧ��
    */
    virtual bool updateValue() override;
    /**
    * @brief ���ظ�����������
    */
    virtual QtProperty* getRootProperty() override;
    /**
    * @brief ���ظ����͵��ַ���ֵ
    */
    virtual std::string getValue()override;
protected:
    /**
    * @brief ��������QtProperty��ʾ
    *   ����ʵ��
    */
    virtual bool updateShowP(QtProperty* pPty) override;
private:    
    //����QtProperty����
    QtProperty* initCreate();
    /**
    * @brief ���������޸���������
    * @param index ����ֵ
    * @return true �޸ĳɹ� false �޸�ʧ��
    */
    bool updateSubProperty(int index);
    /**
    * @brief ���������޸ĸ�������
    * @param index ����ֵ
    * @return true �޸ĳɹ� false �޸�ʧ��
    */
    bool updateParentProperty(int index);
};

class ComplicateAabb3Type : public ComplicateBaseType
{
private:
    //��������
    QtProperty*                     _pRootProperty;
    std::array<QtProperty* , 2>     _subProperty;
    std::array<QtProperty* , 6>     _subSubProperty;
    QtGroupPropertyManager*         _pGroupMgr;
    QtStringPropertyManager*        _pStringMgr;
    QtDoublePropertyManager*        _pDoubleMgr;
    QString                         _rootValue;
    std::string&                    _valueStr;

public:
    ComplicateAabb3Type(ConfigPropertyWidget& ownWidget
        , QString& name
        , WD::WDPropertyDataType type
        , QtGroupPropertyManager* pGroupMgr
        , QtStringPropertyManager* pStringMgr
        , QtDoublePropertyManager* pDoubleMgr
        , std::string& valueStr);
    ~ComplicateAabb3Type();
public:
    /**
    * @brief �Ƿ����������������
    * @param pPty ����������
    * @return true ���� false ������
    */
    virtual bool contains(QtProperty* pPty) const override;
    /**
    * @brief �������õĳ�ʼֵ������ʾ����
    * @return true ���³ɹ� false ����ʧ��
    */
    virtual bool updateValue() override;
    /**
    * @brief ���ظ�����������
    */
    virtual QtProperty* getRootProperty() override;
    /**
    * @brief ���ظ����͵��ַ���ֵ
    */
    virtual std::string getValue()override;
protected:
    /**
    * @brief ��������QtProperty��ʾ
    *   ����ʵ��
    */
    virtual bool updateShowP(QtProperty* pPty) override;
private:    
    //����QtProperty����
    QtProperty* initCreate();
    /**
    * @brief ���������޸���������
    * @param index ����ֵ
    * @return true �޸ĳɹ� false �޸�ʧ��
    */
    bool updateSubProperty(int index);
    /**
    * @brief ���������޸ĸ�������
    * @param index ����ֵ
    * @return true �޸ĳɹ� false �޸�ʧ��
    */
    bool updateParentProperty(int index);
};


class ComplicateAabb2Type : public ComplicateBaseType
{
private:
    //��������
    QtProperty*                     _pRootProperty;
    std::array<QtProperty* , 2>     _subProperty;
    std::array<QtProperty* , 4>     _subSubProperty;
    QtGroupPropertyManager*         _pGroupMgr;
    QtStringPropertyManager*        _pStringMgr;
    QtDoublePropertyManager*        _pDoubleMgr;
    QString                         _rootValue;
    std::string&                    _valueStr;

public:
    ComplicateAabb2Type(ConfigPropertyWidget& ownWidget
        , QString& name
        , WD::WDPropertyDataType type
        , QtGroupPropertyManager* pGroupMgr
        , QtStringPropertyManager* pStringMgr
        , QtDoublePropertyManager* pDoubleMgr
        , std::string& valueStr);
    ~ComplicateAabb2Type();
public:
    /**
    * @brief �Ƿ����������������
    * @param pPty ����������
    * @return true ���� false ������
    */
    virtual bool contains(QtProperty* pPty) const override;
    /**
    * @brief �������õĳ�ʼֵ������ʾ����
    * @return true ���³ɹ� false ����ʧ��
    */
    virtual bool updateValue() override;
    /**
    * @brief ���ظ�����������
    */
    virtual QtProperty* getRootProperty() override;
    /**
    * @brief ���ظ����͵��ַ���ֵ
    */
    virtual std::string getValue()override;
protected:
    /**
    * @brief ��������QtProperty��ʾ
    *   ����ʵ��
    */
    virtual bool updateShowP(QtProperty* pPty) override;
private:    
    //����QtProperty����
    QtProperty* initCreate();
    /**
    * @brief ���������޸���������
    * @param index ����ֵ
    * @return true �޸ĳɹ� false �޸�ʧ��
    */
    bool updateSubProperty(int index);
    /**
    * @brief ���������޸ĸ�������
    * @param index ����ֵ
    * @return true �޸ĳɹ� false �޸�ʧ��
    */
    bool updateParentProperty(int index);
};


class ComplicateEulerType : public ComplicateBaseType
{
private:
    //��������
    QtProperty*                     _pRootProperty;
    std::array<QtProperty*, 4>      _pSubProperty;
    QtEnumPropertyManager*          _pEnumMgr;
    QtStringPropertyManager*        _pStringMgr;
    QtDoublePropertyManager*        _pDoubleMgr;
    QString                         _rootValue;
    std::string&                    _valueStr;
    int                             _orderIndex;
    QStringList                     _orderStrList;

public:
    ComplicateEulerType(ConfigPropertyWidget& ownWidget
        , QString& name
        , WD::WDPropertyDataType type
        , QtStringPropertyManager* pStringMgr
        , QtDoublePropertyManager* pDoubleMgr
        , QtEnumPropertyManager*   pEnumMgr
        , std::string& valueStr);
    ~ComplicateEulerType();
public:
    /**
    * @brief �Ƿ����������������
    * @param pPty ����������
    * @return true ���� false ������
    */
    virtual bool contains(QtProperty* pPty) const override;
    /**
    * @brief �������õĳ�ʼֵ������ʾ����
    * @return true ���³ɹ� false ����ʧ��
    */
    virtual bool updateValue() override;
    /**
    * @brief ���ظ�����������
    */
    virtual QtProperty* getRootProperty() override;
    /**
    * @brief ���ظ����͵��ַ���ֵ
    */
    virtual std::string getValue()override;
protected:
    /**
    * @brief ��������QtProperty��ʾ
    *   ����ʵ��
    */
    virtual bool updateShowP(QtProperty* pPty) override;
private:    
    //����QtProperty����
    QtProperty* initCreate();
    /**
    * @brief �����������£�ͬ����������������ʾ
    * @param rootValue ��������ֵ
    * @return true ���³ɹ� false ����ʧ��
    */
    bool updateSubProperty(QString rootValue);
    /**
    * @brief �����������£�ͬ�����¸�������
    * @return true ���³ɹ� false ����ʧ��
    */
    bool updateRootProperty(int index);
};
