#pragma once
#include "ConfigPropertyBaseType.h"

class SimpleVectorType: public PropertyBaseType
{
private:
    QtAbstractPropertyManager*          _pManager;
    QtGroupPropertyManager*             _pGroupMgr;
    QtProperty*                         _pRootProperty;
    std::vector<QtProperty*>            _pItemVector;
    //存储数组长度
    size_t                              _vectorSize;

public:
    SimpleVectorType(ConfigPropertyWidget& ownWidget
    , QtGroupPropertyManager* pGroupMgr
    , QtAbstractPropertyManager* pManager
    , WD::WDConfig::Item* pItem);
    ~SimpleVectorType();
public:
    virtual bool updateValueFromWDPty() override;
    virtual bool applyValue() override;
    virtual bool contains(QtProperty* pPty) const override;
    virtual QtProperty* getRootProperty() override;
protected:
    /**
    * @brief 更新所有QtProperty显示
    */
    virtual bool updateShowP(QtProperty* pPty) override;
};