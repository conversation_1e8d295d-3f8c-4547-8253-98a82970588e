#include "ComplicateType.h"
#include "ConfigPropertyWidget.h"


//����Vector�Ļ���
ComplicateBaseType::ComplicateBaseType(ConfigPropertyWidget& ownWidget
    , QString& name
    , WD::WDPropertyDataType type)
    : _ownWidget(ownWidget)
    , _name(name)
    , _type(type)
{

}

ComplicateBaseType::~ComplicateBaseType()
{
}

bool ComplicateBaseType::updateShow(QtProperty* pPty)
{
    _ownWidget.blockValueChangedSignals(true);
    bool bRet = this->updateShowP(pPty);
    _ownWidget.blockValueChangedSignals(false);

    return bRet;
}

bool ComplicateBaseType::isStringValid(QString rootValue, int size)
{
    //�з��ַ���
    QStringList valueList = rootValue.split(" ");
    //�ж����һ���Ӵ��Ƿ�Ϊ��
    if (valueList.back() == QString())
        return false;
    //�жϳ����Ƿ�Ϸ�
    if (valueList.size() != size)
        return false;
    return true;
}



//����Vector��VEC4����
ComplicateVec4Type::ComplicateVec4Type(ConfigPropertyWidget & ownWidget
    , QString & name
    , WD::WDPropertyDataType type
    , QtStringPropertyManager * pStringMgr
    , QtAbstractPropertyManager * pManager
    , std::string & valueStr)
    : ComplicateBaseType(ownWidget, name, type)
    , _pStringMgr(pStringMgr)
    , _pManager(pManager)
    , _valueStr(valueStr)
{
    _pSubProperty.fill(nullptr);
    _pRootProperty = this->initCreate();
    this->updateValue();
}

ComplicateVec4Type::~ComplicateVec4Type()
{
}

bool ComplicateVec4Type::contains(QtProperty * pPty) const
{
    if (pPty == _pRootProperty)
        return true;
    for (auto var : _pSubProperty)
    {
        if (pPty == var)
            return true;
    }
    return false;
}

bool ComplicateVec4Type::updateValue()
{
    if (_valueStr.empty())
        return false;
    if (_pRootProperty == nullptr)
        return false;
    if (_pManager == nullptr)
        return false;
    if (_pStringMgr == nullptr)
        return false;
    switch (_type)
    {
    case WD::PDT_UCVec4:
    {
        QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
        WD::UCVec4 value;
        value.fromString(_valueStr);
        //������Ҫ��ʼ����ֵ
        unsigned char subValue1 = value.x;
        unsigned char subValue2 = value.y;
        unsigned char subValue3 = value.z;
        unsigned char subValue4 = value.w;

        //����ֵ
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        pTMgr->setValue(_pSubProperty.at(2),subValue3);
        pTMgr->setValue(_pSubProperty.at(3),subValue4);

        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2)
            + " " + QString::number(subValue3)
            + " " + QString::number(subValue4);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
    break;
    case WD::PDT_IVec4:
    {
        QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pManager);
        WD::IVec4 value;
        value.fromString(_valueStr);
        //������Ҫ��ʼ����ֵ
        int subValue1 = value.x;
        int subValue2 = value.y;
        int subValue3 = value.z;
        int subValue4 = value.w;
        //����ֵ
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        pTMgr->setValue(_pSubProperty.at(2),subValue3);
        pTMgr->setValue(_pSubProperty.at(3),subValue4);

        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2)
            + " " + QString::number(subValue3)
            + " " + QString::number(subValue4);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
    break;
    case WD::PDT_UIVec4:
    {   
        QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pManager);
        WD::UIVec4 value;
        value.fromString(_valueStr);
        //������Ҫ��ʼ����ֵ
        unsigned int subValue1 = value.x;
        unsigned int subValue2 = value.y;
        unsigned int subValue3 = value.z;
        unsigned int subValue4 = value.w;
        //����ֵ
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        pTMgr->setValue(_pSubProperty.at(2),subValue3);
        pTMgr->setValue(_pSubProperty.at(3),subValue4);

        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2)
            + " " + QString::number(subValue3)
            + " " + QString::number(subValue4);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
    break;
    case WD::PDT_LLVec4:
    {   
        QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
        WD::LLVec4 value;
        value.fromString(_valueStr);
        //������Ҫ��ʼ����ֵ
        long long subValue1 = value.x;
        long long subValue2 = value.y;
        long long subValue3 = value.z;
        long long subValue4 = value.w;
        //����ֵ
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        pTMgr->setValue(_pSubProperty.at(2),subValue3);
        pTMgr->setValue(_pSubProperty.at(3),subValue4);

        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2)
            + " " + QString::number(subValue3)
            + " " + QString::number(subValue4);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
    break;
    case WD::PDT_ULLVec4:
    {   
        QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
        WD::ULLVec4 value;
        value.fromString(_valueStr);
        //������Ҫ��ʼ����ֵ
        unsigned long long subValue1 = value.x;
        unsigned long long subValue2 = value.y;
        unsigned long long subValue3 = value.z;
        unsigned long long subValue4 = value.w;
        //����ֵ
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        pTMgr->setValue(_pSubProperty.at(2),subValue3);
        pTMgr->setValue(_pSubProperty.at(3),subValue4);

        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2)
            + " " + QString::number(subValue3)
            + " " + QString::number(subValue4);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
    case WD::PDT_FVec4:
    {   
        QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pManager);
        WD::FVec4 value;
        value.fromString(_valueStr);
        //������Ҫ��ʼ����ֵ
        float subValue1 = value.x;
        float subValue2 = value.y;
        float subValue3 = value.z;
        float subValue4 = value.w;
        //����ֵ
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        pTMgr->setValue(_pSubProperty.at(2),subValue3);
        pTMgr->setValue(_pSubProperty.at(3),subValue4);

        QString rootValue = QString::number(subValue1, 'f', 2) 
            + " " + QString::number(subValue2, 'f', 2)
            + " " + QString::number(subValue3, 'f', 2)
            + " " + QString::number(subValue4, 'f', 2);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
        break;
    case WD::PDT_DVec4:
    {   
        QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pManager);
        WD::DVec4 value;
        value.fromString(_valueStr);
        //������Ҫ��ʼ����ֵ
        double subValue1 = value.x;
        double subValue2 = value.y;
        double subValue3 = value.z;
        double subValue4 = value.w;
        //����ֵ
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        pTMgr->setValue(_pSubProperty.at(2),subValue3);
        pTMgr->setValue(_pSubProperty.at(3),subValue4);

        QString rootValue = QString::number(subValue1, 'f', 2) 
            + " " + QString::number(subValue2, 'f', 2)
            + " " + QString::number(subValue3, 'f', 2)
            + " " + QString::number(subValue4, 'f', 2);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
        break;
    default:
        return false;
        break;
    }
    return true;
}

QtProperty * ComplicateVec4Type::getRootProperty()
{
    return _pRootProperty;
}

std::string ComplicateVec4Type::getValue()
{
    if (_pRootProperty != nullptr)
    {
        QString qStr = _pStringMgr->value(_pRootProperty);
        return qStr.toUtf8().data();
    }
    return std::string();
}

bool ComplicateVec4Type::updateShowP(QtProperty * pPty)

{
    if (pPty == nullptr)
        return false;
    
    if (_pStringMgr == nullptr || _pManager == nullptr)
        return false;

    if (_pRootProperty == nullptr
        || _pSubProperty.size() != 4)
        return false;


    //���ͱ�ʶ���������������͸�������
    int type = 0;

    if (pPty == _pRootProperty)
    {
        type = 1;
    }

    if(type == 0)
    {
        for (size_t i = 0; i < _pSubProperty.size(); ++i)
        {
            if (_pSubProperty.at(i) == pPty)
            {
                type = 2;
                break;
            }
        }
    }
    switch (type)
    {
    case 1:
        {
            QString rootValue = _pStringMgr->value(_pRootProperty);
            if (! isStringValid(rootValue,4))
                return false;
            //����������ʾֵ
            this->updateSubProperty(rootValue);
        }break;
    case 2:
        {
            //���¸�����ʾ��ֵ
            this->updateRootProperty();
            
        }break;
    default:
        return false;
        break;
    }
    return true;
}

QtProperty * ComplicateVec4Type::initCreate()
{
    if (_pStringMgr == nullptr)
        return nullptr;
    if (_pManager == nullptr)
        return nullptr;
    //����������
    QtProperty* p = nullptr;
    QtProperty* subPty1 = nullptr;
    QtProperty* subPty2 = nullptr;
    QtProperty* subPty3 = nullptr;
    QtProperty* subPty4 = nullptr;

    //��������
    QString subPtyName1 = _name + "X";
    QString subPtyName2 = _name + "Y";
    QString subPtyName3 = _name + "Z";
    QString subPtyName4 = _name + "w";
    switch (_type)
    {
    case WD::PDT_UCVec4:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
            //������������
            p = _pStringMgr ->addProperty(_name);
            //������������
            subPty1 = pTMgr->addProperty(QVariant::Char, subPtyName1);
            subPty2 = pTMgr->addProperty(QVariant::Char, subPtyName2);
            subPty3 = pTMgr->addProperty(QVariant::Char, subPtyName3);
            subPty4 = pTMgr->addProperty(QVariant::Char, subPtyName4);
            //��Ӳ㼶��ϵ
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
            p->addSubProperty(subPty3);
            p->addSubProperty(subPty4);
        }
        break;
    case WD::PDT_IVec4:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pManager);
            //������������
            p = _pStringMgr ->addProperty(_name);
            //������������
            subPty1 = pTMgr->addProperty(subPtyName1);
            subPty2 = pTMgr->addProperty(subPtyName2);
            subPty3 = pTMgr->addProperty(subPtyName3);
            subPty4 = pTMgr->addProperty(subPtyName4);
            //��Ӳ㼶��ϵ
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
            p->addSubProperty(subPty3);
            p->addSubProperty(subPty4);
        }
        break;
    case WD::PDT_UIVec4:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pManager);
            //������������
            p = _pStringMgr ->addProperty(_name);
            //������������
            subPty1 = pTMgr->addProperty(subPtyName1);
            subPty2 = pTMgr->addProperty(subPtyName2);
            subPty3 = pTMgr->addProperty(subPtyName3);
            subPty4 = pTMgr->addProperty(subPtyName4);
            //��Ӳ㼶��ϵ
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
            p->addSubProperty(subPty3);
            p->addSubProperty(subPty4);
        }
        break;
    case WD::PDT_LLVec4:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
            //������������
            p = _pStringMgr ->addProperty(_name);
            //������������
            subPty1 = pTMgr->addProperty(QVariant::LongLong,subPtyName1);
            subPty2 = pTMgr->addProperty(QVariant::LongLong,subPtyName2);
            subPty3 = pTMgr->addProperty(QVariant::LongLong,subPtyName3);
            subPty4 = pTMgr->addProperty(QVariant::LongLong,subPtyName4);
            //��Ӳ㼶��ϵ
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
            p->addSubProperty(subPty3);
            p->addSubProperty(subPty4);
        }
        break;
    case WD::PDT_ULLVec4:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
            //������������
            p = _pStringMgr ->addProperty(_name);
            //������������
            subPty1 = pTMgr->addProperty(QVariant::LongLong,subPtyName1);
            subPty2 = pTMgr->addProperty(QVariant::LongLong,subPtyName2);
            subPty3 = pTMgr->addProperty(QVariant::LongLong,subPtyName3);
            subPty4 = pTMgr->addProperty(QVariant::LongLong,subPtyName4);
            //��Ӳ㼶��ϵ
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
            p->addSubProperty(subPty3);
            p->addSubProperty(subPty4);
        }
        break;
    case WD::PDT_FVec4:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pManager);
            //������������
            p = _pStringMgr ->addProperty(_name);
            //������������
            subPty1 = pTMgr->addProperty(subPtyName1);
            subPty2 = pTMgr->addProperty(subPtyName2);
            subPty3 = pTMgr->addProperty(subPtyName3);
            subPty4 = pTMgr->addProperty(subPtyName4);
            //��Ӳ㼶��ϵ
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
            p->addSubProperty(subPty3);
            p->addSubProperty(subPty4);
        }
        break;
    case WD::PDT_DVec4:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pManager);
            //������������
            p = _pStringMgr ->addProperty(_name);
            //������������
            subPty1 = pTMgr->addProperty(subPtyName1);
            subPty2 = pTMgr->addProperty(subPtyName2);
            subPty3 = pTMgr->addProperty(subPtyName3);
            subPty4 = pTMgr->addProperty(subPtyName4);
            //��Ӳ㼶��ϵ
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
            p->addSubProperty(subPty3);
            p->addSubProperty(subPty4);
        }
        break;
    default:
        break;
    }
    //����������������붨������
    _pSubProperty = {subPty1,subPty2,subPty3,subPty4};
    return p;
}

bool ComplicateVec4Type::updateSubProperty(QString rootValue)
{
    QStringList subValueList = rootValue.split(" ");
    //��������
    QtProperty* sub1 = _pSubProperty.at(0);
    QtProperty* sub2 = _pSubProperty.at(1);
    QtProperty* sub3 = _pSubProperty.at(2);
    QtProperty* sub4 = _pSubProperty.at(3);
    //����������Ҫ���µ�ֵ
    QString subValue1 = subValueList.at(0);
    QString subValue2 = subValueList.at(1);
    QString subValue3 = subValueList.at(2);
    QString subValue4 = subValueList.at(3);

    switch (_type)
    {
    case WD::PDT_UCVec4:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
            unsigned char value1 = *subValue1.toLocal8Bit().data();
            unsigned char value2 = *subValue2.toLocal8Bit().data();
            unsigned char value3 = *subValue3.toLocal8Bit().data();
            unsigned char value4 = *subValue4.toLocal8Bit().data();
            //��������������ʾֵ
            pTMgr->setValue(sub1, value1);
            pTMgr->setValue(sub2, value2);
            pTMgr->setValue(sub3, value3);
            pTMgr->setValue(sub4, value4);
            return true;
        }
        break;
    case WD::PDT_IVec4:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pManager);
            //��������������ʾֵ
            pTMgr->setValue(sub1, subValue1.toInt());
            pTMgr->setValue(sub2, subValue2.toInt());
            pTMgr->setValue(sub3, subValue3.toInt());
            pTMgr->setValue(sub4, subValue4.toInt());
            return true;
        }
        break;
    case WD::PDT_UIVec4:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pManager);
            //��������������ʾֵ
            pTMgr->setValue(sub1, static_cast<unsigned int>(subValue1.toInt()));
            pTMgr->setValue(sub2, static_cast<unsigned int>(subValue2.toInt()));
            pTMgr->setValue(sub3, static_cast<unsigned int>(subValue3.toInt()));
            pTMgr->setValue(sub4, static_cast<unsigned int>(subValue4.toInt()));
            return true;
        }
        break;
    case WD::PDT_LLVec4:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
            //��������������ʾֵ
            pTMgr->setValue(sub1, subValue1.toLongLong());
            pTMgr->setValue(sub2, subValue1.toLongLong());
            pTMgr->setValue(sub3, subValue1.toLongLong());
            pTMgr->setValue(sub4, subValue1.toLongLong());
            return true;
        }
        break;
    case WD::PDT_ULLVec4:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
            //��������������ʾֵ
            pTMgr->setValue(sub1, subValue1.toLongLong());
            pTMgr->setValue(sub2, subValue2.toLongLong());
            pTMgr->setValue(sub3, subValue3.toLongLong());
            pTMgr->setValue(sub4, subValue4.toLongLong());
            return true;
        }
        break;
    case WD::PDT_FVec4:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pManager);
            pTMgr->setValue(sub1, static_cast<float>(subValue1.toDouble()));
            pTMgr->setValue(sub2, static_cast<float>(subValue2.toDouble()));
            pTMgr->setValue(sub3, static_cast<float>(subValue3.toDouble()));
            pTMgr->setValue(sub4, static_cast<float>(subValue4.toDouble()));
            return true;
        }
        break;
    case WD::PDT_DVec4:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pManager);
            pTMgr->setValue(sub1, subValue1.toDouble());
            pTMgr->setValue(sub2, subValue2.toDouble());
            pTMgr->setValue(sub3, subValue3.toDouble());
            pTMgr->setValue(sub4, subValue4.toDouble());
            return true;
        }
        break;
    default:
        return false;
        break;
    }
    return true;
}

bool ComplicateVec4Type::updateRootProperty()
{
    QString rootValue = _pStringMgr->value(_pRootProperty);
    QStringList rootValueList = rootValue.split(" ");
    //��������
    QtProperty* sub1 = _pSubProperty.at(0);
    QtProperty* sub2 = _pSubProperty.at(1);
    QtProperty* sub3 = _pSubProperty.at(2);
    QtProperty* sub4 = _pSubProperty.at(3);

    switch (_type)
    {
    case WD::PDT_UCVec4:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
            uchar subValue1 = pTMgr->value(sub1).toChar().toLatin1();
            uchar subValue2 = pTMgr->value(sub2).toChar().toLatin1();
            uchar subValue3 = pTMgr->value(sub3).toChar().toLatin1();
            uchar subValue4 = pTMgr->value(sub4).toChar().toLatin1();
            
            rootValue = QVariant(subValue1).toString() 
                + " " + QVariant(subValue2).toString() 
                + " " +  QVariant(subValue3).toString()
                + " " +  QVariant(subValue4).toString();
        }
        break;
    case WD::PDT_IVec4:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pManager);
            int subValue1 = pTMgr->value(sub1);
            int subValue2 = pTMgr->value(sub2);
            int subValue3 = pTMgr->value(sub3);
            int subValue4 = pTMgr->value(sub4);

            rootValue = QString::number(subValue1) 
                + " " + QString::number(subValue2) 
                + " " + QString::number(subValue4)
                + " " + QString::number(subValue3);
        }
        break;
    case WD::PDT_UIVec4:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pManager);
            unsigned int subValue1 = pTMgr->value(sub1);
            unsigned int subValue2 = pTMgr->value(sub2);
            unsigned int subValue3 = pTMgr->value(sub3);
            unsigned int subValue4 = pTMgr->value(sub4);

            rootValue = QString::number(subValue1) 
                + " " + QString::number(subValue2) 
                + " " + QString::number(subValue3)
                + " " + QString::number(subValue4);
        }
        break;
    case WD::PDT_LLVec4:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
            long long subValue1 = pTMgr->value(sub1).toLongLong();
            long long subValue2 = pTMgr->value(sub2).toLongLong();
            long long subValue3 = pTMgr->value(sub3).toLongLong();
            long long subValue4 = pTMgr->value(sub4).toLongLong();
            
            rootValue = QString::number(subValue1) 
                + " " + QString::number(subValue2) 
                + " " + QString::number(subValue3)
                + " " + QString::number(subValue4);
        }
        break;
    case WD::PDT_ULLVec4:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
            unsigned long long subValue1 = pTMgr->value(sub1).toLongLong();
            unsigned long long subValue2 = pTMgr->value(sub2).toLongLong();
            unsigned long long subValue3 = pTMgr->value(sub3).toLongLong();
            unsigned long long subValue4 = pTMgr->value(sub4).toLongLong();
            
            rootValue = QString::number(subValue1) 
                + " " + QString::number(subValue2) 
                + " " + QString::number(subValue3)
                + " " + QString::number(subValue4);
        }
        break;
    case WD::PDT_FVec4:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pManager);
            float subValue1 = pTMgr->value(sub1);
            float subValue2 = pTMgr->value(sub2);
            float subValue3 = pTMgr->value(sub3);
            float subValue4 = pTMgr->value(sub4);

            rootValue = QString::number(subValue1,'f',2) 
                + " " + QString::number(subValue2,'f',2) 
                + " " + QString::number(subValue3,'f',2)
                + " " + QString::number(subValue4,'f',2);
        }
        break;
    case WD::PDT_DVec4:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pManager);
            double subValue1 = pTMgr->value(sub1);
            double subValue2 = pTMgr->value(sub2);
            double subValue3 = pTMgr->value(sub3);
            double subValue4 = pTMgr->value(sub4);

            rootValue = QString::number(subValue1,'f',2) 
                + " " + QString::number(subValue2,'f',2) 
                + " " + QString::number(subValue3,'f',2);
                + " " + QString::number(subValue4,'f',2);
        }
        break;
    default:
        return false;
        break;
    }
    _pStringMgr->setValue(_pRootProperty,rootValue);
    return true;
}


//����Vector��VEC3����
ComplicateVec3Type::ComplicateVec3Type(ConfigPropertyWidget & ownWidget
    , QString & name
    , WD::WDPropertyDataType type
    , QtStringPropertyManager * pStringMgr
    , QtAbstractPropertyManager * pManager
    , std::string & valueStr)
    : ComplicateBaseType(ownWidget, name, type)
    , _pStringMgr(pStringMgr)
    , _pManager(pManager)
    , _valueStr(valueStr)
{
    _pSubProperty.fill(nullptr);
    _pRootProperty = this->initCreate();
    this->updateValue();
}

ComplicateVec3Type::~ComplicateVec3Type()
{
}

bool ComplicateVec3Type::contains(QtProperty * pPty) const
{
    if (pPty == _pRootProperty)
        return true;
    for (auto var : _pSubProperty)
    {
        if (pPty == var)
            return true;
    }
    return false;
}

bool ComplicateVec3Type::updateValue()
{
    if (_valueStr.empty())
        return false;
    if (_pRootProperty == nullptr)
        return false;
    if (_pManager == nullptr)
        return false;
    if (_pStringMgr == nullptr)
        return false;
    switch (_type)
    {
    case WD::PDT_UCVec3:
    {
        QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
        WD::UCVec3 value;
        value.fromString(_valueStr);
        //������Ҫ��ʼ����ֵ
        unsigned char subValue1 = value.x;
        unsigned char subValue2 = value.y;
        unsigned char subValue3 = value.z;

        //����ֵ
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        pTMgr->setValue(_pSubProperty.at(2),subValue3);

        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2)
            + " " + QString::number(subValue3);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
    break;
    case WD::PDT_IVec3:
    {
        QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pManager);
        WD::IVec3 value;
        value.fromString(_valueStr);
        //������Ҫ��ʼ����ֵ
        int subValue1 = value.x;
        int subValue2 = value.y;
        int subValue3 = value.z;
        //����ֵ
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        pTMgr->setValue(_pSubProperty.at(2),subValue3);
        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2)
            + " " + QString::number(subValue3);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
    break;
    case WD::PDT_UIVec3:
    {   
        QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pManager);
        WD::UIVec3 value;
        value.fromString(_valueStr);
        //������Ҫ��ʼ����ֵ
        unsigned int subValue1 = value.x;
        unsigned int subValue2 = value.y;
        unsigned int subValue3 = value.z;
        //����ֵ
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        pTMgr->setValue(_pSubProperty.at(2),subValue3);
        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2)
            + " " + QString::number(subValue3);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
    break;
    case WD::PDT_LLVec3:
    {   
        QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
        WD::LLVec3 value;
        value.fromString(_valueStr);
        //������Ҫ��ʼ����ֵ
        long long subValue1 = value.x;
        long long subValue2 = value.y;
        long long subValue3 = value.z;
        //����ֵ
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        pTMgr->setValue(_pSubProperty.at(2),subValue3);
        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2)
            + " " + QString::number(subValue3);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
    break;
    case WD::PDT_ULLVec3:
    {   
        QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
        WD::ULLVec3 value;
        value.fromString(_valueStr);
        //������Ҫ��ʼ����ֵ
        unsigned long long subValue1 = value.x;
        unsigned long long subValue2 = value.y;
        unsigned long long subValue3 = value.z;
        //����ֵ
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        pTMgr->setValue(_pSubProperty.at(2),subValue3);
        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2)
            + " " + QString::number(subValue3);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
    case WD::PDT_FVec3:
    {   
        QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pManager);
        WD::FVec3 value;
        value.fromString(_valueStr);
        //������Ҫ��ʼ����ֵ
        float subValue1 = value.x;
        float subValue2 = value.y;
        float subValue3 = value.z;
        //����ֵ
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        pTMgr->setValue(_pSubProperty.at(2),subValue3);
        QString rootValue = QString::number(subValue1, 'f', 2) 
            + " " + QString::number(subValue2, 'f', 2)
            + " " + QString::number(subValue3, 'f', 2);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
        break;
    case WD::PDT_DVec3:
    {   
        QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pManager);
        WD::DVec3 value;
        value.fromString(_valueStr);
        //������Ҫ��ʼ����ֵ
        double subValue1 = value.x;
        double subValue2 = value.y;
        double subValue3 = value.z;
        //����ֵ
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        pTMgr->setValue(_pSubProperty.at(2),subValue3);
        QString rootValue = QString::number(subValue1, 'f', 2) 
            + " " + QString::number(subValue2, 'f', 2)
            + " " + QString::number(subValue3, 'f', 2);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
        break;
    default:
        return false;
        break;
    }
    
    return true;
}

QtProperty * ComplicateVec3Type::getRootProperty()
{
    return _pRootProperty;
}

std::string ComplicateVec3Type::getValue()
{
    if (_pRootProperty != nullptr)
    {
        QString qStr = _pStringMgr->value(_pRootProperty);
        return qStr.toUtf8().data();
    }
    return std::string();
}

bool ComplicateVec3Type::updateShowP(QtProperty * pPty)

{
    if (pPty == nullptr)
        return false;
    
    if (_pStringMgr == nullptr || _pManager == nullptr)
        return false;

    if (_pRootProperty == nullptr
        || _pSubProperty.size() != 3)
        return false;


    //���ͱ�ʶ���������������͸�������
    int type = 0;

    if (pPty == _pRootProperty)
    {
        type = 1;
    }

    if(type == 0)
    {
        for (size_t i = 0; i < _pSubProperty.size(); ++i)
        {
            if (_pSubProperty.at(i) == pPty)
            {
                type = 2;
                break;
            }
        }
    }
    switch (type)
    {
    case 1:
        {
            QString rootValue = _pStringMgr->value(_pRootProperty);
            if (! isStringValid(rootValue,3))
                return false;
            //����������ʾֵ
            this->updateSubProperty(rootValue);
        }break;
    case 2:
        {
            //���¸�����ʾ��ֵ
            this->updateRootProperty();
            
        }break;
    default:
        return false;
        break;
    }
    return true;
}

QtProperty * ComplicateVec3Type::initCreate()
{
    if (_pStringMgr == nullptr)
        return nullptr;
    if (_pManager == nullptr)
        return nullptr;
    //����������
    QtProperty* p = nullptr;
    QtProperty* subPty1 = nullptr;
    QtProperty* subPty2 = nullptr;
    QtProperty* subPty3 = nullptr;

    //��������
    QString subPtyName1 = _name + "X";
    QString subPtyName2 = _name + "Y";
    QString subPtyName3 = _name + "Z";
    switch (_type)
    {
    case WD::PDT_UCVec3:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
            //������������
            p = _pStringMgr ->addProperty(_name);
            //������������
            subPty1 = pTMgr->addProperty(QVariant::Char, subPtyName1);
            subPty2 = pTMgr->addProperty(QVariant::Char, subPtyName2);
            subPty3 = pTMgr->addProperty(QVariant::Char, subPtyName3);
            //��Ӳ㼶��ϵ
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
            p->addSubProperty(subPty3);
        }
        break;
    case WD::PDT_IVec3:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pManager);
            //������������
            p = _pStringMgr ->addProperty(_name);
            //������������
            subPty1 = pTMgr->addProperty(subPtyName1);
            subPty2 = pTMgr->addProperty(subPtyName2);
            subPty3 = pTMgr->addProperty(subPtyName3);
            //��Ӳ㼶��ϵ
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
            p->addSubProperty(subPty3);
        }
        break;
    case WD::PDT_UIVec3:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pManager);
            //������������
            p = _pStringMgr ->addProperty(_name);
            //������������
            subPty1 = pTMgr->addProperty(subPtyName1);
            subPty2 = pTMgr->addProperty(subPtyName2);
            subPty3 = pTMgr->addProperty(subPtyName3);
            //��Ӳ㼶��ϵ
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
            p->addSubProperty(subPty3);
        }
        break;
    case WD::PDT_LLVec3:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
            //������������
            p = _pStringMgr ->addProperty(_name);
            //������������
            subPty1 = pTMgr->addProperty(QVariant::LongLong,subPtyName1);
            subPty2 = pTMgr->addProperty(QVariant::LongLong,subPtyName2);
            subPty3 = pTMgr->addProperty(QVariant::LongLong,subPtyName3);
            //��Ӳ㼶��ϵ
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
            p->addSubProperty(subPty3);
        }
        break;
    case WD::PDT_ULLVec3:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
            //������������
            p = _pStringMgr ->addProperty(_name);
            //������������
            subPty1 = pTMgr->addProperty(QVariant::LongLong,subPtyName1);
            subPty2 = pTMgr->addProperty(QVariant::LongLong,subPtyName2);
            subPty3 = pTMgr->addProperty(QVariant::LongLong,subPtyName3);
            //��Ӳ㼶��ϵ
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
            p->addSubProperty(subPty3);
        }
        break;
    case WD::PDT_FVec3:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pManager);
            //������������
            p = _pStringMgr ->addProperty(_name);
            //������������
            subPty1 = pTMgr->addProperty(subPtyName1);
            subPty2 = pTMgr->addProperty(subPtyName2);
            subPty3 = pTMgr->addProperty(subPtyName3);
            //��Ӳ㼶��ϵ
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
            p->addSubProperty(subPty3);
        }
        break;
    case WD::PDT_DVec3:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pManager);
            //������������
            p = _pStringMgr ->addProperty(_name);
            //������������
            subPty1 = pTMgr->addProperty(subPtyName1);
            subPty2 = pTMgr->addProperty(subPtyName2);
            subPty3 = pTMgr->addProperty(subPtyName3);
            //��Ӳ㼶��ϵ
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
            p->addSubProperty(subPty3);
        }
        break;
    default:
        break;
    }
    //����������������붨������
    _pSubProperty = {subPty1,subPty2,subPty3};
    return p;
}

bool ComplicateVec3Type::updateSubProperty(QString rootValue)
{
    QStringList subValueList = rootValue.split(" ");
    //��������
    QtProperty* sub1 = _pSubProperty.at(0);
    QtProperty* sub2 = _pSubProperty.at(1);
    QtProperty* sub3 = _pSubProperty.at(2);
    //����������Ҫ���µ�ֵ
    QString subValue1 = subValueList.at(0);
    QString subValue2 = subValueList.at(1);
    QString subValue3 = subValueList.at(2);

    switch (_type)
    {
    case WD::PDT_UCVec3:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
            unsigned char value1 = *subValue1.toLocal8Bit().data();
            unsigned char value2 = *subValue2.toLocal8Bit().data();
            unsigned char value3 = *subValue3.toLocal8Bit().data();
            //��������������ʾֵ
            pTMgr->setValue(sub1, value1);
            pTMgr->setValue(sub2, value2);
            pTMgr->setValue(sub3, value3);
            return true;
        }
        break;
    case WD::PDT_IVec3:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pManager);
            //��������������ʾֵ
            pTMgr->setValue(sub1, subValue1.toInt());
            pTMgr->setValue(sub2, subValue2.toInt());
            pTMgr->setValue(sub3, subValue3.toInt());
            return true;
        }
        break;
    case WD::PDT_UIVec3:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pManager);
            //��������������ʾֵ
            pTMgr->setValue(sub1, static_cast<unsigned int>(subValue1.toInt()));
            pTMgr->setValue(sub2, static_cast<unsigned int>(subValue2.toInt()));
            pTMgr->setValue(sub3, static_cast<unsigned int>(subValue3.toInt()));
            return true;
        }
        break;
    case WD::PDT_LLVec3:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
            //��������������ʾֵ
            pTMgr->setValue(sub1, subValue1.toLongLong());
            pTMgr->setValue(sub2, subValue1.toLongLong());
            pTMgr->setValue(sub3, subValue1.toLongLong());
            return true;
        }
        break;
    case WD::PDT_ULLVec3:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
            //��������������ʾֵ
            pTMgr->setValue(sub1, subValue1.toLongLong());
            pTMgr->setValue(sub2, subValue2.toLongLong());
            pTMgr->setValue(sub3, subValue3.toLongLong());
            return true;
        }
        break;
    case WD::PDT_FVec3:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pManager);
            pTMgr->setValue(sub1, static_cast<float>(subValue1.toDouble()));
            pTMgr->setValue(sub2, static_cast<float>(subValue2.toDouble()));
            pTMgr->setValue(sub3, static_cast<float>(subValue3.toDouble()));
            return true;
        }
        break;
    case WD::PDT_DVec3:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pManager);
            pTMgr->setValue(sub1, subValue1.toDouble());
            pTMgr->setValue(sub2, subValue2.toDouble());
            pTMgr->setValue(sub3, subValue3.toDouble());
            return true;
        }
        break;
    default:
        return false;
        break;
    }
    return true;
}

bool ComplicateVec3Type::updateRootProperty()
{
    QString rootValue = _pStringMgr->value(_pRootProperty);
    QStringList rootValueList = rootValue.split(" ");
    //��������
    QtProperty* sub1 = _pSubProperty.at(0);
    QtProperty* sub2 = _pSubProperty.at(1);
    QtProperty* sub3 = _pSubProperty.at(2);

    switch (_type)
    {
    case WD::PDT_UCVec3:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
            uchar subValue1 = pTMgr->value(sub1).toChar().toLatin1();
            uchar subValue2 = pTMgr->value(sub2).toChar().toLatin1();
            uchar subValue3 = pTMgr->value(sub3).toChar().toLatin1();
            
            rootValue = QVariant(subValue1).toString() 
                + " " + QVariant(subValue2).toString() 
                + " " +  QVariant(subValue3).toString();
        }
        break;
    case WD::PDT_IVec3:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pManager);
            int subValue1 = pTMgr->value(sub1);
            int subValue2 = pTMgr->value(sub2);
            int subValue3 = pTMgr->value(sub3);

            rootValue = QString::number(subValue1) 
                + " " + QString::number(subValue2) 
                + " " + QString::number(subValue3);
        }
        break;
    case WD::PDT_UIVec3:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pManager);
            unsigned int subValue1 = pTMgr->value(sub1);
            unsigned int subValue2 = pTMgr->value(sub2);
            unsigned int subValue3 = pTMgr->value(sub3);

            rootValue = QString::number(subValue1) 
                + " " + QString::number(subValue2) 
                + " " + QString::number(subValue3);
        }
        break;
    case WD::PDT_LLVec3:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
            long long subValue1 = pTMgr->value(sub1).toLongLong();
            long long subValue2 = pTMgr->value(sub2).toLongLong();
            long long subValue3 = pTMgr->value(sub3).toLongLong();
            
            rootValue = QString::number(subValue1) 
                + " " + QString::number(subValue2) 
                + " " + QString::number(subValue3);
        }
        break;
    case WD::PDT_ULLVec3:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
            unsigned long long subValue1 = pTMgr->value(sub1).toLongLong();
            unsigned long long subValue2 = pTMgr->value(sub2).toLongLong();
            unsigned long long subValue3 = pTMgr->value(sub3).toLongLong();
            
            rootValue = QString::number(subValue1) 
                + " " + QString::number(subValue2) 
                + " " + QString::number(subValue3);
        }
        break;
    case WD::PDT_FVec3:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pManager);
            float subValue1 = pTMgr->value(sub1);
            float subValue2 = pTMgr->value(sub2);
            float subValue3 = pTMgr->value(sub3);

            rootValue = QString::number(subValue1,'f',2) 
                + " " + QString::number(subValue2,'f',2) 
                + " " + QString::number(subValue3,'f',2);
        }
        break;
    case WD::PDT_DVec3:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pManager);
            double subValue1 = pTMgr->value(sub1);
            double subValue2 = pTMgr->value(sub2);
            double subValue3 = pTMgr->value(sub3);

            rootValue = QString::number(subValue1,'f',2) 
                + " " + QString::number(subValue2,'f',2) 
                + " " + QString::number(subValue3,'f',2);
        }
        break;
    default:
        return false;
        break;
    }
    _pStringMgr->setValue(_pRootProperty,rootValue);
    return true;
}


//����Vector��VEC2����
ComplicateVec2Type::ComplicateVec2Type(ConfigPropertyWidget & ownWidget
    , QString & name
    , WD::WDPropertyDataType type
    , QtStringPropertyManager * pStringMgr
    , QtAbstractPropertyManager * pManager
    , std::string & valueStr)
    : ComplicateBaseType(ownWidget, name, type)
    , _pStringMgr(pStringMgr)
    , _pManager(pManager)
    , _valueStr(valueStr)
{
    _pSubProperty.fill(nullptr);
    _pRootProperty = this->initCreate();
    this->updateValue();
}

ComplicateVec2Type::~ComplicateVec2Type()
{
}

bool ComplicateVec2Type::contains(QtProperty * pPty) const
{
    if (pPty == _pRootProperty)
        return true;
    for (auto var : _pSubProperty)
    {
        if (pPty == var)
            return true;
    }
    return false;
}

bool ComplicateVec2Type::updateValue()
{
    if (_valueStr.empty())
        return false;
    if (_pRootProperty == nullptr)
        return false;
    if (_pManager == nullptr)
        return false;
    if (_pStringMgr == nullptr)
        return false;
    switch (_type)
    {
    case WD::PDT_UCVec2:
    {
        QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
        WD::UCVec2 value;
        value.fromString(_valueStr);
        //������Ҫ��ʼ����ֵ
        unsigned char subValue1 = value.x;
        unsigned char subValue2 = value.y;

        //����ֵ
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);

        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
    break;
    case WD::PDT_IVec2:
    {
        QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pManager);
        WD::IVec2 value;
        value.fromString(_valueStr);
        //������Ҫ��ʼ����ֵ
        int subValue1 = value.x;
        int subValue2 = value.y;
        //����ֵ
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);

        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
    break;
    case WD::PDT_UIVec2:
    {   
        QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pManager);
        WD::UIVec2 value;
        value.fromString(_valueStr);
        //������Ҫ��ʼ����ֵ
        unsigned int subValue1 = value.x;
        unsigned int subValue2 = value.y;
        //����ֵ
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
    break;
    case WD::PDT_LLVec2:
    {   
        QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
        WD::LLVec2 value;
        value.fromString(_valueStr);
        //������Ҫ��ʼ����ֵ
        long long subValue1 = value.x;
        long long subValue2 = value.y;
        //����ֵ
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
    break;
    case WD::PDT_ULLVec2:
    {   
        QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
        WD::ULLVec3 value;
        value.fromString(_valueStr);
        //������Ҫ��ʼ����ֵ
        unsigned long long subValue1 = value.x;
        unsigned long long subValue2 = value.y;
        //����ֵ
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);

        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
    case WD::PDT_FVec2:
    {   
        QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pManager);
        WD::FVec2 value;
        value.fromString(_valueStr);
        //������Ҫ��ʼ����ֵ
        float subValue1 = value.x;
        float subValue2 = value.y;
        //����ֵ
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        QString rootValue = QString::number(subValue1, 'f', 2) 
            + " " + QString::number(subValue2, 'f', 2);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
        break;
    case WD::PDT_DVec2:
    {   
        QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pManager);
        WD::DVec2 value;
        value.fromString(_valueStr);
        //������Ҫ��ʼ����ֵ
        double subValue1 = value.x;
        double subValue2 = value.y;
        //����ֵ
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        QString rootValue = QString::number(subValue1, 'f', 2) 
            + " " + QString::number(subValue2, 'f', 2);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
        break;
    default:
        return false;
        break;
    }
    return true;
}

QtProperty * ComplicateVec2Type::getRootProperty()
{
    return _pRootProperty;
}

std::string ComplicateVec2Type::getValue()
{
    if (_pRootProperty != nullptr)
    {
        QString qStr = _pStringMgr->value(_pRootProperty);
        return qStr.toUtf8().data();
    }
    return std::string();
}

bool ComplicateVec2Type::updateShowP(QtProperty * pPty)

{
    if (pPty == nullptr)
        return false;
    
    if (_pStringMgr == nullptr || _pManager == nullptr)
        return false;

    if (_pRootProperty == nullptr
        || _pSubProperty.size() != 2)
        return false;


    //���ͱ�ʶ���������������͸�������
    int type = 0;

    if (pPty == _pRootProperty)
    {
        type = 1;
    }

    if(type == 0)
    {
        for (size_t i = 0; i < _pSubProperty.size(); ++i)
        {
            if (_pSubProperty.at(i) == pPty)
            {
                type = 2;
                break;
            }
        }
    }
    switch (type)
    {
    case 1:
        {
            QString rootValue = _pStringMgr->value(_pRootProperty);
            if (! isStringValid(rootValue,2))
                return false;
            //����������ʾֵ
            this->updateSubProperty(rootValue);
        }break;
    case 2:
        {
            //���¸�����ʾ��ֵ
            this->updateRootProperty();
            
        }break;
    default:
        return false;
        break;
    }
    return true;
}

QtProperty * ComplicateVec2Type::initCreate()
{
    if (_pStringMgr == nullptr)
        return nullptr;
    if (_pManager == nullptr)
        return nullptr;
    //����������
    QtProperty* p = nullptr;
    QtProperty* subPty1 = nullptr;
    QtProperty* subPty2 = nullptr;

    //��������
    QString subPtyName1 = _name + "X";
    QString subPtyName2 = _name + "Y";
    switch (_type)
    {
    case WD::PDT_UCVec2:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
            //������������
            p = _pStringMgr ->addProperty(_name);
            //������������
            subPty1 = pTMgr->addProperty(QVariant::Char, subPtyName1);
            subPty2 = pTMgr->addProperty(QVariant::Char, subPtyName2);
            //��Ӳ㼶��ϵ
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
        }
        break;
    case WD::PDT_IVec2:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pManager);
            //������������
            p = _pStringMgr ->addProperty(_name);
            //������������
            subPty1 = pTMgr->addProperty(subPtyName1);
            subPty2 = pTMgr->addProperty(subPtyName2);
            //��Ӳ㼶��ϵ
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
        }
        break;
    case WD::PDT_UIVec2:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pManager);
            //������������
            p = _pStringMgr ->addProperty(_name);
            //������������
            subPty1 = pTMgr->addProperty(subPtyName1);
            subPty2 = pTMgr->addProperty(subPtyName2);
            //��Ӳ㼶��ϵ
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
        }
        break;
    case WD::PDT_LLVec2:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
            //������������
            p = _pStringMgr ->addProperty(_name);
            //������������
            subPty1 = pTMgr->addProperty(QVariant::LongLong,subPtyName1);
            subPty2 = pTMgr->addProperty(QVariant::LongLong,subPtyName2);
            //��Ӳ㼶��ϵ
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
        }
        break;
    case WD::PDT_ULLVec2:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
            //������������
            p = _pStringMgr ->addProperty(_name);
            //������������
            subPty1 = pTMgr->addProperty(QVariant::LongLong,subPtyName1);
            subPty2 = pTMgr->addProperty(QVariant::LongLong,subPtyName2);
            //��Ӳ㼶��ϵ
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
        }
        break;
    case WD::PDT_FVec2:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pManager);
            //������������
            p = _pStringMgr ->addProperty(_name);
            //������������
            subPty1 = pTMgr->addProperty(subPtyName1);
            subPty2 = pTMgr->addProperty(subPtyName2);
            //��Ӳ㼶��ϵ
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
        }
        break;
    case WD::PDT_DVec2:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pManager);
            //������������
            p = _pStringMgr ->addProperty(_name);
            //������������
            subPty1 = pTMgr->addProperty(subPtyName1);
            subPty2 = pTMgr->addProperty(subPtyName2);
            //��Ӳ㼶��ϵ
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
        }
        break;
    default:
        break;
    }
    //����������������붨������
    _pSubProperty = {subPty1,subPty2};
    return p;
}

bool ComplicateVec2Type::updateSubProperty(QString rootValue)
{
    QStringList subValueList = rootValue.split(" ");
    //��������
    QtProperty* sub1 = _pSubProperty.at(0);
    QtProperty* sub2 = _pSubProperty.at(1);
    //����������Ҫ���µ�ֵ
    QString subValue1 = subValueList.at(0);
    QString subValue2 = subValueList.at(1);

    switch (_type)
    {
    case WD::PDT_UCVec2:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
            unsigned char value1 = *subValue1.toLocal8Bit().data();
            unsigned char value2 = *subValue2.toLocal8Bit().data();
            //��������������ʾֵ
            pTMgr->setValue(sub1, value1);
            pTMgr->setValue(sub2, value2);
            return true;
        }
        break;
    case WD::PDT_IVec2:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pManager);
            //��������������ʾֵ
            pTMgr->setValue(sub1, subValue1.toInt());
            pTMgr->setValue(sub2, subValue2.toInt());
            return true;
        }
        break;
    case WD::PDT_UIVec2:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pManager);
            //��������������ʾֵ
            pTMgr->setValue(sub1, static_cast<unsigned int>(subValue1.toInt()));
            pTMgr->setValue(sub2, static_cast<unsigned int>(subValue2.toInt()));
            return true;
        }
        break;
    case WD::PDT_LLVec2:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
            //��������������ʾֵ
            pTMgr->setValue(sub1, subValue1.toLongLong());
            pTMgr->setValue(sub2, subValue1.toLongLong());
            return true;
        }
        break;
    case WD::PDT_ULLVec2:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
            //��������������ʾֵ
            pTMgr->setValue(sub1, subValue1.toLongLong());
            pTMgr->setValue(sub2, subValue2.toLongLong());
            return true;
        }
        break;
    case WD::PDT_FVec2:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pManager);
            pTMgr->setValue(sub1, static_cast<float>(subValue1.toDouble()));
            pTMgr->setValue(sub2, static_cast<float>(subValue2.toDouble()));
            return true;
        }
        break;
    case WD::PDT_DVec2:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pManager);
            pTMgr->setValue(sub1, subValue1.toDouble());
            pTMgr->setValue(sub2, subValue2.toDouble());
            return true;
        }
        break;
    default:
        return false;
        break;
    }
    return true;
}

bool ComplicateVec2Type::updateRootProperty()
{
    QString rootValue = _pStringMgr->value(_pRootProperty);
    QStringList rootValueList = rootValue.split(" ");
    //��������
    QtProperty* sub1 = _pSubProperty.at(0);
    QtProperty* sub2 = _pSubProperty.at(1);

    switch (_type)
    {
    case WD::PDT_UCVec2:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
            uchar subValue1 = pTMgr->value(sub1).toChar().toLatin1();
            uchar subValue2 = pTMgr->value(sub2).toChar().toLatin1();
            
            rootValue = QVariant(subValue1).toString() 
                + " " + QVariant(subValue2).toString();
        }
        break;
    case WD::PDT_IVec2:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pManager);
            int subValue1 = pTMgr->value(sub1);
            int subValue2 = pTMgr->value(sub2);

            rootValue = QString::number(subValue1) 
                + " " + QString::number(subValue2);
        }
        break;
    case WD::PDT_UIVec2:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pManager);
            unsigned int subValue1 = pTMgr->value(sub1);
            unsigned int subValue2 = pTMgr->value(sub2);

            rootValue = QString::number(subValue1) 
                + " " + QString::number(subValue2);
        }
        break;
    case WD::PDT_LLVec2:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
            long long subValue1 = pTMgr->value(sub1).toLongLong();
            long long subValue2 = pTMgr->value(sub2).toLongLong();
            
            rootValue = QString::number(subValue1) 
                + " " + QString::number(subValue2);
        }
        break;
    case WD::PDT_ULLVec2:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pManager);
            unsigned long long subValue1 = pTMgr->value(sub1).toLongLong();
            unsigned long long subValue2 = pTMgr->value(sub2).toLongLong();
            
            rootValue = QString::number(subValue1) 
                + " " + QString::number(subValue2);
        }
        break;
    case WD::PDT_FVec2:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pManager);
            float subValue1 = pTMgr->value(sub1);
            float subValue2 = pTMgr->value(sub2);

            rootValue = QString::number(subValue1,'f',2) 
                + " " + QString::number(subValue2,'f',2);
        }
        break;
    case WD::PDT_DVec2:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pManager);
            double subValue1 = pTMgr->value(sub1);
            double subValue2 = pTMgr->value(sub2);

            rootValue = QString::number(subValue1,'f',2) 
                + " " + QString::number(subValue2,'f',2);
        }
        break;
    default:
        return false;
        break;
    }
    _pStringMgr->setValue(_pRootProperty,rootValue);
    return true;
}


//����Vector��Quat����
ComplicateQuatType::ComplicateQuatType(ConfigPropertyWidget & ownWidget
    , QString & name
    , WD::WDPropertyDataType type
    , QtStringPropertyManager * pStringMgr
    , QtDoublePropertyManager * pDoubleMgr
    , std::string & valueStr)
    : ComplicateBaseType(ownWidget, name, type)
    , _pStringMgr(pStringMgr)
    , _pDoubleMgr(pDoubleMgr)
    , _valueStr(valueStr)
{
    _pSubProperty.fill(nullptr);
    _pSubSubProperty.fill(nullptr);
    _pRootProperty = this->initCreate();
    _subValues.fill(QString());
    _subSubValues.fill(double(0));
    this->updateValue();
}

ComplicateQuatType::~ComplicateQuatType()
{
}

bool ComplicateQuatType::contains(QtProperty * pPty) const
{
    if (pPty == nullptr)
        return false;
    if (_pRootProperty == pPty)
        return true;
    for (size_t i = 0; i < _pSubProperty.size(); ++i)
    {
        if (_pSubProperty.at(i) == pPty)
            return true;
    }
    for (size_t i = 0; i < _pSubSubProperty.size(); ++i)
    {
        if (_pSubSubProperty.at(i) == pPty)
            return true;
    }
    return false;
}

bool ComplicateQuatType::updateValue()
{
    if (_pRootProperty == nullptr)
        return false;
    if (_pDoubleMgr == nullptr)
        return false;
    if (_pStringMgr == nullptr)
        return false;
    //��Ԫ����תת������
    RotateData rotationData = RotateData();
    switch (_type)
    {
    case WD::PDT_FQuat:
        {
            WD::FQuat value;
            value.fromString(_valueStr);
            //תΪDQUAT
            WD::TQuat<double> tDQuat = {value.x, value.y, value.z, value.w};
            rotationData.setRottion(tDQuat,rotationData);
            std::string tStr = rotationData.toString();
            QString rootValue = QString::fromUtf8(tStr.data());
            //�������������ó�ֵ
            _rootValue = rootValue;
        }
        break;
    case WD::PDT_DQuat:
        {
            WD::DQuat value;
            value.fromString(_valueStr);
            rotationData.setRottion(value,rotationData);
            std::string tStr = rotationData.toString();
            QString rootValue = QString::fromUtf8(tStr.data());
            //�������������ó�ֵ
            _rootValue = rootValue;
        }
        break;
    default:
        break;
    }
    //��������������ֵ
    double upX = rotationData.nx;
    double upY = rotationData.ny;
    double upZ = rotationData.nz;

    double rightX = rotationData.dx;
    double rightY = rotationData.dy;
    double rightZ = rotationData.dz;

    QString strUpX = QString::number(upX, 'f', 3);
    QString strUpY = QString::number(upY, 'f', 3);
    QString strUpZ = QString::number(upZ, 'f', 3);

    QString strRightX = QString::number(rightX, 'f', 3);
    QString strRightY = QString::number(rightY, 'f', 3);
    QString strRightZ = QString::number(rightZ, 'f', 3);
    //ƴ��Up������ֵ
    QString tUpValue = strUpX + " " + strUpY + " " + strUpZ;
    //ƴ��Right������ֵ
    QString tRightValue = strRightX+ " " + strRightY + " " + strRightZ;
    //����ֵ
    _pStringMgr->setValue(_pRootProperty, _rootValue);

    _pStringMgr->setValue(_pSubProperty.at(0), tUpValue);
    _pStringMgr->setValue(_pSubProperty.at(1), tRightValue);

    _pDoubleMgr->setValue(_pSubSubProperty.at(0),upX);
    _pDoubleMgr->setValue(_pSubSubProperty.at(1),upY);
    _pDoubleMgr->setValue(_pSubSubProperty.at(2),upZ);
    _pDoubleMgr->setValue(_pSubSubProperty.at(3),rightX);
    _pDoubleMgr->setValue(_pSubSubProperty.at(4),rightY);
    _pDoubleMgr->setValue(_pSubSubProperty.at(5),rightZ);
    //����ֵ
    _subValues    = {tUpValue, tRightValue};
    _subSubValues = {upX, upY, upZ, rightX, rightY, rightZ};

    return true;
}

QtProperty * ComplicateQuatType::getRootProperty()
{
    return _pRootProperty;
}

std::string ComplicateQuatType::getValue() 
{
    if (_rootValue.isEmpty())
        return "";
    //��������ֵת��ΪDQuat����
    RotateData rotation;
    rotation.fromQString(_rootValue);
    WD::DQuat value = rotation.rotation(rotation);

    std::string valueStr =  value.toString();
    return valueStr; 
}

bool ComplicateQuatType::updateShowP(QtProperty * pPty)
{
    if (pPty == nullptr)
        return false;
    
    if (_pStringMgr == nullptr || _pDoubleMgr == nullptr)
        return false;

    if (_pRootProperty == nullptr
        || _pSubProperty.size() != 2
        || _pSubSubProperty.size() != 6)
        return false;
    //�ж����������ͱ�ʶ
    int type = 0;

    if (pPty == _pRootProperty)
    {
        type = 1;
    }

    if(type == 0)
    {
        for (size_t i = 0; i < _pSubProperty.size(); ++i)
        {
            if (_pSubProperty.at(i) == pPty)
            {
                type = 2;
                break;
            }
        }
    }

    if (type == 0)
    {
        for (size_t i = 0; i < _pSubSubProperty.size(); ++i)
        {
            if (_pSubSubProperty.at(i) == pPty)
            {
                type = 3;
                break;
            }
        }
    }
    WD::DVec3 up;
    WD::DVec3 right;
    switch (type)
    {
    case 1:
        {
            //�޸ĵ��Ǹ�������
            QString rootValue = _pStringMgr->value(_pRootProperty);
            if (!isStringValid(rootValue,6))
                return false;
            QStringList values = rootValue.split(" ");

            up    = WD::DVec3(values[0].toDouble(), values[1].toDouble(),  values[2].toDouble());
            right = WD::DVec3(values[3].toDouble(), values[4].toDouble(),  values[5].toDouble());
        }break;
    case 2:
        {
            //�޸ĵ���UP �� Right
            QString upValue     = _pStringMgr->value(_pSubProperty[0]);
            QString rightValue  = _pStringMgr->value(_pSubProperty[1]);
            if ((!isStringValid(upValue,3)) || (!isStringValid(rightValue,3)))
                return false;

            QStringList values0 = upValue.split(" ");
            QStringList values1 = rightValue.split(" ");
            
            up    = WD::DVec3 (values0[0].toDouble(), values0[1].toDouble(),  values0[2].toDouble());
            right = WD::DVec3 (values1[0].toDouble(), values1[1].toDouble(),  values1[2].toDouble());
        }break;
    case 3:
        {
            //�޸ĵ�����������
            up = WD::DVec3 (_pDoubleMgr->value(_pSubSubProperty[0])
                , _pDoubleMgr->value(_pSubSubProperty[1])
                , _pDoubleMgr->value(_pSubSubProperty[2]));
            right = WD::DVec3 (_pDoubleMgr->value(_pSubSubProperty[3])
                , _pDoubleMgr->value(_pSubSubProperty[4])
                , _pDoubleMgr->value(_pSubSubProperty[5]));
        }break;
    default:
        return false;
        break;
    }
    if (up.lengthSq() <= WD::NumLimits<float>::Epsilon 
       || right.lengthSq() <= WD::NumLimits<float>::Epsilon)
    {
        //���ش���ֵ
       _pStringMgr->setValue(_pRootProperty, _rootValue);

       QString subValue0 = _subValues.at(0);
       _pStringMgr->setValue(_pSubProperty[0], subValue0);
       QString subValue1 = _subValues.at(1);
       _pStringMgr->setValue(_pSubProperty[1], subValue1);

       _pDoubleMgr->setValue(_pSubSubProperty[0], _subSubValues.at(0));
       _pDoubleMgr->setValue(_pSubSubProperty[1], _subSubValues.at(1));
       _pDoubleMgr->setValue(_pSubSubProperty[2], _subSubValues.at(2));
       
       _pDoubleMgr->setValue(_pSubSubProperty[3], _subSubValues.at(3));
       _pDoubleMgr->setValue(_pSubSubProperty[4], _subSubValues.at(4));
       _pDoubleMgr->setValue(_pSubSubProperty[5], _subSubValues.at(5));
       return false;
    }

    //ͬ��������ʾֵ
    {
        if (type != 1)
        {
            QString rootValue = QString("%1 %2 %3 %4 %5 %6")
                .arg(QString::number(up.x,'f',3))
                .arg(QString::number(up.y,'f',3))
                .arg(QString::number(up.z,'f',3))
                .arg(QString::number(right.x,'f',3))
                .arg(QString::number(right.y,'f',3))
                .arg(QString::number(right.z,'f',3));
            _pStringMgr->setValue(_pRootProperty, rootValue);
        }
        if (type != 2)
        {
            QString subValue0 = QString("%1 %2 %3")
                .arg(QString::number(up.x,'f',3))
                .arg(QString::number(up.y,'f',3))
                .arg(QString::number(up.z,'f',3));
            _pStringMgr->setValue(_pSubProperty[0], subValue0);
            QString subValue1 = QString("%1 %2 %3")
                .arg(QString::number(right.x,'f',3))
                .arg(QString::number(right.y,'f',3))
                .arg(QString::number(right.z,'f',3));
            _pStringMgr->setValue(_pSubProperty[1], subValue1);
        }
        

        _pDoubleMgr->setValue(_pSubSubProperty[0], up.x);
        _pDoubleMgr->setValue(_pSubSubProperty[1], up.y);
        _pDoubleMgr->setValue(_pSubSubProperty[2], up.z);
        
        _pDoubleMgr->setValue(_pSubSubProperty[3], right.x);
        _pDoubleMgr->setValue(_pSubSubProperty[4], right.y);
        _pDoubleMgr->setValue(_pSubSubProperty[5], right.z);
    }
    //���������б����ֵ
    QString upQStr      =   QString::fromUtf8(up.toString().data());
    QString rightQStr   =   QString::fromUtf8(right.toString().data());

    _rootValue = upQStr + " " + rightQStr;

    _subValues.at(0) = upQStr;
    _subValues.at(1) = rightQStr;

    _subSubValues.at(0) = up.x;
    _subSubValues.at(1) = up.y;
    _subSubValues.at(2) = up.z;

    _subSubValues.at(3) = right.x;
    _subSubValues.at(4) = right.y;
    _subSubValues.at(5) = right.z;

    return true;
}

QtProperty * ComplicateQuatType::initCreate()
{
    if (_pStringMgr == nullptr)
        return nullptr;
    if (_pDoubleMgr == nullptr)
        return nullptr;
    //������������
    QtProperty* pRootPty    =   nullptr;
    //Up������
    QtProperty* pUpPty      =   nullptr;
    QtProperty* pUpXPty     =   nullptr;
    QtProperty* pUpYPty     =   nullptr;
    QtProperty* pUpZPty     =   nullptr;
    //Right������
    QtProperty* pRightPty      =   nullptr;
    QtProperty* pRightXPty     =   nullptr;
    QtProperty* pRightYPty     =   nullptr;
    QtProperty* pRightZPty     =   nullptr;

    //��������ʾ����

    QString upName  = _name + "Up";
    QString upXName = upName + "X";
    QString upYName = upName + "Y";
    QString upZName = upName + "Z";

    QString rightName = _name + "Right";
    QString rightXName = rightName + "X";
    QString rightYName = rightName + "Y";
    QString rightZName = rightName + "Z";
    //������������
    pRootPty    =   _pStringMgr->addProperty(_name);
    //����UP��Right
    pUpPty      =   _pStringMgr->addProperty(upName);
    pRightPty   =   _pStringMgr->addProperty(rightName);
    //����UP��Right�µ�������������
    pUpXPty     =   _pDoubleMgr->addProperty(upXName);
    pUpYPty     =   _pDoubleMgr->addProperty(upYName);
    pUpZPty     =   _pDoubleMgr->addProperty(upZName);
    //���ò���
    _pDoubleMgr->setSingleStep(pUpXPty,double(0.1));
    _pDoubleMgr->setSingleStep(pUpYPty,double(0.1));
    _pDoubleMgr->setSingleStep(pUpZPty,double(0.1));

    pRightXPty     =   _pDoubleMgr->addProperty(rightXName);
    pRightYPty     =   _pDoubleMgr->addProperty(rightYName);
    pRightZPty     =   _pDoubleMgr->addProperty(rightZName);

    _pDoubleMgr->setSingleStep(pRightXPty,double(0.1));
    _pDoubleMgr->setSingleStep(pRightYPty,double(0.1));
    _pDoubleMgr->setSingleStep(pRightZPty,double(0.1));

    //��Ӹ��ӹ�ϵ
    pUpPty->addSubProperty(pUpXPty);
    pUpPty->addSubProperty(pUpYPty);
    pUpPty->addSubProperty(pUpZPty);

    pRightPty->addSubProperty(pRightXPty);
    pRightPty->addSubProperty(pRightYPty);
    pRightPty->addSubProperty(pRightZPty);

    pRootPty->addSubProperty(pUpPty);
    pRootPty->addSubProperty(pRightPty);
    //���붨������
    _pSubProperty = {pUpPty, pRightPty};
    _pSubSubProperty = {pUpXPty, pUpYPty, pUpZPty, pRightXPty, pRightYPty, pRightZPty};
    //���ظ�������
    return pRootPty;
}


//����Vector��Mat4����
ComplicateMat4Type::ComplicateMat4Type(ConfigPropertyWidget & ownWidget
    , QString & name
    , WD::WDPropertyDataType type
    , QtGroupPropertyManager * pGroupMgr
    , QtStringPropertyManager * pStringMgr
    , QtDoublePropertyManager * pDoubleMgr
    , std::string & valueStr)
    : ComplicateBaseType(ownWidget, name, type)
    , _pGroupMgr(pGroupMgr)
    , _pStringMgr(pStringMgr)
    , _pDoubleMgr(pDoubleMgr)
    , _valueStr(valueStr)
{
    _subProperty.fill(nullptr);
    _subSubProperty.fill(nullptr);
    _pRootProperty = this->initCreate();
    this->updateValue();
}
ComplicateMat4Type::~ComplicateMat4Type()
{
}

bool ComplicateMat4Type::contains(QtProperty * pPty) const
{
    if (pPty == nullptr)
        return false;
    if (_pRootProperty == pPty)
        return true;
    for (size_t i = 0; i < _subProperty.size(); ++i)
    {
        if (_subProperty.at(i) == pPty)
            return true;
    }
    for (size_t i = 0; i < _subSubProperty.size(); ++i)
    {
        if (_subSubProperty.at(i) == pPty)
            return true;
    }
    return false;
}

bool ComplicateMat4Type::updateValue()
{
    if (_pRootProperty == nullptr)
        return false;
    if (_pStringMgr == nullptr)
        return false;
    if (_pDoubleMgr == nullptr)
        return false;
    //����������ֵ
    QString subValue1;
    QString subValue2;
    QString subValue3;
    QString subValue4;
    //������������ֵ
    double subSubValue11;
    double subSubValue12;
    double subSubValue13;
    double subSubValue14;

    double subSubValue21;
    double subSubValue22;
    double subSubValue23;
    double subSubValue24;

    double subSubValue31;
    double subSubValue32;
    double subSubValue33;
    double subSubValue34;

    double subSubValue41;
    double subSubValue42;
    double subSubValue43;
    double subSubValue44;

    switch (_type)
    {
    case WD::PDT_FMat4:
        {
            WD::FMat4 value;
            value.fromString(_valueStr);
            _rootValue = QString::fromUtf8(value.toString().data());
        }
        break;
    case WD::PDT_DMat4:
        {
            WD::DMat4 value;
            value.fromString(_valueStr);
            _rootValue = QString::fromUtf8(value.toString().data());
        }
        break;
    default:
        return false;
        break;
    }
    if (!isStringValid(_rootValue,16))
        return false;

    QStringList valueList = _rootValue.split(", ");
    //��������������ֵ
    subSubValue11 = valueList.at(0).toDouble();
    subSubValue12 = valueList.at(1).toDouble();
    subSubValue13 = valueList.at(2).toDouble();
    subSubValue14 = valueList.at(3).toDouble();
    
    subSubValue21 = valueList.at(4).toDouble();
    subSubValue22 = valueList.at(5).toDouble();
    subSubValue23 = valueList.at(6).toDouble();
    subSubValue24 = valueList.at(7).toDouble();
    
    subSubValue31 = valueList.at(8).toDouble();
    subSubValue32 = valueList.at(9).toDouble();
    subSubValue33 = valueList.at(10).toDouble();
    subSubValue34 = valueList.at(11).toDouble();
    
    subSubValue41 = valueList.at(12).toDouble();
    subSubValue42 = valueList.at(13).toDouble();
    subSubValue43 = valueList.at(14).toDouble();
    subSubValue44 = valueList.at(15).toDouble();

    //������������ֵ
    subValue1 = QString::number(subSubValue11, 'f', 2)  
        + " " + QString::number(subSubValue12, 'f', 2)  
        + " " + QString::number(subSubValue13, 'f', 2)  
        + " " + QString::number(subSubValue14, 'f', 2);

    subValue2 = QString::number(subSubValue21, 'f', 2)  
        + " " + QString::number(subSubValue22, 'f', 2)  
        + " " + QString::number(subSubValue23, 'f', 2)  
        + " " + QString::number(subSubValue24, 'f', 2);

    subValue3 = QString::number(subSubValue31, 'f', 2)  
        + " " + QString::number(subSubValue32, 'f', 2)  
        + " " + QString::number(subSubValue33, 'f', 2)  
        + " " + QString::number(subSubValue34, 'f', 2);

    subValue4 = QString::number(subSubValue41, 'f', 2)  
        + " " + QString::number(subSubValue42, 'f', 2)  
        + " " + QString::number(subSubValue43, 'f', 2)  
        + " " + QString::number(subSubValue44, 'f', 2);
    //������������ֵ
    _pStringMgr->setValue(_subProperty.at(0), subValue1);
    _pStringMgr->setValue(_subProperty.at(1), subValue2);
    _pStringMgr->setValue(_subProperty.at(2), subValue3);
    _pStringMgr->setValue(_subProperty.at(3), subValue4);
    //��������������ֵ
    _pDoubleMgr->setValue(_subSubProperty.at(0), subSubValue11);
    _pDoubleMgr->setValue(_subSubProperty.at(1), subSubValue12);
    _pDoubleMgr->setValue(_subSubProperty.at(2), subSubValue13);
    _pDoubleMgr->setValue(_subSubProperty.at(3), subSubValue14);

    _pDoubleMgr->setValue(_subSubProperty.at(4), subSubValue21);
    _pDoubleMgr->setValue(_subSubProperty.at(5), subSubValue22);
    _pDoubleMgr->setValue(_subSubProperty.at(6), subSubValue23);
    _pDoubleMgr->setValue(_subSubProperty.at(7), subSubValue24);

    _pDoubleMgr->setValue(_subSubProperty.at(8), subSubValue31);
    _pDoubleMgr->setValue(_subSubProperty.at(9), subSubValue32);
    _pDoubleMgr->setValue(_subSubProperty.at(10), subSubValue33);
    _pDoubleMgr->setValue(_subSubProperty.at(11), subSubValue34);

    _pDoubleMgr->setValue(_subSubProperty.at(12), subSubValue41);
    _pDoubleMgr->setValue(_subSubProperty.at(13), subSubValue42);
    _pDoubleMgr->setValue(_subSubProperty.at(14), subSubValue43);
    _pDoubleMgr->setValue(_subSubProperty.at(15), subSubValue44);

    return true;
}

QtProperty * ComplicateMat4Type::getRootProperty()
{
    return _pRootProperty;
}

std::string ComplicateMat4Type::getValue()
{
    if (_rootValue.isEmpty())
        return "";
    std::string value = _rootValue.toUtf8().data();
    return value;
}

bool ComplicateMat4Type::updateShowP(QtProperty * pPty)
{
    if (pPty == nullptr)
        return false;
    
    if (_pStringMgr == nullptr || _pDoubleMgr == nullptr)
        return false;

    if (_pRootProperty == nullptr
        || _subProperty.size() != 4
        || _subSubProperty.size() != 16)
        return false;

    //���ͱ�ʶ���������������͸�������
    int type = 0;
    //������ʶ����¼�޸�����������
    int index = -1;
    for (int i = 0; i < _subProperty.size(); ++i)
    {
        if (_subProperty.at(i) == pPty)
        {
            type = 1;
            index = i;
            break;
        }
    }

    if(type == 0)
    {
        for (int i = 0; i < _subSubProperty.size(); ++i)
        {
            if (_subSubProperty.at(i) == pPty)
            {
                type = 2;
                index = i;
                break;
            }
        }
    }
    if (index < 0)
        return false;

    switch (type)
    {
    //��������
    case 1:
        {
            //����������ʾֵ
            if(!this->updateSubProperty(index))
                return false;
        }break;
    //����������
    case 2:
        {
            //���¸�����ʾ��ֵ
            if(!this->updateParentProperty(index))
                return false;
            
        }break;
    default:
        return false;
        break;
    }
    //����_valueStr��ֵ
    QString subValue1 = _pStringMgr->value(_subProperty.at(0));
    QString subValue2 = _pStringMgr->value(_subProperty.at(1));
    QString subValue3 = _pStringMgr->value(_subProperty.at(2));
    QString subValue4 = _pStringMgr->value(_subProperty.at(3));

    _rootValue = subValue1 + " " +  subValue2 + " " + subValue3 + " " + subValue4;
    return true;
}

QtProperty * ComplicateMat4Type::initCreate()
{
    if (_pGroupMgr == nullptr)
        return nullptr;
    if (_pStringMgr == nullptr)
        return nullptr;
    if (_pDoubleMgr == nullptr)
        return nullptr;

    //�����������
    _pRootProperty = _pGroupMgr->addProperty(_name);
    //������������
    QString subName1 = "[0]";
    QString subName2 = "[1]";
    QString subName3 = "[2]";
    QString subName4 = "[3]";
    //��������������
    QString subSubName11 = subName1+"[0]";
    QString subSubName12 = subName1+"[1]";
    QString subSubName13 = subName1+"[2]";
    QString subSubName14 = subName1+"[3]";

    QString subSubName21 = subName2+"[0]";
    QString subSubName22 = subName2+"[1]";
    QString subSubName23 = subName2+"[2]";
    QString subSubName24 = subName2+"[3]";

    QString subSubName31 = subName3+"[0]";
    QString subSubName32 = subName3+"[1]";
    QString subSubName33 = subName3+"[2]";
    QString subSubName34 = subName3+"[3]";

    QString subSubName41 = subName4+"[0]";
    QString subSubName42 = subName4+"[1]";
    QString subSubName43 = subName4+"[2]";
    QString subSubName44 = subName4+"[3]";
    //������������
    QtProperty* subPty1 = _pStringMgr->addProperty(subName1);
    QtProperty* subPty2 = _pStringMgr->addProperty(subName2);
    QtProperty* subPty3 = _pStringMgr->addProperty(subName3);
    QtProperty* subPty4 = _pStringMgr->addProperty(subName4);
    _subProperty = {subPty1, subPty2, subPty3, subPty4};
    //��������������
    QtProperty* subSubPty11 = _pDoubleMgr->addProperty(subSubName11);
    QtProperty* subSubPty12 = _pDoubleMgr->addProperty(subSubName12);
    QtProperty* subSubPty13 = _pDoubleMgr->addProperty(subSubName13);
    QtProperty* subSubPty14 = _pDoubleMgr->addProperty(subSubName14);

    QtProperty* subSubPty21 = _pDoubleMgr->addProperty(subSubName21);
    QtProperty* subSubPty22 = _pDoubleMgr->addProperty(subSubName22);
    QtProperty* subSubPty23 = _pDoubleMgr->addProperty(subSubName23);
    QtProperty* subSubPty24 = _pDoubleMgr->addProperty(subSubName24);

    QtProperty* subSubPty31 = _pDoubleMgr->addProperty(subSubName31);
    QtProperty* subSubPty32 = _pDoubleMgr->addProperty(subSubName32);
    QtProperty* subSubPty33 = _pDoubleMgr->addProperty(subSubName33);
    QtProperty* subSubPty34 = _pDoubleMgr->addProperty(subSubName34);

    QtProperty* subSubPty41 = _pDoubleMgr->addProperty(subSubName41);
    QtProperty* subSubPty42 = _pDoubleMgr->addProperty(subSubName42);
    QtProperty* subSubPty43 = _pDoubleMgr->addProperty(subSubName43);
    QtProperty* subSubPty44 = _pDoubleMgr->addProperty(subSubName44);

    _subSubProperty = {subSubPty11,subSubPty12,subSubPty13,subSubPty14
                      ,subSubPty21,subSubPty22,subSubPty23,subSubPty24
                      ,subSubPty31,subSubPty32,subSubPty33,subSubPty34
                      ,subSubPty41,subSubPty42,subSubPty43,subSubPty44};

    //��Ӳ㼶��ϵ
    _pRootProperty->addSubProperty(subPty1);
    _pRootProperty->addSubProperty(subPty2);
    _pRootProperty->addSubProperty(subPty3);
    _pRootProperty->addSubProperty(subPty4);

    subPty1->addSubProperty(subSubPty11);
    subPty1->addSubProperty(subSubPty12);
    subPty1->addSubProperty(subSubPty13);
    subPty1->addSubProperty(subSubPty14);

    subPty2->addSubProperty(subSubPty21);
    subPty2->addSubProperty(subSubPty22);
    subPty2->addSubProperty(subSubPty23);
    subPty2->addSubProperty(subSubPty24);

    subPty3->addSubProperty(subSubPty31);
    subPty3->addSubProperty(subSubPty32);
    subPty3->addSubProperty(subSubPty33);
    subPty3->addSubProperty(subSubPty34);

    subPty4->addSubProperty(subSubPty41);
    subPty4->addSubProperty(subSubPty42);
    subPty4->addSubProperty(subSubPty43);
    subPty4->addSubProperty(subSubPty44);

    return _pRootProperty;
}

bool ComplicateMat4Type::updateSubProperty(int index)
{
    //��ȡ�޸ĵ�ֵ
    QString valueStr = _pStringMgr->value(_subProperty.at(index));
    if (!isStringValid(valueStr,4))
        return false;
    QStringList valueList = valueStr.split(" ");
    //�������������Ŀ�ʼ�ͽ�������
    int indexBegin = index * 4;
    int indexEnd   = (index+1) * 4;
    //ѭ����ʱ����
    int temp = 0;
    //ѭ��������������������ʾֵ
    for (size_t i = indexBegin; i < indexEnd; ++i)
    {
        QtProperty* subSubProperty = _subSubProperty.at(i);
        double subSubValue = valueList.at(temp).toDouble();
        _pDoubleMgr->setValue(subSubProperty, subSubValue);
        temp += 1;
    }
    return true;
}

bool ComplicateMat4Type::updateParentProperty(int index)
{
    //���㸸����������ֵ
    int parentIndex = index / 4;
    if (parentIndex < 0 && parentIndex > 3)
        return false;
    QtProperty* parentPty = _subProperty.at(parentIndex);
    QtProperty* currentPty = _subSubProperty.at(index);
    if (parentPty == nullptr)
        return false;
    //�Ƿ�Ϸ�
    QString parentValue = _pStringMgr->value(parentPty);
    if (!isStringValid(parentValue,4))
        return false;
    QStringList valueList = parentValue.split(" ");
    //�����޸ĵ������������ڸ�������ֵ������
    int valueIndex = index % 4;
    if (valueIndex < 0 && valueIndex > 3)
        return false;
    //��ȡ�޸ĵ�ֵ
    double currentValue = _pDoubleMgr->value(currentPty);
    QString currentStrValue = QString::number(currentValue, 'f', 2);
    //Ӧ���޸ĵ�����������
    valueList.replace(valueIndex,currentStrValue);
    return true;
}

//����Vector��Mat3����

ComplicateMat3Type::ComplicateMat3Type(ConfigPropertyWidget & ownWidget
    , QString & name
    , WD::WDPropertyDataType type
    , QtGroupPropertyManager * pGroupMgr
    , QtStringPropertyManager * pStringMgr
    , QtDoublePropertyManager * pDoubleMgr
    , std::string & valueStr)
    : ComplicateBaseType(ownWidget, name, type)
    , _pGroupMgr(pGroupMgr)
    , _pStringMgr(pStringMgr)
    , _pDoubleMgr(pDoubleMgr)
    , _valueStr(valueStr)
{
    _subProperty.fill(nullptr);
    _subSubProperty.fill(nullptr);
    _pRootProperty = this->initCreate();
    this->updateValue();
}

ComplicateMat3Type::~ComplicateMat3Type()
{
}

bool ComplicateMat3Type::contains(QtProperty * pPty) const
{
    if (pPty == nullptr)
        return false;
    if (_pRootProperty == pPty)
        return true;
    for (size_t i = 0; i < _subProperty.size(); ++i)
    {
        if (_subProperty.at(i) == pPty)
            return true;
    }
    for (size_t i = 0; i < _subSubProperty.size(); ++i)
    {
        if (_subSubProperty.at(i) == pPty)
            return true;
    }
    return false;
}

bool ComplicateMat3Type::updateValue()
{
    if (_pRootProperty == nullptr)
        return false;
    if (_pStringMgr == nullptr)
        return false;
    if (_pDoubleMgr == nullptr)
        return false;
    //����������ֵ
    QString subValue1;
    QString subValue2;
    QString subValue3;
    //������������ֵ
    double subSubValue11;
    double subSubValue12;
    double subSubValue13;

    double subSubValue21;
    double subSubValue22;
    double subSubValue23;

    double subSubValue31;
    double subSubValue32;
    double subSubValue33;

    switch (_type)
    {
    case WD::PDT_FMat3:
        {
            WD::FMat3 value;
            value.fromString(_valueStr);
            _rootValue = QString::fromUtf8(value.toString().data());
        }
        break;
    case WD::PDT_DMat3:
        {
            WD::DMat3 value;
            value.fromString(_valueStr);
            _rootValue = QString::fromUtf8(value.toString().data());
        }
        break;
    default:
        return false;
        break;
    }
    if (!isStringValid(_rootValue,9))
        return false;

    QStringList valueList = _rootValue.split(", ");
    //��������������ֵ
    subSubValue11 = valueList.at(0).toDouble();
    subSubValue12 = valueList.at(1).toDouble();
    subSubValue13 = valueList.at(2).toDouble();
    
    subSubValue21 = valueList.at(3).toDouble();
    subSubValue22 = valueList.at(4).toDouble();
    subSubValue23 = valueList.at(5).toDouble();
    
    subSubValue31 = valueList.at(6).toDouble();
    subSubValue32 = valueList.at(7).toDouble();
    subSubValue33 = valueList.at(8).toDouble();
    //������������ֵ
    subValue1 = QString::number(subSubValue11, 'f', 2)  
        + " " + QString::number(subSubValue12, 'f', 2)  
        + " " + QString::number(subSubValue13, 'f', 2);

    subValue2 = QString::number(subSubValue21, 'f', 2)  
        + " " + QString::number(subSubValue22, 'f', 2)  
        + " " + QString::number(subSubValue23, 'f', 2);

    subValue3 = QString::number(subSubValue31, 'f', 2)  
        + " " + QString::number(subSubValue32, 'f', 2)  
        + " " + QString::number(subSubValue33, 'f', 2);
    //������������ֵ
    _pStringMgr->setValue(_subProperty.at(0), subValue1);
    _pStringMgr->setValue(_subProperty.at(1), subValue2);
    _pStringMgr->setValue(_subProperty.at(2), subValue3);
    //��������������ֵ
    _pDoubleMgr->setValue(_subSubProperty.at(0), subSubValue11);
    _pDoubleMgr->setValue(_subSubProperty.at(1), subSubValue12);
    _pDoubleMgr->setValue(_subSubProperty.at(2), subSubValue13);


    _pDoubleMgr->setValue(_subSubProperty.at(3), subSubValue21);
    _pDoubleMgr->setValue(_subSubProperty.at(4), subSubValue22);
    _pDoubleMgr->setValue(_subSubProperty.at(5), subSubValue23);

    _pDoubleMgr->setValue(_subSubProperty.at(6), subSubValue31);
    _pDoubleMgr->setValue(_subSubProperty.at(7), subSubValue32);
    _pDoubleMgr->setValue(_subSubProperty.at(8), subSubValue33);


    return true;
}

QtProperty * ComplicateMat3Type::getRootProperty()
{
    return _pRootProperty;
}

std::string ComplicateMat3Type::getValue()
{
    if (_rootValue.isEmpty())
        return "";
    std::string value = _rootValue.toUtf8().data();
    return value;
}

bool ComplicateMat3Type::updateShowP(QtProperty * pPty)
{
    if (pPty == nullptr)
        return false;
    
    if (_pStringMgr == nullptr || _pDoubleMgr == nullptr)
        return false;

    if (_pRootProperty == nullptr
        || _subProperty.size() != 3
        || _subSubProperty.size() != 9)
        return false;

    //���ͱ�ʶ���������������͸�������
    int type = 0;
    //������ʶ����¼�޸�����������
    int index = -1;
    for (int i = 0; i < _subProperty.size(); ++i)
    {
        if (_subProperty.at(i) == pPty)
        {
            type = 1;
            index = i;
            break;
        }
    }
    if(type == 0)
    {
        for (int i = 0; i < _subSubProperty.size(); ++i)
        {
            if (_subSubProperty.at(i) == pPty)
            {
                type = 2;
                index = i;
                break;
            }
        }
    }
    if (index < 0)
        return false;

    switch (type)
    {
    //��������
    case 1:
        {
            //����������ʾֵ
            if(!this->updateSubProperty(index))
                return false;
        }break;
    //����������
    case 2:
        {
            //���¸�����ʾ��ֵ
            if(!this->updateParentProperty(index))
                return false;
            
        }break;
    default:
        return false;
        break;
    }
    //����_valueStr��ֵ
    QString subValue1 = _pStringMgr->value(_subProperty.at(0));
    QString subValue2 = _pStringMgr->value(_subProperty.at(1));
    QString subValue3 = _pStringMgr->value(_subProperty.at(2));

    _rootValue = subValue1 + " " +  subValue2 + " " + subValue3;
    return true;
}

QtProperty * ComplicateMat3Type::initCreate()
{
    if (_pGroupMgr == nullptr)
        return nullptr;
    if (_pStringMgr == nullptr)
        return nullptr;
    if (_pDoubleMgr == nullptr)
        return nullptr;

    //�����������
    _pRootProperty = _pGroupMgr->addProperty(_name);
    //������������
    QString subName1 = "[0]";
    QString subName2 = "[1]";
    QString subName3 = "[2]";
    //��������������
    QString subSubName11 = subName1+"[0]";
    QString subSubName12 = subName1+"[1]";
    QString subSubName13 = subName1+"[2]";

    QString subSubName21 = subName2+"[0]";
    QString subSubName22 = subName2+"[1]";
    QString subSubName23 = subName2+"[2]";

    QString subSubName31 = subName3+"[0]";
    QString subSubName32 = subName3+"[1]";
    QString subSubName33 = subName3+"[2]";

    //������������
    QtProperty* subPty1 = _pStringMgr->addProperty(subName1);
    QtProperty* subPty2 = _pStringMgr->addProperty(subName2);
    QtProperty* subPty3 = _pStringMgr->addProperty(subName3);
    _subProperty = {subPty1, subPty2, subPty3};
    //��������������
    QtProperty* subSubPty11 = _pDoubleMgr->addProperty(subSubName11);
    QtProperty* subSubPty12 = _pDoubleMgr->addProperty(subSubName12);
    QtProperty* subSubPty13 = _pDoubleMgr->addProperty(subSubName13);

    QtProperty* subSubPty21 = _pDoubleMgr->addProperty(subSubName21);
    QtProperty* subSubPty22 = _pDoubleMgr->addProperty(subSubName22);
    QtProperty* subSubPty23 = _pDoubleMgr->addProperty(subSubName23);

    QtProperty* subSubPty31 = _pDoubleMgr->addProperty(subSubName31);
    QtProperty* subSubPty32 = _pDoubleMgr->addProperty(subSubName32);
    QtProperty* subSubPty33 = _pDoubleMgr->addProperty(subSubName33);


    _subSubProperty = {subSubPty11,subSubPty12,subSubPty13
                      ,subSubPty21,subSubPty22,subSubPty23
                      ,subSubPty31,subSubPty32,subSubPty33};

    //��Ӳ㼶��ϵ
    _pRootProperty->addSubProperty(subPty1);
    _pRootProperty->addSubProperty(subPty2);
    _pRootProperty->addSubProperty(subPty3);

    subPty1->addSubProperty(subSubPty11);
    subPty1->addSubProperty(subSubPty12);
    subPty1->addSubProperty(subSubPty13);

    subPty2->addSubProperty(subSubPty21);
    subPty2->addSubProperty(subSubPty22);
    subPty2->addSubProperty(subSubPty23);

    subPty3->addSubProperty(subSubPty31);
    subPty3->addSubProperty(subSubPty32);
    subPty3->addSubProperty(subSubPty33);

    return _pRootProperty;
}

bool ComplicateMat3Type::updateSubProperty(int index)
{
    //��ȡ�޸ĵ�ֵ
    QString valueStr = _pStringMgr->value(_subProperty.at(index));
    if (!isStringValid(valueStr,3))
        return false;
    QStringList valueList = valueStr.split(" ");
    //�������������Ŀ�ʼ�ͽ�������
    int indexBegin = index * 3;
    int indexEnd   = (index+1) * 3;
    //ѭ����ʱ����
    int temp = 0;
    //ѭ��������������������ʾֵ
    for (size_t i = indexBegin; i < indexEnd; ++i)
    {
        QtProperty* subSubProperty = _subSubProperty.at(i);
        double subSubValue = valueList.at(temp).toDouble();
        _pDoubleMgr->setValue(subSubProperty, subSubValue);
        temp += 1;
    }
    return true;
}

bool ComplicateMat3Type::updateParentProperty(int index)
{
    //���㸸����������ֵ
    int parentIndex = index / 3;
    if (parentIndex < 0 && parentIndex > 2)
        return false;
    QtProperty* parentPty = _subProperty.at(parentIndex);
    QtProperty* currentPty = _subSubProperty.at(index);
    if (parentPty == nullptr)
        return false;
    //�Ƿ�Ϸ�
    QString parentValue = _pStringMgr->value(parentPty);
    if (!isStringValid(parentValue,3))
        return false;
    QStringList valueList = parentValue.split(" ");
    //�����޸ĵ������������ڸ�������ֵ������
    int valueIndex = index % 3;
    if (valueIndex < 0 && valueIndex > 2)
        return false;
    //��ȡ�޸ĵ�ֵ
    double currentValue = _pDoubleMgr->value(currentPty);
    QString currentStrValue = QString::number(currentValue, 'f', 2);
    //Ӧ���޸ĵ�����������
    valueList.replace(valueIndex,currentStrValue);
    return true;
}

//����Vector��Mat2����
ComplicateMat2Type::ComplicateMat2Type(ConfigPropertyWidget & ownWidget
    , QString & name
    , WD::WDPropertyDataType type
    , QtGroupPropertyManager * pGroupMgr
    , QtStringPropertyManager * pStringMgr
    , QtDoublePropertyManager * pDoubleMgr
    , std::string & valueStr)
    : ComplicateBaseType(ownWidget, name, type)
    , _pGroupMgr(pGroupMgr)
    , _pStringMgr(pStringMgr)
    , _pDoubleMgr(pDoubleMgr)
    , _valueStr(valueStr)
{
    _subProperty.fill(nullptr);
    _subSubProperty.fill(nullptr);
    _pRootProperty = this->initCreate();
    this->updateValue();
}

ComplicateMat2Type::~ComplicateMat2Type()
{
}

bool ComplicateMat2Type::contains(QtProperty * pPty) const
{
    if (pPty == nullptr)
        return false;
    if (_pRootProperty == pPty)
        return true;
    for (size_t i = 0; i < _subProperty.size(); ++i)
    {
        if (_subProperty.at(i) == pPty)
            return true;
    }
    for (size_t i = 0; i < _subSubProperty.size(); ++i)
    {
        if (_subSubProperty.at(i) == pPty)
            return true;
    }
    return false;
}

bool ComplicateMat2Type::updateValue()
{
    if (_pRootProperty == nullptr)
        return false;
    if (_pStringMgr == nullptr)
        return false;
    if (_pDoubleMgr == nullptr)
        return false;
    //����������ֵ
    QString subValue1;
    QString subValue2;
    //������������ֵ
    double subSubValue11;
    double subSubValue12;

    double subSubValue21;
    double subSubValue22;

    switch (_type)
    {
    case WD::PDT_FMat2:
        {
            WD::FMat2 value;
            value.fromString(_valueStr);
            _rootValue = QString::fromUtf8(value.toString().data());
        }
        break;
    case WD::PDT_DMat2:
        {
            WD::DMat2 value;
            value.fromString(_valueStr);
            _rootValue = QString::fromUtf8(value.toString().data());
        }
        break;
    default:
        return false;
        break;
    }
    if (!isStringValid(_rootValue,4))
        return false;

    QStringList valueList = _rootValue.split(", ");
    //��������������ֵ
    subSubValue11 = valueList.at(0).toDouble();
    subSubValue12 = valueList.at(1).toDouble();
    
    subSubValue21 = valueList.at(2).toDouble();
    subSubValue22 = valueList.at(3).toDouble();
    //������������ֵ
    subValue1 = QString::number(subSubValue11, 'f', 2)  
        + " " + QString::number(subSubValue12, 'f', 2);

    subValue2 = QString::number(subSubValue21, 'f', 2)  
        + " " + QString::number(subSubValue22, 'f', 2);
    //������������ֵ
    _pStringMgr->setValue(_subProperty.at(0), subValue1);
    _pStringMgr->setValue(_subProperty.at(1), subValue2);
    //��������������ֵ
    _pDoubleMgr->setValue(_subSubProperty.at(0), subSubValue11);
    _pDoubleMgr->setValue(_subSubProperty.at(1), subSubValue12);


    _pDoubleMgr->setValue(_subSubProperty.at(3), subSubValue21);
    _pDoubleMgr->setValue(_subSubProperty.at(4), subSubValue22);

    return true;
}

QtProperty * ComplicateMat2Type::getRootProperty()
{
    return _pRootProperty;
}

std::string ComplicateMat2Type::getValue()
{
    if (_rootValue.isEmpty())
        return "";
    std::string value = _rootValue.toUtf8().data();
    return value;
}

bool ComplicateMat2Type::updateShowP(QtProperty * pPty)
{
    if (pPty == nullptr)
        return false;
    
    if (_pStringMgr == nullptr || _pDoubleMgr == nullptr)
        return false;

    if (_pRootProperty == nullptr
        || _subProperty.size() != 2
        || _subSubProperty.size() != 4)
        return false;

    //���ͱ�ʶ���������������͸�������
    int type = 0;
    //������ʶ����¼�޸�����������
    int index = -1;
    for (int i = 0; i < _subProperty.size(); ++i)
    {
        if (_subProperty.at(i) == pPty)
        {
            type = 1;
            index = i;
            break;
        }
    }
    if(type == 0)
    {
        for (int i = 0; i < _subSubProperty.size(); ++i)
        {
            if (_subSubProperty.at(i) == pPty)
            {
                type = 2;
                index = i;
                break;
            }
        }
    }
    if (index < 0)
        return false;

    switch (type)
    {
    //��������
    case 1:
        {
            //����������ʾֵ
            if(!this->updateSubProperty(index))
                return false;
        }break;
    //����������
    case 2:
        {
            //���¸�����ʾ��ֵ
            if(!this->updateParentProperty(index))
                return false;
            
        }break;
    default:
        return false;
        break;
    }
    //����_valueStr��ֵ
    QString subValue1 = _pStringMgr->value(_subProperty.at(0));
    QString subValue2 = _pStringMgr->value(_subProperty.at(1));

    _rootValue = subValue1 + " " +  subValue2;
    return true;
}

QtProperty * ComplicateMat2Type::initCreate()
{
    if (_pGroupMgr == nullptr)
        return nullptr;
    if (_pStringMgr == nullptr)
        return nullptr;
    if (_pDoubleMgr == nullptr)
        return nullptr;

    //�����������
    _pRootProperty = _pGroupMgr->addProperty(_name);
    //������������
    QString subName1 = "[0]";
    QString subName2 = "[1]";
    //��������������
    QString subSubName11 = subName1+"[0]";
    QString subSubName12 = subName1+"[1]";

    QString subSubName21 = subName2+"[0]";
    QString subSubName22 = subName2+"[1]";

    //������������
    QtProperty* subPty1 = _pStringMgr->addProperty(subName1);
    QtProperty* subPty2 = _pStringMgr->addProperty(subName2);
    _subProperty = {subPty1, subPty2};
    //��������������
    QtProperty* subSubPty11 = _pDoubleMgr->addProperty(subSubName11);
    QtProperty* subSubPty12 = _pDoubleMgr->addProperty(subSubName12);

    QtProperty* subSubPty21 = _pDoubleMgr->addProperty(subSubName21);
    QtProperty* subSubPty22 = _pDoubleMgr->addProperty(subSubName22);


    _subSubProperty = {subSubPty11,subSubPty12
                      ,subSubPty21,subSubPty22};

    //��Ӳ㼶��ϵ
    _pRootProperty->addSubProperty(subPty1);
    _pRootProperty->addSubProperty(subPty2);

    subPty1->addSubProperty(subSubPty11);
    subPty1->addSubProperty(subSubPty12);

    subPty2->addSubProperty(subSubPty21);
    subPty2->addSubProperty(subSubPty22);

    return _pRootProperty;
}

bool ComplicateMat2Type::updateSubProperty(int index)
{
    //��ȡ�޸ĵ�ֵ
    QString valueStr = _pStringMgr->value(_subProperty.at(index));
    if (!isStringValid(valueStr,2))
        return false;
    QStringList valueList = valueStr.split(" ");
    //�������������Ŀ�ʼ�ͽ�������
    int indexBegin = index * 2;
    int indexEnd   = (index+1) * 2;
    //ѭ����ʱ����
    int temp = 0;
    //ѭ��������������������ʾֵ
    for (size_t i = indexBegin; i < indexEnd; ++i)
    {
        QtProperty* subSubProperty = _subSubProperty.at(i);
        double subSubValue = valueList.at(temp).toDouble();
        _pDoubleMgr->setValue(subSubProperty, subSubValue);
        temp += 1;
    }
    return true;
}

bool ComplicateMat2Type::updateParentProperty(int index)
{
    //���㸸����������ֵ
    int parentIndex = index / 2;
    if (parentIndex < 0 && parentIndex > 1)
        return false;
    QtProperty* parentPty = _subProperty.at(parentIndex);
    QtProperty* currentPty = _subSubProperty.at(index);
    if (parentPty == nullptr)
        return false;
    //�Ƿ�Ϸ�
    QString parentValue = _pStringMgr->value(parentPty);
    if (!isStringValid(parentValue,2))
        return false;
    QStringList valueList = parentValue.split(" ");
    //�����޸ĵ������������ڸ�������ֵ������
    int valueIndex = index % 2;
    if (valueIndex < 0 && valueIndex > 1)
        return false;
    //��ȡ�޸ĵ�ֵ
    double currentValue = _pDoubleMgr->value(currentPty);
    QString currentStrValue = QString::number(currentValue, 'f', 2);
    //Ӧ���޸ĵ�����������
    valueList.replace(valueIndex,currentStrValue);
    return true;
}

//����Vector��Aabb3����
ComplicateAabb3Type::ComplicateAabb3Type(ConfigPropertyWidget & ownWidget
    , QString & name
    , WD::WDPropertyDataType type
    , QtGroupPropertyManager * pGroupMgr
    , QtStringPropertyManager * pStringMgr
    , QtDoublePropertyManager * pDoubleMgr
    , std::string & valueStr)
    : ComplicateBaseType(ownWidget, name, type)
    , _pGroupMgr(pGroupMgr)
    , _pStringMgr(pStringMgr)
    , _pDoubleMgr(pDoubleMgr)
    , _valueStr(valueStr)
{
    _subProperty.fill(nullptr);
    _subSubProperty.fill(nullptr);
    _pRootProperty = this->initCreate();
    this->updateValue();
}

ComplicateAabb3Type::~ComplicateAabb3Type()
{
}

bool ComplicateAabb3Type::contains(QtProperty * pPty) const
{
    if (pPty == nullptr)
        return false;
    if (_pRootProperty == pPty)
        return true;
    for (size_t i = 0; i < _subProperty.size(); ++i)
    {
        if (_subProperty.at(i) == pPty)
            return true;
    }
    for (size_t i = 0; i < _subSubProperty.size(); ++i)
    {
        if (_subSubProperty.at(i) == pPty)
            return true;
    }
    return false;
}

bool ComplicateAabb3Type::updateValue()
{
    if (_pRootProperty == nullptr)
        return false;
    if (_pStringMgr == nullptr)
        return false;
    if (_pDoubleMgr == nullptr)
        return false;
    //����������ֵ
    QString subValue1;
    QString subValue2;
    //������������ֵ
    double subSubValue11;
    double subSubValue12;
    double subSubValue13;

    double subSubValue21;
    double subSubValue22;
    double subSubValue23;

    switch (_type)
    {
    case WD::PDT_FAabb3:
        {
            WD::FAabb3 value;
            value.fromString(_valueStr);
            _rootValue = QString::fromUtf8(value.toString().data());
        }
        break;
    case WD::PDT_DAabb3:
        {
            WD::DAabb3 value;
            value.fromString(_valueStr);
            _rootValue = QString::fromUtf8(value.toString().data());
        }
        break;
    default:
        return false;
        break;
    }
    if (!isStringValid(_rootValue,6))
        return false;

    QStringList valueList = _rootValue.split(" ");
    //������������ֵ
    subValue1 = valueList.at(0)  + " " + valueList.at(1)  + " " + valueList.at(2);
    subValue2 = valueList.at(3)  + " " + valueList.at(4)  + " " + valueList.at(5);
    //��������������ֵ
    subSubValue11 = valueList.at(0).toDouble();
    subSubValue12 = valueList.at(1).toDouble();
    subSubValue13 = valueList.at(2).toDouble();
    
    subSubValue21 = valueList.at(3).toDouble();
    subSubValue22 = valueList.at(4).toDouble();
    subSubValue23 = valueList.at(5).toDouble();

    //������������ֵ
    _pStringMgr->setValue(_subProperty.at(0), subValue1);
    _pStringMgr->setValue(_subProperty.at(1), subValue2);
    //��������������ֵ
    _pDoubleMgr->setValue(_subSubProperty.at(0), subSubValue11);
    _pDoubleMgr->setValue(_subSubProperty.at(1), subSubValue12);
    _pDoubleMgr->setValue(_subSubProperty.at(2), subSubValue13);

    _pDoubleMgr->setValue(_subSubProperty.at(3), subSubValue21);
    _pDoubleMgr->setValue(_subSubProperty.at(4), subSubValue22);
    _pDoubleMgr->setValue(_subSubProperty.at(5), subSubValue23);

    return true;
}

QtProperty * ComplicateAabb3Type::getRootProperty()
{
    return _pRootProperty;
}

std::string ComplicateAabb3Type::getValue()
{
    if (_rootValue.isEmpty())
        return "";
    std::string value = _rootValue.toUtf8().data();
    return value;
}

bool ComplicateAabb3Type::updateShowP(QtProperty * pPty)
{
    if (pPty == nullptr)
        return false;
    
    if (_pStringMgr == nullptr || _pDoubleMgr == nullptr)
        return false;

    if (_pRootProperty == nullptr
        || _subProperty.size() != 2
        || _subSubProperty.size() != 6)
        return false;

    //���ͱ�ʶ���������������͸�������
    int type = 0;
    //������ʶ����¼�޸�����������
    int index = -1;
    for (int i = 0; i < _subProperty.size(); ++i)
    {
        if (_subProperty.at(i) == pPty)
        {
            type = 1;
            index = i;
            break;
        }
    }

    if(type == 0)
    {
        for (int i = 0; i < _subSubProperty.size(); ++i)
        {
            if (_subSubProperty.at(i) == pPty)
            {
                type = 2;
                index = i;
                break;
            }
        }
    }
    if (index < 0)
        return false;

    switch (type)
    {
    //��������
    case 1:
        {
            //����������ʾֵ
            if(!this->updateSubProperty(index))
                return false;
        }break;
    //����������
    case 2:
        {
            //���¸�����ʾ��ֵ
            if(!this->updateParentProperty(index))
                return false;
            
        }break;
    default:
        return false;
        break;
    }
    //����_valueStr��ֵ
    QString subValue1 = _pStringMgr->value(_subProperty.at(0));
    QString subValue2 = _pStringMgr->value(_subProperty.at(1));
    QString subValue3 = _pStringMgr->value(_subProperty.at(2));

    _rootValue = subValue1 + " " +  subValue2 + " " + subValue3;
    return true;
}

QtProperty * ComplicateAabb3Type::initCreate()
{
    if (_pGroupMgr == nullptr)
        return nullptr;
    if (_pStringMgr == nullptr)
        return nullptr;
    if (_pDoubleMgr == nullptr)
        return nullptr;

    //�����������
    _pRootProperty = _pGroupMgr->addProperty(_name);
    //������������
    QString subName1 = _name+"Min";
    QString subName2 = _name+"Max";
    //��������������
    QString minNameX = subName1+".X";
    QString minNameY = subName1+".Y";
    QString minNameZ = subName1+".Z";

    QString maxNameX = subName2+".X";
    QString maxNameY = subName2+".Y";
    QString maxNameZ = subName2+".Z";

    //������������
    QtProperty* subPty1 = _pStringMgr->addProperty(subName1);
    QtProperty* subPty2 = _pStringMgr->addProperty(subName2);
    _subProperty = {subPty1, subPty2};
    //��������������
    QtProperty* minXPty = _pDoubleMgr->addProperty(minNameX);
    QtProperty* minYPty = _pDoubleMgr->addProperty(minNameY);
    QtProperty* minZPty = _pDoubleMgr->addProperty(minNameZ);

    QtProperty* maxXPty = _pDoubleMgr->addProperty(maxNameX);
    QtProperty* maxYPty = _pDoubleMgr->addProperty(maxNameY);
    QtProperty* maxZPty = _pDoubleMgr->addProperty(maxNameZ);

    _subSubProperty = {minXPty, minYPty, minZPty, 
                       maxXPty, maxYPty, maxZPty};

    //��Ӹ��ӹ�ϵ
    _pRootProperty->addSubProperty(subPty1);
    _pRootProperty->addSubProperty(subPty2);

    subPty1->addSubProperty(minXPty);
    subPty1->addSubProperty(minYPty);
    subPty1->addSubProperty(minZPty);

    subPty2->addSubProperty(maxXPty);
    subPty2->addSubProperty(maxYPty);
    subPty2->addSubProperty(maxZPty);

    return _pRootProperty;
}

bool ComplicateAabb3Type::updateSubProperty(int index)
{
    //��ȡ�޸ĵ�ֵ
    QString valueStr = _pStringMgr->value(_subProperty.at(index));
    if (!isStringValid(valueStr,3))
        return false;
    QStringList valueList = valueStr.split(" ");
    //�������������Ŀ�ʼ�ͽ�������
    int indexBegin = index * 3;
    int indexEnd   = (index+1) * 3;
    //ѭ����ʱ����
    int temp = 0;
    //ѭ��������������������ʾֵ
    for (size_t i = indexBegin; i < indexEnd; ++i)
    {
        QtProperty* subSubProperty = _subSubProperty.at(i);
        double subSubValue = valueList.at(temp).toDouble();
        _pDoubleMgr->setValue(subSubProperty, subSubValue);
        temp += 1;
    }
    return true;
}

bool ComplicateAabb3Type::updateParentProperty(int index)
{
    //���㸸����������ֵ
    int parentIndex = index / 3;
    if (parentIndex < 0 && parentIndex > 2)
        return false;
    QtProperty* parentPty = _subProperty.at(parentIndex);
    QtProperty* currentPty = _subSubProperty.at(index);
    if (parentPty == nullptr)
        return false;
    //�Ƿ�Ϸ�
    QString parentValue = _pStringMgr->value(parentPty);
    if (!isStringValid(parentValue,3))
        return false;
    QStringList valueList = parentValue.split(" ");
    //�����޸ĵ������������ڸ�������ֵ������
    int valueIndex = index % 3;
    if (valueIndex < 0 && valueIndex > 2)
        return false;
    //��ȡ�޸ĵ�ֵ
    double currentValue = _pDoubleMgr->value(currentPty);
    QString currentStrValue = QString::number(currentValue, 'f', 2);
    //Ӧ���޸ĵ�����������
    valueList.replace(valueIndex,currentStrValue);
    _pStringMgr->setValue(parentPty, valueList.join(" "));
    return true;
}

//����Vector��Aabb2����

ComplicateAabb2Type::ComplicateAabb2Type(ConfigPropertyWidget & ownWidget
    , QString & name
    , WD::WDPropertyDataType type
    , QtGroupPropertyManager * pGroupMgr
    , QtStringPropertyManager * pStringMgr
    , QtDoublePropertyManager * pDoubleMgr
    , std::string & valueStr)
    : ComplicateBaseType(ownWidget, name, type)
    , _pGroupMgr(pGroupMgr)
    , _pStringMgr(pStringMgr)
    , _pDoubleMgr(pDoubleMgr)
    , _valueStr(valueStr)
{
    _subProperty.fill(nullptr);
    _subSubProperty.fill(nullptr);
    _pRootProperty = this->initCreate();
    this->updateValue();
}

ComplicateAabb2Type::~ComplicateAabb2Type()
{
}

bool ComplicateAabb2Type::contains(QtProperty * pPty) const
{
    if (pPty == nullptr)
        return false;
    if (_pRootProperty == pPty)
        return true;
    for (size_t i = 0; i < _subProperty.size(); ++i)
    {
        if (_subProperty.at(i) == pPty)
            return true;
    }
    for (size_t i = 0; i < _subSubProperty.size(); ++i)
    {
        if (_subSubProperty.at(i) == pPty)
            return true;
    }
    return false;
}

bool ComplicateAabb2Type::updateValue()
{
    if (_pRootProperty == nullptr)
        return false;
    if (_pStringMgr == nullptr)
        return false;
    if (_pDoubleMgr == nullptr)
        return false;
    //����������ֵ
    QString subValue1;
    QString subValue2;
    //������������ֵ
    double subSubValue11;
    double subSubValue12;

    double subSubValue21;
    double subSubValue22;

    switch (_type)
    {
    case WD::PDT_FAabb2:
        {
            WD::FAabb2 value;
            value.fromString(_valueStr);
            _rootValue = QString::fromUtf8(value.toString().data());
        }
        break;
    case WD::PDT_DAabb2:
        {
            WD::DAabb2 value;
            value.fromString(_valueStr);
            _rootValue = QString::fromUtf8(value.toString().data());
        }
        break;
    default:
        return false;
        break;
    }
    if (!isStringValid(_rootValue,4))
        return false;

    QStringList valueList = _rootValue.split(" ");
    //������������ֵ
    subValue1 = valueList.at(0)  + " " + valueList.at(1);
    subValue2 = valueList.at(2)  + " " + valueList.at(3);
    //��������������ֵ
    subSubValue11 = valueList.at(0).toDouble();
    subSubValue12 = valueList.at(1).toDouble();
    
    subSubValue21 = valueList.at(2).toDouble();
    subSubValue22 = valueList.at(3).toDouble();

    //������������ֵ
    _pStringMgr->setValue(_subProperty.at(0), subValue1);
    _pStringMgr->setValue(_subProperty.at(1), subValue2);
    //��������������ֵ
    _pDoubleMgr->setValue(_subSubProperty.at(0), subSubValue11);
    _pDoubleMgr->setValue(_subSubProperty.at(1), subSubValue12);

    _pDoubleMgr->setValue(_subSubProperty.at(2), subSubValue21);
    _pDoubleMgr->setValue(_subSubProperty.at(3), subSubValue22);

    return true;
}

QtProperty * ComplicateAabb2Type::getRootProperty()
{
    return _pRootProperty;
}

std::string ComplicateAabb2Type::getValue()
{
    if (_rootValue.isEmpty())
        return "";
    std::string value = _rootValue.toUtf8().data();
    return value;
}

bool ComplicateAabb2Type::updateShowP(QtProperty * pPty)
{
    if (pPty == nullptr)
        return false;
    
    if (_pStringMgr == nullptr || _pDoubleMgr == nullptr)
        return false;

    if (_pRootProperty == nullptr
        || _subProperty.size() != 2
        || _subSubProperty.size() != 4)
        return false;

    //���ͱ�ʶ���������������͸�������
    int type = 0;
    //������ʶ����¼�޸�����������
    int index = -1;
    for (int i = 0; i < _subProperty.size(); ++i)
    {
        if (_subProperty.at(i) == pPty)
        {
            type = 1;
            index = i;
            break;
        }
    }
    if(type == 0)
    {
        for (int i = 0; i < _subSubProperty.size(); ++i)
        {
            if (_subSubProperty.at(i) == pPty)
            {
                type = 2;
                index = i;
                break;
            }
        }
    }
    if (index < 0)
        return false;

    switch (type)
    {
    //��������
    case 1:
        {
            //����������ʾֵ
            if(!this->updateSubProperty(index))
                return false;
        }break;
    //����������
    case 2:
        {
            //���¸�����ʾ��ֵ
            if(!this->updateParentProperty(index))
                return false;
            
        }break;
    default:
        return false;
        break;
    }
    //����_valueStr��ֵ
    QString subValue1 = _pStringMgr->value(_subProperty.at(0));
    QString subValue2 = _pStringMgr->value(_subProperty.at(1));

    _rootValue = subValue1 + " " +  subValue2;
    return true;
}

QtProperty * ComplicateAabb2Type::initCreate()
{
    if (_pGroupMgr == nullptr)
        return nullptr;
    if (_pStringMgr == nullptr)
        return nullptr;
    if (_pDoubleMgr == nullptr)
        return nullptr;

    //�����������
    _pRootProperty = _pGroupMgr->addProperty(_name);
    //������������
    QString subName1 = _name+"Min";
    QString subName2 = _name+"Max";
    //��������������
    QString minNameX = subName1+".X";
    QString minNameY = subName1+".Y";

    QString maxNameX = subName2+".X";
    QString maxNameY = subName2+".Y";

    //������������
    QtProperty* subPty1 = _pStringMgr->addProperty(subName1);
    QtProperty* subPty2 = _pStringMgr->addProperty(subName2);
    _subProperty = {subPty1, subPty2};
    //��������������
    QtProperty* minXPty = _pDoubleMgr->addProperty(minNameX);
    QtProperty* minYPty = _pDoubleMgr->addProperty(minNameY);

    QtProperty* maxXPty = _pDoubleMgr->addProperty(maxNameX);
    QtProperty* maxYPty = _pDoubleMgr->addProperty(maxNameY);

    _subSubProperty = {minXPty, minYPty,
                       maxXPty, maxYPty};

    //��Ӹ��ӹ�ϵ
    _pRootProperty->addSubProperty(subPty1);
    _pRootProperty->addSubProperty(subPty2);

    subPty1->addSubProperty(minXPty);
    subPty1->addSubProperty(minYPty);

    subPty2->addSubProperty(maxXPty);
    subPty2->addSubProperty(maxYPty);

    return _pRootProperty;
}

bool ComplicateAabb2Type::updateSubProperty(int index)
{
    //��ȡ�޸ĵ�ֵ
    QString valueStr = _pStringMgr->value(_subProperty.at(index));
    if (!isStringValid(valueStr,2))
        return false;
    QStringList valueList = valueStr.split(" ");
    //�������������Ŀ�ʼ�ͽ�������
    int indexBegin = index * 2;
    int indexEnd   = (index+1) * 2;
    //ѭ����ʱ����
    int temp = 0;
    //ѭ��������������������ʾֵ
    for (size_t i = indexBegin; i < indexEnd; ++i)
    {
        QtProperty* subSubProperty = _subSubProperty.at(i);
        double subSubValue = valueList.at(temp).toDouble();
        _pDoubleMgr->setValue(subSubProperty, subSubValue);
        temp += 1;
    }
    return true;
}

bool ComplicateAabb2Type::updateParentProperty(int index)
{
    //���㸸����������ֵ
    int parentIndex = index / 2;
    if (parentIndex < 0 && parentIndex > 1)
        return false;
    QtProperty* parentPty = _subProperty.at(parentIndex);
    QtProperty* currentPty = _subSubProperty.at(index);
    if (parentPty == nullptr)
        return false;
    //�Ƿ�Ϸ�
    QString parentValue = _pStringMgr->value(parentPty);
    if (!isStringValid(parentValue,2))
        return false;
    QStringList valueList = parentValue.split(" ");
    //�����޸ĵ������������ڸ�������ֵ������
    int valueIndex = index % 2;
    if (valueIndex < 0 && valueIndex > 1)
        return false;
    //��ȡ�޸ĵ�ֵ
    double currentValue = _pDoubleMgr->value(currentPty);
    QString currentStrValue = QString::number(currentValue, 'f', 2);
    //Ӧ���޸ĵ�����������
    valueList.replace(valueIndex,currentStrValue);
    _pStringMgr->setValue(parentPty, valueList.join(" "));
    return true;
}

//����Vector��Euler����
ComplicateEulerType::ComplicateEulerType(ConfigPropertyWidget & ownWidget
    , QString & name
    , WD::WDPropertyDataType type
    , QtStringPropertyManager * pStringMgr
    , QtDoublePropertyManager * pDoubleMgr
    , QtEnumPropertyManager * pEnumMgr
    , std::string & valueStr)
    : ComplicateBaseType(ownWidget, name, type)
    , _pStringMgr(pStringMgr)
    , _pDoubleMgr(pDoubleMgr)
    , _pEnumMgr(pEnumMgr)
    , _valueStr(valueStr)
{
    _pSubProperty.fill(nullptr);
    _orderIndex = -1;
    //orderö��ֵ
    _orderStrList << "XYZ" << "YXZ" << "ZXY" << "ZYX" << "YZX" << "XZY";
    _pRootProperty = this->initCreate();
    this->updateValue();
}

ComplicateEulerType::~ComplicateEulerType()
{
}

bool ComplicateEulerType::contains(QtProperty * pPty) const
{
    if (pPty == _pRootProperty)
        return true;
    for (auto var : _pSubProperty)
    {
        if (pPty == var)
            return true;
    }
    return false;
}

bool ComplicateEulerType::updateValue()
{
    if (_pRootProperty == nullptr)
        return false;
    if (_pDoubleMgr == nullptr)
        return false;
    if (_pStringMgr == nullptr)
        return false;
    if (_pEnumMgr == nullptr)
        return false;
    QString rootValue;
    
    switch (_type)
    {
    case WD::PDT_FEuler:
    {   
        WD::FEuler value;
        value.fromString(_valueStr);

        std::string tStr = value.toString();
        //������Ҫ��ʼ����ֵ
        
        float subValue1 = value.x;
        float subValue2 = value.y;
        float subValue3 = value.z;
        int   enumOrder = value.order;
        _orderIndex = enumOrder;
        //����ֵ
        _pDoubleMgr->setValue(_pSubProperty.at(0),subValue1);
        _pDoubleMgr->setValue(_pSubProperty.at(1),subValue2);
        _pDoubleMgr->setValue(_pSubProperty.at(2),subValue3);
        _pEnumMgr->setEnumNames(_pSubProperty.at(3),_orderStrList);
        _orderIndex = value.order;
        _pEnumMgr->setValue(_pSubProperty.at(3),_orderIndex);
        QString oderString = _orderStrList.at(_orderIndex);
        rootValue = QString::number(subValue1, 'f', 2) 
            + " " + QString::number(subValue2, 'f', 2) 
            + " " + QString::number(subValue3, 'f', 2) 
            + " " + oderString;
        }
        break;
    case WD::PDT_DEuler:
    {   
        WD::FEuler value;
        value.fromString(_valueStr);
        std::string tStr = value.toString();
        //������Ҫ��ʼ����ֵ
        rootValue = QString::fromUtf8(tStr.data());
        double subValue1 = value.x;
        double subValue2 = value.y;
        double subValue3 = value.z;
        int   enumOrder = value.order;
        _orderIndex = enumOrder;
        //����ֵ
        _pDoubleMgr->setValue(_pSubProperty.at(0),subValue1);
        _pDoubleMgr->setValue(_pSubProperty.at(1),subValue2);
        _pDoubleMgr->setValue(_pSubProperty.at(2),subValue3);
        _pEnumMgr->setEnumNames(_pSubProperty.at(3),_orderStrList);
        _orderIndex = value.order;
        _pEnumMgr->setValue(_pSubProperty.at(3),_orderIndex);
        QString oderString = _orderStrList.at(_orderIndex);
        rootValue = QString::number(subValue1, 'f', 2) 
            + " " + QString::number(subValue2, 'f', 2) 
            + " " + QString::number(subValue3, 'f', 2) 
            + " " + oderString;
    }
        break;
    default:
        return false;
        break;
    }
    _pStringMgr->setValue(_pRootProperty, rootValue);
    return true;
}

QtProperty * ComplicateEulerType::getRootProperty()
{
    return _pRootProperty;
}

std::string ComplicateEulerType::getValue() 
{
    if (_pRootProperty == nullptr)
        return "";
    if (_pDoubleMgr == nullptr)
        return "";
    if (_pStringMgr == nullptr)
        return "";
    if (_pEnumMgr == nullptr)
        return "";
    //��������ʾֵ
    QString tRootValue = _pStringMgr->value(_pRootProperty);

    //�и����ʾֵ
    QStringList subValueList = tRootValue.split(" ");
    if (subValueList.size() != 4)
        return "";
    //�ַ���תö��
    QString order = subValueList.back();
    int orderIndex = -1;
    if (order == QString("XYZ"))
    {
        orderIndex = 0;
    }
    if (order == QString("YXZ"))
    {
        orderIndex = 1;
    }
    if (order == QString("ZXY"))
    {
        orderIndex = 2;
    }
    if (order == QString("ZYX"))
    {
        orderIndex = 3;
    }
    if (order == QString("YZX"))
    {
        orderIndex = 4;
    }
    if (order == QString("XZY"))
    {
        orderIndex = 5;
    }
    if (orderIndex == -1)
    {
        return "";
    }
    //ƴ��ֵ
    subValueList.replace(3,QString::number(orderIndex));
    std::string strValue = subValueList.join(" ").toUtf8().data();
    return strValue;
}

bool ComplicateEulerType::updateShowP(QtProperty * pPty)
{
    if (pPty == nullptr)
        return false;
    
    if (_pStringMgr == nullptr || _pDoubleMgr == nullptr || _pEnumMgr == nullptr)
        return false;

    if (_pRootProperty == nullptr
        || _pSubProperty.size() != 4)
        return false;


    //���ͱ�ʶ���������������͸�������
    int type = 0;
    //����ö�ٺ�double�ı�ʶ
    int index = -1;
    if (pPty == _pRootProperty)
    {
        type = 1;
    }

    if(type == 0)
    {
        for (int i = 0; i < _pSubProperty.size(); ++i)
        {
            if (_pSubProperty.at(i) == pPty)
            {
                type = 2;
                index = i;
                break;
            }
        }
    }
    switch (type)
    {
    case 1:
        {
            QString rootValue = _pStringMgr->value(_pRootProperty);
            if (! isStringValid(rootValue,4))
                return false;
            //����������ʾֵ
            this->updateSubProperty(rootValue);
        }break;
    case 2:
        {
            //���¸�����ʾ��ֵ
            this->updateRootProperty(index);
            
        }break;
    default:
        return false;
        break;
    }
    return true;
}

QtProperty * ComplicateEulerType::initCreate()
{
    if (_pStringMgr == nullptr)
        return nullptr;
    if (_pDoubleMgr == nullptr)
        return nullptr;
    if (_pEnumMgr == nullptr)
        return nullptr;
    //����������
    QtProperty* p = nullptr;
    QtProperty* subPty1 = nullptr;
    QtProperty* subPty2 = nullptr;
    QtProperty* subPty3 = nullptr;
    QtProperty* subPty4 = nullptr;

    //��������
    QString subPtyName1 = _name + ".X";
    QString subPtyName2 = _name + ".Y";
    QString subPtyName3 = _name + ".Z";
    QString subPtyName4 = _name + ".Order";

    //������������
    p = _pStringMgr ->addProperty(_name);
    //������������
    subPty1 = _pDoubleMgr->addProperty(subPtyName1);
    subPty2 = _pDoubleMgr->addProperty(subPtyName2);
    subPty3 = _pDoubleMgr->addProperty(subPtyName3);
    subPty4 = _pEnumMgr->addProperty(subPtyName4);
    //��Ӳ㼶��ϵ
    p->addSubProperty(subPty1);
    p->addSubProperty(subPty2);
    p->addSubProperty(subPty3);
    p->addSubProperty(subPty4);

    //����������������붨������
    _pSubProperty = {subPty1, subPty2, subPty3, subPty4};
    return p;
}

bool ComplicateEulerType::updateSubProperty(QString rootValue)
{
    QStringList valueList = rootValue.split(" ");
    //��������
    QtProperty* sub1 = _pSubProperty.at(0);
    QtProperty* sub2 = _pSubProperty.at(1);
    QtProperty* sub3 = _pSubProperty.at(2);
    //����������Ҫ���µ�ֵ
    QString subValue1 = valueList.at(0);
    QString subValue2 = valueList.at(1);
    QString subValue3 = valueList.at(2);

    _pDoubleMgr->setValue(sub1,subValue1.toDouble());
    _pDoubleMgr->setValue(sub2,subValue2.toDouble());
    _pDoubleMgr->setValue(sub3,subValue3.toDouble());
    //order���޸�
    QString tOrderStr = _orderStrList[_orderIndex];
    valueList.replace(3, tOrderStr);
    _pStringMgr->setValue(_pRootProperty,valueList.join(" "));

    return true;
}

bool ComplicateEulerType::updateRootProperty(int index)
{
    //order���޸�
    if (index == 3)
    {
        int orderIdx = _pEnumMgr->value(_pSubProperty.at(index));
        //�����޸ĵ�orderIndex
        _orderIndex = orderIdx;
        QString orderStr = _orderStrList[orderIdx];
        //�޸ĸ�������
        QString rootValue = _pStringMgr->value(_pRootProperty);
        QStringList rootValueList = rootValue.split(" ");
        rootValueList.replace(3,orderStr);
        _pStringMgr->setValue(_pRootProperty, rootValueList.join(" "));
        return true;
    }
    else if (index >= 0 && index < 3)
    {
        double value = _pDoubleMgr->value(_pSubProperty.at(index));
        //�޸ĸ�������
        QString rootValue = _pStringMgr->value(_pRootProperty);
        QStringList rootValueList = rootValue.split(" ");
        rootValueList.replace(index,QString::number(value, 'f', 2));
        _pStringMgr->setValue(_pRootProperty, rootValueList.join(" "));
        return true;
    }
    return false;
}
