#include "ConfigPropertyEuler.h"

EulerType::EulerType(ConfigPropertyWidget& ownWidget
    , QtStringPropertyManager* pStringMgr
    , QtDoublePropertyManager* pDoubleMgr
    , QtEnumPropertyManager* pEnumMgr
    , WD::WDConfig::Item* pPtr)
    : PropertyBaseType(ownWidget, pPtr)
    , _pStringMgr(pStringMgr)
    , _pDoubleMgr(pDoubleMgr)
    , _pEnumMgr(pEnumMgr)
{
    _pSubProperty.fill(nullptr);
    _orderIndex = -1;
    //orderö��ֵ
    _orderStrList << "XYZ" << "YXZ" << "ZXY" << "ZYX" << "YZX" << "XZY";
    _pRootProperty = this->initCreate();
}

EulerType::~EulerType()
{
}

bool EulerType::updateValueFromWDPty()
{
    if (_pPty == nullptr)
        return false;
    if (_pRootProperty == nullptr)
        return false;
    if (_pDoubleMgr == nullptr)
        return false;
    if (_pStringMgr == nullptr)
        return false;
    if (_pEnumMgr == nullptr)
        return false;
    QString rootValue;
    

    WD::WDPropertyDataType type = _pPty->type();
    switch (type)
    {
    case WD::PDT_FEuler:
    {   
        WD::WDPropertyFEuler* pPty = WD::WDProperty::As<WD::WDPropertyFEuler>(_pPty.get());
        if (pPty == nullptr)
            return false;
        auto value = pPty->getValue();
        std::string tStr = value.toString();
        //������Ҫ��ʼ����ֵ
        
        float subValue1 = value.x;
        float subValue2 = value.y;
        float subValue3 = value.z;
        int   enumOrder = value.order;
        _orderIndex = enumOrder;
        //����ֵ
        _pDoubleMgr->setValue(_pSubProperty.at(0),subValue1);
        _pDoubleMgr->setValue(_pSubProperty.at(1),subValue2);
        _pDoubleMgr->setValue(_pSubProperty.at(2),subValue3);
        _pEnumMgr->setEnumNames(_pSubProperty.at(3),_orderStrList);
        _orderIndex = value.order;
        _pEnumMgr->setValue(_pSubProperty.at(3),_orderIndex);
        QString oderString = _orderStrList.at(_orderIndex);
        rootValue = QString::number(subValue1, 'f', 2) 
            + " " + QString::number(subValue2, 'f', 2) 
            + " " + QString::number(subValue3, 'f', 2) 
            + " " + oderString;
        }
        break;
    case WD::PDT_DEuler:
    {   
        WD::WDPropertyDEuler* pPty = WD::WDProperty::As<WD::WDPropertyDEuler>(_pPty.get());
        if (pPty == nullptr)
            return false;
        auto value = pPty->getValue();
        std::string tStr = value.toString();
        //������Ҫ��ʼ����ֵ
        rootValue = QString::fromUtf8(tStr.data());
        double subValue1 = value.x;
        double subValue2 = value.y;
        double subValue3 = value.z;
        int   enumOrder = value.order;
        _orderIndex = enumOrder;
        //����ֵ
        _pDoubleMgr->setValue(_pSubProperty.at(0),subValue1);
        _pDoubleMgr->setValue(_pSubProperty.at(1),subValue2);
        _pDoubleMgr->setValue(_pSubProperty.at(2),subValue3);
        _pEnumMgr->setEnumNames(_pSubProperty.at(3),_orderStrList);
        _orderIndex = value.order;
        _pEnumMgr->setValue(_pSubProperty.at(3),_orderIndex);
        QString oderString = _orderStrList.at(_orderIndex);
        rootValue = QString::number(subValue1, 'f', 2) 
            + " " + QString::number(subValue2, 'f', 2) 
            + " " + QString::number(subValue3, 'f', 2) 
            + " " + oderString;
    }
        break;
    default:
        return false;
        break;
    }
    _pStringMgr->setValue(_pRootProperty, rootValue);
    return true;
}

bool EulerType::applyValue() 
{
    if (_pRootProperty == nullptr)
        return false;
    if (_pDoubleMgr == nullptr)
        return false;
    if (_pStringMgr == nullptr)
        return false;
    if (_pEnumMgr == nullptr)
        return false;
    //��ȡֵ
    double value1 = _pDoubleMgr->value(_pSubProperty[0]);
    double value2 = _pDoubleMgr->value(_pSubProperty[1]);
    double value3 = _pDoubleMgr->value(_pSubProperty[2]);
    WD::FEuler::Order value4 = (WD::FEuler::Order)_pEnumMgr->value(_pSubProperty[3]);
    WD::DEuler::Order value5 = (WD::DEuler::Order)_pEnumMgr->value(_pSubProperty[3]);
    //��������ʾֵ
    QString tRootValue = _pStringMgr->value(_pRootProperty);

    WD::WDPropertyDataType type = _pPty->type();
    switch (type)
    {
    case WD::PDT_FEuler:
        {
            WD::WDPropertyFEuler* p = WD::WDProperty::As<WD::WDPropertyFEuler>(_pPty.get());
            WD::FEuler euler(value1, value2, value3, value4);
            p->setValue(euler);
        }
        break;
    case WD::PDT_DEuler:
        {
            WD::WDPropertyDEuler* p = WD::WDProperty::As<WD::WDPropertyDEuler>(_pPty.get());
            WD::DEuler euler(value1, value2, value3, value5);
            p->setValue(euler);
        }
        break;
    default:
        return false;
        break;
    }
    return true;
}

bool EulerType::contains(QtProperty * pPty) const
{
    if (pPty == _pRootProperty)
        return true;
    for (auto var : _pSubProperty)
    {
        if (pPty == var)
            return true;
    }
    return false;
}

QtProperty * EulerType::getRootProperty()
{
    return _pRootProperty;
}

bool EulerType::updateShowP(QtProperty * pPty)
{
    if (pPty == nullptr)
        return false;
    
    if (_pStringMgr == nullptr || _pDoubleMgr == nullptr || _pEnumMgr == nullptr)
        return false;

    if (_pRootProperty == nullptr
        || _pSubProperty.size() != 4)
        return false;


    //���ͱ�ʶ���������������͸�������
    int type = 0;
    //����ö�ٺ�double�ı�ʶ
    int index = -1;
    if (pPty == _pRootProperty)
    {
        type = 1;
    }

    if(type == 0)
    {
        for (int i = 0; i < _pSubProperty.size(); ++i)
        {
            if (_pSubProperty.at(i) == pPty)
            {
                type = 2;
                index = i;
                break;
            }
        }
    }
    switch (type)
    {
    case 1:
        {
            QString rootValue = _pStringMgr->value(_pRootProperty);
            if (! isStringValid(rootValue,4))
                return false;
            //����������ʾֵ
            this->updateSubProperty(rootValue);
        }break;
    case 2:
        {
            //���¸�����ʾ��ֵ
            this->updateRootProperty(index);
            
        }break;
    default:
        return false;
        break;
    }
    return true;
}


QtProperty * EulerType::initCreate()
{
    if (_pStringMgr == nullptr)
        return nullptr;
    if (_pDoubleMgr == nullptr)
        return nullptr;
    if (_pEnumMgr == nullptr)
        return nullptr;
    //����������
    QtProperty* p = nullptr;
    QtProperty* subPty1 = nullptr;
    QtProperty* subPty2 = nullptr;
    QtProperty* subPty3 = nullptr;
    QtProperty* subPty4 = nullptr;

    //�����������ʻ�WDCtxTs
    std::string ptyName = _pPty->name();
    QString tPtyName = QString::fromUtf8(ptyName.c_str());
    //��������
    QString subPtyName1 = tPtyName + ".X";
    QString subPtyName2 = tPtyName + ".Y";
    QString subPtyName3 = tPtyName + ".Z";
    QString subPtyName4 = tPtyName + ".Order";

    //������������
    p = _pStringMgr ->addProperty(tPtyName);
    //������������
    subPty1 = _pDoubleMgr->addProperty(subPtyName1);
    subPty2 = _pDoubleMgr->addProperty(subPtyName2);
    subPty3 = _pDoubleMgr->addProperty(subPtyName3);
    subPty4 = _pEnumMgr->addProperty(subPtyName4);
    //��Ӳ㼶��ϵ
    p->addSubProperty(subPty1);
    p->addSubProperty(subPty2);
    p->addSubProperty(subPty3);
    p->addSubProperty(subPty4);

    //����������������붨������
    _pSubProperty = {subPty1, subPty2, subPty3, subPty4};
    return p;
}

bool EulerType::updateSubProperty(QString rootValue)
{
    QStringList valueList = rootValue.split(" ");
    //��������
    QtProperty* sub1 = _pSubProperty.at(0);
    QtProperty* sub2 = _pSubProperty.at(1);
    QtProperty* sub3 = _pSubProperty.at(2);
    //����������Ҫ���µ�ֵ
    QString subValue1 = valueList.at(0);
    QString subValue2 = valueList.at(1);
    QString subValue3 = valueList.at(2);

    _pDoubleMgr->setValue(sub1,subValue1.toDouble());
    _pDoubleMgr->setValue(sub2,subValue2.toDouble());
    _pDoubleMgr->setValue(sub3,subValue3.toDouble());
    //order���޸�
    QString tOrderStr = _orderStrList[_orderIndex];
    valueList.replace(3, tOrderStr);
    _pStringMgr->setValue(_pRootProperty,valueList.join(" "));

    return true;
}

bool EulerType::updateRootProperty(int index)
{
    //order���޸�
    if (index == 3)
    {
        int orderIdx = _pEnumMgr->value(_pSubProperty.at(index));
        //�����޸ĵ�orderIndex
        _orderIndex = orderIdx;
        QString orderStr = _orderStrList[orderIdx];
        //�޸ĸ�������
        QString rootValue = _pStringMgr->value(_pRootProperty);
        QStringList rootValueList = rootValue.split(" ");
        rootValueList.replace(3,orderStr);
        _pStringMgr->setValue(_pRootProperty, rootValueList.join(" "));
        return true;
    }
    else if (index >= 0 && index < 3)
    {
        double value = _pDoubleMgr->value(_pSubProperty.at(index));
        //�޸ĸ�������
        QString rootValue = _pStringMgr->value(_pRootProperty);
        QStringList rootValueList = rootValue.split(" ");
        rootValueList.replace(index,QString::number(value, 'f', 2));
        _pStringMgr->setValue(_pRootProperty, rootValueList.join(" "));
        return true;
    }
    return false;
}
