<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>UiCreateCATE</class>
 <widget class="QDialog" name="UiCreateCATE">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>780</width>
    <height>700</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>UiCreateCATE</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="2" column="0">
    <widget class="QGroupBox" name="groupBox_2">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>200</height>
      </size>
     </property>
     <property name="title">
      <string>ComponentObjectList</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_7" stretch="1">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayoutAtomObjects"/>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayoutVertexEdit"/>
      </item>
     </layout>
    </widget>
   </item>
   <item row="1" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="0,0">
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_7" stretch="0,0">
       <item>
        <widget class="QGroupBox" name="groupBox_4">
         <property name="title">
          <string>ModelDataReference</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_5">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <layout class="QVBoxLayout" name="verticalLayoutDataRef"/>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="groupBox_5">
         <property name="title">
          <string>ModelData</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_6">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <layout class="QVBoxLayout" name="verticalLayoutDataList"/>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <widget class="QGroupBox" name="groupBox_6">
       <property name="title">
        <string>DataProperty</string>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_8">
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <layout class="QVBoxLayout" name="verticalLayoutProperty"/>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
   <item row="0" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QGroupBox" name="groupBox_3">
       <property name="title">
        <string>CurrAtomProperty</string>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_9">
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_4"/>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QGroupBox" name="groupBox">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>200</height>
        </size>
       </property>
       <property name="title">
        <string>AtomParameter</string>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_6" stretch="0">
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayoutCATEParams"/>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
