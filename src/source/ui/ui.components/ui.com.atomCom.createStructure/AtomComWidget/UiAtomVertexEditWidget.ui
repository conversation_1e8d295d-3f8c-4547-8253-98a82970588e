<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>UiAtomVertexEditWidget</class>
 <widget class="QWidget" name="UiAtomVertexEditWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1009</width>
    <height>457</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>UiAtomObjects</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <widget class="QTableWidget" name="tableWidget">
     <property name="alternatingRowColors">
      <bool>false</bool>
     </property>
     <property name="selectionMode">
      <enum>QAbstractItemView::ExtendedSelection</enum>
     </property>
     <property name="selectionBehavior">
      <enum>QAbstractItemView::SelectRows</enum>
     </property>
    </widget>
   </item>
   <item row="0" column="1">
    <layout class="QVBoxLayout" name="verticalLayout">
     <item>
      <spacer name="verticalSpacer">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>40</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonAdd">
       <property name="text">
        <string>Add</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonCopy">
       <property name="text">
        <string>Copy</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonDelete">
       <property name="text">
        <string>Delete</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonImport">
       <property name="text">
        <string>Import</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonExport">
       <property name="text">
        <string>Export</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonOk">
       <property name="text">
        <string>Ok</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="verticalSpacer_2">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>40</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
