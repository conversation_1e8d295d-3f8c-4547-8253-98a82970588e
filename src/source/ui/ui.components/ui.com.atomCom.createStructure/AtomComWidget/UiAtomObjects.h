#pragma once

#include <QWidget>
#include "ui_UiAtomObjects.h"
#include "core/WDCore.h"
#include "core/node/WDNode.h"
#include "../../ui.commonLibrary/ui.commonLib.excel/QTableWidget2Excel.h"


using ExcelData = QTableWidget2Excel::ExcelData;
class UiAtomObjects : public QWidget
{
    Q_OBJECT
public:
    UiAtomObjects(WD::WDBMCatalog& cataMgr, QWidget *parent = Q_NULLPTR);
    ~UiAtomObjects();
signals:
    /**
    * @brief ���ݸı��ź�
    */
    void sigDataChanged();
    /**
    * @brief ��ǰԪ������ı��ź�
    */
    void sigCurrentAtomObjectChanged(WD::WDNode::SharedPtr& pObjNode);
    /**
    * @brief Ԫ���������ݱ����ź�
    */
    void sigImportObjects();
    /**
    * @brief Ԫ���������ݱ����ź�
    */
    void sigExportObjects();
public:
    /**
    * @brief ָ��Ŀ¼�Լ�Ԫ���ڵ���´���
    */
    void updateWidget(WD::WDNode::SharedPtr pCateNode, WD::WDNode::SharedPtr pObjNode);
public slots:
    void slotAddClicked();
    void slotDeleteClicked();
    void slotAppClicked();
    void slotImportClicked();
    void slotExportClicked();
    void slotTableWidgetCurrentItemChanged(QTableWidgetItem *, QTableWidgetItem *);

    bool updateTableWidgetByExcel(std::string& filePath);

    bool exportExcelByTableWidget(std::string& filePath);
private:
    //ʹ��Ԫ�����������
    bool insertRow(const WD::WDNode::SharedPtr pSTCANode, const WD::WDNode::SharedPtr pObjNode, int currentRow);
    //ɾ������
    void deleteSingleRow(int row);
    // ɾ������
    void deleteRows(std::vector<size_t>& rows);
    //����Excel����ʱ��Ӻ��޸�SPRF�ڵ�
    bool addAndModifyScomNodeByExcel(const ExcelData& excelData);
    //����Excel����ʱɾ��SPRF�ڵ�
    bool deleteScomNodeByExcel(const ExcelData& excelData);
private:
    Ui::UiAtomObjects   ui;
    WD::WDBMCatalog& _cataMgr;
    WD::WDNode::WeakPtr _pSTCANode;
    WD::WDNode::WeakPtr _pObjNode;
};
