#include "UiObjectProp.h"
#include "core/WDTranslate.h"

UiObjectProp::UiObjectProp(QWidget *parent)
    : QWidget(parent)
{
    ui.setupUi(this);

    _pGroup = nullptr;
    ui.pushButton->setText(QString::fromUtf8(WD::WDTs("ui_com_atomCom_createAtom", "Apply").c_str()));
	_objectPropertyWidget =   new ObjectPropertyWidget(false);
    
    // ���¼�֪ͨ��Ӧ
    connect(ui.pushButton,    SIGNAL(clicked()), SLOT(slotOnApp()));
}
UiObjectProp::~UiObjectProp()
{
    ui.verticalLayout->removeWidget(_objectPropertyWidget->widget());
	_objectPropertyWidget->deleteLater();
}

void UiObjectProp::updateWidget(WD::WDPropertyGroup::SharedPtr pGroup)
{
    // ���ԭ���Դ���
    if (pGroup == nullptr)
        return;

	ui.verticalLayout->removeWidget(_objectPropertyWidget->widget());
	_objectPropertyWidget->update(*pGroup);
	ui.verticalLayout->addWidget(_objectPropertyWidget->widget());
}

void    UiObjectProp::slotOnApp()
{
    //Ӧ�ø���
	_objectPropertyWidget->applyChanges();
    emit sigDataChanged();
}