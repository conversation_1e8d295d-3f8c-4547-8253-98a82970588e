#include "UiComSceneSnapshoot.h"
#include "core/common/WDConfig.h"
#include "core/viewer/WDViewer.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/nodeTree/WDNodeTree.h"

// 快照数据文件名称
static constexpr const char* Snapshoot_Data_File_Name = "snopshoot.xml";

UiComSceneSnapshoot::UiComSceneSnapshoot(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject *parent)
    : QObject(parent)
    , IUiComponent(mainWindow, attrs)
    , _core(mainWindow.core())
{
}
UiComSceneSnapshoot::~UiComSceneSnapshoot()
{
}

void UiComSceneSnapshoot::onNotice(UiNotice * pNotice)
{
    int nType = pNotice->type();
    switch (nType)
    {
    case UiNoticeType::UNT_MainWindowShowed:
        {
            // 加载场景根节点和相机视图
            if (!this->loadSceneSnapshoot())
            {
		        // 相机定位到根节点
                auto& nodeTree = _core.nodeTree();
			    WD::DAabb3 rootAabb = WD::DAabb3::Null();
			    for (size_t i = 0; i < nodeTree.topLevelNodeCount(); ++i)
			    {
				    auto pNode = nodeTree.topLevelNode(i);
				    if (pNode == nullptr)
				    {
				        assert(false && "无效根节点!");
				        continue;
				    }
				    rootAabb.unions(pNode->aabb());
			    }
                if (rootAabb.isNull())
                    rootAabb = WD::DAabb3(WD::DVec3(-100.0), WD::DVec3(100.0));
                _core.viewer().lookAtAabb(rootAabb, WD::WDViewer::LAD_FrontUpLeft);
            }
        }
        break;
    case UiNoticeType::UNT_ReadyUnload:
        {
            // 保存场景根节点和相机视图
            this->saveSceneSnapshoot();
        }
        break;
    default:
        break;
    }
}

bool UiComSceneSnapshoot::loadSceneSnapshoot()
{
    // 配置项决定是否加载场景视图
    if (!this->enableSnapshoot())
        return false;

    // 获取XML根节点
    WD::WDFileReader file(std::string(this->path().toUtf8().data() + std::string("/") + Snapshoot_Data_File_Name));
    if (file.isBad())
    {
        return false;
    }
    file.readAll();
    size_t length = file.length();
    if (length == 0)
        return false;
    try
    {
        WD::XMLDoc      doc;
        doc.parse<0>((char*)file.data());
        WD::XMLNode*    pXmlRoot    =   doc.first_node("root");
        if (pXmlRoot == nullptr)
            return false;

        std::string name    =   pXmlRoot->name();
        std::string value   =   pXmlRoot->value();
        
        // 读取根节点
        this->readXml(doc, pXmlRoot);
    }
    catch (...)
    {
        return false;
    }

    file.close();
    return true;
}
void UiComSceneSnapshoot::saveSceneSnapshoot()
{
    // 配置项决定是否加载场景视图
    if (!this->enableSnapshoot())
        return ;

    // 打开文件
    FILE* pFile = fopen(std::string(this->path().toUtf8().data() + std::string("/") + Snapshoot_Data_File_Name).c_str(), "wb");
    if (pFile == nullptr)
        return ;

    // 准备根节点
    WD::XMLDoc doc;
    WD::XMLNode* pXmlInfo  =   doc.allocate_node(rapidxml::node_pi, "xml version='1.0' encoding='utf-8'");
    WD::XMLNode* pXmlRoot  =   doc.allocate_node(rapidxml::node_element, "root");
    doc.append_node(pXmlInfo);
    doc.append_node(pXmlRoot);

    // 写入根节点
    this->writeXml(doc, pXmlRoot);

    std::string strXML;
    rapidxml::print(std::back_inserter(strXML), doc);
    // 写入文件
    fwrite(strXML.c_str(), strXML.size(), 1, pFile);
    fclose(pFile);
}

bool UiComSceneSnapshoot::writeXml(WD::XMLDoc& doc, WD::XMLNode* pXmlRoot)
{
    if (pXmlRoot == nullptr)
        return false;

    // 写入场景锚点
    auto target = _core.viewer().browseTarget();
    auto pXmlTarget = doc.allocate_node(rapidxml::node_element, "Target", doc.allocate_string(ToString(target).c_str()));
    pXmlRoot->append_node(pXmlTarget);
    // 写ObjsRoot
    {
        auto pObsRoot = _core.scene().obsRoot();
        if (!pObsRoot.empty())
        {
            auto pXmlObsRoot = doc.allocate_node(rapidxml::node_element, "ObsRoot", 0);
            for (auto& pObsNode : pObsRoot)
            {
                if (pObsNode == nullptr)
                    continue;
                auto pXmlObjsNode = doc.allocate_node(rapidxml::node_element, "Node", doc.allocate_string(pObsNode->uuid().toString().c_str()));
                pXmlObsRoot->append_node(pXmlObjsNode);
            }
            pXmlRoot->append_node(pXmlObsRoot);
        }
    }

    // 写入相机状态
    auto    pCamera     =   _core.viewer().camera();
    auto    pOCamera    =   WD::WDOrthographicCamera::SharedCast(pCamera);
    if (pOCamera != nullptr)
    {
        auto pXmlCamera = doc.allocate_node(rapidxml::node_element, "Camera");
        pXmlCamera->append_node(
            doc.allocate_node(rapidxml::node_element, "Left", doc.allocate_string(WD::ToString(pOCamera->left()).c_str())));
        pXmlCamera->append_node(
            doc.allocate_node(rapidxml::node_element, "Right", doc.allocate_string(WD::ToString(pOCamera->right()).c_str())));
        pXmlCamera->append_node(
            doc.allocate_node(rapidxml::node_element, "Top", doc.allocate_string(WD::ToString(pOCamera->top()).c_str())));
        pXmlCamera->append_node(
            doc.allocate_node(rapidxml::node_element, "Bottom", doc.allocate_string(WD::ToString(pOCamera->bottom()).c_str())));
        pXmlCamera->append_node(
            doc.allocate_node(rapidxml::node_element, "ZNear", doc.allocate_string(WD::ToString(pOCamera->zNear()).c_str())));
        pXmlCamera->append_node(
            doc.allocate_node(rapidxml::node_element, "ZFar", doc.allocate_string(WD::ToString(pOCamera->zFar()).c_str())));
        pXmlCamera->append_node(
            doc.allocate_node(rapidxml::node_element, "Zoom", doc.allocate_string(WD::ToString(pOCamera->zoom()).c_str())));
        pXmlCamera->append_node(
            doc.allocate_node(rapidxml::node_element, "Eye", doc.allocate_string(WD::ToString(pOCamera->eye()).c_str())));
        pXmlCamera->append_node(
            doc.allocate_node(rapidxml::node_element, "Up", doc.allocate_string(WD::ToString(pOCamera->upDir()).c_str())));
        pXmlRoot->append_node(pXmlCamera);
    }

    return true;
}
bool UiComSceneSnapshoot::readXml(WD::XMLDoc& doc, WD::XMLNode* pXmlRoot)
{
    WDUnused(doc);
    if (pXmlRoot == nullptr)
        return false;
    auto& viewer = _core.viewer();
    
    // 读取场景锚点
    auto pXmlTarget = pXmlRoot->first_node("Target");
    WD::DVec3 target;
    if (pXmlTarget != nullptr)
    {
        bool success = false;
        target = WD::FromString<WD::DVec3>(pXmlTarget->value(), &success);
        if (success) 
            viewer.setBrowseTarget(target);
    }
    // 读取ObjsRoot
    auto pScene = viewer.scene();
    if (pScene != nullptr)
    {
        auto pXmlObsRoot = pXmlRoot->first_node("ObsRoot");
        if (pXmlObsRoot != nullptr)
        {
            std::vector<WD::WDUuid> uuids;
            auto pXmlObjsNode = pXmlObsRoot->first_node("Node");
            for (pXmlObjsNode; pXmlObjsNode != nullptr; pXmlObjsNode = pXmlObjsNode->next_sibling("Node"))
            {
                std::string uuidStr =   pXmlObjsNode->value();
                bool        bOk     =   false;
                WD::WDUuid  uuid    =   WD::FromString<WD::WDUuid>(uuidStr, &bOk);
                if (bOk)
                {
                    uuids.emplace_back(uuid);
                }
            }
            auto pNodes = _core.getBMDesign().findNodes(uuids);
            pScene->clear();
            for (auto& pNode : pNodes)
            {
                pScene->add(pNode);
            }
        }
    }

    // 读取相机状态
    auto    pCamera     =   viewer.camera();
    auto    pOCamera    =   WD::WDOrthographicCamera::SharedCast(pCamera);
    if (pOCamera != nullptr)
    {
        auto pXmlCamera = pXmlRoot->first_node("Camera");
        if (pXmlCamera != nullptr)
        {
            WD::real    left    =   0.0;
            WD::real    right   =   0.0;
            WD::real    top     =   0.0;
            WD::real    bottom  =   0.0;
            WD::real    zNear   =   0.0;
            WD::real    zFar    =   0.0;
            WD::real    zoom    =   0.0;
            WD::DVec3   eye     =   WD::DVec3::Zero();
            WD::DVec3   up      =   WD::DVec3::AxisZ();
            auto pXmlLeft = pXmlCamera->first_node("Left");
            if (pXmlLeft != nullptr)
            {
                bool success = false;
                auto tempLeft = WD::FromString<WD::real>(pXmlLeft->value(), &success);
                if (success) 
                    left = tempLeft;
            }
            auto pXmlRight = pXmlCamera->first_node("Right");
            if (pXmlRight != nullptr)
            {
                bool success = false;
                auto tempRight = WD::FromString<WD::real>(pXmlRight->value(), &success);
                if (success) 
                    right = tempRight;
            }
            auto pXmlTop = pXmlCamera->first_node("Top");
            if (pXmlTop != nullptr)
            {
                bool success = false;
                auto tempTop = WD::FromString<WD::real>(pXmlTop->value(), &success);
                if (success) 
                    top = tempTop;
            }
            auto pXmlBottom = pXmlCamera->first_node("Bottom");
            if (pXmlBottom != nullptr)
            {
                bool success = false;
                auto tempBottom = WD::FromString<WD::real>(pXmlBottom->value(), &success);
                if (success) 
                    bottom = tempBottom;
            }
            auto pXmlZNear = pXmlCamera->first_node("ZNear");
            if (pXmlZNear != nullptr)
            {
                bool success = false;
                auto tempZNear = WD::FromString<WD::real>(pXmlZNear->value(), &success);
                if (success) 
                    zNear = tempZNear;
            }
            auto pXmlZFar = pXmlCamera->first_node("ZFar");
            if (pXmlZFar != nullptr)
            {
                bool success = false;
                auto tempZFar = WD::FromString<WD::real>(pXmlZFar->value(), &success);
                if (success) 
                    zFar = tempZFar;
            }
            auto pXmlZoom = pXmlCamera->first_node("Zoom");
            if (pXmlZoom != nullptr)
            {
                bool success = false;
                auto tempZoom = WD::FromString<WD::real>(pXmlZoom->value(), &success);
                if (success) 
                    zoom = tempZoom;
            }
            auto pXmlEye = pXmlCamera->first_node("Eye");
            if (pXmlEye != nullptr)
            {
                bool success = false;
                auto tempEye = WD::FromString<WD::DVec3>(pXmlEye->value(), &success);
                if (success) 
                    eye = tempEye;
            }
            auto pXmlUp = pXmlCamera->first_node("Up");
            if (pXmlUp != nullptr)
            {
                bool success = false;
                auto tempUp = WD::FromString<WD::DVec3>(pXmlUp->value(), &success);
                if (success) 
                    up = tempUp;
            }

            pOCamera->setLeft(left);
            pOCamera->setRight(right);
            pOCamera->setTop(top);
            pOCamera->setBottom(bottom);
            pOCamera->setZNear(zNear);
            pOCamera->setZFar(zFar);
            pOCamera->setZoom(zoom);

            pOCamera->lookAt(eye, target, up);
            pOCamera->update();
        }
    }

    // 重绘
    _core.needRepaint();

    return true;
}

bool UiComSceneSnapshoot::enableSnapshoot()
{
    auto pCfgViewSnapshoot = _core.cfg().query("view.snapshoot");
    if (pCfgViewSnapshoot == nullptr)
        return false;
    auto pValue = pCfgViewSnapshoot->value<bool>();
    if (pValue == nullptr)
        return false;

    return *pValue;
}