#include "UiComAtomComCreateClassification.h"
#include "core/WDCore.h"
#include "core/nodeTree/WDNodeTree.h"

UiComAtomComCreateClassification::UiComAtomComCreateClassification(IMainWindow& mainWindow
    , const UiComponentAttributes& attrs, QObject *parent)
    : QObject(parent)
    , IUiComponent(mainWindow, attrs)
{
	_pBaseWidget = new CreateBaseWidget(mWindow().core(), mWindow().widget());
}

UiComAtomComCreateClassification::~UiComAtomComCreateClassification()
{
    if (_pBaseWidget != nullptr)
    {
        delete _pBaseWidget;
        _pBaseWidget = nullptr;
    }
}

void UiComAtomComCreateClassification::onNotice(UiNotice * pNotice)
{
    int nType = pNotice->type();
    switch (nType)
    {
    case UiNoticeType::UNT_Action:
    {
        UiActionNotice* pActionNotice = static_cast<UiActionNotice*>(pNotice);
        if (pActionNotice->action().is("action.atomCom.createClassification"))
        {
            this->createClassificationNode();
        }
    }
    break;
    default:
        break;
    }
}

void UiComAtomComCreateClassification::createClassificationNode()
{
    if (_pBaseWidget == nullptr)
        return;

    if (_pBaseWidget->update())
    {
        if(_pBaseWidget->isHidden())
            _pBaseWidget->show();
        else
            _pBaseWidget->activateWindow();
    }
}