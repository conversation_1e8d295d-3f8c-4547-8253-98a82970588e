#pragma once

#include <QDialog>
#include "ui_CreateBaseWidget.h"
#include "core/WDCore.h"
#include "core/node/WDNode.h"
#include "../../wizDesignerApp/UiInterface/UiInterface.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiNodeNameHelpter.h"

class CreateBaseWidget : public QDialog
{
    Q_OBJECT

public:
    CreateBaseWidget(WD::WDCore& core, QWidget *parent = Q_NULLPTR);
    ~CreateBaseWidget();
public:
    /**
    * @brief 准备等级创建界面
    * @param curNode 当前选中节点
    */
    bool update();
private slots:
    /**
    * @brief 类型选择通知响应
    * @param typeName 类型名称
    */
    void        slotOnComboBoxTypeCurrentIndexChanged(int index);
    void        slotOnCreate();

private:
    /**
     * @brief 根据节点获取指定类型可挂载的父节点
    */
    WD::WDNode::SharedPtr getParentNode(WD::WDNode::SharedPtr pNode, const std::string& type) const;
    /**
    * @brief 判断父节点中是否包含指定名称子节点
    * @param parent 父节点
    * @param name 子节点名称
    * @return 包含返回true，不包含返回false
    */
    bool nameContains(WD::WDNode::SharedPtr parent, const std::string& name);
private:
    Ui::CreateBaseWidget    ui;
    //
    WD::WDCore&             _core;
    //用途
    WD::StringVector        _purposes;
    // 创建节点父节点
    WD::WDNode::WeakPtr     _parent;
    //窗口高度
    int                     _mWindowHeight;
    //comboBox高度
    int                     _comboBoxHeight;
    // 节点名称助手
    UiNodeNameHelpter           _nameHelpter;
};
