<?xml version="1.0" encoding="utf-8"?>

<TS language="简体中文">
    <context>
		<name>CreateBaseWidget</name>
		<message>
			<source>title</source>
			<translation>创建等级</translation>
		</message>
		<message>
			<source>Type</source>
			<translation>类型</translation>
		</message>
		<message>
			<source>Name</source>
			<translation>名称</translation>
		</message>
		<message>
			<source>Purpose</source>
			<translation>用途</translation>
		</message>
		<message>
			<source>Answer</source>
			<translation>回答</translation>
		</message>
		<message>
			<source>Question</source>
			<translation>问题</translation>
		</message>
		<message>
			<source>CatRef</source>
			<translation>元件引用</translation>
		</message>
		<message>
			<source>Create</source>
			<translation>创建</translation>
		</message>
		<message>
			<source>unset</source>
			<translation>未设置</translation>
		</message>
		<message>
			<source>PIPE</source>
			<translation>管道</translation>
		</message>
		<message>
			<source>NOZZ</source>
			<translation>管嘴</translation>
		</message>
		<message>
			<source>INSU</source>
			<translation>保温</translation>
		</message>
		<message>
			<source>TRAC</source>
			<translation>伴热</translation>
		</message>
		<message>
			<source>CreateLevelBox</source>
			<translation>创建等级提示框</translation>
		</message>
		<message>
			<source>Radius</source>
			<translation>半径</translation>
		</message>
		<message>
			<source>SPCOCannotCreateChildNode</source>
			<translation>标准等级SPCO无法创建子节点!</translation>
		</message>
		<message>
	        <source>SCOMCannotConnectChildNode</source>
	        <translation>元件部件SCOM无法创建子节点!</translation>
        </message>
        <message>
	        <source>createFail</source>
	        <translation>创建失败!</translation>
        </message>
		<message>
			<source>sameNameExist</source>
			<translation>名称已存在，创建失败!</translation>
		</message>
		<message>
			<source>parent is invalid</source>
			<translation>挂载的父节点无效!</translation>
		</message>
		<message>
			<source>node creation failed</source>
			<translation>节点创建失败!</translation>
		</message>
	</context>
</TS> 