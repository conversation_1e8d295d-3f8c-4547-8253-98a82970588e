#pragma     once

#include    "../../wizDesignerApp/UiInterface/UiInterface.h"
#include    <QObject>
#include    "core/nodeTree/WDNodeTree.h"
#include    "core/message/WDMessage.h"
#include    "core/WDTranslate.h"
#include    "core/viewer/WDViewer.h"
#include    "core/extension/WDPluginFormat.h"
#include    "core/businessModule/design/WDBMDesign.h"
#include    "GeometryCleanDialog.h"

class UiComClean
    : public QObject
    , public IUiComponent
{
    Q_OBJECT
private:
    GeomCleanDialog* _geomCleanDialog;
public:
    UiComClean(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject* parent = nullptr)
        : QObject(parent)
        , IUiComponent(mainWindow, attrs)
    {
        _geomCleanDialog = new GeomCleanDialog(mWindow().core(), mWindow().widget());
    }
    ~UiComClean()
    {
    }
public:
    /**
    *   @brief ֪ͨ�¼�
    *   @param pNotice �¼�����
    */
    virtual void onNotice(UiNotice* pNotice) override
    {
        int     nType       =   pNotice->type();
        switch (nType)
        {
        case UiNoticeType::UNT_AllReady:
            {
            }
            break;
        case UiNoticeType::UNT_Action:
            {
                UiActionNotice* pActionNotice = static_cast<UiActionNotice*>(pNotice);
                if (pActionNotice->action().is("action.design.geometry.clean"))
                {
                    if (_geomCleanDialog->isHidden())
                        _geomCleanDialog->show();
                    else
                        _geomCleanDialog->activateWindow();
                }
            }
            break;
        default:
            break;
        }
    }
private:
};