#pragma once
#include "core/common/WDPlatform.hpp"
#include "WDPainter/WDStyle.h"

WD_NAMESPACE_BEGIN

// 表格的备注 (标题栏和注释栏)
struct TableRemkras
{
    // 字体
    std::optional<WDFontStyle> textStyle;
    // 内容
    std::string text;
    // 对齐方式
    WDAlign::HAlign textAlign;
    // 备注栏高度(默认为10.0)
    double height = 10.0;
    // 是否显示(不显示时不绘制备注栏)
    bool bVisible = false;
    // 水平偏移
    // 当对齐方式为靠左时,这是从表格的左侧向右的偏移,如果值为负会取绝对值
    // 当对齐方式为居中时,值为正时向右偏移，为负时向左偏移
    // 当对齐方式为靠右时,这是从表格的右侧向左的偏移,如果值为负会取绝对值
    double horizontalOffset = 0.0;
    TableRemkras(const WDAlign::HAlign& textAlign = WDAlign::HAlign::HA_Left, double horizontalOffset = 0.0)
        : textAlign(textAlign), horizontalOffset(horizontalOffset)
    {
    }
    void clear()
    {
        textStyle = std::nullopt;
        text.clear();
        height = 10.0;
        bVisible = false;
    }
    void resetFont()
    {
        textStyle = std::nullopt;
    }
};







WD_NAMESPACE_END
