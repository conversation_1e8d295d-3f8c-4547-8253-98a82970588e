#include    "ThrustAndMementJson.h"
#include    "WDRapidjson.h"
#include    "core/common/WDFileReader.hpp"

ThrustAndMementJson::ThrustAndMementJson(const QString& folderPath)
{
    std::string path = "/端点推力和力矩.json";
    auto str = folderPath + QString::fromUtf8(path.c_str());
    WD::WDFileReader fileReader(str.toLocal8Bit().data());
    if (fileReader.isBad())
        return ;
    fileReader.readAll();
    WD::JsonDoc doc;

    doc.Parse((char*)fileReader.data(), fileReader.length());
    if (doc.HasParseError())
        return; 
    for (auto& object : doc.GetArray())
    {
        QString numStr = "";
        if (object.HasMember("节点编号"))
        {
            numStr = object["节点编号"].GetString();
        }
        // 参数列表
        std::vector<TAMData> datas;
        if (object.HasMember("力和力矩"))
        {
            auto& valueParams = object["力和力矩"];
            assert(valueParams.IsArray());
            for (auto& valueParam : valueParams.GetArray())
            {
                TAMData data;
                if (valueParam.HasMember("CASE"))
                {
                    data._case = valueParam["CASE"].GetString();
                }
                if (valueParam.HasMember("X"))
                {
                    data._x = valueParam["X"].GetString();
                }
                if (valueParam.HasMember("Y"))
                {
                    data._y = valueParam["Y"].GetString();
                }
                if (valueParam.HasMember("Z"))
                {
                    data._z = valueParam["Z"].GetString();
                }
                if (valueParam.HasMember("MX"))
                {
                    data._mx = valueParam["MX"].GetString();
                }
                if (valueParam.HasMember("MY"))
                {
                    data._my = valueParam["MY"].GetString();
                }
                if (valueParam.HasMember("MZ"))
                {
                    data._mz = valueParam["MZ"].GetString();
                }
                datas.emplace_back(data);
            }
            _tamDataMap[numStr.toInt()] = datas;
            datas.clear();
        }
    }
}
