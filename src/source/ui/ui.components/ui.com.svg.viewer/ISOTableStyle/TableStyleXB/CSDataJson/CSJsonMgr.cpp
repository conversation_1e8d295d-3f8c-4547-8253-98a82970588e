#include    "CSJsonMgr.h"
#include    <QApplication>
#include    <QProcess>
#include    <iostream>

DependJsonMgr::DependJsonMgr(const QString& folderPath,const QString& excelPath)
{
    _pMatDataTable = std::make_shared<MatDataTable>(folderPath);
    _pConfiguration = std::make_shared<ConfigurationJson>(folderPath);
    _pDesignParameter = std::make_shared<DesignParameterJson>(folderPath);
    _pMaxStressTable = std::make_shared<MaxStressTableJson>(folderPath);
    _pThrustAndMement = std::make_shared<ThrustAndMementJson>(folderPath);
    _pNodeCoordinateJson = std::make_shared<NodeCoordinateJson>(folderPath);
    _pValnDataJson = std::make_shared<ValnDataJson>(folderPath);
    _pPipeDataJson = std::make_shared<PipeDataJson>(folderPath);
    _pNoteExcel = std::make_shared<NoteExcel>(excelPath);
}