#pragma once
#include <QString>
#include <regex>
#include <unordered_map>

class JsonPipeData
{
public:
    JsonPipeData(){}
	~JsonPipeData(){}

public:
    std::string _lineNumber;
    std::string _description;
    std::string _pipeSpec;
    std::string _material;
    std::string _class;
    std::string _size;
    std::string _specification;
    std::string _from;
    std::string _to;
    std::string _oPE_Pressure;
    std::string _oPE_Temperature;
    std::string _designPressure;
    std::string _designTemperature;
    std::string _testPressure;
    std::string _supplier;
    std::string _remarks;
    std::string _dn;
};

class PipeDataJson
{
public:
    PipeData<PERSON>son(const QString& folderPath);
    ~PipeDataJson() {

    }

public:
    std::string _prijectName;
    std::string _fileName;
    std::vector<JsonPipeData> _data;
};