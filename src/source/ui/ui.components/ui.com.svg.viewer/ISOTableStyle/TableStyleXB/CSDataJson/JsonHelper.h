#pragma once
#include <QString>
#include <regex>
#include <unordered_map>

class JsonHelper
{

public:
    <PERSON>sonHelper() {}
    ~JsonHelper() {}

public:
    /**
     * @brief 安全获取Json值，至少保证不会崩溃
     * @tparam T 获取的值类型
     * @param value jsonValue
     * @param name 名称
     * @param outValue 输出的值
     * @return 是否获取成功，如果 jsonValue不包含对应名称的值或者值类型不匹配，则返回false
     */
    template <typename T>
    static bool GetJsonValueSafely(const WD::JsonValue& value, const std::string_view& name, T& outValue)
    {
        const char* tName = name.data();
        if (!value.IsObject() || !value.HasMember(tName))
            return false;

        const auto& tValue = value[tName];
        if (tValue.IsNull())
            return false;

        if constexpr (std::is_same_v<T, bool>)
        {
            outValue = tValue.GetBool();
            return true;
        }
        else if constexpr (std::is_same_v<T, int>)
        {
            outValue = tValue.GetInt();
            return true;
        }
        else if constexpr (std::is_same_v<T, int64_t>)
        {
            outValue = tValue.GetInt64();
            return true;
        }
        else if constexpr (std::is_same_v<T, unsigned int>)
        {
            outValue = tValue.GetUint();
            return true;
        }
        else if constexpr (std::is_same_v<T, uint64_t>)
        {
            outValue = tValue.GetUint64();
            return true;
        }
        else if constexpr (std::is_same_v<T, float>)
        {
            outValue = tValue.GetFloat();
            return true;
        }
        else if constexpr (std::is_same_v<T, double>)
        {
            outValue = tValue.GetDouble();
            return true;
        }
        else if constexpr (std::is_same_v<T, std::string>)
        {
            outValue = tValue.GetString();
            return true;
        }
        else if constexpr (std::is_same_v<T, QString>)
        {
            outValue = QString::fromUtf8(tValue.GetString());
            return true;
        }
        // 暂不支持
        //else if constexpr (std::is_same_v<T, JsonValue>)
        //{
        //    outValue = tValue.GetObj();
        //    return true;
        //}
        //else if constexpr (std::is_same_v<T, ArrayValue>)
        //{
        //    outValue = tValue.GetArray();
        //    return true;
        //}
        else
        {
            return false;
        }
    }

    /**
     * @brief 安全获取Json的属性值，至少保证不会崩溃
     * @tparam T 获取的值类型
     * @param value json对象
     * @param key 属性名称
     * @return 结果, 如果获取失败，则返回默认值
     */
    template<typename T>
    static T GetJsonValueSafely(const WD::JsonValue& value, const std::string_view& name)
    {
        T ret;
        GetJsonValueSafely(value, name, ret);
        return ret;
    }
};