<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>BatchDrawIsoDialog</class>
 <widget class="QDialog" name="BatchDrawIsoDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>427</width>
    <height>383</height>
   </rect>
  </property>
  <property name="focusPolicy">
   <enum>Qt::StrongFocus</enum>
  </property>
  <property name="windowTitle">
   <string>一键出图</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,1,0,0,0">
     <item>
      <widget class="QLabel" name="labelPath">
       <property name="text">
        <string>OutputPath</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEditPath"/>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonSelectPath">
       <property name="text">
        <string>...</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="summaryCheckBox">
       <property name="text">
        <string>Summary</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="outputPaperSize"/>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox">
     <property name="title">
      <string>InputList</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="1,0">
      <item>
       <widget class="QListWidget" name="listWidget"/>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout"/>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <widget class="QLabel" name="labelIndexTableFormat">
       <property name="text">
        <string>IndexTableFormat</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="comboBoxTableFormat"/>
     </item>
     <item>
      <widget class="QLabel" name="labelISOFormat">
       <property name="text">
        <string>ISOFormat</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="comboBoxIsoFormat"/>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonGenerate">
       <property name="text">
        <string>Generate</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
