#pragma once

#include <QDialog>
#include "ui_ISOFrameSettingDialog.h"
#include "core/math/Math.hpp"
#include "ISOPaperMgr.h"

class ISOFrameSettingDialog : public QDialog
{
    Q_OBJECT
public:
    ISOFrameSettingDialog(ISOPaperMgr& mgr, QWidget *parent = Q_NULLPTR);
    ~ISOFrameSettingDialog();
protected:
    virtual void showEvent(QShowEvent*) override;
public slots:
    void slotPushButtonNewClicked();
    void slotPushButtonCopyClicked();
    void slotPushButtonChangeClicked();
    void slotPushButtonApplyClicked();
    void slotPushButtonDeleteClicked();
    void slotComboboxTypeCurrentIndexChanged(int);
    void slotListWidgetCurrentRowChanged(int);
public:
    void getPaperConfigFromDialog(WD::ISOPaper& paper);
    void updateDialog();
    void copyPaper(const WD::ISOPaper& from, WD::ISOPaper& to);
private:
    const static constexpr char* defaultName = "ISO";
    void initDialog();
    void retranslateUi();
    /**
     * @brief 检测名称是否合法(是否已经存在)
    */
    inline bool checkName(const QString& name) const
    {
        return ui.listWidgetFrameList->findItems(name, Qt::MatchFlag::MatchExactly).empty();
    }
protected:
    Ui::ISOFrameSettingDialog ui;
    ISOPaperMgr& _mgr;
};