#pragma once

#include <QDialog>
#include "ui_ISOSettingDialog.h"
#include "core/math/Math.hpp"
#include "ISOSettingDialog.h"

class ISOPlotAreaSettingDialog : public ISOSettingDialog
{
    Q_OBJECT
public:
    ISOPlotAreaSettingDialog(QWidget* parent = Q_NULLPTR) 
    {
        WDUnused(parent);
    };
    ~ISOPlotAreaSettingDialog() {};
public:
    virtual void updateDialog() override {};
private:
protected:

};