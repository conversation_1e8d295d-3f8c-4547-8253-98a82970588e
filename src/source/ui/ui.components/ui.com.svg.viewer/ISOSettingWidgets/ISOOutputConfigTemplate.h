#pragma once
#include "core/math/Math.hpp"
#include "ISOPaper.h"
WD_NAMESPACE_BEGIN
class ISOOutputConfigTemplate
{
public:
    ISOOutputConfigTemplate();
    ~ISOOutputConfigTemplate();
public:

private:
    ISOPaper* _pPaper = nullptr;
    // 材料表区
    ISOMaterialArea* _pMaterialArea = nullptr;
    // 标题栏区
    ISOTitleBarArea* _pTitleBarArea = nullptr;
    // 签署栏
    ISOSignBarArea*  _pSignBarArea = nullptr;
    // 图纸说明区
    ISOPaperDescArea* _pPaperDescArea = nullptr;
    // 绘图区
    ISOPolttingArea* _pPolttingArea = nullptr;
};
WD_NAMESPACE_END