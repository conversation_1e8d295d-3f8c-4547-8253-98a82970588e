#pragma once

#include <QDialog>
#include "ui_ISOFrameSettingDialog.h"
#include "core/math/Math.hpp"
#include "ISOPaperMgr.h"

class ISOTemplateEditDialog : public QDialog
{
    Q_OBJECT
public:
    ISOTemplateEditDialog(ISOPaperMgr& mgr, QWidget* parent = Q_NULLPTR) 
        : _mgr(mgr)
    {
        WDUnused(parent);
    };
    ~ISOTemplateEditDialog() {};
protected:
    //virtual void showEvent(QShowEvent*) override;
public slots:
public:
private:
    const static constexpr char* defaultName = "ISO";
    void initDialog() {};
    void retranslateUi() {};
    /**
     * @brief 检测名称是否合法(是否已经存在)
    */
    inline bool checkName(const QString& name) const
    {
        return ui.listWidgetFrameList->findItems(name, Qt::MatchFlag::MatchExactly).empty();
    }
protected:
    Ui::ISOFrameSettingDialog ui;
    ISOPaperMgr& _mgr;
};