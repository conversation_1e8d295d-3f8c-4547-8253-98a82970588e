<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ISOMaterialListEditDialog</class>
 <widget class="QDialog" name="ISOMaterialListEditDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>631</width>
    <height>451</height>
   </rect>
  </property>
  <property name="focusPolicy">
   <enum>Qt::StrongFocus</enum>
  </property>
  <property name="windowTitle">
   <string>ISOFrameSetting</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_4">
   <item row="0" column="1">
    <widget class="QLabel" name="labelName">
     <property name="text">
      <string>Name</string>
     </property>
    </widget>
   </item>
   <item row="1" column="3" colspan="2">
    <widget class="QComboBox" name="comboBoxType"/>
   </item>
   <item row="0" column="0">
    <widget class="QLabel" name="labelFrameList">
     <property name="text">
      <string>FrameList</string>
     </property>
    </widget>
   </item>
   <item row="3" column="1">
    <widget class="QPushButton" name="pushButtonNew">
     <property name="focusPolicy">
      <enum>Qt::StrongFocus</enum>
     </property>
     <property name="text">
      <string>New</string>
     </property>
     <property name="autoDefault">
      <bool>false</bool>
     </property>
    </widget>
   </item>
   <item row="1" column="7" colspan="2">
    <widget class="QSpinBox" name="spinBoxWidth">
     <property name="maximum">
      <number>999999</number>
     </property>
    </widget>
   </item>
   <item row="3" column="8">
    <widget class="QPushButton" name="pushButtonApply">
     <property name="focusPolicy">
      <enum>Qt::StrongFocus</enum>
     </property>
     <property name="text">
      <string>Apply</string>
     </property>
     <property name="autoDefault">
      <bool>false</bool>
     </property>
    </widget>
   </item>
   <item row="3" column="4" colspan="2">
    <widget class="QPushButton" name="pushButtonDelete">
     <property name="focusPolicy">
      <enum>Qt::StrongFocus</enum>
     </property>
     <property name="text">
      <string>Delete</string>
     </property>
     <property name="autoDefault">
      <bool>false</bool>
     </property>
    </widget>
   </item>
   <item row="1" column="0" rowspan="3">
    <widget class="QListWidget" name="listWidgetFrameList"/>
   </item>
   <item row="0" column="3">
    <widget class="QLabel" name="labelType">
     <property name="text">
      <string>Type</string>
     </property>
    </widget>
   </item>
   <item row="3" column="2" colspan="2">
    <widget class="QPushButton" name="pushButtonChange">
     <property name="focusPolicy">
      <enum>Qt::StrongFocus</enum>
     </property>
     <property name="text">
      <string>Change</string>
     </property>
     <property name="autoDefault">
      <bool>false</bool>
     </property>
    </widget>
   </item>
   <item row="1" column="1" colspan="2">
    <widget class="QLineEdit" name="lineEditName"/>
   </item>
   <item row="0" column="5" colspan="2">
    <widget class="QLabel" name="labelPaperHeight">
     <property name="text">
      <string>PaperHeight</string>
     </property>
    </widget>
   </item>
   <item row="0" column="7" colspan="2">
    <widget class="QLabel" name="labelPaperWidth">
     <property name="text">
      <string>PaperWidth</string>
     </property>
    </widget>
   </item>
   <item row="3" column="6" colspan="2">
    <widget class="QPushButton" name="pushButtonCopy">
     <property name="focusPolicy">
      <enum>Qt::StrongFocus</enum>
     </property>
     <property name="text">
      <string>Copy</string>
     </property>
     <property name="autoDefault">
      <bool>false</bool>
     </property>
    </widget>
   </item>
   <item row="1" column="5" colspan="2">
    <widget class="QSpinBox" name="spinBoxHeight">
     <property name="maximum">
      <number>999999</number>
     </property>
     <property name="singleStep">
      <number>1</number>
     </property>
    </widget>
   </item>
   <item row="2" column="1" colspan="8">
    <widget class="QTabWidget" name="tabWidget">
     <property name="currentIndex">
      <number>3</number>
     </property>
     <widget class="QWidget" name="FrameSetting">
      <attribute name="title">
       <string>FrameSetting</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout">
       <item row="0" column="1">
        <widget class="QComboBox" name="comboBoxStrokeWidth"/>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="labelBindingLineUpSpacing">
         <property name="text">
          <string>BindingLineUpSpacing</string>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="labelLineType">
         <property name="text">
          <string>LineType</string>
         </property>
        </widget>
       </item>
       <item row="3" column="3">
        <widget class="QSpinBox" name="spinBoxBindingLineRightSpacing">
         <property name="maximum">
          <number>9999</number>
         </property>
        </widget>
       </item>
       <item row="2" column="3">
        <widget class="QSpinBox" name="spinBoxBindingLineDownSpacing">
         <property name="maximum">
          <number>9999</number>
         </property>
        </widget>
       </item>
       <item row="5" column="0">
        <widget class="QLabel" name="labelDrawingScale">
         <property name="text">
          <string>DrawingScale</string>
         </property>
        </widget>
       </item>
       <item row="2" column="2">
        <widget class="QLabel" name="labelBindingLineDownSpacing">
         <property name="text">
          <string>BindingLineDownSpacing</string>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="QSpinBox" name="spinBoxBindingLineLeftSpacing">
         <property name="maximum">
          <number>9999</number>
         </property>
        </widget>
       </item>
       <item row="4" column="1">
        <widget class="QCheckBox" name="checkBoxBorderCoordinates">
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="labelBindingLineLeftSpacing">
         <property name="text">
          <string>BindingLineLeftSpacing</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QComboBox" name="comboBoxLineType"/>
       </item>
       <item row="4" column="0">
        <widget class="QLabel" name="labelBorderCoordinates">
         <property name="text">
          <string>BorderCoordinates</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QSpinBox" name="spinBoxBindingLineUpSpacing">
         <property name="maximum">
          <number>9999</number>
         </property>
        </widget>
       </item>
       <item row="0" column="0">
        <widget class="QLabel" name="labelStrokeWidth">
         <property name="text">
          <string>StrokeWidth</string>
         </property>
        </widget>
       </item>
       <item row="3" column="2">
        <widget class="QLabel" name="labelBindingLineRightSpacing">
         <property name="text">
          <string>BindingLineRightSpacing</string>
         </property>
        </widget>
       </item>
       <item row="5" column="1" colspan="2" alignment="Qt::AlignLeft">
        <widget class="QLineEdit" name="lineEditDrawingScale"/>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="TitleBar">
      <attribute name="title">
       <string>TitleBar</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_2">
       <item row="0" column="0">
        <widget class="QLabel" name="labelTitleBarWidth">
         <property name="text">
          <string>Width</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QSpinBox" name="spinBoxTitleBarWidth">
         <property name="maximum">
          <number>9999</number>
         </property>
        </widget>
       </item>
       <item row="0" column="2">
        <widget class="QLabel" name="labelTitleBarRowHeight">
         <property name="text">
          <string>RowHeight</string>
         </property>
        </widget>
       </item>
       <item row="0" column="3">
        <widget class="QSpinBox" name="spinBoxTitleBarRowHeight">
         <property name="maximum">
          <number>9999</number>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="labelTitleBarHeight">
         <property name="text">
          <string>Height</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QSpinBox" name="spinBoxTitleBarHeight">
         <property name="maximum">
          <number>9999</number>
         </property>
        </widget>
       </item>
       <item row="1" column="2">
        <widget class="QLabel" name="labelTitleBarColWidth">
         <property name="text">
          <string>ColWidth</string>
         </property>
        </widget>
       </item>
       <item row="1" column="3">
        <widget class="QSpinBox" name="spinBoxTitleBarColWidth">
         <property name="maximum">
          <number>9999</number>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="labelTitleBarPosition">
         <property name="text">
          <string>Position</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QComboBox" name="comboBoxTitleBarPosition"/>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="labelTitleBarOuterBorderStrokeWidth">
         <property name="text">
          <string>OuterBorderStrokeWidth</string>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="QComboBox" name="comboBoxTitleBarOuterBorderStrokeWidth"/>
       </item>
       <item row="3" column="2">
        <widget class="QLabel" name="labelTitleBarOuterBorderLineType">
         <property name="text">
          <string>OuterBorderLineType</string>
         </property>
        </widget>
       </item>
       <item row="3" column="3">
        <widget class="QComboBox" name="comboBoxTitleBarOuterBorderLineType"/>
       </item>
       <item row="4" column="0">
        <widget class="QLabel" name="labelTitleBarInnerBorderStrokeWidth">
         <property name="text">
          <string>InnerBorderStrokeWidth</string>
         </property>
        </widget>
       </item>
       <item row="4" column="1">
        <widget class="QComboBox" name="comboBoxTitleBarInnerBorderStrokeWidth"/>
       </item>
       <item row="4" column="2">
        <widget class="QLabel" name="labelTitleBarInnerBorderLineType">
         <property name="text">
          <string>InnerBorderLineType</string>
         </property>
        </widget>
       </item>
       <item row="4" column="3">
        <widget class="QComboBox" name="comboBoxTitleBarInnerBorderLineType"/>
       </item>
       <item row="5" column="0">
        <widget class="QLabel" name="labelTitleBarFontFamliy">
         <property name="text">
          <string>FontFamliy</string>
         </property>
        </widget>
       </item>
       <item row="5" column="1">
        <widget class="QComboBox" name="comboBoxTitleBarFontFamliy"/>
       </item>
       <item row="5" column="2">
        <widget class="QLabel" name="labelTitleBarFontSize">
         <property name="text">
          <string>FontSize</string>
         </property>
        </widget>
       </item>
       <item row="5" column="3">
        <widget class="QComboBox" name="comboBoxTitleBarFontSize"/>
       </item>
       <item row="6" column="0">
        <widget class="QLabel" name="labelTitleBarTextAlign">
         <property name="text">
          <string>TextAlign</string>
         </property>
        </widget>
       </item>
       <item row="6" column="1">
        <widget class="QComboBox" name="comboBoxTitleBarAlign"/>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="SignBar">
      <attribute name="title">
       <string>SignBar</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_3">
       <item row="0" column="0">
        <widget class="QLabel" name="labelSignBarWidth">
         <property name="text">
          <string>Width</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QSpinBox" name="spinBoxSignBarWidth">
         <property name="maximum">
          <number>9999</number>
         </property>
        </widget>
       </item>
       <item row="0" column="2">
        <widget class="QLabel" name="labelSignBarRowHeight">
         <property name="text">
          <string>RowHeight</string>
         </property>
        </widget>
       </item>
       <item row="0" column="3">
        <widget class="QSpinBox" name="spinBoxSignBarRowHeight">
         <property name="maximum">
          <number>9999</number>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="labelSignBarHeight">
         <property name="text">
          <string>Height</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QSpinBox" name="spinBoxSignBarHeight">
         <property name="maximum">
          <number>9999</number>
         </property>
        </widget>
       </item>
       <item row="1" column="2">
        <widget class="QLabel" name="labelSignBarColWidth">
         <property name="text">
          <string>ColWidth</string>
         </property>
        </widget>
       </item>
       <item row="1" column="3">
        <widget class="QSpinBox" name="spinBoxSignBarColWidth">
         <property name="maximum">
          <number>9999</number>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="labelSignBarPosition">
         <property name="text">
          <string>Position</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QComboBox" name="comboBoxSignBarPosition"/>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="labelSignBarOuterBorderStrokeWidth">
         <property name="text">
          <string>OuterBorderStrokeWidth</string>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="QComboBox" name="comboBoxSignBarOuterBorderStrokeWidth"/>
       </item>
       <item row="3" column="2">
        <widget class="QLabel" name="labelSignBarOuterBorderLineType">
         <property name="text">
          <string>OuterBorderLineType</string>
         </property>
        </widget>
       </item>
       <item row="3" column="3">
        <widget class="QComboBox" name="comboBoxSignBarOuterBorderLineType"/>
       </item>
       <item row="4" column="0">
        <widget class="QLabel" name="labelSignBarInnerBorderStrokeWidth">
         <property name="text">
          <string>InnerBorderStrokeWidth</string>
         </property>
        </widget>
       </item>
       <item row="4" column="1">
        <widget class="QComboBox" name="comboBoxSignBarInnerBorderStrokeWidth"/>
       </item>
       <item row="4" column="2">
        <widget class="QLabel" name="labelSignBarInnerBorderLineType">
         <property name="text">
          <string>InnerBorderLineType</string>
         </property>
        </widget>
       </item>
       <item row="4" column="3">
        <widget class="QComboBox" name="comboBoxSignBarInnerBorderLineType"/>
       </item>
       <item row="5" column="0">
        <widget class="QLabel" name="labelSignBarFontFamliy">
         <property name="text">
          <string>FontFamliy</string>
         </property>
        </widget>
       </item>
       <item row="5" column="1">
        <widget class="QComboBox" name="comboBoxSignBarFontFamliy"/>
       </item>
       <item row="5" column="2">
        <widget class="QLabel" name="labelSignBarFontSize">
         <property name="text">
          <string>FontSize</string>
         </property>
        </widget>
       </item>
       <item row="5" column="3">
        <widget class="QComboBox" name="comboBoxSignBarFontSize"/>
       </item>
       <item row="6" column="0">
        <widget class="QLabel" name="labelSignBarTextAlign">
         <property name="text">
          <string>TextAlign</string>
         </property>
        </widget>
       </item>
       <item row="6" column="1">
        <widget class="QComboBox" name="comboBoxSignBarAlign"/>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="MaterialBar">
      <attribute name="title">
       <string>MaterialBar</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_3">
       <item row="4" column="2">
        <widget class="QLabel" name="labelMaterialBarPosition">
         <property name="text">
          <string>Position</string>
         </property>
        </widget>
       </item>
       <item row="10" column="0">
        <widget class="QLabel" name="labelTitle2">
         <property name="text">
          <string>Title</string>
         </property>
        </widget>
       </item>
       <item row="11" column="5">
        <widget class="QComboBox" name="comboBoxTotalWeight"/>
       </item>
       <item row="13" column="3">
        <widget class="QCheckBox" name="checkBoxSummaryNum">
         <property name="text">
          <string>CheckBox</string>
         </property>
        </widget>
       </item>
       <item row="12" column="4">
        <widget class="QLineEdit" name="lineEditWeight_2"/>
       </item>
       <item row="7" column="4">
        <widget class="QComboBox" name="comboBoxLength"/>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="labelFont">
         <property name="text">
          <string>Font</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QLabel" name="labelMaterialBarFontFamliy">
         <property name="text">
          <string>FontFamliy</string>
         </property>
        </widget>
       </item>
       <item row="1" column="5">
        <widget class="QSpinBox" name="spinBoxMaterialBarHeight">
         <property name="maximum">
          <number>9999</number>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QComboBox" name="comboBoxMaterialBarFontFamliy"/>
       </item>
       <item row="9" column="0">
        <widget class="QLabel" name="labelSummary">
         <property name="text">
          <string>Summary</string>
         </property>
        </widget>
       </item>
       <item row="11" column="2">
        <widget class="QComboBox" name="comboBoxMaterialCode"/>
       </item>
       <item row="0" column="2">
        <widget class="QLabel" name="labelMaterialBarFontSize">
         <property name="text">
          <string>FontSize</string>
         </property>
        </widget>
       </item>
       <item row="6" column="4">
        <widget class="QLineEdit" name="lineEditLength"/>
       </item>
       <item row="8" column="4">
        <widget class="QLineEdit" name="lineEditLength_2"/>
       </item>
       <item row="11" column="0">
        <widget class="QLabel" name="labelContent2">
         <property name="text">
          <string>Content</string>
         </property>
        </widget>
       </item>
       <item row="6" column="0">
        <widget class="QLabel" name="labelTitle">
         <property name="text">
          <string>Title</string>
         </property>
        </widget>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="labelDetails">
         <property name="text">
          <string>Details</string>
         </property>
        </widget>
       </item>
       <item row="8" column="5">
        <widget class="QLineEdit" name="lineEditWidth"/>
       </item>
       <item row="4" column="1">
        <widget class="QComboBox" name="comboBoxTexture"/>
       </item>
       <item row="9" column="2">
        <widget class="QCheckBox" name="checkBoxSummaryDescribe">
         <property name="text">
          <string>CheckBox</string>
         </property>
        </widget>
       </item>
       <item row="6" column="2">
        <widget class="QLineEdit" name="lineEditRowDescribe"/>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="labelDescribe">
         <property name="text">
          <string>Describe</string>
         </property>
        </widget>
       </item>
       <item row="13" column="1">
        <widget class="QCheckBox" name="checkBoxSummaryMaterialGrade">
         <property name="text">
          <string>CheckBox</string>
         </property>
        </widget>
       </item>
       <item row="12" column="2">
        <widget class="QLineEdit" name="lineEditMaterialCode_2"/>
       </item>
       <item row="12" column="0">
        <widget class="QLabel" name="labelRowWidth2">
         <property name="text">
          <string>RowWidth</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QComboBox" name="comboBoxDescribe"/>
       </item>
       <item row="6" column="1">
        <widget class="QLineEdit" name="lineEditIndex"/>
       </item>
       <item row="3" column="1">
        <widget class="QComboBox" name="comboBoxDetails"/>
       </item>
       <item row="12" column="1">
        <widget class="QLineEdit" name="lineEditMaterialGrade_2"/>
       </item>
       <item row="10" column="3">
        <widget class="QLineEdit" name="lineEditNum"/>
       </item>
       <item row="1" column="4">
        <widget class="QSpinBox" name="spinBoxMaterialBarWidth">
         <property name="maximum">
          <number>9999</number>
         </property>
        </widget>
       </item>
       <item row="11" column="3">
        <widget class="QComboBox" name="comboBoxNum"/>
       </item>
       <item row="8" column="0">
        <widget class="QLabel" name="labelRowWidth">
         <property name="text">
          <string>RowWidth</string>
         </property>
        </widget>
       </item>
       <item row="0" column="4">
        <widget class="QLabel" name="labelMaterialBarWidth">
         <property name="text">
          <string>Width</string>
         </property>
        </widget>
       </item>
       <item row="6" column="5">
        <widget class="QLineEdit" name="lineEditWidth_2"/>
       </item>
       <item row="11" column="1">
        <widget class="QComboBox" name="comboBoxMaterialGrade"/>
       </item>
       <item row="13" column="0">
        <widget class="QLabel" name="labelSummary2">
         <property name="text">
          <string>Summary</string>
         </property>
        </widget>
       </item>
       <item row="1" column="2">
        <widget class="QComboBox" name="comboBoxMaterialBarFontSize"/>
       </item>
       <item row="7" column="2">
        <widget class="QComboBox" name="comboBoxRowDescribe"/>
       </item>
       <item row="8" column="3">
        <widget class="QLineEdit" name="lineEditSpecifications_2"/>
       </item>
       <item row="4" column="3">
        <widget class="QComboBox" name="comboBoxMaterialBarPosition"/>
       </item>
       <item row="7" column="5">
        <widget class="QComboBox" name="comboBoxWidth"/>
       </item>
       <item row="4" column="0">
        <widget class="QLabel" name="labelTexture">
         <property name="text">
          <string>Texture</string>
         </property>
        </widget>
       </item>
       <item row="10" column="4">
        <widget class="QLineEdit" name="lineEditWeight"/>
       </item>
       <item row="7" column="3">
        <widget class="QComboBox" name="comboBoxSpecifications"/>
       </item>
       <item row="9" column="3">
        <widget class="QCheckBox" name="checkBoxSummarySpecifications">
         <property name="text">
          <string>CheckBox</string>
         </property>
        </widget>
       </item>
       <item row="13" column="5">
        <widget class="QCheckBox" name="checkBoxSummaryTotalWeight">
         <property name="text">
          <string>CheckBox</string>
         </property>
        </widget>
       </item>
       <item row="10" column="1">
        <widget class="QLineEdit" name="lineEditMaterialGrade"/>
       </item>
       <item row="7" column="1">
        <widget class="QComboBox" name="comboBoxIndex"/>
       </item>
       <item row="9" column="5">
        <widget class="QCheckBox" name="checkBoxSummaryWidth">
         <property name="text">
          <string>CheckBox</string>
         </property>
        </widget>
       </item>
       <item row="8" column="1">
        <widget class="QLineEdit" name="lineEditIndexRowWidth"/>
       </item>
       <item row="6" column="3">
        <widget class="QLineEdit" name="lineEditSpecifications"/>
       </item>
       <item row="13" column="2">
        <widget class="QCheckBox" name="checkBoxSummary7">
         <property name="text">
          <string>CheckBox</string>
         </property>
        </widget>
       </item>
       <item row="10" column="2">
        <widget class="QLineEdit" name="lineEditMaterialCode"/>
       </item>
       <item row="8" column="2">
        <widget class="QLineEdit" name="lineEditRowDescribe_2"/>
       </item>
       <item row="0" column="5">
        <widget class="QLabel" name="labelMaterialBarHeight">
         <property name="text">
          <string>Height</string>
         </property>
        </widget>
       </item>
       <item row="7" column="0">
        <widget class="QLabel" name="labelContent">
         <property name="text">
          <string>Content</string>
         </property>
        </widget>
       </item>
       <item row="9" column="1">
        <widget class="QCheckBox" name="checkBoxSummaryIndex">
         <property name="text">
          <string>CheckBox</string>
         </property>
        </widget>
       </item>
       <item row="10" column="5">
        <widget class="QLineEdit" name="lineEditTotalWeight"/>
       </item>
       <item row="9" column="4">
        <widget class="QCheckBox" name="checkBoxSummaryLength">
         <property name="text">
          <string>CheckBox</string>
         </property>
        </widget>
       </item>
       <item row="13" column="4">
        <widget class="QCheckBox" name="checkBoxSummaryWeight">
         <property name="text">
          <string>CheckBox</string>
         </property>
        </widget>
       </item>
       <item row="11" column="4">
        <widget class="QComboBox" name="comboBoxWeight"/>
       </item>
       <item row="1" column="3">
        <widget class="QLabel" name="labelMaterialSize">
         <property name="text">
          <string>MatrialSize</string>
         </property>
        </widget>
       </item>
       <item row="12" column="3">
        <widget class="QLineEdit" name="lineEditNum_2"/>
       </item>
       <item row="6" column="6">
        <widget class="QLineEdit" name="lineEditTexture_2"/>
       </item>
       <item row="7" column="6">
        <widget class="QComboBox" name="comboBoxTexture_2"/>
       </item>
       <item row="8" column="6">
        <widget class="QLineEdit" name="lineEditTexture"/>
       </item>
       <item row="9" column="6">
        <widget class="QCheckBox" name="checkBoxSummaryTexture">
         <property name="text">
          <string>CheckBox</string>
         </property>
        </widget>
       </item>
       <item row="12" column="5">
        <widget class="QLineEdit" name="lineEditTotalWeight_2"/>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="DrawingInstructionsBar">
      <attribute name="title">
       <string>DrawingInstructionsBar</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_3">
       <item row="0" column="3">
        <widget class="QComboBox" name="comboBoxDrawingInstructionsBarFormat"/>
       </item>
       <item row="3" column="2">
        <widget class="QLabel" name="labelHeader3">
         <property name="text">
          <string>Header3</string>
         </property>
        </widget>
       </item>
       <item row="4" column="0">
        <widget class="QLineEdit" name="lineEditContent1"/>
       </item>
       <item row="4" column="1">
        <widget class="QLineEdit" name="lineEditContent2"/>
       </item>
       <item row="4" column="7">
        <widget class="QLineEdit" name="lineEditContent7"/>
       </item>
       <item row="4" column="8">
        <widget class="QLineEdit" name="lineEditContent8"/>
       </item>
       <item row="3" column="6">
        <widget class="QLabel" name="labelHeader6">
         <property name="text">
          <string>Header6</string>
         </property>
        </widget>
       </item>
       <item row="4" column="6">
        <widget class="QLineEdit" name="lineEditContent6"/>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="labelHeader1">
         <property name="text">
          <string>Header1</string>
         </property>
        </widget>
       </item>
       <item row="3" column="3">
        <widget class="QLabel" name="labelHeader4">
         <property name="text">
          <string>Header4</string>
         </property>
        </widget>
       </item>
       <item row="3" column="8">
        <widget class="QLabel" name="labelHeader8">
         <property name="text">
          <string>Header8</string>
         </property>
        </widget>
       </item>
       <item row="3" column="4">
        <widget class="QLabel" name="labelHeader5">
         <property name="text">
          <string>Header5</string>
         </property>
        </widget>
       </item>
       <item row="4" column="3">
        <widget class="QLineEdit" name="lineEditContent4"/>
       </item>
       <item row="3" column="7">
        <widget class="QLabel" name="labelHeader7">
         <property name="text">
          <string>Header7</string>
         </property>
        </widget>
       </item>
       <item row="4" column="2">
        <widget class="QLineEdit" name="lineEditContent3"/>
       </item>
       <item row="4" column="4">
        <widget class="QLineEdit" name="lineEditContent5"/>
       </item>
       <item row="3" column="1">
        <widget class="QLabel" name="labelHeader2">
         <property name="text">
          <string>Header2</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1" colspan="2">
        <widget class="QLabel" name="labelDrawingInstructionsBarFormat">
         <property name="text">
          <string>Format</string>
         </property>
        </widget>
       </item>
       <item row="1" column="3">
        <widget class="QLabel" name="labelDrawingInstructionsBarWidth">
         <property name="text">
          <string>Width</string>
         </property>
        </widget>
       </item>
       <item row="2" column="3">
        <widget class="QSpinBox" name="spinBoxDrawingInstructionsBarWidth">
         <property name="maximum">
          <number>9999</number>
         </property>
        </widget>
       </item>
       <item row="2" column="0" colspan="3">
        <widget class="QLabel" name="labelInstructionAreaSize">
         <property name="text">
          <string>labelInstructionAreaSize</string>
         </property>
        </widget>
       </item>
       <item row="2" column="4">
        <widget class="QSpinBox" name="spinBoxDrawingInstructionsBarHeight">
         <property name="maximum">
          <number>9999</number>
         </property>
        </widget>
       </item>
       <item row="1" column="4">
        <widget class="QLabel" name="labelDrawingInstructionsBarHeight">
         <property name="text">
          <string>Height</string>
         </property>
        </widget>
       </item>
       <item row="2" column="8">
        <widget class="QSpinBox" name="spinBoxDrawingInstructionsBarCol">
         <property name="maximum">
          <number>9999</number>
         </property>
        </widget>
       </item>
       <item row="1" column="8">
        <widget class="QLabel" name="labelDrawingInstructionsBarCol">
         <property name="text">
          <string>Col</string>
         </property>
        </widget>
       </item>
       <item row="1" column="7">
        <widget class="QLabel" name="labelDrawingInstructionsBarRow">
         <property name="text">
          <string>Row</string>
         </property>
        </widget>
       </item>
       <item row="2" column="7">
        <widget class="QSpinBox" name="spinBoxDrawingInstructionsBarRow">
         <property name="maximum">
          <number>9999</number>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <tabstops>
  <tabstop>listWidgetFrameList</tabstop>
  <tabstop>lineEditName</tabstop>
  <tabstop>comboBoxType</tabstop>
  <tabstop>spinBoxHeight</tabstop>
  <tabstop>spinBoxWidth</tabstop>
  <tabstop>tabWidget</tabstop>
  <tabstop>comboBoxStrokeWidth</tabstop>
  <tabstop>comboBoxLineType</tabstop>
  <tabstop>spinBoxBindingLineUpSpacing</tabstop>
  <tabstop>spinBoxBindingLineDownSpacing</tabstop>
  <tabstop>spinBoxBindingLineLeftSpacing</tabstop>
  <tabstop>spinBoxBindingLineRightSpacing</tabstop>
  <tabstop>checkBoxBorderCoordinates</tabstop>
  <tabstop>lineEditDrawingScale</tabstop>
  <tabstop>pushButtonNew</tabstop>
  <tabstop>pushButtonChange</tabstop>
  <tabstop>pushButtonDelete</tabstop>
  <tabstop>pushButtonCopy</tabstop>
  <tabstop>pushButtonApply</tabstop>
  <tabstop>spinBoxTitleBarWidth</tabstop>
  <tabstop>spinBoxTitleBarRowHeight</tabstop>
  <tabstop>spinBoxTitleBarHeight</tabstop>
  <tabstop>spinBoxTitleBarColWidth</tabstop>
  <tabstop>comboBoxTitleBarPosition</tabstop>
  <tabstop>comboBoxTitleBarOuterBorderStrokeWidth</tabstop>
  <tabstop>comboBoxTitleBarOuterBorderLineType</tabstop>
  <tabstop>comboBoxTitleBarInnerBorderStrokeWidth</tabstop>
  <tabstop>comboBoxTitleBarInnerBorderLineType</tabstop>
  <tabstop>comboBoxTitleBarFontFamliy</tabstop>
  <tabstop>comboBoxTitleBarFontSize</tabstop>
  <tabstop>comboBoxTitleBarAlign</tabstop>
  <tabstop>spinBoxSignBarWidth</tabstop>
  <tabstop>spinBoxSignBarRowHeight</tabstop>
  <tabstop>spinBoxSignBarHeight</tabstop>
  <tabstop>spinBoxSignBarColWidth</tabstop>
  <tabstop>comboBoxSignBarPosition</tabstop>
  <tabstop>comboBoxSignBarOuterBorderStrokeWidth</tabstop>
  <tabstop>comboBoxSignBarOuterBorderLineType</tabstop>
  <tabstop>comboBoxSignBarInnerBorderStrokeWidth</tabstop>
  <tabstop>comboBoxSignBarInnerBorderLineType</tabstop>
  <tabstop>comboBoxSignBarFontFamliy</tabstop>
  <tabstop>comboBoxSignBarFontSize</tabstop>
  <tabstop>comboBoxSignBarAlign</tabstop>
 </tabstops>
 <resources/>
 <connections/>
</ui>
