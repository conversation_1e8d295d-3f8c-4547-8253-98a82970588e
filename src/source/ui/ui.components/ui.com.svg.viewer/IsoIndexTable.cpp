#include "IsoIndexTable.h"
#include "ISOSaveHelper.h"
#include <QDate>
#include <QFileInfo>
#include <qfiledialog.h>
#include "core/common/WDStringConvert.h"

#include "../../ui.commonLibrary/ui.commonLib.excel/QTableWidget2Excel.h"
#ifdef WIN32
#include <qaxobject.h>
#include <combaseapi.h>
#endif

WD_NAMESPACE_USE
constexpr const double standardWidth = 210.0;
constexpr const double standardHeight = 297.0;

static std::string DoubleValueConvertToString(const double& val)
{
    char valStr[1024] = { 0 };
    sprintf_s(valStr, sizeof(valStr), "%.2lf", val);
    return valStr;
}

StringVector IsoIndexTable::GenSvgIndexTable(const PaperInfos& paperInfos
    , const ISOUnitConvert& ucvt
    , const IndexTableInfo& tableInfo)
{
    constexpr const int indexTableRowCnt = 25;
    constexpr const int totalIndexCnt = 30;
    //总行数               +2是因为索引表最后两行会用做汇总信息
    const int totalCnt = static_cast<int>(paperInfos.size()) + 2;
    // 计算图纸数量       +(totalIndexCnt - indexTableRowCnt) 是因为签署栏
    int paperCnt = static_cast<int>(((totalCnt + (totalIndexCnt - indexTableRowCnt)) / totalIndexCnt)
        + ((totalCnt + (totalIndexCnt - indexTableRowCnt)) % totalIndexCnt > 0 ? 1 : 0));

    const auto tableSize = tableInfo.size();
    double width   =   tableSize.x;
    double height  =   tableSize.y;
    char szView[1024] = { 0 };
    sprintf(szView, "%d %d %d %d", 0, 0, static_cast<int>(ucvt.paperToPixel(width)), static_cast<int>(ucvt.paperToPixel(height)));
    const double xScale = width / standardWidth;
    const double yScale = height / standardHeight;
    const DVec2 scale = DVec2(xScale, yScale);

    const double margeLeft      =   12.25 * xScale;
    const double margeRight     =   16 * xScale;
    const double margeTop       =   10 * yScale;
    const double margeBottom    =   13.82 * yScale;

    StringVector ret;
    ret.reserve(paperCnt);
    double weight = 0.0;
    double tableEachColHeight = 219.0 * yScale / static_cast<double>(totalIndexCnt);
    for (int idx = 0; idx < paperCnt; ++idx)
    {
        WDSvgXmlCreator  svg("xml version='1.0' encoding='utf-8' standalone='no'");
        svg.root("svg")
            .attr("viewBox", std::string(szView))
            .attr("xmlns", "http://www.w3.org/2000/svg")
            .attr("xmlns:xlink", "http://www.w3.org/1999/xlink");

        auto sRoot = svg.root("svg");
        WDISOSVGPainter painter(ucvt);
        painter.unitType = ISOUnitType::UT_mm;

        auto tmpGroup = sRoot.append("g").attr(SVG_GROUP_ATTR_NAME, "sideArea");
        painter.pGroup = &tmpGroup;
        // 内边框的左上角
        const DVec2 position = DVec2(margeLeft, margeTop);
        // 绘制内边框
        double innSizeLineWidth = 0.5 * xScale;
        double normalLineWidth = 0.18 * xScale;
        painter.drawRect(position
            , DVec2(standardWidth - margeLeft - margeRight, standardHeight - margeTop - margeBottom)
            , WDLineStyle(innSizeLineWidth));
        tmpGroup = sRoot.append("g").attr(SVG_GROUP_ATTR_NAME, "tableInfo");
        painter.pGroup = &tmpGroup;
        WDFontStyle fontStyle;
        fontStyle.fontSize = 3;
        {
            // 
            painter.drawLine(position + DVec2(51.25, 0) * scale, position + DVec2(51.25, 23.8) * scale, WDLineStyle(innSizeLineWidth));
            painter.drawLine(position + DVec2(117.93, 0) * scale, position + DVec2(117.93, 23.8) * scale, WDLineStyle(innSizeLineWidth));
            painter.drawLine(position + DVec2(0, 23.8) * scale, position + DVec2(181.75, 23.8) * scale, WDLineStyle(innSizeLineWidth));
            painter.drawLine(position + DVec2(0, 39.68) * scale, position + DVec2(181.75, 39.68) * scale, WDLineStyle(innSizeLineWidth));
            // 索引表表头
            painter.drawLine(position + DVec2(7.2, 39.68) * scale, position + DVec2(7.2, 54.18) * scale, WDLineStyle(normalLineWidth));
            painter.drawLine(position + DVec2(50.45, 39.68) * scale, position + DVec2(50.45, 54.18) * scale, WDLineStyle(normalLineWidth));
            painter.drawLine(position + DVec2(55.41, 39.68) * scale, position + DVec2(55.41, 54.18) * scale, WDLineStyle(normalLineWidth));
            painter.drawLine(position + DVec2(117.11, 39.68) * scale, position + DVec2(117.11, 54.18) * scale, WDLineStyle(normalLineWidth));
            painter.drawLine(position + DVec2(155.21, 39.68) * scale, position + DVec2(155.21, 54.18) * scale, WDLineStyle(normalLineWidth));
            painter.drawLine(position + DVec2(117.11, 44.52) * scale, position + DVec2(155.21, 44.52) * scale, WDLineStyle(normalLineWidth));
            painter.drawLine(position + DVec2(136.93, 44.52) * scale, position + DVec2(136.93, 54.18) * scale, WDLineStyle(normalLineWidth));

            painter.drawLine(position + DVec2(137.75, 0) * scale, position + DVec2(137.75, 31.74) * scale, WDLineStyle(normalLineWidth));
            painter.drawLine(position + DVec2(117.93, 7.94) * scale, position + DVec2(181.75, 7.94) * scale, WDLineStyle(normalLineWidth));
            painter.drawLine(position + DVec2(117.93, 15.88) * scale, position + DVec2(181.75, 15.88) * scale, WDLineStyle(normalLineWidth));
            painter.drawLine(position + DVec2(117.93, 23.8) * scale, position + DVec2(181.75, 23.8) * scale, WDLineStyle(normalLineWidth));
            painter.drawLine(position + DVec2(0, 31.74) * scale, position + DVec2(181.75, 31.74) * scale, WDLineStyle(normalLineWidth));
            painter.drawLine(position + DVec2(34.63, 23.8) * scale, position + DVec2(34.63, 39.68) * scale, WDLineStyle(normalLineWidth));
            painter.drawLine(position + DVec2(117.93, 23.8) * scale, position + DVec2(117.93, 31.74) * scale, WDLineStyle(normalLineWidth));
            painter.drawLine(position + DVec2(142.71, 15.88) * scale, position + DVec2(142.71, 23.8) * scale, WDLineStyle(0.05 * xScale));

        }
        // 绘制表头
        {
            const DVec2 infoPosS = position;
            // 公司信息 -> 公司名称 + 公司logo
            DVec2 posS = infoPosS + DVec2(0) * scale;
            DVec2 size = DVec2(51.25, 23.8) * scale;
            {
                DVec2 logoSize = DVec2(15.0) * scale;
                posS.y = posS.y + (size.y - logoSize.y) / 2;
                painter.drawImage(posS + DVec2(1.0), WDImage(tableInfo.logoPath), logoSize);
                painter.drawText(tableInfo.companyName, infoPosS + DVec2(16.0, 0), infoPosS + size, fontStyle);
            }
            // 索引表名称
            posS = infoPosS + DVec2(51.25, 0) * scale;
            size = DVec2(66.68, 23.8) * scale;
            fontStyle.fontSize = 6;
            painter.drawText(tableInfo.tableName, posS, posS + size, fontStyle);
            fontStyle.fontSize = 3;
            // 项目文件号
            posS = infoPosS + DVec2(117.93, 0) * scale;
            size = DVec2(19.82, 7.94) * scale;
            painter.drawText(WD::WDTs("ISOIndexTable", "FileNumber"), posS, posS + size, fontStyle);
            posS = infoPosS + DVec2(137.75, 0) * scale;
            size = DVec2(44, 7.94) * scale;
            painter.drawText(tableInfo.projectFileNumber, posS, posS + size, fontStyle);
            // 文表号
            posS = infoPosS + DVec2(117.93, 7.94) * scale;
            size = DVec2(19.82, 7.94) * scale;
            painter.drawText(WD::WDTs("ISOIndexTable", "DocumentNumber"), posS, posS + size, fontStyle);
            posS = infoPosS + DVec2(137.75, 7.94) * scale;
            size = DVec2(44, 7.94) * scale;
            painter.drawText(tableInfo.documentNumber, posS, posS + size, fontStyle);
            // 版次
            posS = infoPosS + DVec2(117.93, 15.88) * scale;
            size = DVec2(19.82, 7.94) * scale;
            painter.drawText(WD::WDTs("TableStyle", "Version"), posS, posS + size, fontStyle);
            posS = infoPosS + DVec2(137.75, 15.88) * scale;
            size = DVec2(4.96, 7.94) * scale;
            painter.drawText(tableInfo.edition, posS, posS + size, fontStyle);
            // 页码
            posS = infoPosS + DVec2(142.71, 15.88) * scale;
            size = DVec2(39, 7.94) * scale;
            char index[1024] = { 0 };
            sprintf_s(index, sizeof(index), WD::WDTs("ISOIndexTable", "PageNumber").c_str(), idx + 1, paperCnt);
            painter.drawText(index, posS, posS + size, fontStyle);
            // 工程名称
            posS = infoPosS + DVec2(0, 23.8) * scale;
            size = DVec2(34.63, 7.94) * scale;
            painter.drawText(WD::WDTs("ISOIndexTable", "ProjectName"), posS, posS + size, fontStyle);
            posS = infoPosS + DVec2(34.63, 23.8) * scale;
            size = DVec2(83.44, 7.94) * scale;
            painter.drawText(tableInfo.engineeringName, posS, posS + size, fontStyle);
            // 单元名称
            posS = infoPosS + DVec2(118.07, 23.8) * scale;
            size = DVec2(19.82, 7.94) * scale;
            painter.drawText(WD::WDTs("ISOIndexTable", "Units"), posS, posS + size, fontStyle);
            posS = infoPosS + DVec2(137.89, 23.8) * scale;
            size = DVec2(44, 7.94) * scale;
            painter.drawText(tableInfo.unitName, posS, posS + size, fontStyle);
            // 业主文件编号
            posS = infoPosS + DVec2(0, 31.74) * scale;
            size = DVec2(34.63, 7.94) * scale;
            painter.drawText(WD::WDTs("ISOIndexTable", "OwnDocumentNumber"), posS, posS + size, fontStyle);
            posS = infoPosS + DVec2(34.63, 31.74) * scale;
            size = DVec2(147.27, 7.94) * scale;
            painter.drawText(tableInfo.ownerFileNumber, posS, posS + size, fontStyle);
        }
        tmpGroup = sRoot.append("g").attr(SVG_GROUP_ATTR_NAME, "indexTable");
        painter.pGroup = &tmpGroup;
        // 绘制索引表
        {
            fontStyle.weight = WDFontStyle::Bold;
            fontStyle.fontSize = 4.0;
            const DVec2 indexTablePosS = position + DVec2(0, 39.68) * scale;
            // 绘制表头索引表表头不规则,这里写死处理
            //顺序号
            DVec2 posS = indexTablePosS + DVec2(0) * scale;
            DVec2 size = DVec2(7.2, 14.5) * scale;
            painter.drawText(WD::WDTs("ISOIndexTable", "SequenceNumber"), posS, posS + size, fontStyle, WDAlign(), true);
            // 文表(图)号
            posS = indexTablePosS + DVec2(7.2, 0) * scale;
            size = DVec2(43.25, 14.5) * scale;
            painter.drawText(WD::WDTs("ISOIndexTable", "SequenceDrawNumber"), posS, posS + size, fontStyle, WDAlign(), true);
            // 版次
            posS = indexTablePosS + DVec2(50.45, 0) * scale;
            size = DVec2(4.96, 14.5) * scale;
            painter.drawText(WD::WDTs("TableStyle", "Version").c_str(), posS, posS + size, fontStyle, WDAlign(), true);
            // 名称
            posS = indexTablePosS + DVec2(55.41, 0) * scale;
            size = DVec2(61.7, 14.5) * scale;
            painter.drawText(WD::WDTs("ISOIndexTable", "IndexTableName"), posS, posS + size, fontStyle, WDAlign(), true);
            // 文件数量
            posS = indexTablePosS + DVec2(117.11, 0) * scale;
            size = DVec2(38.1, 4.84) * scale;
            painter.drawText(WD::WDTs("ISOIndexTable", "NumberOfFiles"), posS, posS + size, fontStyle, WDAlign(), true);
            // 文表(A4)
            posS = indexTablePosS + DVec2(117.11, 4.84) * scale;
            size = DVec2(19.82, 9.68) * scale;
            painter.drawText(WD::WDTs("ISOIndexTable", "SequenceNumberA4"), posS, posS + size, fontStyle, WDAlign(), true);
            // 图纸(A1)
            posS = indexTablePosS + DVec2(136.93, 4.84) * scale;
            size = DVec2(18.28, 9.68) * scale;
            painter.drawText(WD::WDTs("ISOIndexTable", "PaperNumberA1"), posS, posS + size, fontStyle, WDAlign(), true);
            // 备注
            posS = indexTablePosS + DVec2(155.21, 0) * scale;
            size = DVec2(25.7, 14.5) * scale;
            painter.drawText(WD::WDTs("TableStyle", "Notes"), posS, posS + size, fontStyle, WDAlign(), true);


            fontStyle.fontSize = 3;
            // 表格内容
            auto indexCnt = idx == 0 ? indexTableRowCnt : totalIndexCnt;
            ISOTableWidget table(IVec2(indexCnt, 7));
            table.position = position + DVec2(0, 54.18) * scale;
            table.tableDistribution.horizontal = ISOTableWidget::TableDistribution::Horizontal::H_AllFixed;
            table.tableDistribution.vertical = ISOTableWidget::TableDistribution::Vertical::V_Uniform;
            table.setTableHeight(indexCnt * tableEachColHeight);
            table.setColWidths({7.2, 43.25, 4.96, 61.7, 19.82, 18.28, 26.52});
            table.outerBorderLine = innSizeLineWidth;
            table.innerBorderLine = normalLineWidth;

            int row = 0;
            int currentRowCnt = idx == 0 ? 0 : ( indexTableRowCnt + ( idx - 1 ) * totalIndexCnt );
            for (int i = currentRowCnt; i < ( indexTableRowCnt + idx * totalIndexCnt ) && ( i < totalCnt ); ++i)
            {
                int rowIdx = row++;
                if (i == totalCnt - 2)
                {
                    table.setText(rowIdx, 0, ToString(paperInfos.size() + 1));
                    table.setText(rowIdx, 1, tableInfo.documentNumber);
                    table.setText(rowIdx, 2, tableInfo.edition);
                    table.setText(rowIdx, 3, tableInfo.tableName);
                    table.setText(rowIdx, 4, ToString(paperCnt));
                    continue;
                }
                else if (i == totalCnt - 1)
                {
                    table.setText(rowIdx, 3, WD::WDTs("ISOIndexTable", "SubTotal"));
                    table.setText(rowIdx, 4, ToString(paperCnt));
                    table.setText(rowIdx, 5, DoubleValueConvertToString(weight));
                    continue;
                }
                const auto& paperInfo = paperInfos[i];
                table.setText(rowIdx, 0, ToString(i + 1));
                table.setText(rowIdx, 1, paperInfo.drawingNumber);
                table.setText(rowIdx, 2, paperInfo.edition);
                table.setText(rowIdx, 3, paperInfo.name);
                double subWeight = 0.0;
                switch (paperInfo.pType)
                {
                case  ISOPaper::PaperSize::PT_A0:
                    subWeight = 2.0;
                    break;
                case  ISOPaper::PaperSize::PT_A1:
                    subWeight = 1.0;
                    break;
                case  ISOPaper::PaperSize::PT_A2:
                    subWeight = 0.5;
                    break;
                case  ISOPaper::PaperSize::PT_A3:
                    subWeight = 0.25;
                    break;
                case  ISOPaper::PaperSize::PT_A4:
                    subWeight = 0.125;
                    break;
                default:
                    subWeight = 0.25;
                    break;
                }
                weight += subWeight;
                table.setText(rowIdx, 5, DoubleValueConvertToString(subWeight));
            }
            table.setColAlign(WDAlign::HA_Left, 1);
            table.setColAlign(WDAlign::HA_Left, 3);
            fontStyle.weight = WDFontStyle::Normal;
            table.update(painter, fontStyle);
        }
        tmpGroup = sRoot.append("g").attr(SVG_GROUP_ATTR_NAME, "signTable");
        painter.pGroup = &tmpGroup;
        // 绘制签字表
        if (idx == 0)
        {
            // 表格内容
            ISOTableWidget table(IVec2(totalIndexCnt - indexTableRowCnt, 6));
            table.position = position + DVec2(0, 236.68) * scale;
            table.tableDistribution.horizontal = ISOTableWidget::TableDistribution::Horizontal::H_AllFixed;
            table.tableDistribution.vertical = ISOTableWidget::TableDistribution::Vertical::V_Uniform;
            table.setTableHeight((totalIndexCnt - indexTableRowCnt) * tableEachColHeight);
            table.setColWidths({7.2, 48.21, 30.85, 30.85, 38.1, 26.52});
            table.outerBorderLine = innSizeLineWidth;
            table.innerBorderLine = normalLineWidth;

            table.setText(3, 0, tableInfo.edition);
            table.setText(3, 1, WD::WDTs("ISOIndexTable", "ConstructionPurposes"));
            std::string date = QDateTime::currentDateTime().toString("yyyy-MM-dd").toUtf8().data();
            table.setText(3, 5, date);
            table.setText(4, 0, WD::WDTs("TableStyle", "Version"));
            table.setText(4, 1, WD::WDTs("ISOIndexTable", "Describe"));
            table.setText(4, 2, WD::WDTs("ISOIndexTable", "Establishment"));
            table.setText(4, 3, WD::WDTs("TableStyle", "ISOCheck"));
            table.setText(4, 5, WD::WDTs("TableStyle", "Date"));
            table.update(painter, fontStyle);
        }

        painter.drawText(WD::WDTs("ISOIndexTable", "Statement"), DVec2(margeLeft / 2, height - margeBottom / 2)
            , fontStyle, WDAlign(WD::WDAlign::HA_Left, WD::WDAlign::VA_Center));

        painter.drawText(tableInfo.footer, DVec2(width - margeRight / 2, height - margeBottom / 2)
            , fontStyle, WDAlign(WD::WDAlign::HA_Right, WD::WDAlign::VA_Center));

        ret.emplace_back(svg.toString());
    }
    return ret;
}

bool    IsoIndexTable::GenExcelIndexTable(const char* filaPath
    , const PaperInfos& paperInfos
    , const IndexTableInfo& tableInfo)
{
#ifdef WIN32
    constexpr const int indexTableRowCnt = 25;
    constexpr const int totalIndexCnt = 30;
    //总行数               +2是因为索引表最后两行会用做汇总信息
    const int totalCnt = static_cast<int>(paperInfos.size()) + 2;
    // 计算图纸数量       +(totalIndexCnt - indexTableRowCnt) 是因为签署栏
    const int paperCnt = static_cast<int>(((totalCnt + (totalIndexCnt - indexTableRowCnt)) / totalIndexCnt)
        + ((totalCnt + (totalIndexCnt - indexTableRowCnt)) % totalIndexCnt > 0 ? 1 : 0));

    const auto tableSize = tableInfo.size();
    const double width   =   tableSize.x;
    const double height  =   tableSize.y;
    const double xScale = width / standardWidth;
    const double yScale = height / standardHeight;

    // 单位 : 字符
    std::array<double, 10> widths = {3.5, 10, 8.88, 2.13, 13.5, 13.5, 8.63, 2, 6, 11.25};
    for (auto& each : widths)
        each *= xScale;

    // 单位 : 磅
    std::array<double, 9> heights = {15, 21.95, 21.95, 21.95, 21.95, 21.95, 13.35, 13.35, 13.35};
    for (auto& each : heights)
        each *= yScale;

    const int row = static_cast<int>(heights.size() + totalIndexCnt);
    const int col = static_cast<int>(widths.size());

    const double tableEachColHeight = 20.1;

    // 连接excel控件
    HRESULT r = OleInitialize(0);
    if (r != S_OK && r != S_FALSE)
        return false;
    {
        QTableWidget2Excel excel;
        QAxObject* pWorkBook = excel.createNewWorkBook();
        if (pWorkBook == nullptr)
        {
            OleUninitialize();
            return false;
        }
        // 获取工作簿的工作表1
        QAxObject* pWorkSheet = QTableWidget2Excel::GetWorkSheetByIndex(*pWorkBook, 1);
        if (pWorkSheet == nullptr)
        {
            OleUninitialize();
            return false;
        }

        // 页面设置
        {
            QString leftFooter = QString::fromUtf8(WD::WDTs("ISOIndexTable", "Statement").c_str());
            QString rightFootr = QString::fromUtf8(tableInfo.footer.c_str());
            QTableWidget2Excel::QAxObjectSPtr pPageSet(pWorkSheet->querySubObject("PageSetup"));
            if (pPageSet != nullptr)
            {
                // 页边距设置
                const double margeLeft      =   14.0;
                const double margeRight     =   12.0;
                const double margeTop       =   7.0;
                const double margeBottom    =   11.0;
                
                const double marginHeader   =   30.0;
                const double marginfooter   =   3.0;

                const double footerFontSize =   0.18;
                // 设置界面间距
                pPageSet->dynamicCall("SetLeftMargin(double)",      QTableWidget2Excel::CvtMMToPoint(margeLeft   * xScale));
                pPageSet->dynamicCall("SetRightMargin(double)",     QTableWidget2Excel::CvtMMToPoint(margeRight  * xScale));
                pPageSet->dynamicCall("SetTopMargin(double)",       QTableWidget2Excel::CvtMMToPoint(margeTop    * yScale));
                pPageSet->dynamicCall("SetBottomMargin(double)",    QTableWidget2Excel::CvtMMToPoint(margeBottom * yScale));
                
                pPageSet->setProperty("FitToPagesTall", false);
                pPageSet->setProperty("FitToPagesWide", false);
                // 设置页眉页脚的间距
                pPageSet->dynamicCall("SetHeaderMargin(double)",    QTableWidget2Excel::CvtMMToPoint(marginHeader * yScale));
                pPageSet->dynamicCall("SetFooterMargin(double)",    QTableWidget2Excel::CvtMMToPoint(marginfooter * yScale));

                // 设置对齐方式
                pPageSet->dynamicCall("SetCenterHorizontally(bool)",    true);
                pPageSet->dynamicCall("SetCenterVertically(bool)",      true);
                // 设置页眉页脚及字体
                pPageSet->dynamicCall("Font.Size",      QTableWidget2Excel::CvtMMToPoint(footerFontSize));
                pPageSet->setProperty("LeftFooter",     leftFooter);
                pPageSet->setProperty("RightFooter",    rightFootr);

                pPageSet->setProperty("PrintArea",      QString("$A$1:$J$%1").arg( paperCnt * row));
                //pPageSet->setProperty("PrintTitleRows", QString("$1:$9"));
            }
        }
        pWorkSheet->setProperty("Name", QString::fromUtf8(tableInfo.tableName.c_str()));
        double weight = 0.0;
        // 收集数据
        QTableWidget2Excel::ExcelCellDatas datas;
        // 先设置一个尽量大的size避免频繁的new和delete
        datas.reserve(1000);

        WDImage data = WDImage(tableInfo.logoPath);
        // 留白
        const double left = 2.5 * xScale;
        // 获取合并后单元格的size(磅)
        const double cellWidth = QTableWidget2Excel::CvtCharToPoint(widths[0] + widths[1] + widths[2])- left;
        const double cellHeight = heights[0] + heights[1] + heights[2];
        // 这里根据单元格大小自动计算图片大小
        const auto picSize = std::fmin(cellWidth, cellHeight) * 0.6;
        //
        const double picTopMargin = (cellHeight - picSize) / 2;
        // 
        const double textLeft = left + picSize;
        const double textBoxWidth = (cellWidth - textLeft);
        const double textWidth = textBoxWidth * 0.9;
        for (int idx = 0; idx < paperCnt; ++idx)
        {
            const int currentRow = row * idx;
            auto startCell = pWorkSheet->querySubObject("Range(const QString&)", QString("A%1").arg(1 + currentRow));
            if (startCell == nullptr)
            {
                assert(false);
                OleUninitialize();
                return false;
            }
            auto targetRange = startCell->querySubObject("Resize(int, int)", row, col);
            if (targetRange == nullptr)
            {
                assert(false);
                OleUninitialize();
                return false;
            }
            for (int i = 0; i < heights.size(); ++i)
            {
                if (!QTableWidget2Excel::SetExcelRowHeight(*targetRange, i + 1, heights[i]))
                    assert(false);
            }
            for (int rowIndex = 0; rowIndex < totalIndexCnt; ++rowIndex)
            {
                if (!QTableWidget2Excel::SetExcelRowHeight(*targetRange, static_cast<int>(heights.size()) + 1 + rowIndex, tableEachColHeight))
                    assert(false);
            }
            for (int i = 0; i < widths.size(); ++i)
            {
                if (!QTableWidget2Excel::SetExcelColWidth(*targetRange, i + 1, widths[i]))
                    assert(false);
            }
            targetRange->dynamicCall("Clear()");
            // 表头数据
            {
                datas.emplace_back(QTableWidget2Excel::CellData());
                datas.back().index.setIndex(QString("A%1").arg(2 + currentRow), QString("C%1").arg(4 + currentRow));
                datas.back().value = QString("");
                // 这里特殊处理LOGO
                {
                    // 获取合并后单元格的坐标(mm)
                    auto pCell = QTableWidget2Excel::QAxObjectSPtr(QTableWidget2Excel::CellIndex(QString("A%1").arg(2 + currentRow))
                        .getCell(*pWorkSheet));
                    if (pCell != nullptr)
                    {
                        double top = pCell->property("Top").toDouble();

                        // log坐标
                        std::array<double, 2> logIndex = {left, top + picTopMargin};
                        // log大小
                        std::array<double, 2> logSize = {picSize, picSize};
                        // 绘制logo
                        QTableWidget2Excel::ExcelDrawImage(*pWorkSheet, data, logIndex, logSize);

                        // 文字坐标
                        std::array<double, 2> textIndex = {textLeft, top};
                        // 文字大小
                        std::array<double, 2> textSize = {textWidth, cellHeight};
                        // 绘制文字
                        QTableWidget2Excel::DrawTextByIndex(*pWorkSheet, QString::fromUtf8(tableInfo.companyName.c_str()), textIndex, textSize);
                    }
                }
                // 表格名称
                datas.emplace_back(QTableWidget2Excel::CellData());
                datas.back().index.setIndex(QString("D%1").arg(2 + currentRow), QString("F%1").arg(4 + currentRow));
                datas.back().value = QString::fromUtf8(tableInfo.tableName.c_str());
                datas.back().font.size = 15;
                datas.back().font.bBold = true;
                // 项目文件号
                datas.emplace_back(QTableWidget2Excel::CellData());
                datas.back().index.setIndex(QString("G%1").arg(2 + currentRow));
                datas.back().value = QString::fromUtf8(WD::WDTs("ISOIndexTable", "FileNumber").c_str());

                datas.emplace_back(QTableWidget2Excel::CellData());
                datas.back().index.setIndex(QString("H%1").arg(2 + currentRow), QString("J%1").arg(2 + currentRow));
                datas.back().value = QString::fromUtf8(tableInfo.projectFileNumber.c_str());
                // 文表号
                datas.emplace_back(QTableWidget2Excel::CellData());
                datas.back().index.setIndex(QString("G%1").arg(3 + currentRow));
                datas.back().value = QString::fromUtf8(WD::WDTs("ISOIndexTable", "DocumentNumber").c_str());

                datas.emplace_back(QTableWidget2Excel::CellData());
                datas.back().index.setIndex(QString("H%1").arg(3 + currentRow), QString("J%1").arg(3 + currentRow));
                datas.back().value = QString::fromUtf8(tableInfo.documentNumber.c_str());
                // 版次
                datas.emplace_back(QTableWidget2Excel::CellData());
                datas.back().index.setIndex(QString("G%1").arg(4 + currentRow));
                datas.back().value = QString::fromUtf8(WD::WDTs("TableStyle", "Version").c_str());

                datas.emplace_back(QTableWidget2Excel::CellData());
                datas.back().index.setIndex(QString("H%1").arg(4 + currentRow));
                datas.back().value = QString::fromUtf8(tableInfo.edition.c_str());

                datas.emplace_back(QTableWidget2Excel::CellData());
                datas.back().index.setIndex(QString("I%1").arg(4 + currentRow), QString("J%1").arg(4 + currentRow));
                char paperIndexStr[1024] = {0};
                sprintf_s(paperIndexStr, sizeof(paperIndexStr), WD::WDTs("ISOIndexTable", "PageNumber").c_str(), idx + 1, paperCnt);
                datas.back().value = QString::fromUtf8(paperIndexStr);
                // 工程名称
                datas.emplace_back(QTableWidget2Excel::CellData());
                datas.back().index.setIndex(QString("A%1").arg(5 + currentRow), QString("B%1").arg(5 + currentRow));
                datas.back().value = QString::fromUtf8(WD::WDTs("ISOIndexTable", "ProjectName").c_str());

                datas.emplace_back(QTableWidget2Excel::CellData());
                datas.back().index.setIndex(QString("C%1").arg(5 + currentRow), QString("F%1").arg(5 + currentRow));
                datas.back().value = QString::fromUtf8(tableInfo.engineeringName.c_str());
                datas.back().font.hAlign = QTableWidget2Excel::ExcelFont::HAlign::HA_Left;
                // 单元名称
                datas.emplace_back(QTableWidget2Excel::CellData());
                datas.back().index.setIndex(QString("G%1").arg(5 + currentRow));
                datas.back().value = QString::fromUtf8(WD::WDTs("ISOIndexTable", "Units").c_str());

                datas.emplace_back(QTableWidget2Excel::CellData());
                datas.back().index.setIndex(QString("H%1").arg(5 + currentRow), QString("J%1").arg(5 + currentRow));
                datas.back().value = QString::fromUtf8(tableInfo.unitName.c_str());
                datas.back().font.hAlign = QTableWidget2Excel::ExcelFont::HAlign::HA_Left;
                // 业主文件编号
                datas.emplace_back(QTableWidget2Excel::CellData());
                datas.back().index.setIndex(QString("A%1").arg(6 + currentRow), QString("B%1").arg(6 + currentRow));
                datas.back().value = QString::fromUtf8(WD::WDTs("ISOIndexTable", "OwnDocumentNumber").c_str());

                datas.emplace_back(QTableWidget2Excel::CellData());
                datas.back().index.setIndex(QString("C%1").arg(6 + currentRow), QString("J%1").arg(6 + currentRow));
                datas.back().value = QString::fromUtf8(tableInfo.ownerFileNumber.c_str());
                datas.back().font.hAlign = QTableWidget2Excel::ExcelFont::HAlign::HA_Left;
            }
            // 绘制索引表
            {
                //顺序号
                datas.emplace_back(QTableWidget2Excel::CellData());
                datas.back().index.setIndex(QString("A%1").arg(7 + currentRow), QString("A%1").arg(9 + currentRow));
                datas.back().value = QString::fromUtf8(WD::WDTs("ISOIndexTable", "SequenceNumber").c_str());
                datas.back().font.bBold = true;
                datas.back().font.bWrapText = true;

                // 文表(图)号
                datas.emplace_back(QTableWidget2Excel::CellData());
                datas.back().index.setIndex(QString("B%1").arg(7 + currentRow), QString("C%1").arg(9 + currentRow));
                datas.back().value = QString::fromUtf8(WD::WDTs("ISOIndexTable", "SequenceDrawNumber").c_str());
                datas.back().font.bBold = true;
                // 版次
                datas.emplace_back(QTableWidget2Excel::CellData());
                datas.back().index.setIndex(QString("D%1").arg(7 + currentRow), QString("D%1").arg(9 + currentRow));
                datas.back().value = QString::fromUtf8(WD::WDTs("TableStyle", "Version").c_str());
                datas.back().font.bWrapText = true;
                datas.back().font.bBold = true;
                // 名称
                datas.emplace_back(QTableWidget2Excel::CellData());
                datas.back().index.setIndex(QString("E%1").arg(7 + currentRow), QString("F%1").arg(9 + currentRow));
                datas.back().value = QString::fromUtf8(WD::WDTs("ISOIndexTable", "IndexTableName").c_str());
                datas.back().font.bBold = true;
                // 文件数量
                datas.emplace_back(QTableWidget2Excel::CellData());
                datas.back().index.setIndex(QString("G%1").arg(7 + currentRow), QString("I%1").arg(7 + currentRow));
                datas.back().value = QString::fromUtf8(WD::WDTs("ISOIndexTable", "NumberOfFiles").c_str());
                datas.back().font.bBold = true;
                // 文表(A4)
                datas.emplace_back(QTableWidget2Excel::CellData());
                datas.back().index.setIndex(QString("G%1").arg(8 + currentRow), QString("G%1").arg(9 + currentRow));
                datas.back().value = QString::fromUtf8(WD::WDTs("ISOIndexTable", "SequenceNumberA4").c_str());
                datas.back().font.bBold = true;
                datas.back().font.bWrapText = true;
                // 图纸(A1)
                datas.emplace_back(QTableWidget2Excel::CellData());
                datas.back().index.setIndex(QString("H%1").arg(8 + currentRow), QString("I%1").arg(9 + currentRow));
                datas.back().value = QString::fromUtf8(WD::WDTs("ISOIndexTable", "PaperNumberA1").c_str());
                datas.back().font.bBold = true;
                datas.back().font.bWrapText = true;
                // 备注
                datas.emplace_back(QTableWidget2Excel::CellData());
                datas.back().index.setIndex(QString("J%1").arg(7 + currentRow), QString("J%1").arg(9 + currentRow));
                datas.back().value = QString::fromUtf8(WD::WDTs("TableStyle", "Notes").c_str());
                datas.back().font.bBold = true;

                int rowIdx = static_cast<int>(heights.size()) + currentRow;
                int currentRowCnt = idx == 0 ? 0 : (indexTableRowCnt + (idx - 1) * totalIndexCnt);
                for (int i = currentRowCnt; i < (indexTableRowCnt + idx * totalIndexCnt); ++i)
                {
                    ++rowIdx;
                    std::array<QString, 7> texts;
                    if (i == totalCnt - 2)
                    {
                        texts[0] = QString::number(paperInfos.size() + 1);
                        texts[1] = QString::fromUtf8(tableInfo.documentNumber.c_str());
                        texts[2] = QString::fromUtf8(tableInfo.edition.c_str());
                        texts[3] = QString::fromUtf8(tableInfo.tableName.c_str());
                        texts[4] = QString::number(paperCnt);
                    }
                    else if (i == totalCnt - 1)
                    {
                        texts[3] = QString::fromUtf8(WD::WDTs("ISOIndexTable", "SubTotal").c_str());
                        texts[4] = QString::number(paperCnt);
                        texts[5] = QString::number(weight);
                    }
                    else if (i < totalCnt)
                    {
                        const auto& paperInfo = paperInfos[i];
                        texts[0] = QString::number(i + 1);
                        texts[1] = QString::fromUtf8(paperInfo.drawingNumber.c_str());
                        texts[2] = QString::fromUtf8(paperInfo.edition.c_str());
                        texts[3] = QString::fromUtf8(paperInfo.name.c_str());
                        double subWeight = 0.0;
                        switch (paperInfo.pType)
                        {
                        case  ISOPaper::PaperSize::PT_A0:
                            subWeight = 2.0;
                            break;
                        case  ISOPaper::PaperSize::PT_A1:
                            subWeight = 1.0;
                            break;
                        case  ISOPaper::PaperSize::PT_A2:
                            subWeight = 0.5;
                            break;
                        case  ISOPaper::PaperSize::PT_A3:
                            subWeight = 0.25;
                            break;
                        case  ISOPaper::PaperSize::PT_A4:
                            subWeight = 0.125;
                            break;
                        default:
                            subWeight = 0.25;
                            break;
                        }
                        weight += subWeight;
                        texts[5] = QString::number(subWeight);
                    }
                    datas.emplace_back(QTableWidget2Excel::CellData());
                    datas.back().index.setIndex(QString("A%1").arg(rowIdx));
                    datas.back().value = texts[0];

                    datas.emplace_back(QTableWidget2Excel::CellData());
                    datas.back().index.setIndex(QString("B%1").arg(rowIdx), QString("C%1").arg(rowIdx));
                    datas.back().value = texts[1];
                    datas.back().font.hAlign = QTableWidget2Excel::ExcelFont::HAlign::HA_Left;

                    datas.emplace_back(QTableWidget2Excel::CellData());
                    datas.back().index.setIndex(QString("D%1").arg(rowIdx));
                    datas.back().value = texts[2];

                    datas.emplace_back(QTableWidget2Excel::CellData());
                    datas.back().index.setIndex(QString("E%1").arg(rowIdx), QString("F%1").arg(rowIdx));
                    datas.back().value = texts[3];
                    datas.back().font.hAlign = QTableWidget2Excel::ExcelFont::HAlign::HA_Left;

                    datas.emplace_back(QTableWidget2Excel::CellData());
                    datas.back().index.setIndex(QString("G%1").arg(rowIdx));
                    datas.back().value = texts[4];

                    datas.emplace_back(QTableWidget2Excel::CellData());
                    datas.back().index.setIndex(QString("H%1").arg(rowIdx), QString("I%1").arg(rowIdx));
                    datas.back().value = texts[5];

                    datas.emplace_back(QTableWidget2Excel::CellData());
                    datas.back().index.setIndex(QString("J%1").arg(rowIdx));
                    datas.back().value = texts[6];
                }
            }
            // 开始设置数据之前给部分边框设置为粗线
            std::vector<QTableWidget2Excel::CellIndex> needBoldLineIndexs;
            // 绘制签字表
            auto tableIdxCnt = totalIndexCnt - indexTableRowCnt;
            if (idx == 0 && tableIdxCnt > 0 && tableIdxCnt < row)
            {
                int rowIdx = row - tableIdxCnt + currentRow;
                for (int i = 0; i < totalIndexCnt - indexTableRowCnt; ++i)
                {
                    ++rowIdx;
                    std::array<QString, 6> texts;
                    if (rowIdx == row - 1)
                    {
                        texts[0] = QString::fromUtf8(tableInfo.edition.c_str());
                        texts[1] = QString::fromUtf8(WD::WDTs("ISOIndexTable", "ConstructionPurposes").c_str());
                        texts[5] = QDateTime::currentDateTime().toString("yyyy-MM-dd").toUtf8().data();
                    }
                    else if (rowIdx == row)
                    {
                        texts[0] = QString::fromUtf8(WD::WDTs("TableStyle", "Version").c_str());
                        texts[1] = QString::fromUtf8(WD::WDTs("ISOIndexTable", "Describe").c_str());
                        texts[2] = QString::fromUtf8(WD::WDTs("ISOIndexTable", "Establishment").c_str());
                        texts[3] = QString::fromUtf8(WD::WDTs("TableStyle", "ISOCheck").c_str());
                        texts[5] = QString::fromUtf8(WD::WDTs("TableStyle", "Date").c_str());
                    }
                    datas.emplace_back(QTableWidget2Excel::CellData());
                    datas.back().index.setIndex(QString("A%1").arg(rowIdx));
                    datas.back().value = texts[0];

                    datas.emplace_back(QTableWidget2Excel::CellData());
                    datas.back().index.setIndex(QString("B%1").arg(rowIdx), QString("D%1").arg(rowIdx));
                    datas.back().value = texts[1];

                    datas.emplace_back(QTableWidget2Excel::CellData());
                    datas.back().index.setIndex(QString("E%1").arg(rowIdx));
                    datas.back().value = texts[2];

                    datas.emplace_back(QTableWidget2Excel::CellData());
                    datas.back().index.setIndex(QString("F%1").arg(rowIdx));
                    datas.back().value = texts[3];

                    datas.emplace_back(QTableWidget2Excel::CellData());
                    datas.back().index.setIndex(QString("G%1").arg(rowIdx), QString("I%1").arg(rowIdx));
                    datas.back().value = texts[4];

                    datas.emplace_back(QTableWidget2Excel::CellData());
                    datas.back().index.setIndex(QString("J%1").arg(rowIdx));
                    datas.back().value = texts[5];
                }

                needBoldLineIndexs.emplace_back(QTableWidget2Excel::CellIndex(QString("A%1").arg(10 + currentRow)
                    , QString("J%1").arg(row - tableIdxCnt + currentRow)));
                needBoldLineIndexs.emplace_back(QTableWidget2Excel::CellIndex(QString("A%1").arg(row - tableIdxCnt + 1 + currentRow)
                    , QString("J%1").arg(row + currentRow)));
            }
            else
            {
                needBoldLineIndexs.emplace_back(QTableWidget2Excel::CellIndex(QString("A%1").arg(10 + currentRow)
                    , QString("J%1").arg(row + currentRow)));
            }
            needBoldLineIndexs.emplace_back(QTableWidget2Excel::CellIndex(QString("A%1").arg(2 + currentRow)
                , QString("C%1").arg(4 + currentRow)));
            needBoldLineIndexs.emplace_back(QTableWidget2Excel::CellIndex(QString("D%1").arg(2 + currentRow)
                , QString("F%1").arg(4 + currentRow)));
            needBoldLineIndexs.emplace_back(QTableWidget2Excel::CellIndex(QString("G%1").arg(2 + currentRow)
                , QString("J%1").arg(4 + currentRow)));
            needBoldLineIndexs.emplace_back(QTableWidget2Excel::CellIndex(QString("A%1").arg(5 + currentRow)
                , QString("J%1").arg(6 + currentRow)));
            needBoldLineIndexs.emplace_back(QTableWidget2Excel::CellIndex(QString("A%1").arg(7 + currentRow)
                , QString("J%1").arg(9 + currentRow)));

            for (auto& index : needBoldLineIndexs)
            {
                auto pRange = index.getCell(*pWorkSheet, false);
                if (pRange == nullptr)
                    continue;
                QTableWidget2Excel::SetRangeLindWidth(*pRange, 2, 3);
            }
        }
        // 设置数据
        QTableWidget2Excel::SetSheetData(*pWorkSheet, datas);
#if 0
        // 打印预览设置
        QTableWidget2Excel::SetPrintRange(*pWorkSheet, std::array<int, 2>{row, static_cast<int>(widths.size())}, std::array<int, 2>{paperCnt, 1});
#endif
        pWorkSheet->deleteLater();
        QTableWidget2Excel::SaveWorkBook(QString::fromUtf8(filaPath), *pWorkBook);
        // 关闭工作簿
        pWorkBook->dynamicCall("Close");
        pWorkBook->deleteLater();
    }

    OleUninitialize();
#endif
    return true;
}