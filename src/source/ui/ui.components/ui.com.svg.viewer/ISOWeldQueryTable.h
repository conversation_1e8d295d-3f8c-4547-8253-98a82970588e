#pragma once

#include "core/math/Math.hpp"

WD_NAMESPACE_BEGIN

class ISODrawContext;
/**
 * @brief ISO焊点类型的查询表
*/
class ISOWeldQueryTable 
{
public:
    /**
     * @brief 焊缝类型, 包含工厂和现场类型
    */
    enum RWeldType
    {
        RWT_Unknown = 0,
        // 现场对接焊
        RWT_SiteButt,
        // 现场承插焊
        RWT_SiteSocket,
        // 工厂对接焊
        RWT_FactoryButt,
        // 工厂承插焊
        RWT_FactorySocket,
        // 螺纹接头(不分工厂或现场)
        RWT_ScrewedJoint,
    };
public:
    static inline ISOWeldQueryTable& Get() 
    {
        return _s_self;
    };
public:
    ~ISOWeldQueryTable();
private:
    ISOWeldQueryTable();
private:
    /**
     * @brief 焊缝类型, 不包含工厂和现场类型
    */
    enum TWeldType
    {
        TWT_Unknown = 0,
        // 对接焊
        TWT_Butt,
        // 承插焊
        TWT_Socket,
        // 螺纹接头
        TWT_ScrewedJoint,
    };
    static ISOWeldQueryTable _s_self;
    std::map<std::pair<std::string, std::string>, TWeldType> _map;
public:
    /**
     * @brief 读取配置文件, 将配置文件的内容追加到现有的连接表中
    */
    bool appendFromXml(const std::string_view& fileName);
    /**
     * @brief 根据连接类型以及工厂标记查询焊缝类型
    */
    RWeldType query(const std::string& cType1
        , const std::string& cType2
        , bool bShop) const;
public:
    /**
     * @brief 绘制焊缝
     * @param cxt ISO绘制上下文
     * @param type 焊缝类型
     * @param pos 焊缝位置
     * @param dir 焊缝朝向
     * @param normal 焊缝法向
    */
    static bool DrawWeld(ISODrawContext& cxt
        , RWeldType type
        , const DVec3& pos
        , const DVec3& dir
        , const DVec3& normal);
};

WD_NAMESPACE_END


