#include    "ISOPaperMgr.h"

ISOPaperMgr::ISOPaperMgr()
{
    _currentPaper = nullptr;
    _defaultPaper = new WD::ISOPaper;
    _defaultPaper->update(std::nullopt);
}

ISOPaperMgr::~ISOPaperMgr()
{
    for (auto& each : _papers)
    {
        auto& paper = each.pPaper;
        if (paper != nullptr)
            delete paper;
    }
    _papers.clear();
    if (_defaultPaper != nullptr)
        delete _defaultPaper;
}

bool ISOPaperMgr::addPaper(const QString& name, WD::ISOPaper* paper, WD::ISOPaper::PaperSize type)
{
    if (findPaper(name) != nullptr)
        return false;
    _papers.push_back({name, paper, type});
    return true;
}
bool ISOPaperMgr::insertPaper(int index, const QString& name, WD::ISOPaper* paper)
{
    if (findPaper(name) != nullptr)
        return false;
    _papers.insert(_papers.begin() + index, {name, paper});
    return true;
}
bool ISOPaperMgr::insertPaper(int index, PaperItem item)
{
    if (findPaper(item.name) != nullptr)
        return false;
    _papers.insert(_papers.begin() + index, item);
    return true;
}
WD::ISOPaper* ISOPaperMgr::findPaper(const QString& name)
{
    for (auto& each : _papers)
    {
        if (each.name == name)
            return each.pPaper;
    }
    return nullptr;
}
WD::ISOPaper* ISOPaperMgr::getPaper(int index)
{
    if (index >= _papers.size())
        return nullptr;
    return _papers[index].pPaper;
}
ISOPaperMgr::PaperItem* ISOPaperMgr::findPaperItem(const QString& name)
{
    for (auto& each : _papers)
    {
        if (each.name == name)
            return &each;
    }
    return nullptr;
}
ISOPaperMgr::PaperItem* ISOPaperMgr::getPaperItem(int index)
{
    if (index >= _papers.size())
        return nullptr;
    return &(_papers[index]);
}

void ISOPaperMgr::deletePaper(const QString& name)
{
    for (int i = 0; i < _papers.size(); ++i)
    {
        if (_papers[i].name == name)
        {
            delete _papers[i].pPaper;
            _papers.erase(_papers.begin() + i);
        }
    }
}

void ISOPaperMgr::deletePaper(int index)
{
    if (index >= _papers.size())
        return;
    if (_papers[index].pPaper != nullptr)
        delete _papers[index].pPaper;
    _papers.erase(_papers.begin() + index);
}