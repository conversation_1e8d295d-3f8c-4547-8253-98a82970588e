#pragma once
#include <QPointF>
#include "core/math/Math.hpp"
#include "WDRapidxml.h"
#include "../../../utilLib/util.svgTool/WDSvgXmlCreator.h"

class ISOSvgItemHelper
{
public:
	ISOSvgItemHelper();
	~ISOSvgItemHelper();
    /**
     * @brief 将角度转化到0-360之间
     * @param angle 传入角度
     * @return 转化后的角度
    */
    static int NormalizeAngle(int angle);
    /**
     * @brief 正则解析path字符串
     * @param str
     * @return 坐标集
    */
    static std::vector< WD::DVec2>  RegexPathParser(const QString& str);
    /**
     * @brief path形图元点集转化为字符串
     * @param points
     * @param str
    */
    static void PointToPathStr(const std::vector< WD::DVec2>& points, QString& str);
    /**
     * @brief 正则解析group附带的矩阵数据
     * @param str
     * @param rotateCenter
     * @param angle
    */
    static void RegexMatrixParser(const QString& str, WD::DMat4& matrix,WD::DVec2& rotateCenter);
    /**
     * @brief 将旋转矩阵数据转为svg字符串
     * @param rotateCenter 
     * @param matrix 
    */
    static QString RotateMatrixToStr(const  WD::DVec2& rotateCenter, const WD::DMat4& matrix);
    /**
     * @brief 提取旋转矩阵角度，构造出一个以新的中心旋转的矩阵
     * @param rotateCenter 新的旋转中心
     * @param matrix 传入旋转矩阵
     * @return 返回一个新的旋转矩阵，角度不变，旋转中心改变
    */
    static const WD::DMat4 TransLateRotateMatrix(const  WD::DVec2& rotateCenter, const WD::DMat4& matrix);
    /**
     * @brief 找到一组点的最大最小值
     * @param points 
     * @param min 
     * @param max 
    */
    static void FindMaxMinPoints(const std::vector< WD::DVec2>& points, WD::DVec2& min, WD::DVec2& max);
    /**
     * @brief 子节点拷贝
     * @param pNewNode 
     * @param pOldNode 
    */
    static void CopyChildNode(WD::WDSvgXmlCreatorNode* pNewNode, WD::XMLNode* pOldNode);
    /**
     * @brief 对点二维点应用矩阵
     * @param matrix 
     * @param point 
     * @return 二维点
    */
    static const  WD::DVec2 PointApplyMatrix(const WD::DMat4& matrix, const WD::DVec2& point);
    /**
     * @brief WD::Color转为QString字符串
     * @param color 
     * @param str 
     * @return 
    */
    static const  void ColorToString(const WD::Color color, QString& str,bool highLight = false);
    /**
     * @brief 由QString转为WD::Color
     * @param str 
     * @param color 
     * @return 
    */
    static const  void ColorFromString(const  QString& str, WD::Color& color);
    /**
     * @brief 计算点到线段的距离
     * @param point 待计算的点
     * @param lineStart 线段起点
     * @param lineEnd 线段终点
     * @return 
    */
    static double CalculatePointToLineDistance(WD::DVec2 point, WD::DVec2 lineStart, WD::DVec2 lineEnd)
    {
        //方向向量
        auto direction = lineEnd - lineStart;
        //start到point的向量
        auto direction2 = point - lineStart;
        //叉积绝对值除以模长
        return (std::abs(direction2.x * direction.y - direction2.y * direction.x)) / (std::sqrt(direction.x * direction.x + direction.y * direction.y));
    }

    /**
     * @brief 根据一条线段的两个端点和线宽，算出线的占用矩形范围，并返回矩形的顶点
    */
    static std::vector<WD::DVec2> CalculateRect(WD::DVec2 start, WD::DVec2 end,double width);


    struct Ray2D
    {
        // 射线端点
        WD::DVec2 origin;
        // 射线方向
        WD::DVec2 direction;
    };

    /**
       * @brief 线段与包围盒相交 (Slabs Method 方式)
       * @param  startPos 线段的端点
       * @param  endPos 线段的端点
       * @return 返回值分别表示:
       *   - 交点个数: 取值范围[0,2]
       *       - = 0 时,表示不相交
       *       - = 1 时,表示只有一个交点(或者两个交点重合)
       *       - = 2 时,表示有两个交点(或者两个交点重合)
       *   - 近交点距离: 当值 < 0 时，表示无效
       *       - 交点个数 = 0 时,该值无效
       *       - 交点个数 >= 1 时,数组的第一个值
       *   - 远交点距离: 当值 < 0 时，表示无效
       *       - 交点个数 = 0 时,该值无效
       *       - 交点个数 >= 1 时,数组的第二个值
       */
       //template<typename double = double>
    static std::pair<uint, std::pair<double, double> >  Intersect(const WD::DVec2& startPos, const WD::DVec2& endPos, const WD::TAabb2<double>& aabb) {
        double tmin;
        double tmax;
        double tymin;
        double tymax;

        if (aabb.contains(startPos) && aabb.contains(endPos))
        {
            return std::make_pair(0, std::make_pair(double(-1), double(-1)));
        }
        Ray2D ray;
        ray.origin = startPos;
        ray.direction = endPos - startPos;

        const double invdirx = 1 / ray.direction.x;
        const double invdiry = 1 / ray.direction.y;

        const WD::TVec2<double>& tOrigin = ray.origin;

        if (invdirx >= 0)
        {
            tmin = (aabb.min.x - tOrigin.x) * invdirx;
            tmax = (aabb.max.x - tOrigin.x) * invdirx;

        }
        else
        {
            tmin = (aabb.max.x - tOrigin.x) * invdirx;
            tmax = (aabb.min.x - tOrigin.x) * invdirx;
        }

        if (invdiry >= 0)
        {
            tymin = (aabb.min.y - tOrigin.y) * invdiry;
            tymax = (aabb.max.y - tOrigin.y) * invdiry;
        }
        else
        {
            tymin = (aabb.max.y - tOrigin.y) * invdiry;
            tymax = (aabb.min.y - tOrigin.y) * invdiry;
        }

        if ((tmin > tymax) || (tymin > tmax))
            return std::make_pair(0, std::make_pair(double(-1), double(-1)));

        // These lines also handle the case where tmin or tmax is NaN
        // (result of 0 * Infinity). x !== x returns true if x is NaN

        if (tymin > tmin || tmin != tmin)
            tmin = tymin;

        if (tymax < tmax || tmax != tmax)
            tmax = tymax;

        //if ((tmin > tzmax) || (tzmin > tmax))
        //    return std::make_pair(0, std::make_pair(double(-1), double(-1)));
        //
        //if (tzmin > tmin || tmin != tmin)
        //    tmin = tzmin;
        //
        //if (tzmax < tmax || tmax != tmax)
        //    tmax = tzmax;

        //return point closest to the ray (positive side)

        if (tmax < 0 || tmin > 1)
            return std::make_pair(0, std::make_pair(double(-1), double(-1)));
        if (tmin < 0 || tmax > 1)
            return std::make_pair(1, std::make_pair(tmax, tmax));

        return std::make_pair(2, std::make_pair(tmin, tmax));
    }

};