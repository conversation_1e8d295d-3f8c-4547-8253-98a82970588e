#include "ISOGraphics.h"



const ISOGraphics::Type& ISOGraphics::type() const
{
	return _type;
}
ISOGraphics::ISOFlags& ISOGraphics::flags()
{
	return _flags;
}
const WD::DAabb2& ISOGraphics::aabb() const
{
	return _aabb;
}

void ISOGraphics::setParentMatrix(const WD::DMat4& parentTransform)
{
	_parentTransform = parentTransform;
}

const WD::DMat4& ISOGraphics::parentMatrix() const
{
	return _parentTransform;
}
const WD::DMat4& ISOGraphics::matrix() const
{
	return _transform;
}
const WD::DMat4 ISOGraphics::inverse() const
{
	WD::DMat4 invMat = _transform;
	invMat.invert();
	return invMat;
}
void  ISOGraphics::setMatrix(const WD::DMat4& transform)
{
	_transform = transform;
	_flags.addFlag(ISOFlag_Update);
}
void  ISOGraphics::updateMatrix(const WD::DMat4& transform)
{
	_transform = transform * _transform;
	_flags.addFlag(ISOFlag_Update);
}
void ISOGraphics::update(bool bForeceUpdate)
{
	if (!bForeceUpdate && !_flags.hasFlag(ISOFlag_Update))
		return;
	_flags.removeFlag(ISOFlag_Update);

	this->onUpdate();
}

std::pair<bool, WD::DVec2> ISOGraphics::pickup(const WD::DVec2& point, bool sceneIntersect, const double& pixelDeviation) const
{
	WDUnused(pixelDeviation);
	WDUnused(sceneIntersect);
	WDUnused(point);
	return std::make_pair(false, WD::DVec2::Zero());
}
ISOGraphics::FrameSelectResult ISOGraphics::frameSelect(const WD::DAabb2& aabb) const
{
	WDUnused(aabb);
	return FrameSelectResult::FST_None;
}
void ISOGraphics::move(const WD::DVec2& offset)
{
	WDUnused(offset);
}
