#pragma once
#include "ISOGraphics.h"
#include <QString>

class ISOGraphicsText : public ISOGraphics
{
public:
	ISOGraphicsText(Type type);
	~ISOGraphicsText();
public:
	/**
	 * @brief 设置文本内容
	 * @param text 
	*/
	void setText(QString text);
	/**
	 * @brief 返回文本内容
	 * @return 
	*/
	const QString& text() const;
	/**
	 * @brief 设置字体大小
	 * @param size 
	*/
	void setFontSize(double size);
	/**
	 * @brief 返回字体大小
	 * @return 
	*/
	double fontSize() const;
	/**
	 * @brief 设置字体起点(x最小,y最大点)
	 * @param pos 
	*/
	void setPos(WD::DVec2 pos);
	/**
	 * @brief 获取字体起点
	 * @return 
	*/
	const WD::DVec2& pos() const;
public:
	/**
	 * @brief 拾取
	 * @param point 拾取的位置
	 * @return 是否拾取到当前对象，如果拾取到当前对象，则可以获取拾取的点
	*/
	virtual std::pair<bool, WD::DVec2> pickup(const WD::DVec2& point, bool sceneIntersect = true, const double& pixelDeviation = 0) const override;
	/**
	 * @brief 框选功能
	 * @param selectType 拾取的类型
	 * @param aabb 场景中框选的包围盒
	 * @return 拾取结果
	*/
	virtual FrameSelectResult frameSelect(const WD::DAabb2& frameAabb) const override;
	/**
	 * @brief 移动对象
	 * @param offset 移动偏移量
	*/
	virtual void move(const WD::DVec2& offset) override;
	/**
	 * @brief 更新矩阵，应用矩阵变换
	*/
	virtual void updateMatrix(const WD::DMat4& transform) override;
protected:
	/**
	 * @brief 触发更新
	*/
	virtual void onUpdate() override;
protected:
	void updateAabb();
private:
	//计算占用矩形的四个顶点(矩阵变换之前)
	inline const std::vector<WD::DVec2> calVertSets() const;
private:
	QString _text = "";
	double     _fontSize = 12.0;
	WD::DVec2 _pos = WD::DVec2(0, 0);
	QString _fontFamily = "SimSun";
	int		_fontWeight = 50;
	QString _fontStyle = "normal";
};