#pragma once
#include "ISOGraphics.h"

class GraphicalObjectManager
{
public:
	GraphicalObjectManager();
	~GraphicalObjectManager();
public:
	//从xml中读取图元数据
	void loadFromXml(WD::XMLDoc* pDoc);
	/**
	 * @brief 保存所有图元到xml
	 * @param pDoc 
	*/
	void saveToXml(WD::XMLDoc* pDoc) const;
	/**
	 * @brief 添加图元到图元列表
	 * @param item 
	*/
	void addGraphics(ISOGraphics* item);
	/**
	 * @brief 从图元列表中删除图元
	 * @param item 
	*/
	void removeGraphics(ISOGraphics* item);
	const std::vector<ISOGraphics*>& graphics() const
	{
		return _graphics;
	}
private:
	/**
	 * @brief 从xml节点获取图元数据
	 * @param pNode 数据节点
	*/
	static std::vector<ISOGraphics*> FromXml(WD::XMLDoc* pDoc);
	/**
	 * @brief 将数据写入xml(内存中)
	*/
	static void ToXml(WD::XMLDoc* pDoc, const std::vector<ISOGraphics*>& dataVec);
private:
	std::vector<ISOGraphics*> _graphics;
};