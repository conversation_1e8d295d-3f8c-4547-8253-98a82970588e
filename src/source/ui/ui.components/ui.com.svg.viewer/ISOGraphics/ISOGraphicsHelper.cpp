#include "ISOGraphicsHelper.h"
#include "qregularexpression.h"
#include <QDebug>


ISOSvgItemHelper::ISOSvgItemHelper()
{
}

ISOSvgItemHelper::~ISOSvgItemHelper()
{
}

int ISOSvgItemHelper::NormalizeAngle(int angle)
{
    while (angle < 0)
    {
        angle += 360;
    }
    while (angle > 360)
    {
        angle -= 360;
    }
    return angle;
}

std::vector<WD::DVec2> ISOSvgItemHelper::RegexPathParser(const QString& str)
{
    {
        std::vector< WD::DVec2> points;
        points.clear();
        WD::DVec2 point;
        QRegularExpression pointRe("([ML])(\\d*\\.?\\d+),(\\d*\\.?\\d+)");
        QString path = str;
        QRegularExpressionMatchIterator iter = pointRe.globalMatch(path);
        if (!iter.isValid())
        {
            return points;
        }
        while (iter.hasNext())
        {
            auto pointStr = iter.next();
            point.x = pointStr.captured(2).toDouble();
            point.y = pointStr.captured(3).toDouble();
            points.emplace_back(point);
        }
        return points;
    }
}

void ISOSvgItemHelper::RegexMatrixParser(const QString& str, WD::DMat4& matrix,WD::DVec2& rotateCenter)
{
    //QRegularExpression regex("rotate\\((-?\\d+\\.\\d+),(-?\\d+\\.\\d+),(-?\\d+\\.\\d+)\\");
    WD::DVec3 center(0.0,0.0,0.0);
    double angle = 0.0;
    //QRegularExpressionMatch match = regex.match(str);
    //if(match.hasMatch())
    //{
    //    angle = match.captured(1).toDouble();
    //    rotateCenter.x = (match.captured(2).toDouble());
    //    rotateCenter.y = (match.captured(3).toDouble());
    //}
    QRegularExpression regex(R"(rotate\((-?\d+\.\d+),(-?\d+\.\d+),(-?\d+\.\d+)\))");
    QRegularExpressionMatchIterator iter = regex.globalMatch(str);
    while (iter.hasNext())
    {
        auto match = iter.next();
        angle = match.captured(1).toDouble();
        center.x = (match.captured(2).toDouble());
        center.y = (match.captured(3).toDouble());
    }
    //位置偏移矩阵
    WD::DMat4 offMatT = WD::DMat4::MakeTranslation(center);
    //位置偏移矩阵的逆
    WD::DMat4 offMatTInv = WD::DMat4::MakeTranslation(-center);
    //我们的矩阵和svg的矩阵角度处理是反的，所以取-
    WD::DMat4 offMatR = WD::DMat4::MakeRotationZ(-angle);
    rotateCenter.x = center.x;
    rotateCenter.y = center.y;
    matrix = offMatT * offMatR * offMatTInv;
}

QString ISOSvgItemHelper::RotateMatrixToStr(const  WD::DVec2& rotateCenter, const WD::DMat4& matrix)
{
    if (matrix == WD::DMat4::Identity())
    {
        return "";
    }
    auto angle = atan2(matrix[1][0], matrix[0][0]);
    angle = angle * 180 / std::acos(-1);
    QString tempStr = "rotate";
    //我们的矩阵和svg的矩阵角度处理是反的，所以取-
    tempStr += QString("(%1,%2,%3)").arg(QString::number(-angle)).arg(QString::number(rotateCenter.x)).arg(QString::number(rotateCenter.y));
    return tempStr;
}

const WD::DMat4 ISOSvgItemHelper::TransLateRotateMatrix(const WD::DVec2& rotateCenter, const WD::DMat4& matrix)
{
    if (matrix == WD::DMat4::Identity())
    {
        return matrix;
    }
    auto angle = atan2(matrix[1][0], matrix[0][0]);
    angle = angle * 180 / std::acos(-1);
    //位置偏移矩阵
    WD::DMat4 offMatT = WD::DMat4::MakeTranslation(WD::DVec3(rotateCenter.x, rotateCenter.y,0));
    //位置偏移矩阵的逆
    WD::DMat4 offMatTInv = WD::DMat4::MakeTranslation(-WD::DVec3(rotateCenter.x, rotateCenter.y, 0));
    //我们的矩阵和svg的矩阵角度处理是反的，所以取-
    WD::DMat4 offMatR = WD::DMat4::MakeRotationZ(angle);
    auto resultMatrix = offMatT * offMatR * offMatTInv;
    return resultMatrix;
}

void ISOSvgItemHelper::FindMaxMinPoints(const std::vector< WD::DVec2>& points, WD::DVec2& min, WD::DVec2& max)
{
    double minX = std::numeric_limits<double>::max();
    double maxX = std::numeric_limits<double>::min();
    double minY = std::numeric_limits<double>::max();
    double maxY = std::numeric_limits<double>::min();

    for (auto& i : points)
    {
        if (i.x < minX)
            minX = i.x;
        if (i.x > maxX)
            maxX = i.x;
        if (i.y < minY)
            minY = i.y;
        if (i.y > maxY)
            maxY = i.y;
    }
    min = WD::DVec2(minX - 2,minY - 2);
    max = WD::DVec2(maxX + 2, maxY + 2);
}

void ISOSvgItemHelper::CopyChildNode(WD::WDSvgXmlCreatorNode* pNewNode, WD::XMLNode* pOldNode)
{
    if (pOldNode == nullptr)
    {
        return;
    }

    //拷贝子节点
    for (WD::XMLNode* pChildNode = pOldNode->first_node()
        ; pChildNode
        ; pChildNode = pChildNode->next_sibling())
    {
        auto newChild = pNewNode->append(pChildNode->name(), pChildNode->value());
        //拷贝子节点属性
        for (WD::XMLAttr* pAttr = pChildNode->first_attribute()
            ; pAttr
            ; pAttr = pAttr->next_attribute())
        {
            //if (strcmp(pAttr->name(), "font_family") == 0)
            //{
            //    newChild.attr("font_family", QString::fromUtf8("SimSun").toUtf8().data());
            //}
            //else
            {
                newChild.attr(pAttr->name(), pAttr->value());
            }
        }

        if (strcmp(pChildNode->name(), "text") != 0)
        {
            CopyChildNode(&newChild, pChildNode);
        }
    }

}

void ISOSvgItemHelper::PointToPathStr(const std::vector< WD::DVec2>& points, QString& str)
{
    QString newPath = "M";
    auto size = points.size();
    if (size < 1)
    {
        str = "";
    }

    for (auto& i : points)
    {
        newPath += QString("%1,%2L").arg(QString::number(i.x)).arg(QString::number(i.y));
    }
    newPath.chop(1);
    str = newPath;
}

const  WD::DVec2 ISOSvgItemHelper::PointApplyMatrix(const WD::DMat4& matrix, const  WD::DVec2& point)
{
    auto re = matrix * WD::DVec3(point.x, point.y, 0);
    return  WD::DVec2(re.x, re.y);
}

const void ISOSvgItemHelper::ColorToString(const WD::Color color, QString& str, bool highLight)
{
    //当颜色的a分量为0，则字符串为“none”，svg中none表示透明
    if (color.a == 0)
    {
        str = "none";
        return;
    }

    if (highLight)
    {
        str = QString("rgb(255,0,0)");
    }
    else
    {
        str = QString("rgb(%1,%2,%3)").arg(color.r).arg(color.g).arg(color.b);
    }
}

const void ISOSvgItemHelper::ColorFromString(const QString& str, WD::Color& color)
{
    WD::Color none;
    color = none.fromString(str.toUtf8().data());
    if (str.compare("none",Qt::CaseInsensitive) == 0)
    {
        //当颜色的a分量为0，则字符串为“none”，svg中none表示透明
        color.a = 0;
    }
    else
    {
        color.a = 255;
    }
}

std::vector<WD::DVec2> ISOSvgItemHelper::CalculateRect(WD::DVec2 start, WD::DVec2 end, double width)
{
    double dx = end.x - start.x;
    double dy = end.y - start.y;
    //直线的垂直向量
    WD::DVec2 n(-dy,dx);
    n.normalize();
    std::vector<WD::DVec2> points;
    points.reserve(4);
    points.emplace_back(WD::DVec2(start.x + n.x * width/2, start.y + n.y * width / 2));
    points.emplace_back(WD::DVec2(start.x - n.x * width / 2, start.y - n.y * width / 2));
    points.emplace_back(WD::DVec2(end.x + n.x * width / 2, end.y + n.y * width / 2));
    points.emplace_back(WD::DVec2(end.x - n.x * width / 2, end.y - n.y * width / 2));
    return points;
}