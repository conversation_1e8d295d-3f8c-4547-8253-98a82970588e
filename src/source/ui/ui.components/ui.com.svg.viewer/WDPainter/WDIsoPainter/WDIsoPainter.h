#pragma once
#include "../WDAbstractPainter2D.h"

WD_NAMESPACE_BEGIN

/**
* @brief Iso的绘制类
*/
class WDISOPainter : public WDAbstractPainter2D
{
public:
    WDISOPainter() : WDAbstractPainter2D()
    {}
    virtual ~WDISOPainter()
    {}
public:
    virtual DVec2 calcTextSize(const std::string& str, const WDFontStyle& style = WDFontStyle()) override = 0;
public:
    virtual void drawPoint(const DVec2& point
        , const WDPointStyle& style = WDPointStyle()) = 0;
    virtual void drawPoints(const std::vector<DVec2>& points
        , const WDPointStyle& style = WDPointStyle()) override = 0;
    virtual void drawLine(const DVec2& sPos, const DVec2& ePos
        , const WDLineStyle& style = WDLineStyle()) override = 0;
    virtual void drawLines(const std::vector<DVec2>& points
        , const WDLineStyle& style = WDLineStyle()) override = 0;
    virtual void drawPolyLines(const std::vector<DVec2>& points
        , const WDLineStyle& style = WDLineStyle()) override = 0;
    virtual void drawCircle(const DVec2& center, const double& r
        , const WDLineStyle& style = WDLineStyle()) override = 0;
    virtual void fillCircle(const DVec2& center, const double& r
        , const WDShapeFillStyle& style = WDShapeFillStyle()) override = 0;
    virtual void drawRect(const DVec2& posS, const DVec2& size
        , const WDLineStyle& style = WDLineStyle()) = 0;
    virtual void fillRect(const DVec2& posS, const DVec2& size
        , const WDShapeFillStyle& style = WDShapeFillStyle()) override = 0;
    virtual void drawPolygon(const std::vector<DVec2>& pts
        , const WDLineStyle& style = WDLineStyle()) = 0;
    virtual void fillPolygon(const std::vector<DVec2>& pts
        , const WDShapeFillStyle& style = WDShapeFillStyle()) override = 0;
    virtual bool drawText(const std::string& text
        , const DVec2& posS
        , const DVec2& posE
        , const WDFontStyle& style = WDFontStyle()
        , const WDAlign& textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center }
        , bool bAbleTruncationStr = false) override = 0;
    virtual void drawTextV(const std::vector<std::string>& text
        , const DVec2& posS
        , const DVec2& posE
        , const WDFontStyle& style = WDFontStyle()
        , const WDAlign& textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center }
        ) override = 0;
    virtual void drawText(const std::string& text
        , const DVec2& posS
        , const WDFontStyle& style = WDFontStyle()
        , const WDAlign& textAlign = { WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center }
        , int angle = 0) override = 0;
    virtual bool drawImage(const DVec2& pos, const WDImage& image, const std::optional<DVec2>& tarSize = std::nullopt) override = 0;
    /**
     * @brief 绘制圆弧
     * @param center 圆心
     * @param radius 半径
     * @param angleStart 起始角度
     * @param angleEnd 结束角度
     * @param style
     * @return
     */
    virtual bool drawArc(const DVec2& center,
        const double& radius,
        const double& angleStart,
        const double& angleEnd,
        const WDLineStyle& style = WDLineStyle()) = 0;
};
WD_NAMESPACE_END


