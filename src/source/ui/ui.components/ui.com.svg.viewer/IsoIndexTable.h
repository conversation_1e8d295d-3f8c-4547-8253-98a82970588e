#pragma once
#include "WDCore.h"
#include "ISOPaper.h"
#include "core/WDTranslate.h"

WD_NAMESPACE_BEGIN
/**
 * @brief 表格样式基类
*/
class IsoIndexTable
{
public:
    // 索引表信息,注意: 尺寸单位为mm, 字符串格式为utf8
    struct IndexTableInfo
    {
        // 目前只支持A4
        ISOPaper::PaperSize pType = ISOPaper::PaperSize::PT_A4;
        // 表格名称
        std::string tableName   = WD::WDTs("ISOIndexTable", "Pipe Segment Drawing Index Table");
        // 公司名称
        std::string companyName = WD::WDTs("ISOIndexTable", "CEI");
        // 公司logo路径
        std::string logoPath;
        // 项目文件号
        std::string projectFileNumber;
        // 文表号
        std::string documentNumber;
        // 版次
        std::string edition;
        // 工程名称
        std::string engineeringName;
        // 单元名称
        std::string unitName;
        // 业主文件编号
        std::string ownerFileNumber;
        // 注释
        std::string footer;
        DVec2 size() const
        {
            switch (pType)
            {
            case  ISOPaper::PaperSize::PT_A0:
                return DVec2(841.0, 1189.0);
            case  ISOPaper::PaperSize::PT_A1:
                return DVec2(594.0, 841.0);
            case  ISOPaper::PaperSize::PT_A2:
                return DVec2(420.0, 594.0);
            case  ISOPaper::PaperSize::PT_A3:
                return DVec2(297.0, 420.0);
                // 默认是A4
            default:
                return DVec2(210.0, 297.0);
            }
        }
        IndexTableInfo()
        {
            char path[1024] = { 0 };
            sprintf_s(path, sizeof(path), "%s/iso/logo/syhg.png", WD::Core().dataDirPath());
            logoPath = path;
        }
    };
public:
    using PaperInfos = std::vector<ISOPaper::PaperInfo>;
    /*
     * @brief 生成SVG格式的Iso图索引表
     * @param papers ISO图纸列表
     * @param tableInfo 索引表信息
     * @return 生成的索引表svg数据,当图纸数量过多时,会自动分割成多张索引表
    */
    static StringVector GenSvgIndexTable(const PaperInfos& paperInfos
        , const ISOUnitConvert&     ucvt
        , const IndexTableInfo&     tableInfo   =   IndexTableInfo());

    static bool GenExcelIndexTable(const char* filaPath
        , const PaperInfos& paperInfos
        , const IndexTableInfo& tableInfo);
};

WD_NAMESPACE_END