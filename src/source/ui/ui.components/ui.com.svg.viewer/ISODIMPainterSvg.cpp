#include "ISODIMPainterSvg.h"
#include "ISOBindingBoxComputer.h"
#include "viewer/dimension/WDDIMQLeader.h"


WD_NAMESPACE_BEGIN

ISOCollision::BindingBox ISOCollision::ComputeBindingBox(const ISOUnitConvert& uCvt, const CObject& object)
{
    BindingBox rBox = std::make_pair(DAabb2::Null(), DObb2::Null());
    ISOBindingBoxComputer computer(uCvt);
    size_t idx = object.index();
    switch (idx)
    {
    case 1: // CSegment
        {
            auto p = std::get_if<CSegment>(&object);
            if (p == nullptr)
                return rBox;

            computer.drawLine(p->sPos
                , p->ePos
                , p->style);
        }
        break;
    case 2: // CRect
        {
            auto p = std::get_if<CRect>(&object);
            if (p == nullptr)
                return rBox;

            computer.drawRect(p->center
                , p->size
                , p->xAxis
                , p->planeNormal
                , p->style);
        }
        break;
    case 3: // CText
        {
            auto p = std::get_if<CText>(&object);
            if (p == nullptr)
                return rBox;

            computer.drawText(p->text
                , p->position
                , p->rightDir
                , p->upDir
                , p->style
                , p->textAlign);
        }
        break;
    case 4: // CTexts
        {
            auto p = std::get_if<CTexts>(&object);
            if (p == nullptr)
                return rBox;

            computer.drawTexts(p->pos
                , p->rightDir
                , p->upDir
                , p->texts
                , p->align
                , p->rowAlign);
        }
        break;
    case 5: // CCircle
        {
            auto p = std::get_if<CCircle>(&object);
            if (p == nullptr)
                return rBox;

            computer.drawCircle(p->center
                , p->radius
                , p->planeNormal
                , p->style);
        }
        break;
    default:
        {
            return rBox;
        }
        break;
    }

    computer.update();
    if (computer.aabb().isNull() || computer.obb().isNull())
        return rBox;

    rBox.first = computer.aabb();
    rBox.second = computer.obb();

    return rBox;
}

bool ISOCollision::addObject(const CItem& item)
{
    auto rBox = ComputeBindingBox(_uCvt, item.object);
    if (rBox.first.isNull() || rBox.second.isNull())
        return false;

    _items.push_back(item);
    auto& back  = _items.back();
    back.aabb   = rBox.first;
    back.obb    = rBox.second;
    // 对于文本类型，这里默认给一个类型值
    if (back.typeId == -1 && IsTextObject(back.object))
        back.typeId = T_Text;

    return true;
}
bool ISOCollision::checkObject(const CItem& item, CItem* pOutItem) const
{
    // groupId是-1的对象，不做碰撞
    if (item.groupId == -1)
        return false;
    auto rBox = ComputeBindingBox(_uCvt, item.object);
    if (rBox.first.isNull() || rBox.second.isNull())
        return false;
    auto aItem = item;
    aItem.aabb = rBox.first;
    aItem.obb = rBox.second;
    // 对于文本类型，这里默认给一个类型值
    if (aItem.typeId == -1 && IsTextObject(aItem.object))
        aItem.typeId = T_Text;
    // 计算是否与已有标签碰撞
    bool needCollision = false;
    for (const auto& tItem : _items)
    {
        // 对于groupId是-1的对象，不做碰撞
        if (tItem.groupId == -1)
            continue;

        // 如果两个item的groupId相同,则视为同一组对象，不做碰撞
        if (aItem.groupId == tItem.groupId)
            continue;

        // 对于出现了文本对象，必须要做碰撞校验, 即: 文本对象不能与其他任何对象重叠
        if (aItem.typeId == T_Text || tItem.typeId == T_Text)
        {
            needCollision = true;
        }
        // 对于出现了两边均为引线标注的引线的对象, 必须要做碰撞校验，以防止引线交叉
        else if (aItem.typeId == WDDIMQLeader::CSegmentTypeId && tItem.typeId == WDDIMQLeader::CSegmentTypeId)
        {
            needCollision = true;
        }
        // 否则,不做碰撞计算
        else
        {
            needCollision = false;
        }

        // 不需要碰撞计算
        if (!needCollision)
            continue;

        // 先校验aabb包围盒
        if (!aItem.aabb.intersects(tItem.aabb))
            continue;
        // 再校验obb包围盒
        if (!aItem.obb.intersects(tItem.obb))
            continue;

        if (pOutItem != nullptr)
            (*pOutItem) = tItem;

        return true;
    }

    return false;
}

bool ISOCollision::IsTextObject(const CObject& object)
{
    size_t idx = object.index();
    switch (idx)
    {
    case 3: // CText
    case 4: // CTexts
        return true;
        break;
    default:
        break;
    }
    return false;
}

ISOPainter::TextSizeCalculator::TextSizeCalculator(const ISOUnitConvert& uCvt
    , const std::string_view& text
    , const WDDIMFontStyle& style)
{
    int fontSz      = Max(1, static_cast<int>(uCvt.worldToPixel(style.fontSize)));
    int borderWidth = Max(1, static_cast<int>(uCvt.worldToPixel(style.bolderWidth)));

    font.setFamily(QString::fromUtf8(style.famliy.c_str()));
    font.setPixelSize(fontSz);

    QString str = QString::fromUtf8(text.data());
    QFontMetrics fontM(font);
    int w = fontM.horizontalAdvance(str);
    int h = fontM.height();
    auto tightRect = fontM.tightBoundingRect(str);
    // 紧裹着文字像素的宽高
    this->size = DVec2(w, h);
    this->tightSize = DVec2(tightRect.width(), tightRect.height());
    // 边框宽高
    this->borderSize = DVec2::Zero();
    this->borderPosOffset = DVec2::Zero();
    if (style.bolderType != WDDIMFontStyle::BolderType::BT_None) // 这里 +2.0f 是将边框整体放大1像素，方式边框与文字边缘重合,影响美观
    {
        this->borderSize        = this->size + DVec2(borderWidth);
        this->borderPosOffset   = DVec2(- borderWidth * 0.5, borderWidth * 0.5 + 1.0);
    }
    // 
    this->halfSize          = this->size * 0.5;
    this->halfTightSize     = this->tightSize * 0.5;
    this->halfBorderSize    = this->borderSize * 0.5;
    // 计算一个最大尺寸
    this->maxSize           = DVec2::Max(this->size, this->borderSize);
    this->halfMaxSize       = this->maxSize * 0.5;
}

void ISOPainter::TextSizeCalculator::TextSizeCalculatorDxf(const ISOUnitConvert& uCvt
    , const std::string_view& text
    , const WDDIMFontStyle& style)
{
    int fontSz = Max(1, static_cast<int>(uCvt.worldToPixel(style.fontSize)));
    int borderWidth = Max(1, static_cast<int>(uCvt.worldToPixel(style.bolderWidth)));

    font.setFamily(QString::fromUtf8(style.famliy.c_str()));
    font.setPixelSize(fontSz);

    QString str = QString::fromUtf8(text.data());
    QFontMetrics fontM(font);

    int w = fontM.horizontalAdvance(str);
    auto fontHeight = fontSz * 25.4 / 72;
    fontHeight = uCvt.paperToPixel(fontHeight);
    int h = fontM.height();
    auto tightRect = fontM.tightBoundingRect(str);
    // 紧裹着文字像素的宽高
    this->size = DVec2(w, h);
    this->tightSize = DVec2(tightRect.width(), tightRect.height());
    // 边框宽高
    this->borderSize = DVec2::Zero();
    this->borderPosOffset = DVec2::Zero();
    if (style.bolderType != WDDIMFontStyle::BolderType::BT_None) // 这里 +2.0f 是将边框整体放大1像素，方式边框与文字边缘重合,影响美观
    {
        this->borderSize = DVec2(size.x, size.y) + DVec2(borderWidth);
        this->borderPosOffset = DVec2(borderWidth * 0.5, borderWidth * 0.5 + 1.0);
    }
    // 
    this->halfSize = this->size * 0.5;
    this->halfTightSize = this->tightSize * 0.5;
    this->halfBorderSize = this->borderSize * 0.5;
    // 计算一个最大尺寸
    this->maxSize = DVec2::Max(this->size, this->borderSize);
    this->halfMaxSize = this->maxSize * 0.5;
}

double ISOPainter::TextSizeCalculator::RotateAngle(const DVec2& vec)
{
    // 这里使用屏幕坐标构造一个坐标系, up为屏幕上方向:-Y, right为屏幕右方向:X, front使用up和right计算得到
    static const auto right = DVec3::AxisX();
    static const auto up    = DVec3::AxisNY();
    static const auto front = DVec3::Cross(up, right).normalized();

    DVec3 tRight = DVec3(vec.normalized());
    auto tmpDot = DVec3::Dot(tRight, up);
    if (Abs(1.0 - Abs(tmpDot)) <= 0.01)
    {
        tRight = up;
    }
    else
    {
        DVec3 tFront = DVec3::Cross(up, tRight);
        if (DVec3::Dot(tFront, front) < 0.0)
            tRight = -tRight;
    }
    double rAngle = DVec3::Angle(right, tRight);
    if (DVec3::Dot(tRight, up) > 0.0)
        rAngle = -rAngle;

    return rAngle;
}

ISOPainter::ISOPainter(const ISOUnitConvert& cvt)
    : uCvt(cvt)
{
}

ISOPainter::~ISOPainter()
{
}

DVec2 ISOPainter::calcTextSize(const std::string& text
    , const WDDIMFontStyle& style)
{
    if (text.empty())
    {
        assert(false);
        return DVec2::Zero();
    }
    TextSizeCalculator tc(uCvt, text, style);

    return uCvt.pixelToWorld(tc.maxSize);
}


ISODIMPainterSvg::ISODIMPainterSvg(ISOUnitConvert& cvt)
    : ISOPainter(cvt)
{
}

ISODIMPainterSvg::~ISODIMPainterSvg()
{
}

void ISODIMPainterSvg::drawPoints(const std::vector<DVec3>& points
    , const WDDIMPointStyle& style)
{
    WDUnused(style);
    if (pGroup == nullptr)
    {
        assert(false);
        return;
    }
    if (points.empty())
    {
        assert(false);
        return;
    }
}

void ISODIMPainterSvg::drawLine(const DVec3& sPos
    , const DVec3& ePos
    , const WDDIMLineStyle& style)
{
    if (pGroup == nullptr)
    {
        assert(false);
        return;
    }
    if (DVec3::DistanceSq(sPos, ePos) <= Epsilon)
    {
        assert(false);
        return;
    }

    auto screenS = uCvt.worldToScreen(sPos);
    auto screenE = uCvt.worldToScreen(ePos);
    // 屏幕像素单位的线宽
    int screenLineWidth  = Max(1, static_cast<int>(uCvt.worldToPixel(style.width)));
    //<line x1 = "1024" y1 = "0" x2 = "1024" y2 = "1024" stroke = "white" stroke - width = "1" / >
    auto rNode = pGroup->append("line")
        .attr("x1", screenS.x)
        .attr("y1", screenS.y)
        .attr("x2", screenE.x)
        .attr("y2", screenE.y)
        .attr("stroke", style.color)
        .attr("stroke-width", screenLineWidth);

    std::string dashStr = getDashStr(style.type);
    if (!dashStr.empty())
        rNode.attr("stroke-dasharray", dashStr);
}

void ISODIMPainterSvg::drawLines(const std::vector<DVec3>& points
    , const WDDIMLineStyle& style)
{
    if (pGroup == nullptr)
    {
        assert(false);
        return;
    }
    if (points.size() < 2)
    {
        assert(false);
        return;
    }

    std::string dashStr = getDashStr(style.type);

    // 屏幕像素单位的线宽
    int screenLineWidth = Max(1, static_cast<int>(uCvt.worldToPixel(style.width)));

    auto tmpGroup = pGroup->append("g");
    for (size_t i = 0; i < points.size() - 1; i += 2)
    {
        auto screenS = uCvt.worldToScreen(points[i]);
        auto screenE = uCvt.worldToScreen(points[i + 1]);

        auto rNode = tmpGroup.append("line")
            .attr("x1", screenS.x)
            .attr("y1", screenS.y)
            .attr("x2", screenE.x)
            .attr("y2", screenE.y)
            .attr("stroke", style.color)
            .attr("stroke-width", screenLineWidth);

        if (!dashStr.empty())
            rNode.attr("stroke-dasharray", dashStr);
    }
}

void ISODIMPainterSvg::drawBrokenLine(const std::vector<DVec3>& points
    , const WDDIMLineStyle& style, WD::Color fillColor)
{
    if (points.size() < 2)
    {
        assert(false);
        return;
    }
    // 转换到屏幕坐标
    std::vector<DVec2> screenPts;
    screenPts.reserve(points.size());
    for (size_t i = 0; i < points.size(); ++i)
    {
        screenPts.push_back(uCvt.worldToScreen(points[i]));
    }
    std::string dStr;
    dStr.reserve(screenPts.size() * 20);

    char buf[1024] = { 0 };
    sprintf_s(buf, sizeof(buf), "M%lf,%lf", screenPts.front().x, screenPts.front().y);
    dStr += buf;

    for (size_t i = 1; i < screenPts.size(); ++i)
    {
        sprintf_s(buf, sizeof(buf), "L%lf,%lf", screenPts[i].x, screenPts[i].y);
        dStr += buf;
    }

    // 屏幕像素单位的线宽
    int screenLineWidth = Max(1, static_cast<int>(uCvt.worldToPixel(style.width)));
    QString fillColorStr("none");
    if (fillColor.a != 0)
    {
        fillColorStr = QString("rgb(%1,%2,%3)").arg(fillColor.r).arg(fillColor.g).arg(fillColor.b);
    }
    auto rNode = pGroup->append("path")
        .attr("d", dStr)
        .attr("stroke", style.color)
        .attr("stroke-width", screenLineWidth)
        .attr("fill", fillColorStr.toUtf8().data());

    std::string dashStr = getDashStr(style.type);
    if (!dashStr.empty())
        rNode.attr("stroke-dasharray", dashStr);
}

void ISODIMPainterSvg::drawLoopLine(const std::vector<DVec3>& points
    , const WDDIMLineStyle& style, WD::Color fillColor)
{
    if (points.size() < 3)
    {
        assert(false);
        return;
    }
    // 转换到屏幕坐标
    std::vector<DVec2> screenPts;
    screenPts.reserve(points.size());
    for (size_t i = 0; i < points.size(); ++i)
    {
        screenPts.push_back(uCvt.worldToScreen(points[i]));
    }
    std::string dStr;
    dStr.reserve((screenPts.size() + 1) * 20);

    char buf[1024] = { 0 };
    sprintf_s(buf, sizeof(buf), "M%lf,%lf", screenPts.front().x, screenPts.front().y);
    dStr += buf;

    for (size_t i = 1; i < screenPts.size(); ++i)
    {
        sprintf_s(buf, sizeof(buf), "L%lf,%lf", screenPts[i].x, screenPts[i].y);
        dStr += buf;
    }
    // 如果指定的第一个点和最后一个点不重合，则需要连接这两个点
    if (DVec2::DistanceSq(screenPts.front(), screenPts.back()) >= Epsilon)
    {
        sprintf_s(buf, sizeof(buf), "L%lf,%lf", screenPts.front().x, screenPts.front().y);
        dStr += buf;
    }
    // 屏幕像素单位的线宽
    int screenLineWidth = Max(1, static_cast<int>(uCvt.worldToPixel(style.width)));
    QString fillColorStr("none");
    if (fillColor.a != 0)
    {
        fillColorStr = QString("rgb(%1,%2,%3)").arg(fillColor.r).arg(fillColor.g).arg(fillColor.b);
    }
    auto rNode = pGroup->append("path")
        .attr("d", dStr)
        .attr("stroke", style.color)
        .attr("stroke-width", screenLineWidth)
        .attr("fill", fillColorStr.toUtf8().data());

    std::string dashStr = getDashStr(style.type);
    if (!dashStr.empty())
        rNode.attr("stroke-dasharray", dashStr);
}

void ISODIMPainterSvg::drawLoopLine2D(const std::vector<DVec2>& points, const WDDIMLineStyle& style, WD::Color fillColor)
{
    if (points.size() < 3)
    {
        assert(false);
        return;
    }
    // 转换到屏幕坐标
    std::string dStr;
    dStr.reserve((points.size() + 1) * 20);

    char buf[1024] = { 0 };
    sprintf_s(buf, sizeof(buf), "M%lf,%lf", points.front().x, points.front().y);
    dStr += buf;

    for (size_t i = 1; i < points.size(); ++i)
    {
        sprintf_s(buf, sizeof(buf), "L%lf,%lf", points[i].x, points[i].y);
        dStr += buf;
    }
    // 如果指定的第一个点和最后一个点不重合，则需要连接这两个点
    if (DVec2::DistanceSq(points.front(), points.back()) >= Epsilon)
    {
        sprintf_s(buf, sizeof(buf), "L%lf,%lf", points.front().x, points.front().y);
        dStr += buf;
    }
    // 屏幕像素单位的线宽
    int screenLineWidth = Max(1, static_cast<int>(uCvt.worldToPixel(style.width)));
    auto rNode = pGroup->append("path")
        .attr("d", dStr)
        .attr("stroke", style.color)
        .attr("stroke-width", screenLineWidth)
        .attr("fill", "none");

    std::string dashStr = getDashStr(style.type);
    if (!dashStr.empty())
        rNode.attr("stroke-dasharray", dashStr);
}

void ISODIMPainterSvg::drawText2D(const std::string& text
    , const DVec2& position
    , const WDDIMFontStyle& style
    , const WDDIMAlign& textAlign)
{
    WDUnused(textAlign);
    if (pGroup == nullptr)
    {
        assert(false);
        return ;
    }

    pGroup->append("text", text.c_str())
        .attr("x", position.x)
        .attr("y", position.y)
        .attr("font-size", 16)
        .attr("font_family", std::string("TrueType"))
        .attr("font-style", "normal")
        .attr("font-weight", 40)
        .attr("fill", style.color)
        .attr("stroke", "none");
}

void ISODIMPainterSvg::drawArc(const DVec3& center
    , double radius
    , const DVec3& sDirection
    , const DVec3& eDirection
    , const WDDIMLineStyle& style)
{
    if (pGroup == nullptr)
    {
        assert(false);
        return;
    }
    if (radius <= Epsilon)
    {
        assert(false);
        return;
    }
    if (sDirection.lengthSq() <= Epsilon || eDirection.lengthSq() <= Epsilon)
    {
        assert(false);
        return;
    }
    double angle = DVec3::Angle(sDirection, eDirection);
    if (angle <= Epsilon)
    {
        assert(false);
        return;
    }
    // 计算圆弧顶点
    DVec3 up = DVec3::Cross(sDirection, eDirection).normalized();
    uint cnt = 8;// ArcTravel(angle, radius, 0.4);

    DVec3 pt0 = center + sDirection * radius;
    auto screenPt0 = uCvt.worldToScreen(pt0);
    char buf[1024] = { 0 };
    sprintf_s(buf, sizeof(buf), "M%lf,%lf", screenPt0.x, screenPt0.y);
    std::string pathD;
    pathD.reserve(1024);
    pathD += buf;
    for (uint i = 0; i <= cnt; ++i)
    {
        double tAngle = static_cast<double>(i) / static_cast<double>(cnt) * angle;
        DMat3 rMat = DMat3::MakeRotation(tAngle, up);
        DVec3 pt = center + rMat * sDirection * radius;

        auto screenPt = uCvt.worldToScreen(pt);
        sprintf_s(buf, sizeof(buf), "L%lf,%lf", screenPt.x, screenPt.y);
        pathD += buf;
    }

    // 屏幕像素单位的线宽
    int screenLineWidth = Max(1, static_cast<int>(uCvt.worldToPixel(style.width)));
    auto rNode = pGroup->append("path")
        .attr("d", pathD.c_str())
        .attr("stroke", style.color)
        .attr("stroke-width", screenLineWidth)
        .attr("fill", "none");

    std::string dashStr = getDashStr(style.type);
    if (!dashStr.empty())
        rNode.attr("stroke-dasharray", dashStr);
}

void ISODIMPainterSvg::drawRect(const DVec3& center
    , const DVec2& size
    , const DVec3& xAxis
    , const DVec3& planeNormal
    , const WDDIMLineStyle& style)
{
    if (pGroup == nullptr)
    {
        assert(false);
        return;
    }
    if (size.x <= Epsilon || size.y <= Epsilon)
    {
        assert(false);
        return;
    }
    if (xAxis.lengthSq() <= Epsilon || planeNormal.lengthSq() <= Epsilon)
    {
        assert(false);
        return;
    }

    DVec3 yAxis = DVec3::Cross(planeNormal, xAxis).normalized();
    DVec2 halfSize = size * 0.5;

    // 世界坐标的四个顶点
    std::array<DVec3, 4> pts = {
        center + xAxis * halfSize.x + yAxis * halfSize.y,
        center - xAxis * halfSize.x + yAxis * halfSize.y,
        center - xAxis * halfSize.x - yAxis * halfSize.y,
        center + xAxis * halfSize.x - yAxis * halfSize.y,
    };
    // 转换到屏幕坐标
    std::array<DVec2, 4> screenPts;
    for (size_t i = 0; i < pts.size(); ++i)
    {
        screenPts[i] = uCvt.worldToScreen(pts[i]);
    }

    char buf[1024] = { 0 };
    sprintf_s(buf, sizeof(buf), "M%lf,%lfL%lf,%lfL%lf,%lfL%lf,%lfL%lf,%lf"
        , screenPts[0].x, screenPts[0].y
        , screenPts[1].x, screenPts[1].y
        , screenPts[2].x, screenPts[2].y
        , screenPts[3].x, screenPts[3].y
        , screenPts[0].x, screenPts[0].y);

    // 屏幕像素单位的线宽
    int screenLineWidth = Max(1, static_cast<int>(uCvt.worldToPixel(style.width)));

    auto rNode = pGroup->append("path")
        .attr("d", std::string(buf))
        .attr("stroke", style.color)
        .attr("stroke-width", screenLineWidth)
        .attr("fill", "none");

    std::string dashStr = getDashStr(style.type);
    if (!dashStr.empty())
        rNode.attr("stroke-dasharray", dashStr);
}

void ISODIMPainterSvg::fillRect(const DVec3& center
    , const DVec2& size
    , const DVec3& xAxis
    , const DVec3& planeNormal
    , const WDDIMShapeFillStyle& style)
{
    if (pGroup == nullptr)
    {
        assert(false);
        return;
    }
    if (size.x <= Epsilon || size.y <= Epsilon)
    {
        assert(false);
        return;
    }
    if (xAxis.lengthSq() <= Epsilon || planeNormal.lengthSq() <= Epsilon)
    {
        assert(false);
        return;
    }

    DVec3 yAxis = DVec3::Cross(planeNormal, xAxis).normalized();
    DVec2 halfSize = size * 0.5;

    // 世界坐标的四个顶点
    std::array<DVec3, 4> pts = {
        center + xAxis * halfSize.x + yAxis * halfSize.y,
        center - xAxis * halfSize.x + yAxis * halfSize.y,
        center - xAxis * halfSize.x - yAxis * halfSize.y,
        center + xAxis * halfSize.x - yAxis * halfSize.y,
    };
    // 转换到屏幕坐标
    std::array<DVec2, 4> screenPts;
    for (size_t i = 0; i < pts.size(); ++i)
    {
        screenPts[i] = uCvt.worldToScreen(pts[i]);
    }
    char buf[1024] = { 0 };
    sprintf_s(buf, sizeof(buf), "M%lf,%lfL%lf,%lfL%lf,%lfL%lf,%lfL%lf,%lf"
        , screenPts[0].x, screenPts[0].y
        , screenPts[1].x, screenPts[1].y
        , screenPts[2].x, screenPts[2].y
        , screenPts[3].x, screenPts[3].y
        , screenPts[0].x, screenPts[0].y);

    pGroup->append("path")
        .attr("d", std::string(buf))
        .attr("stroke", "none")
        .attr("fill", style.color);
}

void ISODIMPainterSvg::drawCircle(const DVec3& center
    , double radius
    , const DVec3& planeNormal
    , const WDDIMLineStyle& style)
{
    if (radius <= Epsilon)
    {
        assert(false);
        return;
    }
    if (planeNormal.lengthSq() <= Epsilon)
    {
        assert(false);
        return;
    }

    DVec3 axisX = DMat3::MakeRotationUseDirectionZ(planeNormal)[0];
    uint cnt = 24;//ArcTravel(360.0, radius, 0.4);

    DVec3 pt0 = center + axisX * radius;
    auto screenPt0 = uCvt.worldToScreen(pt0);
    char buf[1024] = { 0 };
    sprintf_s(buf, sizeof(buf), "M%lf,%lf", screenPt0.x, screenPt0.y);
    std::string pathD;
    pathD.reserve(1024);
    pathD += buf;
    for (uint i = 0; i <= cnt; ++i)
    {
        double tAngle = static_cast<double>(i) / static_cast<double>(cnt) * 360.0;
        DMat3 rMat = DMat3::MakeRotation(tAngle, planeNormal);
        DVec3 pt = center + rMat * axisX * radius;

        auto screenPt = uCvt.worldToScreen(pt);
        sprintf_s(buf, sizeof(buf), "L%lf,%lf", screenPt.x, screenPt.y);
        pathD += buf;
    }
    // 屏幕像素单位的线宽
    int screenLineWidth = Max(1, static_cast<int>(uCvt.worldToPixel(style.width)));

    auto rNode = pGroup->append("path")
        .attr("d", pathD.c_str())
        .attr("stroke", style.color)
        .attr("stroke-width", screenLineWidth)
        .attr("fill", "none");

    std::string dashStr = getDashStr(style.type);
    if (!dashStr.empty())
        rNode.attr("stroke-dasharray", dashStr);
}

void ISODIMPainterSvg::fillCircle(const DVec3& center
    , double radius
    , const DVec3& planeNormal
    , const WDDIMShapeFillStyle& style
    , const std::optional<DVec3>& xAxis)
{
    if (radius <= Epsilon)
    {
        assert(false);
        return;
    }
    if (planeNormal.lengthSq() <= Epsilon)
    {
        assert(false);
        return;
    }
    if (radius <= Epsilon)
    {
        assert(false);
        return;
    }
    if (planeNormal.lengthSq() <= Epsilon)
    {
        assert(false);
        return;
    }

    DVec3 axisX = DMat3::MakeRotationUseDirectionZ(planeNormal)[0];
    if (xAxis)
        axisX = xAxis.value().normalized();

    uint cnt = 24;//ArcTravel(360.0, radius, 0.4);

    DVec3 pt0 = center + axisX * radius;
    auto screenPt0 = uCvt.worldToScreen(pt0);
    char buf[1024] = { 0 };
    sprintf_s(buf, sizeof(buf), "M%lf,%lf", screenPt0.x, screenPt0.y);
    std::string pathD;
    pathD.reserve(1024);
    pathD += buf;
    for (uint i = 0; i <= cnt; ++i)
    {
        double tAngle = static_cast<double>(i) / static_cast<double>(cnt) * 360.0;
        DMat3 rMat = DMat3::MakeRotation(tAngle, planeNormal);
        DVec3 pt = center + rMat * axisX * radius;

        auto screenPt = uCvt.worldToScreen(pt);
        sprintf_s(buf, sizeof(buf), "L%lf,%lf", screenPt.x, screenPt.y);
        pathD += buf;
    }
    pGroup->append("path")
        .attr("d", pathD.c_str())
        .attr("stroke", "none")
        .attr("fill", style.color);
}

void ISODIMPainterSvg::drawTriangle(const std::array<DVec3, 3>& vertices
    , const WDDIMLineStyle& style)
{
    if (pGroup == nullptr)
    {
        assert(false);
        return;
    }
    if (!DTriangle3::IsTriangle(vertices[0], vertices[1], vertices[2]))
    {
        assert(false);
        return;
    }
    // 转换到屏幕坐标
    std::array<DVec2, 3> screenPts;
    for (size_t i = 0; i < vertices.size(); ++i)
    {
        screenPts[i] = uCvt.worldToScreen(vertices[i]);
    }
    char buf[1024] = { 0 };
    sprintf_s(buf, sizeof(buf), "M%lf,%lfL%lf,%lfL%lf,%lfL%lf,%lf"
        , screenPts[0].x, screenPts[0].y
        , screenPts[1].x, screenPts[1].y
        , screenPts[2].x, screenPts[2].y
        , screenPts[0].x, screenPts[0].y);

    // 屏幕像素单位的线宽
    int screenLineWidth = Max(1, static_cast<int>(uCvt.worldToPixel(style.width)));

    auto rNode = pGroup->append("path")
        .attr("d", std::string(buf))
        .attr("stroke", style.color)
        .attr("stroke-width", screenLineWidth)
        .attr("fill", "none");

    std::string dashStr = getDashStr(style.type);
    if (!dashStr.empty())
        rNode.attr("stroke-dasharray", dashStr);
}

void ISODIMPainterSvg::fillTriangle(const std::array<DVec3, 3>& vertices
    , const WDDIMShapeFillStyle& style
    , const std::optional<DVec3>& xAxis)
{
    WDUnused(xAxis);
    if (pGroup == nullptr)
    {
        assert(false);
        return;
    }
    if (!DTriangle3::IsTriangle(vertices[0], vertices[1], vertices[2]))
    {
        assert(false);
        return;
    }
    // 转换到屏幕坐标
    std::array<DVec2, 3> screenPts;
    for (size_t i = 0; i < vertices.size(); ++i)
    {
        screenPts[i] = uCvt.worldToScreen(vertices[i]);
    }
    char buf[1024] = { 0 };
    sprintf_s(buf, sizeof(buf), "M%lf,%lfL%lf,%lfL%lf,%lfL%lf,%lf"
        , screenPts[0].x, screenPts[0].y
        , screenPts[1].x, screenPts[1].y
        , screenPts[2].x, screenPts[2].y
        , screenPts[0].x, screenPts[0].y);
    pGroup->append("path")
        .attr("d", std::string(buf))
        .attr("stroke", "none")
        .attr("fill", style.color);
}

DVec2 ISODIMPainterSvg::drawText(const std::string& text
    , const DVec3& position
    , const DVec3& rightDir
    , const DVec3& upDir
    , const WDDIMFontStyle& style
    , const WDDIMAlign& textAlign)
{
    return drawTextP(text, position, rightDir, upDir, style, textAlign);
}

DVec2 ISODIMPainterSvg::drawTexts(const DVec3& pos
    , const DVec3& rightDir
    , const DVec3& upDir
    , std::vector<std::pair<std::string, WDDIMFontStyle> > texts
    , WDDIMAlign align
    , WDDIMAlign::HAlign rowAlign)
{
    if (pGroup == nullptr)
    {
        assert(false);
        return DVec2::Zero();
    }
    if (texts.empty()
        || rightDir.lengthSq() <= Epsilon
        || upDir.lengthSq() <= Epsilon)
    {
        assert(false);
        return DVec2::Zero();
    }
    struct RowData
    {
        // 行的左上角坐标
        DVec3 posLT = DVec3::Zero();
        // 行包含文本的尺寸
        DVec2 textSz = DVec2::Zero();
    };
    // 以左上角对齐，每一行的左上角坐标
    std::vector<RowData> rowDatas;
    rowDatas.reserve(texts.size());

    DVec3 rowPos = pos;
    // 所有文本的宽高
    DVec2 allSz = DVec2::Zero();
    for (const auto& t : texts)
    {
        DVec2 sz = this->calcTextSize(t.first, t.second);
        rowDatas.push_back({ rowPos , sz });
        // 宽度取最大值
        allSz.x     =   Max(allSz.x, sz.x);
        // 高度累加
        allSz.y     +=  sz.y;
        // 向下移动到下一行
        rowPos      -=  upDir * sz.y;
    }

    // 根据指定的对齐方式，重新计算每一行的坐标
    DVec2 halfAllSz = allSz * 0.5;
    for (size_t i = 0; i < rowDatas.size(); ++i)
    {
        auto& tPos = rowDatas[i].posLT;
        const auto& tSz = rowDatas[i].textSz;
        // 文本水平对齐
        switch (align.hAlign)
        {
        case WDDIMAlign::HA_Left:
            break;
        case WDDIMAlign::HA_Center:
            tPos = tPos - halfAllSz.x * rightDir;
            break;
        case WDDIMAlign::HA_Right:
            tPos = tPos - allSz.x * rightDir;
            break;
        default:
            break;
        }
        // 文本垂直对齐
        switch (align.vAlign)
        {
        case WDDIMAlign::VA_Top:
            break;
        case WDDIMAlign::VA_Center:
            tPos = tPos + halfAllSz.y * upDir;
            break;
        case WDDIMAlign::VA_Bottom:
            tPos = tPos + allSz.y * upDir;
            break;
        default:
            break;
        }
        // 行对齐
        switch (rowAlign)
        {
        case WD::WDDIMAlign::HA_Left:
            break;
        case WD::WDDIMAlign::HA_Center:
            tPos = tPos + (allSz.x - tSz.x) * 0.5 * rightDir;
            break;
        case WD::WDDIMAlign::HA_Right:
            tPos = tPos + (allSz.x - tSz.x) * rightDir;
            break;
        default:
            break;
        }
    }
    // 绘制文本
    WDDIMAlign tmpAlign;
    tmpAlign.hAlign = WDDIMAlign::HA_Left;
    tmpAlign.vAlign = WDDIMAlign::VA_Top;
    for (size_t i = 0; i < texts.size(); ++i)
    {
        const auto& t       = texts[i];
        const auto& text    = t.first;
        const auto& style   = t.second;
        const auto& tPos    = rowDatas[i].posLT;
        this->drawTextP(text, tPos, rightDir, upDir, style, tmpAlign);
    }
    return allSz;
}

DVec2 ISODIMPainterSvg::drawTextP(const std::string& text
    , const DVec3& position
    , const DVec3& rightDir
    , const DVec3& upDir
    , const WDDIMFontStyle& style
    , const WDDIMAlign& textAlign)
{
    if (pGroup == nullptr)
    {
        assert(false);
        return DVec2::Zero();
    }
    if (text.empty()
        || rightDir.lengthSq() <= Epsilon
        || upDir.lengthSq() <= Epsilon)
    {
        assert(false);
        return DVec2::Zero();
    }
    TextSizeCalculator tc(uCvt, text, style);

    // 原始的坐标，因为SVG文字原点在左下角，因此这里也代表文本的左下角坐标
    DVec2 textPosSrc    = uCvt.worldToScreen(position);
    DVec2 textPos       = textPosSrc;

    // 水平对齐
    switch (textAlign.hAlign)
    {
    case WDDIMAlign::HA_Left:
        break;
    case WDDIMAlign::HA_Center:
        textPos.x = textPos.x - tc.halfSize.x;
        break;
    case WDDIMAlign::HA_Right:
        textPos.x = textPos.x - tc.size.x;
        break;
    default:
        break;
    }
    // 垂直对齐, 这里注意，SVG的文字默认是左下角对齐的
    switch (textAlign.vAlign)
    {
    case WDDIMAlign::VA_Top:
        textPos.y = textPos.y + tc.size.y;
        break;
    case WDDIMAlign::VA_Center:
        textPos.y = textPos.y + tc.halfSize.y;
        break;
    case WDDIMAlign::VA_Bottom:
        break;
    default:
        break;
    }
    auto ratioP2W       = uCvt.pixelToWorldRatio();
    // 计算文本旋转方向
    DVec2 textRight     = uCvt.worldToScreen(position + rightDir * 10.0 * ratioP2W);
    textRight           = textRight - textPosSrc;
    double angle        = TextSizeCalculator::RotateAngle(textRight);
    char tsBuf[1024]    = { 0 };
    sprintf_s(tsBuf, sizeof(tsBuf), "rotate(%lf,%lf,%lf)", angle, textPosSrc.x, textPosSrc.y);
    auto pTextGroup     = pGroup->append("g").attr("transform", std::string(tsBuf));

    // 屏幕坐标的文字大小
    int screenFontSize      = Max(1, static_cast<int>(uCvt.worldToPixel(style.fontSize)));
    // 屏幕坐标的边框线宽
    int screenBorderWidth   = Max(1, static_cast<int>(uCvt.worldToPixel(style.bolderWidth)));

    pTextGroup.append("text", text.c_str())
        .attr("x", textPos.x)
        .attr("y", textPos.y)
        .attr("font_family", tc.font.family().toUtf8().data())
        .attr("font-size", screenFontSize)
        .attr("font-style", "normal")
        .attr("font-weight", tc.font.weight())
        .attr("fill", style.color)
        .attr("stroke", "none");
    // 边框的左下角坐标
    auto tBorderPosLB = textPos + tc.borderPosOffset;
    // 边框的中心点坐标
    auto bBorderPosCen = DVec2(tBorderPosLB.x + tc.halfBorderSize.x, tBorderPosLB.y - tc.halfBorderSize.y);
    // 是否有边框
    bool bBolder = false;
    // 绘制文本边框
    switch (style.bolderType)
    {
    case WDDIMFontStyle::BolderType::BT_Rect:
    {
        std::array<DVec2, 4> screenPts =
        {
              tBorderPosLB
            , DVec2(tBorderPosLB.x + tc.borderSize.x, tBorderPosLB.y)
            , DVec2(tBorderPosLB.x + tc.borderSize.x, tBorderPosLB.y - tc.borderSize.y)
            , DVec2(tBorderPosLB.x, tBorderPosLB.y - tc.borderSize.y)
        };
        char pathDBuf[1024] = { 0 };
        sprintf_s(pathDBuf, sizeof(pathDBuf), "M%lf,%lfL%lf,%lfL%lf,%lfL%lf,%lfL%lf,%lf"
            , screenPts[0].x, screenPts[0].y
            , screenPts[1].x, screenPts[1].y
            , screenPts[2].x, screenPts[2].y
            , screenPts[3].x, screenPts[3].y
            , screenPts[0].x, screenPts[0].y);
        pTextGroup.append("path")
            .attr("d", std::string(pathDBuf))
            .attr("stroke", style.bolderColor)
            .attr("stroke-width", screenBorderWidth)
            .attr("fill", "none");
        bBolder = true;
    }
    break;
    case WDDIMFontStyle::BolderType::BT_Circle:
    {
        pTextGroup.append("circle")
            .attr("cx", bBorderPosCen.x)
            .attr("cy", bBorderPosCen.y)
            .attr("r", Max(tc.halfBorderSize.x, tc.halfBorderSize.y))
            .attr("stroke", style.bolderColor)
            .attr("stroke-width", screenBorderWidth)
            .attr("fill", "none");
        bBolder = true;
    }break;
    case WDDIMFontStyle::BolderType::BT_Ellipse:
    {
        auto ptLB = textPos + DVec2(-style.bolderWidth * 0.5, style.bolderWidth * 0.5);
        pTextGroup.append("ellipse")
            .attr("cx", bBorderPosCen.x)
            .attr("cy", bBorderPosCen.y)
            .attr("rx", tc.halfBorderSize.x)
            .attr("ry", tc.halfBorderSize.y)
            .attr("stroke", style.bolderColor)
            .attr("stroke-width", screenBorderWidth)
            .attr("fill", "none");
        bBolder = true;
    }break;
    default:
        break;
    }

    return tc.maxSize * ratioP2W;
}

std::string ISODIMPainterSvg::getDashStr(int lineType)
{
    std::string rStr = "";
    switch (lineType)
    {
    case WD::WDDIMLineStyle::LT_Solid:
        break;
    case WD::WDDIMLineStyle::LT_Dash:
        rStr = "7 3";
        break;
    case WD::WDDIMLineStyle::LT_Dot:
        break;
    case WD::WDDIMLineStyle::LT_DashDot:
        break;
    case WD::WDDIMLineStyle::LT_Center:
        break;
    case WD::WDDIMLineStyle::LT_DashDotDot:
        break;
    case WD::WDDIMLineStyle::LT_DashDotDotDot:
        break;
    case WD::WDDIMLineStyle::LT_User:
        break;
    default:
        break;
    }
    return rStr;
}

WD_NAMESPACE_END

