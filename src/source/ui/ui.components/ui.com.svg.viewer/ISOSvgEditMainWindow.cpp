#include "ISOSvgEditMainWindow.h"
#include "node/WDNode.h"
#include <QDockWidget>
#include <QString>
#include <QPushButton>
#include <QLabel>
#include <QMessageBox>
#include <qaction.h>
#include <QGroupBox>
#include <QComboBox>
#include <QCheckBox>
#include <QFileDialog>
#include <qapplication.h>
#include "QtPropertyBrowser/qttreepropertybrowser.h"
#include "WDTranslate.h"
#include "core/WDCore.h"
#include "core/message/WDMessage.h"
#include "WDRapidxml.h"
#include <QHeaderView>
#include "ISOGraphics/ISOGraphicsLine.h"
#include "ISOGraphics/ISOGraphicsText.h"
#include "ISOGraphics/ISOGraphicsPath.h"
#include "ISOGraphics/ISOGraphicsEllipse.h"
#include "ISOGraphics/ISOGraphicsRect.h"
#include "ISOGraphics/ISOGraphicsHelper.h"
#include <QDebug>

static constexpr const char* Fill = "fill";
static constexpr const char* FontSize = "font-size";
static constexpr const char* FontWeight = "font-weight";
static constexpr const char* Stroke = "stroke";
static constexpr const char* StrokeWidth = "stroke-width";
static constexpr const char* GroupNodeName = "g";
static constexpr const char* TextX = "x";
static constexpr const char* TextY = "y";
static constexpr const char* LineFirstX = "x1";
static constexpr const char* LineSecondX = "x2";
static constexpr const char* LineFirstY = "y1";
static constexpr const char* LineSecondY = "y2";
static constexpr const char* PathNodeD = "d";
static constexpr const char* RadiusX = "rx";
static constexpr const char* RadiusY = "ry";
static constexpr const char* Width = "width";
static constexpr const char* Height = "height";

ISOSvgEditMainWindow::ISOSvgEditMainWindow(WD::WDCore& core, WD::ISOPaper& paper, QWidget* parent)
    : QMainWindow(parent)
    , _core(core)
    , _paper(paper)
    , _svg(WD::WDSvgXmlCreator("xml version='1.0' encoding='utf-8' standalone='no'"))
{
    ui.setupUi(this);
    this->setMouseTracking(true);
    this->setWindowTitle(QString::fromUtf8("ISO编辑"));

    _pView = new ISOSvgEditView(_paper, _svg, _manager);
    this->setCentralWidget(_pView);
    _pWidget = new ObjectPropertyWidget();
    _pWidget->layout()->setContentsMargins(2, 2, 2, 2);
    _pWidget->layout()->setSpacing(2);
    _pWidget->setWindowTitle(QString::fromUtf8("ISO编辑"));
    _pGroup = WD::WDPropertyGroup::MakeShared();

    _pEditDockWidget = new QDockWidget(QString::fromUtf8("ISO编辑"));
    _pEditDockWidget->setAllowedAreas(Qt::RightDockWidgetArea | Qt::LeftDockWidgetArea);
    _pMainwidget = new QWidget();
    auto lay = new QVBoxLayout();
    _pMainwidget->setLayout(lay);
    _pEditDockWidget->setWidget(_pMainwidget);
    lay->addWidget(_pWidget);
    lay->addStretch();

    this->addDockWidget(Qt::RightDockWidgetArea, _pEditDockWidget);
    _pEditDockWidget->setVisible(true);
    updateIsoAddList();
    double width = _paper.uCvt.paperToPixel(_paper.size().width());
    double height = _paper.uCvt.paperToPixel(_paper.size().height());
    char szView[1024] = { 0 };
    sprintf(szView, "%d %d %d %d", 0, 0, static_cast<int>(width), static_cast<int>(height));
    _svg.root("svg")
        .attr("viewBox", std::string(szView))
        .attr("xmlns", "http://www.w3.org/2000/svg")
        .attr("xmlns:xlink", "http://www.w3.org/1999/xlink");
    QObject::connect(_pView, &ISOSvgEditView::signalUpdateDataView, this, &ISOSvgEditMainWindow::slotUpdateGraphicsData);
    QObject::connect(_pView, &ISOSvgEditView::cancelChecked, this, &ISOSvgEditMainWindow::slotBtnCancelChecked);
    QObject::connect(_pWidget, &ObjectPropertyWidget::dataChanged, this, &ISOSvgEditMainWindow::slotValueEditApply);
}
ISOSvgEditMainWindow::~ISOSvgEditMainWindow()
{
    if (_pWidget != nullptr)
    {
        delete _pWidget;
        _pWidget = nullptr;
    }

    if (_pEditDockWidget != nullptr)
    {
        delete _pEditDockWidget;
        _pEditDockWidget = nullptr;
    }
    QApplication::restoreOverrideCursor();
}

void ISOSvgEditMainWindow::genSvgXmlCreator(const std::string& isoSvgData)
{
    auto _myData = isoSvgData;
    //新建一个svg的xml存于内存中（没有存文件）
    auto doc =  new WD::XMLDoc;
    doc->parse<0>((char*)isoSvgData.data());
    
    WD::XMLNode* svgNode = doc->first_node("svg");
    if (svgNode == nullptr)
        return;

    //找出绘图区节点拷贝
    WD::XMLNode* gNode = nullptr;
    for (gNode = svgNode->first_node("g")
        ; gNode
        ; gNode = gNode->next_sibling("g"))
    {
        auto attr = gNode->first_attribute("groupName");
        if (attr != nullptr && strcmp(attr->value(), "polttingArea") == 0)
        {
            break;
        }
        if ((attr != nullptr && strcmp(attr->value(), "sideArea") == 0))
        {
            //边框区域里面只有一个rect和一个path，path的起点就是绘图区的左上角起点
            //这里是为了拿到绘图区边框的起点
            auto pathNode = gNode->first_node("path");
            if (pathNode != nullptr)
            {
                auto pAttrD = pathNode->first_attribute(PathNodeD);
                if (pAttrD == nullptr)
                {
                    continue;
                }
                auto points = ISOSvgItemHelper::RegexPathParser(QString::fromUtf8(pAttrD->value()));
                if (points.empty())
                {
                    continue;
                }
                _areaStartPos = points.front();
            }
        }
    }
    if (gNode == nullptr)
    {
        return;
    }
    //拷贝绘图区节点及其子节点到新建的svg中
    auto inputSvgNode = _svg.doc().first_node("svg");
    //找出原有节点删除
    WD::XMLNode* gOldNode = nullptr;
    for (gOldNode = inputSvgNode->first_node("g")
        ; gOldNode
        ; gOldNode = gOldNode->next_sibling("g"))
    {
        auto attr = gOldNode->first_attribute("groupName");
        if (attr != nullptr && strcmp(attr->value(), "polttingArea") == 0)
        {
            break;
        }
    }

    if (gOldNode != nullptr)
    {
        inputSvgNode->remove_node(gOldNode);
    }
    auto grp = _svg.root().append("g").attr("groupName", "polttingArea");

    WD::WDSvgXmlCreator::Node tGroup(_svg.doc(), grp.native());
    ISOSvgItemHelper::CopyChildNode(&tGroup, gNode);
}

void ISOSvgEditMainWindow::initTreeView()
{
    _pGroup->clear();
    auto svgNode = _svg.doc().first_node("svg");
    if (svgNode == nullptr)
        return;
    auto areaNode = svgNode->first_node("g");
    std::string xml;
    if (areaNode == nullptr)
        return;
    // 读取SVG 数据，转换为ISOGraphics
    _manager.loadFromXml(&_svg.doc());
    _pView->createDrawingBounds(_areaStartPos);
    // 刷新编辑视图显示
    _pView->reflashDisplay();
    //设置保存状态，默认是已保存，有更新触发设置为未保存
    _pView->setSaved(true);
}

void ISOSvgEditMainWindow::slotEditBtnClicked(bool checked)
{
    if (checked)
    {
        show();
    }
}

void ISOSvgEditMainWindow::slotUpdateGraphicsData()
{
    //当界面编辑触发的属性变化不需要反复更新到界面，这里跳过
    if (!_valueUpdate)
    {
        return;
    }
    _pGroup->clear();
    ISOGraphics* pItem = _pView->currentGraphicsItem();
    if (pItem == nullptr)
    {
        _pWidget->update(*_pGroup);
        return;
    }
    switch (pItem->type())
    {
    case   ISOGraphics::Type::GT_Text:
    {
        auto pGraphicsText = dynamic_cast<ISOGraphicsText*>(pItem);
        if (pGraphicsText == nullptr)
        {
            return;
        }
        int size = pGraphicsText->fontSize();
        _pGroup->addPropertyString(WD::WDTs("ISOEditDialog", "Text"), pGraphicsText->text().toUtf8().data());
        auto pFontProperty = _pGroup->addPropertyInt(WD::WDTs("ISOEditDialog", FontSize), size);
        if (pFontProperty != nullptr)
        {
            pFontProperty->setRangeMin(1);
        }
        _pGroup->addPropertyDouble(TextX, pGraphicsText->pos().x);
        _pGroup->addPropertyDouble(TextY, pGraphicsText->pos().y);
        break;
    }
    case   ISOGraphics::Type::GT_Line:
    {
        auto pGraphicsLine = dynamic_cast<ISOGraphicsLine*>(pItem);
        if (pGraphicsLine == nullptr)
        {
            return;
        }
        _pGroup->addPropertyDouble(LineFirstX, pGraphicsLine->startPos().x);
        _pGroup->addPropertyDouble(LineFirstY, pGraphicsLine->startPos().y);
        _pGroup->addPropertyDouble(LineSecondX, pGraphicsLine->endPos().x);
        _pGroup->addPropertyDouble(LineSecondY, pGraphicsLine->endPos().y);
        int lineWidth = pItem->strokeWidth();
        auto pProperty = _pGroup->addPropertyInt(WD::WDTs("ISOEditDialog", "StrokeWidth"), lineWidth);
        if (pProperty != nullptr)
        {
            pProperty->setRangeMin(1);
        }
        break;
    }
    case   ISOGraphics::Type::GT_Ellipse:
    {
        auto pGraphicsEllipse = dynamic_cast<ISOGraphicsEllipse*>(pItem);
        if (pGraphicsEllipse == nullptr)
        {
            return;
        }
        _pGroup->addPropertyDouble(WD::WDTs("ISOEditDialog", "RadiusX"), pGraphicsEllipse->radiusX());
        _pGroup->addPropertyDouble(WD::WDTs("ISOEditDialog", "RadiusY"), pGraphicsEllipse->radiusY());
        _pGroup->addPropertyDouble(TextX, pGraphicsEllipse->center().x);
        _pGroup->addPropertyDouble(TextY, pGraphicsEllipse->center().y);
        int lineWidth = pItem->strokeWidth();
        auto pProperty = _pGroup->addPropertyInt(WD::WDTs("ISOEditDialog", "StrokeWidth"), lineWidth);
        if (pProperty != nullptr)
        {
            pProperty->setRangeMin(1);
        }
        _pGroup->addPropertyBool(WD::WDTs("ISOEditDialog", "Fill"), pItem->isFilled());
        _pGroup->addPropertyBool(WD::WDTs("ISOEditDialog", "Stroke"), pItem->haveStroke());
        break;
    }
    case   ISOGraphics::Type::GT_Rect:
    {
        auto pGraphicsRect = dynamic_cast<ISOGraphicsRect*>(pItem);
        if (pGraphicsRect == nullptr)
        {
            return;
        }
        _pGroup->addPropertyDouble(TextX, pGraphicsRect->startPos().x);
        _pGroup->addPropertyDouble(TextY, pGraphicsRect->startPos().y);
        _pGroup->addPropertyDouble(WD::WDTs("ISOEditDialog", "RectRadiusX"), pGraphicsRect->radiusX());
        _pGroup->addPropertyDouble(WD::WDTs("ISOEditDialog", "RectRadiusY"), pGraphicsRect->radiusY());
        _pGroup->addPropertyDouble(WD::WDTs("ISOEditDialog", "Width"), pGraphicsRect->width());
        _pGroup->addPropertyDouble(WD::WDTs("ISOEditDialog", "Height"), pGraphicsRect->height());
        int lineWidth = pItem->strokeWidth();
        auto pProperty = _pGroup->addPropertyInt(WD::WDTs("ISOEditDialog", "StrokeWidth"), lineWidth);
        if (pProperty != nullptr)
        {
            pProperty->setRangeMin(1);
        }
        _pGroup->addPropertyBool(WD::WDTs("ISOEditDialog", "Fill"), pItem->isFilled());
        _pGroup->addPropertyBool(WD::WDTs("ISOEditDialog", "Stroke"), pItem->haveStroke());
        break;
    }
    case   ISOGraphics::Type::GT_Path:
    {
        auto pGraphicsPath = dynamic_cast<ISOGraphicsPath*>(pItem);
        if (pGraphicsPath == nullptr)
        {
            return;
        }
        int lineWidth = pItem->strokeWidth();
        auto pProperty = _pGroup->addPropertyInt(WD::WDTs("ISOEditDialog", "StrokeWidth"), lineWidth);
        if (pProperty != nullptr)
        {
            pProperty->setRangeMin(1);
        }
        _pGroup->addPropertyBool(WD::WDTs("ISOEditDialog", "Fill"), pItem->isFilled());
        _pGroup->addPropertyBool(WD::WDTs("ISOEditDialog", "Stroke"), pItem->haveStroke());
        break;
    }
    default:
        break;
    }
    //旋转角度，值域-180到180,181 = -179；
    double angle = 0.0;
    angle = atan2(pItem->matrix()[1][0], pItem->matrix()[0][0]);
    angle = angle * 180 / std::acos(-1);
    auto item =  _pGroup->addPropertyDouble(WD::WDTs("ISOEditDialog", "RotateAngle"), angle);
    item->setRangeMax(180);
    item->setRangeMin(-180);
    _pWidget->update(*_pGroup);
}

void ISOSvgEditMainWindow::slotBtnCancelChecked(ISOGraphics::Type type)
{
    QString typeStr = "";
    switch (type)
    {
    case ISOGraphics::GT_Text:
        typeStr = "text";
        break;
    case ISOGraphics::GT_Line:
        typeStr = "line";
        break;
    case ISOGraphics::GT_Ellipse:
        typeStr = "ellipse";
        break;
    case ISOGraphics::GT_Path:
        typeStr = "path";
        break;
    case ISOGraphics::GT_Rect:
        typeStr = "rect";
        break;
    default:
        return;
    }
    for (auto& i : ui.toolBar->actions())
    {
        qDebug() << i->property("type").toString();
        if (i->property("type").toString().compare(typeStr, Qt::CaseInsensitive) == 0)
        {
            i->setChecked(false);
            break;
        }
    }
    
}

void ISOSvgEditMainWindow::slotAddBtnChecked()
{
    auto btn = qobject_cast<QAction*>(QObject::sender());
    if (!btn->isChecked())
    {
        _pView->deleteNewCreate();
        return;
    }

    for (auto& i : ui.toolBar->actions())
    {
        if (i->isChecked() && i !=  btn)
        {
            i->setChecked(false);
            _pView->deleteNewCreate();
            break;
        }
    }

    ISOGraphics::Type type = ISOGraphics::Type::GT_Group;
    auto typeStr = btn->property("type").toString();
    if (typeStr.compare("text",Qt::CaseInsensitive) == 0)
    {
        type = ISOGraphics::Type::GT_Text;
    }
    else if (typeStr.compare("line", Qt::CaseInsensitive) == 0)
    {
        type = ISOGraphics::Type::GT_Line;
    }
    else if (typeStr.compare("rect", Qt::CaseInsensitive) == 0)
    {
        type = ISOGraphics::Type::GT_Rect;
    }
    else if (typeStr.compare("ellipse", Qt::CaseInsensitive) == 0)
    {
        type = ISOGraphics::Type::GT_Ellipse;
    }
    else if (typeStr.compare("path", Qt::CaseInsensitive) == 0)
    {
        type = ISOGraphics::Type::GT_Path;
    }
    _pView->createGraphics(type);
    QApplication::restoreOverrideCursor();
}

void ISOSvgEditMainWindow::slotCheckStateChanged(int state)
{
    if (state == 0)
    {
        _pView->setContinuousCreation(false);
    }
    else
    {
        _pView->setContinuousCreation(true);
    }
}

void ISOSvgEditMainWindow::slotDeleteBtnChecked()
{
    if (_pView->currentGraphicsItem() == nullptr)
    {
        return;
    }
    auto index = QMessageBox::question(this, QStringLiteral("提示"), QStringLiteral("确认删除当前选中所有图元?"), QStringLiteral("确认"), QStringLiteral("取消"));
    if (index != 0)
    {
        return;
    }
    _pView->deleteGraphics();
}

void ISOSvgEditMainWindow::closeEvent(QCloseEvent* event)
{
    if (!_pView->saved())
    {
        auto index = QMessageBox::question(this, QStringLiteral("提示"), QStringLiteral("有未保存的更改是否保存后关闭窗口"), QStringLiteral("保存"), QStringLiteral("不保存"), QStringLiteral("取消关闭"));
        switch (index)
        {
        case 0:
        {
            //保存
            if (!_pView->saveView())
            {
                event->ignore();
                return;
            }
            saveEditData(_svg.toString());
            _pView->setSaved(true);
            event->accept();
            break;
        }
        case 1:
        {
            //不保存
            if (!_pView->saveView())
            {
                event->ignore();
                return;
            }
            _pView->setSaved(true);
            event->accept();
            break;
        }
        default:
            //关闭
            event->ignore();
            return;
        }
    }
    QMainWindow::closeEvent(event);
}

void ISOSvgEditMainWindow::keyPressEvent(QKeyEvent* event)
{
    //ctrl s保存
    if (event->modifiers() == Qt::ControlModifier)
    {
        if (event->key() == Qt::Key_S)
        {
            slotSave();
        }
    }
    return QMainWindow::keyPressEvent(event);
}

void ISOSvgEditMainWindow::slotValueEditApply(QtProperty* p)
{
    WDUnused(p);
    _valueUpdate = false;
    auto item = _pView->currentGraphicsItem();
    if (item == nullptr)
    {
        return;
    }
    
    double angle = 0.0;
    _pGroup->propertyToValueDouble(WD::WDTs("ISOEditDialog", "RotateAngle"), angle);
    angle = ISOSvgItemHelper::NormalizeAngle(angle);
    double originalAngle = 0.0;
    originalAngle = atan2(item->matrix()[1][0], item->matrix()[0][0]);
    originalAngle = originalAngle * 180 / std::acos(-1);
    originalAngle = ISOSvgItemHelper::NormalizeAngle(originalAngle);
    if (angle != originalAngle)
    {
        _pView->rotateGraphics(angle, false);
        _valueUpdate = true;
        return;
    }

    item->flags().addFlag(ISOGraphics::ISOFlag::ISOFlag_Update);
    switch (item->type())
    {
    case ISOGraphics::GT_Text:
    {
        auto pGraphicsText = dynamic_cast<ISOGraphicsText*>(item);
        if (pGraphicsText == nullptr)
        {
            break;
        }
        std::string text;
        _pGroup->propertyToValueString(WD::WDTs("ISOEditDialog", "Text"), text);
        int size = pGraphicsText->fontSize();
        _pGroup->propertyToValueInt(WD::WDTs("ISOEditDialog", FontSize), size);
        double x1 = pGraphicsText->pos().x;
        double y1 = pGraphicsText->pos().y;
        _pGroup->propertyToValueDouble(WD::WDTs("ISOEditDialog", TextX), x1);
        _pGroup->propertyToValueDouble(WD::WDTs("ISOEditDialog", TextY), y1);
        pGraphicsText->setText(QString::fromUtf8(text.c_str()));
        pGraphicsText->setPos(WD::DVec2(x1, y1));
        pGraphicsText->setFontSize(size);
        break;
    }
    case ISOGraphics::GT_Line:
    {
        auto pGraphicsLine = dynamic_cast<ISOGraphicsLine*>(item);
        if (pGraphicsLine == nullptr)
        {
            break;
        }
        double x1 = pGraphicsLine->startPos().x;
        double y1 = pGraphicsLine->startPos().y;
        double x2 = pGraphicsLine->endPos().x;
        double y2 = pGraphicsLine->endPos().y;

        _pGroup->propertyToValueDouble(WD::WDTs("ISOEditDialog", LineFirstX), x1);
        _pGroup->propertyToValueDouble(WD::WDTs("ISOEditDialog", LineFirstY), y1);
        _pGroup->propertyToValueDouble(WD::WDTs("ISOEditDialog", LineSecondX), x2);
        _pGroup->propertyToValueDouble(WD::WDTs("ISOEditDialog", LineSecondY), y2);
        pGraphicsLine->setStartPos(WD::DVec2(x1, y1));
        pGraphicsLine->setEndPos(WD::DVec2(x2, y2));
        int lineSize = item->strokeWidth();
        _pGroup->propertyToValueInt(WD::WDTs("ISOEditDialog", "StrokeWidth"), lineSize);
        item->setStrokeWidth(lineSize);
        break;
    }
    case ISOGraphics::GT_Path:
    {
        auto pGraphicsPath = dynamic_cast<ISOGraphicsPath*>(item);
        if (pGraphicsPath == nullptr)
        {
            break;
        }
        bool filled = item->isFilled();
        _pGroup->propertyToValueBool(WD::WDTs("ISOEditDialog", "Fill"), filled);
        item->setIsFilled(filled);
        bool haveStroke = item->haveStroke();
        _pGroup->propertyToValueBool(WD::WDTs("ISOEditDialog", "Stroke"), haveStroke);
        item->setHaveStroke(haveStroke);
        int lineSize = item->strokeWidth();
        _pGroup->propertyToValueInt(WD::WDTs("ISOEditDialog", "StrokeWidth"), lineSize);
        item->setStrokeWidth(lineSize);
        break;
    }
    case   ISOGraphics::Type::GT_Ellipse:
    {
        auto pGraphicsEllipse = dynamic_cast<ISOGraphicsEllipse*>(item);
        if (pGraphicsEllipse == nullptr)
        {
            return;
        }
        double radiusX = pGraphicsEllipse->radiusX();
        double radiusY = pGraphicsEllipse->radiusY();
        WD::DVec2 center = pGraphicsEllipse->center();
        _pGroup->propertyToValueDouble(WD::WDTs("ISOEditDialog", "RadiusX"), radiusX);
        _pGroup->propertyToValueDouble(WD::WDTs("ISOEditDialog", "RadiusY"), radiusY);
        _pGroup->propertyToValueDouble(TextX, center.x);
        _pGroup->propertyToValueDouble(TextY, center.y);

        bool filled = item->isFilled();
        _pGroup->propertyToValueBool(WD::WDTs("ISOEditDialog", "Fill"), filled);
        item->setIsFilled(filled);
        bool haveStroke = item->haveStroke();
        _pGroup->propertyToValueBool(WD::WDTs("ISOEditDialog", "Stroke"), haveStroke);
        item->setHaveStroke(haveStroke);

        pGraphicsEllipse->setCenter(center);
        pGraphicsEllipse->setRadiusX(radiusX);
        pGraphicsEllipse->setRadiusY(radiusY);
        int lineSize = item->strokeWidth();
        _pGroup->propertyToValueInt(WD::WDTs("ISOEditDialog", "StrokeWidth"), lineSize);
        item->setStrokeWidth(lineSize);
        break;
    }
    case   ISOGraphics::Type::GT_Rect:
    {
        auto pGraphicsRect = dynamic_cast<ISOGraphicsRect*>(item);
        if (pGraphicsRect == nullptr)
        {
            return;
        }
        double radiusX = pGraphicsRect->radiusX();
        double radiusY = pGraphicsRect->radiusY();
        WD::DVec2 startPos = pGraphicsRect->startPos();
        double width = pGraphicsRect->width();
        double height = pGraphicsRect->height();
        _pGroup->propertyToValueDouble(WD::WDTs("ISOEditDialog", "RectRadiusX"), radiusX);
        _pGroup->propertyToValueDouble(WD::WDTs("ISOEditDialog", "RectRadiusY"), radiusY);
        _pGroup->propertyToValueDouble(TextX, startPos.x);
        _pGroup->propertyToValueDouble(TextY, startPos.y);
        _pGroup->propertyToValueDouble(WD::WDTs("ISOEditDialog", "Width"), width);
        _pGroup->propertyToValueDouble(WD::WDTs("ISOEditDialog", "Height"), height);

        bool filled = item->isFilled();
        _pGroup->propertyToValueBool(WD::WDTs("ISOEditDialog", "Fill"), filled);
        item->setIsFilled(filled);
        bool haveStroke = item->haveStroke();
        _pGroup->propertyToValueBool(WD::WDTs("ISOEditDialog", "Stroke"), haveStroke);
        item->setHaveStroke(haveStroke);

        pGraphicsRect->setStartPos(startPos);
        pGraphicsRect->setRadiusX(radiusX);
        pGraphicsRect->setRadiusY(radiusY);
        pGraphicsRect->setWidth(width);
        pGraphicsRect->setHeight(height);
        int lineSize = item->strokeWidth();
        _pGroup->propertyToValueInt(WD::WDTs("ISOEditDialog", "StrokeWidth"), lineSize);
        item->setStrokeWidth(lineSize);
        break;
    }
    default:
        break;
    }
    _pView->reflashDisplay();
    _valueUpdate = true;
}

void ISOSvgEditMainWindow::updateIsoAddList()
{
    QString iconPath = QString::fromUtf8(_core.dataDirPath()) + QString("iso\\actionsIcon\\");
    auto pActionAddText = ui.toolBar->addAction(QString::fromStdString(WD::WDTs("ISOEditDialog", "addText")));
    pActionAddText->setIcon(QIcon(iconPath + "text.png"));
    pActionAddText->setProperty("type", "text");
    QObject::connect(pActionAddText, SIGNAL(triggered()), this, SLOT(slotAddBtnChecked()));
    pActionAddText->setCheckable(true);

    auto pActionAddLine = ui.toolBar->addAction(QString::fromStdString(WD::WDTs("ISOEditDialog", "addLine")));
    pActionAddLine->setIcon(QIcon(iconPath + "line.png"));
    pActionAddLine->setProperty("type", "line");
    QObject::connect(pActionAddLine, SIGNAL(triggered()), this, SLOT(slotAddBtnChecked()));
    pActionAddLine->setCheckable(true);

    auto pActionAddRect = ui.toolBar->addAction(QString::fromStdString(WD::WDTs("ISOEditDialog", "addRect")));
    pActionAddRect->setIcon(QIcon(iconPath + "rect.png"));
    pActionAddRect->setProperty("type", "rect");
    QObject::connect(pActionAddRect, SIGNAL(triggered()), this, SLOT(slotAddBtnChecked()));
    pActionAddRect->setCheckable(true);

    auto pActionAddCircle = ui.toolBar->addAction(QString::fromStdString(WD::WDTs("ISOEditDialog", "addCircle")));
    pActionAddCircle->setIcon(QIcon(iconPath + "ellipse.png"));
    pActionAddCircle->setProperty("type", "ellipse");
    QObject::connect(pActionAddCircle, SIGNAL(triggered()), this, SLOT(slotAddBtnChecked()));
    pActionAddCircle->setCheckable(true);

    auto pActionAddPath = ui.toolBar->addAction(QString::fromStdString(WD::WDTs("ISOEditDialog", "addPath")));
    pActionAddPath->setIcon(QIcon(iconPath + "path.png"));
    pActionAddPath->setProperty("type", "path");
    QObject::connect(pActionAddPath, SIGNAL(triggered()), this, SLOT(slotAddBtnChecked()));
    pActionAddPath->setCheckable(true);

    auto pCheckBox = new QCheckBox();
    pCheckBox->setText(QString::fromStdString(WD::WDTs("ISOEditDialog", "ContinuousCreation")));
    pCheckBox->setChecked(_pView->continuousCreation());
    QObject::connect(pCheckBox, &QCheckBox::stateChanged, this, &ISOSvgEditMainWindow::slotCheckStateChanged);
    ui.toolBar->addWidget(pCheckBox);

    auto pActionDelete = ui.toolBar->addAction(QString::fromStdString(WD::WDTs("ISOEditDialog", "delete")));
    pActionDelete->setIcon(QIcon(iconPath + "delete.png"));
    pActionDelete->setProperty("type", "delete");
    QObject::connect(pActionDelete, SIGNAL(triggered()), this, SLOT(slotDeleteBtnChecked()));

    auto pActionSave = ui.toolBar->addAction(QString::fromStdString(WD::WDTs("ISOEditDialog", "save")));
    pActionSave->setIcon(QIcon(iconPath + "save.png"));
    pActionSave->setProperty("type", "save");
    QObject::connect(pActionSave, &QAction::triggered, this, &ISOSvgEditMainWindow::slotSave);
    auto workPath = QApplication::applicationDirPath() + "/debug.debug";
    qDebug() << workPath;
    //if (QFile::exists(workPath))
    //{
    //    auto pActionImprove = ui.toolBar->addAction(QString::fromStdString(WD::WDTs("ISOEditDialog", "Improve")));
    //    pActionImprove->setIcon(QIcon(iconPath + "cheese.png"));
    //    pActionImprove->setProperty("type", "Improve");
    //    QObject::connect(pActionImprove, &QAction::triggered, this, &ISOSvgEditMainWindow::slotImproveSvg);

    //    auto pActionCalculate = ui.toolBar->addAction(QString::fromStdString(WD::WDTs("ISOEditDialog", "Calculate")));
    //    pActionCalculate->setIcon(QIcon(iconPath + "cheese.png"));
    //    pActionCalculate->setProperty("type", "Calculate");
    //    QObject::connect(pActionCalculate, &QAction::triggered, this, &ISOSvgEditMainWindow::slotCalculateSvg);
    //}
}

void ISOSvgEditMainWindow::slotSave()
{
    auto btn = qobject_cast<QAction*>(QObject::sender());
    //如果是在创建的时候点保存，恢复按钮状态，清除未创建完成的图元
    for (auto& i : ui.toolBar->actions())
    {
        if (i->isChecked() && i != btn)
        {
            i->blockSignals(true);
            i->setChecked(false);
            i->blockSignals(false);
            _pView->deleteNewCreate();
            break;
        }
    }

    if (!_pView->saveView())
    {
        return;
    }
    saveEditData(_svg.toString());
    _pView->createDrawingBounds(_areaStartPos);
    _pView->setSaved(true);
    WD_INFO_T("Common", "SaveSuccess");
}

void ISOSvgEditMainWindow::slotImproveSvg()
{
    //此接口只有调试 计算明可夫斯基和用到了
    std::vector<WD::DVec2> san1;
    san1.emplace_back(WD::DVec2(51, -143));
    san1.emplace_back(WD::DVec2(151, -143));
    san1.emplace_back(WD::DVec2(151, -43));
    std::vector<WD::DVec2> san2;
    san2.emplace_back(WD::DVec2(-50, -50));
    san2.emplace_back(WD::DVec2(50, 50));
    san2.emplace_back(WD::DVec2(-50, 50));
    auto path = _pView->addPath(san1);
    path->setStrokeColor(WD::Color(222, 255, 0, 255));
    auto path2 = _pView->addPath(san2);
    path2->setStrokeColor(WD::Color(222, 255, 0, 255));
    //此接口只有调试 计算明可夫斯基和用到了
    //正常编辑功能不用这个，后续可能会支持导入功能，所以保留了代码段
    //QString fileName = QFileDialog::getOpenFileName(
    //    this
    //    , QString::fromUtf8("打开")
    //    , ""
    //    , QString::fromUtf8("SVG文件(*.svg)"));
    //// 获取XML数据
    //WD::WDFileReader file(fileName.toLocal8Bit().data());
    //if (file.isBad())
    //{
    //    return;
    //}
    //file.readAll();
    //size_t length = file.length();
    //if (length == 0)
    //    return;

    ////解析XML文件
    //WD::XMLDoc doc;
    //doc.parse<0>((char*)file.data());
    //WD::XMLNode* pXmlNodeRoot = doc.first_node("svg");
    //if (pXmlNodeRoot == nullptr)
    //    return;
    //WD::XMLNode* pXmlNodeObjs = pXmlNodeRoot->first_node("g");
    //if (pXmlNodeObjs == nullptr)
    //    return ;

    ////拷贝绘图区节点及其子节点到新建的svg中
    //auto inputSvgNode = _svg.doc().first_node("svg");
    ////找出绘图区节点拷贝
    //WD::XMLNode* gNode = nullptr;
    //for (gNode = pXmlNodeRoot->first_node("g")
    //    ; gNode
    //    ; gNode = gNode->next_sibling("g"))
    //{
    //    if (gNode == nullptr)
    //    {
    //        continue;
    //    }
    //    WD::XMLCreator::Node tGroup(_svg.doc(), inputSvgNode->first_node("g"));
    //    ISOSvgItemHelper::CopyChildNode(&tGroup, gNode);
    //}
    //initTreeView();
}

void ISOSvgEditMainWindow::slotCalculateSvg()
{
    _pView->calculateSvg();
}