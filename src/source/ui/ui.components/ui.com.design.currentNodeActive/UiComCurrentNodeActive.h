#pragma once

#include "../../wizDesignerApp/UiInterface/UiInterface.h"

class UiComCurrentNodeActive : public IUiComponent
{
public:
    UiComCurrentNodeActive(IMainWindow& mainWindow, const UiComponentAttributes& attrs);
    ~UiComCurrentNodeActive();
public:
    virtual void onNotice(UiNotice* pNotice) override;
private:
    // 先校验指定节点(node)是否有需要激活或取消激活的祖先节点，如果有，则从该祖先节点开始，递归激活或取消激活自身以及所有子孙节点
    void setActiveFlag(WD::WDNode& node, bool active);
private:
    // 模型树当前节点改变通知
    void onCurrentNodeChanged(WD::WDNode::SharedPtr pCurrNode, WD::WDNode::SharedPtr pPrevNode, WD::WDNodeTree& sender);
private:
    std::set<std::string> _types;
};
