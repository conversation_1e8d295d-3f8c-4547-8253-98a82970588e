#pragma once

#include "ExtendItemBase.h"

/**
* @brief 显示SPCO节点的引用属性 _pSrcNode为该行的SPCO类型节点
*/
class RefTableWidgetItem : public ExtendItemBase
{
public:
    RefTableWidgetItem(WD::WDCore& core, std::string refAttrStr, WD::WDNode::SharedPtr spcoNode);
    RefTableWidgetItem(WD::WDCore& core, std::string refAttrStr);
    ~RefTableWidgetItem();
public:
    /**
    * @brief 复制当前节点信息(不包括源节点)
    * @return 返回新建的RefTableWidgetItem对象指针
    */
    virtual ExtendItemBase* copy() override;
    /**
    * @brief 修改源节点_headerText属性的值
    */
    virtual void onModify() override;
    /**
    * @brief 根据文本设置源节点_headerText属性的值
    * @param pSpcoNode  源节点
    * @param questionStr 
    * @param type 
    * @return 源节点（SPCO类型）
    */
    virtual WD::WDNode::SharedPtr onNew(WD::WDNode::SharedPtr pSpcoNode
        , const std::string questionStr
        , const std::string& type) override;
    /**
    * @brief 销毁源节点
    */
    virtual void onDelete() override;
};