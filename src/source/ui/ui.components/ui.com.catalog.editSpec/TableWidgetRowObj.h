#pragma once

#include "ExtendItemBase.h"
#include "AnswerTableWidgetItem.h"
#include "SpcoTableWidgetItem.h"
#include "RefTableWidgetItem.h"

/**
* @brief 行对象的状态枚举
*/
enum RowObjStatus
{
    Default = 0,
    Modifiied = 1,
    New = 2,
    Delete = 3
};

/**
* @brief 表格行对象,表格上的每一行都代表着一个SPCO节点
* 表格第一列是SPCO节点的名称，最后一列是SPCO节点引用的元件对象
* 在这之间的列表示中间的等级节点SELE的问题和答案
* 元件 | SPEC问题 | SELE问题 | SELE问题 ... | 元件
*/
class TableWidgetRowObj
{
public:
    // 使用SPEC节点的问题和SPCO节点的构造函数
    TableWidgetRowObj(WD::WDCore& core, WD::WDNode& spco);
    // 使用表头数据和表格内容数组的构造函数(excel导入时使用)
    TableWidgetRowObj(WD::WDCore& core, WD::StringVector& headerVec, WD::StringVector& valueVec);
    TableWidgetRowObj(WD::WDCore& core);
    ~TableWidgetRowObj(){}

    TableWidgetRowObj& operator=(const TableWidgetRowObj& rowObj)
    {
        TableWidgetRowObj *tmpobj = new TableWidgetRowObj(rowObj._core);
        tmpobj->_items = rowObj._items;
        tmpobj->_status = rowObj._status;
        return *tmpobj;
    }

public:
    // 获取表头数据
    WD::StringVector getHeaderVector();
    // 获取Item数据
    WD::StringVector getItemTextVector();
    // 获取管理的ITEM
    inline std::vector<ExtendItemBase*>& getItems()
    {
        return _items;
    }
    // 设置状态
    inline void setRowObjStatus(RowObjStatus status)
    {
        _status = status;
    }
    // 获取状态
    inline RowObjStatus getRowObjStatus() const
    {
        return _status;
    }
    // 复制，在添加一行时使用
    void copyRowObj(TableWidgetRowObj& newRowObj);
    // 使用一行数据更新Item显示
    void updateText(WD::StringVector& textVec);
    /**
    * @brief 在Modify状态下，根据Item的Text更新各个节点
    * @param pSpecNode SPEC节点
    */
    void onModifyStatus();
    /**
    * @brief 在New状态下，根据Item的answer和SPCO名称创建新节点
    * @param pSpecNode SPEC节点
    */
    void onNewStatus(WD::WDNode::SharedPtr pSpecNode);
    /**
    * @brief 在Delete状态下，根据Item的answer和SPCO名称创建新节点
    * @param pSpecNode SPEC节点
    */
    void onDeleteStatus();
private:
    /**
    * @brief 根据SPCO节点递归创建ITEM
    */
    void createItems(WD::WDNode& pNode);
    /**
     * @brief 根据SPCO节点循环创建引用ITEM
     * @param node SPCO节点
    */
    void creatRefItems(WD::WDNode& node);
private:
    WD::WDCore& _core;
    // 用来保存 表头:itemText 数据
    using ItemDataPair = std::pair<std::string, std::string>;
    // 用来保存行对象所储存的所有Item数据
    using RowDataVector = std::vector<ItemDataPair>;
    // 用来保存Item
    std::vector<ExtendItemBase*> _items;
    // 行对象状态
    RowObjStatus _status;
};