#pragma once
#include "qdialog.h"
#include "ui_SVGLogDialog.h"

class SVGLogDialog 
    : public QDialog
{
    Q_OBJECT
public:
    SVGLogDialog(QWidget *parent = nullptr);
    virtual~SVGLogDialog();
    /**
    * @brief ������־�ַ���
    */
    void    setLogStr(const QString& logStr);
public:
    virtual void showEvent(QShowEvent *) override;
    virtual void hideEvent(QHideEvent *) override;
public slots:
    void    onHandlePushButtonOkClicked();
private:
    /**
    * @brief ���淭��
    */
    void retranslateUi();
private:
    Ui::SVGLogDialog   ui;
    QString             _logStr;
};
