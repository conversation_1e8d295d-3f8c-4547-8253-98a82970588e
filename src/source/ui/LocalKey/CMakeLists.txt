set(TARGET_NAME LocalKey)

set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON) 
find_package(Qt5 COMPONENTS Widgets WebSockets REQUIRED) 
find_package(ZLIB REQUIRED) # ZLIB::ZLIB

file(GLOB_RECURSE HEADER_FILES *.h *.hpp)
file(GLOB_RECURSE SOURCE_FILES *.c *.cpp)

source_group(Header FILES ${HEADER_FILES})
source_group(Source FILES ${SOURCE_FILES})

set(RCC_FILES
    "LocalKey.qrc"
)

if(WIN32)
	add_executable(${TARGET_NAME} WIN32
			${HEADER_FILES}
			${SOURCE_FILES}
			${FORM_FILES}
            ${RCC_FILES}
            "LocalKey.rc"
	)
else()
	add_executable(${TARGET_NAME}
			${HEADER_FILES}
			${SOURCE_FILES}
			${FORM_FILES}
            ${RCC_FILES}
	)
endif()

target_link_libraries(${TARGET_NAME} PUBLIC
    Qt5::Widgets
	Qt5::WebSockets
    ZLIB::ZLIB
    util.sdk.license
	wizDesignerCore
) 

target_include_directories(${TARGET_NAME} PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})

set_target_properties(${TARGET_NAME} PROPERTIES FOLDER "ui")

DeployQt(${TARGET_NAME})