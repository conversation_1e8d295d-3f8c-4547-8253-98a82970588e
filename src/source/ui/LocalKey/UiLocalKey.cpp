
#include    "UiLocalKey.h"
#include    <QFileDialog>
#include    <QDebug>
#include    <QIcon>
#include    <QWidget>
#include    <QPainter>
#include    <QSpacerItem>
#include    <QByteArray>
#include    <QPaintEvent>
#include    "qrencode/qrencode.h"
#include    <QPushButton>
#include    <QGridLayout>
#include    <QMessageBox>
#include    <QFileDialog>
#include    <QLabel>
#include    <QUrlQuery>
#include    <QTimer>
#include    "core/WIZDesignerVersion.h"



LocalKeyWidget::LocalKeyWidget(QString licensePath,QString httpSvr,QString product,QString version)
{
    _licensePath    =   licensePath;
    _httpSvr        =   httpSvr;
    _product        =   product;
    _version        =   version;
    setupUi();
    updateData();
    resize(648,648);
    setMargin(48);

    this->setWindowTitle(QString::fromUtf8(WIZDesigner_COMPANY_NAME));
    this->setWindowIcon(QIcon(":/LocalKey/logo"));

    String  stdName     =   _product.toStdString();
    String  stdVersion  =   _version.toStdString();
    String  localData   =   Helper::collectLocalData(stdName,stdVersion);

    _webSocket  =   new WebSocket(localData,_licensePath.toLocal8Bit().data());
    _timer      =   new QTimer(this);
    /// 如果收到信息则提示成功
    _webSocket->_evtRecvLicense =   [this](bool bOK,const char* path,const char* ,size_t )
    {
        //return;
        if (bOK)
        {
            QMessageBox::information( this
                                    , QString::fromUtf8("提示")
                                    , QString::fromUtf8("授权成功，请重启软件!")
                                    , QMessageBox::Ok);
        }
        else
        {
            char        szBuf[1024] =   {0};
            sprintf(szBuf,"写入文件(%s)失败，检查目录有效性,建议尝试通过授权页面启动授权软件!",path);
            QString     qMsg    =   QString::fromUtf8(szBuf);

            QMessageBox::information( this
                , QString::fromUtf8("提示")
                , qMsg
                , QMessageBox::Ok);
        }
        
    };
    connect(_timer, SIGNAL(timeout()), this, SLOT(onTimer()));
    /// start之后,设置间隔时间并启动定时器，每隔一秒触发一次槽函数
    _timer->start(3000);
}

LocalKeyWidget::~LocalKeyWidget()
{
    delete  _timer;
    delete  _webSocket;
}


void    LocalKeyWidget::setupUi()
{
    if (this->objectName().isEmpty())
        this->setObjectName(QString::fromUtf8("this"));

    _gridLayout = new QGridLayout(this);
    _gridLayout->setObjectName(QString::fromUtf8("_gridLayout"));

    QLabel* label = new QLabel(this);
    label->setObjectName(QString::fromUtf8("label"));
    label->setGeometry(QRect(90, 40, 72, 15));
    label->setMaximumSize(QSize(16777215, 30));
    /// label->setText(CSTR2QSTR("请用手机扫描二维码获取license信息"));
    label->setText(QString::fromUtf8("请拍照发给管理员"));
    _gridLayout->addWidget(label, 0, 0, 1, 1);

    _widget = new QWidget(this);
    _widget->setObjectName(QString::fromUtf8("_widget"));

    _gridLayout->addWidget(_widget, 1, 0, 1, 1);

    _saveBtn = new QPushButton(this);
    _saveBtn->setObjectName(QString::fromUtf8("_saveBtn"));
    _saveBtn->setText(QString::fromUtf8("保存文件"));
    _gridLayout->addWidget(_saveBtn, 2, 0, 1, 1);
    QObject::connect(_saveBtn, SIGNAL(clicked()), this, SLOT(onClickedPushButton()));

    margin      =   48;
    foreground  =   QColor("black");
    background  =   QColor("white");
    casesen     =   true;
    mode        =   MODE_8;
    level       =   LEVEL_H;
    percent     =   0.23;
}

void    LocalKeyWidget::updateData()
{
    String  name        =   _product.toStdString();
    String  version     =   _version.toStdString();
    String  localData   =   Helper::collectLocalData(name,version);
    QString urlStr      =   _httpSvr + localData.c_str();
    this->text          =   urlStr.toLocal8Bit();
}


void    LocalKeyWidget::paintEvent (QPaintEvent *event) 
{
    QWidget::paintEvent (event);
    QPainter painter(this);
    QRcode *qrcode = QRcode_encodeString(text.data() , 7, (QRecLevel)level, (QRencodeMode)mode, casesen ? 1 : 0);
    if(0 != qrcode) 
    {
        unsigned char *point = qrcode->data;
        painter.setPen(Qt::NoPen);
        painter.setBrush(this->background);
        painter.drawRect(0, 0, this->width(), this->height());
        double scale = (this->width () - 2.0 * margin) / qrcode->width;
        painter.setBrush(this->foreground);
        for (int y = 0; y < qrcode->width; y ++)
        {
            for (int x = 0; x < qrcode->width; x ++)
            {
                if (*point & 1) 
                {
                    QRectF r(margin + x * scale, margin + y * scale, scale, scale);
                    painter.drawRects(&r, 1);
                }
                point ++;
            }
        }
        point = NULL;
        QRcode_free(qrcode);
    }
    qrcode = NULL;
    event->accept ();
}

void    LocalKeyWidget::onClickedPushButton()
{
    QString fileName = QFileDialog::getSaveFileName(this,
        QString::fromUtf8("另存为"),
        QString::fromUtf8("Local.LSRC"),
        QString::fromUtf8("(*.LSRC);;所有文件(*)"));

    if(fileName.isEmpty())
        return;

    QString suffix(".LSRC");
    if (!fileName.endsWith(suffix, Qt::CaseInsensitive))
        fileName += suffix;

    String  name        =   _product.toStdString();
    String  version     =   _version.toStdString();

    String  localData   =   Helper::collectLocalData(name,version);
    String  strPath     =   fileName.toLocal8Bit().data();
    FILE*   pFile       =   fopen(strPath.c_str(), "wb");
    if (pFile == 0)
    {
        QMessageBox::warning(this, QString::fromUtf8("提示"), QString::fromUtf8("创建授权文件失败，请检查是否具备文件创建权限!"));
        return;
    }
    fwrite(localData.data(),    1,  localData.size(),  pFile);
    fclose(pFile);

    QMessageBox::information(this, QString::fromUtf8("提示"), QString::fromUtf8("保存成功"));
}


void    LocalKeyWidget::onTimer()
{
    _webSocket->connectTo(nullptr);
}
