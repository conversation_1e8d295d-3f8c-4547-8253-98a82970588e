#include <QFileDialog>
#include "LicenseCheckDialog.h"
#include <time.h>
#include "core/WDLicense.h"
#include "core/common/WDFileReader.hpp"
#include "../wizDesignerApp/UiInterface/UiTranslate.h"
#include <QProcess>
#include <QMessageBox>
#include "../../utilLib/util.sdk.license/sdk.license.h"

// 产品名称
static constexpr const char* SProductName = "WIZDesigner";
// 产品类型
static constexpr const char* SProductType = "professional";
// URL
static constexpr const char* SUrl = "https://license.wiz.top/pages/p?t=";


//读取并校验授权文件
bool CheckLicenseFile(const std::string& fileName, std::string* pOutKey = nullptr)
{
    WD::WDFileReader reader(fileName.c_str());
    if (reader.isBad()) 
        return false;

    reader.readAll();

    const char*     key     = (const char*)reader.data();
    std::string     strKey  =  key; 
    if (pOutKey != nullptr)
    {
        *pOutKey = strKey;
    }

    WD::WDLicense::SetData(strKey.c_str());
    
    reader.close();

    return WD::WDLicense::Check();
}

LicenseCheckDialog::LicenseCheckDialog(QWidget *parent, const QString& windowTiltle)
    : QDialog(parent)
    ,_bSuccessed(false)
{
    ui.setupUi(this);

    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    retranslateUi();
    setWindowTitle(windowTiltle);

    connect(ui.pushButtonOk, &QPushButton::clicked, this, &LicenseCheckDialog::slotBtnOkClicked);
    connect(ui.selectFileBtn, &QPushButton::clicked, this, &LicenseCheckDialog::slotSelectFileClicked);
    connect(ui.pushButtonCreateKey, &QPushButton::clicked, this, &LicenseCheckDialog::slotBtnCreateKeyClicked);

    {
        QFileInfo info(DefaultLisenceFileName());
        ui.lineEdit->setText(info.absoluteFilePath());
    }

    _pProcess = new QProcess(this);
    {
        QProcess pro;
        QFileInfo info(LocalkeyExeName());
        _localKeyPath = info.absoluteFilePath();

        _pProcess->setProgram(_localKeyPath);

        QStringList args;
        args << DefaultLisenceFileName()    // 授权文件路径名称
            << SProductName                 // 产品名称
            << SProductType                 // 产品类型
            << SUrl;                        // url

        _pProcess->setArguments(args);
    }
    connect(_pProcess, &QProcess::errorOccurred, this, &LicenseCheckDialog::slotProcessErrorOccurred);

    _pTipDialog = new LicenseTipDialog(this);
    _pTipDialog->setWindowTitle(windowTiltle);
}
LicenseCheckDialog::~LicenseCheckDialog()
{
    disconnect(_pProcess, &QProcess::errorOccurred, this, &LicenseCheckDialog::slotProcessErrorOccurred);
    _pProcess->deleteLater();
}

void LicenseCheckDialog::slotBtnOkClicked()
{
    _bSuccessed = false;
    QString fileName = ui.lineEdit->text();
    if (fileName.isEmpty())
    {
        QPalette p = ui.resultTip->palette();
        p.setColor(QPalette::WindowText, Qt::red);
        ui.resultTip->setPalette(p);
        ui.resultTip->setText(QString::fromUtf8(WD::WDTs("LicenseCheckDialog", "× please select the authorization document").c_str()));

        return;
    }

    std::string strKey;
    _bSuccessed = CheckLicenseFile(fileName.toLocal8Bit().data(), &strKey);

    if (!_bSuccessed)
    {
        QPalette p = ui.resultTip->palette();
        p.setColor(QPalette::WindowText, Qt::red);
        ui.resultTip->setPalette(p);
        ui.resultTip->setText(QString::fromUtf8(WD::WDTs("LicenseCheckDialog", "× authorization not obtained, please select another authorization file").c_str()));

    }
    else
    {
        FILE* pFile = fopen(DefaultLisenceFileName().toLocal8Bit().data(), "wb");
        if (pFile == 0)
        {
            ui.resultTip->setText(QString::fromUtf8(WD::WDTs("LicenseCheckDialog", "√ authorization verificatin succeeded, but file saving failed").c_str()));
            return;
        }
        else
        {
            fwrite(strKey.c_str(), 1, strKey.size(), pFile);
            fclose(pFile);
        }
        close();
    }
}
void LicenseCheckDialog::slotSelectFileClicked()
{
    QString fileName = QFileDialog::getOpenFileName(this,
        QString::fromUtf8(WD::WDTs("LicenseCheckDialog", "open the authorization file").c_str())
        , QApplication::applicationDirPath()
        , QString::fromUtf8(WD::WDTs("LicenseCheckDialog", "authorization file").c_str()).append("(*)"));
    if (fileName.isEmpty())
        return;

    ui.lineEdit->setText(fileName);
}
void LicenseCheckDialog::slotBtnCreateKeyClicked()
{
    _pProcess->start();
}

void LicenseCheckDialog::slotProcessErrorOccurred(QProcess::ProcessError error)
{
    QString errStr ;
    switch (error)
    {
    case QProcess::FailedToStart:
        {
            auto info = WD::WDTs("LicenseCheckDialog", "auto start app failure");
            info += QChar::LineFeed;
            info += QChar::LineFeed;
            info += WD::WDTs("LicenseCheckDialog", "reason");
            info += ": ";
            info += WD::WDTs("LicenseCheckDialog", "the called program is missing， or does not have sufficient permissions or resources to call the program");
            info += WD::WDTs("LicenseCheckDialog", "app path");
            info += ": ";
            info += _localKeyPath.toStdString();
            info += QChar::LineFeed;
            info += QChar::LineFeed;
            info += WD::WDTs("LicenseCheckDialog", "solution");
            info += ": ";
            info += WD::WDTs("LicenseCheckDialog", "check the pragram path or try to execute the program manually");
            errStr = QString::fromUtf8(info.c_str());
        }
        break;
    case QProcess::Crashed:
        {
            auto info = WD::WDTs("LicenseCheckDialog", "auto start app failure");
            info += QChar::LineFeed;
            info += QChar::LineFeed;
            info += WD::WDTs("LicenseCheckDialog", "reason");
            info += ": ";
            info += WD::WDTs("LicenseCheckDialog", "the invoker crashes");
            info += QChar::LineFeed;
            info += QChar::LineFeed;
            info += _localKeyPath.toStdString();
            info += QChar::LineFeed;
            info += QChar::LineFeed;
            info += WD::WDTs("LicenseCheckDialog", "solution");
            info += ": ";
            info += WD::WDTs("LicenseCheckDialog", "check the pragram path or try to execute the program manually");
            errStr = QString::fromUtf8(info.c_str());
        }
        break;
    case QProcess::Timedout:
        {
            auto info = WD::WDTs("LicenseCheckDialog", "auto start app failure");
            info += QChar::LineFeed;
            info += QChar::LineFeed;
            info += WD::WDTs("LicenseCheckDialog", "reason");
            info += ": ";
            info += WD::WDTs("LicenseCheckDialog", "startup timed out");
            info += QChar::LineFeed;
            info += QChar::LineFeed;
            info += _localKeyPath.toStdString();
            info += QChar::LineFeed;
            info += QChar::LineFeed;
            info += WD::WDTs("LicenseCheckDialog", "solution");
            info += ": ";
            info += WD::WDTs("LicenseCheckDialog", "check the pragram path or try to execute the program manually");
            errStr = QString::fromUtf8(info.c_str());
        }
        break;
    case QProcess::ReadError:
        {
            auto info = WD::WDTs("LicenseCheckDialog", "auto start app failure");
            info += QChar::LineFeed;
            info += QChar::LineFeed;
            info += WD::WDTs("LicenseCheckDialog", "reason");
            info += ": ";
            info += WD::WDTs("LicenseCheckDialog", "an error occurred while trying to read from the called program");
            info += QChar::LineFeed;
            info += QChar::LineFeed;
            info += _localKeyPath.toStdString();
            info += QChar::LineFeed;
            info += QChar::LineFeed;
            info += WD::WDTs("LicenseCheckDialog", "solution");
            info += ": ";
            info += WD::WDTs("LicenseCheckDialog", "check the pragram path or try to execute the program manually");
            errStr = QString::fromUtf8(info.c_str());
        }
        break;
    case QProcess::WriteError:
        {
            auto info = WD::WDTs("LicenseCheckDialog", "auto start app failure");
            info += QChar::LineFeed;
            info += QChar::LineFeed;
            info += WD::WDTs("LicenseCheckDialog", "reason");
            info += ": ";
            info += WD::WDTs("LicenseCheckDialog", "an error occurred while trying to write from the called program");
            info += QChar::LineFeed;
            info += QChar::LineFeed;
            info += _localKeyPath.toStdString();
            info += QChar::LineFeed;
            info += QChar::LineFeed;
            info += WD::WDTs("LicenseCheckDialog", "solution");
            info += ": ";
            info += WD::WDTs("LicenseCheckDialog", "check the pragram path or try to execute the program manually");
            errStr = QString::fromUtf8(info.c_str());
        }
        break;
    case QProcess::UnknownError:
        {
            auto info = WD::WDTs("LicenseCheckDialog", "auto start app failure");
            info += QChar::LineFeed;
            info += QChar::LineFeed;
            info += WD::WDTs("LicenseCheckDialog", "reason");
            info += ": ";
            info += WD::WDTs("LicenseCheckDialog", "unknow");
            info += QChar::LineFeed;
            info += QChar::LineFeed;
            info += _localKeyPath.toStdString();
            info += QChar::LineFeed;
            info += QChar::LineFeed;
            info += WD::WDTs("LicenseCheckDialog", "solution");
            info += ": ";
            info += WD::WDTs("LicenseCheckDialog", "check the pragram path or try to execute the program manually");
            errStr = QString::fromUtf8(info.c_str());
        }
        break;
    default:
        break;
    }

    _pTipDialog->setTipText(errStr);
    _pTipDialog->exec();
}

bool CheckLicense()
{
    std::string fileName = LicenseCheckDialog::DefaultLisenceFileName().toLocal8Bit().data();
    if (CheckLicenseFile(fileName))
        return true;
    
    LicenseCheckDialog mgr;
    mgr.exec();

    return mgr.getLicenseVaild();
}
void LicenseCheckDialog::retranslateUi()
{
    Trs("LicenseCheckDialog"
    , static_cast<QDialog*>(this)
    , ui.label_2
    , ui.pushButtonOk
    , ui.pushButtonCreateKey);
}
