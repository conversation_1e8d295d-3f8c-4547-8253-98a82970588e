#include "Collaboration.h"

#include <gflags/gflags.h>

#include "WDRapidxml.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/businessModule/catalog/WDBMCatalog.h"
#include "core/businessModule/WDBMClaimMgr.h"
#include "core/common/WDStringConvert.h"

#include "common/DesignGlog.h"
#include "service/DesignKafkaService.h"
#include "service/DesignNode2Queue.h"
#include "rpc/client/DesignServiceConnectionPool.h"
#include "rpc/client/DesignServiceClient.h"
#include "service/ConfigParser.h"
#include "rpc/minio/MinioTaskState.h"
#include "rpc/minio/MinioDownloader.h"
#include "store/db_store/DbStore.h"
#include "service/DesignTLVFile.h"
#include "service/DesignQueue2RpcService.h"
#include "serialize/WDNodeCache.h"

static constexpr const char* XmlFileName = "config-DC.xml";
static constexpr const char* Cfg_Key_Db = "Cfg_Key_Db";
static constexpr const char* Cfg_Key_Grpc = "Cfg_Key_Grpc";
static constexpr const char* Cfg_Key_Active = "Cfg_Key_Active";

namespace NSVectorInt64
{
    /**
     * @brief 子节点列表是否有效[parent[c1,c2]]
     * @param vec 子节点列表
     */
    static bool Valid(const design::VectorInt64& vec)
    {
        return vec.vec_int64_size() >= 1;
    }

    /**
     * @brief 从子节点列表中获取父节点rid
     * @param vec 子节点列表
     */
    static std::optional<uint64_t> Parent(const design::VectorInt64& vec)
    {
        if (vec.vec_int64_size() < 1)
            return std::nullopt;

        return vec.vec_int64(0);
    }

    /**
    * @brief 获取子节点列表中的子节点rid列表
    * @param vec 子节点列表
    */
    static std::vector<uint64_t> Children(const design::VectorInt64& vec)
    {
        std::vector<uint64_t> children;
        auto size = vec.vec_int64_size();
        if (size == 0)
            return children;

        for (size_t i = 1; i < size; ++i)
        {
            children.push_back(vec.vec_int64(i));
        }
        return children;
    }
};

/**
 * @brief 根据节点所属模块获取队列节点类型
 * @param node 节点
 * @return 队列节点类型
 */
static wiz::DesignNode2Queue::NodeType NodeBMType(WD::WDNode& node)
{
    if (node.getBMSub<WD::WDBMDesign>() != nullptr)
        return wiz::DesignNode2Queue::NodeType::DESIGN;
    else if (node.getBMSub<WD::WDBMCatalog>() != nullptr)
        return wiz::DesignNode2Queue::NodeType::CATALOG;

    assert(false);
    // 理论上不存在这种情况，出现视为DESIGN
    return wiz::DesignNode2Queue::NodeType::DESIGN;
}

Collaboration::Collaboration(WD::WDCore& core)
    : _core(core)
{
    _pLoginServer = new LoginServer(core, *this);
    _pModelServer = new ModelServer(core, *this);

    // 默认不激活协同服务
    _isCollaboration = false;

    // 读取DC配置
    this->readConfig();

    // 初始化
    this->init();
}

Collaboration::~Collaboration()
{
}

void Collaboration::init()
{
    // 初始化日志
    auto logPath = std::string(_core.exeDirPath()) + "/logs";
    wiz::initGoogleLogging("Collaboration", logPath, true, 7);

    // 初始化grpc服务
    auto& pool = wiz::DesignServiceConnectionPool::getInstance();
    pool.initialize(_cfgInfo.grpc);
}

static void ParseXMLDoc(WD::XMLDoc& doc, char* data)
{
    try
    {
        doc.parse<0>(data);
    }
    catch (const rapidxml::parse_error&)
    {
        assert(false);
    }
    catch (...)
    {
        assert(false);
    }
}

void Collaboration::readConfig()
{
    std::string filePath = std::string(_core.dataDirPath()) + std::string(XmlFileName);
    WD::WDFileReader file(filePath);
    if (file.isBad())
        return;
    file.readAll();
    if (file.length() == 0)
        return;

    WD::XMLDoc doc;
    ParseXMLDoc(doc, (char*)file.data());
    WD::XMLNode* pXmlNodeRoot = doc.first_node("Root");
    if (pXmlNodeRoot == nullptr)
        return;
    for (WD::XMLNode* pXmlNode = pXmlNodeRoot->first_node("item")
         ; pXmlNode
         ; pXmlNode = pXmlNode->next_sibling())
    {
        auto pKeyAttr = pXmlNode->first_attribute("key");
        if (pKeyAttr == nullptr)
            continue;
        std::string key = pKeyAttr->value();

        std::string value;
        auto pValueAttr = pXmlNode->first_attribute("value");
        if (pValueAttr != nullptr)
        {
            value = pValueAttr->value();
        }

        if (key == Cfg_Key_Db)
        {
            _cfgInfo.db = value;
        }
        else if (key == Cfg_Key_Grpc)
        {
            _cfgInfo.grpc = value;
        }
        else if (key == Cfg_Key_Active)
        {
            bool bOk = false;
            auto realValue = WD::FromString<bool>(value, &bOk);
            if (bOk)
            {
                _isCollaboration = realValue;
            }
        }
    }
}


LoginServer::LoginServer(WD::WDCore& core, Collaboration& collaboration)
    : _core(core)
    , _collaboration(collaboration)
    , _nodeCache(WD::WDNodeCacheByType::getInstance())
{
}

LoginServer::LoginUserConfig LoginServer::login(std::string_view userName, std::string_view password) const
{
    LoginUserConfig userConfig;
    // 客户端服务
    auto& service = wiz::DesignServiceClient::getInstance();

    design::UserInfo user;
    user.set_user(userName);
    user.set_pwd(password);

    // 先获取公钥
    auto userConfigStr = service.LoginChain(user);
    if (!userConfigStr)
    {
        LOG(INFO) << "登录失败";
        return userConfig;
    }
    LOG(INFO) << userConfigStr.value();

    // 登录成功缓存用户账号密码
    _collaboration.userInfo().userAccount = userName;
    _collaboration.userInfo().password = password;

    // 解析登录用户配置
    userConfig = this->parseUserConfig(userConfigStr.value());

    return userConfig;
}

static bool BelongMaxTime(int64_t localTime, int64_t servarTime)
{
    return true;
}

// 下载minio文件并导入本地store
void LoginServer::downloadFromMinioAndUpsetDB(wiz::ServiceConfig serviceConfig,
                                              WD::store::IStore& store) const
{
    auto progressCallback = [](const wiz::TaskProgress& progress)
    {
        static int i = 0;

        LOG_IF(
            INFO, i%10==0
            || progress.state == wiz::MinioTaskState::COMPLETED
            || progress.state == wiz::MinioTaskState::FAILED
        ) << "\rDownloading " << progress.objectName
                    << ": " << std::fixed << std::setprecision(2) << progress.getPercentage() << "% "
                    << "(" << progress.bytesDownloaded << "/" << progress.totalBytes << " bytes) "
                    << std::setprecision(2) << (progress.speed / 1024.0 / 1024.0) << " MB/s";
        ++i;
    };

    std::vector<std::tuple<std::string, std::string, std::string>> tasks;
    for (const auto& sn : serviceConfig.minio->nodeSnapshotBasePath)
    {
        tasks.push_back(std::make_tuple(
            serviceConfig.minio->bucketName,
            sn.fileName,
            "/tmp/" + sn.fileName
        ));
    }
    wiz::MinioDownloader downloader(
        serviceConfig.minio->endpoint,
        serviceConfig.minio->accessKey,
        serviceConfig.minio->secretKey,
        false,
        4
    );
    auto taskIds = downloader.addTasks(tasks, progressCallback);
    downloader.waitForCompletion(5000);
    // 导入本地
    for (const auto& t : tasks)
    {
        auto tvf = wiz::DesignTLVFile(std::get<2>(t));
        tvf.updateStore(store);
    }
}

bool LoginServer::openProject(const UserCfgProj& project, std::string_view userRole)
{
    auto& service = wiz::DesignServiceClient::getInstance();
    // 设置项目信息
    design::UserInfo user;
    user.set_user(_collaboration.userInfo().userAccount);
    user.set_pwd(_collaboration.userInfo().password);
    // TODO: SERVER (目前没有关联角色)
    // user.set_role(_collaboration.userInfo().roleCode);
    design::ProjectInfo projectInfo;
    projectInfo.set_user(user.user());
    projectInfo.set_projectcode(project.code);

    // 获取服务配置信息
    auto configJson = service.getConfig(projectInfo);
    // LOG(INFO) << "Raw config from service: " << configJson;
    auto serviceConfig = wiz::ConfigParser::parseFromJson(configJson);
    auto mv = serviceConfig.minio->getLatestSnapshotTime();
    LOG(INFO) << "server config:" << serviceConfig.toJson() << "|last snapshot time:" << mv.first << " - " << mv.second;
    auto fileMaxUpdateTime = mv.second;
    auto dbPrefix = std::to_string(serviceConfig.projectInfo->id);
    auto storeUPtr = std::make_unique<WD::store::DbStore>(_collaboration.cfgInfo().db, dbPrefix, 10); // 10个表，这里的参数不要改动
    // 将store注入queue，这样消费数据及时保存
    auto& queue = wiz::DesignNode2Queue::getInstance();
    // 初始化节点队列
    queue.initialize(projectInfo, &_core.getBMDesign(), &_core.getBMCatalog(), 0);
    queue.setStore(std::move(storeUPtr));
    auto& store = queue.getStore();
    // 初始化节点缓存
    _nodeCache.initialize(&store, &queue.getSerializer(design::NodeType::DESIGN), &queue.getSerializer(design::NodeType::CATALOG));
    // 监测队列，发往服务端
    auto& rpcService = wiz::DesignQueue2RpcService::getInstance();
    rpcService.initialize(projectInfo);
    rpcService.start();
    rpcService.self().detach();

    auto& kafkaService = wiz::DesignKafkaService::getInstance();
    const auto& kafkaConfig = serviceConfig.kafka.value();
    // todo 这里的group id 需要保持唯一
    const std::string groupId = "dc-" + projectInfo.projectcode() + user.user();
    kafkaService.initialize(kafkaConfig.bootstrapServers, kafkaConfig.topic, groupId, 0);

    // 对比本地时间与远程时间； // 模型类型可以只填DESIGN； CATALOG因长期不变 导致差异过大
    auto localMaxUpdateTime = store.fetchMaxUpdateTime(WD::store::NodeType::DESIGN);
    if (serviceConfig.minio.value().nodeSnapshotBasePath.empty())
    {
        // 没有文件，项目未实现初始化；直接消费kafka; 等待导入
        kafkaService.start();
    }
    else
    {
        if (fileMaxUpdateTime - localMaxUpdateTime > 2 * 24 * 60 * 60 * 1000)
        {
            // 时间差异过大，直接下载
            // todo 对比 offset 差异是否过大
            downloadFromMinioAndUpsetDB(serviceConfig, store);
            localMaxUpdateTime = fileMaxUpdateTime; // 更新完之后时间就与远程文件一致
        }
        LOG(INFO) << "will start consumer from time:" << localMaxUpdateTime;
        auto partitionOffset = kafkaService.
            getOffsetByTimestamp(localMaxUpdateTime, {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11});
        // logging 用于定位问题
        for (auto& [topic, partition, offset] : partitionOffset)
        {
            LOG(INFO) << "start consumer from " << topic << " " << partition << " " << offset;
        }
        kafkaService.startFromTopicPartitions(partitionOffset);
    }

    // 启动项目成功则缓存项目信息
    _collaboration.projectInfo().code   =   project.code;
    _collaboration.projectInfo().name   =   project.name;
    _collaboration.projectInfo().id     =   project.id;

    return true;
}

void LoginServer::load() const
{
    // 本地数据库中的数据转化为节点预挂载在树上
    auto desiIds = _nodeCache.getCache(design::NodeType::DESIGN).getChildrenIdsFromStore(0);
    auto loginDesiNodes = _nodeCache.getCache(design::NodeType::DESIGN).getNodes(desiIds);
    auto cataIds = _nodeCache.getCache(design::NodeType::CATALOG).getChildrenIdsFromStore(0);
    auto loginCataNodes = _nodeCache.getCache(design::NodeType::CATALOG).getNodes(cataIds);

    // 先清除根节点的所有子节点
    auto& desiMgr = _core.getBMDesign();
    auto pDesiRoot = desiMgr.root();
    if (pDesiRoot != nullptr)
    {
        auto& children = pDesiRoot->children();
        desiMgr.destroy(children, false);

        // 再将这些节点挂到根节点上
        for (const auto& pNode : loginDesiNodes)
        {
            if (pNode == nullptr)
                continue;
            desiMgr.setParent(pNode, pDesiRoot, false);
        }
    }
    // 先清除根节点的所有子节点
    auto& cataMgr = _core.getBMCatalog();
    auto pCataRoot = cataMgr.root();
    if (pCataRoot != nullptr)
    {
        auto& children = pCataRoot->children();
        cataMgr.destroy(children, false);

        // 再将这些节点挂到根节点上
        for (const auto& pNode : loginCataNodes)
        {
            if (pNode == nullptr)
                continue;
            desiMgr.setParent(pNode, pCataRoot, false);
        }
    }
}

LoginServer::LoginUserConfig LoginServer::parseUserConfig(std::string_view str) const
{
    LoginUserConfig userConfig;

    WD::JsonDoc doc;
    doc.Parse((char*)str.data(), str.size());
    if (doc.HasParseError())
        return userConfig;
    if (!doc.IsObject())
        return userConfig;

    // TODO: SERVER 目前的数据结构不是最终形态

    // 获取角色列表
    std::vector<UserCfgRole> userRoles;
    WD::JsonValue objRoles = GetJsonValueSafely<WD::JsonValue>(doc, "role");
    if (objRoles.IsArray() && !objRoles.Empty())
    {
        for (auto& objRole : objRoles.GetArray())
        {
            auto& backEle = userRoles.emplace_back();
            backEle.name = GetJsonValueSafely<std::string>(objRole, "name");
            backEle.code = GetJsonValueSafely<std::string>(objRole, "code");
        }
    }
    // 获取项目列表
    WD::JsonValue objProjs = GetJsonValueSafely<WD::JsonValue>(doc, "projects");
    if (objProjs.IsArray() && !objProjs.Empty())
    {
        for (auto& objProj : objProjs.GetArray())
        {
            auto& backEle = userConfig.emplace_back();
            backEle.id = GetJsonValueSafely<uint64_t>(objProj, "id");
            backEle.name = GetJsonValueSafely<std::string>(objProj, "name");
            backEle.code = GetJsonValueSafely<std::string>(objProj, "code");
            backEle.roles = userRoles;
        }
    }

    return userConfig;
}


ModelServer::ModelServer(WD::WDCore& core, Collaboration& collaboration)
    : _core(core)
      , _collaboration(collaboration)
{
}

bool ModelServer::projectInitialed() const
{
    // TODO: NEWDC
    return false;
}

/**
* @brief 新增节点，包含所有子孙节点
* @param parRId 父节点rid
* @param pNodes 连续的节点列表
* @param queue 队列
* @param nodeType 队列节点类型
* @return 是否新增成功
*/
static bool AddNodesLevel(uint64_t parRId
                          , const WD::WDNode::Nodes& pNodes
                          , wiz::DesignNode2Queue& queue
                          , const wiz::DesignNode2Queue::NodeType& nodeType
                          , design::TreeActionFlag flag = design::TreeActionFlag::TAF_INSERT)
{
    if (pNodes.empty())
        return false;

    // 新增节点需先将节点推送到队列以生成协同id
    if (!queue.pushNodes(pNodes, nodeType))
    {
        LOG(ERROR) << "Error: Failed to push nodes to queue";
        return false;
    }

    // 获取左侧节点RID
    const auto& pFront = pNodes.front();
    if (pFront == nullptr)
        return false;
    uint64_t leftRid = 0;
    const auto& pLeft = pFront->prevBrother();
    if (pLeft != nullptr)
        leftRid = pLeft->getRemoteId();

    // 组织节点关系
    design::NodeTreeAction treeAction;
    treeAction.set_parentid(parRId);
    for (const auto& pNode : pNodes)
    {
        if (pNode == nullptr)
            continue;

        treeAction.add_siblings(pNode->getRemoteId());
    }
    treeAction.set_leftsiblingid(leftRid);
    treeAction.set_flag(flag);
    return queue.pushNodeTreeAction(treeAction, nodeType);
}

/**
 * @brief 遍历新增（广度优先）
 * @param parRId 父节点rid
 * @param children 子节点列表
 * @param queue 队列
 * @param nodeType 队列节点类型
 */
static void AddNodeBreadth(WD::WDNode::SharedPtr pTopLevel
                           , wiz::DesignNode2Queue& queue
                           , const wiz::DesignNode2Queue::NodeType& nodeType
                           , design::TreeActionFlag flag = design::TreeActionFlag::TAF_INSERT)
{
    if (pTopLevel == nullptr)
        return;

    std::map<uint64_t, WD::WDNode::Nodes> mapLevel;
    std::vector<uint64_t> idxLevel;
    std::queue<WD::WDNode::SharedPtr> tQueue;
    tQueue.push(pTopLevel);

    while (!tQueue.empty())
    {
        const auto& pCur = tQueue.front();
        tQueue.pop();
        if (pCur == nullptr)
            continue;
        auto parRid = pCur->getRemoteId();
        idxLevel.push_back(parRid);

        // 如果当前节点有子节点，将它们添加到映射中
        const auto& pChildren = pCur->children();
        if (!pChildren.empty())
        {
            mapLevel[parRid] = pChildren;

            // 将子节点加入队列以继续遍历
            for (const auto& pChild : pChildren)
            {
                tQueue.push(pChild);
            }
        }
    }

    // 处理新增节点数据
    for (auto idxRid : idxLevel)
    {
        auto it = mapLevel.find(idxRid);
        if (it == mapLevel.end())
            continue;

        AddNodesLevel(it->first, it->second, queue, nodeType, flag);
    }
}

/**
 * @brief 新增节点，包含所有子孙节点
 * @param parRId 父节点rid
 * @param pNodes 连续的节点列表
 * @param queue 队列
 * @param nodeType 队列节点类型
 * @return 是否新增成功
 */
static bool AddNodesR(uint64_t parRId
                      , const WD::WDNode::Nodes& pNodes
                      , wiz::DesignNode2Queue& queue
                      , const wiz::DesignNode2Queue::NodeType& nodeType
                      , design::TreeActionFlag flag = design::TreeActionFlag::TAF_INSERT)
{
    AddNodesLevel(parRId, pNodes, queue, nodeType, flag);
    for (const auto& pNode : pNodes)
    {
        if (pNode == nullptr)
            continue;

        AddNodeBreadth(pNode, queue, nodeType, flag);
    }
    return true;
}

/**
 * @brief 修改节点数据
 * @param pNodes 节点列表（可不连续？）
 * @param queue 队列
 * @param nodeType 队列节点类型
 * @return 修改是否成功
 */
static bool ModifyNodes(WD::WDNode::Nodes pNodes
                        , wiz::DesignNode2Queue& queue
                        , const wiz::DesignNode2Queue::NodeType& nodeType)
{
    if (pNodes.empty())
        return false;

    if (!queue.pushNodes(pNodes, nodeType))
    {
        LOG(ERROR) << "Error: Failed to push nodes to queue";
        return false;
    }

    return true;
}

/**
* @brief 删除节点
* @param parRId 父节点rid
* @param pNodes 连续的节点列表
* @param queue 队列
* @param nodeType 队列节点类型
* @return 是否删除成功
*/
static bool RemoveNodes(uint64_t parRId
                        , const WD::WDNode::Nodes& pNodes
                        , wiz::DesignNode2Queue& queue
                        , const wiz::DesignNode2Queue::NodeType& nodeType)
{
    if (pNodes.empty())
        return false;

    // 组织节点关系
    design::NodeTreeAction treeAction;
    treeAction.set_parentid(parRId);
    for (const auto& pNode : pNodes)
    {
        if (pNode == nullptr)
            continue;
        treeAction.add_siblings(pNode->getRemoteId());
    }
    treeAction.set_flag(design::TreeActionFlag::TAF_DELETE);
    return queue.pushNodeTreeAction(treeAction, nodeType);
}

bool ModelServer::push(const MSNodes& msNodes) const
{
    auto& queue = wiz::DesignNode2Queue::getInstance();
    for (const auto& [pWNode, action] : msNodes)
    {
        auto pNode = pWNode.lock();
        if (pNode == nullptr)
            continue;
        const auto& nodeType = NodeBMType(*pNode);
        switch (action)
        {
        case NTA_Add:
            {
                auto pParent = pNode->parent();
                if (pParent != nullptr)
                {
                    AddNodesLevel(pParent->getRemoteId(), {pNode}, queue, nodeType);
                }
            }
            break;
        case NTA_Modify:
            {
                ModifyNodes({pNode}, queue, nodeType);
            }
            break;
        case NTA_Remove:
            {
                auto pParent = pNode->parent();
                if (pParent != nullptr)
                {
                    RemoveNodes(pParent->getRemoteId(), {pNode}, queue, nodeType);
                }
            }
            break;
        default:
            break;
        }
    }
    return true;
}
bool ModelServer::push() const
{
    // 从申领管理中获取增删改移动的节点
    auto pBM = _core.currentBM();
    if (pBM == nullptr)
        return false;
    auto& pClaimMgr = pBM->claimMgr();
    auto& queue = wiz::DesignNode2Queue::getInstance();

    // TODO: 此处拿到的节点有可能会有父子关系，需要确认并优化
    for (const auto& pNode : pClaimMgr.newCreatedNodes().nodes())
    {
        if (pNode == nullptr)
            continue;
        const auto& nodeType = NodeBMType(*pNode);
        AddNodesLevel(pNode->getRemoteId(), {pNode}, queue, nodeType);
    }
    // TODO: 此处拿到的节点有可能会有父子关系，需要确认并优化
    for (const auto& pNode : pClaimMgr.deletedNodes().nodes())
    {
        if (pNode == nullptr)
            continue;
        const auto& nodeType = NodeBMType(*pNode);
        RemoveNodes(pNode->getRemoteId(), {pNode}, queue, nodeType);
    }
    const auto& pClaimedNodes = pClaimMgr.nodes();
    for (const auto& pNode : pClaimedNodes)
    {
        if (pNode == nullptr)
            continue;
        const auto& nodeType = NodeBMType(*pNode);
        ModifyNodes({pNode}, queue, nodeType);
    }

    // TODO: 通过申领管理无法获取移动的节点

    return true;
}
bool ModelServer::pushAll() const
{
    // 获取当前模块的所有顶层节点
    auto pBM = _core.currentBM();
    if (pBM == nullptr)
        return false;
    auto pRoot = pBM->root();
    if (pRoot == nullptr)
        return false;
    const auto& pNodes = pRoot->children();
    if (pNodes.empty())
        return false;

    auto& queue = wiz::DesignNode2Queue::getInstance();
    const auto& nodeType = NodeBMType(*pRoot);
    return AddNodesR(pRoot->getRemoteId(), pRoot->children(), queue, nodeType, design::TreeActionFlag::TAF_UPDATE_ALL);
}
bool ModelServer::pull()
{
    // 节点对应协同端id
    std::map<int64_t, WD::WDNode::SharedPtr> allNodes;
    auto pBM = _core.currentBM();
    if (pBM == nullptr)
        return false;
    WD::WDNode::RecursionHelpter(*pBM->root(), [&allNodes](WD::WDNode& node)
    {
        allNodes[node.getRemoteId()] = WD::WDNode::ToShared(&node);
    });

    auto& queue = wiz::DesignNode2Queue::getInstance();

    // 根据消息反序列化出的节点map
    std::map<uint64_t, WD::WDNode::SharedPtr> mapUpdateNode;
    // 根据消息解析出的节点关系
    std::vector<design::VectorInt64> relations;

    // 获取批量消息
    std::vector<wiz::KafkaMessageWrapperPtr> messages;
    auto& kafkaService = wiz::DesignKafkaService::getInstance();
    kafkaService.popMessages(messages, 100); // 一次最多获取100条消息
    for (auto& m : messages)
    {
        const auto& nodeType = m->tlv.getNodeType();
        if (m->tlv.getType() == 1)
        {
            // 节点数据
            design::NodeAttrsRecord record;
            auto ok = record.ParseFromArray(m->tlv.getData(), m->tlv.getLength());
            if (!ok)
                continue;
            // 反序列化成 WDNode
            auto pNode = WD::WDNode::MakeShared();
            queue.deserializeNode(pNode, nodeType, record);
            auto uuid = pNode->uuid().toString();
            LOG(INFO) << pNode->name() << "|" << uuid << "|traceId: " << record.traceid();

            // 保存数据库
            queue.getStore().upsetNodeAttrsRecord(record, uuid, nodeType, WD::store::IStore::StoreFrom::FromServer);

            // 根据userId区分是否需要同时更新到场景
            const auto& userId = record.additionalinfo().user();
            if (userId != _collaboration.userInfo().id)
                mapUpdateNode[pNode->getRemoteId()] = pNode;
        }
        else
        {
            // 节点关系
            design::NodeTreeRecord treeRecord;
            auto ok = treeRecord.ParseFromArray(m->tlv.getData(), m->tlv.getLength());
            if (!ok)
                continue;

            // 保存数据库
            queue.getStore().upsetNodeTreeRecord(treeRecord, nodeType);

            // 根据userId区分是否需要同时更新到场景
            const auto& userId = treeRecord.additionalinfo().user();
            if (userId != _collaboration.userInfo().id)
                relations.push_back(treeRecord.children());
        }
    }

    // 更新到缓存
    // 此时的消息已由服务器保证顺序
    for (const auto& relation : relations)
    {
        // 节点关系必须保证有父节点
        if (!NSVectorInt64::Valid(relation))
            continue;
        // 父节点
        WD::WDNode::SharedPtr pParent = nullptr;
        auto pRid = NSVectorInt64::Parent(relation);
        if (!pRid)
            continue;
        if (auto it = allNodes.find(pRid.value()); it != allNodes.end())
            pParent = it->second;
        if (pParent == nullptr)
            continue;
        auto pCurBM = pParent->getBMBase();
        if (pCurBM == nullptr)
            continue;
        // 新的子节点
        auto newChildren = NSVectorInt64::Children(relation);

        // 收集父节点于当前场景树上的子节点列表(pair-second是用来标识有没有在新的子节点列表中找到，便于删除节点)
        std::map<uint64_t, std::pair<WD::WDNode::SharedPtr, bool>> oldChilren;
        for (const auto& pChild : pParent->children())
        {
            if (pChild == nullptr)
                continue;
            oldChilren[pChild->getRemoteId()] = std::make_pair(pChild, false);
        }

        // 优先处理增删，以便接下来设置正确的节点顺序
        for (const auto& newChild : newChildren)
        {
            // 在当前子节点列表中没有找到，即为新增的节点
            if (auto it = oldChilren.find(newChild); it == oldChilren.end())
            {
                // 在缓存的更新节点中查询新增节点
                auto addNodeItr = mapUpdateNode.find(newChild);
                if (addNodeItr != mapUpdateNode.end())
                {
                    // 先将新增节点挂载父节点下，此时不排序
                    pCurBM->setParent(addNodeItr->second, pParent);
                    addNodeItr->second->update();

                    allNodes[addNodeItr->first] = addNodeItr->second;

                    // TODO: 标识当前缓存的更新节点为已处理
                }
                else
                {
                    // TODO: 当前缓存的更新节点中没有，需要在之前累计的未处理数据中查询处理
                    // TODO: 处理完成后将未处理数据中对应数据移除
                }
            }
            // 若找到了则标识，且更新节点数据
            else
            {
                it->second.second = true;

                // 找到则更新节点数据
                auto updateNodeItr = mapUpdateNode.find(newChild);
                if (updateNodeItr != mapUpdateNode.end())
                {
                    // 更新节点
                    it->second.first->copy(updateNodeItr->second.get());
                    it->second.first->update();

                    // TODO: 标识当前缓存的更新节点为已处理
                }
            }
        }
        // 遍历当前场景中的子节点列表，标识为未找到的即是需要删除的节点
        for (const auto& oldChild : oldChilren)
        {
            if (!oldChild.second.second)
            {
                pCurBM->destroy(oldChild.second.first);
                allNodes.erase(oldChild.first);
            }
        }

        // 此处为降低复杂度，蛮力设置所有子节点的关系。TODO: 提供效率更高的排序算法
        WD::WDNode::SharedPtr pNext = nullptr;
        for (auto itr = newChildren.rbegin(); itr != newChildren.rend(); ++itr)
        {
            WD::WDNode::SharedPtr pNode = nullptr;
            if (auto it = allNodes.find(*itr); it != allNodes.end())
            {
                pNode = it->second;
            }
            else
            {
                // TODO: 缓存未处理的节点关系
                continue;
            }

            pCurBM->setParent(pNode, pParent, pNext);

            pNext = pNode;
        }
        // 更新父节点
        pParent->triggerUpdate();
    }
    return true;
}
bool ModelServer::clear()
{
    auto pBM = _core.currentBM();
    if (pBM == nullptr)
        return false;
    auto nodeType = wiz::DesignNode2Queue::NodeType::DESIGN;
    if (pBM->name() == "Design")
        nodeType = wiz::DesignNode2Queue::NodeType::DESIGN;
    else if (pBM->name() == "Catalog")
        nodeType = wiz::DesignNode2Queue::NodeType::CATALOG;

    auto& queue = wiz::DesignNode2Queue::getInstance();

    // 组织节点关系
    design::NodeTreeAction treeAction;
    treeAction.set_parentid(0);
    treeAction.set_flag(design::TreeActionFlag::TAF_DELETE);
    return queue.pushNodeTreeAction(treeAction, nodeType);
}

void ModelServer::initClaimCallback()
{
    auto pBM = _core.currentBM();
    if (pBM != nullptr)
    {
        auto& claimMgrV1 = pBM->claimMgr();
        claimMgrV1.funcCheckInBefore() = std::bind(&ModelServer::checkInBefore, this, std::placeholders::_1, std::placeholders::_2);
        claimMgrV1.funcCheckInAttrBefore() = std::bind(&ModelServer::checkInAttrBefore, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
        claimMgrV1.funcCheckOutBefore() = std::bind(&ModelServer::checkOutBefore, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
    }
}

std::vector<size_t> ModelServer::checkInBefore(const WD::WDBMClaimMgr::CheckItems& items
    , bool bShowMessage)
{
    std::vector<size_t> result;
    size_t index = 0;
    for (index; index < items.size(); ++index)
        result.push_back(index);
    return result;
    // TODO: NEWDC
}
std::vector<size_t> ModelServer::checkInAttrBefore(const WD::WDBMClaimMgr::AttrCheckItems& items
    , bool bShowMessage
    , bool& bCancelModify)
{
    std::vector<size_t> result;
    size_t index = 0;
    for (index; index < items.size(); ++index)
        result.push_back(index);
    return result;
    // TODO: NEWDC
}
std::vector<size_t> ModelServer::checkOutBefore(const WD::WDBMClaimMgr::CheckItems& items
    , bool bShowMessage
    , bool& bCancelCheckOut)
{
    std::vector<size_t> result;
    size_t index = 0;
    for (index; index < items.size(); ++index)
        result.push_back(index);
    return result;
    // TODO: NEWDC
}