//
// Created for DesignService client error handling
//

#ifndef DESIGN_SERVICE_ERROR_H
#define DESIGN_SERVICE_ERROR_H

#include <stdexcept>
#include <string>
#include <grpcpp/grpcpp.h>

namespace wiz {

/**
 * 基础设计服务错误类
 */
class DesignServiceError : public std::runtime_error {
public:
    explicit DesignServiceError(const std::string& message) 
        : std::runtime_error(message) {}
};

/**
 * 连接错误
 */
class DesignServiceConnectionError : public DesignServiceError {
public:
    explicit DesignServiceConnectionError(const std::string& message) 
        : DesignServiceError("连接错误: " + message) {}
};

/**
 * 超时错误
 */
class DesignServiceTimeoutError : public DesignServiceError {
public:
    explicit DesignServiceTimeoutError(const std::string& message) 
        : DesignServiceError("超时错误: " + message) {}
};

/**
 * 认证错误
 */
class DesignServiceAuthError : public DesignServiceError {
public:
    explicit DesignServiceAuthError(const std::string& message) 
        : DesignServiceError("认证错误: " + message) {}
};

/**
 * 服务器错误
 */
class DesignServiceServerError : public DesignServiceError {
public:
    explicit DesignServiceServerError(const std::string& message) 
        : DesignServiceError("服务器错误: " + message) {}
};

/**
 * 客户端错误
 */
class DesignServiceClientError : public DesignServiceError {
public:
    explicit DesignServiceClientError(const std::string& message) 
        : DesignServiceError("客户端错误: " + message) {}
};

/**
 * 从gRPC状态创建适当的错误类型
 */
inline std::exception_ptr CreateExceptionFromStatus(const grpc::Status& status, const std::string& method) {
    std::string message = method + " 失败: " + status.error_message();
    
    switch (status.error_code()) {
        case grpc::StatusCode::DEADLINE_EXCEEDED:
            return std::make_exception_ptr(DesignServiceTimeoutError(message));
        case grpc::StatusCode::UNAVAILABLE:
            return std::make_exception_ptr(DesignServiceConnectionError(message));
        case grpc::StatusCode::UNAUTHENTICATED:
            return std::make_exception_ptr(DesignServiceAuthError(message));
        case grpc::StatusCode::INTERNAL:
        case grpc::StatusCode::UNKNOWN:
        case grpc::StatusCode::UNIMPLEMENTED:
        case grpc::StatusCode::RESOURCE_EXHAUSTED:
            return std::make_exception_ptr(DesignServiceServerError(message));
        case grpc::StatusCode::INVALID_ARGUMENT:
        case grpc::StatusCode::OUT_OF_RANGE:
        case grpc::StatusCode::FAILED_PRECONDITION:
            return std::make_exception_ptr(DesignServiceClientError(message));
        default:
            return std::make_exception_ptr(DesignServiceError(message));
    }
}

} // namespace wiz

#endif // DESIGN_SERVICE_ERROR_H
