//
// Created for DesignService client configuration
//

#ifndef DESIGN_SERVICE_CONFIG_H
#define DESIGN_SERVICE_CONFIG_H

#include <string>
#include <chrono>
#include <string>

namespace wiz
{
    /**
     * 设计服务客户端配置类
     */
    class DesignServiceConfig
    {
    public:
        /**
         * 获取配置单例实例
         */
        static DesignServiceConfig& getInstance()
        {
            static DesignServiceConfig instance;
            return instance;
        }

        /**
         * 设置服务器地址
         */
        void setServerAddress(const std::string& address)
        {
            serverAddress_ = address;
        }

        void setAuthorization(const std::string& authorization)
        {
            authorization_ = authorization;
        }

        const std::string& getAuthorization() const
        {
            return authorization_;
        }

        /**
         * 获取服务器地址
         */
        const std::string& getServerAddress() const
        {
            return serverAddress_;
        }

        /**
         * 设置默认超时时间（毫秒）
         */
        void setDefaultTimeoutMs(int timeoutMs)
        {
            defaultTimeoutMs_ = timeoutMs;
        }

        /**
         * 获取默认超时时间（毫秒）
         */
        int getDefaultTimeoutMs() const
        {
            return defaultTimeoutMs_;
        }

        /**
         * 设置最大重试次数
         */
        void setMaxRetries(int maxRetries)
        {
            maxRetries_ = maxRetries;
        }

        /**
         * 获取最大重试次数
         */
        int getMaxRetries() const
        {
            return maxRetries_;
        }

        /**
         * 设置重试间隔（毫秒）
         */
        void setRetryIntervalMs(int intervalMs)
        {
            retryIntervalMs_ = intervalMs;
        }

        /**
         * 获取重试间隔（毫秒）
         */
        int getRetryIntervalMs() const
        {
            return retryIntervalMs_;
        }

        /**
         * 设置连接池大小
         */
        void setConnectionPoolSize(int size)
        {
            connectionPoolSize_ = size;
        }

        /**
         * 获取连接池大小
         */
        int getConnectionPoolSize() const
        {
            return connectionPoolSize_;
        }

        /**
         * 设置是否使用安全连接
         */
        void setUseSecureConnection(bool useSecure)
        {
            useSecureConnection_ = useSecure;
        }

        /**
         * 获取是否使用安全连接
         */
        bool getUseSecureConnection() const
        {
            return useSecureConnection_;
        }

        /**
         * 设置SSL证书路径
         */
        void setSslCertPath(const std::string& path)
        {
            sslCertPath_ = path;
        }

        /**
         * 获取SSL证书路径
         */
        const std::string& getSslCertPath() const
        {
            return sslCertPath_;
        }

    private:
        DesignServiceConfig()
            : serverAddress_("localhost:50051"),
              authorization_(""),
              defaultTimeoutMs_(5000),
              maxRetries_(3),
              retryIntervalMs_(500),
              connectionPoolSize_(5),
              useSecureConnection_(false),
              sslCertPath_("")
        {
        }

        // 禁止拷贝和赋值
        DesignServiceConfig(const DesignServiceConfig&) = delete;
        DesignServiceConfig& operator=(const DesignServiceConfig&) = delete;

        std::string serverAddress_;
        std::string authorization_;
        int defaultTimeoutMs_;
        int maxRetries_;
        int retryIntervalMs_;
        int connectionPoolSize_;
        bool useSecureConnection_;
        std::string sslCertPath_;
    };
} // namespace wiz

#endif // DESIGN_SERVICE_CONFIG_H
