#include "MinioDownloader.h"

#include <fstream>
#include <filesystem>
#include <algorithm>
#include <chrono>
#include <glog/logging.h>

namespace wiz
{
    namespace fs = std::filesystem;

    MinioDownloader::MinioDownloader(
        const std::string& endpoint,
        const std::string& accessKey,
        const std::string& secretKey,
        bool secure,
        size_t numThreads
    ) : endpoint(endpoint),
        access<PERSON>ey(accessKey),
        secretKey(secretKey),
        secure(secure),
        numThreads(numThreads),
        running(true)
    {
        // Initialize MinIO client
        initClient();

        // Start worker threads
        for (size_t i = 0; i < numThreads; ++i)
        {
            workers.emplace_back(&MinioDownloader::workerThread, this, i);
        }
    }

    MinioDownloader::~MinioDownloader()
    {
        shutdown();
    }

    void MinioDownloader::initClient()
    {
        // Create MinIO client
        minio::s3::BaseUrl baseUrl(endpoint, secure);

        // Create a static credentials provider
        auto provider = std::make_unique<minio::creds::StaticProvider>(access<PERSON><PERSON>, secret<PERSON>ey);

        // Create the client with the provider
        client = std::make_unique<minio::s3::Client>(baseUrl, provider.release());
    }

    void MinioDownloader::workerThread(size_t threadId)
    {
        while (running)
        {
            std::shared_ptr<MinioTask> task = nullptr;

            // Get a task from the queue
            {
                std::unique_lock<std::mutex> lock(queueMutex);

                // Wait for a task or shutdown signal
                condition.wait(lock, [this]
                {
                    return !taskQueue.empty() || !running;
                });

                // Check if we're shutting down
                if (!running && taskQueue.empty())
                {
                    break;
                }

                // Get the next task
                if (!taskQueue.empty())
                {
                    task = taskQueue.front();
                    taskQueue.pop();
                }
            }

            // Process the task
            if (task)
            {
                processTask(task);
            }
        }
    }

    void MinioDownloader::processTask(const std::shared_ptr<MinioTask>& task)
    {
        // Check if the task is already running or completed
        if (task->getState() == MinioTaskState::RUNNING ||
            task->getState() == MinioTaskState::COMPLETED)
        {
            return;
        }

        // Set the task state to running
        task->setState(MinioTaskState::RUNNING);

        try
        {
            {
                std::lock_guard<std::mutex> lock(tasksMutex);
                // Create directories for the destination path
                if (!createDirectories(task->getFilePath()))
                {
                    task->notifyCompletion(false, "Failed to create destination directory");
                    return;
                }
            }

            // Get object information
            minio::s3::StatObjectArgs args;
            args.bucket = task->getBucketName();
            args.object = task->getObjectName();

            auto statResult = client->StatObject(args);

            if (!statResult)
            {
                auto err = statResult.Error();
                task->notifyCompletion(false, "Failed to get object information: " +
                                       (err ? err.String() : statResult.message));
                return;
            }

            // Set total size
            task->setTotalSize(statResult.size);

            // Download the file in chunks

            // Update task state
            bool success = downloadFileInChunks(task);
            task->notifyCompletion(success, success ? "Download completed successfully" : "Download failed");
        }
        catch (const std::exception& e)
        {
            task->notifyCompletion(false, "Exception during download: " + std::string(e.what()));
        }
    }

    bool MinioDownloader::downloadFileInChunks(const std::shared_ptr<MinioTask>& task) const
    {
        // Open the output file
        std::ofstream outputFile(task->getFilePath(), std::ios::binary | std::ios::trunc);
        if (!outputFile)
        {
            return false;
        }

        // Get total size
        uint64_t totalSize = task->getProgress().totalBytes;
        uint64_t chunkSize = task->getChunkSize();

        // Calculate the number of chunks
        uint64_t numChunks = (totalSize + chunkSize - 1) / chunkSize;
        LOG(INFO) << "Downloading " << task->getObjectName() << " chunk number:" << numChunks;
        for (uint64_t i = 0; i < numChunks; ++i)
        {
            // Check if a task is paused or canceled
            if (task->getState() == MinioTaskState::PAUSED)
            {
                // Wait until a task is resumed or canceled
                while (task->getState() == MinioTaskState::PAUSED)
                {
                    if (!running)
                    {
                        return false;
                    }
                    std::this_thread::sleep_for(std::chrono::milliseconds(100));
                }
            }

            // Check if the task is canceled
            if (task->getState() != MinioTaskState::RUNNING)
            {
                return false;
            }

            // Calculate chunk offset and length
            uint64_t offset = i * chunkSize;
            uint64_t length = chunkSize;
            if (length > totalSize - offset)
            {
                length = totalSize - offset;
            }
            // Download chunk
            if (!downloadChunk(task, offset, length, outputFile))
            {
                return false;
            }
        }

        // Close the output file
        outputFile.close();

        return true;
    }

    bool MinioDownloader::downloadChunk(
        const std::shared_ptr<MinioTask>& task,
        uint64_t offset,
        uint64_t length,
        std::ofstream& outputFile
    ) const
    {
        try
        {
            // Create a buffer for the chunk
            std::vector<char> buffer(length);

            // Download the chunk
            minio::s3::GetObjectArgs args;
            args.bucket = task->getBucketName();
            args.object = task->getObjectName();
            size_t off = offset;
            size_t len = length;
            args.offset = &off;
            args.length = &len;
            // fix range download
            args.extra_headers.Add(
                "Range", "bytes=" + std::to_string(offset) + "-" + std::to_string(offset + length - 1));

            // Setup data callback function
            size_t totalBytesRead = 0;
            args.datafunc = [&buffer, &totalBytesRead, &task, &off,&len](
                const minio::http::DataFunctionArgs& args) -> bool
                {
                    // Get the data from the args
                    const char* data = args.datachunk.data();
                    size_t size = args.datachunk.size();

                    // 记录接收到的数据的前几个字节，用于调试
                    /*    if (size > 0)
                        {
                            std::string hexData;
                            for (size_t i = 0; i < std::min(size, size_t(16)); i++)
                            {
                                char hex[8];
                                snprintf(hex, sizeof(hex), "%02X ", static_cast<unsigned char>(data[i]));
                                hexData += hex;
                            }
                            LOG(INFO) << "Received data at offset " << off << ", first bytes: " << hexData
                                << ", chunk size: " << size << ", total read so far: " << totalBytesRead;
                        }
                    */
                    // 检查是否会超过请求的长度
                    if (totalBytesRead + size > len)
                    {
                        LOG(INFO) << "Truncating received data to requested length at offset " << off;
                        size = len - totalBytesRead;
                    }


                    // Check if we have enough space in the buffer
                    if (totalBytesRead + size > buffer.size())
                    {
                        LOG(WARNING) << "Buffer size (" << buffer.size() << ") is smaller than received data at offset "
                            << off;
                        size = buffer.size() - totalBytesRead;
                    }

                    if (size > 0)
                    {
                        // Copy data to buffer
                        std::memcpy(buffer.data() + totalBytesRead, data, size);

                        // Update progress
                        task->updateProgress(size);

                        totalBytesRead += size;
                    }
                    // LOG(INFO) << "Processed data at offset " << off << ", size: " << size << ", total: " << totalBytesRead;
                    // 如果已经读取的总字节数达到或超过了请求的长度，停止接收更多数据
                    if (totalBytesRead >= len)
                    {
                        // LOG(INFO) << "Already received requested length (" << len << " bytes) at offset " << off << ", stopping";
                        return false; // 停止接收更多数据
                    }
                    return true; // Continue receiving data
                };

            auto getObjectResult = client->GetObject(args);

            if (!getObjectResult)
            {
                LOG(ERROR) << "GetObject failed: "
                    << (getObjectResult.Error() ? getObjectResult.Error().String() : "unknown error");
                return false;
            }

            if (getObjectResult.status_code != 200 && getObjectResult.status_code != 206)
            {
                LOG(ERROR) << "GetObject returned unexpected status code: " << getObjectResult.status_code;
                return false;
            }

            // Only write data if we actually received some
            if (totalBytesRead > 0)
            {
                // Seek to the correct position in the output file
                outputFile.seekp(offset, std::ios::beg);
                if (!outputFile.good())
                {
                    LOG(ERROR) << "Failed to seek to position " << offset << " in file " << task->getFilePath();
                    return false;
                }

                // Write only the bytes we actually read to the output file
                outputFile.write(buffer.data(), totalBytesRead);
                if (!outputFile.good())
                {
                    LOG(ERROR) << "Failed to write " << totalBytesRead << " bytes to file " << task->getFilePath();
                    return false;
                }

                // 记录写入的数据的前几个字节，用于调试
                /*
                std::string hexData;
                for (size_t i = 0; i < std::min(totalBytesRead, size_t(16)); i++)
                {
                    char hex[8];
                    snprintf(hex, sizeof(hex), "%02X ", (unsigned char)buffer[i]);
                    hexData += hex;
                }
                LOG(INFO) << "Write " << totalBytesRead << " bytes to " << task->getFilePath()
                    << " at offset " << offset << ", first bytes: " << hexData;
                */
            }
            else
            {
                LOG(WARNING) << "No data received for chunk at offset " << offset << " with length " << length;
            }

            return true;
        }
        catch (const std::exception& e)
        {
            LOG(ERROR) << "Exception in downloadChunk: " << e.what();
            return false;
        }
    }

    bool MinioDownloader::createDirectories(const std::string& filePath)
    {
        try
        {
            fs::path path(filePath);
            fs::path dir = path.parent_path();

            if (!dir.empty() && !fs::exists(dir))
            {
                return fs::create_directories(dir);
            }

            return true;
        }
        catch (const std::exception&)
        {
            return false;
        }
    }

    std::string MinioDownloader::addTask(
        const std::string& bucketName,
        const std::string& objectName,
        const std::string& destPath,
        const ProgressCallback& progressCallback
    )
    {
        // Create a new task
        auto task = std::make_shared<MinioTask>(
            bucketName,
            objectName,
            destPath,
            1024 * 1024, // 1MB chunk size
            progressCallback,
            false // isUpload = false
        );

        std::string taskId = task->getTaskId();

        // Add the task to the map
        {
            std::lock_guard<std::mutex> lock(tasksMutex);
            tasks[taskId] = task;
        }

        // Add the task to the queue
        {
            std::lock_guard<std::mutex> lock(queueMutex);
            taskQueue.push(task);
        }

        // Notify a worker thread
        condition.notify_one();

        return taskId;
    }

    std::vector<std::string> MinioDownloader::addTasks(
        const std::vector<std::tuple<std::string, std::string, std::string>>& tasks,
        const ProgressCallback& progressCallback
    )
    {
        std::vector<std::string> taskIds;
        taskIds.reserve(tasks.size());

        for (const auto& [bucketName, objectName, destPath] : tasks)
        {
            taskIds.push_back(addTask(bucketName, objectName, destPath, progressCallback));
        }

        return taskIds;
    }

    bool MinioDownloader::pauseTask(const std::string& taskId)
    {
        auto task = findTask(taskId);
        if (!task)
        {
            return false;
        }

        // Only pause running tasks
        if (task->getState() == MinioTaskState::RUNNING)
        {
            task->setState(MinioTaskState::PAUSED);
            return true;
        }

        return false;
    }

    bool MinioDownloader::resumeTask(const std::string& taskId)
    {
        auto task = findTask(taskId);
        if (!task)
        {
            return false;
        }

        // Only resume paused tasks
        if (task->getState() == MinioTaskState::PAUSED)
        {
            task->setState(MinioTaskState::RUNNING);

            // Add a task back to the queue
            {
                std::lock_guard<std::mutex> lock(queueMutex);
                taskQueue.push(task);
            }

            // Notify a worker thread
            condition.notify_one();

            return true;
        }

        return false;
    }

    bool MinioDownloader::cancelTask(const std::string& taskId)
    {
        auto task = findTask(taskId);
        if (!task)
        {
            return false;
        }

        // Set the task state to fail
        task->setState(MinioTaskState::FAILED);
        task->notifyCompletion(false, "Task cancelled");

        return true;
    }

    TaskProgress MinioDownloader::getTaskProgress(const std::string& taskId)
    {
        auto task = findTask(taskId);
        if (!task)
        {
            return {};
        }

        return task->getProgress();
    }

    MinioTaskState MinioDownloader::getTaskState(const std::string& taskId)
    {
        auto task = findTask(taskId);
        if (!task)
        {
            return MinioTaskState::FAILED;
        }

        return task->getState();
    }

    std::vector<std::string> MinioDownloader::getActiveTasks()
    {
        std::vector<std::string> activeTasks;

        std::lock_guard<std::mutex> lock(tasksMutex);
        for (const auto& [taskId, task] : tasks)
        {
            MinioTaskState state = task->getState();
            if (state == MinioTaskState::PENDING ||
                state == MinioTaskState::RUNNING ||
                state == MinioTaskState::PAUSED)
            {
                activeTasks.push_back(taskId);
            }
        }

        return activeTasks;
    }

    bool MinioDownloader::waitForCompletion(uint64_t timeout)
    {
        auto startTime = std::chrono::steady_clock::now();

        while (true)
        {
            // Check if all tasks are completed
            bool allCompleted = true;

            {
                std::lock_guard<std::mutex> lock(tasksMutex);
                for (const auto& [taskId, task] : tasks)
                {
                    MinioTaskState state = task->getState();
                    if (state == MinioTaskState::PENDING ||
                        state == MinioTaskState::RUNNING ||
                        state == MinioTaskState::PAUSED)
                    {
                        allCompleted = false;
                        break;
                    }
                    LOG(INFO) << "Task: " << taskId << " State: " << (int)state << "|" << task->getObjectName();
                }
            }

            if (allCompleted)
            {
                return true;
            }

            // Check timeout
            if (timeout > 0)
            {
                auto currentTime = std::chrono::steady_clock::now();
                auto elapsedMs = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - startTime).count();

                if (elapsedMs >= timeout)
                {
                    return false;
                }
            }

            // Sleep for a short time
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }

    void MinioDownloader::shutdown()
    {
        // Set the running flag to false
        running = false;

        // Notify all worker threads
        condition.notify_all();

        // Wait for all worker threads to finish
        for (auto& worker : workers)
        {
            if (worker.joinable())
            {
                worker.join();
            }
        }

        // Clear workers
        workers.clear();
    }

    const std::shared_ptr<MinioTask> MinioDownloader::getTask(const std::string& task_id)
    {
        auto it = tasks.find(task_id);
        if (it != tasks.end())
        {
            return it->second;
        }
        return nullptr;
    }

    std::shared_ptr<MinioTask> MinioDownloader::findTask(const std::string& taskId)
    {
        std::lock_guard<std::mutex> lock(tasksMutex);

        auto it = tasks.find(taskId);
        if (it != tasks.end())
        {
            return it->second;
        }

        return nullptr;
    }
} // namespace wiz
