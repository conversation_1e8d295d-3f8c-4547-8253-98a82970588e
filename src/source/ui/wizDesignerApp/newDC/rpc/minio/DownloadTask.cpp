#include "DownloadTask.h"
#include <sstream>
#include <iomanip>

namespace wiz {

DownloadTask::DownloadTask(
    const std::string& bucketName,
    const std::string& objectName,
    const std::string& destPath,
    uint64_t chunkSize,
    ProgressCallback progressCallback,
    CompletionCallback completionCallback
) : bucketName(bucketName),
    objectName(objectName),
    destPath(destPath),
    chunkSize(chunkSize),
    state(DownloadTaskState::PENDING),
    progressCallback(progressCallback),
    completionCallback(completionCallback) {
    
    // Initialize progress
    progress.objectName = objectName;
    progress.bytesDownloaded = 0;
    progress.totalBytes = 0;
    progress.speed = 0.0;
    progress.lastUpdateTime = std::chrono::steady_clock::now();
}

DownloadTaskState DownloadTask::getState() const {
    return state.load();
}

void DownloadTask::setState(DownloadTaskState newState) {
    state.store(newState);
}

DownloadProgress DownloadTask::getProgress() const {
    std::lock_guard<std::mutex> lock(mutex);
    return progress;
}

void DownloadTask::updateProgress(uint64_t bytesDownloaded) {
    std::lock_guard<std::mutex> lock(mutex);
    
    // Calculate time since last update
    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - progress.lastUpdateTime).count();
    
    // Update bytes downloaded
    progress.bytesDownloaded += bytesDownloaded;
    
    // Calculate download speed (bytes per second)
    if (duration > 0) {
        double seconds = duration / 1000.0;
        progress.speed = bytesDownloaded / seconds;
        progress.lastUpdateTime = now;
    }
    
    // Notify progress callback
    notifyProgress();
}

void DownloadTask::setTotalSize(uint64_t size) {
    std::lock_guard<std::mutex> lock(mutex);
    progress.totalBytes = size;
}

void DownloadTask::notifyProgress() {
    if (progressCallback) {
        progressCallback(progress);
    }
}

void DownloadTask::notifyCompletion(bool success, const std::string& message) {
    if (completionCallback) {
        completionCallback(success, message);
    }
}

std::string DownloadTask::getTaskId() const {
    std::stringstream ss;
    ss << bucketName << "/" << objectName;
    
    // Create a hash of the string for a shorter ID
    std::hash<std::string> hasher;
    size_t hash = hasher(ss.str());
    
    // Convert hash to hex string
    ss.str("");
    ss << std::hex << std::setw(16) << std::setfill('0') << hash;
    
    return ss.str();
}

} // namespace wiz
