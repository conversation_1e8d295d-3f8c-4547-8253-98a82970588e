#pragma once

#include <string>
#include <functional>
#include <atomic>
#include <mutex>
#include <memory>
#include <filesystem>
#include <chrono>

namespace wiz {

/**
 * @brief 表示上传任务当前状态的枚举
 */
enum class UploadTaskState {
    PENDING,    ///< 任务正在等待处理
    RUNNING,    ///< 任务当前正在处理中
    PAUSED,     ///< 任务已暂停
    COMPLETED,  ///< 任务已成功完成
    FAILED      ///< 任务已失败
};

/**
 * @brief 保存上传进度信息的结构体
 */
struct UploadProgress {
    std::string objectName;                  ///< 正在上传的对象名称
    uint64_t bytesUploaded;                 ///< 目前已上传的字节数
    uint64_t totalBytes;                    ///< 需要上传的总字节数
    double speed;                           ///< 上传速度（字节/秒）
    std::chrono::steady_clock::time_point lastUpdateTime; ///< 进度最后更新时间

    /**
     * @brief 获取上传进度的百分比
     * @return 进度百分比（0-100）
     */
    double getPercentage() const {
        if (totalBytes == 0) return 0.0;
        return static_cast<double>(bytesUploaded) / totalBytes * 100.0;
    }
};

/**
 * @brief 进度更新的回调函数类型
 */
using ProgressCallback = std::function<void(const UploadProgress&)>;

/**
 * @brief 任务完成的回调函数类型
 */
using CompletionCallback = std::function<void(bool success, const std::string& message)>;

/**
 * @brief 表示单个上传任务的类
 */
class UploadTask {
public:
    /**
     * @brief 上传任务的构造函数
     * @param bucketName 目标存储桶名称
     * @param objectName 要上传的对象名称
     * @param sourcePath 要上传的本地文件路径
     * @param contentType 文件的内容类型（MIME类型）
     * @param chunkSize 分块上传时每个块的大小（字节）
     * @param progressCallback 进度更新的回调函数
     * @param completionCallback 任务完成的回调函数
     */
    UploadTask(
        const std::string& bucketName,
        const std::string& objectName,
        const std::string& sourcePath,
        const std::string& contentType = "application/octet-stream",
        uint64_t chunkSize = 5 * 1024 * 1024, // 5MB default chunk size
        ProgressCallback progressCallback = nullptr,
        CompletionCallback completionCallback = nullptr
    );

    /**
     * @brief 获取任务的当前状态
     * @return 当前任务状态
     */
    UploadTaskState getState() const;

    /**
     * @brief 设置任务的状态
     * @param state 新的任务状态
     */
    void setState(UploadTaskState state);

    /**
     * @brief 获取任务的当前进度
     * @return 当前进度信息
     */
    UploadProgress getProgress() const;

    /**
     * @brief 更新任务的进度
     * @param bytesUploaded 本次更新上传的字节数
     */
    void updateProgress(uint64_t bytesUploaded);

    /**
     * @brief 获取存储桶名称
     * @return 存储桶名称
     */
    const std::string& getBucketName() const { return bucketName; }

    /**
     * @brief 获取对象名称
     * @return 对象名称
     */
    const std::string& getObjectName() const { return objectName; }

    /**
     * @brief 获取源文件路径
     * @return 源文件路径
     */
    const std::string& getSourcePath() const { return sourcePath; }

    /**
     * @brief 获取内容类型
     * @return 内容类型
     */
    const std::string& getContentType() const { return contentType; }

    /**
     * @brief 获取块大小
     * @return 块大小（字节）
     */
    uint64_t getChunkSize() const { return chunkSize; }

    /**
     * @brief 设置要上传的文件的总大小
     * @param size 总大小（字节）
     */
    void setTotalSize(uint64_t size);

    /**
     * @brief 如果存在进度回调函数，则调用它
     */
    void notifyProgress();

    /**
     * @brief 如果存在完成回调函数，则调用它
     * @param success 上传是否成功
     * @param message 描述结果的消息
     */
    void notifyCompletion(bool success, const std::string& message);

    /**
     * @brief 获取此任务的唯一标识符
     * @return 任务ID
     */
    std::string getTaskId() const;

    /**
     * @brief 设置上传ID（用于分块上传）
     * @param id 上传ID
     */
    void setUploadId(const std::string& id) { uploadId = id; }

    /**
     * @brief 获取上传ID
     * @return 上传ID
     */
    const std::string& getUploadId() const { return uploadId; }

private:
    std::string bucketName;                  ///< 目标存储桶名称
    std::string objectName;                  ///< 要上传的对象名称
    std::string sourcePath;                  ///< 要上传的本地文件路径
    std::string contentType;                 ///< 文件的内容类型
    uint64_t chunkSize;                      ///< 分块上传时每个块的大小
    std::string uploadId;                    ///< 分块上传的上传ID

    std::atomic<UploadTaskState> state;      ///< 任务的当前状态
    UploadProgress progress;                 ///< 任务的当前进度

    ProgressCallback progressCallback;       ///< 进度更新的回调函数
    CompletionCallback completionCallback;   ///< 任务完成的回调函数

    mutable std::mutex mutex;                ///< 用于线程安全的互斥锁
};

} // namespace wiz
