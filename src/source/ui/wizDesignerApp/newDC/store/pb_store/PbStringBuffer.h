//
// Created by everpan on 25-1-13.
//

#ifndef STRING_BUFFER_H
#define STRING_BUFFER_H

#include <string>

class PbStringBuffer : public std::string {
    // std::string _buffer;
    int _consumed_pos = 0;
    size_t _inner_size = 0;

public:
    PbStringBuffer() = default;

    ~PbStringBuffer() = default;

    PbStringBuffer(const PbStringBuffer &) = delete;

    PbStringBuffer &operator=(const PbStringBuffer &) = delete;

    //StringBuffer(const StringBuffer &) = delete;

    char *allocate(int size);

    char *allocate_fast(int size);

    int free(int len);

    int free_size() const;

    void resize(size_t size);

    size_t size() const;

    void reserve(size_t size);

    void clear();

    // consumer
    const char *consume(int len);

    const char *consume_fast(int size);

    int consume_pos() const;

    int trim_consumed();

    int remain_size() const;

    std::string output_status() const;
};


#endif //STRING_BUFFER_H
