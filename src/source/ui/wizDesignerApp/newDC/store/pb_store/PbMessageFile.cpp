//
// Created by everpan on 25-1-9.
//

#include "PbMessageFile.h"

PbMessageFile::PbMessageFile(const string &fileName, PbMessageFileType type)
    : _type(type) {
    _file = std::make_shared<FileRandomWriterReader>(fileName);
    _file->open();
    _append_offset = PB_MESSAGE_FILE_HEADER_LENGTH;
    auto file_size = _file->fileSize();
    if (file_size > PB_MESSAGE_FILE_HEADER_LENGTH) {
        // todo 如果最后发生了错误，则可能后续的写入出现问题，不定期要checkpoint；后续优化
        _append_offset = file_size;
    }
    _read_offset = PB_MESSAGE_FILE_HEADER_LENGTH;
}

PbMessageFile::~PbMessageFile() {
    flush();
    _file->close();
}

PbMessageFile::Ptr PbMessageFile::MakeShared(const string &fileName, PbMessageFileType type) {
    return std::make_shared<PbMessageFile>(fileName, type);
}

int PbMessageFile::read_more() {
    if (_read_buffer.capacity() < PB_MESSAGE_BUFFER_SIZE) {
        _read_buffer.reserve(PB_MESSAGE_BUFFER_SIZE);
    }
    _read_buffer.trim_consumed();
    auto remain = _read_buffer.remain_size();
    auto len = _read_buffer.capacity() - remain;
    _read_buffer.resize(_read_buffer.capacity()); // -- very important
    auto r_len = _file->pread(_read_buffer.data() + remain
                              , len, _read_offset);

    if (r_len <= 0) { return r_len; }
    _read_offset += r_len;
    _read_buffer.resize(remain + r_len);
    return r_len;
}

off_t PbMessageFile::append(const MessageTyp &message, int *len, uint32_t *crc32) {
    int msg_size = (int) message.ByteSizeLong();
    auto len_size = PbMessageHelper::LengthSize(msg_size);
    auto w_len = len_size + msg_size;
    auto capacity = _write_buffer.capacity();
    if (capacity < PB_MESSAGE_BUFFER_SIZE) {
        _write_buffer.reserve(PB_MESSAGE_BUFFER_SIZE * 2);
    }
    auto buffer_offset = _write_buffer.size();
    auto addr = reinterpret_cast<uint8_t *>(_write_buffer.allocate(w_len));
    if (addr == nullptr) {
        // full ?
        flush();
        capacity = _write_buffer.capacity();
        if (w_len > capacity) {
            _write_buffer.reserve(w_len + PB_MESSAGE_BUFFER_SIZE);
        }
        buffer_offset = _write_buffer.size();
        addr = reinterpret_cast<uint8_t *>(_write_buffer.allocate(w_len));
    }
    auto addr2 = PbMessageHelper::WriteLength(msg_size, addr);
    if (message.SerializeToArray(addr2, msg_size)) {
        if (len != nullptr) { *len = w_len; }
        if (crc32 != nullptr) { *crc32 = FastCRC32(addr, w_len); }
        return _append_offset + buffer_offset;
    }
    _write_buffer.free(w_len);
    return -1;
}

int PbMessageFile::read_message_length() {
    auto remain_size = _read_buffer.remain_size();
    if (remain_size < 1) return -1;
    int n = remain_size > 5 ? 5 : remain_size;
    auto addr1 = (uint8_t *) _read_buffer.consume_fast(0);
    auto addr2 = addr1 + n;

    int len_size = 0, pos = 0;
    while (addr1 < addr2 && *addr1 >= 0x80) {
        len_size |= (*addr1 & 0x7f) << pos;
        pos += 7;
        ++addr1;
    }

    if (addr1 == addr2) { return -1; }
    len_size |= (*addr1 & 0x7f) << pos;
    return len_size;
}

off_t PbMessageFile::read(MessageTyp &message) {
    auto msg_len = read_message_length();

    if (msg_len < 0) {
        auto r = read_more();
        if (r <= 0) return r;
        return read(message);
    }
    if (msg_len == 0) {
        _read_buffer.consume_fast(1);
        message.Clear();
        return _read_offset - _read_buffer.remain_size(); // consume_pos;
    }
    auto size_len = PbMessageHelper::LengthSize(msg_len);
    auto remain_size = _read_buffer.remain_size();
    if (remain_size < size_len + msg_len) {
        if (msg_len + size_len > _read_buffer.capacity()) {
            _read_buffer.reserve(msg_len + size_len + PB_MESSAGE_BUFFER_SIZE);
        }
        auto r = read_more();
        if (r <= 0) return r;
        return read(message);
    }
    auto addr = _read_buffer.consume_fast(size_len + msg_len);
    if (message.ParseFromArray(addr + size_len, msg_len)) {
        return _read_offset - _read_buffer.remain_size();
    }
    return -1;
}

bool PbMessageFile::read(off_t begin, int len, std::string &cache, MessageTyp &message) {
    if (len <= 0) return false;
    cache.reserve(len + 1);
    auto r_len = _file->pread(cache.data(), len, begin);
    if (r_len <= 0) return false;
    if (r_len == len) {
        auto addr = reinterpret_cast<uint8_t *>(cache.data());
        size_t msg_len = 0;
        auto addr2 = PbMessageHelper::ReadLength(&msg_len, addr);
        return message.ParseFromArray(addr2, msg_len);
    }
    return false;
}

void PbMessageFile::flush() {
    if (_write_buffer.empty()) { return; }
    _file->pwrite(_write_buffer.data(), _write_buffer.size(), _append_offset);
    _append_offset += static_cast<off_t>(_write_buffer.size());
    _write_buffer.clear();
}

void PbMessageFile::truncate() {
    _file->truncate(PB_MESSAGE_FILE_HEADER_LENGTH);
    _append_offset = PB_MESSAGE_FILE_HEADER_LENGTH;
    _read_offset = PB_MESSAGE_FILE_HEADER_LENGTH;
}

off_t PbMessageFile::GetAppendOffset() const {
    return _append_offset;
}

off_t PbMessageFile::GetReadOffset() const {
    return _read_offset;
}

PbMessageFile::FilePtr PbMessageFile::GetFile() {
    return _file;
}

uint32_t PbMessageFile::FastCRC32(void *buffer, int len) {
    return crc32(0L, reinterpret_cast<const Bytef *>(buffer), len);
}
