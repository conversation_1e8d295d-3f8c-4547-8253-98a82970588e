//
// Created by everpan on 25-1-13.
//

#include "PbStringBuffer.h"


char *PbStringBuffer::allocate(int len) {
    if (len < 1) { return nullptr; }
    if (free_size() < len) { return nullptr; }
    return allocate_fast(len);
}

char *PbStringBuffer::allocate_fast(int len) {
    auto p = data() + _inner_size;
    auto new_size = _inner_size + len;
    resize(new_size);
    return p;
}

int PbStringBuffer::free(int len) {
    if (len < 1) { return -1; }
    if (size() < len) { return -1; }
    auto new_size = _inner_size - len;
    resize(new_size);
    return len;
}

int PbStringBuffer::free_size() const {
    return static_cast<int>(capacity()) - _inner_size;
}

void PbStringBuffer::resize(size_t size) {
    if (size > capacity()) {
        reserve(size);
    }
    _inner_size = size;
}

size_t PbStringBuffer::size() const {
    return _inner_size;
}

void PbStringBuffer::reserve(size_t size) {
    std::string::reserve(size);
    std::string::resize(size);
}

void PbStringBuffer::clear() {
    _inner_size = 0;
}

int PbStringBuffer::trim_consumed() {
    if (_consumed_pos <= 0) return 0;
    const auto remain = remain_size();
    // if (remain > _consumed_pos) return 0;
    const auto begin = data();
    const auto consumer_begin = begin + _consumed_pos;
    memcpy(begin, consumer_begin, remain);
    auto old_size = _consumed_pos;
    _consumed_pos = 0;
    resize(remain);
    return old_size;
}

const char *PbStringBuffer::consume(int len) {
    if (_consumed_pos >= _inner_size) return nullptr;
    if (len > remain_size()) return nullptr;
    auto begin = data();
    auto consumer_begin = begin + _consumed_pos;
    // resize(_consumer_pos);
    _consumed_pos += len;
    return consumer_begin;
}

const char *PbStringBuffer::consume_fast(int size) {
    auto consumer_begin = data() + _consumed_pos;
    // resize(_consumer_pos);
    _consumed_pos += size;
    return consumer_begin;
}

int PbStringBuffer::consume_pos() const {
    return _consumed_pos;
}

int PbStringBuffer::remain_size() const {
    return _inner_size - _consumed_pos;
}

std::string PbStringBuffer::output_status() const {
    return "size=" + std::to_string(size())
           + ", consumed_pos=" + std::to_string(_consumed_pos)
           + ", remain_size=" + std::to_string(remain_size())
           + ", capacity=" + std::to_string(capacity())
           + ", free_size=" + std::to_string(free_size());
}
