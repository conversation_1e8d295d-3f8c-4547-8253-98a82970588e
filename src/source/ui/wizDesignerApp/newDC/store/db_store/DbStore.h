#pragma once

#include <string>
#include <memory>
#include <vector>
#include "../IStore.h"
#include "proto/node.pb.h"

namespace WD::store
{
    /**
     * @brief Database storage interface based on sqlite_orm
     * Implements the StoreInterface interface, providing storage functionality for node attributes and tree structures
     * Uses table sharding based on node ID modulo tableSize for better performance
     */
    class DbStore final : public IStore
    {
    public:
        /**
         * @brief Constructor
         * @param path Database file directory path
         * @param prefix Project identifier
         * @param tableSize Number of shards to create for node attribute tables
         */
        explicit DbStore(const std::string& path, const std::string& prefix, int tableSize);

        /**
         * @brief Destructor
         */
        ~DbStore() override;

        /**
         * @brief Update or insert a node attribute record
         * @param record Node attribute record to update or insert
         * @param type Node type (DESIGN or CATALOG)
         * @return Whether the operation was successful
         */
        bool upsetNodeAttrsRecord(const design::NodeAttrsRecord& record, const std::string& uuid,
                                  NodeType type, StoreFrom from) override;

        /**
         * @brief Delete a node
         * @param id Node ID
         * @param type Node type (DESIGN or CATALOG)
         * @return Whether the deletion was successful
         */
        bool deleteNode(int64_t id, NodeType type) override;

        /**
         * @brief Get a node attribute record
         * @param id Node ID
         * @param type Node type (DESIGN or CATALOG)
         * @return Node attribute record
         */
        design::NodeAttrsRecord getNodeAttrsRecord(int64_t id, NodeType type) override;

        /**
         * @brief Update or insert a node tree record
         * @param record Node tree record to update or insert
         * @param type Node type (DESIGN or CATALOG)
         * @return Whether the operation was successful
         */
        bool upsetNodeTreeRecord(const design::NodeTreeRecord& record, NodeType type) override;

        /**
         * @brief Delete a tree structure
         * @param parent Parent node ID
         * @param type Node type (DESIGN or CATALOG)
         * @return Whether the deletion was successful
         */
        bool deleteTree(int64_t parent, NodeType type) override;

        /**
         * @brief Get a node tree record
         * @param parent Parent node ID
         * @param type Node type (DESIGN or CATALOG)
         * @return Node tree record
         */
        design::NodeTreeRecord getNodeTreeRecord(int64_t parent, NodeType type) override;
        std::vector<int64_t> getNodeChildIds(int64_t parent, NodeType type) override;

        int64_t fetchMaxUpdateTime(NodeType type) override;

    private:
        class Impl;
        std::unique_ptr<Impl> pImpl;
    };
} // namespace WD::store
