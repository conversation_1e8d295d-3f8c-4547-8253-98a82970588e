//
// Created by everpan on 25-5-13.
//

#include "CommonStore.h"
#include <filesystem>
#include <glog/logging.h>

namespace fs = std::filesystem;

namespace WD::store
{
    CommonStore::CommonStore(const std::string& pathStr)
    {
        if (!fs::exists(pathStr))
        {
            fs::create_directories(pathStr);
        }
        auto p = fs::path(pathStr);
        auto dbFile = p.string();
        try
        {
            _storages[Attrs] = std::make_unique<CodePairStorage>(makeCodePairStorage(dbFile, "attrs"));
            _storages[Attrs]->sync_schema();
            _storages[Type] = std::make_unique<CodePairStorage>(makeCodePairStorage(dbFile, "type"));
            _storages[Type]->sync_schema();
        }
        catch (const std::exception& e)
        {
            throw std::runtime_error("Failed to create code pair storage: " + std::string(e.what()));
        }
    }

    std::map<std::string, int64_t> CommonStore::load(CodePairType type)
    {
        auto& s = _storages[type];
        auto ret = s->get_all_optional<CodePairRow>();
        std::map<std::string, int64_t> m;
        for (const auto& r : ret)
        {
            LOG(INFO) << (type == CodePairType::Attrs ? "attrs: " : "types: ") << r->key << ":" << r->code;
            m[r->key] = r->code;
        }
        return m;
    }

    void CommonStore::save(const std::map<std::string, int64_t>& data, CodePairType type) const
    {
        const auto& s = _storages[type];
        s->transaction([&]
        {
            for (const auto& [k,v] : data)
            {
                s->replace(CodePairRow{k, v});
            }
            return true;
        });
    }
}
