//
// Created by everpan on 2025/4/19.
//

#ifndef DESIGN_SERVICE_BOOTSTRAP_H
#define DESIGN_SERVICE_BOOTSTRAP_H

#include "DesignNode2Queue.h"
#include "serialize/WDBMProtobufSerialize.h"
#include "rpc/client/DesignServiceConnectionPool.h"

namespace wiz
{
    class DesignServiceBootstrap
    {
    public:
        static void start(const design::ProjectInfo& projectInfo,
                          const std::string& dataPath, // 数据存储位置
                          const std::string& serverAddress,
                          WD::WDBMBase* designBase,
                          WD::WDBMBase* catalogBase);
    };
}


#endif //DESIGN_SERVICE_BOOTSTRAP_H
