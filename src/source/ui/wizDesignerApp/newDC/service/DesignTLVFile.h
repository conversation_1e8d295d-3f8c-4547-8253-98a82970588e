//
// Created by everpan on 25-5-13.
//

#pragma once

#include <string>
#include "common/RandomAccessFile.h"
#include "DesignTLV.h"
#include "store/IStore.h"

namespace wiz
{
    class DesignTLVFile
    {
    public:
#pragma pack(push,1)
        struct alignas(1) Header
        {
            int blockSize = 0;
            uint8_t version = 0;
            uint8_t moduleType = 0;
            uint8_t recordType = 0;
            uint64_t lastTime = 0;
            uint64_t projectId = 0;
            std::string toJson() const;
        };
#pragma pack(pop)

        explicit DesignTLVFile(const std::string& fName);
        ~DesignTLVFile() = default;
        /**
         * 获取文件头
         * @return 文件头
         */
        const Header& getHeader() const;

        /**
         * 文件是否有效
         * @return 是否有效
         */
        bool isValid() const;

        /**
         * 获取最大块数
         * @return 最大块数
         */
        uint32_t maxBlockSize() const;

        /**
         * 读取文件块到data
         * @param block 块号
         * @param buffer 缓存，不能被释放
         * @return 是否成功
         */
        bool fetchBlockData(int block, std::string& buffer) const;

        /**
         * 读取文件块到data，并解析成 TLV 列表
         * @param block 块号
         * @param buffer 缓存，不能被释放
         * @return TLV列表
         */
        std::vector<DesignTLV> fetchBlockTLV(int block, std::string& buffer) const;
        int updateStore(WD::store::IStore& store) const;

    private:
        std::unique_ptr<RandomAccessFile> _file;
        Header _header;
        uint32_t _maxBlockSize = 0;
        bool _isvalid = false;
    };
}
