//
// Created by everpan on 2025/4/15.
//

#ifndef CODEPAIRFACTORY_H
#define CODEPAIRFACTORY_H

#include "common/CodePairInterface.h"
#include "common/CodePair.h"
#include "common/CodePairGrpc.h"

class CodePairFactory {
public:
      enum CodePairType {
            Code_UUID = 1, // subTopic == uuid
            Code_Attr_key = 2, // subTopic == attr_key
            Code_Type_Val = 3, // subTopic = type_val
        };
      static CodePairContainerPtr CreateLocalCodePairContainer();

      static CodePairContainerPtr CreateRpcCodePairContainer(const CodePairGrpc::StubPtr& client,const design::ProjectInfo& project_info);
};



#endif //CODEPAIRFACTORY_H
