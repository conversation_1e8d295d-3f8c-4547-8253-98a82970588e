#include <iostream>
#include "../DesignKafkaService.h"

using namespace wiz;

// 辅助函数：打印KafkaMessageMetadata
void printMessageMetadata(const KafkaMessageMetadata& metadata) {
    std::cout << "消息元数据:" << std::endl;
    std::cout << "  主题: " << metadata.topic << std::endl;
    std::cout << "  分区: " << metadata.partition << std::endl;
    std::cout << "  偏移量: " << metadata.offset << std::endl;
    std::cout << "  键: " << metadata.key << std::endl;
    std::cout << "  时间戳: " << metadata.timestamp << std::endl;
}

// 辅助函数：打印KafkaPartitionInfo
void printPartitionInfo(const KafkaPartitionInfo& info) {
    std::cout << "分区信息:" << std::endl;
    std::cout << "  主题: " << info.topic << std::endl;
    std::cout << "  分区: " << info.partition << std::endl;
    std::cout << "  最小偏移量: " << info.lowOffset << std::endl;
    std::cout << "  最大偏移量: " << info.highOffset << std::endl;
    std::cout << "  最后稳定偏移量: " << info.lastStableOffset << std::endl;
    std::cout << "  日志起始偏移量: " << info.logStartOffset << std::endl;
    std::cout << "  领导者: " << info.leader << std::endl;
    
    std::cout << "  副本: ";
    for (const auto& replica : info.replicas) {
        std::cout << replica << " ";
    }
    std::cout << std::endl;
    
    std::cout << "  同步副本: ";
    for (const auto& isr : info.inSyncReplicas) {
        std::cout << isr << " ";
    }
    std::cout << std::endl;
}

// 辅助函数：打印KafkaConsumerGroupInfo
void printConsumerGroupInfo(const KafkaConsumerGroupInfo& info) {
    std::cout << "消费组信息:" << std::endl;
    std::cout << "  组ID: " << info.groupId << std::endl;
    std::cout << "  状态: " << info.state << std::endl;
    std::cout << "  协调者: " << info.coordinator << std::endl;
    std::cout << "  成员数量: " << info.memberCount << std::endl;
    
    std::cout << "  成员列表:" << std::endl;
    for (const auto& member : info.members) {
        std::cout << "    " << member << std::endl;
    }
    
    std::cout << "  分配信息:" << std::endl;
    for (const auto& assignment : info.assignments) {
        const auto& [topic, partition, offset] = assignment;
        std::cout << "    主题: " << topic << ", 分区: " << partition << ", 偏移量: " << offset << std::endl;
    }
}

int main() {
    try {
        std::cout << "=== Kafka JSON 序列化/反序列化示例 ===" << std::endl;
        
        // 示例1: KafkaMessageMetadata 序列化和反序列化
        std::cout << "\n--- 示例1: KafkaMessageMetadata 序列化和反序列化 ---" << std::endl;
        
        // 创建一个KafkaMessageMetadata对象
        KafkaMessageMetadata metadata;
        metadata.topic = "test-topic";
        metadata.partition = 1;
        metadata.offset = 1000;
        metadata.key = "test-key";
        metadata.timestamp = 1625097600000; // 2021-07-01 00:00:00 UTC
        
        // 打印原始对象
        std::cout << "原始对象:" << std::endl;
        printMessageMetadata(metadata);
        
        // 序列化为JSON
        std::string metadataJson = metadata.toJson();
        std::cout << "\nJSON字符串:" << std::endl;
        std::cout << metadataJson << std::endl;
        
        // 反序列化
        KafkaMessageMetadata deserializedMetadata;
        if (deserializedMetadata.fromJson(metadataJson)) {
            std::cout << "\n反序列化对象:" << std::endl;
            printMessageMetadata(deserializedMetadata);
        } else {
            std::cout << "反序列化失败!" << std::endl;
        }
        
        // 示例2: KafkaPartitionInfo 序列化和反序列化
        std::cout << "\n--- 示例2: KafkaPartitionInfo 序列化和反序列化 ---" << std::endl;
        
        // 创建一个KafkaPartitionInfo对象
        KafkaPartitionInfo partitionInfo;
        partitionInfo.topic = "test-topic";
        partitionInfo.partition = 1;
        partitionInfo.lowOffset = 500;
        partitionInfo.highOffset = 2000;
        partitionInfo.lastStableOffset = 1800;
        partitionInfo.logStartOffset = 100;
        partitionInfo.leader = 1;
        partitionInfo.replicas = {1, 2, 3};
        partitionInfo.inSyncReplicas = {1, 2};
        
        // 打印原始对象
        std::cout << "原始对象:" << std::endl;
        printPartitionInfo(partitionInfo);
        
        // 序列化为JSON
        std::string partitionInfoJson = partitionInfo.toJson();
        std::cout << "\nJSON字符串:" << std::endl;
        std::cout << partitionInfoJson << std::endl;
        
        // 反序列化
        KafkaPartitionInfo deserializedPartitionInfo;
        if (deserializedPartitionInfo.fromJson(partitionInfoJson)) {
            std::cout << "\n反序列化对象:" << std::endl;
            printPartitionInfo(deserializedPartitionInfo);
        } else {
            std::cout << "反序列化失败!" << std::endl;
        }
        
        // 示例3: KafkaConsumerGroupInfo 序列化和反序列化
        std::cout << "\n--- 示例3: KafkaConsumerGroupInfo 序列化和反序列化 ---" << std::endl;
        
        // 创建一个KafkaConsumerGroupInfo对象
        KafkaConsumerGroupInfo groupInfo;
        groupInfo.groupId = "test-group";
        groupInfo.state = "Stable";
        groupInfo.coordinator = "broker1:9092";
        groupInfo.memberCount = 2;
        groupInfo.members = {"consumer1", "consumer2"};
        groupInfo.assignments = {
            std::make_tuple("test-topic", 0, 1000),
            std::make_tuple("test-topic", 1, 1500)
        };
        
        // 打印原始对象
        std::cout << "原始对象:" << std::endl;
        printConsumerGroupInfo(groupInfo);
        
        // 序列化为JSON
        std::string groupInfoJson = groupInfo.toJson();
        std::cout << "\nJSON字符串:" << std::endl;
        std::cout << groupInfoJson << std::endl;
        
        // 反序列化
        KafkaConsumerGroupInfo deserializedGroupInfo;
        if (deserializedGroupInfo.fromJson(groupInfoJson)) {
            std::cout << "\n反序列化对象:" << std::endl;
            printConsumerGroupInfo(deserializedGroupInfo);
        } else {
            std::cout << "反序列化失败!" << std::endl;
        }
        
        std::cout << "\n所有测试完成!" << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
        return 1;
    }
}
