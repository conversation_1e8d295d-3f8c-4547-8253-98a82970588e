#include "../DesignQueueManager.h"
#include "serialize/WDBMProtobufSerialize.h"
#include "../CodePairFactory.h"
#include "core/businessModule/WDBMBase.h"
#include <iostream>
#include <chrono>
#include <thread>

using namespace wiz;

int DesignQueueNodeExample()
{
    try
    {
        // 获取单例实例
        auto& queueManager = DesignQueueManager::getInstance();

        // 初始化队列管理器
        if (!queueManager.initialize(1000))
        {
            std::cerr << "Failed to initialize DesignQueueManager" << std::endl;
            return 1;
        }

        std::cout << "DesignQueueManager initialized successfully" << std::endl;

        // 创建CodePairContainer和WDBMBase对象
        // 注意：在实际应用中，这些对象通常由应用程序上下文提供
        auto codePairContainer = CodePairContainerPtr();
        WD::WDBMBase* bmBase = nullptr; // 在实际应用中，这应该是一个有效的WDBMBase对象

        // 设置序列化器
        if (!queueManager.setSerializer(codePairContainer, bmBase))
        {
            std::cerr << "Failed to set serializer" << std::endl;
            return 1;
        }

        std::cout << "Serializer set successfully" << std::endl;

        // 创建一个WDNode对象
        auto node = WD::WDNode::MakeShared();
        node->setName("TestNode");
        // 设置节点的其他属性...

        // 将节点推送到队列
        int64_t nodeId = 123; // 节点的整型映射ID
        if (queueManager.pushNode(node))
        {
            std::cout << "Node pushed successfully" << std::endl;
        }
        else
        {
            std::cerr << "Failed to push node" << std::endl;
        }

        // 创建多个WDNode对象
        std::vector<WD::WDNode::SharedPtr> nodes;

        // 将多个节点推送到队列
        if (queueManager.pushNodes(nodes))
        {
            std::cout << "Multiple nodes pushed successfully" << std::endl;
        }
        else
        {
            std::cerr << "Failed to push multiple nodes" << std::endl;
        }

        // 检查队列大小
        std::cout << "NodeAttrsRecord queue size: " << queueManager.getNodeAttrsRecordQueueSize() << std::endl;

        // 从队列中弹出记录
        std::vector<design::NodeAttrsRecord> records;
        size_t count = queueManager.popNodeAttrsRecords(records, 10);
        std::cout << "Popped " << count << " records from the queue" << std::endl;

        // 关闭队列管理器
        queueManager.shutdown();
        std::cout << "DesignQueueManager shut down" << std::endl;

        return 0;
    }
    catch (const std::exception& e)
    {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}
