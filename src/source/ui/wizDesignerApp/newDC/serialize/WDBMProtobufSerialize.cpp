//
// Created by everpan on 25-1-4.
//

#include "WDBMProtobufSerialize.h"
#include "core/businessModule/typeMgr/WDBMTypeDesc.h"
#include "core/businessModule/WDBDBase.h"
#include "core/businessModule/WDBMBase.h"
#include <glog/logging.h>
WD_NAMESPACE_BEGIN
    /**
     * @brief 判断位置的值是否时(0, 0, 0), 如果是，则表明未设置位置
    */
    bool IsUnsetPos(const DVec3& pos)
    {
        return (Abs(pos.x) <= NumLimits<float>::Epsilon
            && Abs(pos.y) <= NumLimits<float>::Epsilon
            && Abs(pos.z) <= NumLimits<float>::Epsilon);
    }

    /**
     * @brief 判断旋转四元数的值是否是(1, 0, 0, 0), 如果是，则表明未设置旋转
    */
    bool IsUnsetRot(const DQuat& quat)
    {
        return (Abs(1.0 - quat.w) <= NumLimits<float>::Epsilon
            && Abs(quat.x) <= NumLimits<float>::Epsilon
            && Abs(quat.y) <= NumLimits<float>::Epsilon
            && Abs(quat.z) <= NumLimits<float>::Epsilon);
    }

    /**
     * @brief 判断缩放的三个分量都是1或者接近1, 如果是，则表明未设置缩放
    */
    bool IsUnsetScale(const DVec3& scale)
    {
        return (Abs(1.0 - scale.x) <= NumLimits<float>::Epsilon
            && Abs(1.0 - scale.y) <= NumLimits<float>::Epsilon
            && Abs(1.0 - scale.z) <= NumLimits<float>::Epsilon);
    }

    bool WDBMProtobufSerialize::serializeNode(const WDNode::SharedPtr& node,
                                              design::NodeAttrsRecord& record,
                                              const BASFlags& flags) const
    {
        // 检测类型是否具有自动生成标志，如果有该标志，则不做保存
        auto pDesc = node->getTypeDesc();
        if (pDesc != nullptr && pDesc->flags().hasFlag(WDBMTypeDesc::F_AutoGeneration))
        {
            std::cerr << "有自动生成标志,ignore" << std::endl;
            return false;
        }
        auto& attrs = *record.mutable_attrs();
        WDBMProtobufWriter i_writer(_code_container, attrs);
        WDBMAttrWriter writer(i_writer);

        static constexpr auto LEN = offsetof(WDUuid, data4) + 8;
        auto uuid = string(reinterpret_cast<const char*>(&node->uuid()), LEN);
        auto remoteId = _uuid_code.AcquireCode(uuid);
        writer.write("__uuid__", uuid); // 自携带uuid，避免冷启动下远程获取
        node->setRemoteId(remoteId);

        auto epoch = std::chrono::steady_clock::now().time_since_epoch().count();
        node->setTraceId(epoch);
        record.set_id(remoteId);
        record.set_name(node->name());
        record.set_traceid(epoch);
        // todo extend write  long long
        writer.write("Id", static_cast<int>(remoteId));
        // 节点名称
        writer.write("Name", node->name());
        // 节点类型
        //LOG(INFO) << "node:" << node->name() << "|" << node->type() << "|" << node->uuid().toString();
        auto type_id = _type_val_code.AcquireCode(std::string(node->type()));
        record.set_type(static_cast<int>(type_id));
        writer.write("Type", static_cast<int>(type_id));
        // 变换信息
        // if (flags.hasFlag(BASFlag::BASF_Transform))
        // {
        //     // 位置, 不是(0,0,0)时，才写入
        //     const auto& lPos = node->localTranslation();
        //     if (!IsUnsetPos(lPos))
        //         writer.write("Position", lPos);
        //     // 旋转, 不是单位四元数时，才写入
        //     const auto& lRot = node->localRotation();
        //     if (!IsUnsetRot(lRot))
        //         writer.write("Rotation", lRot);
        //     // 缩放, 三个分量均不是1时，才写入
        //     const auto& lScale = node->localScaling();
        //     if (!IsUnsetScale(lScale))
        //         writer.write("Scaling", lScale);
        // }

        WDNode::Flags nodeFlags = node->flags();
        // 可见性
        if (flags.hasFlag(BASFlag::BASF_Visible))
        {
            if (!nodeFlags.hasFlag(WDNode::F_Visible))
                writer.write("Visible", false);
        }
        // 是否锁定
        if (nodeFlags.hasFlag(WDNode::F_Lock))
            writer.write("Lock", true);

        // 隐藏标志
        if (nodeFlags.hasFlag(WDNode::F_ItemHidden))
            writer.write("Hidden", true);

        // 基本颜色和材质
        if (flags.hasFlag(BASFlag::BASF_BasicColor))
        {
            // 基本颜色
            writer.write("BasicColor", node->basicColor());
        }

        // 节点业务对象属性
        // 节点业务对象属性
        auto pBase = node->getBDBase();
        assert(pBase != nullptr && "注意:节点业务数据对象获取失败!");
        if (pBase != nullptr)
            pBase->write(writer);
        return true;
    }

    bool WDBMProtobufSerialize::deserializeNode(WDNode& node, const design::NodeAttrsRecord& record,
                                                const BASFlags& flags) const
    {
        const auto& attrs = record.attrs();
        WDBMProtobufReader i_reader(_code_container, attrs);
        WDBMAttrReader reader(i_reader);

        // 节点guid
        int id = 0;
        if (reader.read("Id", id))
            // {
            //     const auto& mem = _uuid_code.GetLeftCode(id);
            //     if (mem.empty()) return false;
            //     auto& uuid = const_cast<WDUuid&>(node.uuid());
            //     uuid.fromMemory(mem.c_str());
            // }
            node.setRemoteId(id);
        static constexpr auto LEN = offsetof(WDUuid, data4) + 8;
        std::string uuidMem;
        reader.read("__uuid__", uuidMem);
        if (uuidMem.length() >= LEN)
        {
            auto& uuid = const_cast<WDUuid&>(node.uuid());
            uuid.fromMemory(uuidMem.c_str());
        }
        // 节点名称
        std::string nodeName;
        reader.read("Name", nodeName);
        node.setName(nodeName);

        // 节点类型
        // todo read long long
        int type_id = 0;
        reader.read("Type", type_id);
        const auto& typeName = _type_val_code.GetLeftCode(type_id);
        node.setType(*_pBMBase, typeName);

        // 根据类型创建数据对象
        auto pBase = node.getBDBase();
        if (pBase == nullptr)
        {
            pBase = node.createBD();
            assert((pBase != nullptr) && "注意:节点业务数据对象创建失败,确保节点类型合法或已注册!");
        }

        // 变换信息
        // if (flags.hasFlag(BASFlag::BASF_Transform))
        // {
        //     // 位置
        //     DVec3 pos = DVec3::Zero();
        //     reader.read("Position", pos);
        //     node.setLocalTranslation(pos);
        //     // 旋转
        //     DQuat rot = DQuat::Identity();
        //     reader.read("Rotation", rot);
        //     node.setLocalRotation(rot);
        //     // 缩放
        //     DVec3 scale = DVec3(1.0);
        //     reader.read("Scaling", scale);
        //     node.setLocalScaling(scale);
        // }
        WDNode::Flags nodeFlags = node.flags();
        // 可见性
        if (flags.hasFlag(BASFlag::BASF_Visible))
            reader.read("Visible", nodeFlags, WDNode::F_Visible);
        // 是否锁定
        reader.read("Lock", nodeFlags, WDNode::F_Lock);
        // 隐藏标志
        reader.read("Hidden", nodeFlags, WDNode::F_ItemHidden);
        node.setFlags(nodeFlags);
        // 基本颜色和材质
        if (flags.hasFlag(BASFlag::BASF_BasicColor))
        {
            // 节点颜色/材质相关
            Color basicColor;
            if (reader.read("BasicColor", basicColor))
            {
                node.setBasicColor(basicColor);
            }
            // 材质Id
        }

        // 节点业务数据属性
        if (pBase != nullptr)
            pBase->read(reader);

        return id > 0;
    }

WD_NAMESPACE_END
