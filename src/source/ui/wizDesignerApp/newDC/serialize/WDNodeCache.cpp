//
// Created by everpan on 25-5-21.
//

#include "WDNodeCache.h"
#include <glog/logging.h>

WD_NAMESPACE_BEGIN
    void WDNodeCache::initialize(store::IStore* store, WDBMProtobufSerializeInterface* ser,
                                 const design::NodeType type)
    {
        _store = store;
        _serializer = ser;
        _nodeType = type;
    }

    int64_t WDNodeCache::getParentId(const int64_t childId) const
    {
        if (const auto it = _parent.find(childId); it != _parent.end())
        {
            return it->second;
        }
        return -1;
    }

    vector<int64_t> WDNodeCache::getChildrenIds(const int64_t parentId, int deep) const
    {
        // 首先检查缓存中是否有子节点
        auto it = _children.find(parentId);

        // 获取第一层子节点
        if (it == _children.end())
        {
            auto directChildren = getChildrenIdsFromStore(parentId);
            // 使用const_cast来修改缓存，因为方法是const的
            if (!directChildren.empty())
                const_cast<WDNodeCache*>(this)->_children[parentId] = directChildren;
            // 同时更新父子关系缓存
            for (const auto& childId : directChildren)
            {
                const_cast<WDNodeCache*>(this)->_parent[childId] = parentId;
            }
            it = _children.find(parentId);
        }
        const auto& directChildren = it->second;
        // 如果没有子节点或只需要第一层，直接返回
        if (directChildren.empty() || deep == 1)
        {
            return directChildren;
        }

        // 将第一层子节点添加到结果中
        // 如果缓存中没有或需要更深层次的子节点，从存储中获取
        vector<int64_t> result;
        result.insert(result.end(), directChildren.begin(), directChildren.end());

        // 如果需要获取所有层级的子节点(deep == -1)或者还需要更深层次(deep > 1)
        if (deep == -1 || deep > 1)
        {
            // 递归获取每个子节点的子节点
            for (const auto& childId : directChildren)
            {
                // 递归调用，减少深度
                int nextDeep = (deep == -1) ? -1 : deep - 1;
                vector<int64_t> grandChildren = getChildrenIds(childId, nextDeep);

                // 将孙子节点添加到结果中
                if (!grandChildren.empty())
                {
                    result.insert(result.end(), grandChildren.begin(), grandChildren.end());
                }
            }
        }

        return result;
    }

    vector<int64_t> WDNodeCache::getChildrenIdsFromStore(int64_t parentId) const
    {
        return _store->getNodeChildIds(parentId, _nodeType);
    }

    vector<WDNode::SharedPtr> WDNodeCache::getChildren(int64_t parentId, int deep) const
    {
        auto chs = getChildrenIds(parentId, deep);
        return getNodes(chs);
    }

    WDNode::SharedPtr WDNodeCache::getNode(const int64_t nodeId) const
    {
        const auto it = _nodes.find(nodeId);
        if (it != _nodes.end())
        {
            return it->second;
        }
        auto node = getNodeFromStore(nodeId);
        if (node)
        {
            const_cast<WDNodeCache*>(this)->addNode(node);
        }
        return node;
    }

    vector<WDNode::SharedPtr> WDNodeCache::getNodes(vector<int64_t> ids) const
    {
        vector<WDNode::SharedPtr> ret;
        ret.reserve(ids.size());
        for (const auto& id : ids)
        {
            ret.push_back(getNode(id));
        }
        return ret;
    }

    WDNode::SharedPtr WDNodeCache::getNodeFromStore(const int64_t nodeId) const
    {
        const auto r = _store->getNodeAttrsRecord(nodeId, _nodeType);
        if (r.id() > 0)
        {
            auto pNode = WDNode::MakeShared();
            _serializer->deserializeNode(*pNode, r);
            return pNode;
        }
        LOG(WARNING) << "Node(" << (_nodeType == design::NodeType::DESIGN ? "DESIGN" : "CATALOG")
            << ") not found: " << nodeId;
        return nullptr;
    }

    void WDNodeCache::addNode(const WDNode::SharedPtr& node)
    {
        if (node != nullptr)
        {
            const auto r = node->getRemoteId();
            if (r > 0)
            {
                _nodes[r] = node;
            }
        }
    }

    void WDNodeCache::removeNode(const int64_t nodeId)
    {
        _nodes.erase(nodeId);
    }

    WDNodeCacheByType& WDNodeCacheByType::getInstance()
    {
        static WDNodeCacheByType instance;
        return instance;
    }

    void WDNodeCacheByType::initialize(store::IStore* store, WDBMProtobufSerializeInterface* design_ser,
                                       WDBMProtobufSerializeInterface* catalog_ser)
    {
        _caches[design::NodeType::CATALOG].initialize(store, catalog_ser, design::NodeType::CATALOG);
        _caches[design::NodeType::DESIGN].initialize(store, design_ser, design::NodeType::DESIGN);
    }

    WDNodeCache& WDNodeCacheByType::getCache(const design::NodeType type)
    {
        return _caches[type];
    }

    const WDNodeCache& WDNodeCacheByType::getCache(const design::NodeType type) const
    {
        return _caches.at(type);
    }

WD_NAMESPACE_END
