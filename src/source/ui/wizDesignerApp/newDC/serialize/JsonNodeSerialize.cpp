#include "JsonNodeSerialize.h"
#include <fstream>
#include <sstream>

#include "prettywriter.h"
#include "stringbuffer.h"
using JsonDoc = rapidjson::Document;
using JsonValue = rapidjson::Value;

namespace wiz
{
    JsonNodeSerialize::JsonNodeSerialize(WD::WDBMBase* bmBase)
        : m_bmBase(bmBase)
    {
        if (m_bmBase)
        {
            m_nodeSerializer = std::make_unique<WD::WDBMNodeSerializeDC>(*m_bmBase);
        }
    }

    JsonNodeSerialize::~JsonNodeSerialize() = default;

    //
    // 反序列化方法（从JSON到节点）
    //

    JsonNodeSerialize::Nodes JsonNodeSerialize::loadFromFile(const std::string& filePath,
                                                             const WD::BASFlags& flags)
    {
        // 检查序列化器是否已初始化
        if (!m_nodeSerializer)
        {
            return Nodes();
        }

        // 读取文件内容
        std::ifstream file(filePath);
        if (!file.is_open())
        {
            return Nodes();
        }

        std::stringstream buffer;
        buffer << file.rdbuf();
        file.close();

        return loadFromString(buffer.str(), flags);
    }

    JsonNodeSerialize::Nodes JsonNodeSerialize::loadFromString(const std::string& jsonString,
                                                               const WD::BASFlags& flags)
    {
        // 检查序列化器是否已初始化
        if (!m_nodeSerializer)
        {
            return Nodes();
        }

        // 解析JSON字符串
        JsonDoc jsonDoc;
        jsonDoc.Parse(jsonString.c_str());

        // 检查解析是否成功
        if (jsonDoc.HasParseError())
        {
            return Nodes();
        }

        // 创建进度回调函数适配器
        WD::WDBMNodeSerializeDC::FuncProgress funcProgress;

        // 使用WDBMNodeSerialize的loadFromJson方法解析节点
        return m_nodeSerializer->loadFromJson(jsonDoc, jsonDoc, flags, funcProgress);
    }

    //
    // 序列化方法（从节点到JSON）
    //

    bool JsonNodeSerialize::saveToFile(const Nodes& nodes,
                                       const std::string& filePath,
                                       const WD::BASFlags& flags)
    {
        // 检查序列化器是否已初始化
        if (!m_nodeSerializer)
        {
            return false;
        }

        // 将节点序列化为JSON字符串
        std::string jsonString = saveToString(nodes, flags);
        if (jsonString.empty())
        {
            return false;
        }

        // 写入文件
        std::ofstream file(filePath);
        if (!file.is_open())
        {
            return false;
        }

        file << jsonString;
        file.close();

        return true;
    }

    std::string JsonNodeSerialize::saveToString(const Nodes& nodes,
                                                const WD::BASFlags& flags)
    {
        // 检查序列化器是否已初始化
        if (!m_nodeSerializer || nodes.empty())
        {
            return "";
        }

        // 创建JSON文档
        JsonDoc jsonDoc;
        jsonDoc.SetArray();

        // 使用WDBMNodeSerialize的saveToJson方法序列化节点
        m_nodeSerializer->saveToJson(nodes, jsonDoc, jsonDoc, flags);

        // 将JSON文档转换为字符串
        rapidjson::StringBuffer buffer;
        rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
        jsonDoc.Accept(writer);

        return buffer.GetString();
    }
} // namespace wiz
