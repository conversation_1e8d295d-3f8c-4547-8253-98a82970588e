#include "JsonNodeSerialize.h"
#include "core/businessModule/WDBMBase.h"
#include "core/node/WDNode.h"
#include <iostream>

using namespace wiz;

int JsonNodeSerializeExample()
{
    try
    {
        // 创建WDBMBase对象
        // 注意：在实际应用中，这个对象通常由应用程序上下文提供
        WD::WDBMBase* bmBase = nullptr; // 在实际应用中，这应该是一个有效的WDBMBase对象

        // 创建JsonNodeSerialize对象
        JsonNodeSerialize serializer(bmBase);

        // 从文件加载节点
        std::string inputFilePath = "input.json";
        auto nodes = serializer.loadFromFile(inputFilePath, WD::BASFlag::BASF_All);

        // 输出加载的节点信息
        std::cout << "Loaded " << nodes.size() << " nodes from file." << std::endl;
        for (const auto& node : nodes) {
            std::cout << "Node: " << node->name() << ", Type: " << node->type() << std::endl;
            
            // 输出子节点信息
            std::cout << "  Children: " << node->children().size() << std::endl;
            for (const auto& child : node->children()) {
                std::cout << "    Child: " << child->name() << ", Type: " << child->type() << std::endl;
            }
        }

        // 将节点保存到文件
        std::string outputFilePath = "output.json";
        if (serializer.saveToFile(nodes, outputFilePath, WD::BASFlag::BASF_All)) {
            std::cout << "Nodes saved to file successfully." << std::endl;
        } else {
            std::cout << "Failed to save nodes to file." << std::endl;
        }

        // 创建新的节点
        auto rootNode = WD::WDNode::MakeShared();
        rootNode->setName("RootNode");
        rootNode->setType(*bmBase, "Group");

        auto childNode = WD::WDNode::MakeShared();
        childNode->setName("ChildNode");
        childNode->setType(*bmBase, "Mesh");
        
        rootNode->addChild(childNode);

        JsonNodeSerialize::Nodes newNodes = { rootNode };

        // 将节点序列化为JSON字符串
        std::string jsonString = serializer.saveToString(newNodes, WD::BASFlag::BASF_All);
        std::cout << "\nSerialized JSON:\n" << jsonString << std::endl;

        // 从JSON字符串加载节点
        auto loadedNodes = serializer.loadFromString(jsonString, WD::BASFlag::BASF_All);

        // 输出从字符串加载的节点信息
        std::cout << "\nLoaded " << loadedNodes.size() << " nodes from string." << std::endl;
        for (const auto& node : loadedNodes) {
            std::cout << "Node: " << node->name() << ", Type: " << node->type() << std::endl;
            
            // 输出子节点信息
            std::cout << "  Children: " << node->children().size() << std::endl;
            for (const auto& child : node->children()) {
                std::cout << "    Child: " << child->name() << ", Type: " << child->type() << std::endl;
            }
        }

        return 0;
    }
    catch (const std::exception& e)
    {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}
