//
// Created by everpan on 25-3-29.
//

#ifndef CODE_PAIR_GRPC_H
#define CODE_PAIR_GRPC_H
#include <memory>
#include "CodePairInterface.h"
#include "proto/service.grpc.pb.h"
#include "store/db_store/CommonStore.h"

using WD::store::CommonStoreSPtr;

class CodePairGrpc final : public CodePairInterface<>
{
public:
    using StubPtr = std::shared_ptr<design::DesignService::Stub>;

    explicit CodePairGrpc(const StubPtr& client, const design::ProjectInfo& proj)
        : _client(client), _project_info(proj)
    {
    }

    int64_t AcquireCode(const string& key) override;

    void AcquireCodes(LRMapT& req) override;

    int64_t GetRightCode(const string& c1) const override;

    void Add(const string& c1, const int64_t& code) override;
    /**
     * 用本地数据来初始化缓存
     * @param mp 本地数据
     */
    void Add(const map<string, int64_t>& mp) override;

    const string& GetLeftCode(const int64_t& code) const override;

    const LRMapT& GetLRCodeMap() const override;

    const RLMapT& GetRLCodeMap() const override;

    void clear() override;

    const set<int64_t>& GetNewCodeSet() const override;

    void ClearNewCodeSet() override;

    void setCommonStore(const CommonStoreSPtr& store);
    void loadFromStore();
    void saveToStore() const;

private:
    StubPtr _client;
    design::ProjectInfo _project_info;
    LRMapT _lr_map; // local cache
    RLMapT _rl_map;
    CommonStoreSPtr _common_store;
};


#endif //CODE_PAIR_GRPC_H
