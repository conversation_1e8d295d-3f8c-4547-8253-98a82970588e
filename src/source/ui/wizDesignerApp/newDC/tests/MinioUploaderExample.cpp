#include "../rpc/minio/MinioUploader.h"
#include <iostream>
#include <iomanip>
#include <thread>
#include <gtest/gtest.h>

using namespace wiz;

// 进度回调函数
void progressCallback(const TaskProgress& progress)
{
    std::cout << "\r上传 " << progress.objectName
        << ": " << std::fixed << std::setprecision(2) << progress.getPercentage() << "% "
        << "(" << progress.bytesUploaded << "/" << progress.totalBytes << " 字节) "
        << std::setprecision(2) << (progress.speed / 1024.0 / 1024.0) << " MB/s"
        << std::flush;
}

TEST(MinioUploader, uploader)
{
    // 创建上传器实例
    MinioUploader uploader(
        "play.min.io", // MinIO 服务器地址
        "minioadmin", // 访问密钥
        "minioadmin", // 秘密密钥
        true, // 使用 HTTPS
        4 // 工作线程数
    );

    // 添加上传任务
    std::string taskId = uploader.addTask(
        "mybucket", // 存储桶名称
        "myfile.txt", // 对象名称
        "local/path/to/myfile.txt", // 源文件路径
        "text/plain", // 内容类型
        progressCallback // 进度回调
    );

    // 等待所有上传完成
    uploader.waitForCompletion();
}
