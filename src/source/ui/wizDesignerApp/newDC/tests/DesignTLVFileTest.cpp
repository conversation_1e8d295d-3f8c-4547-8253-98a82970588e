//
// Created by everpan on 25-5-13.
//
#include <gtest/gtest.h>
#include <glog/logging.h>
#include "service/DesignTLVFile.h"
#include "store/db_store/DbStore.h"
using namespace wiz;


TEST(DesignTLVFileTest, open)
{
    std::string file = "/Users/<USER>/Downloads/nodeAttr_1916794522764193792_1_20250513103832.nad";
    file = "/tmp/node-snapshot/nodeAttr_1922859953396436992_1_20250516105741.nad";
    DesignTLVFile tlvFile(file);
    std::string dataPath = "/tmp/data";
    auto store = std::make_unique<WD::store::DbStore>(dataPath, "test01", 10);
    tlvFile.updateStore(*store);
}
