#pragma once

#include "core/WDCore.h"
#include <QStatusBar>
#include <QPushButton>
#include <QProgressBar>
#include "DCTipWidget.h"

/**
 * @brief 协同状态栏
*/
class DCStatusBar : public QStatusBar
    , public IDCStatusBar
{
    Q_OBJECT

private:
    WD::WDCore&     _core;
    // 临时消息文本
    QLabel*         _pLabelTempMsg;
    // 中间进度条
    QProgressBar*   _pProgressBar;
    // 按钮默认图片
    QIcon*          _defIcon;
    // 消息通知按钮
    QPushButton*    _pBtnTips;

    // 消息通知窗口
    DCTipWidget*    _pTipWidget;

public:
    DCStatusBar(ICollaboration& collaboration, WD::WDCore& core, QWidget* parent);
    virtual ~DCStatusBar() override;
    
public:
    virtual inline DCTipWidget*     tipWidget() override
    {
        return _pTipWidget;
    }

    virtual bool                    addTip(const QString& id
        , const QString& title
        , const QString& text
        , TipLevel tipLevel = TipLevel::TL_Info
        , TaskTip function = TaskTip()) override;
    virtual void                    showMsg(const QString& msg, const QColor& color, int timeOut = 0) override;
    virtual inline QProgressBar*    progressBar() override
    {
        return _pProgressBar;
    }

private slots:
    /**
     * @brief 系统通知按钮按下通知响应
    */
    void slotOnTipDialogClicked(bool checked);
    /**
     * @brief 系统通知个数更改通知响应
    */
    void slotOnUpdateTipCount();
};