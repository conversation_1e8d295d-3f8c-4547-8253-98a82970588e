#pragma once

#include    "core/WDCore.h"
#include    "core/node/WDNode.h"

class ILoginServer;
class IModelServer;
/**
 * @brief 协同相关数据以及接口
*/
class ICollaboration
{
public:
    /**
     * @brief 协同用户信息
     */
    struct CollabUserInfo
    {
        // 用户账户
        std::string userAccount;
        // 用户密码
        std::string password;
        // 用户ID
        std::string id;
        // 登录角色code
        std::string roleCode;
        // 用户名称
        std::string name;
    };
    /**
    * @brief 协同项目信息
    */
    struct CollabProjectInfo
    {
        // 项目Id
        uint64_t id;
        // 项目名称
        std::string name;
        // 项目code
        std::string code;
    };
    /**
    * @brief 协同配置信息
    */
    struct CollabCfgInfo
    {
        // 数据库地址
        std::string db;
        // grpc地址
        std::string grpc;
    };

public:
    /**
     * @brief 登录服务相关接口
    */
    virtual ILoginServer& loginServer() = 0;
    /**
    * @brief 模型服务相关接口
    */
    virtual IModelServer& modelServer() = 0;

    /**
     * @brief 获取用户信息
    */
    virtual const CollabUserInfo& userInfo() const = 0;
    virtual CollabUserInfo& userInfo() = 0;
    /**
     * @brief 获取项目信息
    */
    virtual const CollabProjectInfo& projectInfo() const = 0;
    virtual CollabProjectInfo& projectInfo() = 0;
    /**
    * @brief 获取配置信息
    */
    virtual const CollabCfgInfo& cfgInfo() const = 0;

    /**
     * @brief 是否激活协同服务
    */
    virtual bool actived() const = 0;
};

/**
* @brief 登录服务接口
*/
class ILoginServer
{
public:
    static constexpr const char* LoginUserRole_Admin = "Admin";
    static constexpr const char* LoginUserRole_PM = "PM";
    static constexpr const char* LoginUserRole_MM = "MM";
    static constexpr const char* LoginUserRole_Designer = "Designer";
    struct UserCfgRole
    {
        // 角色名称
        std::string name;
        // 项目编码
        std::string code;
    };
    struct UserCfgProj
    {
        // 项目id
        uint64_t    id;
        // 项目名称
        std::string name;
        // 项目编码
        std::string code;
        // 角色列表
        std::vector<UserCfgRole> roles;
    };
    /**
     * @brief 登录用户配置
     */
    using LoginUserConfig = std::vector<UserCfgProj>;
public:
    /**
     * @brief 用户登录
     * @param userName 用户名称
     * @param password 登录密码
     * @return 登录用户配置
     */
    virtual LoginUserConfig login(std::string_view userName, std::string_view password) const = 0;
    /**
     * @brief 打开工程项目
     * @param code 项目编码
     * @param userRole 用户角色
     * @return 打开是否成功
     */
    virtual bool openProject(const UserCfgProj& project, std::string_view userRole) = 0;

    /**
     * @brief 加载登录时拉取的数据（后续考虑直接在打开项目时构建节点树，此接口或删除）
     */
    virtual void load() const = 0;
};

/**
 * @brief 模型服务接口
*/
class IModelServer
{
public:
    /**
     * @brief 节点操作
     */
    enum NodeTreeAction
    {
        // 新增
        NTA_Add = 0,
        // 数据修改（仅修改节点数据，不更改节点关系）
        NTA_Modify,
        // 节点移动（仅更改节点关系，不修改节点数据）节点移动会转化为一个新增action和一个删除action
        //NTA_Move,
        // 删除
        NTA_Remove
    };
    /**
     * @brief 模型服务节点数据
     */
    struct MSNode
    {
        // 节点的弱指针
        WD::WDNode::WeakPtr pWNode;
        // 节点操作
        NodeTreeAction      action;

        MSNode()
        {
            action = NTA_Add;
        }
        MSNode(WD::WDNode::SharedPtr v1, NodeTreeAction v2)
            : pWNode(v1)
            , action(v2)
        {
        }
    };
    using MSNodes = std::vector<MSNode>;
public:
    /**
     * @brief 校验项目是否已经被初始化
    */
    virtual bool projectInitialed() const = 0;

    /**
     * @brief 向服务器推送指定数据
     * @param msNodes 推送数据
     * @return 推送是否成功
     */
    virtual bool push(const MSNodes& msNodes) const = 0;
    /**
    * @brief 向服务器推送需要推送的数据
    * @return 推送是否成功
    */
    virtual bool push() const = 0;
    /**
    * @brief 向服务器推送所有数据
    * info-该接口目前仅为初始化提供，切忌频繁调用，多端频繁调用该接口有可能导致数据紊乱
    * @return 推送是否成功
    */
    virtual bool pushAll() const = 0;
    /**
    * @brief 从服务器拉取数据
    * @return 拉取是否成功
    */
    virtual bool pull() = 0;
    /**
    * @brief 清空服务器的数据，慎用(重新初始化时会用到)
    */
    virtual bool clear() = 0;

    /**
     * @brief 初始化申领回调
     */
    virtual void initClaimCallback() = 0;
};
