#pragma once

#include "core/WDCore.h"
#include <QThread>
#include "IRepeatThread.h"

/**
 * @brief 轮询事件线程
*/
class RepeatThread : public QThread
{
    Q_OBJECT
public:
    RepeatThread(QObject* parent = nullptr);

public:
    /**
     * @brief 设置轮询
     * @param key 轮询事件key
     * @param timeSpacing 轮询时间间隔
    */
    inline void setRepeat(const std::string& key, int timeSpacing = 10000)
    {
        _mapRepeat[key] = timeSpacing;
    }
    /**
     * @brief 查询轮询事件
     * @param key 事件key
    */
    inline bool query(const std::string& key)
    {
        return _mapRepeat.find(key) != _mapRepeat.end();
    }
signals:
    /**
     * @brief 获取通知信号
    */
    void sigRepeat(const std::string& key);

protected:
    virtual void run() override;

private:
    // 轮询事件key和间隔时间的map
    std::map<std::string, int>  _mapRepeat;
};

class RepeatListenerMgr
{
public:
    inline const std::vector<IRepeatListener*>& listeners() const
    {
        return _listeners;
    }
    inline bool addListener(IRepeatListener* listener)
    {
        if (std::find(_listeners.begin(), _listeners.end(), listener) != _listeners.end())
            return false;
        _listeners.push_back(listener);
        return true;
    }
    inline bool removeListener(IRepeatListener* listener)
    {
        auto pItr = std::find(_listeners.begin(), _listeners.end(), listener);
        if (pItr == _listeners.end())
            return false;
        _listeners.erase(pItr);
        return true;
    }

private:
    std::vector<IRepeatListener*> _listeners;
};

class UiRepeatThread : public QObject
    , public IRepeatThread
{
    Q_OBJECT
public:
    UiRepeatThread(QObject* parent = nullptr);
    virtual ~UiRepeatThread();

public slots:
    /**
     * @brief 轮询事件响应
    */
    void slotRepeat(const std::string& key);

public:
    inline virtual void setRepeat(const std::string& key, int timeSpacing = 10000) override
    {
        _thread.setRepeat(key, timeSpacing);
    }
    inline virtual bool query(const std::string& key) override
    {
        return _thread.query(key);
    }
    inline virtual void safeStart() override
    {
        if (!_thread.isRunning())
        {
            _thread.start();
        }
    }
    inline virtual void safeQuit() override
    {
        _thread.quit();
        _thread.wait();
    }
    inline virtual void addListener(IRepeatListener* listener) override
    {
        _listenerMgr.addListener(listener);
    }
    inline virtual void removeListener(IRepeatListener* listener) override
    {
        _listenerMgr.removeListener(listener);
    }

private:
    RepeatThread        _thread;
    // 监听者
    RepeatListenerMgr   _listenerMgr;
};