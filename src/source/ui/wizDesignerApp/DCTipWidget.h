#pragma once

#include <QWidget>
#include "ui_DCTipWidget.h"
#include "core/WDCore.h"
#include <QTextBrowser>
#include <QLabel>
#include "UiInterface/ICollaboration.h"
#include "UiInterface/IDCStatusBar.h"

class TipWidget : public QWidget
{
    Q_OBJECT

private:
    // 消息ID
    QString             _msgId;
    // 标题
    QLabel*             _labelTitle;
    // 删除按钮
    QPushButton*        _btnDele;
    // 文本
    QTextBrowser*       _textBrowserText;
    // 消息级别
    TipLevel            _tipLevel;
    QLabel*             _labelTipLevel;
    // 消息回调任务
    TaskTip             _task;

public:
    TipWidget(const QString& id
        , const QString& title
        , const QString& text
        , TipLevel tipLevel
        , TaskTip function);
    ~TipWidget();

public:
    /**
     * @brief 获取通知id
    */
    inline const QString& id() const
    {
        return _msgId;
    }
    /**
     * @brief 获取消息等级
    */
    inline const TipLevel& level() const
    {
        return _tipLevel;
    }

signals:
    /**
     * @brief 任务完成信号
    */
    void    sigFinishTask(TipWidget* widget);
    /**
     * @brief 消息删除信号
    */
    void    sigDeleTip(TipWidget* widget);

public slots:
    void    slotOnBtnDeleClicked(bool b);

protected:
    virtual void paintEvent(QPaintEvent* evt) override;

    virtual void mousePressEvent(QMouseEvent* evt) override;
    virtual bool eventFilter(QObject* obj, QEvent* evt) override;

private:
    /**
     * @brief 应用通知等级到界面
     * @param tipLevel 通知等级
    */
    void useTipLevel(TipLevel tipLevel);
    /**
     * @brief 完成任务
    */
    void finishTast();
};
using TipWidgets = std::vector<TipWidget*>;

class  DCTipWidget : public QWidget
{
    Q_OBJECT

private:
    Ui::DCTipWidget ui;
    WD::WDCore&     _core;

    // 网络会话管理
    ICollaboration& _collaboration;
    // 消息通知界面列表
    TipWidgets      _tipWidgets;

public:
    DCTipWidget(ICollaboration& collaboration, WD::WDCore& core, QWidget* parent = nullptr);
    ~DCTipWidget();

protected:
    virtual void showEvent(QShowEvent* evt) override;
    virtual void hideEvent(QHideEvent* evt) override;

public slots:
    void slotOnDeleTip(TipWidget* w);

public:
    /**
     * @brief 添加通知
     * @param id 序号
     * @param title 标题
     * @param text 文本
     * @param tipLevel 级别
     * @param function 任务回调
     * @return 是否添加成功
    */
    bool            addTip(const QString& id
        , const QString& title
        , const QString& text
        , TipLevel tipLevel
        , TaskTip function);
    /**
     * @brief 移除通知
     * @param id 通知id
     * @return 是否移除成功
    */
    bool            removeTip(const QString& id);
    /**
     * @brief 获取通知个数
     * @return 
    */
    inline size_t   tipCount() const
    {
        return _tipWidgets.size();
    }
    /**
     * @brief 清除通知
     * @return 
    */
    size_t          clearTips();
    /**
     * @brief 获取子通知窗口的序号
     * @param pWidget 子通知窗口
    */
    int             getIndex(TipWidget* pWidget) const;

signals:
    /**
     * @brief 更新通知个数信号
    */
    void    sigUpdateTipCount();

private slots:
    /**
     * @brief 任务完成通知响应
    */
    void    slotOnFinishTask(TipWidget* pWidget);
    /**
     * @brief 清除通知按钮按下通知响应
    */
    void    slotOnClearBtnClicked(bool);

private:
    /**
     * @brief 更新通知界面
    */
    void    updateWidgets();

private:
    void readTip(TipWidget* w);

    /**
     * @brief 界面翻译
    */
    void    retranslateUi();
};
