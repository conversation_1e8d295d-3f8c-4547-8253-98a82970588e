#pragma     once
#include    "core/WDCore.h"
#include    "core/extension/WDPluginFormat.h"
#include    "core/extension/WDExtensionMgr.h"
#include    "core/geometry/WDGeometryMgr.h"
#include    "WDBMNodeSerialize.h"

WD_NAMESPACE_BEGIN
#if 0

/**
* @brief 读取元件模块数据
* @param app 
* @param file 数据文件全路径名称
*/
bool LoadCatalogModuleData(WDCore& app, const std::string& file);
/**
* @brief 写入元件模块数据
* @param app 
* @param file 数据文件全路径名称
*/
bool SaveCatalogModuleData(WDCore& app, const std::string& file);

/**
* @brief 读取设计模块数据
* @param app 
* @param file 数据文件全路径名称
*/
bool LoadDesignModuleData(WDCore& app, const std::string& file);
/**
* @brief 写入设计模块数据
* @param app 
* @param file 数据文件全路径名称
*/
bool SaveDesignModuleData(WDCore& app, const std::string& file);


/**
 * @brief 读取几何管理中的几何体数据,目前只保存了多面体数据
 * @param app 
 * @param file 
 * @return 
*/
bool LoadGeometryMgrData(WDCore& app, const std::string& file);
/**
 * @brief 写入几何管理中的几何体数据,目前只保存了多面体数据
 * @param app
 * @param file
 * @return
*/
bool SaveGeometryMgrData(WDCore& app, const std::string& file);
#endif
WD_NAMESPACE_END
