<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>LicenseTipDialog</class>
 <widget class="QDialog" name="LicenseTipDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>532</width>
    <height>222</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>LicenseMgr</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QTextBrowser" name="textBrowser"/>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonOk">
       <property name="text">
        <string>ok</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
