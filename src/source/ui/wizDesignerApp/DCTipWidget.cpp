#include "DCTipWidget.h"
#include "core/WDTranslate.h"
#include <QPainter>
#include <QMouseEvent>
#include <QNetworkReply>
#include "../wizDesignerApp/UiInterface/UiTranslate.h"
#include "core/log/WDLoggerPort.h"
#include "core/message/WDMessage.h"
#include "WDRapidjson.h"

/**
 * @brief 带事件过滤TextBrowser
*/
class FilterTextBrowser : public QTextBrowser
{
public:
    FilterTextBrowser(QWidget* parent = nullptr) : QTextBrowser(parent){}

protected:
    void mousePressEvent(QMouseEvent* evt) override
    {
        QTextBrowser::mousePressEvent(evt);

        if (evt->button() == Qt::LeftButton)
        {
            auto pParent = this->parent();
            if (pParent != nullptr)
            {
                pParent->eventFilter(this, evt);
            }
        }
    }
};

TipWidget::TipWidget(const QString& id
    , const QString& title
    , const QString& text
    , TipLevel tipLevel
    , TaskTip function)
    : _msgId(id)
    , _tipLevel(tipLevel)
{
    _labelTitle         =   new QLabel();
    _textBrowserText    =   new FilterTextBrowser();
    _labelTipLevel      =   new QLabel();

    _task               =   function;

    // 窗口布局
    auto    pTopLayout  =   new QHBoxLayout();
    pTopLayout->addWidget(_labelTitle, 1);
    pTopLayout->addWidget(_labelTipLevel, 0);
    _btnDele = new QPushButton();
    _btnDele->setText(QString::fromUtf8(WD::WDTs("TipWidget", "delete message").c_str()));
    pTopLayout->addWidget(_btnDele, 0);
    auto    pAllLayout  =   new QVBoxLayout();
    pAllLayout->addLayout(pTopLayout, 0);
    pAllLayout->addWidget(_textBrowserText, 1);
    this->setLayout(pAllLayout);

    // 设置属性
    _labelTitle->setText(title);
    _labelTitle->setFixedHeight(24);
    _labelTipLevel->setMaximumWidth(60);
    _labelTipLevel->setFixedHeight(24);
    _textBrowserText->setFixedHeight(80);
    _textBrowserText->setText(text);
    this->useTipLevel(tipLevel);
    // 事件过滤
    _labelTitle->installEventFilter(this);
    _labelTipLevel->installEventFilter(this);
    _textBrowserText->installEventFilter(this);

    // 消息窗口竖直禁止拉伸
    this->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);

    // 绑定事件通知回调
    connect(_btnDele, &QPushButton::clicked, this, &TipWidget::slotOnBtnDeleClicked);
}
TipWidget::~TipWidget()
{
    if (_labelTitle != nullptr)
    {
        _labelTitle->deleteLater();
        _labelTitle = nullptr;
    }
    if (_textBrowserText != nullptr)
    {
        _textBrowserText->deleteLater();
        _textBrowserText = nullptr;
    }
    if (_labelTipLevel != nullptr)
    {
        _labelTipLevel->deleteLater();
        _labelTipLevel = nullptr;
    }
}

void TipWidget::slotOnBtnDeleClicked(bool b)
{
    WDUnused(b);
    emit sigDeleTip(this);
}

void TipWidget::paintEvent(QPaintEvent* evt)
{
    WDUnused(evt);
    QPainter painter(this);
    painter.fillRect(rect(), QColor(160, 160, 160, 50));
}

void TipWidget::mousePressEvent(QMouseEvent* evt)
{
    if (evt->button() == Qt::LeftButton)
    {
        this->finishTast();
    }
}
bool TipWidget::eventFilter(QObject* obj, QEvent* evt)
{
    auto type = evt->type();
    if (type == QEvent::MouseButtonPress)
    {
        auto mouseEvent = static_cast<QMouseEvent*>(evt);
        if (mouseEvent->button() == Qt::LeftButton)
        {
            // 处理控件上的左键点击事件
            this->finishTast();

            // 返回true表示事件已被处理，不再传递给其他对象
            return true;
        }
    }
    // 对于其他事件或者对象，继续标准事件处理
    return QWidget::eventFilter(obj, evt);
}

void TipWidget::useTipLevel(TipLevel tipLevel)
{
    if (_labelTipLevel == nullptr)
        return ;

    switch (tipLevel)
    {
    case TL_Info:
        {
            _labelTipLevel->setStyleSheet("QLabel {background-color: #FFA000; }");
        }
        break;
    case TL_Impt:
        {
            _labelTipLevel->setStyleSheet("QLabel {background-color: #EB3324; }");
        }
        break;
    default:
        break;
    }
}
void TipWidget::finishTast()
{
    if (_task != nullptr)
    {
        _task(1);
    }
    if (_tipLevel == TipLevel::TL_Info)
    {
        emit sigFinishTask(this);
    }
}


DCTipWidget::DCTipWidget(ICollaboration& collaboration, WD::WDCore& core,  QWidget *)
    : _collaboration(collaboration)
    , _core(core)
{
    ui.setupUi(this);

    ui.scrollArea->widget()->setStyleSheet("QWidget {background-color: #F9F9F9;}");

    // 界面翻译
    this->retranslateUi();

    // 绑定通知响应事件
    connect(ui.pushButtonClear, &QPushButton::clicked,  this, &DCTipWidget::slotOnClearBtnClicked);
}
DCTipWidget::~DCTipWidget()
{
}

void DCTipWidget::showEvent(QShowEvent* evt)
{
    WDUnused(evt);
}
void DCTipWidget::hideEvent(QHideEvent* evt)
{
    WDUnused(evt);
}

void    DCTipWidget::slotOnDeleTip(TipWidget* w)
{
    if (w == nullptr)
        return ;

    auto pItr = std::find(_tipWidgets.begin(), _tipWidgets.end(), w);
    if (pItr != _tipWidgets.end())
    {
        this->readTip(w);
        this->updateWidgets();
    }
}

bool    DCTipWidget::addTip(const QString& id
    , const QString& title
    , const QString& text
    , TipLevel tipLevel
    , TaskTip function)
{
    // 查询是否已存在相同ID消息
    for (auto& pWidget : _tipWidgets)
    {
        if (pWidget->id() == id)
            return false;
    }
    auto pTipWidget = new TipWidget(id, title, text, tipLevel, function);
    connect(pTipWidget, &TipWidget::sigFinishTask, this, &DCTipWidget::slotOnFinishTask);
    connect(pTipWidget, &TipWidget::sigDeleTip,  this, &DCTipWidget::slotOnDeleTip);
    _tipWidgets.push_back(pTipWidget);

    this->updateWidgets();
    return true;
}
bool    DCTipWidget::removeTip(const QString& id)
{
    for (auto pItr = _tipWidgets.begin(); pItr != _tipWidgets.end(); ++pItr)
    {
        if (*pItr == nullptr)
            continue;

        if ((*pItr)->id() == id)
        {
            delete *pItr;
            _tipWidgets.erase(pItr);

            this->updateWidgets();
            return true;
        }
    }

    return false;
}
size_t  DCTipWidget::clearTips()
{
    size_t count = 0;
    auto pItr = _tipWidgets.begin();
    while (pItr != _tipWidgets.end())
    {
        if (*pItr != nullptr)
        {
            delete *pItr;
            count ++;
        }
        pItr = _tipWidgets.erase(pItr);
    }

    this->updateWidgets();
    return count;
}
int     DCTipWidget::getIndex(TipWidget* pWidget) const
{
    for (size_t i = 0; i < _tipWidgets.size(); ++i)
    {
        if (pWidget == _tipWidgets.at(i))
        {
            return (int)i;
        }
    }

    return -1;
}

void    DCTipWidget::slotOnFinishTask(TipWidget* pWidget)
{
    this->readTip(pWidget);
}
void    DCTipWidget::slotOnClearBtnClicked(bool)
{
    this->clearTips();
}
void    DCTipWidget::updateWidgets()
{
    // 删除ui.verticalLayout最后一个addStretch生成的窗口
    auto count = ui.verticalLayout->count();
    ui.verticalLayout->takeAt(count - 1);
    for (auto& tipWidget : _tipWidgets)
    {
        ui.verticalLayout->addWidget(tipWidget);
    }
    // 添加伸缩空间
    ui.verticalLayout->addStretch();
    emit sigUpdateTipCount();
}

void    DCTipWidget::readTip(TipWidget* pWidget)
{
    if (pWidget == nullptr)
        return;

    // TODO: NEWDC向服务器发送

    this->removeTip(pWidget->id());
}

void    DCTipWidget::retranslateUi()
{
    Trs("DCTipWidget"
        , static_cast<QWidget*>(this)
        , ui.pushButtonClear);
}