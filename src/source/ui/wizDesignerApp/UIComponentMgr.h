#pragma once

#include <QLibrary>
#include "core/WDCore.h"
#include "../wizDesignerApp/UiInterface/UiInterface.h"
#include "WDRapidxml.h"

WD_NAMESPACE_USE

class IUiComponent;
/**
* @brief ui组件扩展管理类
*/
class Q_DECL_EXPORT UIComponentMgr
{
public:
    using Extensions = std::vector<IUiComponent*>;
private:
    /**
    * @brief 扩展项数据
    */
    struct ComponentData
    {
    public:
        //扩展项名称
        QString _extName = "";
        //扩展项库
        QLibrary* _pLib = nullptr;
        //扩展项导出信息
        UiComponentInfor _infor;
        //已被创建的扩展项对象
        Extensions _extensions;
    public:
        // 校验动态库是否已被加载
        inline bool isLoaded() const 
        {
            return (_pLib != nullptr) && (_pLib->isLoaded());
        }
        // 指定组件名称创建组件对象
        IUiComponent* createComponent(IMainWindow& mainWindow, const UiComponentAttributes& attrs);
    private:
        // 指定组件对象名称查询组件
        IUiComponent* findByName(const QString& componentName) const;
    };
    /**
    * @brief 扩展项数据列表
    */
    using ComponentDatas = std::vector<ComponentData>;
    /**
     * @brief 显示ui组件加载信息
    */
    using showMessageFunc = std::function<void(const QString& , int , const QColor& )>;
private:
    //扩展项信息列表
    ComponentDatas _extDatas;
    IMainWindow&   _mainWindow;
    QString        _dllPath;
    //启动页显示加载组件信息的回调函数
    showMessageFunc _showMesg;
public:
    UIComponentMgr(IMainWindow& mainWindow);
    virtual ~UIComponentMgr();
public:
    /**
     * @brief 设置显示加载ui组件信息的函数
     * @param func 显示加载ui组件信息的函数
    */
    void setCallBackShowMessage(const showMessageFunc& func)
    {
        _showMesg = func;
    }
    /**
    * @brief 加载所有扩展项
    */
    bool load(const QString& configFile);
    /**
    * @brief 卸载所有扩展项
    */
    void unload();
    /**
    * @brief 向所有组件发送消息
    */
    void sendNotice(UiNotice* pNotice);
    /**
    * @brief 向指定的组件发送消息
    */
    bool sendNotice(const QString& componentName, UiNotice* pNotice);
    /**
    * @brief 获取所有组件
    */
    const ComponentDatas& components() const;
private:
    /**
    * @brief 创建扩展项对象
    */
    IUiComponent* createExtension(const QString& extName, const QString& componentName);
    /**
    * @brief 销毁扩展项
    */
    void destroyExtension(IUiComponent* pExtension);
private:
    Qt::DockWidgetAreas docksAreaFromString(const char* areas);
    Qt::DockWidgetArea  dockAreaFromString(const char* areas);
    bool loadConfigFromXML(char* xml);
    bool loadConfigFromNode(WD::XMLNode* node);
private:
    // 加载组件库
    // comPath 存放所有组件的路径
    // comName 组件名称(组件动态库名称,不带后缀)
    QLibrary* loadComLib(const QString& comPath, const QString& comName);
    void collectTranslationFiles(const QString& dirPath, WD::StringVector& outFiles);
    bool loadExtensionData(const QString& extPath, const QString& extName, ComponentData& outData);
    ComponentData* findExtensionData(const QString& extName);
};
