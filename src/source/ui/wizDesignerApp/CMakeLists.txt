cmake_minimum_required(VERSION 3.10)
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_C_STANDARD 11)
set(TARGET_NAME WIZDesigner)
project(WizDesignerApp)

if (APPLE)
#    set(BUILD_SHARED_LIBS ON)
#    set(VCPKG_LIBRARY_LINKAGE dynamic)
#    set(gflags_USE_STATIC_LIBS OFF)
#    set(GFLAGS_USE_STATIC_LIBS OFF)
#    set(GFLAGS_IS_A_DLL ON)
#    add_definitions(-DGLOG_USE_GLOG_EXPORT=1)
#    set(GLOG_EXPORT ON)
#    add_definitions(-DGLOG_NO_EXPORT)
    find_package(RapidJSON CONFIG REQUIRED)
    find_package(Protobuf CONFIG REQUIRED)
    find_package(gRPC CONFIG REQUIRED)
    find_package(glog CONFIG REQUIRED)
    find_package(RdKafka CONFIG REQUIRED)
    find_package(SqliteOrm CONFIG REQUIRED)
    find_package(miniocpp CONFIG REQUIRED)

    include("../../../cmake/Utils.cmake")
    include("../../../cmake/ProjectVersion.cmake")


    # add_subdirectory("../../utilLib/util.sdk.license" license)
    add_subdirectory("../../../3rd/sources/qtpropertybrowser" qtpropertybrowser)
    add_subdirectory("../../../3rd/sources/QScintilla" QScintilla)
    add_subdirectory("../../../3rd/sources/QtnRibbon" QtnRibbon)
    add_subdirectory("../../wizDesignerCore" wizDesignerCore)
    add_subdirectory("../../../3rd/sources/WIZDesignerDSL" WIZDesignerDSL)
    add_subdirectory("../ui.commonLibrary/ui.commonLib.weakObject" ui.commonLib.weakObject)
    add_subdirectory("newDC")
endif ()

    include_directories("../../../../src/lib/installed/include"
    "./newDC")
## 设置rc文件所需要的属性
# 注意rc文件中FILEVERSION和PRODUCTVERSION属性均使用RC_FILE_VERSION变量替换
# 由于window强制要求文件版本和产品版本分割符为“，”所以此处进行了替换
string(REPLACE "." "," RC_FILE_VERSION "${PRODUCT_VERSION_ENDING_ZERO}")
set(RC_COMPANY_NAME "${COMPANY_NAME}")
set(RC_FILE_DESCRIPTION "${PRODUCT_DESCRIPTION}")
# RC_BLOCKHEADER_FILE_VERSION为rc文件块头部所需要的文件版本，用处和RC_FILE_VERSION不同
set(RC_BLOCKHEADER_FILE_VERSION "${PRODUCT_VERSION_ENDING_ZERO}")
set(RC_INTERNAL_NAME "wizDesignerApp.rc")
set(RC_LEGAL_COPYRIGHT "${LEGAL_COPYRIGHT}")
set(RC_ORIGINAL_FILENAME "WIZDesigner.exe")
set(RC_PRODUCT_NAME "${PRODUCT_NAME} ${PRODUCT_YEAR}")
set(RC_PRODUCT_VERSION "${PRODUCT_VERSION_ENDING_ZERO}")
# 创建wizDesignerApp.rc文件
configure_file(${CMAKE_CURRENT_SOURCE_DIR}/wizDesignerApp.rc.in ${CMAKE_CURRENT_SOURCE_DIR}/wizDesignerApp.rc @ONLY)

set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)
find_package(Qt5 COMPONENTS Core Widgets WebEngineWidgets Network Svg Xml REQUIRED)

#find_package(Bullet REQUIRED)

set(HEADER_FILES
    "LicenseTipDialog.h"
    "LicenseCheckDialog.h"
    "resource.h"
    "WDBMNodeSerialize.h"
    "XMLDataLoader.h"
    "wizDesignerMainWindow.h"
	"UIComponentMgr.h"
	"UiMenuMgr.h"
	"UiInterface/IMainWindow.h"
    "UiInterface/UiComponent.h"
    "UiInterface/UiDebugger.h"
    "UiInterface/UiInterface.h"
    "UiInterface/UiNotice.h"
    "UiInterface/UiSystemNotice.h"
    "UiInterface/UiTranslate.h"
    "UiInterface/UiTranslate.inl"
    "Collaboration.h"
    "UiInterface/ICollaboration.h"
    "UiInterface/IDCStatusBar.h"
    "UiInterface/IRepeatThread.h"
    "UiInterface/UiRepeatThread.h"
    "DCTipWidget.h"
    "DCStatusBar.h"
    "BlockingProgressBarDialog.h"
    "preWidget/LoginUserLayout.h"
    "preWidget/OpenProLayout.h"
    "preWidget/LoadingLayout.h"
    "preWidget/CtrlStartupLayoutBase.h"
    "preWidget/WizSplashScreen.h"
    "UpdateMessage.txt"
    "newDC/queue/FIFOQueue.h"
    "newDC/serialize/WDBMNodeSerialize.h"
    "newDC/serialize/JsonNodeSerialize.h"
    "newDC/serialize/WDBMProtobufSerialize.h"
    "newDC/serialize/WDBMProtobufReaderWriter.h"
    "newDC/serialize/WDNodeCache.h"
    "newDC/service/DesignNode2Queue.h"
    "newDC/service/DesignQueue2RpcService.h"
    "newDC/service/DesignKafkaService.h"
    "newDC/service/DesignKafkaData.h"
    "newDC/service/DesignServiceBootstrap.h"
    "newDC/service/DesignTLV.h"
    "newDC/service/DesignTLVFile.h"
    "newDC/service/CodePairFactory.h"
    "newDC/service/ConfigParser.h"
    "newDC/rpc/client/DesignServiceClient.h"
    "newDC/rpc/client/DesignServiceConnectionPool.h"
    "newDC/rpc/minio/MinioTaskState.h"
    "newDC/rpc/minio/MinioTask.h"
    "newDC/rpc/minio/MinioDownloader.h"
    "newDC/common/DesignGlog.h"
    "newDC/common/CodePairInterface.h"
    "newDC/common/CodePairGrpc.h"
    "newDC/common/RandomAccessFileImpl.h"
    "newDC/proto/node.pb.h"
    "newDC/proto/service.grpc.pb.h"
    "newDC/store/IStore.h"
    "newDC/store/db_store/sqlite_orm.h"
    "newDC/store/db_store/DbStore.h"
    "newDC/store/db_store/NodeStorage.h"
)

set(SOURCE_FILES
    "main.cpp"
    "LicenseTipDialog.cpp"
    "LicenseCheckDialog.cpp"
    "WDBMNodeSerialize.cpp"
    "XMLDataLoader.cpp"
	"UIComponentMgr.cpp"
	"UiMenuMgr.cpp"
	"UiInterface/UiSystemNotice.cpp"
    "UiInterface/UiDebugger.cpp"
    "UiInterface/UiRepeatThread.cpp"
	"wizDesignerMainWindow.cpp"
    "Collaboration.cpp"
    "DCTipWidget.cpp"
    "DCStatusBar.cpp"
    "BlockingProgressBarDialog.cpp"
    "preWidget/LoginUserLayout.cpp"
    "preWidget/OpenProLayout.cpp"
    "preWidget/LoadingLayout.cpp"
    "preWidget/CtrlStartupLayoutBase.cpp"
    "preWidget/WizSplashScreen.cpp"
    "newDC/serialize/WDBMNodeSerialize.cpp"
    "newDC/serialize/JsonNodeSerialize.cpp"
    "newDC/serialize/WDBMProtobufSerialize.cpp"
    "newDC/serialize/WDBMProtobufReaderWriter.cpp"
    "newDC/serialize/WDNodeCache.cpp"
    "newDC/service/DesignNode2Queue.cpp"
    "newDC/service/DesignQueue2RpcService.cpp"
    "newDC/service/DesignKafkaService.cpp"
    "newDC/service/DesignKafkaData.cpp"
    "newDC/service/DesignServiceBootstrap.cpp"
    "newDC/service/DesignTLV.cpp"
    "newDC/service/DesignTLVFile.cpp"
    "newDC/service/CodePairFactory.cpp"
    "newDC/service/ConfigParser.cpp"
    "newDC/rpc/client/DesignServiceClient.cpp"
    "newDC/rpc/minio/MinioTask.cpp"
    "newDC/rpc/minio/MinioDownloader.cpp"
    "newDC/common/CodePairGrpc.cpp"
    "newDC/common/RandomAccessFileUnix.cpp"
    "newDC/common/RandomAccessFileWin.cpp"
    "newDC/proto/node.pb.cc"
    "newDC/proto/service.grpc.pb.cc"
    "newDC/store/db_store/DbStore.cpp"
    "newDC/store/db_store/NodeStorage.cpp"
)

set(FORM_FILES
        "LicenseTipDialog.ui"
        "LicenseCheckDialog.ui"
        "wizDesignerMainWindow.ui"
        "DCTipWidget.ui"
        "BlockingProgressBarDialog.ui"
)

set(RCC_FILES
        "wizDesignerMainWindow.qrc"
)

if (WIN32)
    add_executable(${TARGET_NAME} WIN32
            ${HEADER_FILES}
            ${SOURCE_FILES}
            ${FORM_FILES}
            ${RCC_FILES}
            "wizDesignerApp.rc"
    )
else ()
    add_executable(${TARGET_NAME}
            ${HEADER_FILES}
            ${SOURCE_FILES}
            ${FORM_FILES}
            ${RCC_FILES}
    )
    target_link_libraries(${TARGET_NAME} PRIVATE
            glog::glog
            gRPC::gpr gRPC::grpc gRPC::grpc++ gRPC::grpc++_alts
            RdKafka::rdkafka RdKafka::rdkafka++
            sqlite_orm::sqlite_orm
            miniocpp::miniocpp
    )
endif ()

target_link_libraries(${TARGET_NAME} PUBLIC wizDesignerCore ui.commonLib.WeakObject util.rapidxml util.rapidjson)
target_link_libraries(${TARGET_NAME} PUBLIC Qt5::Core Qt5::Widgets Qt5::WebEngineWidgets Qt5::Network Qt5::Xml QScintilla qtpropertybrowser QtnRibbon)
if (WIN32)
    target_link_libraries(${TARGET_NAME} PRIVATE glog gflags gpr grpc grpc++ address_sorting grpc_unsecure upb_mem_lib upb_message_lib upb_wire_lib upb_base_lib upb_json_lib upb_mini_descriptor_lib upb_textformat_lib grpc++_alts abseil_dll absl_flags_internal absl_flags_commandlineflag absl_flags_commandlineflag_internal absl_flags_marshalling absl_flags_config absl_flags_reflection absl_flags_program_name absl_flags_private_handle_accessor libprotobuf rdkafka rdkafka++ cares re2 libssl libcrypto utf8_range lz4)
    target_link_libraries(${TARGET_NAME} PRIVATE sqlite3 miniocpp curlpp curl pugixml inih INIReader zlib)
endif ()

target_include_directories(${TARGET_NAME} PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})

set_target_properties(${TARGET_NAME} PROPERTIES FOLDER "ui")

if (WIN32)
    add_dependencies(${TARGET_NAME} CopySSLRuntimeFiles)
endif ()

if (WIN32)
    add_custom_command(
            TARGET ${TARGET_NAME}
            POST_BUILD
            COMMAND wizDesignerAppPostBuild.bat
            WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
    )
elseif (UNIX)
    add_custom_command(
            TARGET ${TARGET_NAME}
            POST_BUILD
            COMMAND ./wizDesignerAppPostBuild.sh
            WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
    )
endif ()



if (WIN32)
    DeployQt(${TARGET_NAME})
    CopyImportedRuntimeDependency(${TARGET_NAME})
endif ()
