#include "OpenProLayout.h"
#include "UiInterface/UiTranslate.h"
#include "core/log/WDLoggerPort.h"
#include "core/businessModule/WDBMAuditObjectMgr.h"
#include "core/businessModule/typeMgr/WDBMTypeMgr.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/businessModule/admin/WDBMAdmin.h"
#include "core/businessModule/WDBMPermissionMgr.h"
#include <QDir>
#include <QNetworkReply>
#include <QEventLoop>
#include <QKeyEvent>
#include <filesystem>
#include <QApplication>
#include <QScreen>
#include <QToolTip>
#include "core/businessModule/WDBMProjectMgr.h"

static constexpr const char* Role_Code_Admin	=	"Admin";
static constexpr const char* Role_Code_PM		=	"PM";
static constexpr const char* Role_Code_MM		=	"MM";
static constexpr const char* Role_Code_Designer =	"Designer";

ProComboBox::ProComboBox(QWidget* parent)
	:QComboBox(parent)
{
}
void ProComboBox::keyPressEvent(QKeyEvent* event)
{
	//按下Enter,触发编辑完成信号
	if (event->key() == Qt::Key_Enter || event->key() == Qt::Key_Return)
	{
		// 可编辑状态，回车才触发编辑完成信号
		if(this->isEditable())
			emit sigComboBoxEditFinished();
	}
	else
	{
		QComboBox::keyPressEvent(event);
	}
}


using FinishFunc = std::function<void(QNetworkReply*)>;
/**
 * @brief 同步申请网络需求并进行处理
*/
template<typename ApplyFunc>
static void RequestSync(ApplyFunc apply, FinishFunc finish)
{
	auto pReply = apply();
	// 静态断言apply的返回值类型
	static_assert(std::is_same_v<decltype(pReply), QNetworkReply*>, "Fail to match type");
	QEventLoop evtLoop;
	QObject::connect(pReply, &QNetworkReply::finished, &evtLoop, &QEventLoop::quit);
	evtLoop.exec();
	finish(pReply);
}

static constexpr const uint64_t NewProjectFlagId = 0;

OpenProLayout::OpenProLayout(QSplashScreen& w, WD::WDCore& core, ICollaboration& collaboration, ILoginServer::LoginUserConfig& proList)
	:CtrlStartupLayoutBase(w)
	, _core(core)
	, _proList(proList)
	, _collaboration(collaboration)
{
	 auto dpi = getScreenDpi();
	QFont lableFont(QString::fromUtf8(WD::WDTs("OpenProLayout", "boldface").c_str()), 18, QFont::Normal);
	lableFont.setPointSizeF(18.0 / dpi);
	QFont comboBoxFont(QString::fromUtf8(WD::WDTs("OpenProLayout", "boldface").c_str()), 16, QFont::Normal);
	comboBoxFont.setPointSizeF(18.0 / dpi);

	QString frameStyleSheet = "QFrame{"
		"border-radius: 5px;"
		"background-color: rgba(255, 255, 255, 0.8);"
		"}";

	QString labelStyleSheet = "QLabel{"
		"color: rgb(80, 80, 80);"
		"background-color: transparent;"
		"padding-left: 2px;"
		"padding-top: 2px;"
		"}";

	QString comboBoxStyleSheet = QString::fromLocal8Bit("QComboBox{"
		"color: rgb(80, 80, 80);"
		"background-color: transparent;"
		"margin-top: 2px;"
		"margin-right: 2px;"
		"margin-bottom: 2px;"
		"margin-left: 4px;"

		"border: 1px solid black;"
		"border-radius: 5px;"
		"border-color: rgb(0, 0, 0);"
		"}"
		"QComboBox QAbstractItemView{"
		"color: rgb(80, 80, 80);"
		"border:1px solid black;"
		"background-color:  rgba(255, 255, 255, 0.8);"
		"border-radius: 5px;"
		"}");

	_framePro = new QFrame(&w);
	_framePro->setStyleSheet(frameStyleSheet);
	_labelPro = new QLabel(_framePro);
	_labelPro->setText(WD::WDTs("WizSplashScreen", "Projects").c_str());
	_labelPro->setFont(lableFont);
	_labelPro->setStyleSheet(labelStyleSheet);
	_labelPro->show();
	_comboBoxPro = new ProComboBox(_framePro);
	_comboBoxPro->setStyleSheet(comboBoxStyleSheet);
	_comboBoxPro->setFont(comboBoxFont);
	_comboBoxPro->show();
	this->add(_framePro);
	connect(_comboBoxPro
		, QOverload<int>::of(&ProComboBox::currentIndexChanged)
		, this
		, &OpenProLayout::slotProComboBoxCurrentIndexChanged);
	// 项目下拉框第index项高亮，显示提示信息
	connect(_comboBoxPro
			, QOverload<int>::of(&ProComboBox::highlighted)
			, [&](int index)
			{
				auto itemText = _comboBoxPro->itemText(index);
				auto pView = qobject_cast<QAbstractItemView*>(_comboBoxPro->view());
				if(pView != nullptr)
				{
					auto pModel = pView->model();
					if(pModel != nullptr)
					{
						auto modelIndex = pModel->index(index, 0);
						auto rect = pView->visualRect(modelIndex);

						auto viewWidth = pView->width();
						// 当前项文本未完全显示，则显示提示文本；反之不提示
						if(rect.width() > viewWidth)
						{
							// 获取当前项的局部的z左上角坐标
							auto topLeftLocal = rect.topLeft();
							// 获取全局的左上角坐标
							auto topLeftGlobal = pView->mapToGlobal(topLeftLocal);
							// 将当前项左上角的坐标值向右移动视图的宽度，使提示信息显示在当前项视图的右侧
							QToolTip::showText(topLeftGlobal + QPoint(viewWidth, 0), itemText);
						}
						else
						{
							QToolTip::hideText();
						}
					}
				}
			});
	connect(_comboBoxPro
		, &ProComboBox::sigComboBoxEditFinished
		, this
		, &OpenProLayout::slotComboBoxEditFinish);
	connect(_comboBoxPro
		, &ProComboBox::currentTextChanged
		, this
		, &OpenProLayout::slotComboBoxCurrentTextChanged);
	connect(_comboBoxPro, QOverload<int>::of(&ProComboBox::activated), this, &OpenProLayout::slotProComboBoxCurrentIndexChanged);
	_frameRole = new QFrame(&w);
	_frameRole->setStyleSheet(frameStyleSheet);
	_labelRole = new QLabel(_frameRole);
	_labelRole->setText(WD::WDTs("WizSplashScreen", "Roles").c_str());
	_labelRole->setFont(lableFont);
	_labelRole->setStyleSheet(labelStyleSheet);
	_labelRole->show();
	_comboBoxRole = new QComboBox(_frameRole);
	_comboBoxRole->setStyleSheet(comboBoxStyleSheet);
	_comboBoxRole->setFont(comboBoxFont);
	_comboBoxRole->show();
	// 协同模式展示角色，单机不展示
	if(_collaboration.actived())
		this->add(_frameRole);

	_frameWorkSpace = new QFrame(&w);
	_frameWorkSpace->setStyleSheet(frameStyleSheet);
	_labelWorkSpace = new QLabel(_frameWorkSpace);
	_labelWorkSpace->setText(WD::WDTs("WizSplashScreen", "WorkSpace").c_str());
	_labelWorkSpace->setFont(lableFont);
	_labelWorkSpace->setStyleSheet(labelStyleSheet);
	_labelWorkSpace->show();
	_comboBoxWorkSpace = new QComboBox(_frameWorkSpace);
	_comboBoxWorkSpace->setStyleSheet(comboBoxStyleSheet);
	_comboBoxWorkSpace->setFont(comboBoxFont);
	_comboBoxWorkSpace->show();
	this->add(_frameWorkSpace);

	_frameModel = new QFrame(&w);
	_frameModel->setStyleSheet(frameStyleSheet);
	_labelModel = new QLabel(_frameModel);
	_labelModel->setFont(lableFont);
	_labelModel->setStyleSheet(labelStyleSheet);
	_labelModel->setText(WD::WDTs("WizSplashScreen", "Module").c_str());
	_labelModel->show();
	_comboBoxModel = new QComboBox(_frameModel);
	_comboBoxModel->setStyleSheet(comboBoxStyleSheet);
	_comboBoxModel->setFont(comboBoxFont);
	_comboBoxModel->show();
	this->add(_frameModel);

	// 登录按钮
	_btnLogin = new QPushButton(&w);
	_btnLogin->setText(WD::WDTs("WizSplashScreen", "Log In").c_str());
	_btnLogin->setFont(lableFont);
	_btnLogin->setStyleSheet("QPushButton{"
		"background-color: rgb(0, 160, 220, 1);"
		"color: rgb(255, 255, 255);"
		"border-radius: 5px;"
		"padding-top: 4px;"
		"padding-bottom: 4px;"
		"padding-left: 4px;"
		"padding-right: 4px;"
		"}"
	);
	this->add(_btnLogin);

	//退出按钮
	_btnExit = new QPushButton(&w);
	_btnExit->setText(WD::WDTs("WizSplashScreen", "Exit").c_str());
	_btnExit->setFont(lableFont);
	_btnExit->setStyleSheet("QPushButton{"
		"background-color:  rgba(255, 255, 255, 0.8);"
		"color:black;"
		"border-radius: 5px;"
		"padding-top: 4px;"
		"padding-bottom: 4px;"
		"padding-left: 4px;"
		"padding-right: 4px;"
		"}"
	);

	// 初始化 ComboBox
	_comboBoxModel->addItem("Designer", "Design");
	_comboBoxModel->addItem("Catalog", "Catalog");
	retranslateUi();
	_comboBoxWorkSpace->addItem(QString::fromUtf8(WD::WDTs("OpenProLayout", "all").c_str()));

	QString lableMsgStyleSheet =
		"QLabel{"
		"color: white;"
		"padding-left: 4px;"
		"padding-right: 4px;"
		"padding-top: 4px;"
		"padding-buttom: 4px;"
		"}";
	_labelMsg = new QLabel(&w);
	_labelMsg->setStyleSheet(lableMsgStyleSheet);
	auto msgFont = QFont(QString::fromUtf8(WD::WDTs("OpenProLayout", "boldface").c_str()), 18, QFont::Normal);
	msgFont.setPointSizeF(18.0 / getScreenDpi());
	_labelMsg->setFont(msgFont);

	connect(_btnLogin, &QPushButton::clicked, this, &OpenProLayout::slotLoginBtnClicked);
	connect(_btnExit, &QPushButton::clicked, this, &OpenProLayout::slotExitBtnClicked);
	connect(_comboBoxRole
		, QOverload<int>::of(&QComboBox::currentIndexChanged)
		, this
		, &OpenProLayout::slotRoleChanged);
	connect(this, &OpenProLayout::sigApplyCheckLogin, this, &OpenProLayout::slotApplyCheckLogin);
}
OpenProLayout::~OpenProLayout()
{
	if (_labelPro != nullptr)
	{
		delete _labelPro;
		_labelPro = nullptr;
	}
	if (_comboBoxPro != nullptr)
	{
		delete _comboBoxPro;
		_comboBoxPro = nullptr;
	}
	if (_framePro != nullptr)
	{
		delete _framePro;
		_framePro = nullptr;
	}
	if (_labelRole != nullptr)
	{
		delete _labelRole;
		_labelRole = nullptr;
	}
	if (_comboBoxRole != nullptr)
	{
		delete _comboBoxRole;
		_comboBoxRole = nullptr;
	}
	if (_frameRole != nullptr)
	{
		delete _frameRole;
		_frameRole = nullptr;
	}
	if (_labelWorkSpace != nullptr)
	{
		delete _labelWorkSpace;
		_labelWorkSpace = nullptr;
	}
	if (_comboBoxWorkSpace != nullptr)
	{
		delete _comboBoxWorkSpace;
		_comboBoxWorkSpace = nullptr;
	}
	if (_frameWorkSpace != nullptr)
	{
		delete _frameWorkSpace;
		_frameWorkSpace = nullptr;
	}
	if (_labelModel != nullptr)
	{
		delete _labelModel;
		_labelModel = nullptr;
	}
	if (_comboBoxModel != nullptr)
	{
		delete _comboBoxModel;
		_comboBoxModel = nullptr;
	}	
	if (_frameModel != nullptr)
	{
		delete _frameModel;
		_frameModel = nullptr;
	}
	if (_btnLogin != nullptr)
	{
		delete _btnLogin;
		_btnLogin = nullptr;
	}	
	if (_btnExit != nullptr)
	{
		delete _btnExit;
		_btnExit = nullptr;
	}	
	if (_labelMsg != nullptr)
	{
		delete _labelMsg;
		_labelMsg = nullptr;
	}	
}

void OpenProLayout::showMsg(const QString& msg)
{
	_labelMsg->setText(msg);
	_parent.repaint();
}
void OpenProLayout::keyPressEvent(QKeyEvent* event)
{
	WDUnused(event);
	// 这里判断是否是新建的项目,如果是新建项目的目录，则提示需要创建完成后才能打开
	if (_collaboration.actived())
		return ; 
	uint64_t dt = _comboBoxPro->currentData().toULongLong();
	if (dt != NewProjectFlagId)
		return ;
	//按下Enter,触发编辑完成信号
	if (event->key() == Qt::Key_Enter || event->key() == Qt::Key_Return)
	{
		slotComboBoxEditFinish();
	}
}

void OpenProLayout::onShowWidget()
{
	const auto& rect = _parent.rect();
	int parentH = rect.height();
	int parentW = rect.width();

	// 统一标签的的宽度
	int labelsWidth = WD::Max(_labelPro->width(), _labelWorkSpace->width());
	labelsWidth = WD::Max(labelsWidth, _labelModel->width());
	_labelPro->setMinimumWidth(labelsWidth);
	_labelRole->setMinimumWidth(labelsWidth);
	_labelWorkSpace->setMinimumWidth(labelsWidth);
	_labelModel->setMinimumWidth(labelsWidth);

	// 设置界面几个控件的最小值
	_framePro->setMinimumWidth(250);
	_frameRole->setMinimumWidth(250);
	_frameWorkSpace->setMinimumWidth(250);
	_frameModel->setMinimumWidth(250);
	_btnLogin->setMinimumWidth(250);

	// 统一ComboBox 宽度和位置
	//获取QFrame的宽度
	int comboBoxWidth = _framePro->width() - labelsWidth;
	_comboBoxPro->move(labelsWidth, 0);
	_comboBoxPro->setFixedWidth(comboBoxWidth);
	_comboBoxRole->move(labelsWidth, 0);
	_comboBoxRole->setFixedWidth(comboBoxWidth);
	_comboBoxWorkSpace->move(labelsWidth, 0);
	_comboBoxWorkSpace->setFixedWidth(comboBoxWidth);
	_comboBoxModel->move(labelsWidth, 0);
	_comboBoxModel->setFixedWidth(comboBoxWidth);

	// 统一设置所有控件的高度
	// 高度为35
	int frameHeight = 35;
	_framePro->setMinimumHeight(frameHeight);
	_labelPro->setMinimumHeight(frameHeight);
	_comboBoxPro->setMinimumHeight(frameHeight);
	_frameRole->setMinimumHeight(frameHeight);
	_labelRole->setMinimumHeight(frameHeight);
	_comboBoxRole->setMinimumHeight(frameHeight);
	_frameWorkSpace->setMinimumHeight(frameHeight);
	_labelWorkSpace->setMinimumHeight(frameHeight);
	_comboBoxWorkSpace->setMinimumHeight(frameHeight);
	_frameModel->setMinimumHeight(frameHeight);
	_labelModel->setMinimumHeight(frameHeight);
	_comboBoxModel->setMinimumHeight(frameHeight);
	_btnLogin->setMinimumHeight(frameHeight);
	_btnExit->setMinimumHeight(frameHeight);
	_btnExit->move(parentW - 140, 70);
	_btnExit->show();

	_labelMsg->setMinimumWidth(1200);
	_labelMsg->setMinimumHeight(30);
	_labelMsg->move(10, parentH - 40);
	// 清理上一次打开项目信息
	showMsg("");
	_labelMsg->show();
}
void OpenProLayout::onHideWidget()
{
	_btnExit->hide();
	_labelMsg->hide();
}

void OpenProLayout::slotLoginBtnClicked()
{
	setLoginWidgetEnabled(false);
	// 这里判断是否是新建的项目,如果是新建项目的目录，则提示需要创建完成后才能打开
	if(!_collaboration.actived() && _comboBoxPro->currentData().toULongLong() == NewProjectFlagId)
	{
		showMsg(QString::fromUtf8(WD::WDTs("OpenProLayout", "the new project has not been complete.Please press the [Enter] key to complete the project creation").c_str()));
		setLoginWidgetEnabled(true);
		return ;
	}
	QString info = QString::fromUtf8(WD::WDTs("OpenProLayout", "Current, we are obtaining user information").c_str());
	showMsg(info);

	int prjIdx = _comboBoxPro->currentIndex();
	if (prjIdx >= _proList.size())
	{
		// 项目打开失败
		showMsg(QString::fromUtf8(WD::WDTs("OpenProLayout", "the project failed to open").c_str()));
		setLoginWidgetEnabled(true);
		return;
	}

	// TODO: NEWDC
#if 0
    _networkApi.setProjectInfo(_proList[prjIdx]);
	// 保存项目信息
	auto& prjInfo = _networkApi.projectInfo();

	prjInfo.name = _comboBoxPro->currentText().toLocal8Bit().data();

    // 如果项目路径为空(协同端拉下来的数据和新建的工程),默认路径为
    if (prjInfo.projectDir.empty())
        prjInfo.projectDir = _core.projectMgr().projectsPath();

    // 初始化项目路径
    intProjectPath(prjInfo.projectDataDir());

	// 设置当前选择的工作区
	prjInfo.workingArea = _comboBoxWorkSpace->currentText().toLocal8Bit().data();
#endif
	// 设置当前选择的模块
	std::string moduleType = _comboBoxModel->currentData().toString().toLocal8Bit().data();
	_core.setModuleType(moduleType);

	//单机版本 不需要进行网络通信
	if (!_collaboration.actived())
        goto Login;

	// TODO: NEWDC
	// 获取项目是否已经被初始化
	//prjInfo.initialized = _networkApi.modelServerApi().projectInitialed();

	if (moduleType == "Design")
	{
		// 如果是设计模块，获取用户权限信息并添加到权限管理
		auto pCurBase = _core.currentBM();
		if (pCurBase != nullptr)
		{
			// TODO: NEWDC
#if 0
			showMsg(QString::fromUtf8(WD::WDTs("OpenProLayout", "the current user's permissions for the project are being retrieved").c_str()));
			auto permObjIds = _networkApi.modelServerApi().getPermissionObjects();
			LOG_INFO << "获取到有权限对象个数:" + WD::ToString((size_t)permObjIds.size());
			for (const auto& id : permObjIds)
				pCurBase->permissionMgr().add(id);
			auto declObjIds = _networkApi.modelServerApi().getDeclarationObjects();
			LOG_INFO << "获取到声明对象个数:" + WD::ToString((size_t)declObjIds.size());
			for (const auto& id : declObjIds)
				pCurBase->permissionMgr().add(id);
#endif
		}
		auto roleData = _comboBoxRole->currentData();
		if (!roleData.isValid())
			return ;
		std::string roleCode = roleData.toString().toUtf8().data();
		_collaboration.loginServer().openProject(_proList[prjIdx], roleCode);
	}
	else if (moduleType == "Catalog")
	{
		// 如果是元件模块，同步服务端元件库数据到本地
		showMsg(QString::fromUtf8(WD::WDTs("OpenProLayout", "Data is being synchronized").c_str()));
		if (!_collaboration.modelServer().pull())
		{
			showMsg(QString::fromUtf8(WD::WDTs("OpenProLayout", "Data synchronization failed").c_str()));
			setLoginWidgetEnabled(true);
			return;
		}
	}
	else if (moduleType == "Admin")
	{
		// TODO: NEWDC
#if 0
		// 获取用户权限信息并添加到权限管理
		showMsg(QString::fromUtf8(WD::WDTs("OpenProLayout", "the current user's permissions for the project are being retrieved").c_str()));
		auto permObjIds = _networkApi.modelServerApi().getPermissionObjects();
		LOG_INFO << "获取到有权限对象个数:" + WD::ToString((size_t)permObjIds.size());
		auto pCurBase = _core.currentBM();
		if (pCurBase != nullptr)
		{
			for (const auto& id : permObjIds)
				pCurBase->permissionMgr().add(id);
			auto declObjIds = _networkApi.modelServerApi().getDeclarationObjects();
			LOG_INFO << "获取到声明对象个数:" + WD::ToString((size_t)declObjIds.size());
			for (const auto& id : declObjIds)
				pCurBase->permissionMgr().add(id);
		}
#endif

		showMsg(QString::fromUtf8(WD::WDTs("OpenProLayout", "Data is being synchronized").c_str()));
		if (!_collaboration.modelServer().pull())
		{
			showMsg(QString::fromUtf8(WD::WDTs("OpenProLayout", "Data synchronization failed").c_str()));
			setLoginWidgetEnabled(true);
			return;
		}
	}
	showMsg(QString::fromUtf8(WD::WDTs("OpenProLayout", "verification successful.user information is being retrieved").c_str()));

Login:
    emit sigOpenProResult(0);
    setLoginWidgetEnabled(true);
}
void OpenProLayout::slotExitBtnClicked()
{
	//协同版本 回退至 用户登录页
	if (_collaboration.actived())
	{
		emit sigOpenProResult(1);
	}
	//单机版本 直接退出
	else
	{
		emit sigOpenProResult(-1);
	}
}
void OpenProLayout::slotProComboBoxCurrentIndexChanged(int index)
{
	WDUnused(index);
	// 刷新项目关联角色列表
	this->refreshRoleList();

	if(_collaboration.actived())
		return;
	auto rId = _comboBoxPro->currentData(Qt::UserRole).toULongLong();
	if(rId != NewProjectFlagId)
	{
		_comboBoxPro->setEditable(false);
		return ;
	}
	_comboBoxPro->setEditable(true);
	//设置 可编辑状态的样式
	QFont comboBoxFont(QString::fromUtf8(WD::WDTs("OpenProLayout", "boldface").c_str()), 16, QFont::Normal);
	comboBoxFont.setPointSizeF(18.0 / getScreenDpi());
	_comboBoxPro->setStyleSheet(QString::fromLocal8Bit(
		"QComboBox:editable{"
		"background-color: transparent;"
		"margin-top: 2px;"
		"margin-right: 2px;"
		"margin-bottom: 2px;"
		"margin-left: 4px;"
		"color: rgb(80, 80, 80);"

		"border: 1px solid black;"
		"border-radius: 5px;"
		"border-color: rgb(0, 0, 0);"
		"}"

		"QComboBox QAbstractItemView{"
		"color: rgb(80, 80, 80);"
		"border:1px solid black;"
		"background-color:  rgba(255, 255, 255, 0.8);"
		"border-radius: 5px;"
		"}"
	));
	_comboBoxPro->lineEdit()->setFont(comboBoxFont);
	// 交互优化,选择新建项目自动清空文本框内容, 并显示提示信息
	_comboBoxPro->lineEdit()->clear();
	_comboBoxPro->lineEdit()->setPlaceholderText(QString::fromUtf8(WD::WDTs("OpenProLayout", "Enter the name and press Enter key").c_str()));
}
void OpenProLayout::slotRoleChanged(int index)
{
	WDUnused(index);
	_comboBoxModel->clear();

	auto roleData = _comboBoxRole->currentData();
	if (!roleData.isValid())
		return ;

	// 根据当前选择角色重新初始化模块下拉框
    std::string roleType = roleData.toString().toUtf8().data();
	if (roleType == ILoginServer::LoginUserRole_Admin
		|| roleType == ILoginServer::LoginUserRole_PM)
	{
		_comboBoxModel->addItem("Designer", "Design");
		_comboBoxModel->addItem("Catalog", "Catalog");
		_comboBoxModel->addItem("Admin", "Admin");
	}
	else if (roleType == ILoginServer::LoginUserRole_MM)
	{
		_comboBoxModel->addItem("Designer", "Design");
		_comboBoxModel->addItem("Admin", "Admin");
	}
	if (roleType == ILoginServer::LoginUserRole_Designer)
	{
		_comboBoxModel->addItem("Designer", "Design");
	}
	Trs("WizSplashScreen"
		, _comboBoxModel);
}
void OpenProLayout::slotComboBoxEditFinish()
{
    std::string prjFolderName = _comboBoxPro->currentText().toLocal8Bit().data();
	if (prjFolderName.empty())
	{
		showMsg(QString::fromUtf8(WD::WDTs("OpenProLayout", "project name can't empty").c_str()));
		return;
	}
	bool isInvalid = false;
#ifdef _WIN32
	// win系统文件夹名称不能包含特殊字符: " * < > ? \ / | : . 全为空格
	std::regex parttern("(^\\s*$)|[\"*<>?\\\\/|:.]");
	isInvalid = std::regex_search(prjFolderName, parttern);
#elif __linux__
	// linux系统文件夹名称不能包含特殊字符 /
	std::regex parttern("[\\/]");
	isInvalid = std::regex_search(prjFolderName, parttern);
#endif

	if (isInvalid)
	{
		auto info = QString::fromUtf8(WD::WDTs("OpenProLayout", "the project name is illegal and cannot be").c_str());
		info += ": ";
		info += QString::fromLocal8Bit(prjFolderName.c_str());
		info += QString::fromUtf8(WD::WDTs("OpenProLayout", "please re-edit the project name").c_str());
		showMsg(info);
		return;
	}
	
    // 在当前的项目目录下检查同名项目是否存在
    char prjectsPath[1024] = { 0 };
    sprintf_s(prjectsPath, sizeof(prjectsPath), "%s/%s", _core.projectMgr().projectsPath().c_str(), prjFolderName.c_str());
	if (std::filesystem::exists(prjectsPath))
	{
		showMsg(QString::fromUtf8(WD::WDTs("OpenProLayout", "a project with the same name already exists.Please rename it").c_str()));
		return;
	}
    intProjectPath(prjectsPath);
	// 在最后一项之前插入 item
	int index = 0;
	if (_comboBoxPro->count() != 0)
		index = _comboBoxPro->count() - 1;
	_comboBoxPro->insertItem(index, QString::fromLocal8Bit(prjFolderName.c_str()));
	_comboBoxPro->setCurrentIndex(index);
	
	// 将新建的项目添加到项目列表中
	ILoginServer::UserCfgProj proInfo;
	proInfo.code = prjFolderName;
	_proList.push_back(proInfo);
	auto info = QString::fromUtf8(WD::WDTs("OpenProLayout", "project").c_str());
	info += ": ";
	info += QString::fromLocal8Bit(prjFolderName.c_str());
	info += QString::fromUtf8(WD::WDTs("OpenProLayout", "creation successful").c_str());
	showMsg(info);
}
void OpenProLayout::slotComboBoxCurrentTextChanged(const QString&)
{
	//设置项目 ComboBox 文本改变后的样式（因为在编辑完成的样式不正确，即使在编辑完成的槽函数设置样式也不生效）
	_comboBoxPro->setStyleSheet(QString::fromLocal8Bit(
		"QComboBox{"
		"background-color: transparent;"
		"margin-top: 2px;"
		"margin-right: 2px;"
		"margin-bottom: 2px;"
		"margin-left: 4px;"
		"color: rgb(80, 80, 80);"

		"border: 1px solid black;"
		"border-radius: 5px;"
		"border-color: rgb(0, 0, 0);"
		"}"
		"QComboBox QAbstractItemView{"
		"color: rgb(80, 80, 80);"
		"border:1px solid black;"
		"background-color:  rgba(255, 255, 255, 0.8);"
		"border-radius: 5px;"
		"}"
	));
}
void OpenProLayout::slotApplyCheckLogin(int loginType)
{
	// TODO: NEWDC
}

void OpenProLayout::InitProComboBox()
{
	_comboBoxPro->blockSignals(true);
	_comboBoxPro->clear();
	if (!_collaboration.actived())
	{
		//使用本地的项目初始化项目下拉框
		localInitProList();
	}

	// 初始化项目下拉项
	for (const auto& prj : _proList)
	{
		_comboBoxPro->addItem(QString::fromLocal8Bit(prj.code.c_str()), prj.id);
	}

	//特殊处理单机版本 ：新建项目选项,追加到后面
	if(!_collaboration.actived())
	{
		QString text = QString(">>").append(QString::fromUtf8(WD::WDTs("OpenProLayout", "create project").c_str()));
		_comboBoxPro->addItem(text, NewProjectFlagId);
	}
	_comboBoxPro->setCurrentIndex(-1);
	_comboBoxPro->blockSignals(false);

	_comboBoxPro->setCurrentIndex(0);
}
void OpenProLayout::localInitProList()
{
    // 扫描固定目录下的所有项目, 并更新到项目列表中
    std::string prjectsPath = _core.projectMgr().projectsPath();
    QDir dir(QString::fromLocal8Bit(prjectsPath.c_str()));
    QStringList folderList = dir.entryList(QDir::Dirs | QDir::NoDotAndDotDot);

    //清空项目列表信息
    _proList.clear();
    for (const QString& folderName : folderList)
    {
		ILoginServer::UserCfgProj proInfo;
        proInfo.code = folderName.toLocal8Bit().data();
        _proList.emplace_back(proInfo);
    }
}
void OpenProLayout::setLoginWidgetEnabled(bool flag)
{
	_comboBoxPro->setEnabled(flag);
	_comboBoxRole->setEnabled(flag);
	_comboBoxWorkSpace->setEnabled(flag);
	_comboBoxModel->setEnabled(flag);
	_btnLogin->setEnabled(flag);
}
void OpenProLayout::retranslateUi()
{
	Trs("WizSplashScreen"
		, _comboBoxModel);
}

static void AddChildrenType(WD::WDBMAuditObjectMgr& auditMgr, WD::WDBMTypeMgr& typeMgr, const std::string& typeName)
{
	const auto& childrenTypes = typeMgr.supportedChildTypes(typeName);
	if (childrenTypes.empty())
		return ;

	for (const auto& childrenType : childrenTypes)
	{
		if (childrenType == "USER")
			continue;
		if (auditMgr.checkChildrenType(childrenType))
			continue;
		auditMgr.addChildrenType(childrenType);
		AddChildrenType(auditMgr, typeMgr, childrenType);
	}
}

void OpenProLayout::collectAuditObjIds(const char* jsonFileName, std::set<WD::WDUuid>& ids)
{
	QFile file(QString::fromLocal8Bit(jsonFileName));
	if (!file.open(QIODevice::ReadOnly))
		return;
	auto rData = file.readAll();
	file.close();

	WD::JsonDoc doc;
	doc.Parse((char*)rData.data(), rData.length());
	if (doc.HasParseError())
		return;
	if (!doc.IsArray() || doc.Empty())
		return;
	parseAuditObjIds(doc, ids);
}
void OpenProLayout::parseAuditObjIds(const WD::JsonValue& arr, std::set<WD::WDUuid>& ids)
{
	if (!arr.IsArray())
		return;

	for (const auto& arrV : arr.GetArray())
	{
		if (!arrV.IsObject())
			continue;

		// 读取id
		if (!arrV.HasMember("Type") || !arrV["Type"].IsString() || !arrV.HasMember("Id") || !arrV["Type"].IsString())
			continue;

		std::string type = arrV["Type"].GetString();
		std::string id = arrV["Id"].GetString();

		auto pCurBase = _core.currentBM();
		if (pCurBase != nullptr && pCurBase->auditObjectMgr().contains(type))
		{
			ids.insert(WD::WDUuid::FromString(id));
		}

		// 创建并读取节点属性
		if (arrV.HasMember("children"))
		{
			parseAuditObjIds(arrV["children"], ids);
		}
	}
}

void OpenProLayout::intProjectPath(const std::string& projectPath)
{
    auto path = std::filesystem::path(projectPath);
    WD::WDBMProject info;
    info.projectDir = path.parent_path().string();
    info.code = path.filename().string();
    // 创建设计模块目录
    {
        auto jsonPath = std::filesystem::path(info.moduleDataFileName("Design")).parent_path();
        if (!std::filesystem::exists(jsonPath))
            std::filesystem::create_directories(jsonPath);

        auto xmlPath = std::filesystem::path(info.moduleConfigFileName("Design")).parent_path();
        if (!std::filesystem::exists(xmlPath))
            std::filesystem::create_directories(xmlPath);
    }
    // 创建元件模块目录
    {
        auto jsonPath = std::filesystem::path(info.moduleDataFileName("Catalog")).parent_path();
        if (!std::filesystem::exists(jsonPath))
            std::filesystem::create_directories(jsonPath);

        auto xmlPath = std::filesystem::path(info.moduleConfigFileName("Catalog")).parent_path();
        if (!std::filesystem::exists(xmlPath))
            std::filesystem::create_directories(xmlPath);
    }

}
std::vector<OpenProLayout::RoleData> OpenProLayout::getRolesInfo()
{
	std::vector<RoleData> roles;

	// TODO: NEWDC

	return roles;
}
void OpenProLayout::refreshRoleList()
{
	if (!_collaboration.actived())
		return ;

	_comboBoxRole->clear();
	int prjIdx = _comboBoxPro->currentIndex();
	if (prjIdx >= _proList.size())
		return;
	// 根据项目和用户更新角色选项
	for (const auto& role : _proList[prjIdx].roles)
	{
		auto	name	=	QString::fromUtf8(role.name.c_str());
		auto	code	=	role.code;
		if (code == ILoginServer::LoginUserRole_Admin)
		{
			_comboBoxRole->addItem(name, QString(ILoginServer::LoginUserRole_Admin));
		}
		else if (code == ILoginServer::LoginUserRole_PM)
		{
			_comboBoxRole->addItem(name, QString(ILoginServer::LoginUserRole_PM));
		}
		else if (code == ILoginServer::LoginUserRole_MM)
		{
			_comboBoxRole->addItem(name, QString(ILoginServer::LoginUserRole_MM));
		}
		else if (code == ILoginServer::LoginUserRole_Designer)
		{
			_comboBoxRole->addItem(name, QString(ILoginServer::LoginUserRole_Designer));
		}
	}
}