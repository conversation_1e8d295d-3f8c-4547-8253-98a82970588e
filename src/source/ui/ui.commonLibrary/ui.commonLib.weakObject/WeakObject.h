#pragma once
#include <QWidget>
#include "core/WDCore.h"


/**
* @brief Qt弱引用对象, 保存WD中的object对象的弱引用，注册至Qt元对象系统中用于userData 
* 
* @info 示例(T类型继承WD::Object)
* 
* // 设置userData
* T::SharePtr t;
* QWeakObject qWeakObject(t);
* QVariant userData;
* userData.setValue<QWeakObject>(qWeakObject);
* 
* // 获取userData
* QVariant userData;
* QWeakObject qWeakObject = userData.value<QWeakObject>();
* T::SharePtr t = qWeakObject.object();
* 
*/
class UiWeakObject : public QObject
{
    Q_OBJECT
public:
    UiWeakObject(WD::WDObject::SharedPtr obj = nullptr)
        :_weakObj(obj)
    {
    }
    UiWeakObject(const UiWeakObject& right)
    {
        _weakObj = right.object();
    }
    ~UiWeakObject()
    {}

public:
    /**
    * @brief 获取引用对象指针
    */
    WD::WDObject::SharedPtr object() const
    {
        return _weakObj.lock();
    }
    template <typename ObjectSubClass>
    typename ObjectSubClass::SharedPtr subObject() const
    {
        static_assert(std::is_base_of_v<WD::WDObject, ObjectSubClass>, "ObjectSubClass is not a subclass of WDObject!");
        WD::WDObject::SharedPtr pObj = this->object();
        if (pObj == nullptr)
            return nullptr;
        return pObj->toPtr<ObjectSubClass>();
    }

private:
    WD::WDObject::WeakPtr _weakObj;
};

Q_DECLARE_METATYPE(UiWeakObject)

/**
* @brief 注册类到Qt元对象系统
* (目前 该类型在wizDesignerMainWindow.cpp中注册)
*/
inline static void RegistObjectsToMetaObjectSys()
{
    // 注册QWeakObject类型
    qRegisterMetaType<UiWeakObject>("UiWeakObject");
}
