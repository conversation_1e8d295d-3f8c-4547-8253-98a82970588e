#include    "ManipulatingWord.h"
#include    <qfiledialog.h>
#ifdef WIN32
    #include    <qaxobject.h>
#endif
#include    "core/WDTranslate.h"

class ManipulatingWordPrivate
{
public:
    QAxObject*          _pWord;
    QAxObject*          _pCurDocument;
    bool                _isOpen;
    ManipulatingWord&   _d;
public:
    ManipulatingWordPrivate(ManipulatingWord& d) :_d(d)
    {
#ifdef WIN32
        _pWord = new QAxObject();
        _pCurDocument = nullptr;
        _isOpen = false;
#endif
    }
    ~ManipulatingWordPrivate()
    {
#ifdef WIN32
        if (_pCurDocument != nullptr)
        {
            _pCurDocument->dynamicCall("Close()");
            _isOpen = false;
            delete _pCurDocument;
            _pCurDocument = nullptr;
        }
        if (_pWord != nullptr)
        {
            _pWord->dynamicCall("Quit()");
            delete _pWord;
            _pWord = nullptr;
        }
#endif
    }
public:
    /**
    * @brief 文档是否打开
    */
    bool isOpen() const
    {
        return _isOpen;
    }
    bool createWordApp(ManipulatingWord::ApplicationFlag flag)
    {
        try 
        {
#ifdef WIN32
            bool state = false;
            if (flag == ManipulatingWord::ApplicationFlag::Wps)
            {
                // 用WPS打开
                state = _pWord->setControl("kwps.Application");
            }
            else if (flag == ManipulatingWord::ApplicationFlag::Word)
            {
                state = _pWord->setControl("Word.Application");
            }
            if (!state)
            {
                delete _pWord;
                _pWord = nullptr;
                return false;
            }
            // 不显示窗体
            _pWord->setProperty("Visible", false);
#endif
            return true;
        }
        catch (std::exception& e)
        {
            WDUnused(e);
            assert(false && e.what());
            return false;
        }
    }
    bool createNullWord(const QString & name)
    {
#ifdef WIN32
        // 若与该路径下已有Word文档重名则创建失败
        if (_d.Exists(name))
            return false;
        // 获取所有工作文档
        QAxObject* pDocuments = _pWord->querySubObject("Documents");
        if (pDocuments == nullptr)
            return false;
        // 新建一个文档
        pDocuments->dynamicCall("Add(void)");
        // 获取当前激活的文档
        _pCurDocument = _pWord->querySubObject("ActiveDocument");
        if (_pCurDocument == nullptr)
            return false;
        // 保存到指定路径下
        this->saveAs(name);
        _isOpen = true;
#endif
        return true;
    }
    bool open(const QString & path)
    {
#ifdef WIN32
        if (!_d.Exists(path))
            return false;
        // 获取所有工作文档
        QAxObject* pDocuments = _pWord->querySubObject("Documents");
        if (pDocuments == nullptr)
            return false;
        // 新建一个文档
        _pCurDocument = pDocuments->querySubObject("Open(QString)", path);
        if (_pCurDocument == nullptr)
            return false;
        _isOpen = true;
#endif
        return true;
    }
    void close()
    {
#ifdef WIN32
        if (_pCurDocument != nullptr)
        {
            // 关闭文件
            _pCurDocument->dynamicCall("Close()");
            _isOpen = false;
            delete _pCurDocument;
            _pCurDocument = nullptr;
        }
        // 退出
        if (_pWord != nullptr)
        {
            _pWord->dynamicCall("Quit()");
            delete _pWord;
            _pWord = nullptr;
        }
#endif
    }
    bool setMarks(const std::map<QString, QString>& marks)
    {
        bool flag = true;
#ifdef WIN32
        if (_pCurDocument == nullptr)
            return false;
        
        for (const auto& mark : marks)
        {
            // 获取文档中名称为proj标签
            QAxObject* bookmarkProj = _pCurDocument->querySubObject("Bookmarks(QVariant)", mark.first);
            // 选中标签,将项目名称插到标签位置
            if (bookmarkProj == nullptr)
                return false;
            bookmarkProj->dynamicCall("Select(void)");
            if (!bookmarkProj->querySubObject("Range")->setProperty("Text", mark.second))
            {
                flag = false;
            }
        }
#endif
        return flag;
    }
    bool saveAs(const QString & path)
    {
#ifdef WIN32
        if (_pCurDocument == nullptr)
            return false;
        // 保存碰撞报告到指定位置,"/"换成"\"
        return _pCurDocument->dynamicCall("SaveAs(const QString&)", path).toBool();
#else
        return false;
#endif
    }
    bool save()
    {
#ifdef WIN32
        if (_pCurDocument == nullptr)
            return false;
        return _pCurDocument->dynamicCall("Save()").toBool();
#else
        return false;
#endif
    }
    bool insertTextAtFront(const QString & text)
    {
#ifdef WIN32
        if (_pCurDocument == nullptr)
            return false;
        QAxObject* pSelect = _pWord->querySubObject("Selection");
        if (pSelect == nullptr)
            return false;
        pSelect->dynamicCall("TypeText(const QString&)", text);
#endif
        return true;
    }
    bool insertTextAtEnd(const QString & text)
    {
#ifdef WIN32
        if (_pCurDocument == nullptr)
            return false;
        QAxObject* pWordRange = _pCurDocument->querySubObject("Content");
        if (pWordRange == nullptr)
            return false;
        QAxObject* pParagraphs = pWordRange->querySubObject("Paragraphs");
        if (pParagraphs == nullptr)
            return false;
        QAxObject* pParagraph = pParagraphs->querySubObject("Last");
        if (pParagraph == nullptr)
            return false;
        QAxObject* pParaRange = pParagraph->querySubObject("Range");
        if (pParaRange == nullptr)
            return false;
        pParaRange->dynamicCall("InsertAfter(QString)", text);
        pWordRange->dynamicCall("InsertParagraphAfter(void)");
#endif
        return true;
    }
    bool insertPicture(const QString & label, QString & picPath)
    {
#ifdef WIN32
        if (_pCurDocument == nullptr)
            return false;
        QAxObject* bookMark = _pCurDocument->querySubObject("Bookmarks(QVariant)", label);
        if (bookMark != nullptr)
        {
            bookMark->dynamicCall("Select(void)");
            QAxObject* selection = _pWord->querySubObject("Selection");
            if (selection == nullptr)
                return false;
            selection->querySubObject("ParagraphFormat")->dynamicCall("Alignment", "wdAlignParagraphCenter");
            QAxObject* range = bookMark->querySubObject("Range");
            if (range == nullptr)
                return false;
            QVariant tmp = range->asVariant();
            picPath.replace("/", "\\");
            QList<QVariant>qList;
            qList << QVariant(picPath) << QVariant(false) << QVariant(true) << tmp;
            QAxObject* inlineShapes = _pCurDocument->querySubObject("InlineShapes");
            if (inlineShapes == nullptr)
                return false;
            inlineShapes->dynamicCall("AddPicture(const QString&, QVariant, QVariant, QVariant)", qList);
            return true;
        }
        else
#endif
            return false;
    }
    bool addPicture(QString & picPath)
    {
#ifdef WIN32
        if (_pCurDocument == nullptr)
            return false;
        QAxObject* selection = _pWord->querySubObject("Selection");
        if (selection == nullptr)
            return false;
        picPath.replace("/", "\\");

        QAxObject* inlineShapes = _pCurDocument->querySubObject("InlineShapes");
        if (inlineShapes == nullptr)
            return false;

        return inlineShapes->dynamicCall("AddPicture(const QString&)", picPath).toBool();
#else
        return false;
#endif
    }
};

ManipulatingWord::ManipulatingWord(ApplicationFlag flag)
{
    _p = new ManipulatingWordPrivate(*this);
    _p->createWordApp(flag);
}

ManipulatingWord::~ManipulatingWord()
{
    delete _p;
}

bool ManipulatingWord::open(const QString & path)
{
    return _p->open(path);
}

void ManipulatingWord::close()
{
    _p->close();
}

bool ManipulatingWord::isOpen() const
{
    return _p->isOpen();
}

bool ManipulatingWord::create(const QString & name)
{
    return _p->createNullWord(name);
}

bool ManipulatingWord::setTextMarks(const std::map<QString, QString>& marks)
{
    return _p->setMarks(marks);
}

bool ManipulatingWord::saveAs(const QString & path)
{
    return _p->saveAs(path);
}

bool ManipulatingWord::save()
{
    return _p->save();
}

bool ManipulatingWord::insertTextAtFront(const QString & text)
{
    return _p->insertTextAtFront(text);
}

bool ManipulatingWord::insertTextAtEnd(const QString & text)
{
    return _p->insertTextAtEnd(text);
}

bool ManipulatingWord::setPicMarks(const QString & label, QString & picPath)
{
    return _p->insertPicture(label, picPath);
}

bool ManipulatingWord::insertPicAtFront(QString & picPath)
{
    return _p->addPicture(picPath);
}
