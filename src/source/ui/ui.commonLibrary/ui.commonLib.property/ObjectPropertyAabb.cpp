#include "ObjectPropertyAabb.h"
#include "property/WDPropertyMathType.h"

Aabb3Type::Aabb3Type(ObjectPropertyWidget& ownWidget
    , QtGroupPropertyManager* pGroupMgr
    , QtStringPropertyManager* pStringMgr
    , QtDoublePropertyManager* pDoubleMgr
    , WD::WDProperty::SharedPtr pPtr)
    : PropertyBaseType(ownWidget, pPtr)
    , _pGroupMgr(pGroupMgr)
    , _pStringMgr(pStringMgr)
    , _pDoubleMgr(pDoubleMgr)
{
    _subProperty.fill(nullptr);
    _subSubProperty.fill(nullptr);
    _pRootProperty = this->initCreate();
}

Aabb3Type::~Aabb3Type()
{
}

bool Aabb3Type::updateValueFromWDPty()
{
    if (_pPty == nullptr)
        return false;
    if (_pRootProperty == nullptr)
        return false;
    if (_pStringMgr == nullptr)
        return false;
    if (_pDoubleMgr == nullptr)
        return false;
    //子属性栏的值
    QString subValue1;
    QString subValue2;
    //子子属性栏的值
    double subSubValue11;
    double subSubValue12;
    double subSubValue13;

    double subSubValue21;
    double subSubValue22;
    double subSubValue23;

    WD::WDPropertyDataType type = _pPty->type();
    switch (type)
    {
    case WD::PDT_FAabb3:
        {
            WD::WDPropertyFAabb3* pPty = WD::WDProperty::As<WD::WDPropertyFAabb3>(_pPty.get());
            if (pPty == nullptr)
                return false;
            auto value = pPty->getValue();
            subSubValue11 = value.min.x;
            subSubValue12 = value.min.y;
            subSubValue13 = value.min.z;

            subSubValue21 = value.max.x;
            subSubValue22 = value.max.y;
            subSubValue23 = value.max.z;
            subValue1 = QString::number(subSubValue11, 'f', 2) 
                + " " + QString::number(subSubValue12, 'f', 2) 
                + " " + QString::number(subSubValue13, 'f', 2);
            
            subValue2 = QString::number(subSubValue21, 'f', 2) 
                + " " + QString::number(subSubValue22, 'f', 2) 
                + " " + QString::number(subSubValue23, 'f', 2);
        }
        break;
    case WD::PDT_DAabb3:
        {
            WD::WDPropertyDAabb3* pPty = WD::WDProperty::As<WD::WDPropertyDAabb3>(_pPty.get());
            if (pPty == nullptr)
                return false;
            auto value = pPty->getValue();
            subSubValue11 = value.min.x;
            subSubValue12 = value.min.y;
            subSubValue13 = value.min.z;

            subSubValue21 = value.max.x;
            subSubValue22 = value.max.y;
            subSubValue23 = value.max.z;
            subValue1 = QString::number(subSubValue11, 'f', 2) 
                + " " + QString::number(subSubValue12, 'f', 2) 
                + " " + QString::number(subSubValue13, 'f', 2);
            
            subValue2 = QString::number(subSubValue21, 'f', 2) 
                + " " + QString::number(subSubValue22, 'f', 2) 
                + " " + QString::number(subSubValue23, 'f', 2);
        }
        break;
    default:
        return false;
        break;
    }
    //跟新子属性栏的显示值
    _pStringMgr->setValue(_subProperty[0],subValue1);
    this->updateShow(_subProperty[0]);
    _pStringMgr->setValue(_subProperty[1],subValue1);
    this->updateShow(_subProperty[1]);

    return true;
}

bool Aabb3Type::applyValue()
{
    double value1 = _pDoubleMgr->value(_subSubProperty[0]);
    double value2 = _pDoubleMgr->value(_subSubProperty[1]);
    double value3 = _pDoubleMgr->value(_subSubProperty[2]);

    double value4 = _pDoubleMgr->value(_subSubProperty[3]);
    double value5 = _pDoubleMgr->value(_subSubProperty[4]);
    double value6 = _pDoubleMgr->value(_subSubProperty[5]);

    WD::WDPropertyDataType type = _pPty->type();
    switch (type)
    {
    case WD::PDT_FAabb3:
        {
            WD::WDPropertyFAabb3* pPty = WD::WDProperty::As<WD::WDPropertyFAabb3>(_pPty.get());
            if (pPty == nullptr)
                return false;
            WD::FAabb3 aabb3(value1, value2, value3, value4, value5, value6);
            pPty->setValue(aabb3);
        }
        break;
    case WD::PDT_DAabb3:
        {
            WD::WDPropertyDAabb3* pPty = WD::WDProperty::As<WD::WDPropertyDAabb3>(_pPty.get());
            if (pPty == nullptr)
                return false;
            WD::DAabb3 aabb3(value1, value2, value3, value4, value5, value6);
            pPty->setValue(aabb3);
        }
        break;
    default:
        return false;
        break;
    }
    return true;
}

bool Aabb3Type::contains(QtProperty * pPty) const
{
    if (pPty == nullptr)
        return false;
    if (_pRootProperty == pPty)
        return true;
    for (size_t i = 0; i < _subProperty.size(); ++i)
    {
        if (_subProperty.at(i) == pPty)
            return true;
    }
    for (size_t i = 0; i < _subSubProperty.size(); ++i)
    {
        if (_subSubProperty.at(i) == pPty)
            return true;
    }
    return false;
}

QtProperty * Aabb3Type::getRootProperty()
{
    return _pRootProperty;
}

bool Aabb3Type::updateShowP(QtProperty * pPty)
{
    if (pPty == nullptr)
        return false;
    
    if (_pStringMgr == nullptr || _pDoubleMgr == nullptr)
        return false;

    if (_pRootProperty == nullptr
        || _subProperty.size() != 2
        || _subSubProperty.size() != 6)
        return false;

    //类型标识，区分子属性栏和父属性栏
    int type = 0;
    //索引标识，记录修改属性栏索引
    int index = -1;
    for (int i = 0; i < _subProperty.size(); ++i)
    {
        if (_subProperty.at(i) == pPty)
        {
            type = 1;
            index = i;
            break;
        }
    }
    if(type == 0)
    {
        for (int i = 0; i < _subSubProperty.size(); ++i)
        {
            if (_subSubProperty.at(i) == pPty)
            {
                type = 2;
                index = i;
                break;
            }
        }
    }
    if (index < 0)
        return false;

    switch (type)
    {
    //子属性栏
    case 1:
        {
            //更新子栏显示值
            if(!this->updateSubProperty(index))
                return false;
        }break;
    //子子属性栏
    case 2:
        {
            //更新父栏显示的值
            if(!this->updateParentProperty(index))
                return false;
            
        }break;
    default:
        return false;
        break;
    }
    return true;
}

QtProperty * Aabb3Type::initCreate()
{
    if (_pGroupMgr == nullptr)
        return nullptr;
    if (_pStringMgr == nullptr)
        return nullptr;
    if (_pDoubleMgr == nullptr)
        return nullptr;
    //在这里做国际化
    std::string name = _pPty->name();
    QString qName = QString::fromUtf8(name.data());
    //最外层属性栏
    _pRootProperty = _pGroupMgr->addProperty(qName);
    //子属性栏名称
    QString subName1 = qName+"Min";
    QString subName2 = qName+"Max";
    //子子属性栏名称
    QString minNameX = subName1+".X";
    QString minNameY = subName1+".Y";
    QString minNameZ = subName1+".Z";

    QString maxNameX = subName2+".X";
    QString maxNameY = subName2+".Y";
    QString maxNameZ = subName2+".Z";

    //创建子属性栏
    QtProperty* subPty1 = _pStringMgr->addProperty(subName1);
    QtProperty* subPty2 = _pStringMgr->addProperty(subName2);
    _subProperty = {subPty1, subPty2};
    //创建子子属性栏
    QtProperty* minXPty = _pDoubleMgr->addProperty(minNameX);
    QtProperty* minYPty = _pDoubleMgr->addProperty(minNameY);
    QtProperty* minZPty = _pDoubleMgr->addProperty(minNameZ);

    QtProperty* maxXPty = _pDoubleMgr->addProperty(maxNameX);
    QtProperty* maxYPty = _pDoubleMgr->addProperty(maxNameY);
    QtProperty* maxZPty = _pDoubleMgr->addProperty(maxNameZ);

    _subSubProperty = {minXPty, minYPty, minZPty, 
                       maxXPty, maxYPty, maxZPty};

    //添加父子关系
    _pRootProperty->addSubProperty(subPty1);
    _pRootProperty->addSubProperty(subPty2);

    subPty1->addSubProperty(minXPty);
    subPty1->addSubProperty(minYPty);
    subPty1->addSubProperty(minZPty);

    subPty2->addSubProperty(maxXPty);
    subPty2->addSubProperty(maxYPty);
    subPty2->addSubProperty(maxZPty);

    return _pRootProperty;
}

bool Aabb3Type::updateSubProperty(int index)
{
    //获取修改的值
    QString valueStr = _pStringMgr->value(_subProperty.at(index));
    if (!isStringValid(valueStr,3))
        return false;
    QStringList valueList = valueStr.split(" ");
    //计算子属性栏的开始和结束索引
    int indexBegin = index * 3;
    int indexEnd   = (index+1) * 3;
    //循环临时变量
    int temp = 0;
    //循环，更改子属性栏的显示值
    for (size_t i = indexBegin; i < indexEnd; ++i)
    {
        QtProperty* subSubProperty = _subSubProperty.at(i);
        double subSubValue = valueList.at(temp).toDouble();
        _pDoubleMgr->setValue(subSubProperty, subSubValue);
        temp += 1;
    }
    return true;
}

bool Aabb3Type::updateParentProperty(int index)
{
    //计算父属性栏索引值
    int parentIndex = index / 3;
    if (parentIndex < 0 && parentIndex > 2)
        return false;
    QtProperty* parentPty = _subProperty.at(parentIndex);
    QtProperty* currentPty = _subSubProperty.at(index);
    if (parentPty == nullptr)
        return false;
    //是否合法
    QString parentValue = _pStringMgr->value(parentPty);
    if (!isStringValid(parentValue,3))
        return false;
    QStringList valueList = parentValue.split(" ");
    //计算修改的子子属性栏在父属性栏值的索引
    int valueIndex = index % 3;
    if (valueIndex < 0 && valueIndex > 2)
        return false;
    //获取修改的值
    double currentValue = _pDoubleMgr->value(currentPty);
    QString currentStrValue = QString::number(currentValue, 'f', 2);
    //应用修改到父属性栏上
    valueList.replace(valueIndex,currentStrValue);
    _pStringMgr->setValue(parentPty, valueList.join(" "));
    return true;
}



Aabb2Type::Aabb2Type(ObjectPropertyWidget& ownWidget
    , QtGroupPropertyManager* pGroupMgr
    , QtStringPropertyManager* pStringMgr
    , QtDoublePropertyManager* pDoubleMgr
    , WD::WDProperty::SharedPtr pPtr)
    : PropertyBaseType(ownWidget, pPtr)
    , _pGroupMgr(pGroupMgr)
    , _pStringMgr(pStringMgr)
    , _pDoubleMgr(pDoubleMgr)
{
    _subProperty.fill(nullptr);
    _subSubProperty.fill(nullptr);
    _pRootProperty = this->initCreate();
}

Aabb2Type::~Aabb2Type()
{
}

bool Aabb2Type::updateValueFromWDPty()
{
    if (_pPty == nullptr)
        return false;
    if (_pRootProperty == nullptr)
        return false;
    if (_pStringMgr == nullptr)
        return false;
    if (_pDoubleMgr == nullptr)
        return false;
    //子属性栏的值
    QString subValue1;
    QString subValue2;
    //子子属性栏的值
    double subSubValue11;
    double subSubValue12;

    double subSubValue21;
    double subSubValue22;

    WD::WDPropertyDataType type = _pPty->type();
    switch (type)
    {
    case WD::PDT_FAabb2:
        {
            WD::WDPropertyFAabb2* pPty = WD::WDProperty::As<WD::WDPropertyFAabb2>(_pPty.get());
            if (pPty == nullptr)
                return false;
            auto value = pPty->getValue();
            subSubValue11 = value.min.x;
            subSubValue12 = value.min.y;

            subSubValue21 = value.max.x;
            subSubValue22 = value.max.y;
            subValue1 = QString::number(subSubValue11, 'f', 2) 
                + " " + QString::number(subSubValue12, 'f', 2);
            
            subValue2 = QString::number(subSubValue21, 'f', 2) 
                + " " + QString::number(subSubValue22, 'f', 2);

        }
        break;
    case WD::PDT_DAabb2:
        {
            WD::WDPropertyDAabb2* pPty = WD::WDProperty::As<WD::WDPropertyDAabb2>(_pPty.get());
            if (pPty == nullptr)
                return false;
            auto value = pPty->getValue();
            subSubValue11 = value.min.x;
            subSubValue12 = value.min.y;

            subSubValue21 = value.max.x;
            subSubValue22 = value.max.y;
            subValue1 = QString::number(subSubValue11, 'f', 2) 
                + " " + QString::number(subSubValue12, 'f', 2);
            
            subValue2 = QString::number(subSubValue21, 'f', 2) 
                + " " + QString::number(subSubValue22, 'f', 2);
        }
        break;
    default:
        return false;
        break;
    }
    //跟新子属性栏的显示值
    _pStringMgr->setValue(_subProperty[0],subValue1);
    this->updateShow(_subProperty[0]);
    _pStringMgr->setValue(_subProperty[1],subValue1);
    this->updateShow(_subProperty[1]);

    return true;
}

bool Aabb2Type::applyValue()
{
    double value1 = _pDoubleMgr->value(_subSubProperty[0]);
    double value2 = _pDoubleMgr->value(_subSubProperty[1]);

    double value3 = _pDoubleMgr->value(_subSubProperty[2]);
    double value4 = _pDoubleMgr->value(_subSubProperty[3]);

    WD::WDPropertyDataType type = _pPty->type();
    switch (type)
    {
    case WD::PDT_FAabb2:
        {
            WD::WDPropertyFAabb2* pPty = WD::WDProperty::As<WD::WDPropertyFAabb2>(_pPty.get());
            if (pPty == nullptr)
                return false;
            WD::FAabb2 aabb2(value1, value2, value3, value4);
            pPty->setValue(aabb2);
        }
        break;
    case WD::PDT_DAabb2:
        {
            WD::WDPropertyDAabb2* pPty = WD::WDProperty::As<WD::WDPropertyDAabb2>(_pPty.get());
            if (pPty == nullptr)
                return false;
            WD::DAabb2 aabb2(value1, value2, value3, value4);
            pPty->setValue(aabb2);
        }
        break;
    default:
        return false;
        break;
    }
    return true;
}

bool Aabb2Type::contains(QtProperty * pPty) const
{
    if (pPty == nullptr)
        return false;
    if (_pRootProperty == pPty)
        return true;
    for (size_t i = 0; i < _subProperty.size(); ++i)
    {
        if (_subProperty.at(i) == pPty)
            return true;
    }
    for (size_t i = 0; i < _subSubProperty.size(); ++i)
    {
        if (_subSubProperty.at(i) == pPty)
            return true;
    }
    return false;
}

QtProperty * Aabb2Type::getRootProperty()
{
    return _pRootProperty;
}

bool Aabb2Type::updateShowP(QtProperty * pPty)
{
    if (pPty == nullptr)
        return false;
    
    if (_pStringMgr == nullptr || _pDoubleMgr == nullptr)
        return false;

    if (_pRootProperty == nullptr
        || _subProperty.size() != 2
        || _subSubProperty.size() != 4)
        return false;

    //类型标识，区分子属性栏和父属性栏
    int type = 0;
    //索引标识，记录修改属性栏索引
    int index = -1;
    for (int i = 0; i < _subProperty.size(); ++i)
    {
        if (_subProperty.at(i) == pPty)
        {
            type = 1;
            index = i;
            break;
        }
    }
    if(type == 0)
    {
        for (int i = 0; i < _subSubProperty.size(); ++i)
        {
            if (_subSubProperty.at(i) == pPty)
            {
                type = 2;
                index = i;
                break;
            }
        }
    }
    if (index < 0)
        return false;

    switch (type)
    {
    //子属性栏
    case 1:
        {
            //更新子栏显示值
            if(!this->updateSubProperty(index))
                return false;
        }break;
    //子子属性栏
    case 2:
        {
            //更新父栏显示的值
            if(!this->updateParentProperty(index))
                return false;
            
        }break;
    default:
        return false;
        break;
    }
    return true;
}

QtProperty * Aabb2Type::initCreate()
{
    if (_pGroupMgr == nullptr)
        return nullptr;
    if (_pStringMgr == nullptr)
        return nullptr;
    if (_pDoubleMgr == nullptr)
        return nullptr;
    //在这里做国际化
    std::string name = _pPty->name();
    QString qName = QString::fromUtf8(name.data());
    //最外层属性栏
    _pRootProperty = _pGroupMgr->addProperty(qName);
    //子属性栏名称
    QString subName1 = qName+"Min";
    QString subName2 = qName+"Max";
    //子子属性栏名称
    QString minNameX = subName1+".X";
    QString minNameY = subName1+".Y";

    QString maxNameX = subName2+".X";
    QString maxNameY = subName2+".Y";

    //创建子属性栏
    QtProperty* subPty1 = _pStringMgr->addProperty(subName1);
    QtProperty* subPty2 = _pStringMgr->addProperty(subName2);
    _subProperty = {subPty1, subPty2};
    //创建子子属性栏
    QtProperty* minXPty = _pDoubleMgr->addProperty(minNameX);
    QtProperty* minYPty = _pDoubleMgr->addProperty(minNameY);

    QtProperty* maxXPty = _pDoubleMgr->addProperty(maxNameX);
    QtProperty* maxYPty = _pDoubleMgr->addProperty(maxNameY);

    _subSubProperty = {minXPty, minYPty
                      ,maxXPty, maxYPty};

    //添加父子关系
    _pRootProperty->addSubProperty(subPty1);
    _pRootProperty->addSubProperty(subPty2);

    subPty1->addSubProperty(minXPty);
    subPty1->addSubProperty(minYPty);

    subPty2->addSubProperty(maxXPty);
    subPty2->addSubProperty(maxYPty);

    return _pRootProperty;
}

bool Aabb2Type::updateSubProperty(int index)
{
    //获取修改的值
    QString valueStr = _pStringMgr->value(_subProperty.at(index));
    if (!isStringValid(valueStr,2))
        return false;
    QStringList valueList = valueStr.split(" ");
    //计算子属性栏的开始和结束索引
    int indexBegin = index * 2;
    int indexEnd   = (index+1) * 2;
    //循环临时变量
    int temp = 0;
    //循环，更改子属性栏的显示值
    for (size_t i = indexBegin; i < indexEnd; ++i)
    {
        QtProperty* subSubProperty = _subSubProperty.at(i);
        double subSubValue = valueList.at(temp).toDouble();
        _pDoubleMgr->setValue(subSubProperty, subSubValue);
        temp += 1;
    }
    return true;
}

bool Aabb2Type::updateParentProperty(int index)
{
    //计算父属性栏索引值
    int parentIndex = index / 2;
    if (parentIndex < 0 && parentIndex > 1)
        return false;
    QtProperty* parentPty = _subProperty.at(parentIndex);
    QtProperty* currentPty = _subSubProperty.at(index);
    if (parentPty == nullptr)
        return false;
    //是否合法
    QString parentValue = _pStringMgr->value(parentPty);
    if (!isStringValid(parentValue,2))
        return false;
    QStringList valueList = parentValue.split(" ");
    //计算修改的子子属性栏在父属性栏值的索引
    int valueIndex = index % 2;
    if (valueIndex < 0 && valueIndex > 1)
        return false;
    //获取修改的值
    double currentValue = _pDoubleMgr->value(currentPty);
    QString currentStrValue = QString::number(currentValue, 'f', 2);
    //应用修改到父属性栏上
    valueList.replace(valueIndex,currentStrValue);
    _pStringMgr->setValue(parentPty, valueList.join(" "));
    return true;
}
