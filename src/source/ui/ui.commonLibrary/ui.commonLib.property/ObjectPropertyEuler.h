#pragma once
#include "ObjectPropertyBaseType.h"

class EulerType: public PropertyBaseType
{
private:
    QtStringPropertyManager*    _pStringMgr;
    QtDoublePropertyManager*    _pDoubleMgr;
    QtEnumPropertyManager*      _pEnumMgr;
    QtProperty*                 _pRootProperty;
    std::array<QtProperty*, 4>  _pSubProperty;
    int                         _orderIndex;
    QStringList                 _orderStrList;
public:
    EulerType(ObjectPropertyWidget& ownWidget
    , QtStringPropertyManager* pStringMgr
    , QtDoublePropertyManager* pDoubleMgr
    , QtEnumPropertyManager* pEnumMgr
    , WD::WDProperty::SharedPtr pPtr);
    ~EulerType();
    /**
    * @brief WDProperty的值更新显示界面
    * @return true 更新成功 false 更新失败
    */
    virtual bool updateValueFromWDPty() override;
    /**
    * @brief 将保存的值应用到保存的WDProperty对象上
    * @return true 应用成功 false 应用失败
    */
    virtual bool applyValue() override;
    /**
    * @brief 是否包含此属性栏对象
    * @param pPty 属性栏对象
    * @return true 包含 false 不包含
    */
    virtual bool contains(QtProperty* pPty) const override;
    /**
    * @brief 返回根属性栏对象
    */
    virtual QtProperty* getRootProperty() override;
protected:
    /**
    * @brief 更新所有QtProperty显示
    */
    virtual bool updateShowP(QtProperty* pPty) override;
private:

    /**
    * @brief 初始化界面
    * @return 返回父属性栏
    */
    QtProperty* initCreate();

    /**
    * @brief 父属性栏更新，同步更新子属性栏显示
    * @param rootValue 父属性栏值
    * @return true 更新成功 false 更新失败
    */
    bool updateSubProperty(QString rootValue);
    /**
    * @brief 子属性栏更新，同步更新父属性栏
    * @return true 更新成功 false 更新失败
    */
    bool updateRootProperty(int index);
};
