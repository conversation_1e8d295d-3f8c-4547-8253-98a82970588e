#include "ObjectPropertyVec3.h"

//Vec3类型
Vec3Type::Vec3Type(ObjectPropertyWidget& ownWidget
    , QtStringPropertyManager* pStringMgr
    , QtAbstractPropertyManager* pSubMgr
    , WD::WDProperty::SharedPtr pPtr)
    : PropertyBaseType(ownWidget, pPtr)
    , _pStringMgr(pStringMgr)
    , _pSubMgr(pSubMgr)
{
    _pSubProperty.fill(nullptr);
    _pRootProperty = this->initCreate();
}
Vec3Type::~Vec3Type()
{

}

bool Vec3Type::updateValueFromWDPty()
{
    if (_pPty == nullptr)
        return false;
    if (_pRootProperty == nullptr)
        return false;
    if (_pSubMgr == nullptr)
        return false;
    if (_pStringMgr == nullptr)
        return false;

    WD::WDPropertyDataType type = _pPty->type();
    switch (type)
    {
    case WD::PDT_UCVec3:
    {
        QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
        WD::WDPropertyUCVec3* pPty = WD::WDProperty::As<WD::WDPropertyUCVec3>(_pPty.get());
        if (pPty == nullptr)
            return false;
        auto value = pPty->getValue();
        std::string tStr = value.toString();
        //属性栏要初始化的值
        unsigned char subValue1 = value.x;
        unsigned char subValue2 = value.y;
        unsigned char subValue3 = value.z;
        //赋初值
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        pTMgr->setValue(_pSubProperty.at(2),subValue3);
        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2)
            + " " + QString::number(subValue3);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
    break;
    case WD::PDT_IVec3:
    {
        QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
        WD::WDPropertyIVec3* pPty = WD::WDProperty::As<WD::WDPropertyIVec3>(_pPty.get());
        if (pPty == nullptr)
            return false;
        auto value = pPty->getValue();
        std::string tStr = value.toString();
        //属性栏要初始化的值
        int subValue1 = value.x;
        int subValue2 = value.y;
        int subValue3 = value.z;
        //赋初值
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        pTMgr->setValue(_pSubProperty.at(2),subValue3);
        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2)
            + " " + QString::number(subValue3);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
    break;
    case WD::PDT_UIVec3:
    {   
        QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
        WD::WDPropertyUIVec3* pPty = WD::WDProperty::As<WD::WDPropertyUIVec3>(_pPty.get());
        if (pPty == nullptr)
            return false;
        auto value = pPty->getValue();
        std::string tStr = value.toString();
        //属性栏要初始化的值
        unsigned int subValue1 = value.x;
        unsigned int subValue2 = value.y;
        unsigned int subValue3 = value.z;
        //赋初值
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        pTMgr->setValue(_pSubProperty.at(2),subValue3);
        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2)
            + " " + QString::number(subValue3);
        _pStringMgr->setValue(_pRootProperty, rootValue);

    }
    break;
    case WD::PDT_LLVec3:
    {   
        QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
        WD::WDPropertyLLVec3* pPty = WD::WDProperty::As<WD::WDPropertyLLVec3>(_pPty.get());
        if (pPty == nullptr)
            return false;
        auto value = pPty->getValue();
        std::string tStr = value.toString();
        //属性栏要初始化的值
        long long subValue1 = value.x;
        long long subValue2 = value.y;
        long long subValue3 = value.z;
        //赋初值
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        pTMgr->setValue(_pSubProperty.at(2),subValue3);
        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2)
            + " " + QString::number(subValue3);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
    break;
    case WD::PDT_ULLVec3:
    {   
        QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
        WD::WDPropertyULLVec3* pPty = WD::WDProperty::As<WD::WDPropertyULLVec3>(_pPty.get());
        if (pPty == nullptr)
            return false;
        auto value = pPty->getValue();
        std::string tStr = value.toString();
        //属性栏要初始化的值
        unsigned long long subValue1 = value.x;
        unsigned long long subValue2 = value.y;
        unsigned long long subValue3 = value.z;
        //赋初值
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        pTMgr->setValue(_pSubProperty.at(2),subValue3);
        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2)
            + " " + QString::number(subValue3);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
    case WD::PDT_FVec3:
    {   
        QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
        WD::WDPropertyFVec3* pPty = WD::WDProperty::As<WD::WDPropertyFVec3>(_pPty.get());
        if (pPty == nullptr)
            return false;
        auto value = pPty->getValue();
        std::string tStr = value.toString();
        //属性栏要初始化的值
        float subValue1 = value.x;
        float subValue2 = value.y;
        float subValue3 = value.z;
        //赋初值
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        pTMgr->setValue(_pSubProperty.at(2),subValue3);
        QString rootValue = QString::number(subValue1, 'f', 2) 
            + " " + QString::number(subValue2, 'f', 2)
            + " " + QString::number(subValue3, 'f', 2);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
        break;
    case WD::PDT_DVec3:
    {   
        QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
        WD::WDPropertyDVec3* pPty = WD::WDProperty::As<WD::WDPropertyDVec3>(_pPty.get());
        if (pPty == nullptr)
            return false;
        auto value = pPty->getValue();
        std::string tStr = value.toString();
        //属性栏要初始化的值
        double subValue1 = value.x;
        double subValue2 = value.y;
        double subValue3 = value.z;
        //赋初值
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        pTMgr->setValue(_pSubProperty.at(2),subValue3);
        QString rootValue = QString::number(subValue1, 'f', 2) 
            + " " + QString::number(subValue2, 'f', 2)
            + " " + QString::number(subValue3, 'f', 2);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
        break;
    default:
        return false;
        break;
    }
    return true;
}

bool Vec3Type::applyValue() 
{
    if (_pRootProperty == nullptr)
        return false;
    if (_pStringMgr == nullptr)
        return false;
    //父栏的显示值
    QString tRootValue = _pStringMgr->value(_pRootProperty);
    std::string strValue = tRootValue.toUtf8().data();
    //切割父栏显示值
    //QStringList subValueList = tRootValue.split(" ");
    //if (subValueList.size() != 3)
    //    return false;

    WD::WDPropertyDataType type = _pPty->type();

    switch (type)
    {
    case WD::PDT_UCVec3:
        {
            WD::WDPropertyUCVec3* p = WD::WDProperty::As<WD::WDPropertyUCVec3>(_pPty.get());
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            uchar value1 = pTMgr->value(_pSubProperty[0]).toChar().toLatin1();
            uchar value2 = pTMgr->value(_pSubProperty[1]).toChar().toLatin1();
            uchar value3 = pTMgr->value(_pSubProperty[2]).toChar().toLatin1();
            WD::UCVec3 uCVec3(value1, value2, value3);
            p->setValue(uCVec3);
        }
        break;
    case WD::PDT_IVec3:
        {
            WD::WDPropertyIVec3* p = WD::WDProperty::As<WD::WDPropertyIVec3>(_pPty.get());
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
            int value1 = pTMgr->value(_pSubProperty[0]);
            int value2 = pTMgr->value(_pSubProperty[1]);
            int value3 = pTMgr->value(_pSubProperty[2]);
            WD::IVec3 IVec3(value1, value2, value3);
            p->setValue(IVec3);
        }
        break;
    case WD::PDT_UIVec3:
        {
            WD::WDPropertyUIVec3* p = WD::WDProperty::As<WD::WDPropertyUIVec3>(_pPty.get());
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
            uint value1 = pTMgr->value(_pSubProperty[0]);
            uint value2 = pTMgr->value(_pSubProperty[1]);
            uint value3 = pTMgr->value(_pSubProperty[2]);
            WD::UIVec3 uIVec3(value1, value2, value3);
            p->setValue(uIVec3);
        }
        break;
    case WD::PDT_LLVec3:
        {
            WD::WDPropertyLLVec3* p = WD::WDProperty::As<WD::WDPropertyLLVec3>(_pPty.get());
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            long long value1 = pTMgr->value(_pSubProperty[0]).toLongLong();
            long long value2 = pTMgr->value(_pSubProperty[1]).toLongLong();
            long long value3 = pTMgr->value(_pSubProperty[2]).toLongLong();
            WD::LLVec3 lLVec3(value1, value2, value3);
            p->setValue(lLVec3);
        }
        break;
    case WD::PDT_ULLVec3:
        {
            WD::WDPropertyULLVec3* p = WD::WDProperty::As<WD::WDPropertyULLVec3>(_pPty.get());
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            unsigned long long value1 = pTMgr->value(_pSubProperty[0]).toLongLong();
            unsigned long long value2 = pTMgr->value(_pSubProperty[1]).toLongLong();
            unsigned long long value3 = pTMgr->value(_pSubProperty[2]).toLongLong();
            WD::ULLVec3 uLLVec3(value1, value2, value3);
            p->setValue(uLLVec3);
        }
        break;
    case WD::PDT_FVec3:
        {
            WD::WDPropertyFVec3* p = WD::WDProperty::As<WD::WDPropertyFVec3>(_pPty.get());
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
            float value1 = pTMgr->value(_pSubProperty[0]);
            float value2 = pTMgr->value(_pSubProperty[1]);
            float value3 = pTMgr->value(_pSubProperty[2]);
            WD::FVec3 fVec3(value1, value2, value3);
            p->setValue(fVec3);
        }
        break;
    case WD::PDT_DVec3:
        {
            WD::WDPropertyDVec3* p = WD::WDProperty::As<WD::WDPropertyDVec3>(_pPty.get());
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
            double value1 = pTMgr->value(_pSubProperty[0]);
            double value2 = pTMgr->value(_pSubProperty[1]);
            double value3 = pTMgr->value(_pSubProperty[2]);
            WD::DVec3 dVec3(value1, value2, value3);
            p->setValue(dVec3);
        }
        break;
    default:
        return false;
        break;
    }
    return true;
}

bool Vec3Type::contains(QtProperty * pPty) const
{
    if (pPty == _pRootProperty)
        return true;
    for (auto var : _pSubProperty)
    {
        if (pPty == var)
            return true;
    }
    return false;
}

QtProperty * Vec3Type::getRootProperty()
{
    return _pRootProperty;
}

bool Vec3Type::updateShowP(QtProperty * pPty)
{
    if (pPty == nullptr)
        return false;
    
    if (_pStringMgr == nullptr || _pSubMgr == nullptr)
        return false;

    if (_pRootProperty == nullptr
        || _pSubProperty.size() != 3)
        return false;


    //类型标识，区分子属性栏和父属性栏
    int type = 0;

    if (pPty == _pRootProperty)
    {
        type = 1;
    }

    if(type == 0)
    {
        for (size_t i = 0; i < _pSubProperty.size(); ++i)
        {
            if (_pSubProperty.at(i) == pPty)
            {
                type = 2;
                break;
            }
        }
    }
    switch (type)
    {
    case 1:
        {
            QString rootValue = _pStringMgr->value(_pRootProperty);
            if (! isStringValid(rootValue,3))
                return false;
            //更新子栏显示值
            this->updateSubProperty(rootValue);
        }break;
    case 2:
        {
            //更新父栏显示的值
            this->updateRootProperty();
            
        }break;
    default:
        return false;
        break;
    }
    return true;
}

//初始化创建属性栏
QtProperty* Vec3Type::initCreate()
{
    if (_pStringMgr == nullptr)
        return nullptr;
    if (_pSubMgr == nullptr)
        return nullptr;
    //属性栏对象
    QtProperty* p = nullptr;
    QtProperty* subPty1 = nullptr;
    QtProperty* subPty2 = nullptr;
    QtProperty* subPty3 = nullptr;

    //在这里做国际化WDCtxTs
    std::string ptyName = _pPty->name();
    QString tPtyName = QString::fromUtf8(ptyName.c_str());
    //子栏名称
    QString subPtyName1 = tPtyName + "X";
    QString subPtyName2 = tPtyName + "Y";
    QString subPtyName3 = tPtyName + "Z";
    WD::WDPropertyDataType type = _pPty->type();
    switch (type)
    {
    case WD::PDT_UCVec3:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            //创建父属性栏
            p = _pStringMgr ->addProperty(tPtyName);
            //创建子属性栏
            subPty1 = pTMgr->addProperty(QVariant::Char, subPtyName1);
            subPty2 = pTMgr->addProperty(QVariant::Char, subPtyName2);
            subPty3 = pTMgr->addProperty(QVariant::Char, subPtyName2);
            //添加层级关系
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
            p->addSubProperty(subPty3);
        }
        break;
    case WD::PDT_IVec3:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
            //创建父属性栏
            p = _pStringMgr ->addProperty(tPtyName);
            //创建子属性栏
            subPty1 = pTMgr->addProperty(subPtyName1);
            subPty2 = pTMgr->addProperty(subPtyName2);
            subPty3 = pTMgr->addProperty(subPtyName2);
            //添加层级关系
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
            p->addSubProperty(subPty3);
        }
        break;
    case WD::PDT_UIVec3:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
            //创建父属性栏
            p = _pStringMgr ->addProperty(tPtyName);
            //创建子属性栏
            subPty1 = pTMgr->addProperty(subPtyName1);
            subPty2 = pTMgr->addProperty(subPtyName2);
            subPty3 = pTMgr->addProperty(subPtyName2);
            //添加层级关系
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
            p->addSubProperty(subPty3);
        }
        break;
    case WD::PDT_LLVec3:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            //创建父属性栏
            p = _pStringMgr ->addProperty(tPtyName);
            //创建子属性栏
            subPty1 = pTMgr->addProperty(QVariant::LongLong,subPtyName1);
            subPty2 = pTMgr->addProperty(QVariant::LongLong,subPtyName2);
            subPty3 = pTMgr->addProperty(QVariant::LongLong,subPtyName2);
            //添加层级关系
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
            p->addSubProperty(subPty3);
        }
        break;
    case WD::PDT_ULLVec3:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            //创建父属性栏
            p = _pStringMgr ->addProperty(tPtyName);
            //创建子属性栏
            subPty1 = pTMgr->addProperty(QVariant::LongLong,subPtyName1);
            subPty2 = pTMgr->addProperty(QVariant::LongLong,subPtyName2);
            subPty3 = pTMgr->addProperty(QVariant::LongLong,subPtyName2);
            //添加层级关系
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
            p->addSubProperty(subPty3);
        }
        break;
    case WD::PDT_FVec3:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
            //创建父属性栏
            p = _pStringMgr ->addProperty(tPtyName);
            //创建子属性栏
            subPty1 = pTMgr->addProperty(subPtyName1);
            subPty2 = pTMgr->addProperty(subPtyName2);
            subPty3 = pTMgr->addProperty(subPtyName2);
            //添加层级关系
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
            p->addSubProperty(subPty3);
        }
        break;
    case WD::PDT_DVec3:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
            //创建父属性栏
            p = _pStringMgr ->addProperty(tPtyName);
            //创建子属性栏
            subPty1 = pTMgr->addProperty(subPtyName1);
            subPty2 = pTMgr->addProperty(subPtyName2);
            subPty3 = pTMgr->addProperty(subPtyName2);
            //添加层级关系
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
            p->addSubProperty(subPty3);
        }
        break;
    default:
        return nullptr;
        break;
    }
    //将子属性栏对象存入定长数组
    _pSubProperty = {subPty1,subPty2,subPty3};
    return p;
}

bool Vec3Type::updateSubProperty(QString rootValue)
{
    QStringList subValueList = rootValue.split(" ");
    //子属性栏
    QtProperty* sub1 = _pSubProperty.at(0);
    QtProperty* sub2 = _pSubProperty.at(1);
    QtProperty* sub3 = _pSubProperty.at(2);
    //子属性栏需要更新的值
    QString subValue1 = subValueList.at(0);
    QString subValue2 = subValueList.at(1);
    QString subValue3 = subValueList.at(2);

    WD::WDPropertyDataType type = _pPty->type();
    switch (type)
    {
    case WD::PDT_UCVec3:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            unsigned char value1 = *subValue1.toLocal8Bit().data();
            unsigned char value2 = *subValue2.toLocal8Bit().data();
            unsigned char value3 = *subValue3.toLocal8Bit().data();
            //设置子属性栏显示值
            pTMgr->setValue(sub1, value1);
            pTMgr->setValue(sub2, value2);
            pTMgr->setValue(sub3, value3);
            return true;
        }
        break;
    case WD::PDT_IVec3:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
            //设置子属性栏显示值
            pTMgr->setValue(sub1, subValue1.toInt());
            pTMgr->setValue(sub2, subValue2.toInt());
            pTMgr->setValue(sub3, subValue3.toInt());
            return true;
        }
        break;
    case WD::PDT_UIVec3:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
            //设置子属性栏显示值
            pTMgr->setValue(sub1, static_cast<unsigned int>(subValue1.toInt()));
            pTMgr->setValue(sub2, static_cast<unsigned int>(subValue2.toInt()));
            pTMgr->setValue(sub3, static_cast<unsigned int>(subValue3.toInt()));
            return true;
        }
        break;
    case WD::PDT_LLVec3:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            //设置子属性栏显示值
            pTMgr->setValue(sub1, subValue1.toLongLong());
            pTMgr->setValue(sub2, subValue1.toLongLong());
            pTMgr->setValue(sub3, subValue1.toLongLong());
            return true;
        }
        break;
    case WD::PDT_ULLVec3:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            //设置子属性栏显示值
            pTMgr->setValue(sub1, subValue1.toLongLong());
            pTMgr->setValue(sub2, subValue2.toLongLong());
            pTMgr->setValue(sub3, subValue3.toLongLong());
            return true;
        }
        break;
    case WD::PDT_FVec3:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
            pTMgr->setValue(sub1, static_cast<float>(subValue1.toDouble()));
            pTMgr->setValue(sub2, static_cast<float>(subValue2.toDouble()));
            pTMgr->setValue(sub3, static_cast<float>(subValue3.toDouble()));
            return true;
        }
        break;
    case WD::PDT_DVec3:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
            pTMgr->setValue(sub1, subValue1.toDouble());
            pTMgr->setValue(sub2, subValue2.toDouble());
            pTMgr->setValue(sub3, subValue3.toDouble());
            return true;
        }
        break;
    default:
        return false;
        break;
    }
    return true;
}

bool Vec3Type::updateRootProperty()
{
    QString rootValue = _pStringMgr->value(_pRootProperty);
    QStringList rootValueList = rootValue.split(" ");
    //子属性栏
    QtProperty* sub1 = _pSubProperty.at(0);
    QtProperty* sub2 = _pSubProperty.at(1);
    QtProperty* sub3 = _pSubProperty.at(2);

    WD::WDPropertyDataType type = _pPty->type();
    switch (type)
    {
    case WD::PDT_UCVec3:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            uchar subValue1 = pTMgr->value(sub1).toChar().toLatin1();
            uchar subValue2 = pTMgr->value(sub2).toChar().toLatin1();
            uchar subValue3 = pTMgr->value(sub3).toChar().toLatin1();
            
            rootValue = QVariant(subValue1).toString() 
                + " " + QVariant(subValue2).toString() 
                + " " +  QVariant(subValue3).toString();
        }
        break;
    case WD::PDT_IVec3:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
            int subValue1 = pTMgr->value(sub1);
            int subValue2 = pTMgr->value(sub2);
            int subValue3 = pTMgr->value(sub3);

            rootValue = QString::number(subValue1) 
                + " " + QString::number(subValue2) 
                + " " + QString::number(subValue3);
        }
        break;
    case WD::PDT_UIVec3:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
            unsigned int subValue1 = pTMgr->value(sub1);
            unsigned int subValue2 = pTMgr->value(sub2);
            unsigned int subValue3 = pTMgr->value(sub3);

            rootValue = QString::number(subValue1) 
                + " " + QString::number(subValue2) 
                + " " + QString::number(subValue3);
        }
        break;
    case WD::PDT_LLVec3:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            long long subValue1 = pTMgr->value(sub1).toLongLong();
            long long subValue2 = pTMgr->value(sub2).toLongLong();
            long long subValue3 = pTMgr->value(sub3).toLongLong();
            
            rootValue = QString::number(subValue1) 
                + " " + QString::number(subValue2) 
                + " " + QString::number(subValue3);
        }
        break;
    case WD::PDT_ULLVec3:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            unsigned long long subValue1 = pTMgr->value(sub1).toLongLong();
            unsigned long long subValue2 = pTMgr->value(sub2).toLongLong();
            unsigned long long subValue3 = pTMgr->value(sub3).toLongLong();
            
            rootValue = QString::number(subValue1) 
                + " " + QString::number(subValue2) 
                + " " + QString::number(subValue3);
        }
        break;
    case WD::PDT_FVec3:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
            float subValue1 = pTMgr->value(sub1);
            float subValue2 = pTMgr->value(sub2);
            float subValue3 = pTMgr->value(sub3);

            rootValue = QString::number(subValue1,'f',2) 
                + " " + QString::number(subValue2,'f',2) 
                + " " + QString::number(subValue3,'f',2);
        }
        break;
    case WD::PDT_DVec3:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
            double subValue1 = pTMgr->value(sub1);
            double subValue2 = pTMgr->value(sub2);
            double subValue3 = pTMgr->value(sub3);

            rootValue = QString::number(subValue1,'f',2) 
                + " " + QString::number(subValue2,'f',2) 
                + " " + QString::number(subValue3,'f',2);
        }
        break;
    default:
        return false;
        break;
    }
    _pStringMgr->setValue(_pRootProperty,rootValue);
    return true;
}

//Vec4类型
Vec4Type::Vec4Type(ObjectPropertyWidget & ownWidget
    , QtStringPropertyManager * pStringMgr
    , QtAbstractPropertyManager * pManager
    , WD::WDProperty::SharedPtr pPtr)
    : PropertyBaseType(ownWidget, pPtr)
    , _pStringMgr(pStringMgr)
    , _pSubMgr(pManager)
{
    _pSubProperty.fill(nullptr);
    _pRootProperty = this->initCreate();
}

Vec4Type::~Vec4Type()
{
}

bool Vec4Type::updateValueFromWDPty()
{
    if (_pPty == nullptr)
        return false;
    if (_pRootProperty == nullptr)
        return false;
    if (_pSubMgr == nullptr)
        return false;
    if (_pStringMgr == nullptr)
        return false;

    WD::WDPropertyDataType type = _pPty->type();
    switch (type)
    {
    case WD::PDT_UCVec4:
    {
        QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
        WD::WDPropertyUCVec4* pPty = WD::WDProperty::As<WD::WDPropertyUCVec4>(_pPty.get());
        if (pPty == nullptr)
            return false;
        auto value = pPty->getValue();
        std::string tStr = value.toString();
        //属性栏要初始化的值
        unsigned char subValue1 = value.x;
        unsigned char subValue2 = value.y;
        unsigned char subValue3 = value.z;
        unsigned char subValue4 = value.w;

        //赋初值
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        pTMgr->setValue(_pSubProperty.at(2),subValue3);
        pTMgr->setValue(_pSubProperty.at(3),subValue4);

        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2)
            + " " + QString::number(subValue3)
            + " " + QString::number(subValue4);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
    break;
    case WD::PDT_IVec4:
    {
        QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
        WD::WDPropertyIVec4* pPty = WD::WDProperty::As<WD::WDPropertyIVec4>(_pPty.get());
        if (pPty == nullptr)
            return false;
        auto value = pPty->getValue();
        std::string tStr = value.toString();
        //属性栏要初始化的值
        int subValue1 = value.x;
        int subValue2 = value.y;
        int subValue3 = value.z;
        int subValue4 = value.w;
        //赋初值
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        pTMgr->setValue(_pSubProperty.at(2),subValue3);
        pTMgr->setValue(_pSubProperty.at(3),subValue4);

        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2)
            + " " + QString::number(subValue3)
            + " " + QString::number(subValue4);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
    break;
    case WD::PDT_UIVec4:
    {   
        QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
        WD::WDPropertyUIVec4* pPty = WD::WDProperty::As<WD::WDPropertyUIVec4>(_pPty.get());
        if (pPty == nullptr)
            return false;
        auto value = pPty->getValue();
        std::string tStr = value.toString();
        //属性栏要初始化的值
        unsigned int subValue1 = value.x;
        unsigned int subValue2 = value.y;
        unsigned int subValue3 = value.z;
        unsigned int subValue4 = value.w;
        //赋初值
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        pTMgr->setValue(_pSubProperty.at(2),subValue3);
        pTMgr->setValue(_pSubProperty.at(3),subValue4);

        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2)
            + " " + QString::number(subValue3)
            + " " + QString::number(subValue4);
        _pStringMgr->setValue(_pRootProperty, rootValue);

    }
    break;
    case WD::PDT_LLVec4:
    {   
        QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
        WD::WDPropertyLLVec4* pPty = WD::WDProperty::As<WD::WDPropertyLLVec4>(_pPty.get());
        if (pPty == nullptr)
            return false;
        auto value = pPty->getValue();
        std::string tStr = value.toString();
        //属性栏要初始化的值
        long long subValue1 = value.x;
        long long subValue2 = value.y;
        long long subValue3 = value.z;
        long long subValue4 = value.w;
        //赋初值
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        pTMgr->setValue(_pSubProperty.at(2),subValue3);
        pTMgr->setValue(_pSubProperty.at(3),subValue4);

        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2)
            + " " + QString::number(subValue3)
            + " " + QString::number(subValue4);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
    break;
    case WD::PDT_ULLVec4:
    {   
        QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
        WD::WDPropertyULLVec4* pPty = WD::WDProperty::As<WD::WDPropertyULLVec4>(_pPty.get());
        if (pPty == nullptr)
            return false;
        auto value = pPty->getValue();
        std::string tStr = value.toString();
        //属性栏要初始化的值
        unsigned long long subValue1 = value.x;
        unsigned long long subValue2 = value.y;
        unsigned long long subValue3 = value.z;
        unsigned long long subValue4 = value.w;
        //赋初值
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        pTMgr->setValue(_pSubProperty.at(2),subValue3);
        pTMgr->setValue(_pSubProperty.at(3),subValue4);

        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2)
            + " " + QString::number(subValue3)
            + " " + QString::number(subValue4);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
    case WD::PDT_FVec4:
    {   
        QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
        WD::WDPropertyFVec4* pPty = WD::WDProperty::As<WD::WDPropertyFVec4>(_pPty.get());
        if (pPty == nullptr)
            return false;
        auto value = pPty->getValue();
        std::string tStr = value.toString();
        //属性栏要初始化的值
        float subValue1 = value.x;
        float subValue2 = value.y;
        float subValue3 = value.z;
        float subValue4 = value.w;
        //赋初值
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        pTMgr->setValue(_pSubProperty.at(2),subValue3);
        pTMgr->setValue(_pSubProperty.at(3),subValue4);

        QString rootValue = QString::number(subValue1, 'f', 2) 
            + " " + QString::number(subValue2, 'f', 2)
            + " " + QString::number(subValue3, 'f', 2)
            + " " + QString::number(subValue4, 'f', 2);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
        break;
    case WD::PDT_DVec4:
    {   
        QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
        WD::WDPropertyDVec4* pPty = WD::WDProperty::As<WD::WDPropertyDVec4>(_pPty.get());
        if (pPty == nullptr)
            return false;
        auto value = pPty->getValue();
        std::string tStr = value.toString();
        //属性栏要初始化的值
        double subValue1 = value.x;
        double subValue2 = value.y;
        double subValue3 = value.z;
        double subValue4 = value.w;
        //赋初值
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);
        pTMgr->setValue(_pSubProperty.at(2),subValue3);
        pTMgr->setValue(_pSubProperty.at(3),subValue4);

        QString rootValue = QString::number(subValue1, 'f', 2) 
            + " " + QString::number(subValue2, 'f', 2)
            + " " + QString::number(subValue3, 'f', 2)
            + " " + QString::number(subValue4, 'f', 2);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
        break;
    default:
        return false;
        break;
    }
    return true;
}

bool Vec4Type::applyValue()
 
{
    if (_pRootProperty == nullptr)
        return false;
    if (_pStringMgr == nullptr)
        return false;
    //父栏的显示值
    QString tRootValue = _pStringMgr->value(_pRootProperty);
    std::string strValue = tRootValue.toUtf8().data();

    WD::WDPropertyDataType type = _pPty->type();

    switch (type)
    {
    case WD::PDT_UCVec4:
        {
            WD::WDPropertyUCVec4* p = WD::WDProperty::As<WD::WDPropertyUCVec4>(_pPty.get());
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            uchar value1 = pTMgr->value(_pSubProperty[0]).toChar().toLatin1();
            uchar value2 = pTMgr->value(_pSubProperty[1]).toChar().toLatin1();
            uchar value3 = pTMgr->value(_pSubProperty[2]).toChar().toLatin1();
            uchar value4 = pTMgr->value(_pSubProperty[3]).toChar().toLatin1();
            WD::UCVec4 uCVec4(value1, value2, value3, value4);
            p->setValue(uCVec4);
        }
        break;
    case WD::PDT_IVec4:
        {
            WD::WDPropertyIVec4* p = WD::WDProperty::As<WD::WDPropertyIVec4>(_pPty.get());
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
            int value1 = pTMgr->value(_pSubProperty[0]);
            int value2 = pTMgr->value(_pSubProperty[1]);
            int value3 = pTMgr->value(_pSubProperty[2]);
            int value4 = pTMgr->value(_pSubProperty[3]);
            WD::IVec4 iVec4(value1, value2, value3, value4);
            iVec4.fromString(strValue);
            p->setValue(iVec4);
        }
        break;
    case WD::PDT_UIVec4:
        {
            WD::WDPropertyUIVec4* p = WD::WDProperty::As<WD::WDPropertyUIVec4>(_pPty.get());
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
            uint value1 = pTMgr->value(_pSubProperty[0]);
            uint value2 = pTMgr->value(_pSubProperty[1]);
            uint value3 = pTMgr->value(_pSubProperty[2]);
            uint value4 = pTMgr->value(_pSubProperty[3]);
            WD::UIVec4 uIVec4(value1, value2, value3, value4);
            p->setValue(uIVec4);
        }
        break;
    case WD::PDT_LLVec4:
        {
            WD::WDPropertyLLVec4* p = WD::WDProperty::As<WD::WDPropertyLLVec4>(_pPty.get());
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            long long value1 = pTMgr->value(_pSubProperty[0]).toLongLong();
            long long value2 = pTMgr->value(_pSubProperty[1]).toLongLong();
            long long value3 = pTMgr->value(_pSubProperty[2]).toLongLong();
            long long value4 = pTMgr->value(_pSubProperty[3]).toLongLong();
            WD::LLVec4 lLVec4(value1, value2, value3, value4);
            lLVec4.fromString(strValue);
            p->setValue(lLVec4);
        }
        break;
    case WD::PDT_ULLVec4:
        {
            WD::WDPropertyULLVec4* p = WD::WDProperty::As<WD::WDPropertyULLVec4>(_pPty.get());
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            unsigned long long value1 = pTMgr->value(_pSubProperty[0]).toLongLong();
            unsigned long long value2 = pTMgr->value(_pSubProperty[1]).toLongLong();
            unsigned long long value3 = pTMgr->value(_pSubProperty[2]).toLongLong();
            unsigned long long value4 = pTMgr->value(_pSubProperty[3]).toLongLong();
            WD::ULLVec4 uLLVec4(value1, value2, value3, value4);
            p->setValue(uLLVec4);
        }
        break;
    case WD::PDT_FVec4:
        {
            WD::WDPropertyFVec4* p = WD::WDProperty::As<WD::WDPropertyFVec4>(_pPty.get());
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
            float value1 = pTMgr->value(_pSubProperty[0]);
            float value2 = pTMgr->value(_pSubProperty[1]);
            float value3 = pTMgr->value(_pSubProperty[2]);
            float value4 = pTMgr->value(_pSubProperty[3]);
            WD::FVec4 fVec4(value1, value2, value3, value4);
            p->setValue(fVec4);
        }
        break;
    case WD::PDT_DVec4:
        {
            WD::WDPropertyDVec4* p = WD::WDProperty::As<WD::WDPropertyDVec4>(_pPty.get());
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
            double value1 = pTMgr->value(_pSubProperty[0]);
            double value2 = pTMgr->value(_pSubProperty[1]);
            double value3 = pTMgr->value(_pSubProperty[2]);
            double value4 = pTMgr->value(_pSubProperty[3]);
            WD::DVec4 dVec4(value1, value2, value3, value4);
            p->setValue(dVec4);
        }
        break;
    default:
        return false;
        break;
    }
    return true;
}

bool Vec4Type::contains(QtProperty * pPty) const
{
    if (pPty == _pRootProperty)
        return true;
    for (auto var : _pSubProperty)
    {
        if (pPty == var)
            return true;
    }
    return false;
}

QtProperty * Vec4Type::getRootProperty()
{
    return _pRootProperty;
}

bool Vec4Type::updateShowP(QtProperty * pPty)

{
    if (pPty == nullptr)
        return false;
    
    if (_pStringMgr == nullptr || _pSubMgr == nullptr)
        return false;

    if (_pRootProperty == nullptr
        || _pSubProperty.size() != 4)
        return false;


    //类型标识，区分子属性栏和父属性栏
    int type = 0;

    if (pPty == _pRootProperty)
    {
        type = 1;
    }

    if(type == 0)
    {
        for (size_t i = 0; i < _pSubProperty.size(); ++i)
        {
            if (_pSubProperty.at(i) == pPty)
            {
                type = 2;
                break;
            }
        }
    }
    switch (type)
    {
    case 1:
        {
            QString rootValue = _pStringMgr->value(_pRootProperty);
            if (! isStringValid(rootValue,4))
                return false;
            //更新子栏显示值
            this->updateSubProperty(rootValue);
        }break;
    case 2:
        {
            //更新父栏显示的值
            this->updateRootProperty();
            
        }break;
    default:
        return false;
        break;
    }
    return true;
}

QtProperty * Vec4Type::initCreate()

{
    if (_pStringMgr == nullptr)
        return nullptr;
    if (_pSubMgr == nullptr)
        return nullptr;
    //属性栏对象
    QtProperty* p = nullptr;
    QtProperty* subPty1 = nullptr;
    QtProperty* subPty2 = nullptr;
    QtProperty* subPty3 = nullptr;
    QtProperty* subPty4 = nullptr;

    //在这里做国际化WDCtxTs
    std::string ptyName = _pPty->name();
    QString tPtyName = QString::fromUtf8(ptyName.c_str());
    //子栏名称
    QString subPtyName1 = tPtyName + "X";
    QString subPtyName2 = tPtyName + "Y";
    QString subPtyName3 = tPtyName + "Z";
    QString subPtyName4 = tPtyName + "w";
    WD::WDPropertyDataType type = _pPty->type();
    switch (type)
    {
    case WD::PDT_UCVec4:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            //创建父属性栏
            p = _pStringMgr ->addProperty(tPtyName);
            //创建子属性栏
            subPty1 = pTMgr->addProperty(QVariant::Char, subPtyName1);
            subPty2 = pTMgr->addProperty(QVariant::Char, subPtyName2);
            subPty3 = pTMgr->addProperty(QVariant::Char, subPtyName3);
            subPty4 = pTMgr->addProperty(QVariant::Char, subPtyName4);
            //添加层级关系
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
            p->addSubProperty(subPty3);
            p->addSubProperty(subPty4);
        }
        break;
    case WD::PDT_IVec4:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
            //创建父属性栏
            p = _pStringMgr ->addProperty(tPtyName);
            //创建子属性栏
            subPty1 = pTMgr->addProperty(subPtyName1);
            subPty2 = pTMgr->addProperty(subPtyName2);
            subPty3 = pTMgr->addProperty(subPtyName3);
            subPty4 = pTMgr->addProperty(subPtyName4);
            //添加层级关系
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
            p->addSubProperty(subPty3);
            p->addSubProperty(subPty4);
        }
        break;
    case WD::PDT_UIVec4:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
            //创建父属性栏
            p = _pStringMgr ->addProperty(tPtyName);
            //创建子属性栏
            subPty1 = pTMgr->addProperty(subPtyName1);
            subPty2 = pTMgr->addProperty(subPtyName2);
            subPty3 = pTMgr->addProperty(subPtyName3);
            subPty4 = pTMgr->addProperty(subPtyName4);
            //添加层级关系
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
            p->addSubProperty(subPty3);
            p->addSubProperty(subPty4);
        }
        break;
    case WD::PDT_LLVec4:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            //创建父属性栏
            p = _pStringMgr ->addProperty(tPtyName);
            //创建子属性栏
            subPty1 = pTMgr->addProperty(QVariant::LongLong,subPtyName1);
            subPty2 = pTMgr->addProperty(QVariant::LongLong,subPtyName2);
            subPty3 = pTMgr->addProperty(QVariant::LongLong,subPtyName3);
            subPty4 = pTMgr->addProperty(QVariant::LongLong,subPtyName4);
            //添加层级关系
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
            p->addSubProperty(subPty3);
            p->addSubProperty(subPty4);
        }
        break;
    case WD::PDT_ULLVec4:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            //创建父属性栏
            p = _pStringMgr ->addProperty(tPtyName);
            //创建子属性栏
            subPty1 = pTMgr->addProperty(QVariant::LongLong,subPtyName1);
            subPty2 = pTMgr->addProperty(QVariant::LongLong,subPtyName2);
            subPty3 = pTMgr->addProperty(QVariant::LongLong,subPtyName3);
            subPty4 = pTMgr->addProperty(QVariant::LongLong,subPtyName4);
            //添加层级关系
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
            p->addSubProperty(subPty3);
            p->addSubProperty(subPty4);
        }
        break;
    case WD::PDT_FVec4:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
            //创建父属性栏
            p = _pStringMgr ->addProperty(tPtyName);
            //创建子属性栏
            subPty1 = pTMgr->addProperty(subPtyName1);
            subPty2 = pTMgr->addProperty(subPtyName2);
            subPty3 = pTMgr->addProperty(subPtyName3);
            subPty4 = pTMgr->addProperty(subPtyName4);
            //添加层级关系
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
            p->addSubProperty(subPty3);
            p->addSubProperty(subPty4);
        }
        break;
    case WD::PDT_DVec4:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
            //创建父属性栏
            p = _pStringMgr ->addProperty(tPtyName);
            //创建子属性栏
            subPty1 = pTMgr->addProperty(subPtyName1);
            subPty2 = pTMgr->addProperty(subPtyName2);
            subPty3 = pTMgr->addProperty(subPtyName3);
            subPty4 = pTMgr->addProperty(subPtyName4);
            //添加层级关系
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
            p->addSubProperty(subPty3);
            p->addSubProperty(subPty4);
        }
        break;
    default:
        break;
    }
    //将子属性栏对象存入定长数组
    _pSubProperty = {subPty1,subPty2,subPty3,subPty4};
    return p;
}

bool Vec4Type::updateSubProperty(QString rootValue)

{
    QStringList subValueList = rootValue.split(" ");
    //子属性栏
    QtProperty* sub1 = _pSubProperty.at(0);
    QtProperty* sub2 = _pSubProperty.at(1);
    QtProperty* sub3 = _pSubProperty.at(2);
    QtProperty* sub4 = _pSubProperty.at(3);
    //子属性栏需要更新的值
    QString subValue1 = subValueList.at(0);
    QString subValue2 = subValueList.at(1);
    QString subValue3 = subValueList.at(2);
    QString subValue4 = subValueList.at(3);

    WD::WDPropertyDataType type = _pPty->type();
    switch (type)
    {
    case WD::PDT_UCVec4:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            unsigned char value1 = *subValue1.toLocal8Bit().data();
            unsigned char value2 = *subValue2.toLocal8Bit().data();
            unsigned char value3 = *subValue3.toLocal8Bit().data();
            unsigned char value4 = *subValue4.toLocal8Bit().data();
            //设置子属性栏显示值
            pTMgr->setValue(sub1, value1);
            pTMgr->setValue(sub2, value2);
            pTMgr->setValue(sub3, value3);
            pTMgr->setValue(sub4, value4);
            return true;
        }
        break;
    case WD::PDT_IVec4:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
            //设置子属性栏显示值
            pTMgr->setValue(sub1, subValue1.toInt());
            pTMgr->setValue(sub2, subValue2.toInt());
            pTMgr->setValue(sub3, subValue3.toInt());
            pTMgr->setValue(sub4, subValue4.toInt());
            return true;
        }
        break;
    case WD::PDT_UIVec4:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
            //设置子属性栏显示值
            pTMgr->setValue(sub1, static_cast<unsigned int>(subValue1.toInt()));
            pTMgr->setValue(sub2, static_cast<unsigned int>(subValue2.toInt()));
            pTMgr->setValue(sub3, static_cast<unsigned int>(subValue3.toInt()));
            pTMgr->setValue(sub4, static_cast<unsigned int>(subValue4.toInt()));
            return true;
        }
        break;
    case WD::PDT_LLVec4:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            //设置子属性栏显示值
            pTMgr->setValue(sub1, subValue1.toLongLong());
            pTMgr->setValue(sub2, subValue1.toLongLong());
            pTMgr->setValue(sub3, subValue1.toLongLong());
            pTMgr->setValue(sub4, subValue1.toLongLong());
            return true;
        }
        break;
    case WD::PDT_ULLVec4:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            //设置子属性栏显示值
            pTMgr->setValue(sub1, subValue1.toLongLong());
            pTMgr->setValue(sub2, subValue2.toLongLong());
            pTMgr->setValue(sub3, subValue3.toLongLong());
            pTMgr->setValue(sub4, subValue4.toLongLong());
            return true;
        }
        break;
    case WD::PDT_FVec4:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
            pTMgr->setValue(sub1, static_cast<float>(subValue1.toDouble()));
            pTMgr->setValue(sub2, static_cast<float>(subValue2.toDouble()));
            pTMgr->setValue(sub3, static_cast<float>(subValue3.toDouble()));
            pTMgr->setValue(sub4, static_cast<float>(subValue4.toDouble()));
            return true;
        }
        break;
    case WD::PDT_DVec4:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
            pTMgr->setValue(sub1, subValue1.toDouble());
            pTMgr->setValue(sub2, subValue2.toDouble());
            pTMgr->setValue(sub3, subValue3.toDouble());
            pTMgr->setValue(sub4, subValue4.toDouble());
            return true;
        }
        break;
    default:
        return false;
        break;
    }
    return true;
}

bool Vec4Type::updateRootProperty()
{
    QString rootValue = _pStringMgr->value(_pRootProperty);
    QStringList rootValueList = rootValue.split(" ");
    //子属性栏
    QtProperty* sub1 = _pSubProperty.at(0);
    QtProperty* sub2 = _pSubProperty.at(1);
    QtProperty* sub3 = _pSubProperty.at(2);
    QtProperty* sub4 = _pSubProperty.at(3);

    WD::WDPropertyDataType type = _pPty->type();
    switch (type)
    {
    case WD::PDT_UCVec4:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            uchar subValue1 = pTMgr->value(sub1).toChar().toLatin1();
            uchar subValue2 = pTMgr->value(sub2).toChar().toLatin1();
            uchar subValue3 = pTMgr->value(sub3).toChar().toLatin1();
            uchar subValue4 = pTMgr->value(sub4).toChar().toLatin1();
            
            rootValue = QVariant(subValue1).toString() 
                + " " + QVariant(subValue2).toString() 
                + " " +  QVariant(subValue3).toString()
                + " " +  QVariant(subValue4).toString();
        }
        break;
    case WD::PDT_IVec4:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
            int subValue1 = pTMgr->value(sub1);
            int subValue2 = pTMgr->value(sub2);
            int subValue3 = pTMgr->value(sub3);
            int subValue4 = pTMgr->value(sub4);

            rootValue = QString::number(subValue1) 
                + " " + QString::number(subValue2) 
                + " " + QString::number(subValue3)
                + " " + QString::number(subValue4);
        }
        break;
    case WD::PDT_UIVec4:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
            unsigned int subValue1 = pTMgr->value(sub1);
            unsigned int subValue2 = pTMgr->value(sub2);
            unsigned int subValue3 = pTMgr->value(sub3);
            unsigned int subValue4 = pTMgr->value(sub4);

            rootValue = QString::number(subValue1) 
                + " " + QString::number(subValue2) 
                + " " + QString::number(subValue3)
                + " " + QString::number(subValue4);
        }
        break;
    case WD::PDT_LLVec4:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            long long subValue1 = pTMgr->value(sub1).toLongLong();
            long long subValue2 = pTMgr->value(sub2).toLongLong();
            long long subValue3 = pTMgr->value(sub3).toLongLong();
            long long subValue4 = pTMgr->value(sub4).toLongLong();
            
            rootValue = QString::number(subValue1) 
                + " " + QString::number(subValue2) 
                + " " + QString::number(subValue3)
                + " " + QString::number(subValue4);
        }
        break;
    case WD::PDT_ULLVec4:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            unsigned long long subValue1 = pTMgr->value(sub1).toLongLong();
            unsigned long long subValue2 = pTMgr->value(sub2).toLongLong();
            unsigned long long subValue3 = pTMgr->value(sub3).toLongLong();
            unsigned long long subValue4 = pTMgr->value(sub4).toLongLong();
            
            rootValue = QString::number(subValue1) 
                + " " + QString::number(subValue2) 
                + " " + QString::number(subValue3)
                + " " + QString::number(subValue4);
        }
        break;
    case WD::PDT_FVec4:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
            float subValue1 = pTMgr->value(sub1);
            float subValue2 = pTMgr->value(sub2);
            float subValue3 = pTMgr->value(sub3);
            float subValue4 = pTMgr->value(sub4);

            rootValue = QString::number(subValue1,'f',2) 
                + " " + QString::number(subValue2,'f',2) 
                + " " + QString::number(subValue3,'f',2)
                + " " + QString::number(subValue4,'f',2);
        }
        break;
    case WD::PDT_DVec4:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
            double subValue1 = pTMgr->value(sub1);
            double subValue2 = pTMgr->value(sub2);
            double subValue3 = pTMgr->value(sub3);
            double subValue4 = pTMgr->value(sub4);

            rootValue = QString::number(subValue1,'f',2) 
                + " " + QString::number(subValue2,'f',2) 
                + " " + QString::number(subValue3,'f',2)
                + " " + QString::number(subValue4,'f',2);
        }
        break;
    default:
        return false;
        break;
    }
    _pStringMgr->setValue(_pRootProperty,rootValue);
    return true;
}


//Vec2类型
Vec2Type::Vec2Type(ObjectPropertyWidget& ownWidget
    , QtStringPropertyManager* pStringMgr
    , QtAbstractPropertyManager* pSubMgr
    , WD::WDProperty::SharedPtr pPtr)
    : PropertyBaseType(ownWidget, pPtr)
    , _pStringMgr(pStringMgr)
    , _pSubMgr(pSubMgr)
{
    _pSubProperty.fill(nullptr);
    _pRootProperty = this->initCreate();
}

Vec2Type::~Vec2Type()
{

}

bool Vec2Type::updateValueFromWDPty()

{
    if (_pPty == nullptr)
        return false;
    if (_pRootProperty == nullptr)
        return false;
    if (_pSubMgr == nullptr)
        return false;
    if (_pStringMgr == nullptr)
        return false;

    WD::WDPropertyDataType type = _pPty->type();
    switch (type)
    {
    case WD::PDT_UCVec2:
    {
        QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
        WD::WDPropertyUCVec2* pPty = WD::WDProperty::As<WD::WDPropertyUCVec2>(_pPty.get());
        if (pPty == nullptr)
            return false;
        auto value = pPty->getValue();
        std::string tStr = value.toString();
        //属性栏要初始化的值
        unsigned char subValue1 = value.x;
        unsigned char subValue2 = value.y;

        //赋初值
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);

        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
    break;
    case WD::PDT_IVec2:
    {
        QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
        WD::WDPropertyIVec2* pPty = WD::WDProperty::As<WD::WDPropertyIVec2>(_pPty.get());
        if (pPty == nullptr)
            return false;
        auto value = pPty->getValue();
        std::string tStr = value.toString();
        //属性栏要初始化的值
        int subValue1 = value.x;
        int subValue2 = value.y;
        //赋初值
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);

        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
    break;
    case WD::PDT_UIVec2:
    {   
        QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
        WD::WDPropertyUIVec2* pPty = WD::WDProperty::As<WD::WDPropertyUIVec2>(_pPty.get());
        if (pPty == nullptr)
            return false;
        auto value = pPty->getValue();
        std::string tStr = value.toString();
        //属性栏要初始化的值
        unsigned int subValue1 = value.x;
        unsigned int subValue2 = value.y;
        //赋初值
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);

        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
    break;
    case WD::PDT_LLVec2:
    {   
        QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
        WD::WDPropertyLLVec2* pPty = WD::WDProperty::As<WD::WDPropertyLLVec2>(_pPty.get());
        if (pPty == nullptr)
            return false;
        auto value = pPty->getValue();
        std::string tStr = value.toString();
        //属性栏要初始化的值
        long long subValue1 = value.x;
        long long subValue2 = value.y;
        //赋初值
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);

        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
    break;
    case WD::PDT_ULLVec2:
    {   
        QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
        WD::WDPropertyULLVec2* pPty = WD::WDProperty::As<WD::WDPropertyULLVec2>(_pPty.get());
        if (pPty == nullptr)
            return false;
        auto value = pPty->getValue();
        std::string tStr = value.toString();
        //属性栏要初始化的值
        unsigned long long subValue1 = value.x;
        unsigned long long subValue2 = value.y;
        //赋初值
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);

        QString rootValue = QString::number(subValue1) 
            + " " + QString::number(subValue2);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
    case WD::PDT_FVec2:
    {   
        QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
        WD::WDPropertyFVec2* pPty = WD::WDProperty::As<WD::WDPropertyFVec2>(_pPty.get());
        if (pPty == nullptr)
            return false;
        auto value = pPty->getValue();
        std::string tStr = value.toString();
        //属性栏要初始化的值
        float subValue1 = value.x;
        float subValue2 = value.y;
        //赋初值
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);

        QString rootValue = QString::number(subValue1, 'f', 2) 
            + " " + QString::number(subValue2, 'f', 2);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
        break;
    case WD::PDT_DVec2:
    {   
        QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
        WD::WDPropertyDVec2* pPty = WD::WDProperty::As<WD::WDPropertyDVec2>(_pPty.get());
        if (pPty == nullptr)
            return false;
        auto value = pPty->getValue();
        std::string tStr = value.toString();
        //属性栏要初始化的值
        double subValue1 = value.x;
        double subValue2 = value.y;
        //赋初值
        pTMgr->setValue(_pSubProperty.at(0),subValue1);
        pTMgr->setValue(_pSubProperty.at(1),subValue2);

        QString rootValue = QString::number(subValue1, 'f', 2) 
            + " " + QString::number(subValue2, 'f', 2);
        _pStringMgr->setValue(_pRootProperty, rootValue);
    }
        break;
    default:
        return false;
        break;
    }
    return true;
}

bool Vec2Type::applyValue()
{
    if (_pRootProperty == nullptr)
        return false;
    if (_pStringMgr == nullptr)
        return false;
    //父栏的显示值
    QString tRootValue = _pStringMgr->value(_pRootProperty);
    std::string strValue = tRootValue.toUtf8().data();

    WD::WDPropertyDataType type = _pPty->type();

    switch (type)
    {
    case WD::PDT_UCVec2:
        {
            WD::WDPropertyUCVec2* p = WD::WDProperty::As<WD::WDPropertyUCVec2>(_pPty.get());
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            uchar value1 = pTMgr->value(_pSubProperty[0]).toChar().toLatin1();
            uchar value2 = pTMgr->value(_pSubProperty[1]).toChar().toLatin1();
            WD::UCVec2 uCVec2(value1, value2);
            p->setValue(uCVec2);
        }
        break;
    case WD::PDT_IVec2:
        {
            WD::WDPropertyIVec2* p = WD::WDProperty::As<WD::WDPropertyIVec2>(_pPty.get());
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
            int value1 = pTMgr->value(_pSubProperty[0]);
            int value2 = pTMgr->value(_pSubProperty[1]);
            WD::IVec2 iVec2(value1, value2);
            p->setValue(iVec2);
        }
        break;
    case WD::PDT_UIVec2:
        {
            WD::WDPropertyUIVec2* p = WD::WDProperty::As<WD::WDPropertyUIVec2>(_pPty.get());
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
            uint value1 = pTMgr->value(_pSubProperty[0]);
            uint value2 = pTMgr->value(_pSubProperty[1]);
            WD::UIVec2 uIVec2(value1, value2);
            p->setValue(uIVec2);
        }
        break;
    case WD::PDT_LLVec2:
        {
            WD::WDPropertyLLVec2* p = WD::WDProperty::As<WD::WDPropertyLLVec2>(_pPty.get());
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            long long value1 = pTMgr->value(_pSubProperty[0]).toLongLong();
            long long value2 = pTMgr->value(_pSubProperty[1]).toLongLong();
            WD::LLVec2 lLVec2(value1, value2);
            p->setValue(lLVec2);
        }
        break;
    case WD::PDT_ULLVec2:
        {
            WD::WDPropertyULLVec2* p = WD::WDProperty::As<WD::WDPropertyULLVec2>(_pPty.get());
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            unsigned long long value1 = pTMgr->value(_pSubProperty[0]).toLongLong();
            unsigned long long value2 = pTMgr->value(_pSubProperty[1]).toLongLong();
            WD::ULLVec2 uLLVec2(value1, value2);
            p->setValue(uLLVec2);
        }
        break;
    case WD::PDT_FVec2:
        {
            WD::WDPropertyFVec2* p = WD::WDProperty::As<WD::WDPropertyFVec2>(_pPty.get());
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
            float value1 = pTMgr->value(_pSubProperty[0]);
            float value2 = pTMgr->value(_pSubProperty[1]);
            WD::FVec2 fVec2(value1, value2);
            p->setValue(fVec2);
        }
        break;
    case WD::PDT_DVec2:
        {
            WD::WDPropertyDVec2* p = WD::WDProperty::As<WD::WDPropertyDVec2>(_pPty.get());
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
            float value1 = pTMgr->value(_pSubProperty[0]);
            float value2 = pTMgr->value(_pSubProperty[1]);
            WD::DVec2 dVec2(value1, value2);
            p->setValue(dVec2);
        }
        break;
    default:
        return false;
        break;
    }
    return true;
}

bool Vec2Type::contains(QtProperty * pPty) const

{
    if (pPty == _pRootProperty)
        return true;
    for (auto var : _pSubProperty)
    {
        if (pPty == var)
            return true;
    }
    return false;
}

QtProperty * Vec2Type::getRootProperty()
{
    return _pRootProperty;
}

bool Vec2Type::updateShowP(QtProperty * pPty)


{
    if (pPty == nullptr)
        return false;
    
    if (_pStringMgr == nullptr || _pSubMgr == nullptr)
        return false;

    if (_pRootProperty == nullptr
        || _pSubProperty.size() != 2)
        return false;


    //类型标识，区分子属性栏和父属性栏
    int type = 0;

    if (pPty == _pRootProperty)
    {
        type = 1;
    }

    if(type == 0)
    {
        for (size_t i = 0; i < _pSubProperty.size(); ++i)
        {
            if (_pSubProperty.at(i) == pPty)
            {
                type = 2;
                break;
            }
        }
    }
    switch (type)
    {
    case 1:
        {
            QString rootValue = _pStringMgr->value(_pRootProperty);
            if (! isStringValid(rootValue,4))
                return false;
            //更新子栏显示值
            this->updateSubProperty(rootValue);
        }break;
    case 2:
        {
            //更新父栏显示的值
            this->updateRootProperty();
            
        }break;
    default:
        return false;
        break;
    }
    return true;
}

QtProperty * Vec2Type::initCreate()
{
    if (_pStringMgr == nullptr)
        return nullptr;
    if (_pSubMgr == nullptr)
        return nullptr;
    //属性栏对象
    QtProperty* p = nullptr;
    QtProperty* subPty1 = nullptr;
    QtProperty* subPty2 = nullptr;

    //在这里做国际化WDCtxTs
    std::string ptyName = _pPty->name();
    QString tPtyName = QString::fromUtf8(ptyName.c_str());
    //子栏名称
    QString subPtyName1 = tPtyName + "X";
    QString subPtyName2 = tPtyName + "Y";
    WD::WDPropertyDataType type = _pPty->type();
    switch (type)
    {
    case WD::PDT_UCVec2:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            //创建父属性栏
            p = _pStringMgr ->addProperty(tPtyName);
            //创建子属性栏
            subPty1 = pTMgr->addProperty(QVariant::Char, subPtyName1);
            subPty2 = pTMgr->addProperty(QVariant::Char, subPtyName2);
            //添加层级关系
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
        }
        break;
    case WD::PDT_IVec2:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
            //创建父属性栏
            p = _pStringMgr ->addProperty(tPtyName);
            //创建子属性栏
            subPty1 = pTMgr->addProperty(subPtyName1);
            subPty2 = pTMgr->addProperty(subPtyName2);
            //添加层级关系
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
        }
        break;
    case WD::PDT_UIVec2:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
            //创建父属性栏
            p = _pStringMgr ->addProperty(tPtyName);
            //创建子属性栏
            subPty1 = pTMgr->addProperty(subPtyName1);
            subPty2 = pTMgr->addProperty(subPtyName2);
            //添加层级关系
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
        }
        break;
    case WD::PDT_LLVec2:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            //创建父属性栏
            p = _pStringMgr ->addProperty(tPtyName);
            //创建子属性栏
            subPty1 = pTMgr->addProperty(QVariant::LongLong,subPtyName1);
            subPty2 = pTMgr->addProperty(QVariant::LongLong,subPtyName2);
            //添加层级关系
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
        }
        break;
    case WD::PDT_ULLVec2:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            //创建父属性栏
            p = _pStringMgr ->addProperty(tPtyName);
            //创建子属性栏
            subPty1 = pTMgr->addProperty(QVariant::LongLong,subPtyName1);
            subPty2 = pTMgr->addProperty(QVariant::LongLong,subPtyName2);
            //添加层级关系
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
        }
        break;
    case WD::PDT_FVec2:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
            //创建父属性栏
            p = _pStringMgr ->addProperty(tPtyName);
            //创建子属性栏
            subPty1 = pTMgr->addProperty(subPtyName1);
            subPty2 = pTMgr->addProperty(subPtyName2);
            //添加层级关系
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
        }
        break;
    case WD::PDT_DVec2:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
            //创建父属性栏
            p = _pStringMgr ->addProperty(tPtyName);
            //创建子属性栏
            subPty1 = pTMgr->addProperty(subPtyName1);
            subPty2 = pTMgr->addProperty(subPtyName2);
            //添加层级关系
            p->addSubProperty(subPty1);
            p->addSubProperty(subPty2);
        }
        break;
    default:
        break;
    }
    //将子属性栏对象存入定长数组
    _pSubProperty = {subPty1,subPty2};
    return p;
}

bool Vec2Type::updateSubProperty(QString rootValue)


{
    QStringList subValueList = rootValue.split(" ");
    //子属性栏
    QtProperty* sub1 = _pSubProperty.at(0);
    QtProperty* sub2 = _pSubProperty.at(1);
    //子属性栏需要更新的值
    QString subValue1 = subValueList.at(0);
    QString subValue2 = subValueList.at(1);

    WD::WDPropertyDataType type = _pPty->type();
    switch (type)
    {
    case WD::PDT_UCVec2:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            unsigned char value1 = *subValue1.toLocal8Bit().data();
            unsigned char value2 = *subValue2.toLocal8Bit().data();
            //设置子属性栏显示值
            pTMgr->setValue(sub1, value1);
            pTMgr->setValue(sub2, value2);
            return true;
        }
        break;
    case WD::PDT_IVec2:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
            //设置子属性栏显示值
            pTMgr->setValue(sub1, subValue1.toInt());
            pTMgr->setValue(sub2, subValue2.toInt());
            return true;
        }
        break;
    case WD::PDT_UIVec2:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
            //设置子属性栏显示值
            pTMgr->setValue(sub1, static_cast<unsigned int>(subValue1.toInt()));
            pTMgr->setValue(sub2, static_cast<unsigned int>(subValue2.toInt()));
            return true;
        }
        break;
    case WD::PDT_LLVec2:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            //设置子属性栏显示值
            pTMgr->setValue(sub1, subValue1.toLongLong());
            pTMgr->setValue(sub2, subValue1.toLongLong());
            return true;
        }
        break;
    case WD::PDT_ULLVec2:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            //设置子属性栏显示值
            pTMgr->setValue(sub1, subValue1.toLongLong());
            pTMgr->setValue(sub2, subValue2.toLongLong());
            return true;
        }
        break;
    case WD::PDT_FVec2:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
            pTMgr->setValue(sub1, static_cast<float>(subValue1.toDouble()));
            pTMgr->setValue(sub2, static_cast<float>(subValue2.toDouble()));
            return true;
        }
        break;
    case WD::PDT_DVec2:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
            pTMgr->setValue(sub1, subValue1.toDouble());
            pTMgr->setValue(sub2, subValue2.toDouble());
            return true;
        }
        break;
    default:
        return false;
        break;
    }
    return true;
}

bool Vec2Type::updateRootProperty()

{
    QString rootValue = _pStringMgr->value(_pRootProperty);
    QStringList rootValueList = rootValue.split(" ");
    //子属性栏
    QtProperty* sub1 = _pSubProperty.at(0);
    QtProperty* sub2 = _pSubProperty.at(1);

    WD::WDPropertyDataType type = _pPty->type();
    switch (type)
    {
    case WD::PDT_UCVec2:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            uchar subValue1 = pTMgr->value(sub1).toChar().toLatin1();
            uchar subValue2 = pTMgr->value(sub2).toChar().toLatin1();
            
            rootValue = QVariant(subValue1).toString() 
                + " " + QVariant(subValue2).toString();
        }
        break;
    case WD::PDT_IVec2:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
            int subValue1 = pTMgr->value(sub1);
            int subValue2 = pTMgr->value(sub2);

            rootValue = QString::number(subValue1) 
                + " " + QString::number(subValue2);
        }
        break;
    case WD::PDT_UIVec2:
        {
            QtIntPropertyManager* pTMgr = static_cast<QtIntPropertyManager*>(_pSubMgr);
            unsigned int subValue1 = pTMgr->value(sub1);
            unsigned int subValue2 = pTMgr->value(sub2);

            rootValue = QString::number(subValue1) 
                + " " + QString::number(subValue2);
        }
        break;
    case WD::PDT_LLVec2:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            long long subValue1 = pTMgr->value(sub1).toLongLong();
            long long subValue2 = pTMgr->value(sub2).toLongLong();
            
            rootValue = QString::number(subValue1) 
                + " " + QString::number(subValue2);
        }
        break;
    case WD::PDT_ULLVec2:
        {
            QtVariantPropertyManager* pTMgr = static_cast<QtVariantPropertyManager*>(_pSubMgr);
            unsigned long long subValue1 = pTMgr->value(sub1).toLongLong();
            unsigned long long subValue2 = pTMgr->value(sub2).toLongLong();
            
            rootValue = QString::number(subValue1) 
                + " " + QString::number(subValue2);
        }
        break;
    case WD::PDT_FVec2:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
            float subValue1 = pTMgr->value(sub1);
            float subValue2 = pTMgr->value(sub2);

            rootValue = QString::number(subValue1,'f',2) 
                + " " + QString::number(subValue2,'f',2);
        }
        break;
    case WD::PDT_DVec2:
        {
            QtDoublePropertyManager* pTMgr = static_cast<QtDoublePropertyManager*>(_pSubMgr);
            double subValue1 = pTMgr->value(sub1);
            double subValue2 = pTMgr->value(sub2);

            rootValue = QString::number(subValue1,'f',2) 
                + " " + QString::number(subValue2,'f',2);
        }
        break;
    default:
        return false;
        break;
    }
    _pStringMgr->setValue(_pRootProperty,rootValue);
    return true;
}
