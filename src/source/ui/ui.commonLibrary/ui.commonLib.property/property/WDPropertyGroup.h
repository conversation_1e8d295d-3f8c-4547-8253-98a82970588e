#pragma once

#include "WDPropertyBaseType.h"
#include "WDPropertyBaseTypeVector.h"
#include "WDPropertyMathType.h"
#include "WDPropertyMathTypeVector.h"

WD_NAMESPACE_BEGIN

#define WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE(SubPropertyClassName, FunctionExtName)\
    SubPropertyClassName::SharedPtr addProperty##FunctionExtName(const std::string& name = ""\
        , const SubPropertyClassName::TValueType& value = SubPropertyClassName::TValueType(0)\
        , bool bEditable = true)\
{\
    if (this->findProperty(name) != nullptr)\
        return nullptr;\
    SubPropertyClassName::SharedPtr p = SubPropertyClassName::MakeShared(name, value, bEditable);\
    _propertys.push_back(p);\
    return p;\
}\

#define WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(SubPropertyClassName, FunctionExtName)\
    bool propertyToValue##FunctionExtName(const std::string& name\
        , SubPropertyClassName::TValueType& value)\
{\
    return this->propertyToValue<SubPropertyClassName>(name, value);\
}\

#define WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_EXTENSION(SubPropertyClassName, FunctionExtName)\
    SubPropertyClassName::SharedPtr addProperty##FunctionExtName(const std::string& name = ""\
        , const SubPropertyClassName::TValueType& value = SubPropertyClassName::TValueType()\
        , bool bEditable = true)\
{\
    if (this->findProperty(name) != nullptr)\
        return nullptr;\
    SubPropertyClassName::SharedPtr p = SubPropertyClassName::MakeShared(name, value, bEditable);\
    _propertys.push_back(p);\
    return p;\
}\

#define WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_VECTOR(SubPropertyClassName, FunctionExtName)\
    SubPropertyClassName::SharedPtr addProperty##FunctionExtName(const std::string& name = ""\
        , const SubPropertyClassName::TValueType& value = SubPropertyClassName::TValueType()\
        , bool bEditable = true)\
{\
    if (this->findProperty(name) != nullptr)\
        return nullptr;\
    SubPropertyClassName::SharedPtr p = SubPropertyClassName::MakeShared(name, value, bEditable);\
    _propertys.push_back(p);\
    return p;\
}\


#define WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_RANGE(SubPropertyClassName, FunctionExtName)\
    SubPropertyClassName::SharedPtr addProperty##FunctionExtName(const SubPropertyClassName::TValueType& min\
        , const SubPropertyClassName::TValueType& max\
        , const std::string& name = ""\
        , const SubPropertyClassName::TValueType& value = SubPropertyClassName::TValueType(0)\
        , bool bEditable = true\
        , const SubPropertyClassName::TValueType& step = SubPropertyClassName::SValueStep)\
{\
    if (this->findProperty(name) != nullptr)\
        return nullptr;\
    SubPropertyClassName::SharedPtr p = SubPropertyClassName::MakeShared(min, max, name, value, bEditable, step);\
    _propertys.push_back(p);\
    return p;\
}\

#define WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(SubPropertyClassName, FunctionExtName)\
    SubPropertyClassName::SharedPtr addProperty##FunctionExtName(const std::string& name = ""\
        , const SubPropertyClassName::TValueType& value = SubPropertyClassName::TValueType()\
        , bool bEditable = true)\
{\
    if (this->findProperty(name) != nullptr)\
        return nullptr;\
    SubPropertyClassName::SharedPtr p = SubPropertyClassName::MakeShared(name, value, bEditable);\
    _propertys.push_back(p);\
    return p;\
}\

#define WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(SubPropertyClassName, FunctionExtName)\
    SubPropertyClassName::SharedPtr addProperty##FunctionExtName(const std::string& name = ""\
        , const SubPropertyClassName::TValueType& value = SubPropertyClassName::TValueType()\
        , bool bEditable = true)\
{\
    if (this->findProperty(name) != nullptr)\
        return nullptr;\
    SubPropertyClassName::SharedPtr p = SubPropertyClassName::MakeShared(name, value, bEditable);\
    _propertys.push_back(p);\
    return p;\
}\

/**
* @brief 属性组对象
*/

WD_DECL_CLASS_UUID(WDPropertyGroup,"D045D224-66BC-475C-B0A3-4628635D5130");

class WDPropertyGroup :public WDProperty
{
    WD_DECL_OBJECT(WDPropertyGroup)
    WD_DECL_PROPERTY_ENUNM_DATA_TYPE(WDPropertyDataType::PDT_Group)
public:
    /**
    * @brief 属性对象数组
    */
    using Propertys = std::vector<WDProperty::SharedPtr>;
public:
    WDPropertyGroup(const std::string& name = "");
    ~WDPropertyGroup();
private:
    Propertys       _propertys;
public:
    /**
    * @brief 是否为空，即不包含任何属性对象
    */
    inline bool empty() const;
    /**
    * @brief 根据属性名称查找属性对象
    */
    WDProperty::SharedPtr findProperty(const std::string& name) const;
    /**
    * @brief 查找对应名称的属性并尝试转换到指定的子类类型
    * @return 如果对应名称的属性不存在或非指定类型，则返回nullptr
    */
    template <class WDPropertySubClass>
    inline typename WDPropertySubClass::SharedPtr findProperty(const std::string& name) const;
    /**
     * @brief 查找对应名称的属性并将值赋值给指定引用
     * @tparam WDPropertySubClass 属性子类类型
     * @tparam T 属性值类型（通常省略，由引用类型自动推导）
     * @param name 属性名称
     * @param value 属性值引用
     * @return 查找并引用成功返回true，否则返回false
    */
    template <class WDPropertySubClass, typename T>
    inline bool propertyToValue(const std::string& name, T& value) const;
    /**
    * @brief 是否包含对应名称的属性
    */
    inline bool contains(const std::string& name) const;
    /**
    * @brief 添加属性对象，如果当前组中存在对应名称的属性或者相同的属性对象，则添加失败
    * @return 是否添加成功
    */
    bool addProperty(WDProperty::SharedPtr pProperty);
    /**
    * @brief 指定属性类型以及名称添加属性对象
    * @param type 属性对象类型
    * @param pname 属性对象名称
    * @return 属性对象指针
    */
    WDProperty::SharedPtr addProperty(WDPropertyDataType type, const std::string& name);
    /**
    * @brief 根据名称移除属性对象
    */
    void removeProperty(const std::string& name);
    /**
    * @brief 移除属性对象
    */
    inline void removeProperty(WDProperty::SharedPtr pProperty);
    /**
    * @brief 获取所有属性
    */
    inline const Propertys& propertys() const;
    /**
    * @brief 清除所有属性
    */
    inline void clear();
public:
    // 添加 group 类型属性
    WDPropertyGroup::SharedPtr addPropertyGroup(const std::string& name);
    // 添加 WDPropertyBool 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyBool, Bool)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyBool, Bool)
    // 添加 WDPropertyChar 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyChar, Char)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyChar, Char)
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_RANGE(WDPropertyChar, Char)
    // 添加 WDPropertyUChar 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyUChar, UChar)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyUChar, UChar)
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_RANGE(WDPropertyUChar, UChar)
    // 添加 WDPropertyShort 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyShort, Short)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyShort, Short)
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_RANGE(WDPropertyShort, Short)
    // 添加 WDPropertyUShort 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyUShort, UShort)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyUShort, UShort)
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_RANGE(WDPropertyUShort, UShort)
    // 添加 WDPropertyInt 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyInt, Int)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyInt, Int)
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_RANGE(WDPropertyInt, Int)
    // 添加 WDPropertyUInt 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyUInt, UInt)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyUInt, UInt)
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_RANGE(WDPropertyUInt, UInt)
    // 添加 WDPropertyLong 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyLong, Long)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyLong, Long)
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_RANGE(WDPropertyLong, Long)
    // 添加 WDPropertyULong 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyULong, ULong)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyULong, ULong)
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_RANGE(WDPropertyULong, ULong)
    // 添加 WDPropertyLongLong 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyLongLong, LongLong)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyLongLong, LongLong)
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_RANGE(WDPropertyLongLong, LongLong)
    // 添加 WDPropertyULongLong 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyULongLong, ULongLong)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyULongLong, ULongLong)
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_RANGE(WDPropertyULongLong, ULongLong)
    // 添加 WDPropertyFloat 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyFloat, Float)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyFloat, Float)
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_RANGE(WDPropertyFloat, Float)
    // 添加 WDPropertyDouble 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyDouble, Double)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyDouble, Double)
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_RANGE(WDPropertyDouble, Double)
    // 添加 WDPropertyLDouble 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyLDouble, LDouble)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyLDouble, LDouble)
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_RANGE(WDPropertyLDouble, LDouble)
    // 添加 WDPropertyString 类型属性
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyString, String)
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_EXTENSION(WDPropertyString, String)
    // 添加 WDPropertyGuid 类型属性
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyGuid, Guid)
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_EXTENSION(WDPropertyGuid, Guid)
    
    // 添加 WDPropertyBVec2 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyUCVec2, UCVec2)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyUCVec2, UCVec2)
    // 添加 WDPropertyIVec2 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyIVec2, IVec2)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyIVec2, IVec2)
    // 添加 WDPropertyUIVec2 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyUIVec2, UIVec2)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyUIVec2, UIVec2)
    // 添加 WDPropertyFVec2 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyFVec2, FVec2)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyFVec2, FVec2)
    // 添加 WDPropertyDVec2 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyDVec2, DVec2)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyDVec2, DVec2)
    // 添加 WDPropertyLLVec2 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyLLVec2, LLVec2)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyLLVec2, LLVec2)
    // 添加 WDPropertyULLVec2 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyULLVec2, ULLVec2)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyULLVec2, ULLVec2)
    
    // 添加 WDPropertyBVec3 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyUCVec3, UCVec3)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyUCVec3, UCVec3)
    // 添加 WDPropertyIVec3 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyIVec3, IVec3)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyIVec3, IVec3)
    // 添加 WDPropertyUIVec3 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyUIVec3, UIVec3)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyUIVec3, UIVec3)
    // 添加 WDPropertyFVec3 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyFVec3, FVec3)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyFVec3, FVec3)
    // 添加 WDPropertyDVec3 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyDVec3, DVec3)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyDVec3, DVec3)
    // 添加 WDPropertyLLVec3 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyLLVec3, LLVec3)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyLLVec3, LLVec3)
    // 添加 WDPropertyULLVec3 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyULLVec3, ULLVec3)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyULLVec3, ULLVec3)
        
    // 添加 WDPropertyBVec4 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyUCVec4, UCVec4)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyUCVec4, UCVec4)
    // 添加 WDPropertyIVec4 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyIVec4, IVec4)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyIVec4, IVec4)
    // 添加 WDPropertyUIVec4 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyUIVec4, UIVec4)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyUIVec4, UIVec4)
    // 添加 WDPropertyFVec4 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyFVec4, FVec4)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyFVec4, FVec4)
    // 添加 WDPropertyDVec4 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyDVec4, DVec4)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyDVec4, DVec4)
    // 添加 WDPropertyLLVec4 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyLLVec4, LLVec4)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyLLVec4, LLVec4)
    // 添加 WDPropertyULLVec4 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyULLVec4, ULLVec4)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyULLVec4, ULLVec4)

    // 添加 WDPropertyFQuat 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyFQuat, FQuat)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyFQuat, FQuat)
    // 添加 WDPropertyDQuat 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyDQuat, DQuat)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyDQuat, DQuat)

    // 添加 WDPropertyFMat2 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyFMat2, FMat2)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyFMat2, FMat2)
    // 添加 WDPropertyDMat2 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyDMat2, DMat2)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyDMat2, DMat2)
        
    // 添加 WDPropertyFMat3 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyFMat3, FMat3)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyFMat3, FMat3)
    // 添加 WDPropertyDMat3 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyDMat3, DMat3)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyDMat3, DMat3)
        
    // 添加 WDPropertyFMat4 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyFMat4, FMat4)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyFMat4, FMat4)
    // 添加 WDPropertyDMat4 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyDMat4, DMat4)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyDMat4, DMat4)

    // 添加 WDPropertyFEuler 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyFEuler, FEuler)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyFEuler, FEuler)
    // 添加 WDPropertyDEuler 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyDEuler, DEuler)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyDEuler, DEuler)

    // 添加 WDPropertyFAabb2 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyFAabb2, FAabb2)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyFAabb2, FAabb2)
    // 添加 WDPropertyDAabb2 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyDAabb2, DAabb2)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyDAabb2, DAabb2)

    // 添加 WDPropertyFAabb3 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyFAabb3, FAabb3)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyFAabb3, FAabb3)
    // 添加 WDPropertyDAabb3 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyDAabb3, DAabb3)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyDAabb3, DAabb3)
        
    // 添加 WDPropertyColor 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE(WDPropertyColor, Color)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyColor, Color)

        
    // 添加 WDPropertyBoolVector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_VECTOR(WDPropertyBoolVector, BoolVector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyBoolVector, BoolVector)
    // 添加 WDPropertyCharVector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_VECTOR(WDPropertyCharVector, CharVector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyCharVector, CharVector)
    // 添加 WDPropertyUCharVector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_VECTOR(WDPropertyUCharVector, UCharVector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyUCharVector, UCharVector)
    // 添加 WDPropertyShortVector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_VECTOR(WDPropertyShortVector, ShortVector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyShortVector, ShortVector)
    // 添加 WDPropertyUShortVector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_VECTOR(WDPropertyUShortVector, UShortVector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyUShortVector, UShortVector)
    // 添加 WDPropertyIntVector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_VECTOR(WDPropertyIntVector, IntVector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyIntVector, IntVector)
    // 添加 WDPropertyUIntVector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_VECTOR(WDPropertyUIntVector, UIntVector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyUIntVector, UIntVector)
    // 添加 WDPropertyLongLongVector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_VECTOR(WDPropertyLongLongVector, LongLongVector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyLongLongVector, LongLongVector)
    // 添加 WDPropertyULongLongVector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_VECTOR(WDPropertyULongLongVector, ULongLongVector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyULongLongVector, ULongLongVector)
    // 添加 WDPropertyFloatVector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_VECTOR(WDPropertyFloatVector, FloatVector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyFloatVector, FloatVector)
    // 添加 WDPropertyDoubleVector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_VECTOR(WDPropertyDoubleVector, DoubleVector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyDoubleVector, DoubleVector)
    // 添加 WDPropertyLDoubleVector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_VECTOR(WDPropertyLDoubleVector, LDoubleVector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyLDoubleVector, LDoubleVector)
    // 添加 WDPropertyStringVector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_VECTOR(WDPropertyStringVector, StringVector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyStringVector, StringVector)
    // 添加 WDPropertyGuidVector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_VECTOR(WDPropertyGuidVector, GuidVector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyGuidVector, GuidVector)

    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_BASE_TYPE_EXTENSION(WDPropertyStringList, StringList)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyStringList, StringList)

        
    // 添加 WDPropertyUCVec2Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyUCVec2Vector, UCVec2Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyUCVec2Vector, UCVec2Vector)
    // 添加 WDPropertyIVec2Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyIVec2Vector, IVec2Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyIVec2Vector, IVec2Vector)
    // 添加 WDPropertyUIVec2Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyUIVec2Vector, UIVec2Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyUIVec2Vector, UIVec2Vector)
    // 添加 WDPropertyLLVec2Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyLLVec2Vector, LLVec2Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyLLVec2Vector, LLVec2Vector)
    // 添加 WDPropertyULLVec2Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyULLVec2Vector, ULLVec2Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyULLVec2Vector, ULLVec2Vector)
    // 添加 WDPropertyFVec2Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyFVec2Vector, FVec2Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyFVec2Vector, FVec2Vector)
    // 添加 WDPropertyDVec2Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyDVec2Vector, DVec2Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyDVec2Vector, DVec2Vector)
        
    // 添加 WDPropertyUCVec3Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyUCVec3Vector, UCVec3Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyUCVec3Vector, UCVec3Vector)
    // 添加 WDPropertyIVec3Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyIVec3Vector, IVec3Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyIVec3Vector, IVec3Vector)
    // 添加 WDPropertyUIVec3Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyUIVec3Vector, UIVec3Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyUIVec3Vector, UIVec3Vector)
    // 添加 WDPropertyLLVec3Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyLLVec3Vector, LLVec3Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyLLVec3Vector, LLVec3Vector)
    // 添加 WDPropertyULLVec3Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyULLVec3Vector, ULLVec3Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyULLVec3Vector, ULLVec3Vector)
    // 添加 WDPropertyFVec3Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyFVec3Vector, FVec3Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyFVec3Vector, FVec3Vector)
    // 添加 WDPropertyDVec3Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyDVec3Vector, DVec3Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyDVec3Vector, DVec3Vector)
      
    // 添加 WDPropertyUCVec4Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyUCVec4Vector, UCVec4Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyUCVec4Vector, UCVec4Vector)
    // 添加 WDPropertyIVec4Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyIVec4Vector, IVec4Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyIVec4Vector, IVec4Vector)
    // 添加 WDPropertyUIVec4Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyUIVec4Vector, UIVec4Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyUIVec4Vector, UIVec4Vector)
    // 添加 WDPropertyLLVec4Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyLLVec4Vector, LLVec4Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyLLVec4Vector, LLVec4Vector)
    // 添加 WDPropertyULLVec4Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyULLVec4Vector, ULLVec4Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyULLVec4Vector, ULLVec4Vector)
    // 添加 WDPropertyFVec4Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyFVec4Vector, FVec4Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyFVec4Vector, FVec4Vector)
    // 添加 WDPropertyDVec4Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyDVec4Vector, DVec4Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyDVec4Vector, DVec4Vector)

    // 添加 WDPropertyFQuatVector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyFQuatVector, FQuatVector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyFQuatVector, FQuatVector)
    // 添加 WDPropertyDQuatVector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyDQuatVector, DQuatVector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyDQuatVector, DQuatVector)

    // 添加 WDPropertyFMat2Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyFMat2Vector, FMat2Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyFMat2Vector, FMat2Vector)
    // 添加 WDPropertyDMat2Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyDMat2Vector, DMat2Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyDMat2Vector, DMat2Vector)

    // 添加 WDPropertyFMat3Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyFMat3Vector, FMat3Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyFMat3Vector, FMat3Vector)
    // 添加 WDPropertyDMat3Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyDMat3Vector, DMat3Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyDMat3Vector, DMat3Vector)

    // 添加 WDPropertyFMat4Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyFMat4Vector, FMat4Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyFMat4Vector, FMat4Vector)
    // 添加 WDPropertyDMat4Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyDMat4Vector, DMat4Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyDMat4Vector, DMat4Vector)

    // 添加 WDPropertyFEulerVector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyFEulerVector, FEulerVector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyFEulerVector, FEulerVector)
    // 添加 WDPropertyDEulerVector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyDEulerVector, DEulerVector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyDEulerVector, DEulerVector)

    // 添加 WDPropertyFAabb2Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyFAabb2Vector, FAabb2Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyFAabb2Vector, FAabb2Vector)
    // 添加 WDPropertyDAabb2Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyDAabb2Vector, DAabb2Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyDAabb2Vector, DAabb2Vector)

    // 添加 WDPropertyFAabb3Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyFAabb2Vector, FAabb3Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyFAabb2Vector, FAabb3Vector)
    // 添加 WDPropertyDAabb3Vector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyDAabb2Vector, DAabb3Vector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyDAabb2Vector, DAabb3Vector)
    
    // 添加 WDPropertyColorVector 类型属性
    WD_DECL_PROPERTY_GROUP_ADD_SUB_PROPERTY_FUNCTION_MATH_TYPE_VECTOR(WDPropertyColorVector, ColorVector)
    WD_DECL_PROPERTY_GROUP_VALUE_SUB_PROPERTY_FUNCTION_BASE_TYPE(WDPropertyColorVector, ColorVector)
public:
    /**
    * @brief 属性值转换为字符串
    */
    virtual inline std::string valueToString() const;
    
    /**
    * @brief 字符串转换为属性值
    */
    virtual inline void valueFromString(const std::string& str);


public:
    /**
    * @brief 从源对象拷贝数据到当前对象
    *   子类可重写
    */
    virtual void copy(const WDObject* pSrcObject) override;
    /**
    * @brief 使用当前对象克隆出一个对象
    *   子类可重写
    */
    virtual WDObject::SharedPtr clone() const override;
};

WD_NAMESPACE_END

#include "WDPropertyGroup.inl"