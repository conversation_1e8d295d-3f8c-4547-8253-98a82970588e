#include    "WDPropertyGroup.h"
#include    "core/WDCore.h"

WD_NAMESPACE_BEGIN


WDPropertyGroup::WDPropertyGroup(const std::string& name)
    :WDProperty(SPropertyDataType, name, false)
{
}
WDPropertyGroup::~WDPropertyGroup() 
{
}

WDProperty::SharedPtr WDPropertyGroup::findProperty(const std::string& name) const
{
    for (auto itr = _propertys.begin(); itr != _propertys.end(); ++itr)
    {
        if ((*itr)->name() == name)
            return (*itr);
    }
    return nullptr;
}

bool WDPropertyGroup::addProperty(WDProperty::SharedPtr pProperty)
{
    if (pProperty == nullptr)
        return false;
    for (size_t i = 0; i < _propertys.size(); ++i)
    {
        if (_propertys[i] == pProperty || _propertys[i]->name() == pProperty->name())
            return false;
    }
    _propertys.push_back(pProperty);
    return true;
}

WDProperty::SharedPtr WDPropertyGroup::addProperty(WDPropertyDataType type, const std::string& name)
{
    switch (type)
    {
    case WD::PDT_Null:
        break;
    case WD::PDT_Bool:
        return this->addPropertyBool(name);
        break;
    case WD::PDT_Char:
        return this->addPropertyChar(name);
        break;
    case WD::PDT_UChar:
        return this->addPropertyUChar(name);
        break;
    case WD::PDT_Short:
        return this->addPropertyShort(name);
        break;
    case WD::PDT_UShort:
        return this->addPropertyUShort(name);
        break;
    case WD::PDT_Int:
        return this->addPropertyInt(name);
        break;
    case WD::PDT_UInt:
        return this->addPropertyUInt(name);
        break;
    case WD::PDT_Long:
        return this->addPropertyLong(name);
        break;
    case WD::PDT_ULong:
        return this->addPropertyULong(name);
        break;
    case WD::PDT_LongLong:
        return this->addPropertyLongLong(name);
        break;
    case WD::PDT_ULongLong:
        return this->addPropertyULongLong(name);
        break;
    case WD::PDT_Float:
        return this->addPropertyFloat(name);
        break;
    case WD::PDT_Double:
        return this->addPropertyDouble(name);
        break;
    case WD::PDT_LDouble:
        return this->addPropertyLDouble(name);
        break;
    case WD::PDT_String:
        return this->addPropertyString(name);
        break;

    case WD::PDT_UCVec2:
        return this->addPropertyUCVec2(name);
        break;
    case WD::PDT_IVec2:
        return this->addPropertyIVec2(name);
        break;
    case WD::PDT_UIVec2:
        return this->addPropertyUIVec2(name);
        break;
    case WD::PDT_LLVec2:
        return this->addPropertyLLVec2(name);
        break;
    case WD::PDT_ULLVec2:
        return this->addPropertyULLVec2(name);
        break;
    case WD::PDT_FVec2:
        return this->addPropertyFVec2(name);
        break;
    case WD::PDT_DVec2:
        return this->addPropertyDVec2(name);
        break;

    case WD::PDT_UCVec3:
        return this->addPropertyUCVec3(name);
        break;
    case WD::PDT_IVec3:
        return this->addPropertyIVec3(name);
        break;
    case WD::PDT_UIVec3:
        return this->addPropertyUIVec3(name);
        break;
    case WD::PDT_LLVec3:
        return this->addPropertyLLVec3(name);
        break;
    case WD::PDT_ULLVec3:
        return this->addPropertyULLVec3(name);
        break;
    case WD::PDT_FVec3:
        return this->addPropertyFVec3(name);
        break;
    case WD::PDT_DVec3:
        return this->addPropertyDVec3(name);
        break;

    case WD::PDT_UCVec4:
        return this->addPropertyUCVec4(name);
        break;
    case WD::PDT_IVec4:
        return this->addPropertyIVec4(name);
        break;
    case WD::PDT_UIVec4:
        return this->addPropertyUIVec4(name);
        break;
    case WD::PDT_LLVec4:
        return this->addPropertyLLVec4(name);
        break;
    case WD::PDT_ULLVec4:
        return this->addPropertyULLVec4(name);
        break;
    case WD::PDT_FVec4:
        return this->addPropertyFVec4(name);
        break;
    case WD::PDT_DVec4:
        return this->addPropertyDVec4(name);
        break;

    case WD::PDT_FQuat:
        return this->addPropertyFQuat(name);
        break;
    case WD::PDT_DQuat:
        return this->addPropertyDQuat(name);
        break;

    case WD::PDT_FMat2:
        return this->addPropertyFMat2(name);
        break;
    case WD::PDT_DMat2:
        return this->addPropertyDMat2(name);
        break;
    case WD::PDT_FMat3:
        return this->addPropertyFMat3(name);
        break;
    case WD::PDT_DMat3:
        return this->addPropertyDMat3(name);
        break;
    case WD::PDT_FMat4:
        return this->addPropertyFMat4(name);
        break;
    case WD::PDT_DMat4:
        return this->addPropertyDMat4(name);
        break;

    case WD::PDT_FEuler:
        return this->addPropertyFEuler(name);
        break;
    case WD::PDT_DEuler:
        return this->addPropertyDEuler(name);
        break;

    case WD::PDT_FAabb2:
        return this->addPropertyFAabb2(name);
        break;
    case WD::PDT_DAabb2:
        return this->addPropertyDAabb2(name);
        break;
    case WD::PDT_FAabb3:
        return this->addPropertyFAabb3(name);
        break;
    case WD::PDT_DAabb3:
        return this->addPropertyDAabb3(name);
        break;

    case WD::PDT_Color:
        return this->addPropertyColor(name);
        break;
    case WD::PDT_Guid:
        return this->addPropertyGuid(name);
        break;
    case WD::PDT_Group:
        return this->addPropertyGroup(name);
        break;

    case WD::PDT_BoolVector:
        return this->addPropertyBoolVector(name);
        break;
    case WD::PDT_CharVector:
        return this->addPropertyCharVector(name);
        break;
    case WD::PDT_UCharVector:
        return this->addPropertyUCharVector(name);
        break;
    case WD::PDT_ShortVector:
        return this->addPropertyShortVector(name);
        break;
    case WD::PDT_UShortVector:
        return this->addPropertyUShortVector(name);
        break;
    case WD::PDT_IntVector:
        return this->addPropertyIntVector(name);
        break;
    case WD::PDT_UIntVector:
        return this->addPropertyUIntVector(name);
        break;
    case WD::PDT_LongLongVector:
        return this->addPropertyLongLongVector(name);
        break;
    case WD::PDT_ULongLongVector:
        return this->addPropertyULongLongVector(name);
        break;
    case WD::PDT_FloatVector:
        return this->addPropertyFloatVector(name);
        break;
    case WD::PDT_DoubleVector:
        return this->addPropertyDoubleVector(name);
        break;
    case WD::PDT_LDoubleVector:
        return this->addPropertyLDoubleVector(name);
        break;
    case WD::PDT_StringVector:
        return this->addPropertyStringVector(name);
        break;
    case WD::PDT_StringList:
        return this->addPropertyStringList(name);
        break;

    case WD::PDT_UCVec2Vector:
        return this->addPropertyUCVec2Vector(name);
        break;
    case WD::PDT_IVec2Vector:
        return this->addPropertyIVec2Vector(name);
        break;
    case WD::PDT_UIVec2Vector:
        return this->addPropertyUIVec2Vector(name);
        break;
    case WD::PDT_LLVec2Vector:
        return this->addPropertyLLVec2Vector(name);
        break;
    case WD::PDT_ULLVec2Vector:
        return this->addPropertyULLVec2Vector(name);
        break;
    case WD::PDT_FVec2Vector:
        return this->addPropertyFVec2Vector(name);
        break;
    case WD::PDT_DVec2Vector:
        return this->addPropertyDVec2Vector(name);
        break;

    case WD::PDT_UCVec3Vector:
        return this->addPropertyUCVec3Vector(name);
        break;
    case WD::PDT_IVec3Vector:
        return this->addPropertyIVec3Vector(name);
        break;
    case WD::PDT_UIVec3Vector:
        return this->addPropertyUIVec3Vector(name);
        break;
    case WD::PDT_LLVec3Vector:
        return this->addPropertyLLVec3Vector(name);
        break;
    case WD::PDT_ULLVec3Vector:
        return this->addPropertyULLVec3Vector(name);
        break;
    case WD::PDT_FVec3Vector:
        return this->addPropertyFVec3Vector(name);
        break;
    case WD::PDT_DVec3Vector:
        return this->addPropertyDVec3Vector(name);
        break;

    case WD::PDT_UCVec4Vector:
        return this->addPropertyUCVec4Vector(name);
        break;
    case WD::PDT_IVec4Vector:
        return this->addPropertyIVec4Vector(name);
        break;
    case WD::PDT_UIVec4Vector:
        return this->addPropertyUIVec4Vector(name);
        break;
    case WD::PDT_LLVec4Vector:
        return this->addPropertyLLVec4Vector(name);
        break;
    case WD::PDT_ULLVec4Vector:
        return this->addPropertyULLVec4Vector(name);
        break;
    case WD::PDT_FVec4Vector:
        return this->addPropertyFVec4Vector(name);
        break;
    case WD::PDT_DVec4Vector:
        return this->addPropertyDVec4Vector(name);
        break;

    case WD::PDT_FQuatVector:
        return this->addPropertyFQuatVector(name);
        break;
    case WD::PDT_DQuatVector:
        return this->addPropertyDQuatVector(name);
        break;

    case WD::PDT_FMat2Vector:
        return this->addPropertyFMat2Vector(name);
        break;
    case WD::PDT_DMat2Vector:
        return this->addPropertyDMat2Vector(name);
        break;
    case WD::PDT_FMat3Vector:
        return this->addPropertyFMat3Vector(name);
        break;
    case WD::PDT_DMat3Vector:
        return this->addPropertyDMat3Vector(name);
        break;
    case WD::PDT_FMat4Vector:
        return this->addPropertyFMat4Vector(name);
        break;
    case WD::PDT_DMat4Vector:
        return this->addPropertyDMat4Vector(name);
        break;

    case WD::PDT_FEulerVector:
        return this->addPropertyFEulerVector(name);
        break;
    case WD::PDT_DEulerVector:
        return this->addPropertyDEulerVector(name);
        break;

    case WD::PDT_FAabb2Vector:
        return this->addPropertyFAabb2Vector(name);
        break;
    case WD::PDT_DAabb2Vector:
        return this->addPropertyDAabb2Vector(name);
        break;
    case WD::PDT_FAabb3Vector:
        return this->addPropertyFAabb3Vector(name);
        break;
    case WD::PDT_DAabb3Vector:
        return this->addPropertyDAabb3Vector(name);
        break;
    case WD::PDT_ColorVector:
        return this->addPropertyColorVector(name);
        break;
    case WD::PDT_User:
        return this->addPropertyString(name);
        break;
    default:
        break;
    }

    return nullptr;
}

void WDPropertyGroup::removeProperty(const std::string& name)
{
    for (auto itr = _propertys.begin(); itr != _propertys.end(); ++itr)
    {
        if ((*itr)->name() == name)
        {
            itr = _propertys.erase(itr);
            break;
        }
    }
}

WDPropertyGroup::SharedPtr WDPropertyGroup::addPropertyGroup(const std::string& name)
{
    if (this->findProperty(name) != nullptr)
        return nullptr;
    auto p = WDPropertyGroup::MakeShared();
    p->setName(name);
    _propertys.push_back(p);
    return p;
}

void WDPropertyGroup::copy(const WDObject* pSrcObject)
{
    const WDPropertyGroup* pSrc = dynamic_cast<const WDPropertyGroup*>(pSrcObject);
    if (pSrc == nullptr)
        return ;

    WDProperty::copy(pSrcObject);
    
    this->_propertys.clear();
    const Propertys& srcPtys = pSrc->propertys();
    for (size_t i = 0; i < srcPtys.size(); ++i)
    {
        WDProperty::SharedPtr pNewPty = srcPtys[i]->cloneT<WDProperty>();
        this->addProperty(pNewPty);
    }
}
WDObject::SharedPtr WDPropertyGroup::clone() const
{
    auto p = WDPropertyGroup::MakeShared();
    p->copy(this);
    return p;
}
WD_NAMESPACE_END