#pragma once
#include "TypeAttributeBase.h"

class TypeAttributeElementVector: public TypeAttributeBase
{
private:
    // 所有引用属性的根，只读, 不支持修改
    QtStringPropertyManager*    _pStringMgrOnlyRead;
    // 每一项引用属性
    QtStringPropertyManager*    _pStringMgr;
    std::vector<QtProperty*>    _pSubProperties;
public:
    TypeAttributeElementVector(AttributeWidgetBase& ownWidget
        , const WD::WDBMAttrDesc& attrDesc
        , QtStringPropertyManager* pStringMgrOnlyRead
        , QtStringPropertyManager* pStringMgr
        , const FuncNameTs& nameTs = FuncNameTs());
    ~TypeAttributeElementVector(){};
public:
    virtual bool setCurrentValue(const WD::WDBMAttrValue& value) override;
    virtual WD::WDBMAttrValue getCurrentValue(bool* bOk = nullptr) const override;
    virtual bool contains(QtProperty* pPty) const override;
    int at(QtProperty* pPty);
protected:
    /**
    * @brief 更新所有QtProperty显示
    */
    virtual bool updateShowP(QtProperty* pPty) override;
private:
    // 使用数组长度初始化Property结构
    void init(size_t size);

};