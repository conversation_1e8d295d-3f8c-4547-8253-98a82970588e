#pragma once
#include "TypeAttributeBase.h"

//保存简单类型QtProperty属性栏对应参数的结构体
class TypeAttributeSimple : public TypeAttributeBase
{
public:
    QtAbstractPropertyManager* _pMgr;
public:
    TypeAttributeSimple(AttributeWidgetBase& ownWidget
        , const WD::WDBMAttrDesc& attrDesc
        , QtAbstractPropertyManager* pMgr
        , const FuncNameTs& nameTs = FuncNameTs());
    ~TypeAttributeSimple();
public:
    virtual bool setCurrentValue(const WD::WDBMAttrValue& value) override;
    virtual WD::WDBMAttrValue getCurrentValue(bool* bOk = nullptr) const override;
    virtual bool contains(QtProperty* pPty) const override
    {
        return pPty != nullptr && _pRootProperty == pPty;
    }
};