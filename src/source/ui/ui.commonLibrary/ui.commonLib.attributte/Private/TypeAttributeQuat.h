#pragma once
#include "TypeAttributeBase.h"

class TypeAttributeQuat: public TypeAttributeBase
{
private:
    QtStringPropertyManager*    _pStringMgr;
    QtDoublePropertyManager*    _pDoubleMgr;
    QString                     _rootValue;
    std::array<QtProperty*, 2>  _pSubProperty;
    std::array<QString, 2>      _subValues;
    std::array<QtProperty*, 6>  _pSubSubProperty;
    std::array<double,6>        _subSubValues;

public:
    TypeAttributeQuat(AttributeWidgetBase& ownWidget
        , const WD::WDBMAttrDesc& attrDesc
        , QtStringPropertyManager* pStringMgr
        , QtDoublePropertyManager* pDoubleMgr
        , const FuncNameTs& nameTs = FuncNameTs());
    ~TypeAttributeQuat();
public:
    virtual bool setCurrentValue(const WD::WDBMAttrValue& value) override;
    virtual WD::WDBMAttrValue getCurrentValue(bool* bOk = nullptr) const override;
    virtual bool contains(QtProperty* pPty) const override;
protected:
    /**
    * @brief 更新所有QtProperty显示
    */
    virtual bool updateShowP(QtProperty* pPty)  override;
private:
    //更新容器内保存的值
    bool updateArrayValue();
    //转换当前值为四元数
    WD::DQuat toDQuat() const;
};