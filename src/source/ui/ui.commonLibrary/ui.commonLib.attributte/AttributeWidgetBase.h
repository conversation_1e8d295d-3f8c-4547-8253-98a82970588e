#pragma once

#include <QObject>
#include "core/businessModule/typeMgr/WDBMTypeMgr.h"

class TypeAttributeBase;

class QtProperty;
class QtTreePropertyBrowser;
class QtGroupPropertyManager;
class QtIntPropertyManager;
class QtSpinBoxFactory;
class QtStringPropertyManager;
class QtLineEditFactory;
class MyLineEditFactory;
class QtVariantPropertyManager;
class QtVariantEditorFactory;
class QtColorPropertyManager;
class QtColorEditorFactory;
class QtDoublePropertyManager;
class QtDoubleSpinBoxFactory;
class QtBrowserItem;
class QtEnumPropertyManager;
class QtEnumEditorFactory;

class AttributeWidgetBase : public QObject
{
    Q_OBJECT
public:
    AttributeWidgetBase(QObject *parent = nullptr);
    ~AttributeWidgetBase();
protected:
    /**      
    * @brief 断掉/连接所有属性值改变信号
    * @param bBlock false 表示断掉，true表示连接
    */
    virtual void blockValueChangedSignals(bool bBlock) = 0;

    friend class TypeAttributeBase;
};

