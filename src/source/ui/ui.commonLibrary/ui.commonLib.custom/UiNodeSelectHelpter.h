#pragma once

#include <QObject>
#include "core/WDCore.h"
#include "core/node/WDNode.h"
#include "core/businessModule/WDBMCommon.h"

class QLineEdit;
class QCheckBox;

/**
 * @brief 从模型树上获取某个节点的界面辅助对象
*/
class UiNodeSelectHelpter : public QObject
{
    Q_OBJECT
public:
    /**
     * @brief 命令方法
    */
    using CommandFunction   = std::function<WD::WDNode::SharedPtr(std::string type, UiNodeSelectHelpter& sender)>;
    /**
     * @brief 命令对象，包含命令关键字和命令方法
    */
    using Command           = std::pair<std::string, CommandFunction>;
    /**
     * @brief 命令对象列表
    */
    using Commands          = std::vector<Command>;
public:
    /**
     * @brief 构造
     * @param core core app
     * @param type 模块类型
     * @param pParent 父对象指针
    */
    UiNodeSelectHelpter(WD::WDCore& core, const std::string& type = "Design");
    UiNodeSelectHelpter(const UiNodeSelectHelpter& right) = delete;
    UiNodeSelectHelpter(UiNodeSelectHelpter&& right) = delete;
    UiNodeSelectHelpter operator=(const UiNodeSelectHelpter& right) = delete;
    UiNodeSelectHelpter operator=(UiNodeSelectHelpter&& right) = delete;
    ~UiNodeSelectHelpter();
signals:
    /**
     * @brief 当前节点改变信号
     * @param pCurrentNode 当前节点
     * @param pPrevNode 改变之前的节点
    */
    void sigCurrentNodeChanged(WD::WDNode* pCurrentNode, WD::WDNode* pPrevNode);
public:
    /**
     * @brief 获取 core
    */
    WD::WDCore& core()
    {
        return _core;
    }
    /**
     * @brief 设置当前节点
    */
    void setNode(WD::WDNode::SharedPtr pNode);
    /**
     * @brief 设置当前节点为根节点
    */
    void setNodeUseTheRootNode();
    /**
     * @brief 获取当前节点
    */
    inline WD::WDNode::SharedPtr node() const
    {
        return _node.lock();
    }
    /**
     * @brief 退出
     *      如果当前是从节点树上选择节点的状态，则退出该状态
    */
    void exit();
    /**
     * @brief 设置节点选择状态复选框
    */
    void setCheckBox(QCheckBox* pCheckBox);
    /**
     * @brief 设置节点名称显示文本编辑器
     * @param pLineEdit 用于显示当前节点名称
     * @param readOnly 指定pLineEdit是否只读
    */
    void setLineEdit(QLineEdit* pLineEdit);
    /**
     * @brief 获取所有命令对象
     *  命令对象将会在 LineEdit 输入完成后通过输入内容依次执行每条命令来获取需要的节点
     *  ！注意:
     *      1. 命令关键字不区分大小写， 即：WORLD <=> wrold <=> World ...
     *      2. 命令的调用顺序严格按照 命令对象数组 的排列顺序。如果调用方需要调整命令执行顺序，需要调用方自己调整命令对象数组顺序
     *      3. 对象内部不校验命令关键字重复，也就是说，需要调用方自己保证命令关键字的唯一性
     *          , 如果命令关键字重复，则结果总是命令对象数组中靠前的命令被执行
     *  默认对象自带三个命令,分别是:
     *      1.  "WORLD"  从当前对象指定的模块(设计模块/元件模块/管理模块)中获取根节点 做为命令返回值
     *      2.  "*"      与 WORLD命令等价
     *      3.  "SITE"   从节点树上的当前节点开始，依次向上查找，直到查找到节点类型为SITE的节点
     *      4.  "ZONE"   从节点树上的当前节点开始，依次向上查找，直到查找到节点类型为ZONE的节点
     *      5.  "OWNER"  获取节点树上当前节点的父节点 做为命令返回值
     *      6.  "CE"     获取节点树上的当前节点 做为命令返回值
     *      7.  "FIRST"  获取节点树上当前节点的父节点的第一个子节点
     *      8.  "LAST"   获取节点树上当前节点的父节点的最后一个子节点
     *      9.  "PREV"   获取节点树上当前节点的前一个兄弟节点
     *      10. "NEXT"   获取节点树上当前节点的后一个兄弟节点
     *  需要添加自定义命令时，使用此接口获取到现有的所有命令,然后再将自己需要添加的命令对象push到命令对象数组中,最后再设置给当前对象
    */
    inline const Commands& commands() const
    {
        return _commands;
    }
    /**
     * @brief 设置所有命令对象
    */
    inline void setCommands(const Commands& commands)
    {
        _commands = commands;
    }
    /**
     * @brief 设置所有命令对象
    */
    inline void setCommands(Commands&& commands)
    {
        _commands = std::forward<Commands>(commands);
    }
private slots:
    // 复选框 状态更改 槽
    void slotCheckBoxStateChanged(int state);
    // 文本编辑器 编辑完成 槽
    void slotLineEditEditingFinished();
private:
    // 模型树当前节点改变通知 响应
    void onNodeTreeCurrentNodeChanged(WD::WDNode::SharedPtr pCurrNode
        , WD::WDNode::SharedPtr pPrevNode
        , WD::WDNodeTree& sender);
private:
    // 指定节点更新LineEdit显示
    void updateLineEditText(WD::WDNode::SharedPtr pNode);
private:
    WD::WDCore&         _core;
    std::string         _type;
    WD::WDNode::WeakPtr _node;
    QLineEdit*          _pLineEdit;
    QCheckBox*          _pCheckBox;
    Commands            _commands;
};
