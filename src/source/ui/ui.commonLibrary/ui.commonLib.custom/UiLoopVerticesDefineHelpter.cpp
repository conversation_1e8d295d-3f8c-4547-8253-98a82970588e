#include "UiLoopVerticesDefineHelpter.h"
#include "core/viewer/WDViewer.h"

/**
* @brief 指定最大小数位数将double数转换成QString
*/
static QString DoubleValueToQString(const double& value, const int& size = 2)
{
    return QString::number(QString::number(value, 'f', size).toDouble(), 'f', QLocale::FloatingPointShortest);
}

UiLoopVerticesEditHelpter::UiLoopVerticesEditHelpter(WD::WDCore& core)
    :_core(core)
{
    _plane                          = std::nullopt;
    _pTableWidget                   = nullptr;
}
UiLoopVerticesEditHelpter::~UiLoopVerticesEditHelpter()
{
}

bool UiLoopVerticesEditHelpter::setPlane(const WD::DVec3& normal, const WD::DVec3& point)
{
    if (normal.lengthSq() <= WD::NumLimits<float>::Epsilon)
        return false;

    // 使用法线和位置信息构造一个平面
    WD::DPlane tPlane = WD::DPlane(normal.normalized(), point);
    // 如果平面已存在，对比设置的平面与已存在的平面是否相同，如果相同，则跳过后续计算
    if (_plane
        && _plane.value().constant == tPlane.constant
        && WD::DVec3::DistanceSq(_plane.value().normal, tPlane.normal) <= WD::NumLimits<float>::Epsilon)
    {
        return true;
    }
    // 新的平面
    _plane = tPlane;
    // 更新所有顶点
    bool bVertChanged = false;
    for (auto& tPoint : _points)
    {
        const WD::DVec3& pos    = tPoint.xyz();
        WD::DVec3 prjPos        = _plane.value().project(pos);
        if (WD::DVec3::DistanceSq(pos, prjPos) <= WD::NumLimits<float>::Epsilon)
            continue;
        // 投影
        tPoint.setXyz(prjPos);
        bVertChanged = true;
    }
    if (!bVertChanged)
        return true;
    // 如果设置了表格控件，同步表格内容
    if (_pTableWidget != nullptr)
    {
        blockTableWidgetSignals(true);
        assert(_points.size() == _pTableWidget->rowCount());
        for (int i = 0; i < _pTableWidget->rowCount(); ++i)
        {
            QTableWidgetItem* pItemX        = _pTableWidget->item(i, 0);
            QTableWidgetItem* pItemY        = _pTableWidget->item(i, 1);
            QTableWidgetItem* pItemZ        = _pTableWidget->item(i, 2);
            QTableWidgetItem* pItemFRadius  = _pTableWidget->item(i, 3);
            if (pItemX != nullptr)
                pItemX->setText(QString::number(_points[i].x));
            if (pItemY != nullptr)
                pItemY->setText(QString::number(_points[i].y));
            if (pItemZ != nullptr)
                pItemZ->setText(QString::number(_points[i].z));
            if (pItemFRadius != nullptr)
                pItemFRadius->setText(QString::number(_points[i].w));
        }
        blockTableWidgetSignals(false);
    }
    emit sigVerticesChanged();

    return true;
}
void UiLoopVerticesEditHelpter::addPoint(const WD::DVec4& point)
{
    WD::DVec3 tPoint = point.xyz();

    // 如果平面有效，则进行投影
    if (_plane)
        tPoint = _plane.value().project(tPoint);

    // !TODO: 校验点的xyz坐标是否符合规则,如果不符合，返回添加失败
    // 添加点
    _points.push_back(WD::DVec4(tPoint, point.w));

    // 如果设置了表格控件，同步表格内容
    if (_pTableWidget != nullptr)
    {
        blockTableWidgetSignals(true);

        int row = _pTableWidget->rowCount();
        _pTableWidget->setRowCount(row + 1);

        for (int col = 0; col < _pTableWidget->columnCount(); ++col)
        {
            if (col >= _points[row].size())
                break;
            QTableWidgetItem* pItem = new QTableWidgetItem(DoubleValueToQString(_points[row][col]));
            _pTableWidget->setItem(row, col, pItem);
        }
        assert(_points.size() == _pTableWidget->rowCount());

        blockTableWidgetSignals(false);
    }
    emit sigVerticesChanged();
}
void UiLoopVerticesEditHelpter::setPoint(size_t index, const WD::DVec4& point)
{
    // 点索引无效
    if (index >= _points.size())
    {
        assert(false);
        return;
    }

    WD::DVec3 tPoint = point.xyz();
    // 如果平面有效，则进行投影
    if (_plane)
        tPoint = _plane.value().project(tPoint);

    WD::DVec4 tPoint4 = WD::DVec4(tPoint, point.w);
    if (_points[index] == tPoint4)
        return;

    _points[index] = tPoint4;

    // 如果设置了表格控件，同步表格内容
    if (_pTableWidget != nullptr)
    {
        assert(_points.size() == _pTableWidget->rowCount());
        blockTableWidgetSignals(true);

        int row = static_cast<int>(index);
        for (int col = 0; col < _pTableWidget->columnCount(); ++col)
        {
            if (col >= _points[row].size())
                break;
            QTableWidgetItem* pItem = _pTableWidget->item(row, col);
            if (pItem != nullptr)
            {
                pItem->setText(QString::number(_points[row][col]));
            }
        }

        blockTableWidgetSignals(false);
    }
    emit sigVerticesChanged();
}
void UiLoopVerticesEditHelpter::setPoints(const WD::DVec4Vector& points)
{
    if (_points == points)
        return;

    _points = points;

    // 如果设置了表格控件，同步表格内容
    if (_pTableWidget != nullptr)
    {
        blockTableWidgetSignals(true);

        _pTableWidget->clearContents();
        _pTableWidget->setRowCount(static_cast<int>(_points.size()));
        assert(_points.size() == _pTableWidget->rowCount());

        for (int row = 0; row < _pTableWidget->rowCount(); ++row)
        {
            for (int col = 0; col < _pTableWidget->columnCount(); ++col)
            {
                if (col >= _points[row].size())
                    break;
                QTableWidgetItem* pItem = new QTableWidgetItem(QString::number(_points[row][col]));
                _pTableWidget->setItem(row, col, pItem);
            }
        }


        blockTableWidgetSignals(false);
    }
    emit sigVerticesChanged();
}
void UiLoopVerticesEditHelpter::removePoint(int index)
{
    if (index < 0 || index >= _points.size())
        return;


    if (_pTableWidget != nullptr)
    {
        assert(_points.size() == _pTableWidget->rowCount());

        blockTableWidgetSignals(true);

        // 移除对应行
        _pTableWidget->removeRow(index);

        blockTableWidgetSignals(false);
    }

    _points.erase(_points.begin() + index);

    emit sigVerticesChanged();
}
void UiLoopVerticesEditHelpter::clear()
{
    // 存储添加的点
    _points.clear();
    // 环顶点所在的平面
    _plane = std::nullopt;
    // 清除界面显示
    if (_pTableWidget != nullptr)
    {
        blockTableWidgetSignals(true);

        _pTableWidget->clearContents();
        _pTableWidget->setRowCount(0);

        blockTableWidgetSignals(false);
    }

    emit sigVerticesChanged();
}

void UiLoopVerticesEditHelpter::setTableWidget(QTableWidget* pTableWidget)
{
    if (_pTableWidget != nullptr)
    {
        disconnect(_pTableWidget
            , &QTableWidget::itemChanged
            , this
            , &UiLoopVerticesEditHelpter::slotTableWidgetItemChanged);
    }

    _pTableWidget = pTableWidget;

    if (_pTableWidget != nullptr)
    {
        connect(_pTableWidget
            , &QTableWidget::itemChanged
            , this
            , &UiLoopVerticesEditHelpter::slotTableWidgetItemChanged);
    }
}

void UiLoopVerticesEditHelpter::slotTableWidgetItemChanged(QTableWidgetItem* pItem)
{
    if (pItem == nullptr)
        return ;
    bool bOk        = false;
    double value    = pItem->text().toDouble(&bOk);
    if (!bOk)
    {
        assert(false);
        return;
    }
    // 获取修改的行
    int row = pItem->row();
    if (row >= _points.size())
    {
        assert(false);
        return;
    }
    int col = pItem->column();
    if (col >= _points[row].size())
    {
        assert(false);
        return;
    }
    _points[row][col] = value;

    emit sigVerticesChanged();
}