#pragma once

#include <QObject>
#include <QTableWidget>
#include "core/WDCore.h"
#include "core/viewer/capturePositioning/WDCapturePositioning.h"

class UiLoopVerticesEditHelpter : public QObject
{
    Q_OBJECT
public:
    UiLoopVerticesEditHelpter(WD::WDCore& core);
    UiLoopVerticesEditHelpter(const UiLoopVerticesEditHelpter& right) = delete;
    UiLoopVerticesEditHelpter(UiLoopVerticesEditHelpter&& right) = delete;
    UiLoopVerticesEditHelpter operator=(const UiLoopVerticesEditHelpter& right) = delete;
    UiLoopVerticesEditHelpter operator=(UiLoopVerticesEditHelpter&& right) = delete;
    ~UiLoopVerticesEditHelpter();
signals:
    /**
     * @brief 顶点数据改变信号
     *   添加，删除，移除，清除或者修改某个顶点的坐标值时发送该信号
    */
    void sigVerticesChanged();
public:
    /**
     * @brief 设置环顶点所在平面
     * @param normal 平面法线
     * @param point 平面上任意一点
     * @return 是否设置成功，如果平面法线是0向量，则设置失败
    */
    bool setPlane(const WD::DVec3& normal, const WD::DVec3& point);
    /**
     * @brief 获取环顶点所在平面
    */
    const std::optional<WD::DPlane>& plane() const
    {
        return _plane;
    }
    /**
     * @brief 指定x,y,z坐标(世界坐标)以及圆角半径添加一个点
    */
    void addPoint(const WD::DVec4& point);
    /**
     * @brief 指定索引以及指定x,y,z坐标(世界坐标)以及圆角半径修改一个点
     * @param index 点索引
     * @param point 点数据
    */
    void setPoint(size_t index, const WD::DVec4& point);
    /**
     * @brief 设置顶点列表
     * @param points 顶点列表
    */
    void setPoints(const WD::DVec4Vector& points);
    /**
     * @brief 移除对应下标的点
    */
    void removePoint(int index);
    /**
     * @brief 获取点个数
    */
    inline int pointCount() const
    {
        return static_cast<int>(_points.size());
    }
    /**
     * @brief 获取指定索引的点,xyz表示坐标点(世界坐标),w表示圆角半径
     *  注意: 如果平面数据有效，这里获取的点均是投影在该平面上的
    */
    const WD::DVec4& getPoint(int index) const
    {
        if (index < 0 || index >= _points.size())
            return WD::DVec4::Zero();
        return _points[index];
    }
    /**
     * @brief 获取顶点列表
     *  注意: 如果平面数据有效，这里获取的点均是投影在该平面上的
    */
    const std::vector<WD::DVec4>& points() const
    {
        return _points;
    }
    /**
     * @brief 清除所有顶点以及平面数据
    */
    void clear();
    /**
     * @brief 设置表格窗口,用于显示环顶点坐标
    */
    void setTableWidget(QTableWidget* pTableWidget);
private slots:
    // 表格内容改变槽函数
    void slotTableWidgetItemChanged(QTableWidgetItem* pItem);
private:
    // 打断或连接所有表格相关的信号
    void blockTableWidgetSignals(bool bBlock)
    {
        if (_pTableWidget == nullptr)
            return;
        _pTableWidget->blockSignals(bBlock);
    }
private:
    WD::WDCore&                     _core;
    // 存储添加的点
    WD::DVec4Vector                 _points;
    // 环顶点所在的平面
    std::optional<WD::DPlane>       _plane;
    // 表格窗口，用于显示顶点坐标
    QTableWidget*                   _pTableWidget;
};