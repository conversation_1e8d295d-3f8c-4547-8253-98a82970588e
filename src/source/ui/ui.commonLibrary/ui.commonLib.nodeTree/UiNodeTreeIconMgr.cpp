#include    "UiNodeTreeIconMgr.h"
#include    <QApplication>
#include    <QFile>
#include    "core/businessModule/typeMgr/WDBMTypeDesc.h"

UiNodeTreeIconMgr::UiNodeTreeIconMgr(WD::WDCore& app)
    : _app(app)
{
    QString dataPath = QString::fromLocal8Bit(_app.dataDirPath());
    _iconBaseDir = dataPath + QString("nodeTypeConfig/");
    _defaultIcon = QIcon(_iconBaseDir + QString("iconDefault/default.png"));
}


UiNodeTreeIconMgr::~UiNodeTreeIconMgr()
{
}

const QIcon& UiNodeTreeIconMgr::getIcon(WD::WDNode::SharedPtr pNode)
{
    if (pNode == nullptr)
        return _defaultIcon;

    auto pDesc = pNode->getTypeDesc();
    if (pDesc == nullptr)
        return _defaultIcon;

    const std::string* pResIcon = pDesc->findResource("icon");
    if (pResIcon == nullptr)
        return _defaultIcon;

    auto& tResIcon = *pResIcon;
    QString iconPath = _iconBaseDir + QString::fromUtf8(tResIcon.c_str());
    if (!QFile::exists(iconPath))
        return _defaultIcon;

    auto fItr = _iconMap.find(iconPath);
    if (fItr != _iconMap.end())
        return fItr->second;

    _iconMap.insert({ iconPath, QIcon(iconPath) });
    return _iconMap[iconPath];
}
