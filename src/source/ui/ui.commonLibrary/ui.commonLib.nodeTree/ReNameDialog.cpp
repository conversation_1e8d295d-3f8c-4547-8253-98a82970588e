#include "ReNameDialog.h"
#include "core/nodeTree/WDNodeTree.h"
#include "core/businessModule/WDBMBase.h"
#include "ui_ReNameDialog.h"
#include "core/WDTranslate.h"
#include "core/message/WDMessage.h"

ReNameDialog::ReNameDialog(WD::WDCore& core, WD::WDBMBase& bmBase, QWidget* parent)
    :QDialog(parent)
    , _core(core)
    , ui(new Ui::ReNameDialog)
    , _nameHelpter(bmBase)
{
    ui->setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    retranslateUi();

    connect(ui->pushBtnCE, &QPushButton::clicked, this, &ReNameDialog::slotCEBtnClicked);
    connect(ui->pushBtnApply, &QPushButton::clicked, this, &ReNameDialog::slotApplyBtnClicked);
    connect(ui->pushBtnClose, &QPushButton::clicked, this, &ReNameDialog::slotCloseBtnClicked);
    connect(ui->lineEdit, &QLineEdit::returnPressed, this, &ReNameDialog::slotApplyBtnClicked);
    _nameHelpter.setLineEdit(ui->lineEdit);
}
ReNameDialog::~ReNameDialog()
{
    if(ui != nullptr)
    {
        delete ui;
        ui = nullptr;
    }
    _pNode.reset();
}
void ReNameDialog::showEvent(QShowEvent* event)
{
    // 显示重命名窗口
    event->accept();
    //将节点树当前的选中的节点设置为默认改名称的节点
    auto pNode  = this->getCurrentNode();
    setLineEditText(pNode);
}
void ReNameDialog::slotCEBtnClicked()
{
    auto pNode = getCurrentNode();
    if(pNode == nullptr)
    {
        WD_WARN_T("ReNameDialog", "Please select need reName Node on nodeTree!");
        return;
    }
    setLineEditText(pNode);
}
void ReNameDialog::slotApplyBtnClicked()
{
    auto pNode  = _pNode.lock();
    if(pNode == nullptr)
    {
        WD_WARN_T("ReNameDialog", "The target node is invalid, use the Current Node button to reset the node you want to rename!");
        return;
    }
    auto pBMBase = pNode->getBMBase();
    if (pBMBase == nullptr)
    {
        assert(false && "Node BMBase is null!");
        return;
    }
    
    // 校验新名称是否合法
    if(!_nameHelpter.valid())
    {
        WD_WARN_T("ReNameDialog", "The target Name is invalid, please reset!");
        return;
    }
    auto text = _nameHelpter.name();

    // 获取修改方式
    auto type = ui->comboBox->currentIndex();
    switch(type)
    {
    case Only:
        // 仅修改当前节点的名称
        {
            // text与节点名称相同则不需要修改
            if (text == pNode->name())
                return;

            if (!text.empty())
            {
                // 判断名称是否存在
                if (_nameHelpter.exists())
                {
                    WD_WARN_T("ReNameDialog", "Failed to change the node name because the name is the same!");
                    return;
                }
            }
            else
            {
                // 特殊出GRIDSY类型，该类型名称不能为空
                if(pNode->isType("GRIDSY"))
                {
                    WD_WARN_T("ReNameDialog", "Rename failed, name of this type can not empty");
                    return;
                }
            }
            pNode->setAttribute("Name", text);
        }
        break;
    case All:
        // 修改当前节点及所有子孙节点
        {
            if(text.empty())
            {
                return;
            }
            // 获取被替换的字符串
            auto oldName = pNode->name();
            int failedCount = 0;
            WD::WDNode::RecursionHelpter(*pNode, [this, &oldName, &text, &failedCount](WD::WDNode& node)
            {
                auto ret = replaceNodeName(node, oldName, text);
                if(!ret)
                    failedCount++;
            });
            if(failedCount > 0 )
            {
                char buf[1024] = { 0 };
                sprintf_s(buf, sizeof(buf), "重命名失败%d个!", failedCount);
                WD_WARN(buf);
            }
        }
        break;
    case UnSet:
        // 将当前节点的名称设置为默认的
        {
            // 判断如果是未命名节点，直接跳过
            if (!pNode->isNamed())
                return;

            // 特殊出GRIDSY类型，该类型名称不能为空
            if (pNode->isType("GRIDSY"))
            {
                WD_WARN_T("ReNameDialog", "Rename failed, name of this type can not empty");
                return;
            }
            // 将节点名称清空
            pNode->setAttribute("Name", std::string(""));
        }
        break;
    default:
        break;
    }
}
void ReNameDialog::slotCloseBtnClicked()
{
    this->close();
}
WD::WDNode::SharedPtr ReNameDialog::getCurrentNode()
{
    auto pCurrentNode = _core.nodeTree().currentNode();
    if (pCurrentNode == nullptr)
    {
        return nullptr;
    }
    _pNode = pCurrentNode;
    return pCurrentNode;
}
void ReNameDialog::setLineEditText(const WD::WDNode::SharedPtr pNode)
{
    ui->lineEdit->clear();
    if(pNode == nullptr)
        return;

    QString nameT = QString::fromUtf8(pNode->name().c_str());
    ui->lineEdit->setText(nameT);
}
bool ReNameDialog::isStartWith(const WD::WDNode& node, const std::string& str)
{
    const auto& name  = node.name();
    if(name.size() < str.size())
        return false;
    return name.substr(0, str.size()) == str;
}
bool ReNameDialog::replaceNodeName(WD::WDNode& node, const std::string& srcStr, std::string destStr)
{
    auto pBm = node.getBMBase();
    if(pBm == nullptr)
        return false;

    auto name = node.name();

    if(!isStartWith(node, srcStr))
        return true;

    // 获取名称需保留的字符串
    auto remindStr = name.substr(srcStr.size(), name.size() - srcStr.size());
    destStr.append(remindStr);

    // text与节点名称相同则不需要修改
    if (destStr == node.name())
        return true;

    if(pBm->nameExists(destStr))
        return false;

    node.setAttribute("Name", destStr);
    return true;
}
void ReNameDialog::retranslateUi()
{

    // 此处不使用组件的控件翻译，为了降低模块间的耦合
    WD::WDCxtTsBg("ReNameDialog");

    auto title = QString::fromUtf8(WD::WDCxtTs("ReName").c_str());
    this->setWindowTitle(QString::fromUtf8(WD::WDCxtTs("ReName").c_str()));
    ui->labelName->setText(QString::fromUtf8(WD::WDCxtTs("Name").c_str()));
    ui->labelRange->setText(QString::fromUtf8(WD::WDCxtTs("Range").c_str()));
    ui->comboBox->addItem(QString::fromUtf8(WD::WDCxtTs("Only").c_str()));
    ui->comboBox->addItem(QString::fromUtf8(WD::WDCxtTs("Re-name all").c_str()));
    ui->comboBox->addItem(QString::fromUtf8(WD::WDCxtTs("Un-name").c_str()));
    ui->pushBtnCE->setText(QString::fromUtf8(WD::WDCxtTs("CE").c_str()));
    ui->pushBtnApply->setText(QString::fromUtf8(WD::WDCxtTs("Apply").c_str()));
    ui->pushBtnClose->setText(QString::fromUtf8(WD::WDCxtTs("Close").c_str()));

    WD::WDCxtTsEd();
}
