#include    "UiNodeTreeView.h"
#include    "core/node/WDNode.h"
#include    <QKeyEvent>
#include    <QMouseEvent>
#include    <QDragEnterEvent>
#include    <QDragMoveEvent>
#include    <QDropEvent>
#include    <QMimeData>
#include    <QDrag>
#include    <QAbstractProxyModel>
#include    <QScrollBar>
#include    "UiNodeItemModel.h"

NodeTreeView::NodeTreeView(WD::WDCore& core)
    :_core(core)
{
    // 隐藏表头
    this->setHeaderHidden(true);
    // 固定行高(以免每次更新视图时每次都计算行高导致卡顿)
    this->setUniformRowHeights(true);
    // 设置右键属性
    this->setContextMenuPolicy(Qt::CustomContextMenu);
    //设置窗口部件可以接收拖入,默认不接收
    this->setAcceptDrops(true);
    //开启拖拽功能
    this->setDragEnabled(true);
    //设置多选
    this->setSelectionMode(QAbstractItemView::ExtendedSelection);
    //设置控件内部移动
    //this->setDragDropMode(QAbstractItemView::InternalMove);
}
NodeTreeView::~NodeTreeView()
{
}

void NodeTreeView::currentChanged(const QModelIndex& current, const QModelIndex& previous)
{
    emit sigCurrentChanged(current, previous);
}

void NodeTreeView::mousePressEvent(QMouseEvent* event)
{
    // 右键节点树定位当前节点
    if(event->button() == Qt::RightButton)
    {
        // 获取当前点击位置的QModelIndex
        QModelIndex curIndex = indexAt(event->pos());
        this->setCurrentIndex(curIndex);
        return;
    }
    if (event->button() != Qt::LeftButton)
        return;
	// 获取当前点击位置的QModelIndex
	QModelIndex curIndex = indexAt(event->pos());
	this->setCurrentIndex(curIndex);
	//获取当前选中的所有Item
	QModelIndexList indices = this->selectedIndexes();
	if (indices.empty())
	{
		QTreeView::mousePressEvent(event);
		return;
	}
    //pModel：过滤器模型
    auto pModel = qobject_cast<QAbstractProxyModel*>(this->model());
    if (pModel == nullptr)
    {
        QTreeView::mousePressEvent(event);
        return;
    }


	std::vector<WD::WDNode::WeakPtr> nodes;
	for (const auto& index : indices)
	{
        //通过过滤器模型的映射获取tIndex（源模型的节点索引）
        auto tIndex =  pModel->mapToSource(index);
        WD::WDNode::SharedPtr pNode = NodeItemModel::GetNode(tIndex);
		if (pNode != nullptr)
			nodes.push_back(pNode);
	}
	if (nodes.empty())
	{
		QTreeView::mousePressEvent(event);
		return;
	}

	QMimeData* mimeData = new TMyQMimeData<std::vector<WD::WDNode::WeakPtr> >("NodesMimeData", std::move(nodes));
	//创建QDrag,用来移动数据
	QDrag* drag = new QDrag(this);
	drag->setMimeData(mimeData);
	//执行移动操作
	drag->exec(Qt::MoveAction);
	
    QTreeView::mousePressEvent(event);
}

void NodeTreeView::dragEnterEvent(QDragEnterEvent* event)
{
    //如果有自己定义的MIME类型数据,则进行移动操作
    if (event->source() == this && event->mimeData()->objectName() == QString::fromUtf8("NodesMimeData"))
    {
        event->setDropAction(Qt::MoveAction);
        event->accept();
    }
    else
    {
        event->ignore();
    }
    QTreeView::dragEnterEvent(event);
}
void NodeTreeView::dragMoveEvent(QDragMoveEvent* event)
{
    //如果有自己定义的MIME类型数据,则进行移动操作
    if (event->source() == this && event->mimeData()->objectName() == QString::fromUtf8("NodesMimeData"))
    {
        event->setDropAction(Qt::MoveAction);
        event->accept();
    }
    else
    {
        event->ignore();
    }
    QTreeView::dragMoveEvent(event);
}
void NodeTreeView::dropEvent(QDropEvent* event)
{
    if (event->mimeData()->objectName() == QString::fromUtf8("NodesMimeData"))
    {
        //TODO:待添加移动节点
        return;
    }
    else
    {
        event->ignore();
    }
    QTreeView::dropEvent(event);
}


SearchListView::SearchListView(WD::WDCore& core, QWidget* parent)
    : QListView(parent)
    , _core(core)
{
    connect(this, &QListView::clicked, this, &SearchListView::slotCurrentChanged);
}

SearchListView::~SearchListView()
{}