#include    "UiCommonLibNodeTree.h"
#include    "viewer/WDViewer.h"
#include    "scene/WDScene.h"
#include    "core/WDTranslate.h"
#include    "core/message/WDMessage.h"

void MySortFilterProxyModel::addFilterType(const std::string& type)
{
    if (_filterTypes.contains(type))
        return;
    _filterTypes.append(type);
}
void MySortFilterProxyModel::removeFilterType(const std::string& type)
{
    if (!_filterTypes.contains(type))
        return;
    _filterTypes.removeOne(type);
}
bool MySortFilterProxyModel::filterAcceptsRow(int source_row, const QModelIndex& source_parent) const
{
    auto index = sourceModel()->index(source_row, this->filterKeyColumn(), source_parent);
    if (!index.isValid())
        return QSortFilterProxyModel::filterAcceptsRow(source_row, source_parent);
    WD::WDNode* pNode = static_cast<WD::WDNode*>(index.internalPointer());
    if (pNode == nullptr)
        return QSortFilterProxyModel::filterAcceptsRow(source_row, source_parent);
    // 如果节点带有隐藏标志，则过滤
    if (pNode->flags().hasFlag(WD::WDNode::F_ItemHidden))
        return false;
    if (_filterTypes.contains(pNode->type().data()))
        return false;
    return true;
}

UiCommonLibNodeTree::UiCommonLibNodeTree(WD::WDCore& core,WD::WDNodeTree& tree, QWidget * parent)
    : _core(core)
{
    WDUnused(parent);
    _pNodeModel = new NodeItemModel(core,tree);
    _pFilterNodeModel = new MySortFilterProxyModel(this);       //创建过滤器
    _pFilterNodeModel->setSourceModel(_pNodeModel);             //过滤器设置源QAbstractItemModel
    _pFilterNodeModel->setFilterKeyColumn(0);                   //设置过滤列
    _pNodeView = new NodeTreeView(core);                        
    _pNodeView->setModel(_pFilterNodeModel);                    //view设置过滤器
    _verticalLayout = new QVBoxLayout(this);
    _verticalLayout->addWidget(_pNodeView);
    _verticalLayout->setContentsMargins(0, 0, 0, 0);

    _lableSearch = new QLabel(this);
    _lableSearch->setText(QString::fromUtf8(WD::WDTs("NodeTreeWidget", "Search").c_str()));
    _lineEditSearch = new QLineEdit(this);
    _serachResultDialog = new QDialog(this);
    _serachResultDialog->setWindowTitle(QString::fromUtf8(WD::WDTs("NodeTreeWidget", "SearchResult").c_str()));
    //去掉对话框右上角的问号（帮助按钮）
    _serachResultDialog->setWindowFlags(_serachResultDialog->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    _serachResultDialog->resize(300,500);

    _searchListModel = new SearchListModel(core);
    _searchListView = new SearchListView(core);
    _searchListView->setModel(_searchListModel);
    QVBoxLayout* vLayoutResult = new QVBoxLayout(_serachResultDialog);
    vLayoutResult->addWidget(_searchListView);
    _serachResultDialog->setLayout(vLayoutResult);

    _hLayoutSearch = new QHBoxLayout();
    _hLayoutSearch->addWidget(_lableSearch);
    _hLayoutSearch->addWidget(_lineEditSearch);
    connect(_lineEditSearch, &QLineEdit::returnPressed, this, [=]()
    {
        auto text = _lineEditSearch->text().toUtf8().toStdString();

        // 移除text末尾的空格
        while (!text.empty() && std::isspace(text.back()))
        {
            text.pop_back();
        }

        if(text.empty())
        {
            _searchListModel->setData({});
            _serachResultDialog->close();
            return;
        }

        // 节点树模糊搜索
        auto nodes = _core.nodeTree().fuzzyFindNodes(text);
        if(nodes.empty())
        {
            // 清除并关闭界面
            _searchListModel->setData({});
            _serachResultDialog->close();

            // 未按照名称查找到, 尝试使用GUID查找
            bool bGuidOk = false;
            auto uuid = WD::WDUuid::FromString(text, &bGuidOk);
            // 不是GUID, 直接返回
            if (!bGuidOk)
            {
                WD_WARN_T("NodeTreeWidget", "no node with the name");
                return;
            }
            auto pFindNode = _core.nodeTree().findNode(uuid);
            if (pFindNode == nullptr) 
            {
                WD_WARN_T("NodeTreeWidget", "no node with the GUID");
                return;
            }
            // 直接设置为当前选中的节点
            _core.nodeTree().setCurrentNode(pFindNode);
            _core.needRepaint();
            return;
        }
        if (nodes.size() == 1)
        {
            _searchListModel->setData({});
            _serachResultDialog->close();
            _core.nodeTree().setCurrentNode(nodes[0]);
            _core.needRepaint();
            return;
        }
        _searchListModel->setData(nodes);
        // 显示搜索结果弹窗
        _serachResultDialog->show();
    });
    connect(_searchListView, &SearchListView::itemClicked, [=](WD::WDNode::SharedPtr pNode){
        if(pNode == nullptr)
            return;
        _core.nodeTree().setCurrentNode(pNode);
        _core.needRepaint();
    });

    //节点树的搜索功能
    _pSearchNodeWidget = new SearchNodeWidget(_core, this);

    // 获取当前业务对象
    auto pBmBase = _core.currentBM();
    if(pBmBase != nullptr)
    {
        // 重命名
        _pReNameDialog = new ReNameDialog(_core, *pBmBase, this);
    }
    else
    {
        _pReNameDialog = nullptr;
    }
    //该信号槽绑定过于暴力，当信号触发多遍时，节点树刷新会很卡MVC内部有实现该绑定，一般不需要特殊绑定
    //connect(_pNodeModel, &NodeItemModel::dataChanged, [this]()
    //{
    //    auto pView = _pNodeView->viewport();
    //    if(pView != nullptr)
    //    {
    //        pView->update();
    //    }
    //});
    connect(_pNodeView
        , SIGNAL(sigCurrentChanged(const QModelIndex&, const QModelIndex&))
        , this
        , SLOT(slotNodeViewCurrentChanged(const QModelIndex&, const QModelIndex&)));
    connect(_pNodeView
        , SIGNAL(doubleClicked(const QModelIndex&))
        , this
        , SLOT(slotNodeViewDoubleClicked(const QModelIndex&)));
    connect(_pNodeView
        , SIGNAL(customContextMenuRequested(const QPoint&))
        , this
        , SLOT(slotCustomContextMenuRequested(const QPoint&)));
    connect(_pNodeView
        , SIGNAL(collapsed(const QModelIndex&))
        , this
        , SLOT(slotNodeCollapsed(const QModelIndex&)));
}

UiCommonLibNodeTree::~UiCommonLibNodeTree()
{
    if (_pNodeView != nullptr)
    {
        delete _pNodeView;
        _pNodeView = nullptr;
    }
    if (_pFilterNodeModel != nullptr)
    {
        delete _pFilterNodeModel;
        _pFilterNodeModel = nullptr;
    }
    if (_pNodeModel != nullptr)
    {
        delete _pNodeModel;
        _pNodeModel = nullptr;
    }
    if (_verticalLayout != nullptr)
    {
        delete _verticalLayout;
        _verticalLayout = nullptr;
    }
    if (_pReNameDialog != nullptr)
    {
        delete _pReNameDialog;
        _pReNameDialog = nullptr;
    }
    if (_pSearchNodeWidget != nullptr)
    {
        delete _pSearchNodeWidget;
        _pSearchNodeWidget = nullptr;
    }
    if (_lableSearch != nullptr)
    {
        delete _lableSearch;
        _lableSearch = nullptr;
    }
    if (_lineEditSearch != nullptr)
    {
        delete _lineEditSearch;
        _lineEditSearch = nullptr;
    }
    if (_searchListModel != nullptr)
    {
        delete _searchListModel;
        _searchListModel = nullptr;
    }
    if (_searchListView != nullptr)
    {
        delete _searchListView;
        _searchListView = nullptr;
    }
    if (_hLayoutSearch != nullptr)
    {
        delete _hLayoutSearch;
        _hLayoutSearch = nullptr;
    }
    if (_serachResultDialog != nullptr)
    {
        delete _serachResultDialog;
        _serachResultDialog = nullptr;
    }
}

WD::WDNode::SharedPtr UiCommonLibNodeTree::getIndexNode(const QModelIndex& index) const
{
    return _pNodeModel->GetNode(index);
}

QModelIndex UiCommonLibNodeTree::getCurrentIndex() const
{
    auto filterIndex = _pNodeView->currentIndex();
    return _pFilterNodeModel->mapToSource(filterIndex);
}

QModelIndex UiCommonLibNodeTree::getNodeModelIndex(WD::WDNode::SharedPtr pNode) const
{
    auto sourceModelIndex = _pNodeModel->nodeModelIndex(pNode);
    return _pFilterNodeModel->mapFromSource(sourceModelIndex);
}

void UiCommonLibNodeTree::setCurrentNodeViewIndex(const QModelIndex & index) const
{
    _pNodeView->setCurrentIndex(index);
}

void UiCommonLibNodeTree::currentNodeViewScrollTo(const QModelIndex & index) const
{
    _pNodeView->scrollTo(index);
}

void UiCommonLibNodeTree::nodeViewExpand(const QModelIndex & index) const
{
    _pNodeView->expand(index);
}

void UiCommonLibNodeTree::nodeViewCollapse(const QModelIndex& index) const
{
    if (_pNodeView->isExpanded(index))
		_pNodeView->collapse(index);
}

void UiCommonLibNodeTree::slotNodeViewCurrentChanged(const QModelIndex& currIndex, const QModelIndex& prevIndex)
{
    auto sourceIndex = _pFilterNodeModel->mapToSource(currIndex);
    auto sourcePrevIndex = _pFilterNodeModel->mapToSource(prevIndex);
    emit sigNodeViewCurrentChanged(sourceIndex, sourcePrevIndex);
}

void UiCommonLibNodeTree::slotNodeViewDoubleClicked(const QModelIndex& currIndex)
{
    auto sourceIndex = _pFilterNodeModel->mapToSource(currIndex);
    emit sigNodeNodeViewDoubleClicked(sourceIndex);
}

void UiCommonLibNodeTree::slotCustomContextMenuRequested(const QPoint& pos)
{
    auto index = _pNodeView->indexAt(pos);
    auto sourceModelIndex = _pFilterNodeModel->mapToSource(index);
    WD::WDNode::SharedPtr pCurrNode = _pNodeModel->GetNode(sourceModelIndex);
    if (pCurrNode == nullptr)
        return;
    emit sigCustomContextMenuRequested(pCurrNode.get());
}

void UiCommonLibNodeTree::slotNodeCollapsed(const QModelIndex& index)
{
    auto sourceIndex = _pFilterNodeModel->mapToSource(index);
    emit sigNodeCollapsed(sourceIndex);
}


QWidget* UiCommonLibNodeTree::searchWidget()const
{
    return _pSearchNodeWidget;
}
void UiCommonLibNodeTree::showReNameWidget()
{
    if(_pReNameDialog->isHidden())
    {
        _pReNameDialog->show();
    }
    else
    {
        _pReNameDialog->activateWindow();
        // 触发CE按钮，更新界面信息
        _pReNameDialog->slotCEBtnClicked();
    }
}

QAction * UiCommonLibNodeTree::getMenuAction(QMenu * pContextMenu, const QString & actionName)
{
    if (pContextMenu == nullptr)
        return nullptr;
    for (auto pAction : pContextMenu->actions())
    {
        if (pAction->text() == actionName)
            return pAction;
    }
    return nullptr;
}
void UiCommonLibNodeTree::addFilterType(const std::string& type)
{
    _pFilterNodeModel->addFilterType(type);
    _pFilterNodeModel->invalidate();
}
void UiCommonLibNodeTree::removeFilterType(const std::string& type)
{
    _pFilterNodeModel->removeFilterType(type);
    _pFilterNodeModel->invalidate();
}
void UiCommonLibNodeTree::updateItemView(QModelIndex& parent)
{
    if (!parent.isValid())
        return ;

    QModelIndex index   =   _pNodeModel->index(0, 0, parent);
    _pNodeView->dataChanged(index, index);

    updateItemView(index);
}
