#include "SearchNodeWidget.h"
#include "core/nodeTree/WDNodeTree.h"
#include "core/message/WDMessage.h"
#include <QStandardItem>
#include <QAbstractItemView>
#include "ui_SearchNodeWidget.h"
#include "core/WDTranslate.h"

SearchNodeWidget::SearchNodeWidget(WD::WDCore& core, QWidget* parent)
    :QWidget(parent)
    , _core(core)
    , ui(new Ui::SearchNodeWidget)
{
    ui->setupUi(this);
    ui->pushBtnSearch->setText(QString::fromUtf8(WD::WDTs("NodeTreeWidget", "Search").c_str()));
    ui->lineEdit->setFixedWidth(240);

    connect(ui->lineEdit, &QLineEdit::returnPressed, this, &SearchNodeWidget::slotPushBtnSearchClicked);
    connect(ui->pushBtnSearch, &QPushButton::clicked, this, &SearchNodeWidget::slotPushBtnSearchClicked);
    _core.nodeTree().noticeCurrentNodeChanged() += {this, & SearchNodeWidget::onCurrentNodeChanged};
}
SearchNodeWidget::~SearchNodeWidget()
{
    _core.nodeTree().noticeCurrentNodeChanged() -= {this, & SearchNodeWidget::onCurrentNodeChanged};
    delete ui;
}

void SearchNodeWidget::slotPushBtnSearchClicked()
{
    auto text = ui->lineEdit->text().toUtf8().toStdString();
    // 移除text末尾的空格
    while(!text.empty() && std::isspace(text.back()))
    {
        text.pop_back();
    }

    if(text.empty())
        return;

    auto pNode = _core.nodeTree().findNode(text);
    // 找到同名节点则跳转，未找到就不做任何操作
    if(pNode == nullptr)
    {
        WD_WARN_T("NodeTreeWidget", "no node with the name");
        return;
    }
    _core.nodeTree().setCurrentNode(pNode);
}
void SearchNodeWidget::onCurrentNodeChanged(WD::WDNode::SharedPtr pCurrNode, WD::WDNode::SharedPtr pPrevNode, WD::WDNodeTree& sender)
{
    WDUnused2(pPrevNode, sender);
    if(pCurrNode == nullptr)
    {
        ui->lineEdit->clear();
        return;
    }
    auto nodeName = pCurrNode->name();
    ui->lineEdit->setText(QString::fromUtf8(nodeName.c_str()));
}

