#pragma once

#include <QWidget>
#include "core/WDCore.h"
#include "core/businessModule/typeMgr/WDBMAttrDesc.h"
#include "ui_AttributeListWidget.h"
#include "TypeInfo.h"

class AttributeListWidget : public QWidget
{
    Q_OBJECT
public:
    AttributeListWidget(WD::WDCore& core, QWidget *parent = Q_NULLPTR);
    ~AttributeListWidget();
signals:
    /**
     * @brief 当前属性描述改变
    */
    void sigCurrentAttrDescChanged(int index);
    /**
     * @brief 清空属性描述信息界面
    */
    void clearAttrDescInfo();
public:
    /**
     * @brief 使用WDBMAttrDesc数组更新窗口
     * @param modelName 模块名称
     * @param attributes WDBMAttrDesc属性组
    */
    void updateWidget(const std::string& modelName, const Attributes& attributes);
    /**
     * @brief 获取属性列表当前属性的索引号
     * @return 
    */
    inline int getCurrentAttributIndex()
    {
        return _attributeIndex;
    }
private:
    /**
     * @brief 清空属性表
    */
    void clear()const;
private:
    Ui::AttributeListWidget ui;
    WD::WDCore& _core;
    // 属性索引号
    int _attributeIndex;
};
