#pragma once

#include <QWidget>
#include "core/WDCore.h"
#include "ui_TypeListWidget.h"

class TypeListWidget : public QWidget
{
    Q_OBJECT

public:
    TypeListWidget(WD::WDCore& core, QWidget *parent = Q_NULLPTR);
    ~TypeListWidget();
signals:
    /**
     * @brief 当前类型的索引改变
     * @param index 当前选中的类型index
    */
    void sigCurrentTypeIndexChanged(int index);
public:
    /**
     * @brief 更新窗口
     * @param types 
    */
    void updateWidget(std::string modelName, const std::vector<std::string>& types) const;
    /**
     * @brief 获取当前类型的索引
     * @return 
    */
    int getCurrentTypeIndex()
    {
        return _typeIndex;
    }
private:
    Ui::TypeListWidget ui;
    WD::WDCore& _core;
    // 当前类型索引
    int _typeIndex; 
};
