
#include <QApplication>
#include "WIZDesignerDictionaryMainWindow.h"
#include "core/WDCore.h"
#include "core/WDTranslate.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/businessModule/catalog/WDBMCatalog.h"
#include "core/businessModule/typeMgr/WDBMTypeMgr.h"

int main(int argc, char* argv[])
{
	/****** 创建 QApp对象 *******/
	QApplication a(argc, argv);
    // 翻译文件目录
    QString translatePath = a.applicationDirPath() + QString("/../data/translate");
    // 加载翻译文件,这里应该扫描目录，但是现在暂时写死使用一个文件
    WD::StringVector tsFiles;
    QString tsZhCnQtUi = translatePath + QString("/zh_cn/q_ui_zh_cn.xml");
    tsFiles.push_back(tsZhCnQtUi.toLocal8Bit().data());
    // 加载中文翻译
    WD::WDTranslate::Instance()->load(tsFiles);
    //切换语言
    if (!WD::WDTranslate::Instance()->languages().empty())
    {
        WD::WDTranslate::Instance()->setCurrentLanguage(WD::WDTranslate::Instance()->languages().front());
    }

	/****** 创建WCore对象  *******/
	WD::WDCore coreApp(a.applicationDirPath().toLocal8Bit().data());

	/****** 初始化元件模块管理对象以及设计模块管理对象  *******/
	//初始化元件模块管理
	if (!coreApp.getBMCatalog().init())
	{
		assert(false && "元件模块管理初始化失败！！！");
	}
	//初始化设计模块管理
	if (!coreApp.getBMDesign().init())
	{
		assert(false && "设计模块管理初始化失败！！！");
	}

    WIZDesignerDictionaryMainWindow dialog(coreApp);
    dialog.showMaximized();

	int r = a.exec();

	/****** 卸载项目 *******/
	coreApp.getBMDesign().uninit();
	coreApp.getBMCatalog().uninit();

	/****** 卸载app *******/
	coreApp.destroy();
	return r;
}

