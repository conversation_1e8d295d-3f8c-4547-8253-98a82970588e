#pragma     once

#include    "extension/WDPluginTool.h"
#include    "math/Math.hpp"
#include    "common/WDContext.h"
#include    "viewer/WDViewer.h"
#include    "events/WDMouseEvent.h"
#include    "events/WDWheelEvent.h"
#include    "core/WDCore.h"
#include    "geometry/WDGeometry.h"
#include    "material/WDMaterial.h"
#include    "ToolBrowseBase.h"

WD_NAMESPACE_BEGIN

    class PluginToolBrowse: public ToolBrowseBase
    {
    public:
        static constexpr const char* Name = "plugin.tool.browse";
    public:
        PluginToolBrowse(WDCore& app)
            : ToolBrowseBase(app)
        {
            setName(Name);
        }
        virtual ~PluginToolBrowse()
        {
        }
    };

WD_NAMESPACE_END
