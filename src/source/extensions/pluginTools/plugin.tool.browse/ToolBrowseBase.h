#pragma     once

#include    "core/extension/WDPluginTool.h"
#include    "core/math/Math.hpp"
#include    "core/common/WDContext.h"
#include    "core/viewer/WDViewer.h"
#include    "core/events/WDMouseEvent.h"
#include    "core/events/WDWheelEvent.h"
#include    "core/events/WDKeyEvent.h"
#include    "core/events/WDPaintEvent.h"
#include    "core/input/WDKey.h"

#include    "core/events/WDToolSelectEvent.h"
#include    "core/WDCore.h"
#include    "core/nodeTree/WDNodeTree.h"
#include    "core/viewer/primitiveRender/WDLineRender.h"
#include    "core/selections/WDNodeSelection.h"

WD_NAMESPACE_BEGIN

/**
*   @brief 该类是所有工具类的基类，统一实现了操作方法，默认情况下，实现，选装，移动，等浏览功能
*   所有的工具类，如非特殊，都默认继承有默认的功能
*/
class ToolBrowseBase: public WDPluginTool
{
public:
    //判断是点选还是框选的鼠标偏移屏幕距离
    static constexpr const double FrameSelectScreenOffsetPixel = 8.0;
    static constexpr const double FrameSelectScreenOffsetPixelSq = FrameSelectScreenOffsetPixel * FrameSelectScreenOffsetPixel;
protected:
    bool                    _mBtnDown   =   false;
    bool                    _rBtnDown   =   false;
    bool                    _lBtnDown   =   false;
    // 鼠标左键按下时的位置
    DVec2                   _lBtnDownPos;
    // 鼠标中间按下时的位置
    DVec2                   _mBtnDownPos;
    // 鼠标当前位置
    DVec2                   _currPos;
private:
    // 用于绘制框选线框
    WDLine2DRender          _lineRender;
    // 框选线框的参数
    WDLine2DRender::Param   _frameSelectionLineParam;
    // 框选的线框的顶点
    FVec2Vector             _frameSelectionLineVertices;

    // 是否按下 唱、跳、rap、篮球 键
    bool                    _bKeyCtrlDown = false;
public:
    ToolBrowseBase(WDCore& app)
        : WDPluginTool(app)
    {
        _lBtnDown   =   false;
        _mBtnDown   =   false;
        _rBtnDown   =   false;

        _frameSelectionLineParam.color      = Color::yellow;
        _frameSelectionLineParam.lineWidth  = 2.0f;
        _frameSelectionLineParam.style      = WDLineRender::Style::DashDotLine;
    }
    virtual ~ToolBrowseBase()
    {
    }
protected:
    virtual void    onSelect(WDToolSelectEvent* )
    {
        _mBtnDown   =   false;
        _rBtnDown   =   false;
        _lBtnDown   =   false;
        fireSelectEvent(true);
    }
    virtual void    onUnSelect(WDToolSelectEvent* )
    {
        _mBtnDown   =   false;
        _rBtnDown   =   false;
        _lBtnDown   =   false;
        fireSelectEvent(false);
    }
public:
    virtual void    selectEvent(WDToolSelectEvent* e)  override
    {
        WDUnused(e);
        switch (e->toolSelectType())
        {
        case WDToolSelectEvent::TST_Select:
            {
                onSelect(e);
            }
            break;
        case WDToolSelectEvent::TST_Unselect:
            {
                onUnSelect(e);
            }
            break;
        default:
            break;
        }
    }
    virtual void    mousePressEvent(WDMouseEvent* e) override
    {
        using   ArrayNode   =   std::vector<WDNode::SharedPtr>;
        _currPos            =   DVec2(e->pos());
        switch (e->button())
        {
        case WD::WDMouseEvent::MB_LeftButton:
            {
                _lBtnDownPos    =   _currPos;
                _lBtnDown       =   true;

            }
            break;
        case WD::WDMouseEvent::MB_RightButton:
            {
                _rBtnDown       =   true;
            }
            break;
        case WD::WDMouseEvent::MB_MiddleButton:
            {
                _mBtnDownPos    =   _currPos;     
                _mBtnDown       =   true;
            }
            break;
        default:
            break;
        }
    }
    virtual void    mouseReleaseEvent(WDMouseEvent* e) override
    {
        _currPos = DVec2( e->pos());
        switch (e->button())
        {
        case WD::WDMouseEvent::MB_LeftButton:
            {
                WDViewer*   pViewer     =   e->viewer();
                auto    pScene          =   pViewer->scene();

                //鼠标移动距离小于8个像素，判定为点选
                //注意这里用的是距离的平方
                if (pScene != nullptr)
                {
                    WDScene::SelectedListAddMode sMode = _bKeyCtrlDown ? WDScene::SLAM_Append : WDScene::SLAM_Set;
                    if (DVec2::DistanceSq(_lBtnDownPos, _currPos) <= FrameSelectScreenOffsetPixelSq)
                    {
                        DRay ray = pViewer->camera()->createRayFromScreen(DVec2(e->pos()), pViewer->size());
                        // 拾取
                        WDNodePickupResult tRet;
                        pScene->pickup(*pViewer, ray, tRet, sMode);
                    }
                    //否则判定为框选
                    else
                    {
                        DVec2 posLT = DVec2::Min(_lBtnDownPos, _currPos);
                        DVec2 potRB = DVec2::Max(_lBtnDownPos, _currPos);
                        std::vector<WDNode::SharedPtr> rNodes;
                        // 会处理高亮，不改变currentNode
                        pScene->frameSelect(*pViewer
                            , IVec2(posLT), IVec2(potRB)
                            , getFrameSelectType(_lBtnDownPos, _currPos)
                            , rNodes
                            , sMode);
                    }
                    // 获取选择的节点列表，并拿到第一个有效的节点对象, 设置给模型树
                    auto rNodes = pScene->selectedNodes();
                    for (auto pNode : rNodes) 
                    {
                        if (pNode != nullptr)
                        {
                            // 设置模型树的当前节点
                            _app.nodeTree().setCurrentNode(pNode);
                            break;
                        }
                    }
                }

                _lBtnDown   =   false;
            }
            break;
        case WD::WDMouseEvent::MB_RightButton:
            {
                _rBtnDown   =   false;
            }
            break;
        case WD::WDMouseEvent::MB_MiddleButton:
            {
                WDViewer* pViewer = e->viewer();
                if (pViewer != nullptr && DVec2::DistanceSq(_currPos, _mBtnDownPos) <= NumLimits<float>::Epsilon)
                {
                    DVec2 vCen = DVec2(pViewer->size()) * 0.5;
                    pViewer->moveView(IVec2(_currPos), IVec2(vCen));
                }
                _mBtnDown       =   false;
            }
            break;
        default:
            break;
        }
    }
    virtual void    mouseMoveEvent(WDMouseEvent* e) override
    {
        Vec2    curPos      =   Vec2(e->pos());
        auto    viewer      =   e->viewer();
        if (_lBtnDown)
        {
            FVec2 sPos = FVec2(static_cast<float>(_lBtnDownPos.x)
                , static_cast<float>(viewer->size().y) - static_cast<float>(_lBtnDownPos.y));
            FVec2 ePos = FVec2(static_cast<float>(_currPos.x)
                , static_cast<float>(viewer->size().y) - static_cast<float>(_currPos.y));

            _frameSelectionLineVertices.clear();
            _frameSelectionLineVertices.reserve(4);
            _frameSelectionLineVertices.push_back(sPos);
            _frameSelectionLineVertices.push_back(FVec2(sPos.x, ePos.y));
            _frameSelectionLineVertices.push_back(ePos);
            _frameSelectionLineVertices.push_back(FVec2(ePos.x, sPos.y));
        }
        else if (_mBtnDown)
        {
            viewer->rotateView(IVec2(_currPos), IVec2(curPos));
        }
        else if (_rBtnDown)
        {
            viewer->moveView(IVec2(_currPos), IVec2(curPos));
        }
        _currPos    =   curPos;

    }
    virtual void    wheelEvent(WDWheelEvent* e) override
    {
        e->viewer()->zoomView(e->delta());
        _currPos    =   Vec2(e->pos());
    }
    virtual void    paintEvent(WDPaintEvent* e) override
    {
        WDContext& context = *(e->context());

        if (!_lBtnDown || DVec2::DistanceSq(_lBtnDownPos, _currPos) <= FrameSelectScreenOffsetPixelSq)
            return;

        auto type = getFrameSelectType(_lBtnDownPos, _currPos);

        switch (type)
        {
        case WD::WDFrameSelectParam::FST_WhollyWithin:
            _frameSelectionLineParam.style = WDLine2DRender::Style::SolidLine;
            break;
        case WD::WDFrameSelectParam::FST_WhollyAndPartiallyWithin:
            _frameSelectionLineParam.style = WDLine2DRender::Style::DashLine;
            break;
        default:
            break;
        }

        _lineRender.reset();

        _lineRender.addLineLoop(_frameSelectionLineVertices, _frameSelectionLineParam);

        _lineRender.render(context);
    }

    virtual void    keyPressEvent(WDKeyEvent* evt) override
    {
        switch (evt->key())
        {
        case WD::WDKey::Key_Control:
            {
                _bKeyCtrlDown = true;
            }
            break;
        case WD::WDKey::Key_C:
            {
                if (evt->keyBoardModifiers().hasFlag(WDInputEvent::KBM_Control))
                {
                }
            }
            break;
        case WD::WDKey::Key_V:
            {
                if (evt->keyBoardModifiers().hasFlag(WDInputEvent::KBM_Control))
                {
                }
            }
            break;
        default:
            break;
        }
    }
    virtual void    keyReleaseEvent(WDKeyEvent*evt ) override
    {
        switch (evt->key())
        {
        case WD::WDKey::Key_Control:
        {
            _bKeyCtrlDown = false;
        }
        break;
        case WD::WDKey::Key_C:
        {
            if (evt->keyBoardModifiers().hasFlag(WDInputEvent::KBM_Control))
            {
            }
        }
        break;
        case WD::WDKey::Key_V:
        {
            if (evt->keyBoardModifiers().hasFlag(WDInputEvent::KBM_Control))
            {
            }
        }
        break;
        default:
            break;
        }
    }
private:
    WDFrameSelectParam::FrameSelectionType getFrameSelectType(const DVec2& pt0, const DVec2 pt1)
    {
        return  (pt0.x < pt1.x) 
            ? WDFrameSelectParam::FrameSelectionType::FST_WhollyWithin 
            : WDFrameSelectParam::FrameSelectionType::FST_WhollyAndPartiallyWithin;
    }
};

WD_NAMESPACE_END
