set(TARGET_NAME plugin.tool.browse)

set(HEADER_FILES
	"PluginToolBrowse.h"
	"ToolBrowseBase.h"
)

set(SOURCE_FILES
	"main.cpp"
	"PluginToolBrowse.cpp"
)

add_library(${TARGET_NAME} SHARED
		${HEADER_FILES}
		${SOURCE_FILES}
)

target_include_directories(${TARGET_NAME} PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})

target_compile_definitions(${TARGET_NAME} PRIVATE
	-DWIZDESIGNERCORE_EXPORTS
)

target_link_libraries(${TARGET_NAME} PRIVATE wizDesignerCore)

CopyImportedRuntimeDependency(${TARGET_NAME})
