
#include "plugin.format.sql.h"

WD::WDExtension* Create(WD::WDCore& app)
{
    return new WD::PluginFormatSql(app);
}

void Destroy(WD::WDExtension* extension)
{
    delete extension;
}

WD_EXTENSION_EXPORT void GetExtensionInfor(WD::WDExtensionInfor& infor)
{
    infor.anthor            =   "pr.cd";
    infor.name              =   WD::PluginFormatSql::Name;
    infor.type              =   WD::PluginFormatSql::ExtensionType;
    infor.createFunction    =   Create;
    infor.destroyFunction   =   Destroy;
}
