#include "FileManager.h"

WD_NAMESPACE_BEGIN

FileManager::FileManager()
{
    //FileOpen("ab.sql");
}
FileManager::~FileManager()
{
    FileClose();
}
//���ļ���
bool FileManager::FileOpen(const std::string& fileName)
{
    file.open(fileName,std::ios::out);
    if(file.is_open()) return true;
    else return false;
}

//д���ļ���
bool FileManager::FileWrite(const std::string& tbName, const Records& data)
{

    std::string sql="insert into "+tbName+" values ";
    std::string value;

    for (int i = 0; i < data.size(); i++)
    {
        value="(";
        for (int j = 0; j < data[i].size(); j++)
        {
            value+=("'"+data[i][j]+"'");
            value+=",";
        }
        value.erase(value.end()-1);
        value+=");";
        file << sql + value << std::endl;
    }
    return true;
}

//�ر��ļ���
void FileManager::FileClose()
{
    file.close();
}

bool FileManager::WriteString(const std::string & creatTable)
{
    file << creatTable << std::endl;
    return true;
}

WD_NAMESPACE_END