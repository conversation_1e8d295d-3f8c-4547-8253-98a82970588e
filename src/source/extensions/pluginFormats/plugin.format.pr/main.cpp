
#include "plugin.format.pr.h"

WD::WDExtension* Create(WD::WDCore& app)
{
    return new WD::PluginFormatPR(app);
}

void Destroy(WD::WDExtension* extension)
{
    delete extension;
}

WD_EXTENSION_EXPORT void GetExtensionInfor(WD::WDExtensionInfor& infor)
{
    infor.anthor            =   "pr.cd";
    infor.name              =   WD::PluginFormatPR::Name;
    infor.type              =   WD::PluginFormatPR::ExtensionType;
    infor.createFunction    =   Create;
    infor.destroyFunction   =   Destroy;
}
