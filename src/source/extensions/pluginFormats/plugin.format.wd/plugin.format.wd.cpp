
#include    "plugin.format.wd.h"
#include    "core/businessModule/design/equipment/WDBMDEquiSub.h"
#include    "core/common/WDFileReader.hpp"
#include    "core/common/WDMemReader.hpp"
#include    "core/common/WDFileWriter.hpp"
#include    "core/common/WDStream.h"
#include    "core/common/WDMD5.h"

#include    "common/WDTimestamp.hpp"
#include    <filesystem>

//!Liuerning: �����ǽ� �豸��������豸������㼯 ��ԭ���ļ���������͵㼯��� ת���� �豸��������������
//Ŀ����Ϊ��ͳһ���ģ������ݵ� ������Ͷ�Ӧ�㼯���ݵĻ�ȡ�ӿ�
#define EQUIMENT_GEOMETRY_MEMORY_TRANSFER false

WD_NAMESPACE_BEGIN


struct  WDFormatHeader
{
    char    _magic[4]   =   "wd";
    int     _version    =   1;
    char    _author[64] =   u8"����������Ϣ�����ɷ����޹�˾";
};

const Formats& PluginFormatWD::supportFormats(const FormatAttr& attr) const
{
    switch (attr)
    {
    case FormatAttr::FA_Read:
        return _formats;
    case FormatAttr::FA_Write:
        return _formats;
    default:
        break;
    }
    return WDPluginFormat::FormatNull;
    
}

size_t  PluginFormatWD::read(const FormatParam& param,Objects& objs)
{
    using   ObjectMap   =   std::map<WDUuid,ObjectPtr>;
    using   Nodes       =   std::vector<WDNodeSPtr>;
    WDFileReader    fileReader(param.fileName);
    if (fileReader.isBad())
    {
        return  0;
    }
    fileReader.readAll();

    if (fileReader.length() == 0)
        return 0;

    std::vector<char> stream;
    stream.resize(fileReader.length());
    memcpy(&stream.front(), fileReader.data(), fileReader.length());
    WDWdTool::ReadNodesFromWdStream(objs, stream);
    return stream.size();
}

size_t  PluginFormatWD::read(WDInStream* stream,Objects& objs)
{
    std::vector<char> content;
    stream->readArray(content);
    if(content.size() == 0)
        return 0;
    WDWdTool::ReadNodesFromWdStream(objs, content);
    return content.size();
}


size_t  PluginFormatWD::write(const FormatParam& param,const Objects& objs)
{
    std::vector<char> buf;
    WDWdTool::WriteNodesToWdStream(objs, buf);
    if (buf.empty())
        return 0;

    WDMD5::Key    key   =   WDMD5::Generate(buf.data(),(uint)buf.size());

    /// �����Ŀ����ȷ���ļ�д������ǲ��ɱ��жϵ�
    std::string tmp     =   param.fileName + ".tmp";
    /// д�뵽�����ļ�
    FILE*       pFile   =   fopen(tmp.c_str(),"wb");
    if (pFile == nullptr)
        return  0;
    fwrite(buf.data(), 1, buf.size(),pFile);
    fclose(pFile);
    /// ɾ��Դ�ļ�
    std::filesystem::remove(param.fileName);
    /// ������tmp�ļ�
    std::filesystem::rename(tmp,param.fileName);
    /// дmd5
    std::string     md5Key      =   param.fileName + ".md5";
    FILE*           pMD5File    =   fopen(md5Key.c_str(),"wb");
    if (pMD5File == nullptr)
        return  0;
    fwrite(&key,    1,sizeof(key),  pMD5File);
    fclose(pMD5File);

    return  buf.size();
}

size_t  PluginFormatWD::write(WDOutStream* writer,const Objects& objs)
{
    std::vector<char> buf;
    WDWdTool::WriteNodesToWdStream(objs, buf);
    if (buf.empty())
        return 0;
    writer->writeBuffer(buf.data(), buf.size());
    return buf.size();
}

WD_NAMESPACE_END

