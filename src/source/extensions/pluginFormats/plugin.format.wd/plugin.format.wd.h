#pragma once

#include    "extension/WDPluginFormat.h"
#include    "WDCore.h"
#include    "common/WDFileReader.hpp"
#include    "rapidxml/WDRapidxml.h"
#include    "core/node/WDNode.h"
#include    "../../source/utilLib/util.wd/WDWdTool.h"

WD_NAMESPACE_BEGIN

class PluginFormatWD : public WDPluginFormat
{
public:
    static constexpr const char* Name = "plugin.format.design.wd";
private:
    Formats _formats;
public:
    PluginFormatWD(WDCore& app)
        : WDPluginFormat(app)
    {
        _formats.push_back({".wd","",""});
        setName(Name);
    }
    virtual ~PluginFormatWD()
    {

    }
public:
    /**
    * @brief ��ȡ����֧�ֵ����ݸ�ʽ
    */
    virtual const Formats& supportFormats(const FormatAttr& attr = FA_Read) const override;
    /**
    *   @brief ���ļ��ж�ȡ����
    *   @param param:�������
    *   @param result:��ȡ�������
    *   @return ��ȡ���ֽ�����,0:ʧ��
    */
    virtual size_t  read(const FormatParam& param,Objects& result)  override;
    /**
    *   @brief �����ж�ȡ����
    *   @param param:�������
    *   @param result:��ȡ�������
    *   @return ��ȡ���ֽ�����
    */
    virtual size_t  read(WDInStream* stream,Objects& result) override;
    /**
    *   @brief  д��������鵽�ļ�
    *   @return ����д����ֽ�����,0:ʧ��
    */
    virtual size_t  write(const FormatParam& param,const Objects& result) override;
    /**
    *   @brief  д��������鵽����
    *   @return ����д����ֽ�����,0:ʧ��
    */
    virtual size_t  write(WDOutStream* stream,const Objects& result) override;

protected:

};

WD_NAMESPACE_END
