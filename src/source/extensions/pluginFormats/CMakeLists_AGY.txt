#add_subdirectory("plugin.format.iso")
#add_subdirectory("plugin.format.material")
#add_subdirectory("plugin.format.mysql")
#add_subdirectory("plugin.format.sql")
#add_subdirectory("plugin.format.xml")
#add_subdirectory("plugin.format.wd")
#add_subdirectory("plugin.format.json")
#add_subdirectory("plugin.format.svgNode")
#add_subdirectory("plugin.format.sqlLite")
add_subdirectory("plugin.format.pr")
add_subdirectory("plugin.format.obj")
add_subdirectory("plugin.format.stl")

get_all_targts(all_pluginFormats_targets)
foreach(tgt ${all_pluginFormats_targets})
	set_target_properties(${tgt} PROPERTIES FOLDER "extensions/formats")
	set_target_properties(${tgt} PROPERTIES RUNTIME_OUTPUT_DIRECTORY ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/extensions/formats)
	set_target_properties(${tgt} PROPERTIES LIBRARY_OUTPUT_DIRECTORY ${CMAKE_LIBRARY_OUTPUT_DIRECTORY}/extensions/formats)
endforeach()
set(all_formats_targets ${all_pluginFormats_targets} CACHE STRING "" FORCE)
