
#include "plugin.format.iso.h"

WD::WDExtension* Create(WD::WDCore& app)
{
    return new WD::PluginFormatISO(app);
}

void Destroy(WD::WDExtension* extension)
{
    delete extension;
}

WD_EXTENSION_EXPORT void GetExtensionInfor(WD::WDExtensionInfor& infor)
{
    infor.anthor            =   "pr.cd";
    infor.name              =   WD::PluginFormatISO::Name;
    infor.type              =   WD::PluginFormatISO::ExtensionType;
    infor.createFunction    =   Create;
    infor.destroyFunction   =   Destroy;
}
