
#include    "plugin.format.obj.h"
#include    "core/common/WDContext.h"
#include    "core/viewer/WDViewer.h"
#include    "core/message/WDMessage.h"
#include    <filesystem>


#define     EQUIMENT_GEOMETRY_MEMORY_TRANSFER false

// Ĭ��ֵ
static const WD::Color  DefaultAmbient  =   WD::Color(26,26,26);
static const WD::Color  DefaultSpecular =   WD::Color(100,100,100);
static const double     DefaultShininess=   60.0f;

WD_NAMESPACE_BEGIN

const Formats& PluginFormatObj::supportFormats(const FormatAttr& attr) const
{
    switch (attr)
    {
    case FormatAttr::FA_Read:
        return _formatsRead;
    case FormatAttr::FA_Write:
        return _formatsWrite;
    default:
        break;
    }
    return WDPluginFormat::FormatNull;
}

size_t  PluginFormatObj::read(const FormatParam& param, Objects& object)
{
    WDUnused2(param, object);
    return 0;
}

size_t  PluginFormatObj::read(WDInStream* inStream, Objects& object)
{
    WDUnused2(inStream, object);
    return  0;
}

size_t  PluginFormatObj::write(const FormatParam& param, const Objects& objs)
{
    if (param.innerCallback)
        _useMtl = param.innerCallback();
    else
        _useMtl = true;

    auto evt =   [&](float val,const char* msg)
    {
        if (param.evtProgress)
            param.evtProgress(val * 60, msg);
    };

    evt(0.0f,"start!");

    _offset = 1;
    _existColors.clear();

    // ��ȡmtl�ļ���
    const std::filesystem::path path(param.fileName);
    const auto mtlFileName = path.stem().string() + ".mtl";
    const auto mtlFileFullPath = StringTrimTail(param.fileName, path.filename().string()) + mtlFileName;
    const auto ref = "mtllib " + mtlFileName + "\n";

    // д�뵽�ļ���
    FILE* f = fopen(param.fileName.c_str(), "wb");
    if (f == nullptr)
        return 0;
    WDFileWriter fWriter(f);

    // ͬʱ��mtl�ļ�
    auto fMtl = fopen(mtlFileFullPath.c_str(), "wb");
    if (fMtl == nullptr)
        return 0;
    WDFileWriter mtlWriter(fMtl);

    // Ϊ������modelFactory�����������Ƿ�ѡ�񵼳�����
    // �����϶�mtl�ļ�������������modelFactory�޷���ȷʶ��
    fWriter.writeBuffer(ref.data(), ref.length());

    for (auto obj : objs)
    {
        auto pNode = obj->toPtr<WDNode>();
        if (pNode == nullptr)
            continue;
        toObjFormat(*pNode, fWriter, mtlWriter);
    }
    auto nWrite = fWriter.tell();
    if (nWrite == 0)
        return 0;

    fclose(f);
    fclose(fMtl);

    evt(100.0f,"finished!");

    return nWrite;
}

size_t  PluginFormatObj::write(WDOutStream* outStream, const Objects& objs)
{
    WDUnused2(outStream, objs);
    return 0;
}


size_t PluginFormatObj::toObjFormat(const WDNode& node, WDWriter& writer, WDWriter& mtlWriter)
{
    // �ݹ��ӽڵ�
    for (auto child : node.children())
    {
        if (child == nullptr)
            continue;
        toObjFormat(*child, writer, mtlWriter);
    }

    auto pBase  =   node.getBDBase();
    if (pBase == nullptr)
        return writer.tell();
    auto pGraphable = pBase->graphableSupporter();
    if (pGraphable == nullptr)
        return writer.tell();

    // ��־һ������(�ڵ�)
    const std::string o = "o " + node.uuid().toString() + "\n\n";
    writer.writeBuffer(o.data(), o.length());

    if (_useMtl)
    {
        const auto& color = node.basicColor();
        //const auto& pMaterial = node.material(); // �ݲ����ǲ���
        const std::string matName = color.toString();

        if (_existColors.find(color) == _existColors.end())
        {
            // д����ͷ
            const std::string matHead = "newmtl " + matName + "\n";
            mtlWriter.writeBuffer(matHead.data(), matHead.length());
            // д�������Ϣ
            char buf[64];

            // ������
            sprintf(buf, "Kd %f %f %f\n", color.rF(), color.gF(), color.bF());
            mtlWriter.writeBuffer(buf, strlen(buf));

            // ͸����
            sprintf(buf, "d %f\n", color.aF());
            mtlWriter.writeBuffer(buf, strlen(buf));


			// �ɼӿɲ��ӣ����������phong
#if 0
            {
                // ����ǿ��
                sprintf(buf, "Ns %f\n", DefaultShininess);
                mtlWriter.writeBuffer(buf, strlen(buf));

                // ������
                sprintf(buf, "Ka %f %f %f\n", DefaultAmbient.rF(), DefaultAmbient.gF(), DefaultAmbient.bF());
                mtlWriter.writeBuffer(buf, strlen(buf));

                // ���淴���
                sprintf(buf, "Ks %f %f %f\n", DefaultSpecular.rF(), DefaultSpecular.gF(), DefaultSpecular.bF());
                mtlWriter.writeBuffer(buf, strlen(buf));
            }
#endif

            _existColors.insert(color);
        }

        // �л�����
        const std::string checkMaterial = "usemtl " + matName + "\n";
        writer.writeBuffer(checkMaterial.data(), checkMaterial.length());
    }

    const auto& nodeMat = node.globalTransform();

    for (const auto& pGeom : pGraphable->gGeometries(WDGraphableInterface::GGT_Basic))
    {
        if (pGeom == nullptr)
            continue;
        auto pMesh = pGeom->mesh();
        if (pGeom->mesh() == nullptr)
            continue;

        const auto& verts = pMesh->positions();
        const auto& normals = pMesh->normals();
        const auto& uvs = pMesh->uvs();

        char strVecFloat[64]; // > 6 + 3 * 9
        // 1.����
        const FMat4 mat = FMat4(nodeMat * pGeom->transform());
        const auto rs = FMat4::ToMat3(mat);
        for (const auto& v : verts)
        {
            // ��Ҫ���ݱ任�������ʵ��position
            const auto realPos = mat * v;
            sprintf(strVecFloat, "v %f %f %f\n", realPos.x, realPos.y, realPos.z);
            writer.writeBuffer(strVecFloat, strlen(strVecFloat));
        }
        writer.writeBuffer("\n", 1);
        // 2.����
        for (const auto& vn : normals)
        {
            const auto realVn = (rs * vn).normalized();
            sprintf(strVecFloat, "vn %f %f %f\n", realVn.x, realVn.y, realVn.z);
            writer.writeBuffer(strVecFloat, strlen(strVecFloat));
        }
        writer.writeBuffer("\n", 1);
        // 3.����ӳ��(�ݲ�����)
        for (const auto& vt : uvs)
        {
            sprintf(strVecFloat, "vt %f %f\n", vt.x, vt.y);
            writer.writeBuffer(strVecFloat, strlen(strVecFloat));
        }
        writer.writeBuffer("\n", 1);

        // 4.��������������ʱֻ����������ģ�
        char strFace[128];
        const char* fmt = "f %u//%u %u//%u %u//%u\n";

        for (int idx = 0; idx < pMesh->primitiveSets(WDMesh::Solid).size(); idx++)
        {
            auto& pri = pMesh->primitiveSets(WDMesh::Solid)[idx];
            const std::string g = "g " + node.uuid().toString() + "/" + std::to_string(idx + 1) + "\n";
            writer.writeBuffer(g.data(), g.length());

            switch (pri.drawType())
            {
            case WD::WDPrimitiveSet::DT_Array:
                {
                    switch (pri.primitiveType())
                    {
                    case WDPrimitiveSet::PT_Triangles:
                        {
                            const auto& priData =   pri.drawArrayData();
                            int     nStart  =   int(priData.first);
                            int     nEnd    =   int(priData.first + priData.second);

                            for (int i = nStart; i < nEnd; i += 3)
                            {
                                uint I = i + _offset;
                                sprintf(strFace, fmt, I, I, I + 1, I + 1, I + 2, I + 2);
                                writer.writeBuffer(strFace, strlen(strFace));
                            }
                        }
                        break;
                    }
                }
                break;
            case WD::WDPrimitiveSet::DT_ElementByte:
                {
                    if (pri.primitiveType() != WD::WDPrimitiveSet::PT_Triangles)
                        break;
                    const auto& priData = pri.drawElementByteData();
                    for (size_t i = 0; i < priData.size(); i += 3)
                    {
                        sprintf(strFace, fmt
                            , priData[i] + _offset, priData[i] + _offset
                            , priData[i + 1] + _offset, priData[i + 1] + _offset
                            , priData[i + 2] + _offset, priData[i + 2] + _offset);
                        writer.writeBuffer(strFace, strlen(strFace));
                    }
                }
                break;
            case WD::WDPrimitiveSet::DT_ElementUShort:
                {
                    if (pri.primitiveType() != WD::WDPrimitiveSet::PT_Triangles)
                        break;
                    const auto& priData = pri.drawElementUShortData();
                    for (size_t i = 0; i < priData.size(); i += 3)
                    {
                        sprintf(strFace, fmt
                            , priData[i] + _offset, priData[i] + _offset
                            , priData[i + 1] + _offset, priData[i + 1] + _offset
                            , priData[i + 2] + _offset, priData[i + 2] + _offset);
                        writer.writeBuffer(strFace, strlen(strFace));
                    }
                }
                break;
            case WD::WDPrimitiveSet::DT_ElementUInt:
                {
                    if (pri.primitiveType() != WD::WDPrimitiveSet::PT_Triangles)
                        break;
                    const auto& priData = pri.drawElementUIntData();
                    for (size_t i = 0; i < priData.size(); i += 3)
                    {
                        sprintf(strFace, fmt
                            , priData[i] + _offset, priData[i] + _offset
                            , priData[i + 1] + _offset, priData[i + 1] + _offset
                            , priData[i + 2] + _offset, priData[i + 2] + _offset);
                        writer.writeBuffer(strFace, strlen(strFace));
                    }
                }
                break;
            default:
                break;
            }
            writer.writeBuffer("\n", 1);
        }
        _offset += (uint)verts.size();
    }
    return writer.tell();
}

WD_NAMESPACE_END

