set(TARGET_NAME plugin.format.obj)

set(HEADER_FILES
	"plugin.format.obj.h"
)

set(SOURCE_FILES
	"main.cpp"
	"plugin.format.obj.cpp"
	
)

add_library(${TARGET_NAME} SHARED
		${HEADER_FILES}
		${SOURCE_FILES}
)

target_include_directories(${TARGET_NAME} PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})

target_compile_definitions(${TARGET_NAME} PRIVATE
	-DWIZPLAYER_STATIC
)

target_link_libraries(${TARGET_NAME} PRIVATE wizDesignerCore)

CopyImportedRuntimeDependency(${TARGET_NAME})
