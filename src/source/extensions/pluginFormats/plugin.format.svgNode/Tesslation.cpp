#include "Tesslation.h"
#include "core/common/WDPlatform.hpp"

#if WD_PLATFORM == WD_PLATFORM_WIN32
#include <gl/GLU.h>
#define TESS_CALL   __stdcall
#else
#define TESS_CALL
#endif


WD_NAMESPACE_BEGIN

WDTesslation* g_tess  =   nullptr;

void  TESS_CALL _tessBeginCB(GLenum which, void* userData)
{
    WDTesslation* pTess = (WDTesslation*)userData;
    pTess->tessBeginCB(which);
}
void  TESS_CALL _tessBeginCBUser(GLenum which, void* userData)
{
    WDTesslation* pTess = (WDTesslation*)userData;
    pTess->onTessBegin(which);
}

void  TESS_CALL _tessVertexCB(const GLvoid *data, void* userData)
{
    WDTesslation* pTess = (WDTesslation*)userData;
    pTess->tessVertexCB(data);
}
void  TESS_CALL _tessVertexAttr(const GLvoid *data)
{
    g_tess->onTessAddVert(data);
}


void  TESS_CALL _tessErrorCB(GLenum errorCode, void* userData)
{
    WDTesslation* pTess = (WDTesslation*)userData;
    pTess->tessErrorCB(errorCode);
}

void  TESS_CALL _tessCombineCB(const GLdouble newVertex[3], const GLdouble *neighborVertex[4],const GLfloat neighborWeight[4], GLdouble **outData, void* userData)
{
    WDTesslation* pTess = (WDTesslation*)userData;
    pTess->tessCombineCB(newVertex,neighborVertex,neighborWeight,outData);
}

void  TESS_CALL _tessCombineCB1(const GLdouble newVertex[3], const void *neighborVertex[4],const GLfloat neighborWeight[4], void **outData)
{
    g_tess->onTessCombine(newVertex,neighborVertex,neighborWeight,outData);
}


WDTesslation::WDTesslation(size_t nVert,size_t pri)
{
    _vertexs.reserve(nVert);
    _primatives.reserve(pri);
    _vertexIndex    =   0;
    _glTess         =   gluNewTess();
}
WDTesslation::~WDTesslation()
{
    /// ɾ�����񻯶���
    gluDeleteTess(_glTess);  
}

void    WDTesslation::tesslation( const DVec3* vertex, size_t length )
{
    tesslation(vertex,length,false);
}

void    WDTesslation::tesslation(const DVec3* vertex, size_t length,bool bEdge)
{
    tesslation3d(vertex,length,0,bEdge);
}

void    WDTesslation::beginTess(bool userProc,bool bEdge)
{
    _vertexIndex    =   0;
    _vertexs.clear();
    _primatives.clear();
    /// �������񻯵�����ֵ
    gluTessProperty(_glTess, GLU_TESS_TOLERANCE, 1e-20);
    /// �������񻯵�����ֵ
    gluTessProperty(_glTess, GLU_TESS_WINDING_RULE, GLU_TESS_WINDING_ODD);
    gluTessProperty(_glTess, GLU_TESS_BOUNDARY_ONLY, bEdge ? GL_TRUE : GL_FALSE);

    gluTessCallback(_glTess, GLU_TESS_ERROR_DATA,      (void ( TESS_CALL*)())_tessErrorCB);

    if (userProc)
    {
        g_tess = this;
        gluTessCallback(_glTess, GLU_TESS_BEGIN_DATA,   (void ( TESS_CALL*)())_tessBeginCBUser);
        gluTessCallback(_glTess, GLU_TESS_VERTEX,       (void ( TESS_CALL*)())_tessVertexAttr);
        gluTessCallback(_glTess, GLU_TESS_COMBINE,      (void ( TESS_CALL*)())_tessCombineCB1);
    }
    else
    {
        gluTessCallback(_glTess, GLU_TESS_BEGIN_DATA,   (void ( TESS_CALL*)())_tessBeginCB);
        gluTessCallback(_glTess, GLU_TESS_VERTEX_DATA,  (void ( TESS_CALL*)())_tessVertexCB);
        gluTessCallback(_glTess, GLU_TESS_COMBINE_DATA, (void ( TESS_CALL*)())_tessCombineCB);
    }
    /// ��ʼ����
    gluTessBeginPolygon(_glTess, this);
}

void    WDTesslation::setProperty(uint key,uint value)
{
    if (_glTess == nullptr)
        return;
    gluTessProperty(_glTess, key, value);
}
void    WDTesslation::setProperty(uint key,double value)
{
    if (_glTess == nullptr)
        return;
    gluTessProperty(_glTess, key, value);
}

void    WDTesslation::beginContour()
{
    gluTessBeginContour(_glTess);
}
void    WDTesslation::endContour()
{
    gluTessEndContour(_glTess);
}


void    WDTesslation::addTess(const void* pData,const DVec3* datas,size_t cnt,size_t stride)
{
    char*   pUser   =   (char*)pData;
    char*   coord   =   (char*)datas;
    gluTessBeginContour(_glTess);
    for (size_t x = 0; x < cnt; x++)
    {
        double* pCoord  =   (double*)coord;
        gluTessVertex(_glTess, pCoord, pUser);
        coord   +=  stride;
        pUser   +=  stride;
    }
    gluTessEndContour(_glTess);
}
void    WDTesslation::endTess()
{
    /// g_tess  =   this;
    /// ��������
    gluTessEndPolygon(_glTess);
    g_tess  =   nullptr;
}

void    WDTesslation::tesslation(const DVec3Vector& data,bool bEdge)
{
   tesslation3d(&data.front(),data.size(),0,bEdge);
}

void    WDTesslation::tesslation3d(
     const DVec3* datas
    ,size_t cnt
    ,size_t stride
    ,bool bEdge)
{
    _vertexIndex    =   0;
    _vertexs.clear();
    _primatives.clear();
   
    gluTessProperty(_glTess, GLU_TESS_TOLERANCE,        1e-20);
    gluTessProperty(_glTess, GLU_TESS_WINDING_RULE,     GLU_TESS_WINDING_ODD);
    gluTessProperty(_glTess, GLU_TESS_BOUNDARY_ONLY,    bEdge ? GL_TRUE : GL_FALSE);

    gluTessCallback(_glTess, GLU_TESS_BEGIN_DATA,      (void ( TESS_CALL*)())_tessBeginCB);
    gluTessCallback(_glTess, GLU_TESS_ERROR_DATA,      (void ( TESS_CALL*)())_tessErrorCB);
    
    gluTessCallback(_glTess, GLU_TESS_VERTEX_DATA,     (void ( TESS_CALL*)())_tessVertexCB);

    gluTessCallback(_glTess, GLU_TESS_COMBINE_DATA,    (void ( TESS_CALL*)())_tessCombineCB);

    gluTessBeginPolygon(_glTess, this);//��ʼ����
    
    if (_groups.size())
    {
        for (size_t g = 0 ; g < _groups.size(); ++ g)
        {
            IVec2    data    =   _groups[g];
            gluTessBeginContour(_glTess);

            char*   pCoord = (char*)datas;
            if (stride)
            {
                for (size_t i = data.x; i < data.x + data.y; ++i)
                {
                    char*   src     =   pCoord + i * stride;
                    double* coord   =   (double*)src;
                    gluTessVertex(_glTess, coord, coord);
                }
            }
            else
            {
                for (size_t i = data.x; i < data.x + data.y; ++i)
                {
                    gluTessVertex(_glTess, (double*)&datas[i], (void*)&datas[i]);
                }
            }
            gluTessEndContour(_glTess);
        }
    }
    else
    {
        gluTessBeginContour(_glTess);
        char*   pCoord  =   (char*)datas;
        if (stride)
        {
            for(size_t i = 0; i < cnt; ++i)
            {
                char*   data    =   pCoord + i * stride;
                double* coord   =   (double*)data;
                gluTessVertex(_glTess, coord, coord);
            }
        }
        else
        {
            for(size_t i = 0; i < cnt; ++i)
            {
                gluTessVertex(_glTess, (double*)&datas[i], (void*)&datas[i]);
            }
        }
        gluTessEndContour(_glTess);
    }

    
    
    gluTessEndPolygon(_glTess);/// ��������
}

void    WDTesslation::tesslation(const DVec3Vectors& datas,bool bEdge)
{
    _vertexIndex    =   0;
    _vertexs.clear();
    _primatives.clear();
   
    gluTessProperty(_glTess, GLU_TESS_TOLERANCE, 1e-20);
    gluTessProperty(_glTess, GLU_TESS_WINDING_RULE, GLU_TESS_WINDING_ODD);
    gluTessProperty(_glTess, GLU_TESS_BOUNDARY_ONLY, bEdge ? GL_TRUE : GL_FALSE);

    gluTessCallback(_glTess, GLU_TESS_BEGIN_DATA,      (void ( TESS_CALL*)())_tessBeginCB);
    gluTessCallback(_glTess, GLU_TESS_ERROR_DATA,      (void ( TESS_CALL*)())_tessErrorCB);
    gluTessCallback(_glTess, GLU_TESS_VERTEX_DATA,     (void ( TESS_CALL*)())_tessVertexCB);
    gluTessCallback(_glTess, GLU_TESS_COMBINE_DATA,    (void ( TESS_CALL*)())_tessCombineCB);

    

    gluTessBeginPolygon(_glTess, this);//��ʼ����
    
    for(size_t i = 0; i < datas.size(); ++i)
    {
        gluTessBeginContour(_glTess);
        const DVec3Vector& data  = datas[i];
        for (size_t x = 0; x < data.size(); x++)
        {
            gluTessVertex(_glTess, (double*)&data[x], (void*)&data[x]);
        }
        gluTessEndContour(_glTess);
    }
    gluTessEndPolygon(_glTess);/// ��������
    
}

void    WDTesslation::onTessBegin(GLenum )
{
}

void    WDTesslation::onTessAddVert(const void* )
{
}

void    WDTesslation::onTessCombine(
                          const double [3]
                        , const void *[4]
                        , const float [4]
                        , void** )
{
    
}

void    WDTesslation::tessBeginCB(GLenum which)
{
    Primitive   pri;
    pri._type   =   which;
    pri._count  =   0;
    pri._start  =   (unsigned)_vertexs.size();
    _primatives.push_back(pri);
}
void    WDTesslation::tessVertexCB( const GLvoid *pData )
{
    DVec3* pt  =   (DVec3*)pData;
    _vertexs.push_back(*pt);
    ++_primatives.back()._count;
}
void    WDTesslation::tessVertexAttrCB(const GLvoid *pData)
{
    DVec2* pt  =   (DVec2*)pData;
    DVec3 data(*pt, 0.0);
    _vertexs.push_back(data);
    ++_primatives.back()._count;
}

void    WDTesslation::tessErrorCB( GLenum /*errorCode*/ )
{
}

void    WDTesslation::tessCombineCB( const GLdouble newVertex[3], const GLdouble *neighborVertex[4],const GLfloat neighborWeight[4], GLdouble **outData )
{
    WDUnused(neighborVertex);
    WDUnused(neighborWeight);
    _vertices[_vertexIndex][0] = newVertex[0];
    _vertices[_vertexIndex][1] = newVertex[1];
    _vertices[_vertexIndex][2] = newVertex[2];

    *outData = _vertices[_vertexIndex];
    ++_vertexIndex;
}


WD_NAMESPACE_END


