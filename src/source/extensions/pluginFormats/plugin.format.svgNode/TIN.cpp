#include "TIN.h"
#include "gdal_alg.h"
#include "core/math/utils/clipper.hpp"

WD_NAMESPACE_BEGIN

static constexpr double LL_SCALER = 10000000000.0;
static constexpr double LL_SCALER_Inv = 1.0 / LL_SCALER;

using CPath     = ClipperLib::Path;
using CPaths    = ClipperLib::Paths;
using CI        = ClipperLib::cInt;
using CIPt      = ClipperLib::IntPoint;

template <class T>
CIPt PointToCIPt(const TVec2<T>& point)
{
    double x = double(point.x) * LL_SCALER;
    double y = double(point.y) * LL_SCALER;
    return CIPt(CI(x), CI(y));
}
template <class T>
TVec2<T> CIPtToPoint(const CIPt& point)
{
    double x = double(point.X) * LL_SCALER_Inv;
    double y = double(point.Y) * LL_SCALER_Inv;

    return TVec2<T>(T(x), T(y));
}
template <class T>
CIPt PointToCIPt(const TVec3<T>& point)
{
    double x = double(point.x()) * LL_SCALER;
    double y = double(point.y()) * LL_SCALER;
    return CIPt(CI(x), CI(y));
}
template <class T>
void PointsToCPath(const TVec2Vector<T>& points, CPath& rPath)
{
    rPath.reserve(points.size());
    for (const auto& pt : points)
    {
        rPath.push_back(PointToCIPt<T>(pt));
    }
}
template <class T>
void PointsToCPath(const TVec3Vector<T>& points, CPath& rPath)
{
    rPath.reserve(points.size());
    for (const auto& pt : points)
    {
        rPath.push_back(PointToCIPt<T>(pt));
    }
}
template <class T>
void CPathToPoints(const CPath& path, TVec2Vector<T>& rPoints)
{
    rPoints.reserve(path.size());
    for (const auto& pt : path)
    {
        rPoints.push_back(CIPtToPoint<T>(pt));
    }
}

/**
 * @brief �ж���������ȫ��������
 * @param trianglePath ������·��
 * @param triangleInnerPoint ����������(����ԲԲ��)
 * @param areaPath ����·��
*/
bool TriangleInArea(const CPath& trianglePath
    , const CIPt& triangleInnerPoint
    , const CPath& areaPath)
{
    assert(trianglePath.size() == 3);
    assert(areaPath.size() >= 3);
    int rInA        = ClipperLib::PointInPolygon(trianglePath[0], areaPath);
    int rInB        = ClipperLib::PointInPolygon(trianglePath[1], areaPath);
    int rInC        = ClipperLib::PointInPolygon(trianglePath[2], areaPath);

    // ��������������㶼��������,������������������
    if (rInA == 1 && rInB == 1 && rInC == 1)
        return true;
    int rInInner    = ClipperLib::PointInPolygon(triangleInnerPoint, areaPath);
    // ����������������������Ե�ϣ��������������������ڣ�������������������
    if (rInA != 0 && rInB != 0 && rInC != 0 && rInInner == 1)
        return true;

    return false;
}

/**
 * @brief �ж���������ȫ���ڴ��׶���������
 * @param trianglePath ������·��
 * @param triangleInnerPoint ����������(����ԲԲ��)
 * @param areaPath ����·��
 * @param holePaths ����׶�·��
*/
bool TriangleInArea(const CPath& trianglePath
    , const CIPt& triangleInnerPoint
    , const CPath& areaPath
    , const CPaths& holePaths)
{
    assert(trianglePath.size() == 3);
    assert(areaPath.size() >= 3);
    
    // �����ж������ο϶���������
    if (!TriangleInArea(trianglePath, triangleInnerPoint, areaPath))
        return false;

    // Ȼ���ж���������ȫ�ڿ׶���
    for (const auto& holePath : holePaths)
    {
        ClipperLib::Clipper clipper;
        ClipperLib::Paths allRets;
        allRets.reserve(1);

        clipper.AddPath(holePath, ClipperLib::ptClip, true);
        clipper.AddPath(trianglePath, ClipperLib::ptSubject, true);

        bool clipRet = clipper.Execute(ClipperLib::ctIntersection
            , allRets
            , ClipperLib::pftEvenOdd
            , ClipperLib::pftEvenOdd);

        if (!clipRet)
        {
            assert(false);
            return false;
        }

        // ���㵽�����������������׶��ཻ�����������ڿ׶���Χ��
        if (!allRets.empty())
            return false;
    }
    return true;
}


void TIN::addPoints(const DVec3Vector& points)
{
    double thresholdSq = _threshold * _threshold;
    for (const auto& point : points)
    {
        this->addPointPv(point, thresholdSq);
    }
}

void TIN::update()
{
    // ����ȸ���
    double thresholdSq = _threshold * _threshold;
    processContourLine(thresholdSq);
    
    if (_points.empty())
        return;

    _aabb2 = DAabb2::Null();

    std::vector<double> xDatas;
    std::vector<double> yDatas;
    xDatas.reserve(_points.size());
    yDatas.reserve(_points.size());
    for (const auto& pt : _points)
    {
        xDatas.push_back(pt.x);
        yDatas.push_back(pt.y);

        _aabb2.expandByPoint(pt.xy());
    }

    // Delaunay �����ʷ�
    auto pRes = GDALTriangulationCreateDelaunay((int)(xDatas.size()), xDatas.data(), yDatas.data());
    assert(pRes != nullptr);
    if (pRes == nullptr)
        return;

    _triangles.reserve(pRes->nFacets);

    for (int i = 0; i < pRes->nFacets; i++)
    {
        int a = pRes->pasFacets[i].anVertexIdx[0];
        int b = pRes->pasFacets[i].anVertexIdx[1];
        int c = pRes->pasFacets[i].anVertexIdx[2];

        DVec2 ptA = _points[a].xy();
        DVec2 ptB = _points[b].xy();
        DVec2 ptC = _points[c].xy();

        // �ж�������(��ʱ������)
        if (!DTriangle2::Left(ptA, ptB, ptC))
        {
            std::swap(a, c);
            std::swap(ptA, ptC);
        }
        // ����������
        _triangles.push_back(Triangle());
        _triangles.back().indices[0] = static_cast<uint>(a);
        _triangles.back().indices[1] = static_cast<uint>(b);
        _triangles.back().indices[2] = static_cast<uint>(c);
        _triangles.back().aabb2.expandByPoint(ptA);
        _triangles.back().aabb2.expandByPoint(ptB);
        _triangles.back().aabb2.expandByPoint(ptC);
        _triangles.back().normal        = DTriangle3::Normal(_points[a], _points[b], _points[c]);
        _triangles.back().area          = DTriangle3::Area(_points[a], _points[b], _points[c]);
        _triangles.back().innerPoint2   = DTriangle3::InnerCoord(_points[a], _points[b], _points[c]).xy();
    }
}

bool TIN::mesh(OutMesh& outMesh)
{
    if (_points.empty() || _triangles.empty())
        return false;

    outMesh.vertices.reserve(_points.size());
    for (const auto& pt : _points)
    {
        outMesh.vertices.push_back(FVec3(pt));
    }
    outMesh.normals = computeNormals();
    outMesh.uvs     = computeUvs();

    if (outMesh.vertices.size() <= NumLimits<byte>::Max)
    {
        WDPrimitiveSetDraw::DrawElementByteData d;
        d.reserve(_triangles.size() * 3);
        for (const auto& tri : _triangles)
        {
            d.push_back(static_cast<byte>(tri.indices[0]));
            d.push_back(static_cast<byte>(tri.indices[1]));
            d.push_back(static_cast<byte>(tri.indices[2]));
        }
        outMesh.faces.setPrimitiveType(WDPrimitiveSetDraw::PT_Triangles);
        outMesh.faces.setDrawElementByteData(d);
    }
    else if (outMesh.vertices.size() <= NumLimits<ushort>::Max)
    {
        WDPrimitiveSetDraw::DrawElementUShortData d;
        d.reserve(_triangles.size() * 3);
        for (const auto& tri : _triangles)
        {
            d.push_back(static_cast<ushort>(tri.indices[0]));
            d.push_back(static_cast<ushort>(tri.indices[1]));
            d.push_back(static_cast<ushort>(tri.indices[2]));
        }
        outMesh.faces.setPrimitiveType(WDPrimitiveSetDraw::PT_Triangles);
        outMesh.faces.setDrawElementUShortData(d);
    }
    else if (outMesh.vertices.size() <= NumLimits<uint>::Max)
    {
        WDPrimitiveSetDraw::DrawElementUIntData d;
        d.reserve(_triangles.size() * 3);
        for (const auto& tri : _triangles)
        {
            d.push_back(static_cast<uint>(tri.indices[0]));
            d.push_back(static_cast<uint>(tri.indices[1]));
            d.push_back(static_cast<uint>(tri.indices[2]));
        }
        outMesh.faces.setPrimitiveType(WDPrimitiveSetDraw::PT_Triangles);
        outMesh.faces.setDrawElementUIntData(d);
    }
    else
    {
        assert(false);
        return false;
    }

    return true;
}
bool TIN::mesh(const DVec2Vector& area, OutMesh& outMesh)
{
    if (area.empty())
        return false;

    if (_points.empty() || _triangles.empty())
        return false;

    // ���㷨��
    FVec3Vector normals = this->computeNormals();
    // ����uv
    FVec2Vector uvs = this->computeUvs();

    // ����ΰ�Χ��
    CPath areaPath;
    PointsToCPath(area, areaPath);
    if (areaPath.empty())
        return false;

    WDPrimitiveSetDraw::DrawElementUIntData drawIndices;
    for (auto& triangle : _triangles)
    {
        uint ptIdxA = triangle.indices[0];
        uint ptIdxB = triangle.indices[1];
        uint ptIdxC = triangle.indices[2];

        DVec2 ptA = _points[ptIdxA].xy();
        DVec2 ptB = _points[ptIdxB].xy();
        DVec2 ptC = _points[ptIdxC].xy();

        CIPt iPtA = PointToCIPt(ptA);
        CIPt iPtB = PointToCIPt(ptB);
        CIPt iPtC = PointToCIPt(ptC);

        CIPt iPtInner = PointToCIPt(triangle.innerPoint2);

        CPath triPath = { iPtA, iPtB, iPtC };

        ClipperLib::Clipper clipper;
        ClipperLib::Paths allRets;
        allRets.reserve(1);

        clipper.AddPath(areaPath, ClipperLib::ptClip, true);
        clipper.AddPath(triPath, ClipperLib::ptSubject, true);

        bool clipRet = clipper.Execute(ClipperLib::ctIntersection
            , allRets
            , ClipperLib::pftEvenOdd
            , ClipperLib::pftEvenOdd);

        if (!clipRet)
        {
            assert(false);
            continue;
        }

        // û�м��㵽���������������ȫ��������
        if (allRets.empty())
            continue;

        // �����������������ཻ
        for (const auto& rets : allRets)
        {
            uint sIndex = static_cast<uint>(outMesh.vertices.size());
            if (rets.size() < 3)
            {
                assert(false);
                continue;
            }
            DVec2Vector tPolygon;
            CPathToPoints(rets, tPolygon);
            auto tTris = TEarcut<DVec2>::Exec(tPolygon);
            if (tTris.empty())
            {
                assert(false);
                continue;
            }
            for (const auto& ret : rets)
            {
                DVec2 tPt = CIPtToPoint<double>(ret);
                // �����߶�
                DVec3 uvw = DTriangle3::BaryCoord(DVec3(tPt), DVec3(ptA), DVec3(ptB), DVec3(ptC));
                double h = uvw[0] * _points[ptIdxA][2] + uvw[1] * _points[ptIdxB][2] + uvw[2] * _points[ptIdxC][2];
                outMesh.vertices.push_back(FVec3(FVec2(tPt), float(h)));
                FVec3 n = uvw[0] * normals[ptIdxA] + uvw[1] * normals[ptIdxB] + uvw[2] * normals[ptIdxC];
                outMesh.normals.push_back(n);
                FVec2 u = uvw[0] * uvs[ptIdxA] + uvw[1] * uvs[ptIdxB] + uvw[2] * uvs[ptIdxC];
                outMesh.uvs.push_back(u);
            }
            for (size_t i = 0; i < tTris.size(); ++i)
            {
                drawIndices.push_back(tTris[i] + sIndex);
            }
        }
    }

    outMesh.faces.setPrimitiveType(WDPrimitiveSetDraw::PT_Triangles);
    outMesh.faces.setDrawElementUIntData(drawIndices);

    return true;
}
bool TIN::mesh(const DVec2Vector& area, const std::vector<DVec2Vector>& holes, OutMesh& outMesh)
{
    if (area.empty())
        return false;

    if (_points.empty() || _triangles.empty())
        return false;

    // ���㷨��
    FVec3Vector normals = this->computeNormals();
    // ����uv
    FVec2Vector uvs = this->computeUvs();


    // ��������
    CPath areaPath;
    PointsToCPath(area, areaPath);
    if (areaPath.empty())
        return false;

    // �׶�
    CPaths holePaths;
    holePaths.reserve(holes.size());
    for (const auto& hole : holes)
    {
        holePaths.push_back({});
        PointsToCPath(hole, holePaths.back());
    }

    WDPrimitiveSetDraw::DrawElementUIntData drawIndices;
    for (auto& triangle : _triangles)
    {
        uint ptIdxA = triangle.indices[0];
        uint ptIdxB = triangle.indices[1];
        uint ptIdxC = triangle.indices[2];

        DVec2 ptA   = _points[ptIdxA].xy();
        DVec2 ptB   = _points[ptIdxB].xy();
        DVec2 ptC   = _points[ptIdxC].xy();

        CIPt iPtA   = PointToCIPt(ptA);
        CIPt iPtB   = PointToCIPt(ptB);
        CIPt iPtC   = PointToCIPt(ptC);

        CIPt iPtInner = PointToCIPt(triangle.innerPoint2);

        CPath triPath = {iPtA, iPtB, iPtC};

        ClipperLib::Clipper clipper;
        ClipperLib::Paths allRets;
        allRets.reserve(1);

        clipper.AddPath(triPath, ClipperLib::ptClip, true);
        clipper.AddPath(areaPath, ClipperLib::ptSubject, true);
        clipper.AddPaths(holePaths, ClipperLib::ptSubject, true);

        bool clipRet = clipper.Execute(ClipperLib::ctIntersection
            , allRets
            , ClipperLib::pftEvenOdd
            , ClipperLib::pftEvenOdd);

        if (!clipRet)
        {
            assert(false);
            continue;
        }

        // δ���㵽�˵����������ȫ����������
        if (allRets.empty())
            continue;

        for (const auto& rets : allRets)
        {
            uint sIndex = static_cast<uint>(outMesh.vertices.size());
            if (rets.size() < 3)
            {
                assert(false);
                continue;
            }
            DVec2Vector tPolygon;
            CPathToPoints(rets, tPolygon);
            auto tTris = TEarcut<DVec2>::Exec(tPolygon);
            if (tTris.empty())
            {
                assert(false);
                continue;
            }
            for (const auto& ret : rets)
            {
                DVec2 tPt   = CIPtToPoint<double>(ret);
                // �����߶�
                DVec3 uvw   = DTriangle3::BaryCoord(DVec3(tPt), DVec3(ptA), DVec3(ptB), DVec3(ptC));
                double h    = uvw[0] * _points[ptIdxA][2] + uvw[1] * _points[ptIdxB][2] + uvw[2] * _points[ptIdxC][2];
                outMesh.vertices.push_back(FVec3(FVec2(tPt), float(h)));
                FVec3 n     = uvw[0] * normals[ptIdxA] + uvw[1] * normals[ptIdxB] + uvw[2] * normals[ptIdxC];
                outMesh.normals.push_back(n);
                FVec2 u     = uvw[0] * uvs[ptIdxA] + uvw[1] * uvs[ptIdxB] + uvw[2] * uvs[ptIdxC];
                outMesh.uvs.push_back(u);
            }
            for (size_t i = 0; i < tTris.size(); ++i)
            {
                drawIndices.push_back(tTris[i] + sIndex);
            }
        }
    }

    outMesh.faces.setPrimitiveType(WDPrimitiveSetDraw::PT_Triangles);
    outMesh.faces.setDrawElementUIntData(drawIndices);

    return true;
}


void TIN::processContourLine(double thresholdSq)
{
    WDUnused(thresholdSq);
    //���ո߶ȣ��Ӵ�С����
    std::sort(_inputContourLines.begin(), _inputContourLines.end()
        , [](const ContourLine& left, const ContourLine& right)
        {
            return left.first > right.first;
        });

    for (size_t i = 0; i < _inputContourLines.size(); ++i)
    {
        const ContourLine& line = _inputContourLines[i];

        double maxDis = NumLimits<double>::Max;
        if (i > 0)
        {
            const ContourLine& prev = _inputContourLines[i - 1];
            maxDis = Min(maxDis, line.first - prev.first);
        }
        if (i < _inputContourLines.size() - 1)
        {
            const ContourLine& next = _inputContourLines[i + 1];
            maxDis = Min(maxDis, next.first - line.first);
        }
        // ϸ�ֵȸ���(ע��from��deng)
#if 0
        const auto& polygon = line.second;
        for (size_t i = 0; i < polygon.size(); ++i)
        {
            size_t idx0         = i;
            size_t idx1         = i == polygon.size() - 1 ? 0 : i + 1;
            const DVec2& pt0    = polygon[idx0];
            const DVec2& pt1    = polygon[idx1];
        }
#endif
    }
}

void TIN::addPointPv(const DVec3& point, double thresholdSq)
{
    // ���ˮƽ�����ƽ��С�����ޣ�������������ĵ���Ϊ�����ӵ���ɢ���б���
    DVec2 pt2 = DVec2(point.xy());
    for (auto& tPt : _points)
    {
        const DVec2& tPt2 = tPt.xy();
        double dSq = DVec2::DistanceSq(pt2, tPt2);
        if (dSq <= thresholdSq)
        {
            tPt = (tPt + DVec3(point)) * 0.5;
            return;
        }
    }
    // ����ֱ�����
    _points.push_back(DVec3(point));
}

FVec3Vector TIN::computeNormals()
{
    if (_points.empty())
        return FVec3Vector();

    FVec3Vector rNormals;
    rNormals.reserve(_points.size());

    using MIndices = std::vector<size_t>;
    using MIndicesV = std::vector<MIndices>;
    // ���ʹ��ÿ�����������������
    MIndicesV indicesV;
    indicesV.resize(_points.size());
    for (size_t i = 0; i < _triangles.size(); ++i)
    {
        const auto& triangle = _triangles[i];
        indicesV[triangle.indices[0]].push_back(i);
        indicesV[triangle.indices[1]].push_back(i);
        indicesV[triangle.indices[2]].push_back(i);
    }
    // ���ݹ��øö��������������,���㷨��
    DVec3 tNormal = DVec3::Zero();
    for (size_t i = 0; i < indicesV.size(); ++i)
    {
        const MIndices& indices = indicesV[i];
        if (indices.empty())
        {
            tNormal = DVec3::AxisZ();
        }
        else
        {
            tNormal = DVec3::Zero();
            for (const auto& triIdx : indices)
            {
                const auto& triangle = _triangles[triIdx];
                tNormal += triangle.normal;// *triangle.area;
            }
            tNormal = tNormal / static_cast<double>(indices.size());
            tNormal.normalize();
        }
        rNormals.push_back(FVec3(tNormal));
    }
    return rNormals;
}
FVec2Vector TIN::computeUvs(bool normalized)
{
    if (_points.empty())
        return FVec2Vector();
    FVec2Vector rUvs;
    rUvs.reserve(_points.size());
    for (const auto& pt: _points)
    {
        DVec2 uv = pt.xy() - _aabb2.min;
        if (normalized)
            uv /= _aabb2.size();
        rUvs.push_back(FVec2(uv));
    }
    return rUvs;
}

WD_NAMESPACE_END