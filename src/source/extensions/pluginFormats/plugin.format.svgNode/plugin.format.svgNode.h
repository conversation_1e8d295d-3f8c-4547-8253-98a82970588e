#pragma once

#include "core/extension/WDPluginFormat.h"
#include "core/common/WDFileReader.hpp"
#include "core/WDCore.h"
#include "core/rapidxml/WDRapidxml.h"
#include "core/node/WDNode.h"

WD_NAMESPACE_BEGIN

#define SVG_FORMAT_TEST  false

class PluginFormatSvgNode : public WDPluginFormat
{
public:
    static constexpr const char* Name = "plugin.format.design.svgNode";
private:
    Formats _formats;
public:
    PluginFormatSvgNode(WDCore& app)
        : WDPluginFormat(app)
    {
#if SVG_FORMAT_TEST
        _formats.push_back({".svg","1.0.0.0","svg node"});
#endif
        _formats.push_back({ ".xmls","1.0.0.0","dem xml node" });
        setName(Name);
    }
    virtual ~PluginFormatSvgNode()
    {
    }
public:
    /**
    * @brief ��ȡ����֧�ֵ����ݸ�ʽ
    */
    virtual const Formats& supportFormats(const FormatAttr& attr = FA_Read) const override;
    /**
    *   @brief ���ļ��ж�ȡ����
    *   @param param:�������
    *   @param result:��ȡ�������
    *   @return ��ȡ���ֽ�����,0:ʧ��
    */
    virtual size_t  read(const FormatParam& param,Objects& result)  override;
    /**
    *   @brief �����ж�ȡ����
    *   @param param:�������
    *   @param result:��ȡ�������
    *   @return ��ȡ���ֽ�����
    */
    virtual size_t  read(WDInStream* stream,Objects& result) override;
    /**
    *   @brief  д��������鵽�ļ�
    *   @return ����д����ֽ�����,0:ʧ��
    */
    virtual size_t  write(const FormatParam& param,const Objects& result) override;
    /**
    *   @brief  д��������鵽����
    *   @return ����д����ֽ�����,0:ʧ��
    */
    virtual size_t  write(WDOutStream* stream,const Objects& result) override;
private:
    size_t  readXmlData(const FormatParam& param, Objects& result);

    WDNode::SharedPtr createLoopsAreaModel(const Objects& svgs, const std::string& areaName);
    WDNode::SharedPtr createLoopPointsAreaModel(const Objects& svgs, const std::string& areaName);

    WDNode::SharedPtr createLoopsAreaModel(std::vector<DVec3Vector>& vec
        , const std::string& areaName
        , const Color& color
        , bool bSolid = false
        , float bottomEL = -10.0f);
    WDNode::SharedPtr createLoopPointsAreaModel(std::vector<DVec3Vector>& vec
        , const std::string& areaName
        , const Color& color
        , bool bSolid = false
        , float bottomEL = -10.0f);

    void stringToDVec3(const std::string& polyline);
private:
    WDMutex     _mutex;
    DVec3Vector _outs;
};

WD_NAMESPACE_END
