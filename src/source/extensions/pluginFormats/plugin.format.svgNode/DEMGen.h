#pragma once

#include "core/math/Math.hpp"

WD_NAMESPACE_BEGIN

class TerrainGrid
{
private:
    using DVector = std::vector<double>;
    template <size_t Size>
    using DArray = std::array<double, Size>;
    // ��ɢ���б�
    DVec3Vector _points;
    // ��ɢ��map����֪���xy����,��ѯ��ɢ��߶�
    using MapPoints = std::map<DVec2, double>;
    MapPoints   _mapPoints;
    // ������ɢ��ֱ�������x��y����
    DVector _xDatas;
    DVector _yDatas;
    // ����Ķ�ά�����Χ��(����Z)
    DAabb2  _aabb2;
    // �и�����Ķ����
    DPolygon2 _polygon;
    // ��������
    struct GridData
    {
        // ����λ��
        DVec3 position;
        // ���㷨��
        DVec3 normal;
        // ����uv
        DVec2 uv;
        // �����и��Ķ���ζ���λ���б�
        std::vector<DVec3Vector> positions;
        // �����и��Ķ���ζ��㷨���б�
        std::vector<DVec3Vector> normals;
        // �����и��Ķ���ζ���uv�б�
        std::vector<DVec2Vector> uvs;

        // ��������������λ�ù�ϵ
        enum AreaPos
        {
            //������������
            AP_Out = 0,
            //����ȫ����������
            AP_AllIn,
            //���񲿷���������(˵�������������и�)
            AP_PartIn,
        };
        AreaPos ap = AP_AllIn;
    };
    using Grid = std::vector<std::vector<GridData> >;
    Grid _grid;
    // ���񱻶�����и��ı�������(LineLoop)
    FVec3Vector _sideLines;

    // �������ά�����Χ��
    DAabb3  _aabb3;
public:
    // ���Խ�������
    DVec3Vector _testPoints;
public:
    TerrainGrid();
    ~TerrainGrid();
public:
    // ����
    void reset();

    // �����ɢ��
    void addPoints(const DVec3Vector& points);
    void addPoint(const DVec3& pt);
    // ���������и�����
    void setPolygon(const DVec2Vector& polygon);
    // ��ȡ�и�����
    inline const DPolygon2& polygon() const
    {
        return _polygon;
    }

    // ��ȡ�и���(y����)
    template <class T>
    inline T rowSize() const
    {
        return static_cast<T>(_yDatas.size());
    }
    // ��ȡ�и���(x����)
    template <class T>
    inline T colSize() const
    {
        return static_cast<T>(_xDatas.size());
    }
    // �������������֮����������
    void update();
    // ��ȡ�����2D��Χ��(�����߳�)
    inline const DAabb2& aabb2() const
    {
        return _aabb2;
    }
    // ��ȡ�����3D��Χ��(���߳�)
    inline const DAabb3& aabb3() const
    {
        return _aabb3;
    }
    // ��������������
    bool mesh(FVec3Vector& outVers
        , FVec3Vector& outNors
        , FVec2Vector& outUvs
        , std::vector<uint>& outTris);
    // ��ȡ��������
    inline FVec3Vector sideLines() const
    {
        return _sideLines;
    }

    // ��ȡ��ɢ�����ɵ�������
    DVec3Vector triangles() const;
private:
    // ��ȡ���񶥵�λ��
    template <class T>
    inline TVec3<T> position(size_t row, size_t col) const
    {
        assert(row < this->rowSize<size_t>());
        assert(col < this->colSize<size_t>());
        const auto& d = _grid[row][col];
        return TVec3<T>(d.position);
    }
    // ��ȡ���񶥵㷨��
    template <class T>
    inline TVec3<T> normal(size_t row, size_t col) const
    {
        assert(row < this->rowSize<size_t>());
        assert(col < this->colSize<size_t>());
        const auto& d = _grid[row][col];
        return TVec3<T>(d.normal);
    }
    // ��ȡ���񶥵�uv
    template <class T>
    inline TVec2<T> uv(size_t row, size_t col) const
    {
        assert(row < this->rowSize<size_t>());
        assert(col < this->colSize<size_t>());
        const auto& d = _grid[row][col];
        return TVec2<T>(d.uv);
    }

    // ��ȡ������ĸ������λ��, �� [row][col] ���㿪ʼ����ʱ������
    //  ����Ϊ: p0([row][col]), p1([row][col + 1]), p2([row + 1][col + 1]), p3([row + 1][col])
    template <class T>
    bool gridRect(size_t row, size_t col, TVec3Array<T, 4>& outRect) const
    {
        int rNext = static_cast<int>(row) + 1;
        int cNext = static_cast<int>(col) + 1;
        if (rNext >= this->rowSize<int>() || cNext >= this->colSize<int>())
            return false;
        outRect[0] = this->position<T>(row, col);
        outRect[1] = this->position<T>(row, col + 1);
        outRect[2] = this->position<T>(row + 1, col + 1);
        outRect[3] = this->position<T>(row + 1, col);
        return true;
    }
    // ��ȡ������ĸ�����ķ���, �� [row][col] ���㿪ʼ����ʱ������
    //  ����Ϊ: n0([row][col]), n1([row][col + 1]), n2([row + 1][col + 1]), n3([row + 1][col])
    template <class T>
    bool gridRectNormals(size_t row, size_t col, TVec3Array<T, 4>& outNormals) const
    {
        int rNext = static_cast<int>(row) + 1;
        int cNext = static_cast<int>(col) + 1;
        if (rNext >= this->rowSize<int>() || cNext >= this->colSize<int>())
            return false;
        outNormals[0] = this->normal<T>(row, col);
        outNormals[1] = this->normal<T>(row, col + 1);
        outNormals[2] = this->normal<T>(row + 1, col + 1);
        outNormals[3] = this->normal<T>(row + 1, col);
        return true;
    }
    // ��ȡ������ĸ������uv, �� [row][col] ���㿪ʼ����ʱ������
    //  ����Ϊ: u0([row][col]), u1([row][col + 1]), u2([row + 1][col + 1]), u3([row + 1][col])
    template <class T>
    bool gridRectUVs(size_t row, size_t col, TVec2Array<T, 4>& outUVs) const
    {
        int rNext = static_cast<int>(row) + 1;
        int cNext = static_cast<int>(col) + 1;
        if (rNext >= this->rowSize<int>() || cNext >= this->colSize<int>())
            return false;
        outUVs[0] = this->uv<T>(row, col);
        outUVs[1] = this->uv<T>(row, col + 1);
        outUVs[2] = this->uv<T>(row + 1, col + 1);
        outUVs[3] = this->uv<T>(row + 1, col);
        return true;
    }

    /**
     * @brief ����ĳ������Ķ���λ�ø߶�
     *  ͨ��ָ������ɢ�̵߳���и߶Ȳ���,Ŀǰ���õķ�����:�Ȳ��õ����������ʷ֣���������������Ȩ�ط�����߳�ֵ
     *  ����Щ��������Ҫ����ʵ��:
     *      1.�������Ȩ��ֵ��
     *      2.������ֵ��
     *      3.̩ɭ����β�ֵ��
     *      4.������ֵ��
     *      5.�����������ֵ��
    */
    template <class T>
    T computeHeight(size_t row, size_t col) const
    {
        // ����Ķ�ά����
        TVec2<T> tPt(_xDatas[col], _yDatas[row]);
        // ���ȿ������񽻲���Ƿ���Դ���ݵ�
        auto fItr = _mapPoints.find(tPt);
        if (fItr != _mapPoints.end())
        {
            return static_cast<T>(fItr->second);
        }
        // ��������������������������߳�
        T tmpZ = T(0);
        for (const auto& tri : _tris)
        {
            if (tri.sampAlt(tPt, tmpZ))
                return tmpZ;
        }
        if (_points.empty())
        {
            return 0.0;
        }
        // ����������������⣬˵�����񽻲����������ɢ�㹹�ɵ�͹��֮�⣬�������߶����������ĸ߶�
        T minDis = NumLimits<T>::Max;
        DVec3 nearPt = _points[0];
        for (const auto& pt : _points)
        {
            T tDis = DVec2::DistanceSq(pt.xy(), DVec2(tPt));
            if (tDis < minDis)
            {
                minDis = tDis;
                nearPt = pt;
            }
        }
        return T(nearPt.z());
    }
    /**
     * @brief ����ĳ������Ķ��㷨��
     *  ��ȡ���ڵ��ĸ�ƽ��(����������˵,���Ӧ��ֻ���ĸ�, ��������Ǳ�Ե����,��С���ĸ�)���������ĸ�ƽ��ķ��ߣ����Ҽ�Ȩƽ��
    */
    template <class T>
    TVec3<T> computeNormal(size_t row, size_t col) const
    {
        DVec3 v = position<double>(row, col);

        int rowCnt = this->rowSize<int>();
        int colCnt = this->colSize<int>();

        int rPrev = static_cast<int>(row) - 1;
        int rNext = static_cast<int>(row) + 1;
        int cPrev = static_cast<int>(col) - 1;
        int cNext = static_cast<int>(col) + 1;

        // face lb
        DVec3 nLb = DVec3::Zero();
        if (rPrev >= 0 && cPrev >= 0)
        {
            DVec3 v0 = position<double>(rPrev, cPrev);
            DVec3 v1 = position<double>(rPrev, col);
            nLb = DTriangle3::Normal(v0, v1, v);
        }
        // fact rb
        DVec3 nRb = DVec3::Zero();
        if (rPrev >= 0 && cNext < colCnt)
        {
            DVec3 v0 = position<double>(rPrev, col);
            DVec3 v1 = position<double>(rPrev, cNext);
            nRb = DTriangle3::Normal(v0, v1, v);
        }
        // face lt
        DVec3 nLt = DVec3::Zero();
        if (rNext < rowCnt && cPrev >= 0)
        {
            DVec3 v0 = position<double>(row, cPrev);
            DVec3 v1 = position<double>(rNext, col);
            nLt = DTriangle3::Normal(v0, v, v1);
        }
        // face rt
        DVec3 nRt = DVec3::Zero();
        if (rNext < rowCnt && cNext < colCnt)
        {
            DVec3 v0 = position<double>(row, cNext);
            DVec3 v1 = position<double>(rNext, col);
            nRt = DTriangle3::Normal(v, v0, v1);
        }
        // ƽ��
        DVec3 n = DVec3::Normalize((nLt + nLb + nRt + nRb) * 0.25);

        return TVec3<T>(n);
    }
    /**
     * @brief ����ĳ�������uv����
     *  �����ά�����Χ��ȷ��uv�������С��Χ,���ж����uv�����һ��([0,1]����)
    */
    template <class T>
    TVec2<T> computeUv(size_t row, size_t col) const
    {
        // ����Ķ�ά����
        DVec2 pt    = this->position<double>(row, col).xy();
        // ��Χ�д�С
        DVec2 sz    = _aabb2.size();
        // ��Χ����С������
        DVec2 min   = _aabb2.min();
        // ��һ�����uvֵ
        DVec2 d     = (pt - min) / sz;

        return TVec2<T>(d);
    }

    /**
     * @brief  ָ�������ڵ�����һ��������Լ������Rect���и߶�,����,uv����
     * @param pt �����ڵ�����һ��
     * @param rectP �����ĸ�����λ��
     * @param rectN �����ĸ����㷨��
     * @param rectU �����ĸ�����uv
     * @param outP ����Ĳ���λ��(���߶�)
     * @param outN ����Ĳ�������
     * @param outU ����Ĳ���uv
     * @return �Ƿ�����ɹ�
    */
    bool sampPNU(const DVec2& pt
        , const DVec3Array<4>& rectP
        , const DVec3Array<4>& rectN
        , const DVec2Array<4>& rectU
        , DVec3& outP
        , DVec3& outN
        , DVec2& outU) const;
    /**
     * @brief ָ��������һ���Լ��õ������������Rect�Ķ������飬�����߶�
    */
    double sampHeight(const DVec2& pt, const DVec3Array<4>& rectP) const;
    /**
     * @brief ָ�������ڵ�����һ�㣬�����߶�
    */
    double sampHeight(const DVec2& pt) const;
private: 
    static double SortAddData(DVector& datas, const double& data);
private:
    struct Tri
    {
    public:
        // �����ΰ�Χ��   2D
        DAabb2 aabb = DAabb2::Null();
        // �����ε���������  2D
        DVec2Array<3> points;
        // �����ε����������Ӧ�ĸ߳� 
        DArray<3> alts;
        // �������ά�㣬Ŀǰ��������ֻ��Tri3�ṩ�˷���
        DTriangle3 tri3;
    public:
        Tri(const DVec2Array<3>& points, const DArray<3>& alts)
        {
            this->points = points;
            this->alts = alts;
            init();
        }
        Tri(DVec2Array<3>&& points, DArray<3>&& alts)
        {
            this->points = std::forward<DVec2Array<3> >(points);
            this->alts = std::forward<DArray<3> >(alts);
            init();
        }
    public:
        // �����߳�
        // �ɹ���������true��ʧ��(�㲻���ڸ�������)����false
        bool sampAlt(const DVec2& pt, double& outAlt) const
        {
            if (!aabb.contains(pt))
                return false;
            DVec3 tPt   = DVec3(pt - aabb.center());
            DVec3 b     = tri3.baryCoord(tPt);
            const auto& u = b.x();
            const auto& v = b.y();
            const auto& w = b.z();

            static constexpr double e = NumLimits<float>::Epsilon;
            // ���������������,�� ��������� ��������֮��Ϊ1������������������0
            if (u < -e || v < -e || w < -e)
                return false;
            outAlt = u * alts[0] + v * alts[1] + w * alts[2];
            return true;
        }
    private:
        void init()
        {
            if (points.empty())
                return;
            aabb.expandByPoint(points[0]);
            aabb.expandByPoint(points[1]);
            aabb.expandByPoint(points[2]);
            tri3[0] = DVec3(points[0] - aabb.center());
            tri3[1] = DVec3(points[1] - aabb.center());
            tri3[2] = DVec3(points[2] - aabb.center());
        }
    };
    using Tris = std::vector<Tri>;
    Tris _tris;

    // ����ɢ��ʹ�õĵ����������ʷ�
    // �������ɵ������λ���
    void triangulationPoints();

};

WD_NAMESPACE_END
