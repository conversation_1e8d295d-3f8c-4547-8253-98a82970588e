
#include    "TerrainGeometryGen.h"

#include    "Tesslation.h"
#include    "TerrainGridGen.h"
#include    "TIN.h"

WD_NAMESPACE_BEGIN


WDGeometry::SharedPtr LoopsGen(const std::vector<DVec3Vector>& loops
    , bool sideAndBottom
    , float depth)
{
    if (loops.empty())
        return nullptr;

#if 0
    TIN::ContourLines lines;
    lines.reserve(loops.size());
    for (const auto& loop : loops)
    {
        lines.push_back({});
        lines.back().first = loop.back().z();
        lines.back().second.reserve(loop.size());
        for (const auto& pt : loop)
        {
            lines.back().second.push_back(pt.xy());
        }
    }
    TIN tin;
    tin.addContourLines(lines);
    tin.update();

    return nullptr;
#else
    TIN tin;
    for (const auto& loop : loops)
    {
        tin.addPoints(loop);
    }
    tin.update();

    // ��������
    DVec2Vector area = TVec3VectorToVec2Vector<double, double>(loops[0]);
    // �׶������
    std::vector<DVec2Vector> holes;
    holes.reserve(loops.size() - 1);
    for (size_t i = 1; i < loops.size(); ++i)
    {
        holes.push_back({});
        DVec2Vector vs = TVec3VectorToVec2Vector<double, double>(loops[i]);
        holes.back() = std::move(vs);
    }

    TIN::OutMesh outMesh;
    bool bMesh = tin.mesh(area, holes, outMesh);
    if (!bMesh)
        return nullptr;

    WDGeometry::SharedPtr pGeom = WDGeometry::MakeShared();
    pGeom->setMesh(WDMesh::MakeShared());
    auto& poss = pGeom->mesh()->positions();
    auto& nors = pGeom->mesh()->normals();

    // ����
    {
        poss = std::move(outMesh.vertices);
        nors = std::move(outMesh.normals);

        pGeom->mesh()->addPrimitiveSet(outMesh.faces, RenderMode::RM_Face);
    }

    // ����
    if (sideAndBottom)
    {
        for (const auto& loop : loops)
        {

            WDPrimitiveSetDraw pri;
            pri.setPrimitiveType(WDPrimitiveSetDraw::PT_TriangleStrip);

            FVec3Vector tVertices;
            FVec3Vector tNormals;
            tVertices.reserve((loop.size() + 1) * 2);
            tNormals.reserve((loop.size() + 1) * 2);
            for (size_t i = 0; i < loop.size(); ++i)
            {
                size_t idx = i;
                size_t prevIdx = (i == 0) ? loop.size() - 1 : i - 1;
                size_t nextIdx = (i == loop.size() - 1) ? 0 : i + 1;

                const auto& prevPt  = loop[prevIdx];
                const auto& pt      = loop[idx];
                const auto& nextPt  = loop[nextIdx];

                FVec3 prevBot           = FVec3(FVec2(prevPt.xy()), depth);
                const FVec3& ptTop      = FVec3(pt);
                FVec3 ptBot             = FVec3(FVec2(pt.xy()), depth);
                const FVec3& nextTop    = FVec3(nextPt);

                FVec3 n0    = FTriangle3::Normal(prevBot, ptBot, ptTop);
                FVec3 n1    = FTriangle3::Normal(ptTop, ptBot, nextTop);
                FVec3 n     = FVec3::Normalize(n0 + n1);

                tVertices.push_back(ptTop);
                tNormals.push_back(n);
                tVertices.push_back(ptBot);
                tNormals.push_back(n);
            }
            tVertices.push_back(tVertices[0]);
            tNormals.push_back(tNormals[0]);
            tVertices.push_back(tVertices[1]);
            tNormals.push_back(tNormals[1]);

            uint sIndex = static_cast<uint>(poss.size());
            pri.setDrawArrayData(sIndex, (uint)(tVertices.size()));
            pGeom->mesh()->addPrimitiveSet(pri, RenderMode::RM_Face);

            poss.insert(poss.end(), tVertices.begin(), tVertices.end());
            nors.insert(nors.end(), tNormals.begin(), tNormals.end());
        }
    }

    // ����
    if (sideAndBottom)
    {
        WDPrimitiveSetDraw pri;
        pri.setPrimitiveType(WDPrimitiveSetDraw::PT_Triangles);

        FVec3Vector tVertices;
        FVec3Vector tNormals;

        TEarcut<DVec2>::HolePolygon holePolygon;
        holePolygon.reserve(holes.size() + 1);
        holePolygon.push_back(area);

        size_t vSZ = area.size();
        for (const auto& hole: holes)
        {
            vSZ += hole.size();
            holePolygon.push_back(hole);
        }

        tVertices.reserve(vSZ);
        tNormals.reserve(vSZ);

        for (const auto& pt: area)
        {
            tVertices.push_back(FVec3(FVec2(pt), depth));
            tNormals.push_back(FVec3::AxisNZ());
        }
        for (const auto& hole : holes)
        {
            for (const auto& pt : hole)
            {
                tVertices.push_back(FVec3(FVec2(pt), depth));
                tNormals.push_back(FVec3::AxisNZ());
            }
        }

        // ����
        auto tris = TEarcut<DVec2>::Exec(holePolygon);

        WDPrimitiveSetDraw::DrawElementUIntData data;
        data.reserve(tris.size());

        uint sIndex = static_cast<uint>(poss.size());
        for (size_t i = 0; i < tris.size(); i += 3)
        {
            data.push_back((uint)(tris[i + 2]) + sIndex);
            data.push_back((uint)(tris[i + 1]) + sIndex);
            data.push_back((uint)(tris[i + 0]) + sIndex);
        }
        pri.setDrawElementUIntData(data);
        pGeom->mesh()->addPrimitiveSet(pri, RenderMode::RM_Face);

        poss.insert(poss.end(), tVertices.begin(), tVertices.end());
        nors.insert(nors.end(), tNormals.begin(), tNormals.end());
    }

    // ����
    {
        for (const auto& tLoop : loops)
        {
            FVec3Vector tVertices;
            for (const auto& pt : tLoop)
            {
                tVertices.push_back(FVec3(pt));
            }
            WDPrimitiveSetDraw pri;
            pri.setPrimitiveType(WDPrimitiveSetDraw::PT_LineLoop);

            uint sIndex = (uint)(poss.size());
            pri.setDrawArrayData(sIndex, (uint)(tLoop.size()));
            pGeom->mesh()->addPrimitiveSet(pri, RenderMode::RM_Edge);

            poss.insert(poss.end(), tVertices.begin(), tVertices.end());
        }
    }

    pGeom->mesh()->computeAabb();

    return pGeom;
#endif
}

WDGeometry::SharedPtr LoopPointsGen(const DVec3Vector& loop
    , const DVec3Vector& points
    , bool sideAndBottom
    , float depth)
{
    if (loop.empty() || points.empty())
        return nullptr;

    TIN tin;
    tin.addPoints(loop);
    tin.addPoints(points);
    tin.update();

    DVec2Vector area;
    area.reserve(loop.size());
    for (const auto& pt : loop)
    {
        area.push_back(pt.xy());
    }
    TIN::OutMesh outMesh;
    bool bMesh = tin.mesh(area, outMesh);
    if (!bMesh)
        return nullptr;

    WDGeometry::SharedPtr pGeom = WDGeometry::MakeShared();
    pGeom->setMesh(WDMesh::MakeShared());
    auto& poss = pGeom->mesh()->positions();
    auto& nors = pGeom->mesh()->normals();
    // ����
    {
        poss = std::move(outMesh.vertices);
        nors = std::move(outMesh.normals);

        pGeom->mesh()->addPrimitiveSet(outMesh.faces, RenderMode::RM_Face);
    }
    // ����
    if(sideAndBottom)
    {
        WDPrimitiveSetDraw pri;
        pri.setPrimitiveType(WDPrimitiveSetDraw::PT_TriangleStrip);

        FVec3Vector tVertices;
        FVec3Vector tNormals;
        tVertices.reserve((loop.size() + 1) * 2);
        tNormals.reserve((loop.size() + 1) * 2);
        for (size_t i = 0; i < loop.size(); ++i)
        {
            size_t idx = i;
            size_t prevIdx = (i == 0) ? loop.size() - 1 : i - 1;
            size_t nextIdx = (i == loop.size() - 1) ? 0 : i + 1;

            const auto& prevPt  = loop[prevIdx];
            const auto& pt      = loop[idx];
            const auto& nextPt  = loop[nextIdx];

            FVec3 prevBot           = FVec3(FVec2(prevPt.xy()), depth);
            const FVec3& ptTop      = FVec3(pt);
            FVec3 ptBot             = FVec3(FVec2(pt.xy()), depth);
            const FVec3& nextTop    = FVec3(nextPt);

            FVec3 n0    = FTriangle3::Normal(prevBot, ptBot, ptTop);
            FVec3 n1    = FTriangle3::Normal(ptTop, ptBot, nextTop);
            FVec3 n     = FVec3::Normalize(n0 + n1);


            tVertices.push_back(ptTop);
            tNormals.push_back(n);
            tVertices.push_back(ptBot);
            tNormals.push_back(n);

        }
        tVertices.push_back(tVertices[0]);
        tNormals.push_back(tNormals[0]);
        tVertices.push_back(tVertices[1]);
        tNormals.push_back(tNormals[1]);

        uint sIndex = static_cast<uint>(poss.size());
        pri.setDrawArrayData(sIndex, (uint)(tVertices.size()));
        pGeom->mesh()->addPrimitiveSet(pri, RenderMode::RM_Face);

        poss.insert(poss.end(), tVertices.begin(), tVertices.end());
        nors.insert(nors.end(), tNormals.begin(), tNormals.end());

    }
    // ����
    if (sideAndBottom)
    {
        WDPrimitiveSetDraw pri;
        pri.setPrimitiveType(WDPrimitiveSetDraw::PT_Triangles);

        auto tris = TEarcut<DVec2>::Exec(area);

        FVec3Vector tVertices;
        FVec3Vector tNormals;
        tVertices.reserve(area.size());
        tNormals.reserve(area.size());

        for (const auto& pt: area)
        {
            tVertices.push_back(FVec3(FVec2(pt), depth));
            tNormals.push_back(FVec3::AxisNZ());
        }
        WDPrimitiveSetDraw::DrawElementUIntData data;
        data.reserve(tris.size());
        uint sIndex = static_cast<uint>(poss.size());
        for (size_t i = 0; i < tris.size(); i += 3)
        {
            data.push_back((uint)(tris[i + 2]) + sIndex);
            data.push_back((uint)(tris[i + 1]) + sIndex);
            data.push_back((uint)(tris[i + 0]) + sIndex);
        }
        pri.setDrawElementUIntData(data);
        pGeom->mesh()->addPrimitiveSet(pri, RenderMode::RM_Face);

        poss.insert(poss.end(), tVertices.begin(), tVertices.end());
        nors.insert(nors.end(), tNormals.begin(), tNormals.end());
    }

    // ����
    {
        FVec3Vector tVertices;
        tVertices.reserve(loop.size());
        for (const auto& pt : loop)
        {
            tVertices.push_back(FVec3(pt));
        }
        WDPrimitiveSetDraw pri;
        pri.setPrimitiveType(WDPrimitiveSetDraw::PT_LineLoop);

        size_t sIndex = poss.size();
        pri.setDrawArrayData((uint)(sIndex), (uint)(loop.size()));
        pGeom->mesh()->addPrimitiveSet(pri, RenderMode::RM_Edge);

        poss.insert(poss.end(), tVertices.begin(), tVertices.end());
    }

    pGeom->mesh()->computeAabb();

    return pGeom;

}

WD_NAMESPACE_END
