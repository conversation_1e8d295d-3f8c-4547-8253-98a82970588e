
#include    "plugin.format.svgNode.h"
#include    "core/geometry/standardPrimitives/WDGeometryStdPris.h"
#include    "core/common/WDFileWriter.hpp"
#include    "core/businessModule/design/WDBMDesignMgr.h"
#include    "core/businessModule/design/others/WDBMDOthersSub.h"

#include    "Tesslation.h"
#include    "../../source/utilLib/util.isoSymbol/WDSvgReader.h"
#include    "gdal_alg.h"
#include    <random>

#include    "TerrainGridGen.h"
#include    "TIN.h"

#include    "core/common/WDContext.h"
#include    "core/viewer/WDViewer.h"
#include    "core/scene/WDScene.h"
#include    "core/cameras/WDCamera.h"

#include    "core/material/WDDrawHelpter.h"

#include    "TerrainGeometryGen.h"
#include    "core/geometry/standardPrimitives/WDGeometrySphere.h"

WD_NAMESPACE_BEGIN


class PointRender: public WDRenderObject
{
    WD_DECL_OBJECT(PointRender)
public:
    FVec3Vector points;
    WDGeometrySphere::SharedPtr pSphere;
    WDMaterialPhong _material;

    PointRender() 
    {
        pSphere = WDGeometrySphere::MakeShared(1.0f); 
        _renderLayer = { RL_Scene};
    }

    virtual DAabb3   aabb() const
    {
        return DAabb3::Null();
    }
protected:
    /**
    *   @brief ����,��Ҫ����д
    */
    virtual void    update(WDContext&)
    {

    }
    void render(WDContext& context)
    {
        if (points.empty())
            return;

        float       sphereRadiu =   10.0f;
        WDInstances insts;
        insts.resize(points.size());
        for (size_t i = 0; i < points.size(); ++i)
        {
            insts[i]._instanceId    = (uint)(i);
            insts[i]._color         = Color::red;
            float pixelU            = static_cast<float>(context.camera().pixelU(DVec3(points[i])));
            insts[i]._local         = FMat4::Compose(FVec3(points[i]), FVec3(pixelU * sphereRadiu));
        }

        WDDrawHelpter::Guard dg(context, _material);
        dg.drawInstance(insts, *(pSphere->mesh()), WD::RenderMode::RM_Face);
    }
};

PointRender::SharedPtr sPRender = nullptr;

bool CalcVerts(WDSvgObject::SvgObjects& objects, DVec3Vector& outRingD, FVec3Vector& outRingF)
{
    for (auto& var : objects)
    {
        WDSvgObject::SharedPtr  obj = var->toPtr<WDSvgObject>();
        if (obj.get() == nullptr)
            continue;
        // ��ȡ����
        FVec3Vector verts;
        obj->toData(verts, 10);

        if (obj->_type == SVG_TypePath)
        {
            FVec2Vector verts11;
            for (auto vert : verts)
            {
                verts11.push_back(FVec2(vert.x, vert.y));
            }
            WDPropertyFVec2Vector pty0("123", verts11);
            std::string  str11 = pty0.valueToString();
            std::reverse(verts11.begin(), verts11.end());
            pty0.setValue(verts11);
            std::string  str22 = pty0.valueToString();

            if (str11 == str22)
            {
                assert(false);
            }
        }

        // ����ת������������Ϣ��Ӧ��
        // ÿһ�������дӸ��ڵ�̳����ģ������Լ��ľ����ڻ���֮ǰ��Ԥ�ȼ������
        // ��֤������ȷ��
        FVec3Vector rVerts;
        rVerts.reserve(verts.size());
        for (size_t i = 0; i < verts.size(); i++)
        {
            Vec2    data = var->_attr._transform * Vec2(verts[i].x, verts[i].y);
            verts[i].x = (float)(data.x);
            verts[i].y = (float)(data.y);
            rVerts.push_back(verts[i]);
        }
        for (const auto& pt : rVerts)
        {
            outRingD.push_back(DVec3(pt));
        }
        outRingF.insert(outRingF.end(), rVerts.begin(), rVerts.end());
    }
    return outRingD.size() >= 3;
}

const Formats& PluginFormatSvgNode::supportFormats(const FormatAttr& attr ) const
{
    switch (attr)
    {
    case FormatAttr::FA_Read:
        return _formats;
    case FormatAttr::FA_Write:
        return _formats;
    default:
        break;
    }
    return WDPluginFormat::FormatNull;
}

enum AreaType
{
    AT_Unknown = 0,     // δ֪
    AT_Loops = 1,       // �����, һ���ⲿ��(�������������ʱ��) + ����ڲ���(�����������˳ʱ��)���ڲ��������γɿ׶�
    AT_LoopPoints = 2,  // һ����(�������������ʱ��) + �ڻ��ڲ��Ķ����(��ɢ�㣬�޹���)
};

size_t  PluginFormatSvgNode::read(const FormatParam& param,Objects& objs)
{
#if SVG_FORMAT_TEST
    // ������ֱ�Ӷ�ȡSVG�ļ������ɵ��εĴ���,�������ڲ���
    WDSvgReader     reader;
    Objects         svgs;
    bool rRet = reader.loadFromFile(param.fileName.c_str(), svgs) ? 1 : 0;
    if (rRet)
    {
        // ��ȡ��������
        AreaType aType = AreaType::AT_Unknown;
        for (const auto& var : svgs)
        {
            WDSvgObject* pSvg = dynamic_cast<WDSvgObject*>(var.get());
            if (pSvg == nullptr)
                continue;
            const std::string& name = pSvg->name();
            if (name == "loops")
            {
                aType = AreaType::AT_Loops;
            }
            else if (name == "loopPoints")
            {
                aType = AreaType::AT_LoopPoints;
            }
        }

        switch (aType)
        {
        case WD::AT_Loops:
        {
            WDNode::SharedPtr pNode = createLoopsAreaModel(svgs, param.fileName);
            if (pNode == nullptr)
            {
                assert(false && "�������ݽ���ʧ��!");
                return 0;
            }
            objs.push_back(pNode);
            return objs.size();
        }
        break;
        case WD::AT_LoopPoints:
        {
            WDNode::SharedPtr pNode = createLoopPointsAreaModel(svgs, param.fileName);
            if (pNode == nullptr)
            {
                assert(false && "�������ݽ���ʧ��!");
                return 0;
            }
            objs.push_back(pNode);
            return objs.size();
        }
        break;
        default:
            assert(false && "δ֪��������!");
            break;
        }
        return 0;
    }
#endif
    size_t res = this->readXmlData(param, objs);
    if (res != 0)
        return res;
    assert(false && "��Ч�����������ļ�!");
    return 0;

}

size_t  PluginFormatSvgNode::read(WDInStream* ,Objects& )
{
    return  0;
}

size_t  PluginFormatSvgNode::write(const FormatParam& ,const Objects& )
{ 
    return  0;
}

size_t  PluginFormatSvgNode::write(WDOutStream* ,const Objects& )
{
    return  0;
}

size_t PluginFormatSvgNode::readXmlData(const FormatParam& param, Objects& result)
{
    FILE* pFile = fopen(param.fileName.c_str(), "rb");
    if (pFile == nullptr)
        return 0;
    fseek(pFile, 0, SEEK_END);
    size_t  nLen = ftell(pFile);
    fseek(pFile, 0, SEEK_SET);
    char* buf = new char[nLen + 1];
    memset(buf, 0, nLen + 1);
    fread(buf, nLen, 1, pFile);
    fclose(pFile);

    WD::XMLDoc doc;
    WD::XMLNode* rootNode = 0;
    doc.parse<0>((char*)buf);
    rootNode = doc.first_node();
    if (rootNode == nullptr)
        return 0;
    // ��ȡ��������
    AreaType aType = AreaType::AT_Unknown;

    auto pXmlAttrType = rootNode->first_attribute("type");
    if (pXmlAttrType == nullptr)
        return 0;
    std::string type = pXmlAttrType->value();

    auto pXmlAttrName = rootNode->first_attribute("name");
    std::string name = "unknown"; 
    if (pXmlAttrName != nullptr)
        name = pXmlAttrName->value();

    auto pXmlAttrColor = rootNode->first_attribute("color");
    Color color = Color::white;
    if (pXmlAttrColor != nullptr)
        color.fromString(pXmlAttrColor->value());

    auto pXmlAttrSolid = rootNode->first_attribute("solid");
    bool bSolid = false;
    if (pXmlAttrSolid != nullptr)
        bSolid = FromString<bool>(pXmlAttrSolid->value());

    float bottomEL = -10.0f;
    auto pXmlAttrBottomEL = rootNode->first_attribute("bottomEL");
    if (pXmlAttrBottomEL != nullptr)
    {
        bool bOk = false;
        float tValue = 0.0f;
        tValue = FromString<float>(pXmlAttrBottomEL->value(), &bOk);
        if (bOk)
            bottomEL = tValue;
    }

    if (type == "loops")
    {
        aType = AreaType::AT_Loops;
    }
    else if (type == "loopPoints")
    {
        aType = AreaType::AT_LoopPoints;
    }

    WD::XMLNode* loopNode = rootNode->first_node("Loop");
    std::vector<DVec3Vector> points;
    while (loopNode != nullptr)
    {
        WD::XMLNode* polylineNode = loopNode->first_node("polyline");
        while (polylineNode != nullptr)
        {
            auto point = polylineNode->first_attribute("points")->value();
            this->stringToDVec3(point);
            polylineNode = polylineNode->next_sibling("polyline");
        }
        points.push_back(_outs);
        _outs.clear();
        loopNode = loopNode->next_sibling("Loop");
    }

    switch (aType)
    {
    case WD::AT_Loops:
    {
        WDNode::SharedPtr pNode = createLoopsAreaModel(points, name, color, bSolid, bottomEL);
        if (pNode == nullptr)
        {
            assert(false && "�������ݽ���ʧ��!");
            return 0;
        }
        result.push_back(pNode);
        return result.size();
    }
    break;
    case WD::AT_LoopPoints:
    {
        WDNode::SharedPtr pNode = createLoopPointsAreaModel(points, name, color, bSolid, bottomEL);
        if (pNode == nullptr)
        {
            assert(false && "�������ݽ���ʧ��!");
            return 0;
        }
        result.push_back(pNode);
        return result.size();
    }
    break;
    default:
        assert(false && "δ֪��������!");
        break;
    }
    return 0;
}

WDNode::SharedPtr PluginFormatSvgNode::createLoopsAreaModel(const Objects& svgs, const std::string& areaName)
{
    auto& designMgr = _app.designMgr();
    // �������б�
    std::vector<DVec3Vector > rings;

    for (auto& var : svgs)
    {
        WDSvgObject* pSvg = dynamic_cast<WDSvgObject*>(var.get());
        if (pSvg == nullptr)
            continue;
        DVec3Vector dRing;
        FVec3Vector fRing;

        if (pSvg->name() == "outerRing")
        {
            CalcVerts(pSvg->_childs, dRing, fRing);
            // �������ɸ߶�
            for (auto& pt : dRing)
            {
                pt[2] = 2.20;
            }
        }
        else if (pSvg->name() == "innerRing")
        {
            CalcVerts(pSvg->_childs, dRing, fRing);
            // �������ɸ߶�
            for (auto& pt : dRing)
            {
                pt[2] = 10.20;
            }
        }
        else
        {
            continue;
        }

        if (dRing.empty())
            continue;

        // ��
        DVec2Vector polygonV;
        polygonV.reserve(dRing.size());
        for (size_t i = 0; i < dRing.size(); i++)
        {
            polygonV.push_back(dRing[i].xy());
        }
        if (rings.empty())
        {
            if (!DPolygon2::IsCCW(polygonV))
            {
                std::reverse(dRing.begin(), dRing.end());
            }
        }
        else
        {
            if (!DPolygon2::IsCW(polygonV))
            {
                std::reverse(dRing.begin(), dRing.end());
            }
        }
        rings.push_back(std::move(dRing));
    }

    if (rings.empty())
        return nullptr;

    auto pAreaData = WDBMDPR::MakeShared();
    auto pAreaNode = designMgr.create(pAreaData, areaName);

    WDGeometry::SharedPtr rGeom = LoopsGen(rings);
    if (rGeom != nullptr)
    {
        //pAreaData->addGeom( rGeom);

        pAreaNode->setBasicColor(Color(150, 150, 150, 255));
    }

    return pAreaNode;

}
WDNode::SharedPtr PluginFormatSvgNode::createLoopPointsAreaModel(const Objects& svgs, const std::string& areaName)
{
    auto& designMgr = _app.designMgr();
    // ����
    DVec3Vector loop;
    // ���������ɢ��
    DVec3Vector points;

    for (auto& var : svgs)
    {
        WDSvgObject* pSvg = dynamic_cast<WDSvgObject*>(var.get());
        if (pSvg == nullptr)
            continue;

        if (pSvg->name() == "ring")
        {
            DVec3Vector dRing;
            FVec3Vector fRing;
            CalcVerts(pSvg->_childs, dRing, fRing);
            loop = std::move(dRing);
            // �������ɸ߶�
            for (auto& pt : loop)
            {
                pt[2] = 10.20;
            }
        }
        else if (pSvg->name() == "points")
        {
            DVec3Vector dPoints;
            FVec3Vector fPoints;
            CalcVerts(pSvg->_childs, dPoints, fPoints);
            points = std::move(dPoints);

            // �������ɸ߶�
            for (auto& pt : points)
            {
                pt[2] =  20.30;
            }
        }
        else
        {
            continue;
        }
    }

    if (loop.empty() || points.empty())
        return nullptr;

    auto pAreaData = WDBMDPR::MakeShared();
    auto pAreaNode = designMgr.create(pAreaData, areaName);

    WDGeometry::SharedPtr rGeom = LoopPointsGen(loop, points);
    if (rGeom != nullptr)
    {
        //pAreaData->addGeom(rGeom);

        pAreaNode->setBasicColor(Color(0, 150, 0, 255));
    }

    return pAreaNode;
}


WDNode::SharedPtr PluginFormatSvgNode::createLoopsAreaModel(std::vector<DVec3Vector>& vec
    , const std::string& areaName
    , const Color& color
    , bool bSolid
    , float bottomEL)
{
    auto& designMgr = _app.designMgr();
    auto pAreaData = WDBMDPR::MakeShared();
    auto pAreaNode = designMgr.create(pAreaData, areaName);

    DVec2Vector polygonV;
    std::vector<DVec3Vector> rings;

    for (int i = 0; i < vec.size(); ++i)
    {
        DVec3Vector dRing = vec.at(i);
        polygonV.reserve(vec.size());
        for (size_t j = 0; j < dRing.size(); j++)
        {
            polygonV.push_back(dRing[j].xy());
        }
        if (rings.empty())
        {
            if (!DPolygon2::IsCCW(polygonV))
            {
                std::reverse(dRing.begin(), dRing.end());
            }
        }
        else
        {
            if (!DPolygon2::IsCW(polygonV))
            {
                std::reverse(dRing.begin(), dRing.end());
            }
        }
        rings.push_back(std::move(dRing));
    }

    WDGeometry::SharedPtr rGeom = LoopsGen(rings, bSolid, bottomEL);
    if (rGeom != nullptr)
    {
        //pAreaData->addGeom(rGeom);

        pAreaNode->setBasicColor(color);
    }
    return pAreaNode;
}
WDNode::SharedPtr PluginFormatSvgNode::createLoopPointsAreaModel(std::vector<DVec3Vector>& vec
    , const std::string& areaName
    , const Color& color
    , bool bSolid
    , float bottomEL)
{
    auto& designMgr = _app.designMgr();
    auto pAreaData = WDBMDPR::MakeShared();
    auto pAreaNode = designMgr.create(pAreaData, areaName);

    WDGeometry::SharedPtr rGeom = LoopPointsGen(vec.at(0), vec.at(1), bSolid, bottomEL);
    if (rGeom != nullptr)
    {
        //pAreaData->addGeom(rGeom);

        pAreaNode->setBasicColor(color);
    }
    return pAreaNode;
}

void PluginFormatSvgNode::stringToDVec3(const std::string& polyline)
{
    if (polyline.empty())
        return;
    // �������
    std::smatch             smatch;
    // ƥ�両����
    std::regex partten("-?\\s*[0-9]\\d*\\.?\\d*");
    // �������ĸ���������
    std::vector<double> values;
    // ����
    std::sregex_iterator itr(polyline.begin(), polyline.end(), partten);
    for (; itr != std::sregex_iterator(); ++itr)
    {
        smatch = *itr;
        std::string strValue = smatch[0];
        double value = atof(strValue.c_str());
        values.push_back(value);
    }
    int num = 0;
    while (num < values.size())
    {
#ifdef  SVG_COORD
        _outs.push_back(DVec3(values.at(num), - values.at(num + 1), values.at(num + 2)) * 1000);
#else
        _outs.push_back(DVec3(values.at(num), values.at(num + 1), values.at(num + 2)));
#endif
        num += 3;
    }
}

WD_NAMESPACE_END

