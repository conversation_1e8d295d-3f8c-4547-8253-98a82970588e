
#include    "plugin.format.json.h"
#include    "core/businessModule/serialize/WDBMNodeSerialize.h"
#include    "core/rapidjson/WDRapidjson.h"
#include    "core/businessModule/catalog/WDBMCatalogMgr.h"
#include    "core/businessModule/design/WDBMDesignMgr.h"

#define JSON_TEST true
#define WD_TEST false
#define XML_TEST false



WD_NAMESPACE_BEGIN

/**
 * @brief wd��ʽ ������ͷ
*/
struct FormatWDChunkHeader
{
    // �ڵ����ݿ��С(������������ڵ�)
    uint64  chunkSize = 0;
    // ��ǰ��ĸ��ڵ����
    uint    nodeCount = 0;
    // Ԥ��
    uint    placeHolder = 0;
};

const Formats& PluginFormatJson::supportFormats(const FormatAttr& attr) const
{
    switch (attr)
    {
    case FormatAttr::FA_Read:
        return _formats;
    case FormatAttr::FA_Write:
        return _formats;
    default:
        break;
    }
    return WDPluginFormat::FormatNull;
    
}

size_t xmlRead(WDCore& app, const PluginFormatJson::FormatParam& param, PluginFormatJson::Objects& objs)
{
    WDFileReader    file(param.fileName);
    if (file.isBad())
        return  0;
    file.readAll();
    size_t length = file.length();
    if (length == 0)
        return 0;

    XMLDoc doc;
    doc.parse<0>((char*)file.data());
    XMLNode* pXmlRoot = doc.first_node("Root");
    if (pXmlRoot == nullptr)
        return 0;

    auto pBMBase = app.getBMBase(param.moduleType);
    if (pBMBase == nullptr)
        return 0;
    WDBMNodeSerialize serial(pBMBase->typeMgr());

    WDNode::Nodes nodes;
    nodes = serial.loadFromXml(doc, *pXmlRoot);

    objs.reserve(nodes.size());
    for (auto pNode : nodes)
    {
        if (pNode == nullptr)
            continue;
        objs.push_back(pNode);
    }

    return objs.size();
}
size_t jsonRead(WDCore& app, const PluginFormatJson::FormatParam& param, PluginFormatJson::Objects& objs)
{
    WDFileReader    fileReader(param.fileName.c_str());
    if (fileReader.isBad())
        return false;
    fileReader.readAll();

    JsonDoc doc;
    JsonValue& rootObject = doc.SetObject();

    doc.Parse((char*)fileReader.data(), fileReader.length());
    if (doc.HasParseError())
        return false;

    auto pBMBase = app.getBMBase(param.moduleType);
    if (pBMBase == nullptr)
        return 0;
    WDBMNodeSerialize serial(pBMBase->typeMgr());

    WDNode::Nodes nodes;
    nodes = serial.loadFromJson(doc, rootObject);

    objs.reserve(nodes.size());
    for (auto pNode : nodes)
    {
        if (pNode == nullptr)
            continue;
        objs.push_back(pNode);
    }

    return objs.size();
}
size_t wdRead(WDCore& app, const PluginFormatJson::FormatParam& param, PluginFormatJson::Objects& objs)
{
    WDFileReader    file(param.fileName);
    if (file.isBad())
        return  0;

    WDNode::Nodes nodes;
    if (param.moduleType == "Design")
    {
        WDBMNodeSerialize serial(app.designMgr().typeMgr());
        nodes = serial.loadFromWD(file);
    }
    else if (param.moduleType == "Catalog")
    {
        WDBMNodeSerialize serial(app.catalogMgr().typeMgr());
        nodes = serial.loadFromWD(file);
    }

    objs.reserve(nodes.size());
    for (auto pNode : nodes)
    {
        if (pNode == nullptr)
            continue;
        objs.push_back(pNode);
    }

    return objs.size();
}

size_t  PluginFormatJson::read(const FormatParam& param, Objects& objs)
{
    return jsonRead(_app, param, objs);
}

size_t  PluginFormatJson::read(WDInStream* ,Objects& )
{
    return  0;
}

size_t xmlWrite(WDCore& app, const PluginFormatJson::FormatParam& param, const PluginFormatJson::Objects& objs)
{
    FILE* fp = fopen(param.fileName.c_str(), "w");
    if (fp == nullptr)
        return 0;

    WDNode::Nodes nodes;
    nodes.reserve(objs.size());
    for (auto pObj : objs)
    {
        auto pNode = WDNode::ToShared(pObj);
        if (pNode == nullptr)
            continue;
        nodes.push_back(pNode);
    }

    XMLDoc doc;
    XMLNode* pXmlinfo = doc.allocate_node(rapidxml::node_pi, "xml version='1.0' encoding='utf-8'");
    doc.append_node(pXmlinfo);
    XMLNode* pXmlRoot = doc.allocate_node(rapidxml::node_element, "Root");
    doc.append_node(pXmlRoot);

    auto pBMBase = app.getBMBase(param.moduleType);
    if (pBMBase == nullptr)
        return 0;

    WDBMNodeSerialize serial(pBMBase->typeMgr());
    serial.saveToXml(nodes, doc, *pXmlRoot);

    std::string xmlStr;
    rapidxml::print(std::back_inserter(xmlStr), doc, 0);

    fwrite(xmlStr.data(), 1, xmlStr.size(), fp);
    fclose(fp);

    return nodes.size();
}
size_t jsonWrite(WDCore& app, const PluginFormatJson::FormatParam& param, const PluginFormatJson::Objects& objs)
{
    FILE* pFile = fopen(param.fileName.c_str(), "wb");
    if (pFile == nullptr)
        return 0;

    WDNode::Nodes nodes;
    nodes.reserve(objs.size());
    for (auto pObj : objs)
    {
        auto pNode = WDNode::ToShared(pObj);
        if (pNode == nullptr)
            continue;
        nodes.push_back(pNode);
    }

    JsonDoc doc;
    auto& rootObject = doc.SetObject();
    auto pBMBase = app.getBMBase(param.moduleType);
    if (pBMBase == nullptr)
        return 0;
    WDBMNodeSerialize serial(pBMBase->typeMgr());
    serial.saveToJson(nodes, doc, rootObject);

    rapidjson::StringBuffer buffer;
    
    rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
    if (!doc.Accept(writer))
    {
        assert(false && "ע��:Jsonд��ʧ��!");
        return 0;
    }
    std::string content = buffer.GetString();

    fwrite(content.c_str(), 1, content.size(), pFile);
    fclose(pFile);

    return nodes.size();
}
size_t wdWrite(WDCore& app, const PluginFormatJson::FormatParam& param, const PluginFormatJson::Objects& objs)
{
    FILE* fp = fopen(param.fileName.c_str(), "wb");
    if (fp == nullptr)
        return 0;
    WDNode::Nodes nodes;
    nodes.reserve(objs.size());
    for (auto pObj : objs)
    {
        auto pNode = WDNode::ToShared(pObj);
        if (pNode == nullptr)
            continue;
        nodes.push_back(pNode);
    }
    WDFileWriter writer(fp);

    auto pBMBase = app.getBMBase(param.moduleType);
    if (pBMBase == nullptr)
        return 0;
    WDBMNodeSerialize serial(pBMBase->typeMgr());
    serial.saveToWD(nodes, writer);

    fclose(fp);

    return nodes.size();
}


size_t  PluginFormatJson::write(const FormatParam& param,const Objects& objs)
{
    return jsonWrite(_app, param, objs);
}

size_t  PluginFormatJson::write(WDOutStream* stream,const Objects& objs)
{
    WDUnused(stream);
    WDUnused(objs);
    return 0;
}

WD_NAMESPACE_END

