
// Author: <PERSON>, 2008 and 2009
// This is part of a port of the CSG project
// originally written in java by <PERSON><PERSON>
// Email: <EMAIL>
// Web: http://createuniverses.blogspot.com/

#include "IntSet.h"

IntSet::IntSet()
{
	//m_nMaxSize = 10000;
	//m_nSize = 0;
	//m_pInts = new int[m_nMaxSize];

	//length = m_nSize;
}

IntSet::IntSet(int /*nMaxSize*/)
{
	//m_nMaxSize = nMaxSize;
	//m_nSize = 0;
	//m_pInts = new int[m_nMaxSize];

	//length = m_nSize;
}

IntSet::~IntSet()
{
	//delete [] m_pInts;
}

//int IntSet::GetMaxSize()
//{
//	return m_nMaxSize;
//}

size_t IntSet::GetSize()
{
	return m_pInts.size();
	//return m_nSize;
}

size_t IntSet::length()
{
	return m_pInts.size();
}

unsigned int IntSet::GetInt(size_t i)
{
	if(i >= m_pInts.size()) 
        return 0;

	return m_pInts[i];
}

void IntSet::SetInt(int i, unsigned int nInt)
{
	if(i < 0) return;
	if(i >= m_pInts.size()) return;

	m_pInts[i] = nInt;
}

void IntSet::AddInt(const unsigned int nInt)
{
	//if(m_nSize >= m_nMaxSize) return;

	m_pInts.push_back(nInt);

	//m_pInts[m_nSize] = nInt;
	//m_nSize++;

	//length = m_nSize;
}

unsigned int & IntSet::operator[](size_t index)
{
    unsigned int & pInt = m_pInts[index];

	// If its null, we're in trouble...

	return pInt;
}
