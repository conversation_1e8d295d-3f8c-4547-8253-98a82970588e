
// Author: <PERSON>, 2008 and 2009
// This is part of a port of the CSG project
// originally written in java by <PERSON><PERSON>
// Email: <EMAIL>
// Web: http://createuniverses.blogspot.com/

#include "ColorSet.h"

#include "Color.h"

ColorSet::ColorSet()
{
}

ColorSet::ColorSet(int /*nMaxSize*/)
{
}

ColorSet::~ColorSet()
{
	//delete [] m_pColors;
}

//int ColorSet::GetMaxSize()
//{
//	return m_nMaxSize;
//}

size_t ColorSet::GetSize()
{
	//return m_nSize;
	return m_pColors.size();
}

size_t ColorSet::length()
{
	return m_pColors.size();
}

BColor ColorSet::GetColor(size_t i)
{
	if(i >= m_pColors.size()) 
        return BColor();

	return m_pColors[i];
}

void ColorSet::SetColor(int i, const BColor & vColor)
{
	if(i < 0) return;
	if(i >= m_pColors.size()) return;

	m_pColors[i] = vColor;
}

void ColorSet::AddColor(const BColor & vColor)
{
	m_pColors.push_back(vColor);

	//if(m_nSize >= m_nMaxSize) return;

	//m_pColors[m_nSize] = vColor;
	//m_nSize++;
	//length = m_nSize;
}

BColor & ColorSet::operator[](size_t index)
{
	BColor & pColor = m_pColors[index];

	return pColor;
}
