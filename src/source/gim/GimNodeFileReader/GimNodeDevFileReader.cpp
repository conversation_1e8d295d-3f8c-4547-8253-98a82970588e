#include "GimNodeFileReader/GimNodeDevFileReader.h"

GimNodeDevFileReader::GimNodeDevFileReader
(
	const std::string& fileName, 
	const std::string& path,
	const std::unordered_map<std::string, GimFileReader*>& fileMap
) 
	:GimNodeFileReader(GimFileReader::T_NodeDevFile,fileName, path, fileMap) {}

GimNode* GimNodeDevFileReader::createP()
{
	GimNode* pnode = new GimNode;
	pnode->_attrs = this->getAttributes();
	pnode->_fileName = this->_fileName;
	return pnode;
}

bool GimNodeDevFileReader::readP(std::stringstream& data)
{
	std::string line;
	while (getline(data, line))
	{
		if (readFileRef(line))//��¼����.dev.phm.fam
			;
		else if (readAttribute(line))//��¼����һ�����ɸ��Ⱥ�
			;
		else //һ����¼û�еȺ�
		{
			assert(false && "δ֪!");
		}
	}
	return true;
}

bool GimNodeDevFileReader::readFileRef(const std::string& line)
{
	if (line.find(".dev") == std::string::npos 
		&& line.find(".phm") == std::string::npos
		&& line.find(".fam") == std::string::npos
		&& line.find(".Dev") == std::string::npos
		&& line.find(".Phm") == std::string::npos
		&& line.find(".Fam") == std::string::npos)
		return false;
	std::string rline = WD::utf82ansi(line.c_str()); 
	int pos = (int)rline.find("=");
	std::string refFile;
    std::string refIndex;
    refIndex = rline.substr(0, pos);
    refFile = rline.substr(pos + 1, rline.size() - pos - 1);

    int index = int(refFile.size()) - 3;
    if (refFile[index] >= 'A' && refFile[index] <= 'Z')
        refFile[index] = refFile[index] + 32;

    std::unordered_map<std::string, GimFileReader*>::const_iterator fItr = _fileMap.find(refFile);
    if (fItr != _fileMap.end() && fItr->second != nullptr)
    {
        this->_refReaderObject.push_back(std::make_pair(refIndex, fItr->second));
        return true;
    }
    else
        return false;
}

bool GimNodeDevFileReader::readAttribute(const std::string& line)
{
	if (line.find("=") == std::string::npos)
		return false;
	std::string rline = WD::utf82ansi(line.c_str());
	int pos = (int)rline.find("=");
	Attribute attr;
	attr.key = rline.substr(0, pos - 0);
	attr.value = rline.substr(pos + 1, rline.size() - pos - 1);
	this->addAttribute(attr);
	return true;
}