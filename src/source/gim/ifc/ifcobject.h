#ifndef IFC_OBJECT_H
#define IFC_OBJECT_H 

#include "ifcdef.h"

struct STRUCT_MATERIALS;
struct STRUCT__IFC__OBJECT;
struct STRUCT_TREE_ITEM_IFCINSTANCE;

enum ENUM_TREE_ITEM_TYPES {
	TREE_ITEM_CONTAINS = 1,
	TREE_ITEM_NOTREFERENCED,
	TREE_ITEM_DECOMPOSEDBY,
	TREE_ITEM_GROUPEDBY,
	TREE_ITEM_GROUPS,
	TREE_ITEM_SPACEBOUNDARIES,
	TREE_ITEM_IFCINSTANCE,
	TREE_ITEM_IFCENTITY,
	TREE_ITEM_GEOMETRY,
	TREE_ITEM_PROPERTIES,
	TREE_ITEM_PROPERTY,
	TREE_ITEM_PROPERTYSET,
	TREE_ITEM_TEXT
};

enum ENUM_TREE_ITEM_SELECT_STATE {
	TI_CHECKED = 1,
	TI_UNCHECKED = 3,
	TI_PARTLY_CHECKED = 2,
	TI_NONE = 5
};

struct VECTOR3 {
	double							x;
	double							y;
	double							z;
};

struct STRUCT_TREE_ITEM {
	ENUM_TREE_ITEM_TYPES			type;

	int_t						    hTreeItem;
	wchar_t							* nameBuffer;

	STRUCT_TREE_ITEM				* parent;
	STRUCT_TREE_ITEM				* child;
	STRUCT_TREE_ITEM				* next;
};

struct STRUCT_TREE_ITEM_SELECTABLE : STRUCT_TREE_ITEM {
	ENUM_TREE_ITEM_SELECT_STATE		selectState;
};
struct STRUCT_TREE_ITEM_IFCENTITY : STRUCT_TREE_ITEM_SELECTABLE {
	int_t							ifcModel;
	int_t							ifcEntity;
};
struct STRUCT_TREE_ITEM_GEOMETRY : STRUCT_TREE_ITEM_SELECTABLE {
};

struct STRUCT_TREE_ITEM_PROPERTIES : STRUCT_TREE_ITEM {
};

struct STRUCT_TREE_ITEM_DECOMPOSEDBY : STRUCT_TREE_ITEM_SELECTABLE {
};
struct STRUCT_TREE_ITEM_CONTAINS : STRUCT_TREE_ITEM_SELECTABLE {
};
struct STRUCT_TREE_ITEM_SPACEBOUNDARIES : STRUCT_TREE_ITEM_SELECTABLE {
};
struct STRUCT_TREE_ITEM_NOTREFERENCED : STRUCT_TREE_ITEM_SELECTABLE {
};
struct STRUCT_TREE_ITEM_GROUPS : STRUCT_TREE_ITEM_SELECTABLE {
};
struct STRUCT_TREE_ITEM_GROUPEDBY : STRUCT_TREE_ITEM_SELECTABLE {
};
struct STRUCT_TREE_ITEM_TEXT : STRUCT_TREE_ITEM {
	bool							allocated;
};


struct STRUCT__IFC__OBJECT {
	int_t							ifcInstance;
	int_t							ifcEntity;

	ENUM_TREE_ITEM_SELECT_STATE		selectState;

	wchar_t							* entityName;
	bool							hide;
	int_t							segmentationParts;
	STRUCT_TREE_ITEM_IFCINSTANCE	* treeItemModel;
	STRUCT_TREE_ITEM_IFCINSTANCE	* treeItemSpaceBoundary;
	STRUCT_TREE_ITEM_IFCINSTANCE	* treeItemNonReferenced;

	STRUCT_MATERIALS				* materials;
	STRUCT__IFC__OBJECT				* next;

	VECTOR3							vecMin;
	VECTOR3							vecMax;

	int_t							noVertices;
	float							* __vertices;

	int_t							vertexOffset;

	int_t							noPrimitivesForPoints;
	int32_t							* indicesForPoints;
	int_t							indexOffsetForPoints;

	int_t							noPrimitivesForLines;
	int32_t							* indicesForLines;
	int_t							indexOffsetForLines;

	int_t							noPrimitivesForFaces;
	int32_t							* indicesForFaces;
	int_t							indexOffsetForFaces;

	int_t							noPrimitivesForWireFrame;
	int32_t							* indicesForLinesWireFrame;
	int_t							indexOffsetForWireFrame;
};



////

struct STRUCT_TREE_ITEM_IFCINSTANCE : STRUCT_TREE_ITEM_SELECTABLE {
	int_t							ifcModel;
	int_t							ifcInstance;

	STRUCT__IFC__OBJECT				* ifcObject;
};


#endif 

