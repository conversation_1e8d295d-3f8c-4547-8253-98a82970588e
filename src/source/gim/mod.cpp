#include "mod.h"

bool Gim::ModGeo::addParam(const std::string& name, const std::string& value)
{
	auto fItr = _params.find(name);
	if (fItr != _params.end())
	{
		assert(false);
		return false;
	}
	auto rItr = _params.emplace(name, value);
	return rItr.second;
}

bool Gim::ModGeo::addParam(std::string&& name, std::string&& value)
{
	auto fItr = _params.find(name);
	if (fItr != _params.end())
	{
		assert(false);
		return false;
	}
	auto rItr = _params.emplace(std::forward<std::string>(name), std::forward<std::string>(value));
	return rItr.second;
}
