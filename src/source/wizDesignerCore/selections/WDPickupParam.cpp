#include "WDPickupParam.h"
#include "../cameras/WDCamera.h"

WD_NAMESPACE_BEGIN


WDPickupParam::WDPickupParam(const WDCamera* pCamera, const DRay& ray)
    :_pCamera(pCamera), _ray(ray)
{

}
WDPickupParam::WDPickupParam(const WDCamera* pCamera, const IVec2& screenPos)
    :_pCamera(pCamera)
{
    if (_pCamera != nullptr)
    {
        _ray = _pCamera->createRayFromScreen(DVec2(screenPos), IVec2(_pCamera->viewSize()));
    }
}

bool WDPickupParam::intersectAabb(const DAabb3& aabb) const
{
    if (aabb.isNull())
        return false;
    
    //由于线框的拾取是基于屏幕像素的距离的
    //因此在校验包围盒时，需要适当的放大包围盒，要不然会出现明明可以拾取到线框，但是被包围盒相交过滤掉的情况
    //1.拿到包围盒的所有顶点
    DVec3 corners[8];
    aabb.corners(corners);
    //2.拿到包围盒顶点中，离射线端点最远的点
    DVec3 farCorner = corners[0];
    double maxDisSq = DVec3::DistanceSq(_ray.origin, corners[0]);
    for (size_t i = 1; i < 8; ++i)
    {
        double tmpDisSq = DVec3::DistanceSq(_ray.origin, corners[i]);
        if (tmpDisSq > maxDisSq)
        {
            farCorner = corners[i];
            maxDisSq = tmpDisSq;
        }
    }
    //3.使用相机计算最远的点一个屏幕像素对应的世界坐标距离
    double pixelU = _pCamera->pixelU(farCorner);
    DVec3 pixelUOff = DVec3(pixelU * _screenThreshold);
    //4.根据距离对包围盒进行扩大
    DAabb3 tAabb = aabb;
    tAabb.max   = aabb.max + pixelUOff;
    tAabb.min   = aabb.min - pixelUOff;
    //5.使用扩大后的包围盒进行校验
    auto ret = _ray.intersect(tAabb);
    if (ret.first > 0)
        return true;

    return false;
}
bool WDPickupParam::intersectPoint(const DVec3& point) const
{
    const DVec2& rayScreen = _pCamera->worldToScreen(_ray.origin);
    DVec2 pointScreen = _pCamera->worldToScreen(point);
    
    double disSq = DVec2::DistanceSq(rayScreen, pointScreen);
    double minDisSq = _screenThreshold * _screenThreshold;

    return disSq < minDisSq;
}
std::pair<bool, DVec3> WDPickupParam::intersectSegment(const DVec3 & segmentP0, const DVec3 & segmentP1) const
{
    DSegment3 tSegment(segmentP0, segmentP1);
    //校验是否有效的线段
    if (!tSegment.isVaild())
        return std::make_pair(false, DVec3::Zero());
    DVec3 rayPt;
    DVec3 segPt;
    double tDis = _ray.distanceSq(tSegment, &segPt, &rayPt);
    WDUnused(tDis);
    DVec2 screenSegPt = _pCamera->worldToScreen(segPt);
    DVec2 screenRayPt = _pCamera->worldToScreen(rayPt);

    double disSq = DVec2::DistanceSq(screenRayPt, screenSegPt);
    double minDisSq = _screenThreshold * _screenThreshold;

    if (disSq < minDisSq)
        return std::make_pair(true, segPt);

    return std::make_pair(false, DVec3::Zero());
}

WD_NAMESPACE_END