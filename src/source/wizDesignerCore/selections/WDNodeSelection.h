#pragma once

#include "../node/WDNode.h"
#include "WDSelectionInterface.h"
#include "../viewer/WDClip.h"

WD_NAMESPACE_BEGIN
class WDNodeSelection;

/**
* @brief 拾取结果
*   当pNode == nullptr时,说明拾取结果无效
*/
class WDNodePickupResult
{
public:
    /**
     * @brief 拾取的结果节点
    */
    WDNode::WeakPtr node;
    /**
     * @brief 拾取的结果点
    */
    DVec3 point = DVec3(0.0);
    /**
     * @brief 拾取的结果图元
    */
    using Primitive = WDSelectionInterface::PickupResult::Primitive;
    Primitive primitive = Primitive();
};

/**
 * @brief 节点选择(拾取/框选)监听者
*/
class WD_API WDNodeSelectionMonitor
{
    friend class WDNodeSelection;
protected:
    /**
    * @brief 某个节点拾取计算之前通知
    * @param pNode 将要拾取的节点对象
    * @param param 使用的拾取参数
    * @return 是否决定对该节点做拾取
    *   如果返回true，表示拾取该节点; 否则，表示跳过该节点的拾取
    */
    virtual bool pickupSingleNodeBefore(WDNode::SharedPtr pNode
        , const WDPickupParam& param)
    {
        WDUnused(pNode);
        WDUnused(param);
        return true;
    }
    /**
    * @brief 某个节点框选计算之前通知
    * @param pNode 将要进行框选计算的节点对象
    * @param param 使用的框选参数
    * @return 是否决定对该节点做框选
    *   如果返回true，表示框选该节点; 否则，表示跳过该节点的框选
    */
    virtual bool frameSelectSingleNodeBefore(WDNode::SharedPtr pNode
        , const WDFrameSelectParam& param)
    {
        WDUnused(pNode);
        WDUnused(param);
        return true;
    }
    /**
    * @brief 某个节点范围选择计算之前通知
    * @param pNode 将要进行范围选择计算的节点对象
    * @param param 使用的范围选择参数
    * @return 是否决定对该节点做范围选择
    *   如果返回true，表示范围选择该节点; 否则，表示跳过该节点的范围选择
    */
    virtual bool rangeSelectSingleNodeBefore(WDNode::SharedPtr pNode
        , const WDRangeSelectParam& param)
    {
        WDUnused(pNode);
        WDUnused(param);
        return true;
    }
};

/**
 * @brief 节点选择器
 */
class WD_API WDNodeSelection
{
public:
    /**
    * @brief 节点列表
    */
    using Nodes = std::vector<WDNode::SharedPtr>;
public:
    WDNodeSelection(WDNodeSelectionMonitor* pMonitor = nullptr, const WDClip* pClip = nullptr);
    ~WDNodeSelection();
public:
    /**
    * @brief 获取当前监听者
    */
    inline WDNodeSelectionMonitor* monitor() const
    {
        return _pMonitor;
    }
    /**
    * @brief 设置监听者
    */
    inline void setMonitor(WDNodeSelectionMonitor* pMonitor)
    {
        _pMonitor = pMonitor;
    }
    /**
    * @brief 拾取
    * @param nodes 参与拾取的节点列表，遍历列表中的所有节点 以及 递归每一个节点的所有子孙节点 进行拾取计算
    * @param param 拾取参数，此次拾取的规则配置信息
    * @return 是否拾取到结果
    */
    bool pickup(const Nodes& nodes
        , const WDPickupParam& param
        , WDNodePickupResult& outPickupResult) const;
    /**
    * @brief 拾取
    * @param nodes 参与拾取的节点列表，遍历列表中的所有节点 以及 递归每一个节点的所有子孙节点 进行拾取计算
    * @param param 拾取参数，此次拾取的规则配置信息
    * @return 是否拾取到结果
    */
    template <class TypeIterator>
    bool pickup(const TypeIterator& begin
        , const TypeIterator& end
        , const WDPickupParam& param
        , WDNodePickupResult& outPickupResult) const
    {
        outPickupResult.node.reset();
        outPickupResult.point = DVec3(0.0);
        //校验参数
        if (begin == end)
            return false;
        if (param.camera() == nullptr)
            return false;
        if (param.ray().direction.lengthSq() <= NumLimits<double>::Epsilon)
            return false;

        //遍历节点列表并递归每个节点的所有子孙节点进行拾取计算
        WDSelectionInterface::PickupResult outResult;
        for(auto itr = begin; itr != end; ++itr)
        {
            auto pNode = *itr;
            if constexpr (std::is_same_v<decltype(pNode), WDNode*>)
                this->pickupNode(WDNode::ToShared(pNode), param, outPickupResult, outResult);
            else
                this->pickupNode(pNode, param, outPickupResult, outResult);
        }

        return outPickupResult.node.lock() != nullptr;
    }
    /**
    * @brief 框选
    * @param nodes 参与框选的节点列表，遍历列表中的所有节点 以及 递归每一个节点的所有子孙节点 进行拾取计算
    * @param param 框选参数，此次框选的规则配置信息
    * @param outNodes 被框选选中的结果节点列表
    * @return 是否框选到结果
    */
    bool frameSelect(const Nodes& nodes
        , const WDFrameSelectParam& param
        , Nodes& outNodes) const;
    /**
    * @brief 框选
    * @param begin 参与框选的节点列表迭代器的begin
    * @param end 参与框选的节点列表迭代器的end
    * @param param 框选参数，此次框选的规则配置信息
    * @param outNodes 被框选选中的结果节点列表
    * @return 是否框选到结果
    */
    template <class TypeIterator>
    bool frameSelect(const TypeIterator& begin
        , const TypeIterator& end
        , const WDFrameSelectParam& param
        , Nodes& outNodes) const
    {
        //校验参数
        if (begin == end)
            return false;
        if (param.camera() == nullptr)
            return false;

        //遍历节点列表并递归每个节点的所有子孙节点进行框选计算
        for (auto itr = begin; itr != end; ++itr)
        {
            auto pNode = (*itr);
            if constexpr (std::is_same_v<decltype(pNode), WDNode*>)
                this->frameSelectNode(WDNode::ToShared(pNode), param, outNodes);
            else
                this->frameSelectNode(pNode, param, outNodes);
        }
        return !outNodes.empty();
    }
    
    /**
    * @brief 范围选择
    * @param begin 参与选择的节点列表迭代器的begin
    * @param end 参与选择的节点列表迭代器的end
    * @param param 选择参数，此次选择的规则配置信息
    * @param outNodes 被框选选中的结果节点列表
    * @return 是否框选到结果
    */
    bool rangeSelect(const Nodes& nodes
        , const WDRangeSelectParam& param
        , Nodes& outNodes)
    {
        return this->rangeSelect(nodes.begin(), nodes.end(), param, outNodes);
    }
    /**
    * @brief 范围选择
    * @param begin 参与选择的节点列表迭代器的begin
    * @param end 参与选择的节点列表迭代器的end
    * @param param 选择参数，此次选择的规则配置信息
    * @param outNodes 被框选选中的结果节点列表
    * @return 是否框选到结果
    */
    template <class TypeIterator>
    bool rangeSelect(const TypeIterator& begin
        , const TypeIterator& end
        , const WDRangeSelectParam& param
        , Nodes& outNodes)
    {
        //校验参数
        if (begin == end)
            return false;
        if (param.param.index() == 0)
            return false;

        //遍历节点列表并递归每个节点的所有子孙节点进行框选计算
        for (auto itr = begin; itr != end; ++itr)
        {
            auto pNode = (*itr);
            if (pNode != nullptr)
                this->rangeSelectNode(pNode, param, outNodes);
        }
        return !outNodes.empty();
    }
    /**
     * @brief 递归节点以及其所有子节点判断是否在范围内
     * @param pNode 参与选择的节点
     * @param param 范围选择参数
     * @param outNodes 输出的节点列表
    */
    void rangeSelectNode(WDNode::SharedPtr pNode, const WDRangeSelectParam& param, Nodes& outNodes) const;
private:
    //递归拾取节点以及其所有子节点
    bool pickupNode(WDNode::SharedPtr pNode
        , const WDPickupParam& param
        , WDNodePickupResult& outPickupResult
        , WDSelectionInterface::PickupResult& outResult) const;
    //计算单个节点拾取的结果
    bool pickupSingleNode(WDNode& node, const WDPickupParam& param, WDSelectionInterface::PickupResult& outResult) const;
    //递归未被拾取到的节点以及其子孙节点
    void unpickedNodeRecursion(WDNode::SharedPtr pNode) const;
    //递归框选节点以及其所有子节点
    void frameSelectNode(WDNode::SharedPtr pNode, const WDFrameSelectParam& param, Nodes& outNodes) const;
    //计算单个节点的框选结果
    bool frameSelectSingleNode(WDNode& node, const WDFrameSelectParam& param) const;
    //计算单个节点是否在范围中
    bool rangeSelectSingleNode(WDNode& node, const WDRangeSelectParam& param) const;
    //递归未被框选到的节点以及其子孙节点
    void unselectedNodeRecursion(WDNode& node) const;
private:
    // 节点选择监听者
    WDNodeSelectionMonitor* _pMonitor = nullptr;
    // 剖切对象
    const WDClip* _pClip = nullptr;
};

WD_NAMESPACE_END

