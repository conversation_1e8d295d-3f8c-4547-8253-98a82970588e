#pragma     once
#include    "../math/Math.hpp"

WD_NAMESPACE_BEGIN

class WDCamera;

/**
* @brief 框选参数
*/
class WD_API WDFrameSelectParam
{
public:
    //框选类型
    enum FrameSelectionType
    {
        //对象全部在框选范围内
        FST_WhollyWithin = 1, 
        //包含 全部以及部分在框选范围内 的两种情况
        FST_WhollyAndPartiallyWithin,
    };
    /**
    * @brief 框选模式
    */
    enum FrameSelectionMode
    {
        //常规框选模式
        // 所有数据对象进行常规方式框选
        // 例如拾取 几何体实体，网格的网格线等
        FSM_Normal = 0,
        //线框框选模式
        // 如果有线框的数据对象仅框选线框,否则进行常规框选
        FSM_WireFrame,
    };
private:
    //当前相机对象
    const WDCamera * _pCamera;
    //当前框选的视椎数据
    DFrustum _frustum;
    //框选类型
    FrameSelectionType _frameSelectionType = FST_WhollyAndPartiallyWithin;
    //框选模式
    FrameSelectionMode _frameSelectionMode = FSM_Normal;
public:
    WDFrameSelectParam();
    /**
    * @brief 使用相机和两个屏幕坐标构造
    */
    WDFrameSelectParam(const WDCamera* pCamera, const IVec2& screenPos0, const IVec2& screenPos1);
    ~WDFrameSelectParam();
public:
    /**
    * @brief 设置框选使用的相机对象
    */
    inline void setCamera(const WDCamera* pCamera)
    {
        _pCamera = pCamera;
    }
    /**
    * @brief 获取框选使用的相机对象
    */
    inline const WDCamera* camera() const
    {
        return _pCamera;
    }
    /**
    * @brief 设置框选使用的视椎数据
    */
    inline void setFrustum(const DFrustum& frustum)
    {
        _frustum = frustum;
    }
    /**
    * @brief 获取框选使用的视椎数据
    */
    inline const DFrustum& frustum() const
    {
        return _frustum;
    }
    /**
    * @brief 获取框选类型
    */
    inline FrameSelectionType frameSelectionType() const
    {
        return _frameSelectionType;
    }
    /**
    * @brief 设置框选类型
    */
    inline void setFrameSelectionType(FrameSelectionType type)
    {
        _frameSelectionType = type;
    }
    /**
    * @brief 获取框选模式
    */
    inline FrameSelectionMode frameSelectionMode() const
    {
        return _frameSelectionMode;
    }
    /**
    * @brief 设置框选类型
    */
    inline void setFrameSelectionMode(FrameSelectionMode mode)
    {
        _frameSelectionMode = mode;
    }
public:
    /**
    * @brief 检测的返回类型
    */
    enum Result
    {
        //无效数据
        R_InvaildData,
        //有效数据且全部在框选范围外,即未选中任何部分
        R_WhollyWithout,
        //有效数据且部分在框选范围内
        R_PartiallyWithin,
        //有效数据且全部在框选范围内
        R_WhollyWithin,
    };
    /**
    * @brief 使用当前参数的值与包围盒检测
    */
    Result intersectAabb(const DAabb3& aabb) const; 
    /**
    * @brief 使用当前参数的值与点检测
    */
    inline Result intersectPoint(const DVec3& point) const
    {
        if (_frustum.contains(point))
            return Result::R_WhollyWithin;
        else
            return Result::R_WhollyWithout;
    }
    /**
    * @brief 使用当前参数的值与线段检测
    */
    Result intersectSegment(const DVec3& segmentP0
        , const DVec3& segmentP1) const;
    /**
    * @brief 使用当前参数的值与三角形检测
    */
    Result intersectTriangle(const DVec3& triangleP0
        , const DVec3& triangleP1
        , const DVec3& triangleP2) const;
};

WD_NAMESPACE_END

