#include    "WDGeometry.h"
#include    "../WDCore.h"
#include    "../WDObjectCreator.h"

WD_NAMESPACE_BEGIN

static size_t  ObjectToStream(const WDObject& object, WDWriteStream& stream)
{
    size_t  nStart = stream.tell();

    WDUuid  clsId = object.classId();
    WDUuid  objId = object.uuid();

    assert(!clsId.isNull());
    assert(!objId.isNull());
    uint    nName = (uint)(object.name().size());
    uint    nLen = sizeof(uint) + sizeof(clsId) + sizeof(objId) + sizeof(nName) + nName;
    stream.writeBuffer(&nLen, sizeof(nLen));
    stream.writeBuffer(&clsId, sizeof(clsId));
    stream.writeBuffer(&objId, sizeof(objId));
    stream.writeBuffer(&nName, sizeof(nName));
    if (nName > 0)
    {
        stream.writeBuffer((void*)(object.name().c_str()), nName);
    }
    return  stream.tell() - nStart;
}
static size_t  ObjectFromStream(WDObject& object, WDReadStream& stream)
{
    size_t  nStart = stream.tell();
    uint    nSize = 0;
    WDUuid  clsId;
    WDUuid  uuid;
    uint    nName = 0;
    /// 跳过sizeof(uint) 
    stream.readBuffer(&nSize, sizeof(nSize));
    stream.readBuffer(&clsId, sizeof(clsId));
    stream.readBuffer(&uuid, sizeof(uuid));
    object.setUuid(uuid);
    stream.readBuffer(&nName, sizeof(nName));
    if (nName > 0)
    {
        std::string temp;
        temp.resize(nName + 1);
        stream.readBuffer(temp.data(), nName);
        object.setName(temp.c_str());
    }
    return  stream.tell() - nStart;
}
static size_t  FinishBlock(WDWriteStream& st, size_t nStart)
{
    size_t  nTell = st.tell();
    uint    nLen = uint(nTell - nStart);
    st.seek(nStart);
    st.writeBuffer(&nLen, sizeof(nLen));
    st.seek(nTell);
    return  nLen;
}
static WDMesh::SharedPtr ReadMesh(WDReadStream& stream, uint* pLen = nullptr)
{
    size_t  nStart = stream.tell();
    uint    nSize = 0;
    WDUuid  clsId;

    stream.readBuffer(&nSize, sizeof(uint));
    stream.readBuffer(&clsId, sizeof(clsId));
    stream.seek(nStart);
    if (pLen != nullptr)
    {
        *pLen = nSize;
    }
    WDObject::SharedPtr pPtr = Core().objectCreator().create(clsId);
    if (pPtr == nullptr)
    {
        stream.skip(nSize);
        return  nullptr;
    }
    WDMesh::SharedPtr pMesh = WDMesh::SharedCast(pPtr);
    if (pMesh == nullptr)
    {
        stream.skip(nSize);
        return  nullptr;
    }

    pMesh->fromStream(stream);
    return  pMesh;
}

WDGeometry::WDGeometry(WDGeometryType type, const std::string& name)
    : WDObject(name)
    , _gType(type)
    , _position(0.0)
    , _rotation(1.0,0.0,0.0,0.0)
    , _scaling(1.0)
    , _pMesh(nullptr)
{
}
WDGeometry::~WDGeometry()
{
}

Aabb3 WDGeometry::aabb() const
{
    if (_pMesh == nullptr)
        return Aabb3();
    Aabb3 aabb = _pMesh->aabb();
    if (!aabb.isNull())
        aabb.transform(this->transform());
    return aabb;
}

void WDGeometry::copy(const WDObject* pSrcObject)
{
    const WDGeometry* pSrcGeom = dynamic_cast<const WDGeometry*>(pSrcObject);
    if (pSrcGeom == nullptr)
        return;

    WDObject::copy(pSrcObject);

    // 拷贝Transform数据
    this->_position = pSrcGeom->_position;
    this->_rotation = pSrcGeom->_rotation;
    this->_scaling  = pSrcGeom->_scaling;
    // 拷贝Mesh数据
    if (pSrcGeom->_pMesh != nullptr)
    {
        if (_pMesh == nullptr)
            _pMesh = pSrcGeom->_pMesh->cloneT<WDMesh>();
        else
            _pMesh->copy(pSrcGeom->_pMesh.get());
    }
}
WDObject::SharedPtr WDGeometry::clone() const
{
    auto pNewGeom = WDGeometry::MakeShared();
    pNewGeom->copy(this);
    return pNewGeom;
}

size_t  WDGeometry::toStream(WDWriteStream& st) const
{
    size_t  nStart  =   st.tell();
    ObjectToStream(*this, st);
    st.writeBuffer(&_position,     sizeof(_position));
    st.writeBuffer(&_rotation,     sizeof(_rotation));
    st.writeBuffer(&_scaling,      sizeof(_scaling));

    uint hasMesh = _pMesh ? 1 : 0;
    st.writeBuffer(&hasMesh,       sizeof(hasMesh));
    if (hasMesh)
    {
        _pMesh->toStream(st);
    }
    return FinishBlock(st,nStart);
}
size_t  WDGeometry::fromStream(WDReadStream& st)
{
    size_t  nStart  =   st.tell();
    uint    hasMesh =   _pMesh ? 1 : 0;
    ObjectFromStream(*this, st);

    st.readBuffer(&_position,      sizeof(_position));
    st.readBuffer(&_rotation,      sizeof(_rotation));
    st.readBuffer(&_scaling,       sizeof(_scaling));
    st.readBuffer(&hasMesh,        sizeof(hasMesh));

    if (hasMesh)
    {
        _pMesh = ReadMesh(st);
    }
    return  st.tell() - nStart;
}

bool WDGeometry::pickup(const DMat4& transformMatrix
    , const WDPickupParam& param
    , WDSelectionInterface::PickupResult& outResult) const
{
    using PickRetPri = WDSelectionInterface::PickupResult::Primitive;
    if (_pMesh == nullptr)
        return false;

    const auto& positions = _pMesh->positions();
    if (positions.empty())
        return false;
    
    Mat4 transMat = transformMatrix * this->transform();
    // 首先做包围盒计算
    DAabb3 aabb = _pMesh->aabb();
    aabb.transform(transMat);
    if (!param.intersectAabb(aabb))
        return false;
    
    std::vector<DVec3> transPositions;
    transPositions.reserve(positions.size());
    for (size_t i = 0; i < positions.size(); ++i)
    {
        transPositions.push_back(transMat * DVec3(positions[i]));
    }
    
    DVec3 rayOrigin = param.ray().origin;

    const WDMesh::PrimitiveSets* pPris = nullptr;
    switch (param.pickupMode())
    {
    case WD::WDPickupParam::PM_Normal:
        {
            pPris = &(_pMesh->primitiveSets(WDMesh::Solid));
        }break;
        case WD::WDPickupParam::PM_WireFrame:
        {
            pPris = &(_pMesh->primitiveSets(WDMesh::WireFrame));
        }break;
    default:
        break;
    }
    if (pPris == nullptr)
        return false;
    
    bool bPicked        = false;
    const auto& pris    = *pPris;
    // 这里拾取要做一个顺序优化，目前pris中可能存在三种基本图元
    // 分别是: 点, 线, 三角面
    // 而对于一个几何体来说，点有可能落在线上，线有可能落在面上，而为了保证拾取时能准确地拾取到点,线,面
    // 这里要根据图元类型做一个拾取排序: 优先拾取点, 再拾取线，最后再拾取面

    // 1.先拾取所有的点
    for (const auto& pri : pris)
    {
        if (pri.empty())
            continue;

        switch (pri.primitiveType())
        {
        case WDPrimitiveSet::PT_Points:
            {
                switch (pri.drawType())
                {
                case WDPrimitiveSet::DT_Array:
                    {
                        auto& priData = pri.drawArrayData();
                        uint    nStart = priData.first;
                        uint    nCount = priData.first + priData.second;

                        for (uint i = nStart; i < nCount; ++i)
                        {
                            const DVec3& p0 = transPositions[i];

                            if (!param.intersectPoint(p0))
                                continue;

                            if (outResult.setFromPoint(param.ray(), p0))
                            {
                                outResult.setPrimitive(PickRetPri(p0));
                                bPicked = true;
                            }
                        }
                    }
                    break;
                case WDPrimitiveSet::DT_ElementByte:
                    {
                        auto& indices = pri.drawElementByteData();
                        for (size_t i = 0; i < indices.size(); ++i)
                        {
                            const DVec3& p0 = transPositions[indices[i]];

                            if (!param.intersectPoint(p0))
                                continue;

                            if (outResult.setFromPoint(param.ray(), p0))
                            {
                                outResult.setPrimitive(PickRetPri(p0));
                                bPicked = true;
                            }
                        }
                    }
                    break;
                case WDPrimitiveSet::DT_ElementUShort:
                    {
                        auto& indices = pri.drawElementUShortData();
                        for (size_t i = 0; i < indices.size(); ++i)
                        {
                            const DVec3& p0 = transPositions[indices[i]];

                            if (!param.intersectPoint(p0))
                                continue;

                            if (outResult.setFromPoint(param.ray(), p0))
                            {
                                outResult.setPrimitive(PickRetPri(p0));
                                bPicked = true;
                            }
                        }
                    }
                    break;
                case WDPrimitiveSet::DT_ElementUInt:
                    {
                        auto& indices = pri.drawElementUIntData();
                        for (size_t i = 0; i < indices.size(); ++i)
                        {
                            const DVec3& p0 = transPositions[indices[i]];

                            if (!param.intersectPoint(p0))
                                continue;

                            if (outResult.setFromPoint(param.ray(), p0))
                            {
                                outResult.setPrimitive(PickRetPri(p0));
                                bPicked = true;
                            }
                        }
                    }
                    break;
                default:
                    break;
                }
            }
        break;
        default:
            break;
        }
    }

    // 再拾取所有的线
    for (const auto& pri : pris)
    {
        if (pri.empty())
            continue;
        switch (pri.primitiveType())
        {
        case WDPrimitiveSet::PT_Lines:
            {
                switch (pri.drawType())
                {
                case WDPrimitiveSet::DT_Array:
                    {
                        auto& priData = pri.drawArrayData();
                        uint    nStart = priData.first;
                        uint    nCount = priData.first + priData.second;
                        for (uint i = nStart; i < nCount; i += 2)
                        {
                            const DVec3& p0 = transPositions[i + 0];
                            const DVec3& p1 = transPositions[i + 1];

                            auto ret = param.intersectSegment(p0, p1);
                            if (!ret.first)
                                continue;

                            if (outResult.setFromPoint(param.ray(), ret.second))
                            {
                                outResult.setPrimitive(PickRetPri(DSegment3(p0, p1)));
                                bPicked = true;
                            }
                        }
                    }
                    break;
                case WDPrimitiveSet::DT_ElementByte:
                    {
                        auto& indices = pri.drawElementByteData();
                        for (size_t i = 0; i < indices.size(); i += 2)
                        {
                            const DVec3& p0 = transPositions[indices[i + 0]];
                            const DVec3& p1 = transPositions[indices[i + 1]];

                            auto ret = param.intersectSegment(p0, p1);
                            if (!ret.first)
                                continue;

                            if (outResult.setFromPoint(param.ray(), ret.second))
                            {
                                outResult.setPrimitive(PickRetPri(DSegment3(p0, p1)));
                                bPicked = true;
                            }
                        }
                    }
                    break;
                case WDPrimitiveSet::DT_ElementUShort:
                    {
                        auto& indices = pri.drawElementUShortData();
                        for (size_t i = 0; i < indices.size(); i += 2)
                        {
                            const DVec3& p0 = transPositions[indices[i + 0]];
                            const DVec3& p1 = transPositions[indices[i + 1]];

                            auto ret = param.intersectSegment(p0, p1);
                            if (!ret.first)
                                continue;

                            if (outResult.setFromPoint(param.ray(), ret.second))
                            {
                                outResult.setPrimitive(PickRetPri(DSegment3(p0, p1)));
                                bPicked = true;
                            }
                        }
                    }
                    break;
                case WDPrimitiveSet::DT_ElementUInt:
                    {
                        auto& indices = pri.drawElementUIntData();
                        for (size_t i = 0; i < indices.size(); i += 2)
                        {
                            const DVec3& p0 = transPositions[indices[i + 0]];
                            const DVec3& p1 = transPositions[indices[i + 1]];

                            auto ret = param.intersectSegment(p0, p1);
                            if (!ret.first)
                                continue;

                            if (outResult.setFromPoint(param.ray(), ret.second))
                            {
                                outResult.setPrimitive(PickRetPri(DSegment3(p0, p1)));
                                bPicked = true;
                            }
                        }
                    }
                    break;
                default:
                    break;
                }
            }
            break;
        case WDPrimitiveSet::PT_LineLoop:
            {
                auto& priData = pri.drawArrayData();
                uint    nStart = priData.first;
                uint    nCount = priData.first + priData.second;
                for (uint i = nStart; i < nCount; ++i)
                {
                    if (i == nCount - 1)
                    {
                        const DVec3& p0 = transPositions[i + 0];
                        const DVec3& p1 = transPositions[nStart];

                        auto ret = param.intersectSegment(p0, p1);
                        if (!ret.first)
                            continue;

                        if (outResult.setFromPoint(param.ray(), ret.second))
                        {
                            outResult.setPrimitive(PickRetPri(DSegment3(p0, p1)));
                            bPicked = true;
                        }
                    }
                    else
                    {
                        const DVec3& p0 = transPositions[i + 0];
                        const DVec3& p1 = transPositions[i + 1];

                        auto ret = param.intersectSegment(p0, p1);
                        if (!ret.first)
                            continue;

                        if (outResult.setFromPoint(param.ray(), ret.second))
                        {
                            outResult.setPrimitive(PickRetPri(DSegment3(p0, p1)));
                            bPicked = true;
                        }
                    }
                }
            }
            break;
        case WDPrimitiveSet::PT_LineStrip:
            {
                auto& priData = pri.drawArrayData();
                uint    nStart = priData.first;
                uint    nCount = priData.first + priData.second;

                for (uint i = nStart; i < nCount - 1; ++i)
                {
                    const DVec3& p0 = transPositions[i + 0];
                    const DVec3& p1 = transPositions[i + 1];

                    auto ret = param.intersectSegment(p0, p1);
                    if (!ret.first)
                        continue;

                    if (outResult.setFromPoint(param.ray(), ret.second))
                    {
                        outResult.setPrimitive(PickRetPri(DSegment3(p0, p1)));
                        bPicked = true;
                    }
                }
            }
            break;
        default:
            break;
        }
    }

    // 最后拾取所有的三角面
    for (const auto& pri : pris)
    {
        if (pri.empty())
            continue;
        switch (pri.primitiveType())
        {
        case WDPrimitiveSet::PT_Triangles:
            {
                // 目前只做了三角形拾取，还没有做线根点的拾取
                WDPrimitiveSet::DrawType type = pri.drawType();
                switch (type)
                {
                case WD::WDPrimitiveSet::DrawType::DT_Array:
                    {
                        auto&       priData =   pri.drawArrayData();
                        uint        nStart  =   priData.first;
                        uint        nCount  =   priData.first + priData.second;
    
                        for (size_t i = nStart; i < nCount; i += 3)
                        {
                            const DVec3& pt0    =   transPositions[i + 0];
                            const DVec3& pt1    =   transPositions[i + 1];
                            const DVec3& pt2    =   transPositions[i + 2];

                            auto        ret     = param.intersectTriangle(pt0, pt1, pt2);
                            if (!ret.first)
                                continue;

                            if (outResult.setFromRay(param.ray(), ret.second))
                            {
                                outResult.setPrimitive(PickRetPri(DTriangle3(pt0, pt1, pt2)));
                                bPicked = true;
                            }
                        }
                    } 
                    break;
                case WD::WDPrimitiveSet::DrawType::DT_ElementByte:
                    {
                        const WDPrimitiveSet::ElementByteData& indices = pri.drawElementByteData();
                        for (size_t i = 0; i < indices.size(); i += 3) 
                        {
                            const DVec3& pt0    =   transPositions[indices[i + 0]];
                            const DVec3& pt1    =   transPositions[indices[i + 1]];
                            const DVec3& pt2    =   transPositions[indices[i + 2]];

                            auto        ret     = param.intersectTriangle(pt0, pt1, pt2);
                            if (!ret.first)
                                continue;

                            if (outResult.setFromRay(param.ray(), ret.second))
                            {
                                outResult.setPrimitive(PickRetPri(DTriangle3(pt0, pt1, pt2)));
                                bPicked = true;
                            }
                        }
                    }
                    break;
                case WD::WDPrimitiveSet::DrawType::DT_ElementUShort:
                    {
                        const WDPrimitiveSet::ElementUShortData& indices = pri.drawElementUShortData();
                        for (size_t i = 0; i < indices.size(); i += 3)
                        {
                            const DVec3& pt0    =   transPositions[indices[i + 0]];
                            const DVec3& pt1    =   transPositions[indices[i + 1]];
                            const DVec3& pt2    =   transPositions[indices[i + 2]];

                            auto        ret     = param.intersectTriangle(pt0, pt1, pt2);
                            if (!ret.first)
                                continue;

                            if (outResult.setFromRay(param.ray(), ret.second))
                            {
                                outResult.setPrimitive(PickRetPri(DTriangle3(pt0, pt1, pt2)));
                                bPicked = true;
                            }
                        }
                    }
                    break;
                case WD::WDPrimitiveSet::DrawType::DT_ElementUInt:
                    {
                        const WDPrimitiveSet::ElementUIntData& indices = pri.drawElementUIntData();
                        for (size_t i = 0; i < indices.size(); i += 3)
                        {
                            const DVec3& pt0    =   transPositions[indices[i + 0]];
                            const DVec3& pt1    =   transPositions[indices[i + 1]];
                            const DVec3& pt2    =   transPositions[indices[i + 2]];

                            auto        ret     = param.intersectTriangle(pt0, pt1, pt2);
                            if (!ret.first)
                                continue;

                            if (outResult.setFromRay(param.ray(), ret.second))
                            {
                                outResult.setPrimitive(PickRetPri(DTriangle3(pt0, pt1, pt2)));
                                bPicked = true;
                            }
                        }
                    }
                    break;
                default:
                    break;
                }
            }
            break;
        case WDPrimitiveSet::PT_TriangleStrip:
            {
                auto&       priData =   pri.drawArrayData();
                uint        nStart  =   priData.first;
                uint        nCount  =   priData.first + priData.second;
    
                DVec3       pt0     =   transPositions[nStart + 0];
                DVec3       pt1     =   transPositions[nStart + 1];
    
                for (uint i = nStart + 2; i < nCount; ++i)
                {
                    const DVec3& pt2    =   transPositions[i];

                    auto        ret     =   param.intersectTriangle(pt0, pt1, pt2);

                    if (ret.first && outResult.setFromRay(param.ray(), ret.second))
                    {
                        outResult.setPrimitive(PickRetPri(DTriangle3(pt0, pt1, pt2)));
                        bPicked = true;
                    }

                    pt0 = pt1;
                    pt1 = pt2;
                }
            }
            break;
        case WDPrimitiveSet::PT_TriangleFan:
            {
                auto&       priData =   pri.drawArrayData();
                uint        nStart  =   priData.first;
                uint        nCount  =   priData.first + priData.second;
    
                const DVec3& pt0    =   transPositions[nStart + 0];
                DVec3        pt1    =   transPositions[nStart + 1];
    
                for (uint i = nStart + 2; i < nCount; ++i)
                {
                    const DVec3& pt2    =   transPositions[i];

                    auto        ret     =   param.intersectTriangle(pt0, pt1, pt2);
                    
                    if (ret.first && outResult.setFromRay(param.ray(), ret.second))
                    {
                        outResult.setPrimitive(PickRetPri(DTriangle3(pt0, pt1, pt2)));
                        bPicked = true;
                    }

                    pt1 = pt2;
                }
            }
            break;
        default:
            break;
        }
    }

    return bPicked;
}
WDSelectionInterface::FrameSelectResult WDGeometry::frameSelect(const DMat4& transformMatrix
    , const WDFrameSelectParam& param) const
{
    using SelectRet = WDSelectionInterface::FrameSelectResult;
    if(_pMesh == nullptr)
        return SelectRet::FSR_NoData;

    auto& positions = _pMesh->positions();
    if (positions.empty())
        return SelectRet::FSR_NoData;
    
    Mat4 transMat = transformMatrix * this->transform();
    // 首先做包围盒计算
    DAabb3 aabb = _pMesh->aabb();
    aabb.transform(transMat);
    
    auto aabbRet = param.intersectAabb(aabb);
    switch (aabbRet)
    {
    case WD::WDFrameSelectParam::R_WhollyWithout: //aabb全部在视椎体外，那么几何数据也肯定都在视椎体外，直接返回
        return SelectRet::FSR_WhollyWithout;
    case WD::WDFrameSelectParam::R_WhollyWithin: //aabb全部在视椎体内，那么几何数据也肯定全部在视椎体内，直接返回
        return SelectRet::FSR_WhollyWithin;
    default:
        break;
    }

    std::vector<DVec3> transPositions(positions.size());
    for (size_t i = 0; i < positions.size(); ++i)
    {
        transPositions[i] = transMat * DVec3(positions[i]);
    }

    const WDMesh::PrimitiveSets* pPris = nullptr;
    switch (param.frameSelectionMode())
    {
    case WD::WDFrameSelectParam::FSM_Normal:
        {
            pPris = &(_pMesh->primitiveSets(WDMesh::Solid));
        }break;
    case WD::WDFrameSelectParam::FSM_WireFrame:
        {
            pPris = &(_pMesh->primitiveSets(WDMesh::WireFrame));
        }break;
    default:
        break;
    }
    if (pPris == nullptr)
        return SelectRet::FSR_NoData;
    
    /*
    * 可以通过 bPartWithin 和 bPartWithOut判断相交情况
    *   1. bPartWithin && bPartWithout : 表明一部分在视椎体内，一部分在视椎体外
    *   2. bPartWithin && !bPartWithout : 表明全部在视椎体内
    *   3. !bPartWithin && bPartWithout : 表明全部在视椎体外(即不想交)
    *   4. !bPartWithin && !bPartWithout : 计算的数据源有问题，数据正常的情况下不会出现此结果
    */
    bool bPartWithin = false;
    bool bPartWithout = false;

    //通过框选参数的返回结果设置 bPartWithin和bPartWithout 的标记值
    auto _setFlag = [](bool& bPartWithin, bool& bPartWithout, WDFrameSelectParam::Result ret)
    {
        switch (ret)
        {
        case WD::WDFrameSelectParam::R_InvaildData:
            break;
        case WD::WDFrameSelectParam::R_WhollyWithout:
            {
                bPartWithout = true;
            }break;
        case WD::WDFrameSelectParam::R_PartiallyWithin:
            {
                bPartWithin = true;
                bPartWithout = true;
            }break;
        case WD::WDFrameSelectParam::R_WhollyWithin:
            {
                bPartWithin = true;
            }break;
        default:
            break;
        }
    };

    const auto& pris = *pPris;
    for (const auto& pri : pris)
    {
        //如果已经判断到只有部分在视椎体内，则跳出循环，不再继续判断
        if (bPartWithin && bPartWithout)
            break;
        if (pri.empty())
            continue;
        switch (pri.primitiveType())
        {
        case WDPrimitiveSet::PT_Points:
            {
                switch (pri.drawType())
                {
                case WDPrimitiveSet::DT_Array:
                    {
                        auto&   priData     =   pri.drawArrayData();
                        uint    nStart      =   priData.first;
                        uint    nCount      =   priData.first + priData.second;
    
                        for (uint i = nStart; i < nCount; ++i)
                        {
                            if (bPartWithin && bPartWithout)
                                break;
                            const DVec3& p0 =   transPositions[i];
                            auto ret        =   param.intersectPoint(p0);
                            _setFlag(bPartWithin, bPartWithout, ret);
                        }
                    }
                    break;
                case WDPrimitiveSet::DT_ElementByte:
                    {
                        auto&   indices     =   pri.drawElementByteData();
                        for (size_t i = 0; i < indices.size(); ++i)
                        {
                            if (bPartWithin && bPartWithout)
                                break;
                            const DVec3& p0 =   transPositions[indices[i]];
                            auto ret        =   param.intersectPoint(p0);
                            _setFlag(bPartWithin, bPartWithout, ret);
                        }
                    }
                    break;
                case WDPrimitiveSet::DT_ElementUShort:
                    {
                        auto&   indices     =   pri.drawElementUShortData();
                        for (size_t i = 0; i < indices.size(); ++i)
                        {
                            if (bPartWithin && bPartWithout)
                                break;
                            const DVec3& p0 =   transPositions[indices[i]];
                            auto ret        =   param.intersectPoint(p0);
                            _setFlag(bPartWithin, bPartWithout, ret);
                        }
                    }
                    break;
                case WDPrimitiveSet::DT_ElementUInt:
                    {
                        auto&   indices     =   pri.drawElementUIntData();
                        for (size_t i = 0; i < indices.size(); ++i)
                        {
                            if (bPartWithin && bPartWithout)
                                break;
                            const DVec3& p0 =   transPositions[indices[i]];
                            auto ret        =   param.intersectPoint(p0);
                            _setFlag(bPartWithin, bPartWithout, ret);
                        }
                    }
                    break;
                }
            }
            break;
        case WDPrimitiveSet::PT_Lines:
            {
                switch (pri.drawType())
                {
                case WDPrimitiveSet::DT_Array:
                    {
                        auto&   priData     =   pri.drawArrayData();
                        uint    nStart      =   priData.first;
                        uint    nCount      =   priData.first + priData.second;
                        for (uint i = nStart; i < nCount; i += 2)
                        {
                            if (bPartWithin && bPartWithout)
                                break;
                            const DVec3& p0 =   transPositions[i + 0];
                            const DVec3& p1 =   transPositions[i + 1];
                            auto ret        =   param.intersectSegment(p0, p1);
                            _setFlag(bPartWithin, bPartWithout, ret);
                        }
                    }
                    break;
                case WDPrimitiveSet::DT_ElementByte:
                    {
                        auto&   indices     =   pri.drawElementByteData();
                        for (size_t i = 0; i < indices.size(); i += 2)
                        {
                            if (bPartWithin && bPartWithout)
                                break;
                            const DVec3& p0 =   transPositions[indices[i + 0]];
                            const DVec3& p1 =   transPositions[indices[i + 1]];
                            auto ret        =   param.intersectSegment(p0, p1);
                            _setFlag(bPartWithin, bPartWithout, ret);
                        }
                    }
                    break;
                case WDPrimitiveSet::DT_ElementUShort:
                    {
                        auto&   indices     =   pri.drawElementUShortData();
                        for (size_t i = 0; i < indices.size(); i += 2)
                        {
                            if (bPartWithin && bPartWithout)
                                break;
                            const DVec3& p0 =   transPositions[indices[i + 0]];
                            const DVec3& p1 =   transPositions[indices[i + 1]];
                            auto ret        =   param.intersectSegment(p0, p1);
                            _setFlag(bPartWithin, bPartWithout, ret);
                        }
                    }
                    break;
                case WDPrimitiveSet::DT_ElementUInt:
                    {
                        auto&   indices     =   pri.drawElementUIntData();
                        for (size_t i = 0; i < indices.size(); i += 2)
                        {
                            if (bPartWithin && bPartWithout)
                                break;
                            const DVec3& p0 =   transPositions[indices[i + 0]];
                            const DVec3& p1 =   transPositions[indices[i + 1]];
                            auto ret        =   param.intersectSegment(p0, p1);
                            _setFlag(bPartWithin, bPartWithout, ret);
                        }
                    }
                default:
                    break;
                }
            }
            break;
        case WDPrimitiveSet::PT_LineLoop:
            {
                auto&   priData     =   pri.drawArrayData();
                uint    nStart      =   priData.first;
                uint    nCount      =   priData.first + priData.second;
                for (uint i = nStart; i < nCount; ++i)
                {
                    if (bPartWithin && bPartWithout)
                        break;
    
                    if (i == nCount - 1)
                    {
                        const DVec3& p0 =   transPositions[i + 0];
                        const DVec3& p1 =   transPositions[nStart];
                        auto ret        =   param.intersectSegment(p0, p1);
                        _setFlag(bPartWithin, bPartWithout, ret);
                    }
                    else
                    {
                        const DVec3& p0 =   transPositions[i + 0];
                        const DVec3& p1 =   transPositions[i + 1];
                        auto ret        =   param.intersectSegment(p0, p1);
                        _setFlag(bPartWithin, bPartWithout, ret);
                    }
                }
            }
        case WDPrimitiveSet::PT_LineStrip:
            {
                auto&   priData     =   pri.drawArrayData();
                uint    nStart      =   priData.first;
                uint    nCount      =   priData.first + priData.second;
    
                for (uint i = nStart; i < nCount - 1; ++i)
                {
                    if (bPartWithin && bPartWithout)
                        break;
                    const DVec3& p0 =   transPositions[i + 0];
                    const DVec3& p1 =   transPositions[i + 1];
                    auto ret        =   param.intersectSegment(p0, p1);
                    _setFlag(bPartWithin, bPartWithout, ret);
                }
            }
            break;
        case WDPrimitiveSet::PT_Triangles:
            {
                // 目前只做了三角形拾取，还没有做线根点的拾取
                WDPrimitiveSet::DrawType type = pri.drawType();
                switch (type)
                {
                case WD::WDPrimitiveSet::DrawType::DT_Array:
                    {
                        auto&       priData =   pri.drawArrayData();
                        uint        nStart  =   priData.first;
                        uint        nCount  =   priData.first + priData.second;
                        for (size_t i = nStart; i < nCount; i += 3)
                        {
                            if (bPartWithin && bPartWithout)
                                break;
                            const DVec3& p0 =   transPositions[i + 0];
                            const DVec3& p1 =   transPositions[i + 1];
                            const DVec3& p2 =   transPositions[i + 2];
                            auto ret        =   param.intersectTriangle(p0, p1, p2);
                            _setFlag(bPartWithin, bPartWithout, ret);
                        }
                    } 
                    break;
                case WD::WDPrimitiveSet::DrawType::DT_ElementByte:
                    {
                        const WDPrimitiveSet::ElementByteData& indices = pri.drawElementByteData();
                        for (size_t i = 0; i < indices.size(); i += 3) 
                        {
                            if (bPartWithin && bPartWithout)
                                break;
                            const DVec3& p0 =   transPositions[indices[i + 0]];
                            const DVec3& p1 =   transPositions[indices[i + 1]];
                            const DVec3& p2 =   transPositions[indices[i + 2]];
                            auto ret        =   param.intersectTriangle(p0, p1, p2);
                            _setFlag(bPartWithin, bPartWithout, ret);
                        }
                    }
                    break;
                case WD::WDPrimitiveSet::DrawType::DT_ElementUShort:
                    {
                        const WDPrimitiveSet::ElementUShortData& indices = pri.drawElementUShortData();
                        for (size_t i = 0; i < indices.size(); i += 3)
                        {
                            if (bPartWithin && bPartWithout)
                                break;
                            const DVec3& p0 =   transPositions[indices[i + 0]];
                            const DVec3& p1 =   transPositions[indices[i + 1]];
                            const DVec3& p2 =   transPositions[indices[i + 2]];
                            auto ret        =   param.intersectTriangle(p0, p1, p2);
                            _setFlag(bPartWithin, bPartWithout, ret);
                        }
                    }
                    break;
                case WD::WDPrimitiveSet::DrawType::DT_ElementUInt:
                    {
                        const WDPrimitiveSet::ElementUIntData& indices = pri.drawElementUIntData();
                        for (size_t i = 0; i < indices.size(); i += 3)
                        {
                            if (bPartWithin && bPartWithout)
                                break;
                            const DVec3& p0 =   transPositions[indices[i + 0]];
                            const DVec3& p1 =   transPositions[indices[i + 1]];
                            const DVec3& p2 =   transPositions[indices[i + 2]];
                            auto ret        =   param.intersectTriangle(p0, p1, p2);
                            _setFlag(bPartWithin, bPartWithout, ret);
                        }
                    }
                    break;
                default:
                    break;
                }
            }
            break;
        case WDPrimitiveSet::PT_TriangleStrip:
            {
                auto&       priData =   pri.drawArrayData();
                uint        nStart  =   priData.first;
                uint        nCount  =   priData.first + priData.second;
    
                DVec3    p0         =   transPositions[nStart + 0];
                DVec3    p1         =   transPositions[nStart + 1];
                for (uint i = nStart + 2; i < nCount; ++ i)
                {
                    if (bPartWithin && bPartWithout)
                        break;
                    const DVec3& p2 =   transPositions[i];
                    auto ret        =   param.intersectTriangle(p0, p1, p2);
                    _setFlag(bPartWithin, bPartWithout, ret);

                    p0 = p1;
                    p1 = p2;
                }
            }
            break;
        case WDPrimitiveSet::PT_TriangleFan:
            {
                auto&       priData =   pri.drawArrayData();
                uint        nStart  =   priData.first;
                uint        nCount  =   priData.first + priData.second;
                const DVec3& p0     =   transPositions[nStart + 0];
                DVec3           p1  =   transPositions[nStart + 1];
    
                for (uint i = nStart + 2; i < nCount; ++ i)
                {
                    if (bPartWithin && bPartWithout)
                        break;
                    const DVec3& p2 =   transPositions[i];
                    auto ret        =   param.intersectTriangle(p0, p1, p2);
                    _setFlag(bPartWithin, bPartWithout, ret);

                    p1 = p2;
                }
            }
            break;
        default:
            break;
        }
    }
    
    if (bPartWithin && bPartWithout) //部分在视椎体内，部分在视椎体外
        return SelectRet::FSR_PartiallyWithin;
    else if (bPartWithin && !bPartWithout) //全部在视椎体内
        return SelectRet::FSR_WhollyWithin;
    else if (bPartWithout && !bPartWithin) //全部在视椎体外
        return SelectRet::FSR_WhollyWithout;
    else    //无数据
        return SelectRet::FSR_NoData;

}


WD_NAMESPACE_END

