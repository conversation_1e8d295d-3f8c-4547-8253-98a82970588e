#pragma     once

#include    "../common/WDObject.h"
#include    "../math/geometric/WDPrimitiveSet.h"
#include    "../GL/WDVertAttribute.h"

WD_NAMESPACE_BEGIN

WD_DECL_CLASS_UUID(WDM<PERSON>,"22D259C2-4D90-4E5D-9D3C-C126AE4C23C5");
/**
 * @brief 网格对象
 */
class WD_API WDMesh: public WDObject
{
    WD_DECL_OBJECT(WDMesh)
public:
    /**
     * @brief PrimitiveSets
    */
    using PrimitiveSets = std::vector<WDPrimitiveSet>;
    /**
     * @brief Primitive sets type
    */
    enum PSType
    {
        // 实体
        Solid = 0,
        // 线框
        WireFrame = 1,
        // 点
        Points = 2, 
    };
private:
    // 顶点位置列表
    std::vector<FVec3>  _positions;
    // 顶点法线列表
    std::vector<FVec3>  _normals;
    // 顶点uv(纹理坐标)列表
    std::vector<FVec2>  _uvs;
    // 顶点颜色列表
    std::vector<Color>  _colors;
    // 顶点绘制方式以及索引数据,点线面
    PrimitiveSets       _primitiveSets[3];

    // 包围盒信息, 通过顶点位置数据计算得到
    DAabb3              _aabb;
    // 顶点数据描述,描述当前mesh中顶点数据的组织形式
    // 绘制过程中会使用到该数据填充到材质中,特别重要
    // 一般在Mesh对象构造时便指定该组织形式
    VertDescSetPtr      _vertDescSet;
public:
    WDMesh();
    WDMesh(const WDMesh& right) = delete;
    WDMesh(WDMesh&& right) = delete;
    WDMesh& operator=(const WDMesh& right) = delete;
    WDMesh& operator=(WDMesh&& right) = delete;
    virtual ~WDMesh();
public:
    inline void     setVertDescSetPtr(VertDescSetPtr ptr)
    {
        _vertDescSet    =   ptr;
    }
    
    VertDescSetPtr  vertDescSetPtr()
    {
        return  _vertDescSet;
    }
    VertDescSetPtr  vertDescSetPtr() const
    {
        return  _vertDescSet;
    }
    const void*     dataPtr(VertType type) const;
    /**
    * @brief 设置顶点位置列表数据
    */
    inline void setPositions(const std::vector<FVec3>& positions);
    /**
    * @brief 设置顶点位置列表数据
    */
    inline void setPositions(std::vector<FVec3>&& positions);
    /**
    * @brief 获取顶点位置列表数据
    */
    inline const std::vector<FVec3>& positions() const;
    /**
    * @brief 获取顶点位置列表数据
    */
    inline std::vector<FVec3>& positions();

    /**
    * @brief 根据顶点信息自动计算包围盒
    */
    const DAabb3& computeAabb();
    /**
     * @brief 根据已有的包围盒数据设置包围盒
    */
    inline void setAabb(const DAabb3& aabb);
    /**
    * @brief 获取包围盒数据
    */
    inline const DAabb3& aabb() const;

    /**
    * @brief 设置顶点法线列表数据
    */
    inline void setNormals(const std::vector<FVec3>& normals);
    /**
    * @brief 设置顶点位置列表数据
    */
    inline void setNormals(std::vector<FVec3>&& normals);
    /**
    * @brief 获取顶点法线列表数据
    */
    inline const std::vector<FVec3>& normals() const;
    /**
     * @brief 获取顶点法线列表数据
    */
    inline std::vector<FVec3>& normals();

    /**
    * @brief 设置顶点颜色列表数据
    */
    inline void setColors(const std::vector<Color>& colors);
    /**
    * @brief 设置顶点颜色列表数据
    */
    inline void setColors(std::vector<Color>&& colors);
    /**
    * @brief 获取顶点颜色列表数据
    */
    inline const std::vector<Color>& colors() const;
    /**
    * @brief 获取顶点颜色列表数据
    */
    inline std::vector<Color>& colors();

    /**
    * @brief 设置顶点uv列表数据
    */
    inline void setUvs(const std::vector<FVec2>& uvs);
    /**
    * @brief 设置顶点uv列表数据
    */
    inline void setUvs(std::vector<FVec2>&& uvs);
    /**
    * @brief 获取顶点uv列表数据
    */
    inline const std::vector<FVec2>& uvs() const;
    /**
    * @brief 获取顶点uv列表数据
    */
    inline std::vector<FVec2>& uvs();

    /**
    * @brief 获取 PrimitiveSets
    */
    inline const PrimitiveSets& primitiveSets(PSType idx) const
    {
        return _primitiveSets[idx];
    }
    /**
    * @brief 获取 PrimitiveSets
    */
    inline PrimitiveSets& primitiveSets(PSType idx)
    {
        return _primitiveSets[idx];
    }
    /**
    * @brief 添加 WDPrimitiveSet
    */
    inline void addPrimitiveSet(const WDPrimitiveSet& pri, PSType idx)
    {
        if (pri.empty())
        {
            assert(!pri.empty());
            return;
        }
        _primitiveSets[idx].push_back(pri);
    }
    /**
    * @brief 添加 WDPrimitiveSet
    */
    inline void addPrimitiveSet(WDPrimitiveSet&& pri, PSType idx)
    {
        if (pri.empty())
        {
            assert(!pri.empty());
            return;
        }
        _primitiveSets[idx].push_back(std::forward<WDPrimitiveSet>(pri));
    }
    /**
    * @brief 移除 WDPrimitiveSet
    */
    inline void removePrimitiveSet(const WDPrimitiveSet& pri, PSType idx)
    {
        if (pri.empty())
        {
            assert(!pri.empty());
            return;
        }
        for (auto i = _primitiveSets[idx].begin(); i != _primitiveSets[idx].end(); ++i)
        {
            if ((*i) == pri)
            {
                _primitiveSets[idx].erase(i);
                break;
            }
        }
    }
    /**
    * @brief 清除 PrimitiveSets
    */
    inline void clearPrimitiveSet(PSType idx)
    {
        _primitiveSets[idx].clear();
    }
public:
    /**
     * @brief 写入网格的顶点索引等数据到buffer中, 用于生成MD5
     *   子类需要根据自己的顶点索引数据重写该方法
     *   !注意: 这里只需要写入Mesh的顶点以及索引相关数据，不能写入name,guid等数据
     * @param writer writer对象
     * @return 返回对象自身写入的数据总长度
    */
    virtual size_t writeDataToBuffer(WDWriter& writer) const;
public:
    /**
     * @brief 根据网格对象的顶点以及实体索引信息，生成基于三角面的网格边线数据
     * @param mesh 网格对象
     * @return 网格对象实体三角面边线的索引
    */
    static WDPrimitiveSet GenerateTriangleWireframesWithMeshSolid(const WDMesh& mesh);
public:
    /**
    * @brief 从源对象拷贝数据到当前对象
    *   子类可重写
    */
    virtual void copy(const WDObject* pSrcObject) override;
    /**
    * @brief 使用当前对象克隆出一个对象
    *   子类可重写
    */
    virtual WDObject::SharedPtr clone() const override;
public:
    virtual size_t  toStream(WDWriteStream&) const;
    virtual size_t  fromStream(WDReadStream&);
};

WD_NAMESPACE_END

#include "WDMesh.inl"
