#pragma once
#include "WDGeometryStdPris.h"

WD_NAMESPACE_BEGIN

/**
* @brief 圆台体
*/
class WD_API WDGeometryCone :public WDGeometryStdPris
{
    WD_DECL_GEOMETRY_SUB_CLASS(WDGeometryCone, GT_Cone)
public:
    // 顶面直径
    float topDiameter = 1.0f;
    // 底面直径
    float bottomDiameter = 2.0f;
    // 高度
    float height = 1.0f;
public:
    /**
    * @brief 构建
    * @param topDiameter 顶面直径
    * @param bottomDiameter 底面直径
    * @param height 高度
    */
    WDGeometryCone(float topDiameter = 1.0f
        , float bottomDiameter = 2.0f
        , float height = 1.0f
        , const MeshLODSelection& lodSelection = MeshLODSelection());
public:
    /**
    * @brief 重新构建
    * @param topDiameter 顶面直径
    * @param bottomDiameter 底面直径
    * @param height 高度
    */
    WDGeometryCone& rebuildMeshWithParams(float topDiameter
        , float bottomDiameter
        , float height
        , const MeshLODSelection& lodSelection = MeshLODSelection());
public:
    virtual WDObject::SharedPtr clone() const override;
protected:
    virtual void copyParam(const WDGeometryStdPris& src) override;
    virtual WDMesh::SharedPtr generateMesh() const override;
    virtual FKeyPoints generateKeyPoints() const override;
};

WD_NAMESPACE_END


