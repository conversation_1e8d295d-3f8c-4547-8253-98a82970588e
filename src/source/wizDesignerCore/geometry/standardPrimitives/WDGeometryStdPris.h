#pragma once
#include "../WDGeometry.h"
#include "../../math/geometric/standardPrimitives/StandardPrimitivesBuilder.h"
#include "../../math/geometric/standardPrimitives/StandardPrimitiveCommon.h"
#include "../../common/WDSharedObjectPool.h"
#include "../../graphable/WDGraphableInterface.h"

WD_NAMESPACE_BEGIN

/**
* @brief 标准基本体 Standard Primitives
*/
class WD_API WDGeometryStdPris : public WDGeometry
{
    WD_DECL_GEOMETRY_SUB_CLASS(WDGeometryStdPris, GT_Unknown)
public:
    // 生成的网格精细度
    MeshLODSelection lod;
protected:
    WDGeometryStdPris(WDGeometryType type
        , const MeshLODSelection& lod = MeshLODSelection());
public:
    virtual ~WDGeometryStdPris();
public:
    /**
    * @brief 重新构建网格对象
    */
    WDGeometryStdPris& rebuildMesh();
    /**
    * @brief 获取基本体关键点
    *   使用当前基本体参数构建所有关键点数据
    * @param sNumber 第一个关键点的编号，后续关键点编号从 sNumber开始累加
    * @return 如果当前类型的基本体没有关键点，则返回空数组
    */
    WDKeyPoints getKeyPoints(int sNumber = 1) const;
public:
    virtual void copy(const WDObject* pSrcObject) override;
    virtual WDObject::SharedPtr clone() const override;
protected:
    /**
     * @brief 拷贝基本体参数，子类重写
     * @param src 源
     */
    virtual void copyParam(const WDGeometryStdPris& src) = 0;
    /**
     * @brief 根据参数生成网格对象
     * @return 网格对象
     */
    virtual WDMesh::SharedPtr generateMesh() const = 0;
    /**
     * @brief 根据参数生成关键点列表
     * @return 关键点列表
     */
    virtual FKeyPoints generateKeyPoints() const = 0;
protected:
    /**
    * @brief 从MeshStruct数据源取值
    * @param mesh MeshStruct 数据源
    * @param autoComputeAabb 是否自动使用顶点列表更新几何体的包围盒数据
    *   默认开启，如果集合体的Mesh数据中已经有现成的包围盒数据时，可以关闭该选项，并手动 设置Mesh的Aabb包围盒
    */
    static WDMesh::SharedPtr FromMeshStruct(const MeshStruct& meshStruct, bool autoComputeAabb = true);
private:
    /**
     * @brief 关键点结构转换
     * @param keyPts 原始的关键点数据
     * @param sNumber 转换后关键点的起始number
     * @return 转换结果
     */
    static WDKeyPoints KeyPointConvert(const FKeyPoints& keyPts, int sNumber);
};

WD_NAMESPACE_END

