#include "WDGeometryCylinder.h"
#include "../../WDTranslate.h"


WD_NAMESPACE_BEGIN

WDGeometryCylinder::WDGeometryCylinder(float diameter
    , float height
    , const MeshLODSelection& lodSelection)
    :WDGeometryStdPris(GeometryType, lodSelection)
    , diameter(diameter)
    , height(height)
{
    this->rebuildMesh();
}

WDGeometryCylinder& WDGeometryCylinder::rebuildMeshWithParams(float tdiameter
    , float theight
    , const MeshLODSelection& lodSelection)
{
    this->diameter = tdiameter;
    this->height = theight;
    this->lod = lodSelection;
    this->rebuildMesh();
    return *this;
}

WDObject::SharedPtr WDGeometryCylinder::clone() const
{
    auto p = WDGeometryCylinder::MakeShared();
    p->copy(this);
    return p;
}

void WDGeometryCylinder::copyParam(const WDGeometryStdPris& src)
{
    const WDGeometryCylinder* pSrc = dynamic_cast<const WDGeometryCylinder*>(&src);
    if (pSrc == nullptr)
        return;
    this->diameter  = pSrc->diameter;
    this->height = pSrc->height;
}

WDMesh::SharedPtr WDGeometryCylinder::generateMesh() const
{
    return FromMeshStruct(CylinderBuilder::Mesh(diameter
        , height
        , lod));
}
FKeyPoints WDGeometryCylinder::generateKeyPoints() const
{
    return CylinderBuilder::KeyPoints(diameter
        , height);
}


WD_NAMESPACE_END
