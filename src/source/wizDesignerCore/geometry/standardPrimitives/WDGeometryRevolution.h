#pragma once
#include "WDGeometryStdPris.h"
WD_NAMESPACE_BEGIN

/**
* @brief 旋转体 REVO
*/
class WD_API WDGeometryRevolution :public WDGeometryStdPris
{
    WD_DECL_GEOMETRY_SUB_CLASS(WDGeometryRevolution, GT_Revolution)
public:
    // 旋转中心点
    FVec3 center = FVec3::Zero();
    // 旋转轴
    FVec3 axis = FVec3::AxisX();
    // 旋转角度,角度制(取值范围为(0.0, 360.0])
    float angle = 90.0f;
    // 旋转面顶点数组
    FVec3Vector loop = FVec3Vector();
public:
    /**
    * @brief 构建
    * @param center 旋转中心点
    * @param axis 旋转轴
    * @param angle 旋转角度,角度制(取值范围为(0.0, 360.0])
    * @param loop 旋转面顶点数组
    */
    WDGeometryRevolution(const FVec3& center = FVec3::Zero()
        , const FVec3& axis = FVec3::AxisX()
        , const float angle = 90.0f
        , const FVec3Vector& loop = FVec3Vector()
        , const MeshLODSelection& lodSelection = MeshLODSelection());
public:
    /**
    * @brief 重新构建
    * @param center 旋转中心点
    * @param axis 旋转轴
    * @param angle 旋转角度,角度制(取值范围为(0.0, 360.0])
    * @param loop 旋转面顶点数组
    */
    WDGeometryRevolution& rebuildMeshWithParams(const FVec3& center
        , const FVec3& axis
        , const float angle
        , const std::vector<FVec3>& loop
        , const MeshLODSelection& lodSelection = MeshLODSelection());
public:
    virtual WDObject::SharedPtr clone() const override;
protected:
    virtual void copyParam(const WDGeometryStdPris& src) override;
    virtual WDMesh::SharedPtr generateMesh() const override;
    virtual FKeyPoints generateKeyPoints() const override;

};


WD_NAMESPACE_END


