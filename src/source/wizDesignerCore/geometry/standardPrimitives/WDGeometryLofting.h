#pragma once
#include "WDGeometryStdPris.h"

WD_NAMESPACE_BEGIN

/**
* @brief 放样体
*/
class WD_API WDGeometryLofting : public WDGeometryStdPris
{
    WD_DECL_GEOMETRY_SUB_CLASS(WDGeometryLofting, GT_Lofting)
public:
    // 起始端面的法线方向
    FVec3 sDirection    = FVec3::AxisX();
    // 结束端面的法线方向
    FVec3 eDirection    = FVec3::AxisNX();
    // Plaxis方向角坐标
    FVec3 plaxisPos     = FVec3::Zero();
    // 放样的起始面轮廓顶点
    //  其中:
    //  loopS[i].x, loopE[i].x : 表示顶点位置的x分量
    //  loopS[i].y, loopE[i].y : 表示顶点位置的y分量
    //  loopS[i].z, loopE[i].z : 表示顶点的圆角半径
    FVec3Vector loopS   = FVec3Vector();
    // 放样的终止面轮廓顶点（目前需与起始面顶点数保持一致，仅仅是将起始面的边长进行了延长）
    //  其中:
    //  loopS[i].x, loopE[i].x : 表示顶点位置的x分量
    //  loopS[i].y, loopE[i].y : 表示顶点位置的y分量
    //  loopS[i].z, loopE[i].z : 表示顶点的圆角半径
    FVec3Vector loopE   = FVec3Vector();
    // 放样的曲线顶点列表
    FVec3Vector curve   = FVec3Vector();
public:
    /**
     * @brief 构建
     * @param sDirection 起始端面的法线方向
     * @param eDirection 结束端面的法线方向
     * @param plaxisPos Plaxis方向角坐标
     * @param loopS 放样的起始面轮廓顶点
     * @param loopE 放样的终止面轮廓顶点（目前需与起始面顶点数保持一致，仅仅是将起始面的边长进行了延长）
     *   其中:
     *       loopS[i].x, loopE[i].x:表示顶点位置的x分量
     *       loopS[i].y, loopE[i].y:表示顶点位置的y分量
     *       loopS[i].z, loopE[i].z:表示顶点的圆角半径
     * @param curve 放样的曲线顶点列表
    */
    WDGeometryLofting(const FVec3& sDirection = FVec3::AxisX()
        , const FVec3& eDirection = FVec3::AxisNX()
        , const FVec3& plaxisPos = FVec3::Zero()
        , const FVec3Vector& loopS = FVec3Vector()
        , const FVec3Vector& loopE = FVec3Vector()
        , const FVec3Vector& curve = FVec3Vector()
        , const MeshLODSelection& lodSelection = MeshLODSelection());
public:
    /**
    * @brief 重新构建
    * @param xLength x方向长度
    * @param yLength y方向长度
    * @param zLength z方向长度
    */
    WDGeometryLofting& rebuildMeshWithParams(const FVec3& sDirection
        , const FVec3& eDirection
        , const FVec3& plaxisPos
        , const FVec3Vector& loopS
        , const FVec3Vector& loopE
        , const FVec3Vector& curve
        , const MeshLODSelection& lodSelection = MeshLODSelection());
public:
    virtual WDObject::SharedPtr clone() const override;
protected:
    virtual void copyParam(const WDGeometryStdPris& src) override;
    virtual WDMesh::SharedPtr generateMesh() const override;
    virtual FKeyPoints generateKeyPoints() const override;
};



WD_NAMESPACE_END



