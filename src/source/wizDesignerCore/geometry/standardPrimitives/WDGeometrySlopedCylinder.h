#pragma once
#include "WDGeometryStdPris.h"

WD_NAMESPACE_BEGIN

/**
* @brief 斜圆柱体
*/
class WD_API WDGeometrySlopedCylinder :public WDGeometryStdPris
{
    WD_DECL_GEOMETRY_SUB_CLASS(WDGeometrySlopedCylinder, GT_SlopedCylinder)
public:
    // 直径
    float diameter = 1.0f;
    // 高度
    float height = 1.0f;
    // 顶面与X轴的夹角(相当于顶面绕Y轴旋转),角度制,取值范围[-89.9999f, 89.9999f]
    float topXShear = 0.0f;
    // 顶面与Y轴的夹角(相当于顶面绕X轴转转),角度制,取值范围[-89.9999f, 89.9999f]
    float topYShear = 0.0f;
    // 底面与X轴的夹角(相当于底面绕Y轴旋转),角度制,取值范围[-89.9999f, 89.9999f]
    float bottomXShear = 0.0f;
    // 底面与Y轴的夹角(相当于底面绕X轴旋转),角度制,取值范围[-89.9999f, 89.9999f]
    float bottomYShear = 0.0f;
public:
    /**
    * @brief 构建
    * @param diameter 直径
    * @param height 高度
    * @param topXShear 顶面与X轴的夹角(相当于顶面绕Y轴旋转),角度制,取值范围[-89.9999f, 89.9999f]
    * @param topYShear 顶面与Y轴的夹角(相当于顶面绕X轴转转),角度制,取值范围[-89.9999f, 89.9999f]
    * @param bottomXShear 底面与X轴的夹角(相当于底面绕Y轴旋转),角度制,取值范围[-89.9999f, 89.9999f]
    * @param bottomYShear 底面与Y轴的夹角(相当于底面绕X轴旋转),角度制,取值范围[-89.9999f, 89.9999f]
    */
    WDGeometrySlopedCylinder(float diameter = 1.0f
        , float height = 1.0f
        , float topXShear = 0.0f
        , float topYShear = 0.0f
        , float bottomXShear = 0.0f
        , float bottomYShear = 0.0f
        , const MeshLODSelection& lodSelection = MeshLODSelection());
public:
    /**
    * @brief 重新构建
    * @param diameter 直径
    * @param height 高度
    * @param topXShear 顶面与X轴的夹角(相当于顶面绕Y轴旋转),角度制,取值范围[-89.9999f, 89.9999f]
    * @param topYShear 顶面与Y轴的夹角(相当于顶面绕X轴转转),角度制,取值范围[-89.9999f, 89.9999f]
    * @param bottomXShear 底面与X轴的夹角(相当于底面绕Y轴旋转),角度制,取值范围[-89.9999f, 89.9999f]
    * @param bottomYShear 底面与Y轴的夹角(相当于底面绕X轴旋转),角度制,取值范围[-89.9999f, 89.9999f]
    */
    WDGeometrySlopedCylinder& rebuildMeshWithParams(float diameter
        , float height
        , float topXShear = 0.0f
        , float topYShear = 0.0f
        , float bottomXShear = 0.0f
        , float bottomYShear = 0.0f
        , const MeshLODSelection& lodSelection = MeshLODSelection());
public:
    virtual WDObject::SharedPtr clone() const override;
protected:
    virtual void copyParam(const WDGeometryStdPris& src) override;
    virtual WDMesh::SharedPtr generateMesh() const override;
    virtual FKeyPoints generateKeyPoints() const override;
};

WD_NAMESPACE_END


