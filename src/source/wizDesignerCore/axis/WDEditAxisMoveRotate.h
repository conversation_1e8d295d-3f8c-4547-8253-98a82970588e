#pragma once
/**
* @file WDEditAxisMove.h
* @brief 定义移动编辑坐标轴
* <AUTHOR>
* @date xxxx-xx-xx
*/
#include "WDAxis.h"

WD_NAMESPACE_BEGIN

class WDEditAxisMoveRotatePrivate;

/**
 * @brief 移动旋转编辑轴
 */
class WD_API WDEditAxisMoveRotate: public WDEditAxis
{
public:
    /**
    * @brief 轴标志
    */
    enum Axis
    {
        // 无
        A_Null = 0,
        // 沿着 X 轴移动
        A_MX,
        // 沿着 Y 轴移动
        A_MY,
        // 沿着 Z 轴移动
        A_MZ,
        // 沿着 XOY平面移动
        A_MXY,
        // 沿着 YOZ平面移动
        A_MYZ,
        // 沿着 ZOX平面移动
        A_MZX,
        // 绕 X 轴旋转
        A_RX,
        // 绕 Y 轴旋转
        A_RY,
        // 绕 Z 轴旋转
        A_RZ,
    };
    /**
     * @brief 操作类型
    */
    enum HandleType 
    {
        // 无
        HT_None = 0,
        // 移动操作
        HT_Move,
        // 旋转操作
        HT_Rotate,
    };
    /**
     * @brief 选中旋转轴的的弧线部分
    */
    enum AxisArcPart
    {
        // 无
        AAP_None = 0,
        // X轴和Y轴之间的弧线，更接近X的部分
        AAP_XYA,
        // X轴和Y轴之间的弧线，更接近Y的部分
        AAP_XYB,
        // Y轴和Z轴之间的弧线，更接近Y的部分
        AAP_YZA,
        // Y轴和Z轴之间的弧线，更接近Z的部分
        AAP_YZB,
        // Z轴和X轴之间的弧线，更接近Z的部分
        AAP_ZXA,
        // Z轴和X轴之间的弧线，更接近X的部分
        AAP_ZXB,
    };
    /**
    * @brief 移动/旋转通知
    * @param status 编辑状态
    * @param hType 操作类型, 用于判断是移动操作还是旋转操作
    * @param relativeOffset 相对偏移量(世界坐标)
    *   - 如果是移动操作, relativeOffset.xyz()代表移动方向, relativeOffset.w 代表在这个方向上的移动距离
    *   - 如果是旋转操作, relativeOffset.xyz()代表旋转轴, relativeOffset.w 代表绕着这个轴旋转的角度
    *   - 当EditStatus为 EditStart时: 该参数取值总是 DVec4(0)
    *   - 当EditStatus为 Editing时: 该参数取值为前一次鼠标位置与当前鼠标位置对应的世界坐标移动偏移量
    *   - 当EditStatus为 EditEnd时:  该参数取值总是 DVec4(0) 
    * @param absoluteOffset 绝对偏移量(世界坐标)
    *   - 如果是移动操作, absoluteOffset.xyz()代表移动方向, absoluteOffset.w 代表在这个方向上的移动距离
    *   - 如果是旋转操作, absoluteOffset.xyz()代表旋转轴, absoluteOffset.w 代表绕着这个轴旋转的角度
    *   - 当EditStatus为 EditStart时: 该参数取值总是 DVec4(0)
    *   - 当EditStatus为 Editing时: 该参数取值为从鼠标按下开始到当前鼠标的所有移动偏移量的累加
    *   - 当EditStatus为 EditEnd时:  该参数取值为从鼠标按下开始到鼠标结束后所有移动偏移量的累加
    * @param sender 调用通知的轴对象
    */
    using MDelegate = WDTMultiDelegate<void(EditStatus status
        , HandleType hType
        , const DVec4& relativeOffset
        , const DVec4& absoluteOffset
        , WDEditAxisMoveRotate& sender)>;
    /**
     * @brief 右键菜单回调
     * @param axis 轴标志 @see ref Axis
     * @param screenPos 鼠标按下的屏幕坐标位置
    * @param sender 调用通知的轴对象
    */
    using MContextMenuDelegate = WDTMultiDelegate<void(int axis
        , const IVec2& screenPos
        , WDEditAxisMoveRotate& sender)>;
    /**
     * @brief 鼠标点击通知
     * @param axis 轴标志 @see ref Axis
     * @param screenPos 鼠标按下的屏幕坐标位置
     * @param btn 鼠标按键
     * @param sender 调用通知的轴对象
    */
    using MClickedDelegate = WDTMultiDelegate<void(int axis
        , const IVec2& screenPos
        , WDEditAxis::MouseButton btn
        , WDEditAxisMoveRotate& sender)>;
    /**
     * @brief 鼠标双击通知
     * @param axis 轴标志 @see ref Axis
     * @param screenPos 鼠标按下的屏幕坐标位置
     * @param btn 鼠标按键
     * @param sender 调用通知的轴对象
    */
    using MDBClickedDelegate = WDTMultiDelegate<void(int axis
        , const IVec2& screenPos
        , WDEditAxis::MouseButton btn
        , WDEditAxisMoveRotate& sender)>;
private:
    WDEditAxisMoveRotatePrivate* _p;
    friend class WDEditAxisMoveRotatePrivate;
public:
    WDEditAxisMoveRotate();
    ~WDEditAxisMoveRotate();
public:
    /**
    * @brief 移动操作回调,当轴发生移动操作时，触发该回调
    */
    MDelegate& mDelegate();
    /**
     * @brief 右键菜单回调
    */
    MContextMenuDelegate& mContextMenuDelegate();
    /**
     * @brief 鼠标点击回调
    */
    MClickedDelegate& mClickedDelegate();
    /**
     * @brief 鼠标双击回调
    */
    MDBClickedDelegate& mDBClickedDelegate();
    /**
     * @brief 设置移动步长(距离)
    */
    void setStepLength(double step);
    /**
     * @brief 获取移动步长(距离)
    */
    double stepLength();
    /**
     * @brief 设置旋转步长(角度)
    */
    void setStepAngle(double step);
    /**
     * @brief 获取旋转步长(角度)
    */
    double stepAngle();
    /**
    * @brief 获取当前已经被高亮的轴
    * @return 返回高亮的轴索引
    */
    WDEditAxisMoveRotate::Axis hoveredAxis() const;
    /**
    * @brief 获取当前已经被选中的轴
    * @return 返回选中的轴索引
    */
    WDEditAxisMoveRotate::Axis selectedAxis() const;
    /**
     * @brief 获取选中的弧线部分, 用于确定朝向
    */
    AxisArcPart axisArcPart() const;
    /**
    * @brief 给定一个单位向量，计算当沿着着该单位向量移动时，移动偏移量相对于X轴的移动偏移量的权重
    */
    inline real weightX(const DVec3& vec) const
    {
        return DVec3::Dot(vec, this->axisX());
    }
    /**
    * @brief 给定一个单位向量，计算当沿着着该单位向量移动时，移动偏移量相对于Y轴的移动偏移量的权重
    */
    inline real weightY(const DVec3& vec) const
    {
        return DVec3::Dot(vec, this->axisY());
    }
    /**
    * @brief 给定一个单位向量，计算当沿着着该单位向量移动时，移动偏移量相对于Z轴的移动偏移量的权重
    */
    inline real weightZ(const DVec3& vec) const
    {
        return DVec3::Dot(vec, this->axisZ());
    }

    /**
     * @brief 执行一次移动或旋转命令, 当前轴对象将触发通知移动命令
     * @param axis 确定移动轴向的轴
     * @param offset 在该轴向上的移动偏移量或旋转角度
     *  如果指定的是移动轴，则offset代表移动的距离
     *  如果指定的是旋转轴，则offset代表旋转的角度
    */
    void exec(Axis axis, double offset);
public:
    /**
    * @brief 是否有轴被高亮
    * @return 如果有轴被高亮，则返回true,否则返回false
    */
    virtual bool isAxisHovered() const override
    {
        return hoveredAxis() != WDEditAxisMoveRotate::Axis::A_Null;
    }
    /**
    * @brief 是否有轴被选中
    * @return 如果有轴被选中,则返回true,否则返回false
    */
    virtual bool isAxisSelected() const override
    {
        return selectedAxis() != WDEditAxisMoveRotate::Axis::A_Null;
    }
    
    /**
    * @brief 取消轴的高亮状态
    */
    virtual void cancelHovered() override;
    /**
    * @brief 取消轴的选中状态
    */
    virtual void cancelSelected()override;
public:
    /**
     * @brief 预览对齐
     * @param axis 指定对齐轴
     * @param axisArcPart 对齐的旋转轴旋转部分
    */
    void    beginPreviewAlign(Axis axis, AxisArcPart axisArcPart);
    /**
     * @brief 计算对齐偏移量
     * @param vec 对齐向
     *  如果是位置对齐，则为坐标
     *  如果是朝向对齐，则为方向向量
     *  如果为std::nullopt，表示将对齐偏移量置为0
     * @return 对齐偏移量
     *  如果是位置对齐 .xyz()表示方向， w表示该方向的偏移距离
     *  如果是朝向对齐 .xyz()表示旋转轴, w表示旋转角度
    */
    DVec4   calcPreviewAlign(const std::optional<DVec3>& vec = std::nullopt);
    /**
     * @brief 结束预览对齐
    */
    void    endPreviewAlign();
public:
    virtual bool mouseButtonPress(WDContext& context, const IVec2& pos, MouseButton btn) override;
    virtual bool mouseButtonRelease(WDContext& context, const IVec2& pos, MouseButton btn) override;
    virtual bool mouseMove(WDContext& context, const IVec2& pos) override;
    virtual bool mouseDoubleClicked(WDContext& context, const IVec2& pos, MouseButton btn) override;

    virtual void update(WDContext& context) override;
    virtual void render(WDContext& context) override;
protected:
    /**
    * @brief 判断某个值是否是X轴的枚举值,子类实现
    */
    virtual bool isEnumAxisX(int value) const override
    {
        return value == WDEditAxisMoveRotate::Axis::A_MX;
    }
    /**
    * @brief 判断某个值是否是X轴的枚举值,子类实现
    */
    virtual bool isEnumAxisY(int value) const override
    {
        return value == WDEditAxisMoveRotate::Axis::A_MY;
    }
    /**
    * @brief 判断某个值是否是X轴的枚举值,子类实现
    */
    virtual bool isEnumAxisZ(int value) const override
    {
        return value == WDEditAxisMoveRotate::Axis::A_MZ;
    }
};

WD_NAMESPACE_END


