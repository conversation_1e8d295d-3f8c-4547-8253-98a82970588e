#pragma once

#include    "../common/WDObject.h"
#include    "../texture/WDTexture.h"
#include    "renderState/WDRenderStateSet.h"
#include    "../GL/WDOpenGL.h"
#include    "../GL/WDVertAttribute.h"
#include    "../GL/WDInstance.h"
#include    "../GL/WDUniform.h"

WD_NAMESPACE_BEGIN

class   WDContext;
class   WDGeometry;
class   WDMesh;
class   WDProgram;
class   WDPrimitiveSet;

/**
 * @brief 示例属性标志
 */
enum    InstanceAttr 
{
    IA_None             =   0,
    IA_Clip             =   (1<<0),
    IA_InstColor        =   (1<<1),
    IA_Selected         =   (1<<2),
    IA_ShadowCast       =   (1<<3),
    IA_ShadowRecv       =   (1<<4),
    IA_MirrowCast       =   (1<<5),
    IA_MirrowRecv       =   (1<<6),
    IA_Bloom            =   (1<<7),
    IA_Mirror           =   (1<<8),
};
using InstanceAttrs = WDFlags<InstanceAttr, uint>;

/**
*   @brief  下面的常量与shader中的顶点属性一一对应
*   所以shader中必须使用layout指定索引号
*/
enum ShaderInput
{
    SI_Position         =   0,
    SI_Normal           =   1,
    SI_Color            =   2,
    SI_Uv               =   3,
    /// matrix / FMat4
    SI_Local            =   4,
    /// instance id/uint
    SI_InstanceId       =   5,
    /// instance attribute / uint @InstanceAttr
    SI_InstanceAttr     =   6,
};


//using   DataHolder = std::function<WDObject* (const WDUuid& id)>;

WD_DECL_CLASS_UUID(WDMaterial, "70C27ADD-E223-4567-80D7-2C815D5BCA5D");
/**
*   @brief  材质基类,使用者需要实现begin/end方法
*/
class   WD_API  WDMaterial : public WDObject
{
    WD_DECL_OBJECT(WDMaterial);
public:
    using   TextureData     =   std::pair<std::string,WDTexture::SharedPtr>;
    using   Textures        =   std::map<int,TextureData>;
    using   EventChanged    =   std::function<void(WDMaterial*)>;
public:
    WDMaterial();
    WDMaterial(const WDMaterial& right) = delete;
    WDMaterial(WDMaterial&& right) = delete;
    WDMaterial& operator=(const WDMaterial& right) = delete;
    WDMaterial& operator=(WDMaterial&& right) = delete;
    virtual ~WDMaterial();
protected:
    WDRenderStateSet    _states;
    Uniforms            _uniforms;
    /// 材质数据发生变化通知
    EventChanged        _changedEvent;
    /// 对应的程序对象
    /// 对象是通过 _prgName 字段从program lib 中进行查询获取到的
    WDProgram::SharedPtr    _program;
    /// 程序名称
    std::string         _prgName;
protected:
    UfmPtr              _ufmTransparentSort;

public:
    /// <summary>
    /// 设置Uniform 数据，如果系统中已经有了，使用给定参数修改已有数据然后又返回对象指针
    /// 如果没有，那么建立一个，同时赋值,返回对象指针
    /// 发出数据变更通知,
    /// </summary>
    /// <param name="name"></param>
    /// <param name="value"></param>
    /// <returns> 返回当前 uniform 对象,主要目的是用户可以继续进行使用，而不用在进行查询操作</returns>
    UfmPtr  setUniform(const char* name,const int&   value);
    UfmPtr  setUniform(const char* name,const IVec2& value);
    UfmPtr  setUniform(const char* name,const IVec3& value);
    UfmPtr  setUniform(const char* name,const IVec4& value);

    UfmPtr  setUniform(const char* name,const uint&   value);
    UfmPtr  setUniform(const char* name,const UIVec2& value);
    UfmPtr  setUniform(const char* name,const UIVec3& value);
    UfmPtr  setUniform(const char* name,const UIVec4& value);


    UfmPtr  setUniform(const char* name,const float& value);
    UfmPtr  setUniform(const char* name,const FVec2& value);
    UfmPtr  setUniform(const char* name,const FVec3& value);
    UfmPtr  setUniform(const char* name,const FVec4& value);

    UfmPtr  setUniform(const char* name,const FMat2& value);
    UfmPtr  setUniform(const char* name,const FMat3& value);
    UfmPtr  setUniform(const char* name,const FMat4& value);

    UfmPtr  setUniform(const char* name, WDTexture1d::SharedPtr value);
    UfmPtr  setUniform(const char* name, WDTexture2d::SharedPtr value);
    UfmPtr  setUniform(const char* name, WDTexture3d::SharedPtr value);
    UfmPtr  setUniform(const char* name, WDTexture2dArray::SharedPtr value);
    UfmPtr  setUniform(const char* name, WDTextureCube::SharedPtr value);
    UfmPtr  uniform(const std::string& name);
    void    clearUniform();
    void    removeUniform(const std::string& name);

    WDProgram::SharedPtr  program()
    {
        return  _program;
    }
    void        setProgram(WDProgram::SharedPtr ptr)
    {   
        setProgramName(ptr == nullptr ? "" : ptr->name());
        _program    =   ptr;
    }

    void        setProgramName(const std::string& name)
    {
        _prgName    =   name;
    }
    const std::string& programName() const
    {
        return  _prgName;
    }
    
    /**
    * @brief 获取状态集
    */
    const WDRenderStateSet& states() const
    {
        return  _states;
    }
    /**
    * @brief 获取状态集
    */
    WDRenderStateSet& states()
    {
        return _states;
    }
    /**
    * @brief 添加状态
    */
    inline void addState(WDRenderStateBase::SharedPtr pState)
    {
        _states.addState(pState);
    }
    /**
    * @brief 添加状态
    */
    inline void addStates(const std::vector<WDRenderStateBase::SharedPtr>& states)
    {
        _states.addStates(states);
    }
    /**
    * @brief 添加状态
    */
    inline void addStates(std::vector<WDRenderStateBase::SharedPtr>&& states)
    {
        _states.addStates(std::forward<std::vector<WDRenderStateBase::SharedPtr> >(states));
    }

    void        appUniform(WDContext& ctx);
    /**
    * @brief 改变通知
    */
    EventChanged    changedEvent() const
    {
        return  _changedEvent;
    }
    EventChanged&   changedEvent()
    {
        return  _changedEvent;
    }
    /**
    *   @brief  开始绘制函数,切换状态
    *   子类中冲洗该类，并调用需要执行父类函数
    */
    virtual void    begin(WDContext& ctx);
    /**
    *   @brief  绘制结束函数,恢复状态
    */
    virtual void    end(WDContext& ctx);
    /**
     * @brief 批量绘制
     * @param ctx 上下文
     * @param pInstPtr 实例对象数据首地址
     * @param instCount 实例对象数据个数
     * @param mesh 网格数据
     * @param pPriPtr 图元描述数据首地址
     * @param prisCount 图元描述数据个数
    */
    virtual void    render(WDContext& ctx
        , const void* pInstPtr
        , uint instCount
        , const WDMesh& mesh
        , const WDPrimitiveSet* pPriPtr
        , size_t prisCount);
    /**
     * @brief 批量绘制，指定自定义的Instance类型
     * @tparam TInstance 自定义的Instance类型 
     * @param ctx 上下文
     * @param insts 实例对象列表
     * @param mesh 网格数据
     * @param pPriPtr 图元描述数据首地址
     * @param prisCount 图元描述数据个数
    */
    template <typename TInstance>
    void render(WDContext& ctx
        , const std::vector<TInstance>& insts
        , const WDMesh& mesh
        , const WDPrimitiveSet* pPriPtr
        , size_t prisCount)
    {
        this->render(ctx
            , insts.data()
            , static_cast<uint>(insts.size())
            , mesh
            , pPriPtr
            , prisCount);
    }
    /**
     * @brief 批量绘制
     * @param ctx 上下文
     * @param pInstPtr 实例对象数据首地址
     * @param instCount 实例对象数据个数
     * @param pVertPtr 顶点数据首地址
     * @param vertDesc 顶点数据描述
     * @param pPriPtr 图元描述数据首地址
     * @param prisCount 图元描述数据个数
    */
    virtual void render(WDContext& ctx
        , const void* pInstPtr
        , uint instCount
        , const void* pVertPtr
        , const WDVertDescSet& vertDesc
        , const WDPrimitiveSet* pPriPtr
        , size_t prisCount);
    /**
     * @brief 批量绘制
     * @tparam TInstance 自定义的Instance类型 
     * @tparam TVertex 自定义的顶点类型 
     * @param ctx 上下文
     * @param insts Instance数组
     * @param verts 顶点数组
     * @param vertDesc 顶点数据描述
     * @param pPriPtr 图元描述数据首地址
     * @param prisCount 图元描述数据个数
    */
    template <typename TInstance, typename TVertex>
    void render(WDContext& ctx
        , const std::vector<TInstance>& insts
        , const std::vector<TVertex>& verts
        , const WDVertDescSet& vertDesc
        , const WDPrimitiveSet* pPriPtr
        , size_t prisCount)
    {
        this->render(ctx
            , insts.data()
            , static_cast<uint>(insts.size())
            , verts.data()
            , vertDesc
            , pPriPtr
            , prisCount);
    }

    virtual inline bool transparentSort()
    {
        int val;
        _ufmTransparentSort->get(val);
        return val;
    }
    inline void     setTransparentSort(bool t)
    {
        _ufmTransparentSort->set(t);
    }
    virtual inline float  transparency() const
    {
        return 1.0f;
    }
public:
    /**
    * @brief 从源对象拷贝数据到当前对象
    *   子类可重写
    */
    virtual void    copy(const WDObject* pSrcObject) override;
    /**
    * @brief 使用当前对象克隆出一个对象
    *   子类可重写
    */
    virtual WDObject::SharedPtr clone() const override;
public:
    Textures textures() const;
};

WD_DECL_CLASS_UUID(WDMaterialColor, "75C95B22-09F8-42F6-88BA-C3C757770D26");
/**
 * @brief 纯色绘制
*/
class   WD_API  WDMaterialColor : public WDMaterial
{
    WD_DECL_OBJECT(WDMaterialColor);
protected:
    UfmPtr  _ufmColor;
public:
    WDMaterialColor(Color color = Color(255,165,0,255));
    virtual ~WDMaterialColor();
public:
    inline Color color() const
    {
        FVec4 val;
        _ufmColor->get(val);
        return Color(val);
    }
    inline void setColor(const Color& c)
    {
        _ufmColor->set(c.rgbaF());
    }

    virtual inline float transparency() const override
    {
        return color().a;
    }

    inline  void    setTransparency(float transparency)
    {
        Color c =   color();
        c.a = byte(transparency * 255);
        setColor(c);
        setTransparentSort(transparency < 1.0f);
    }
};
WD_DECL_CLASS_UUID(WDMaterialPhong, "72EB73E8-6601-458E-B539-70DB96CD125D");
/**
*   @brief  Blinn-Phong光照模型
*/
class WD_API WDMaterialPhong :public WDMaterial
{
    WD_DECL_OBJECT(WDMaterialPhong);
protected:
    UfmPtr      _ufmLightDir;
    UfmPtr      _ufmLight;
    UfmPtr      _ufmAmbient;
    UfmPtr      _ufmDiffuse;
    UfmPtr      _ufmSpecular;
    
    UfmPtr      _ufmShininess;
    UfmPtr      _ufmTransparency;

private:
    static WDMaterialPhong::SharedPtr _pDefaultMaterial;
public:
    WDMaterialPhong();
    virtual ~WDMaterialPhong();
public:
    /**
    * @brief 默认的一个Phong材质对象
    */
    static WDMaterialPhong::SharedPtr Default();
public:
    inline Color   lightColor() const
    {
        FVec3   val;
        _ufmLight->get(val);
        return  Color(val);
    }
    inline void    setLightColor(const Color& val)
    {
        _ufmLight->set(val.rgbF());
    }

    inline FVec3   lightDir() const
    {
        FVec3   val;
        _ufmLightDir->get(val);
        return  val;
    }
    inline void    setLightDir(const FVec3& val)
    {
        _ufmLightDir->set(val);
    }

    inline Color   ambient() const
    {
        FVec3   val;
        _ufmAmbient->get(val);
        return  Color(val);
    }
    inline void    setAmbient(const Color& val)
    {
        _ufmAmbient->set(val.rgbF());
    }

    
    inline Color   diffuse() const
    {
        FVec3   val;
        _ufmDiffuse->get(val);
        return  Color(val);
    }
    inline void    setDiffuse(const Color& val)
    {
        _ufmDiffuse->set(val.rgbF());
    }

    inline Color   specular() const
    {
        FVec3   val;
        _ufmSpecular->get(val);
        return  Color(val);
    }
    inline void    setSpecular(const Color& val)
    {
        _ufmSpecular->set(val.rgbF());
    }

    inline float   shininess() const
    {
        float   val(0);
        _ufmShininess->get(val);
        return  val;
    }
    inline void    setShininess(const float val)
    {
        _ufmShininess->set(val);
    }

    virtual inline  float   transparency() const override
    {
        float  val(0);
        _ufmTransparency->get(val);
        return  val;
    }
    inline void    setTransparency(float transparency)
    {
        _ufmTransparency->set(transparency);
        setTransparentSort(transparency < 1.0f);
    }


public:
    /**
    * @brief 从源对象拷贝数据到当前对象
    *   子类可重写
    */
    virtual void    copy(const WDObject* pSrcObject) override;
    /**
    * @brief 使用当前对象克隆出一个对象
    *   子类可重写
    */
    virtual WDObject::SharedPtr clone() const override;
};

WD_DECL_CLASS_UUID(WDMaterialText2d,"F3F06A3D-044B-4BE1-9161-FD35616A2A44");
class   WD_API  WDMaterialText2d : public WDMaterialColor
{
    WD_DECL_OBJECT(WDMaterialText2d);
public:
    WDMaterialText2d(Color color = Color(255,255,255));

    virtual ~WDMaterialText2d();
public:
    /**
    * @brief 使用当前对象克隆出一个对象
    *   子类可重写
    */
    virtual WDObject::SharedPtr clone() const override;

};

WD_DECL_CLASS_UUID(WDMaterialP3UV2,"932BA8CB-112F-4FEF-839D-B17C5E9B74E7");
class   WD_API  WDMaterialP3UV2 : public WDMaterial
{
    WD_DECL_OBJECT(WDMaterialP3UV2);
public:
    WDMaterialP3UV2();

    virtual ~WDMaterialP3UV2();
protected:
    UfmPtr  _ufm2d;
    UfmPtr  _ufmTex;
public:

    void    set2d(bool b2d);
public:
    /**
    * @brief 使用当前对象克隆出一个对象
    *   子类可重写
    */
    virtual WDObject::SharedPtr clone() const override;

};

WD_DECL_CLASS_UUID(WDMaterialTexture,"6EC5F85F-B428-4179-9777-515D1674F386");
class   WD_API  WDMaterialTexture :public WDMaterial
{
    WD_DECL_OBJECT(WDMaterialTexture);
protected:

    UfmPtr      _ufmLightDir;
    UfmPtr      _ufmLight;
    UfmPtr      _ufmAmbient;
    UfmPtr      _ufmSpecular;
    UfmPtr      _ufmDiffuse;
    UfmPtr      _ufmShininess;
    UfmPtr      _ufmAlphaTest;
    UfmPtr      _ufmTransparency;

    //给出使用diffuse/texture的比例，调节颜色组成
    UfmPtr      _ufmTextureRenderRate;

private:
    static WDMaterialTexture::SharedPtr _pDefaultMaterial;
public:
    WDMaterialTexture();
    virtual ~WDMaterialTexture();
public:
    /**
    * @brief 默认的一个Phong材质对象
    */
    static WDMaterialTexture::SharedPtr Default();
public:
    inline Color    lightColor() const
    {
        FVec3   val;
        _ufmLight->get(val);
        return  Color(val);
    }
    inline void     setLightColor(const Color& val)
    {
        _ufmLight->set(val.rgbF());
    }

    inline FVec3    lightDir() const
    {
        FVec3   val;
        _ufmLightDir->get(val);
        return  val;
    }
    inline void     setLightDir(const FVec3& val)
    {
        _ufmLightDir->set(val);
    }

    inline Color    ambient() const
    {
        FVec3   val;
        _ufmAmbient->get(val);
        return  Color(val);
    }
    
    inline void     setAmbient(const Color& val)
    {
        _ufmAmbient->set(val.rgbF());
    }
    inline Color    specular() const
    {
        FVec3   val;
        _ufmSpecular->get(val);
        return  Color(val);
    }
    inline void     setSpecular(const Color& val)
    {
        _ufmSpecular->set(val.rgbF());
    }
    inline float    shininess() const
    {
        float   val(0);
        _ufmShininess->get(val);
        return  val;
    }
    inline void     setShininess(const float val)
    {
        _ufmShininess->set(val);
    }
    inline float    alphaTest() const
    {
        float   val(0);
        _ufmAlphaTest->get(val);
        return  val;
    }
    inline void     setAlphTest(float fTest)
    {
        _ufmAlphaTest->set(fTest);
    }
    inline Color    diffuse() const
    {
        FVec3   val;
        _ufmDiffuse->get(val);
        
        return  Color(val);
    }
    inline void     setDiffuse(const Color& val)
    {
        _ufmDiffuse->set(val.rgbF());
    }
    virtual inline float transparency() const override
    {
        float  val(0);
        _ufmTransparency->get(val);
        return  val;
    }
    inline void     setTransparency(float transparency)
    {
        _ufmTransparency->set(transparency);
    }

    inline bool     transparentSort()  override
    {
        return WDMaterial::transparentSort();
    }

    inline float textureRenderRate()
    {
        float val(0);
        _ufmTextureRenderRate->get(val);
        return val;
    }

    inline void setTextureRenderRate(float v)
    {
        _ufmTextureRenderRate->set(v);
    }

public:
    /**
    * @brief 使用当前对象克隆出一个对象
    *   子类可重写
    */
    virtual WDObject::SharedPtr clone() const override;
};


WD_NAMESPACE_END