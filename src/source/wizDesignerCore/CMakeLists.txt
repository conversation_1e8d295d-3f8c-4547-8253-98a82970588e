set(TARGET_NAME wizDesignerCore)

configure_file(${CMAKE_CURRENT_SOURCE_DIR}/WIZDesignerVersion.h.in ${CMAKE_CURRENT_SOURCE_DIR}/WIZDesignerVersion.h @ONLY)

macro(SourceGroupByDirRList dir)
	SourceGroupByDir(${dir} TTMP_FILES)
	list(APPEND ALL_FILES_LIST ${TTMP_FILES})
endmacro(SourceGroupByDirRList)

macro(SourceGroupByDirRenameFolderRList dir folderName)
	SourceGroupByDirRenameFolder(${dir} ${folderName} TTMP_FILES)
	list(APPEND ALL_FILES_LIST ${TTMP_FILES})
endmacro(SourceGroupByDirRenameFolderRList)

if(UNIX)
add_definitions(-DLINUX) 
endif()

SourceGroupByDirRenameFolderRList("." "core")
SourceGroupByDirRList("axis")
SourceGroupByDirRList("boolean")
SourceGroupByDirRList("businessModule") 
SourceGroupByDirRList("businessModule/admin")
SourceGroupByDirRList("businessModule/design")
SourceGroupByDirRList("businessModule/design/private")
SourceGroupByDirRList("businessModule/design/auxiliaryLine")
SourceGroupByDirRList("businessModule/design/auxiliaryLine/private")
SourceGroupByDirRList("businessModule/design/equipment")
SourceGroupByDirRList("businessModule/design/equipment/private")
SourceGroupByDirRList("businessModule/design/pipeWork")
SourceGroupByDirRList("businessModule/design/pipeWork/private")
SourceGroupByDirRList("businessModule/design/structures")
SourceGroupByDirRList("businessModule/design/structures/private")
SourceGroupByDirRList("businessModule/design/support")
SourceGroupByDirRList("businessModule/design/support/private")
SourceGroupByDirRList("businessModule/catalog")
SourceGroupByDirRList("businessModule/catalog/private")
SourceGroupByDirRList("businessModule/catalog/modelBuilder")
SourceGroupByDirRList("businessModule/catalog/modelBuilder/private")
SourceGroupByDirRList("businessModule/dataType")
SourceGroupByDirRList("businessModule/typeMgr")
SourceGroupByDirRList("businessModule/typeMgr/private")
SourceGroupByDirRList("businessModule/serialize")
SourceGroupByDirRList("businessModule/serialize/private")
SourceGroupByDirRList("cameras")
SourceGroupByDirRList("common")
SourceGroupByDirRList("common/private")
SourceGroupByDirRList("events")
SourceGroupByDirRList("extension")
SourceGroupByDirRList("font")
SourceGroupByDirRList("geometry")
SourceGroupByDirRList("geometry/standardPrimitives")
SourceGroupByDirRList("GL")
SourceGroupByDirRList("graphable")
SourceGroupByDirRList("input")
SourceGroupByDirRList("log")
SourceGroupByDirRList("material")
SourceGroupByDirRList("material/renderState")
SourceGroupByDirRList("math")
SourceGroupByDirRList("math/geometric")
SourceGroupByDirRList("math/utils")
SourceGroupByDirRList("math/geometric/standardPrimitives")
SourceGroupByDirRList("math/geometric/standardPrimitives/triangular")
SourceGroupByDirRList("math/geometric/curve")
SourceGroupByDirRList("math/geometric/shape")
SourceGroupByDirRList("message")
SourceGroupByDirRList("node")
SourceGroupByDirRList("nodeTree")
SourceGroupByDirRList("scene")
SourceGroupByDirRList("scene/private")
SourceGroupByDirRList("selections")
SourceGroupByDirRList("texture")
SourceGroupByDirRList("ui")
SourceGroupByDirRList("undoRedo")
SourceGroupByDirRList("viewer")
SourceGroupByDirRList("viewer/capturePositioning")
SourceGroupByDirRList("viewer/objectAxisEditor")
SourceGroupByDirRList("viewer/dimension")
SourceGroupByDirRList("viewer/primitiveRender")
SourceGroupByDirRList("apiDelegate")
SourceGroupByDirRList("globalUiIds")

if(APPLE)
	find_package(OpenGL REQUIRED)
	find_package(glfw3 CONFIG REQUIRED)
	find_package(X11 REQUIRED) # glu.h
endif ()

add_library(${TARGET_NAME} SHARED
		${ALL_FILES_LIST}
		#[[${HEADER_FILES}
		${SOURCE_FILES}]]
)
if(APPLE)
	add_subdirectory("../wizBoolean" wizBoolean)
	add_subdirectory("../utilLib" utilLib)
	target_include_directories(${TARGET_NAME} PRIVATE
			${EXPRTK_INCLUDE_DIRS}
			${OPENGL_INCLUDE_DIRS}
	)
	message(STATUS "OPENGL_INCLUDE_DIRS ${OPENGL_INCLUDE_DIRS}")
	target_link_libraries(${TARGET_NAME} PRIVATE OpenGL::GL glfw)
endif ()
target_include_directories(${TARGET_NAME} PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})

if(MSVC)
target_compile_options(${TARGET_NAME} PRIVATE /bigobj)
endif()
target_compile_definitions(${TARGET_NAME} PRIVATE
	-DWIZDESIGNERCORE_EXPORTS
	-DAPI_EXPORTS
)

target_compile_definitions(${TARGET_NAME} PUBLIC
	-DSTB_IMAGE_STATIC
	-DSTB_IMAGE_IMPLEMENTATION
	-DSTB_IMAGE_WRITE_IMPLEMENTATION
	-DSTBRP_STATIC
	-DSTB_RECT_PACK_IMPLEMENTATION
)

find_package(ZLIB REQUIRED) # ZLIB::ZLIB
target_link_libraries(${TARGET_NAME} PUBLIC ZLIB::ZLIB)

find_package(GLEW REQUIRED) # GLEW::GLEW
target_link_libraries(${TARGET_NAME} PUBLIC GLEW::GLEW)

find_package(Freetype REQUIRED) # Freetype::Freetype
target_link_libraries(${TARGET_NAME} PUBLIC Freetype::Freetype)

if(NOT APPLE)
	# macos 下不支持 uuid
	target_link_libraries(${TARGET_NAME} PRIVATE uuid)
endif()

target_link_libraries(${TARGET_NAME} PRIVATE wizBoolean)

target_link_libraries(${TARGET_NAME} PRIVATE WIZDesignerDSL)

target_link_libraries(${TARGET_NAME} PRIVATE util.rapidxml)

target_link_libraries(${TARGET_NAME} PRIVATE util.rapidjson)

target_link_libraries(${TARGET_NAME} PRIVATE util.sdk.license)

#target_link_libraries(${TARGET_NAME} PRIVATE Python3 Python39 pybind11 uuid)

target_include_directories(${TARGET_NAME} INTERFACE ${CMAKE_CURRENT_SOURCE_DIR}/../../include)
target_include_directories(
	${TARGET_NAME}
	INTERFACE ${CMAKE_CURRENT_SOURCE_DIR}/../../include/core)

if(WIN32)
add_custom_command(
	TARGET ${TARGET_NAME}
	POST_BUILD
	COMMAND				wizDesignerCorePostBuild.bat
	WORKING_DIRECTORY	${CMAKE_CURRENT_SOURCE_DIR}
)
elseif(UNIX)
add_custom_command(
	TARGET ${TARGET_NAME}
	POST_BUILD
	COMMAND				./wizDesignerCorePostBuild.sh
	WORKING_DIRECTORY	${CMAKE_CURRENT_SOURCE_DIR}
)
endif()

CopyImportedRuntimeDependency(${TARGET_NAME})