#include "WDNodeTree.h"

WD_NAMESPACE_BEGIN

WDNodeTree::WDNodeTree()
{
    _startFlag = false;
}

WDNodeTree::~WDNodeTree()
{
    while (!_topLevelNodes.empty())
    {
        if (_topLevelNodes.back() != nullptr)
        {
            //移除顶层节点之前通知
            _noticeRemoveTopLevelNode(_topLevelNodes.back(), *this);

            _topLevelNodes.back() = nullptr;
        }
        _topLevelNodes.pop_back();
    }
}

void WDNodeTree::addTopLevelNode(WDNode::SharedPtr pNode)
{
    if (pNode == nullptr)
        return;
    _topLevelNodes.push_back(pNode->toPtr<WDNode>());
    //添加顶层节点之后通知
    _noticeAddTopLevelNode(pNode, *this);
}
void WDNodeTree::addTopLevelNodes(const Nodes& nodes)
{
    for (size_t i = 0; i < nodes.size(); ++i)
    {
        this->addTopLevelNode(nodes.at(i));
    }
}

void WDNodeTree::removeTopLevelNode(const WDNode::SharedPtr pNode)
{
    if (pNode == nullptr)
        return;

    for (size_t i = 0; i < _topLevelNodes.size(); ++i)
    {
        auto pTopNode = _topLevelNodes.at(i);
        if (pNode == pTopNode)
        {
            this->removeTopLevelNode(i);
            return;
        }
    }
}
void WDNodeTree::removeTopLevelNode(size_t index)
{
    if (index >= _topLevelNodes.size())
        return ;
    Nodes::iterator pItr   =   _topLevelNodes.begin() + index;

    auto pTopNode = (*pItr);

    assert(pTopNode != nullptr);

    auto pCurrNode = this->currentNode();
    if (pCurrNode != nullptr &&  (pTopNode->isAncestor(*pCurrNode) || pTopNode == pCurrNode))
    {
        this->setCurrentNode(nullptr);
    }

    _noticeRemoveTopLevelNode(pTopNode, *this);

    _topLevelNodes.erase(pItr);
}
void WDNodeTree::removeTopLevelNodes()
{
    if (this->currentNode() != nullptr)
    {
        this->setCurrentNode(nullptr);
    }

    while(!_topLevelNodes.empty())
    {
        _noticeRemoveTopLevelNode(_topLevelNodes.back(), *this);
        _topLevelNodes.pop_back();
    }
}

void WDNodeTree::setCurrentNode(WDNode::SharedPtr pNode, bool highlight)
{
    auto pTNode = pNode;
    while (pTNode != nullptr && pTNode->flags().hasFlag(WDNode::F_ItemHidden))
    {
        pTNode = pTNode->parent();
    }

    auto pCurNode = _currentNode.lock();
    if (pCurNode == pTNode)
        return;

    if (pCurNode != nullptr)
    {
        if (highlight)
        {
            WDNode::RecursionHelpter(*pCurNode, [](WDNode& node)
                {
                    auto flag = node.flags().removeFlag(WDNode::Flag::F_TreeSelected);
                    node.setFlags(flag);
                });
        }
    }
    auto prevNode    =   pCurNode;
    _currentNode        =   pTNode ? pTNode->toPtr<WDNode>() : nullptr;
    pCurNode            =   _currentNode.lock();
    if (pCurNode != nullptr)
    {
        if (highlight)
        {
            WDNode::RecursionHelpter(*pCurNode, [](WDNode& node)
                {
                    auto flag = node.flags().addFlag(WDNode::Flag::F_TreeSelected);
                    node.setFlags(flag);
                });
        }
    }
    //通知当前节点改变
    _noticeCurrNodeChanged(pCurNode, prevNode, *this);
}

WDNode::SharedPtr WDNodeTree::findNode(const WDUuid& uuid) const
{
    for (size_t i = 0; i < _topLevelNodes.size(); ++i)
    {
        auto pTopLevelNode = _topLevelNodes.at(i);
        WDNode::SharedPtr pFind = this->findNodeTraversal(pTopLevelNode, uuid);
        if (pFind != nullptr)
            return pFind;
    }
    return nullptr;
}

WDNodeTree::Nodes WDNodeTree::findNodes(const std::string& name, WDNode::SharedPtr parent) const
{
    Nodes rNodes;

    if (parent == nullptr)
    {
        for (size_t i = 0; i < _topLevelNodes.size(); ++i)
        {
            auto pTopLevelNode = _topLevelNodes.at(i);
            this->findNodesTraversal(pTopLevelNode, name, rNodes);
        }
    }
    else
    {
        this->findNodesTraversal(parent, name, rNodes);
    }
    return rNodes;
}

WDNodeTree::Nodes WDNodeTree::fuzzyFindNodes(const std::string& name, WDNode::SharedPtr parent) const
{
    Nodes rNodes;

    if (parent == nullptr)
    {
        for (size_t i = 0; i < _topLevelNodes.size(); ++i)
        {
            auto pTopLevelNode = _topLevelNodes.at(i);
            this->fuzzyFindNodesTraversal(pTopLevelNode, name, rNodes);
        }
    }
    else
    {
        this->fuzzyFindNodesTraversal(parent, name, rNodes);
    }
    return rNodes;
}

WDNode::SharedPtr WDNodeTree::findNode(const std::string& name, WDNode::SharedPtr parent) const
{
    if (parent == nullptr)
    {
        for (size_t i = 0; i < _topLevelNodes.size(); ++i)
        {
            auto pTopLevelNode = _topLevelNodes.at(i);
            WDNode::SharedPtr rNode = this->findNodeTraversal(pTopLevelNode, name);
            if (rNode != nullptr)
                return rNode;
        }
    }
    else
    {
        return this->findNodeTraversal(parent, name);
    }
    return nullptr;
}

WDNode::SharedPtr WDNodeTree::fuzzyFindNode(const std::string& name, WDNode::SharedPtr parent) const
{
    if (parent == nullptr)
    {
        for (size_t i = 0; i < _topLevelNodes.size(); ++i)
        {
            auto pTopLevelNode = _topLevelNodes.at(i);
            WDNode::SharedPtr rNode = this->fuzzyFindNodesTraversal(pTopLevelNode, name);
            if (rNode != nullptr)
                return rNode;
        }
        return nullptr;
    }
    else
    {
        return this->fuzzyFindNodesTraversal(parent, name);
    }
}

WDNode::SharedPtr WDNodeTree::findPrevNode(WDNode::SharedPtr pNode, const std::string& name, WDNodeTree::QueryMode queryMode)
{
    if (pNode == nullptr)
    {
        auto rPNode = findNode(name);

        if (rPNode == nullptr)
            rPNode = fuzzyFindNode(name);
        return rPNode;
    }

    ///存放开始节点的地址
    _startSearchNode = pNode ? pNode->toPtr<WDNode>() : nullptr;
    _startFlag = false;
    ///定义数组长度
    char szBuf[1024];
    strcpy(szBuf, name.c_str());
    /// 需要查找的字符串地址
    _pSearchPtr = (char *)szBuf;
    ///需要查找的字符串的长度
    _nameLens = (int)strlen(szBuf);
    ///查找结果结果
    _searchResult.reset();

    if (_arFinds.empty())
    {
        /// 转到最后一个
        // TREEITEM    sf = NULL;
        for (size_t i = 0; i < _topLevelNodes[0]->childCount(); ++i)
        {
            queryNode(_topLevelNodes[0]->childAt(i), szBuf, queryMode, _arFinds);
        }
        // while (NULL != (sf = queryItem(sf, name, true, queryMode)));
        // sf = queryItem(sf, name, true, queryMode);
        // 返回最后一个
        if (_arFinds.empty())
        {
            return 0;
        }
        return _arFinds.back().lock();
    }
    else
    {
        if (_startSearchNode.lock() == _arFinds.back().lock())
        {
            _arFinds.pop_back();
        }
        if (_arFinds.empty()) // 弹出当前的可能为空了
        {
            return 0;
        }
        WDNode::SharedPtr  res = _arFinds.back().lock();
        _arFinds.pop_back();
        return res;
    }
}

WDNode::SharedPtr WDNodeTree::findNextNode(WDNode::SharedPtr pNode, const std::string& name, WDNodeTree::QueryMode queryMode)
{
    if (pNode == nullptr)
    {
        auto rPNode = findNode(name);

        if (rPNode == nullptr)
            rPNode = fuzzyFindNode(name);
        return rPNode;
    }

    ///存放开始节点的地址
    _startSearchNode = pNode ? pNode->toPtr<WDNode>() : nullptr;
    _startFlag = false;
    ///定义数组长度
    char szBuf[1024];
    strcpy(szBuf, name.c_str());
    /// 需要查找的字符串地址
    _pSearchPtr = (char *)szBuf;
    ///需要查找的字符串的长度
    _nameLens = (int)strlen(szBuf);
    ///查找结果结果
    _searchResult.reset();

    for (size_t i = 0; i < _topLevelNodes[0]->childCount(); ++i)
    {
        if (_searchResult.lock() == nullptr)
        {
            searchNode(_topLevelNodes[0]->childAt(i), queryMode);
        }
        else
        {
            break;
        }
    }
    /// 这里说明向下搜索，则记录下来已经搜索过的
    if (_searchResult.lock() != nullptr)
    {
        _arFinds.push_back(_searchResult.lock());
    }
    _pSearchPtr = 0;
    return _searchResult.lock();
}

WDNode::SharedPtr WDNodeTree::findNodeTraversal(WDNode::SharedPtr pNode, const WDUuid& uuid) const
{
    if (pNode == nullptr)
        return nullptr;
    if (pNode->uuid() == uuid)
        return pNode ? pNode->toPtr<WDNode>() : nullptr;
    for (size_t i = 0; i < pNode->childCount(); ++i)
    {
        WDNode::SharedPtr pFind = this->findNodeTraversal(pNode->childAt(i), uuid);
        if (pFind != nullptr)
            return pFind;
    }
    return nullptr;
}

WDNode::SharedPtr WDNodeTree::findNodeTraversal(WDNode::SharedPtr pNode, const std::string & name) const
{
    if (pNode == nullptr)
        return nullptr;

    if (pNode->name() == name)
        return pNode->toPtr<WDNode>();

    ///else if(strstr( pNode->name().c_str(),name.c_str() ) != NULL)
    ///    return pNode;

    for (size_t i = 0; i < pNode->childCount(); ++i)
    {
        WDNode::SharedPtr pFind = this->findNodeTraversal(pNode->childAt(i), name);
        if (pFind != nullptr)
            return pFind;
    }
    return nullptr;
}

WDNode::SharedPtr WDNodeTree::fuzzyFindNodesTraversal(WDNode::SharedPtr pNode, const std::string & name) const
{
    if (pNode == nullptr)
        return nullptr;

    if (strstr(pNode->name().c_str(), name.c_str()) != nullptr)
        return pNode->toPtr<WDNode>();

    for (size_t i = 0; i < pNode->childCount(); ++i)
    {
        WDNode::SharedPtr pFind = this->fuzzyFindNodesTraversal(pNode->childAt(i), name);
        if (pFind != nullptr)
            return pFind;
    }
    return nullptr;
}

void WDNodeTree::findNodesTraversal(WDNode::SharedPtr pNode, const std::string& name, Nodes& rNodes) const
{
    if (pNode == nullptr)
        return;
    if (pNode->name() == name)
        rNodes.push_back(pNode->toPtr<WDNode>());

    for (size_t i = 0; i < pNode->childCount(); ++i)
    {
        this->findNodesTraversal(pNode->childAt(i), name, rNodes);
    }
}

void WDNodeTree::fuzzyFindNodesTraversal(WDNode::SharedPtr pNode, const std::string & name, Nodes & rNodes) const
{
    if (pNode == nullptr)
        return;

    if (strstr(pNode->name().c_str(), name.c_str()) != nullptr)
    {
        rNodes.push_back(pNode->toPtr<WDNode>());
    }

    for (size_t i = 0; i < pNode->childCount(); ++i)
    {
        this->fuzzyFindNodesTraversal(pNode->childAt(i), name, rNodes);
    }
}

WDNode::SharedPtr  WDNodeTree::findChildNode(WDNode::SharedPtr pNode, const std::string & name, bool bDown)
{
    if (pNode->childCount() == 0)
        return nullptr;

    if (bDown)
    {
        for (size_t i = 0; i < pNode->childCount(); ++i)
        {
            auto pChild  = pNode->childAt(i);
            auto rNodes     = findNodes(name, pChild);
            if (rNodes.size() == 0)
            {
                rNodes = fuzzyFindNodes(name, pChild);
            }

            if (!rNodes.empty())
            {
                return   rNodes[0];
            }
            else
            {
                return this->findChildNode(pChild, name, true);
            }
        }
    }
    else
    {
        for (size_t i = 0; i < pNode->childCount(); ++i)
        {
            auto pChild  = pNode->childAt(i);
            auto rNodes     = findNodes(name, pChild);
            if (rNodes.size() == 0)
            {
                rNodes = fuzzyFindNodes(name, pChild);
            }

            if (!rNodes.empty())
            {
                return   rNodes[0];
            }
            else
            {
                return this->findChildNode(pChild, name, true);
            }
        }
    }
    _startFlag = true;
    return nullptr;
}

void WDNodeTree::queryNode(WDNode::SharedPtr pNode, char* name, WDNodeTree::QueryMode queryMode,
    WDNodeTree::WeakNodes &results)
{
    if (pNode == nullptr)
        return;
    if (pNode->name().c_str() == 0)
    {
        return;
    }
    switch (queryMode)
    {
    case WDNodeTree::QueryMode::SubStr:
        if (strcmpex(pNode->name().c_str(), name, _nameLens) != NULL) // 返回NULL是未查找到
        {
            results.push_back(pNode->toPtr<WDNode>());
        }
        break;
    case WDNodeTree::QueryMode::StartStr:
        if (_strnicmp(pNode->name().c_str(), name, _nameLens) == 0)
        {
            results.push_back(pNode->toPtr<WDNode>());
        }
        break;
    case WDNodeTree::QueryMode::EndStr:
        {
            size_t nlen = strlen(pNode->name().c_str());
            if (nlen >= _nameLens && _stricmp(pNode->name().c_str() + nlen - _nameLens, name) == 0)
            {
                results.push_back(pNode->toPtr<WDNode>());
            }
        }
        break;
    case WDNodeTree::QueryMode::EqualStr:
        {
            if (_stricmp(pNode->name().c_str(), name) == 0)
            {
                results.push_back(pNode->toPtr<WDNode>());
            }
        }
        break;
    }
    for (unsigned i = 0; i < pNode->childCount(); ++i)
    {
        queryNode(pNode->childAt(i), name, queryMode, results);
    }
}

void WDNodeTree::searchNode(WDNode::SharedPtr pNode, WDNodeTree::QueryMode queryMode)
{
    if (pNode == nullptr)
        return;
    if (_startFlag && _nameLens <= strlen(pNode->name().c_str()))
    {
        if (pNode->name().c_str() == 0)
        {
            return;
        }
        switch (queryMode)
        {
        case WDNodeTree::QueryMode::SubStr:
            if (strcmpex(pNode->name().c_str(), _pSearchPtr, _nameLens) != NULL) // 返回NULL是未查找到
            {
                _searchResult = pNode->toPtr<WDNode>();
                return;
            }
            break;
        case WDNodeTree::QueryMode::StartStr:
            if (_strnicmp(pNode->name().c_str(), _pSearchPtr, _nameLens) == 0)
            {
                _searchResult = pNode->toPtr<WDNode>();
                return;
            }
            break;
        case WDNodeTree::QueryMode::EndStr:
            {
                size_t nlen = strlen(pNode->name().c_str());
                if (nlen >= _nameLens && _stricmp(pNode->name().c_str() + nlen - _nameLens, _pSearchPtr) == 0)
                {
                    _searchResult = pNode->toPtr<WDNode>();
                    return;
                }
            }
            break;
        case WDNodeTree::QueryMode::EqualStr:
            {
                if (_stricmp(pNode->name().c_str(), _pSearchPtr) == 0)
                {
                    _searchResult = pNode->toPtr<WDNode>();
                    return;
                }
            }
            break;
        default:
            break;
        }
    }
    if (pNode == _startSearchNode.lock())
    {
        _startFlag = true;
    }
    for (unsigned i = 0; i < pNode->childCount(); ++i)
    {
        if (_searchResult.lock() == 0)
        {
            searchNode(pNode->childAt(i), queryMode);
        }
        else
        {
            break;
        }
    }
}
void WDNodeTree::struper(char* string, char* dst)
{
    char* cp;
    for (cp = string; *cp; ++cp, ++dst)
    {
        *dst = *cp;
        if (('a' <= *cp) && (*cp <= 'z'))
            *dst -= 'a' - 'A';
    }
    *dst = '\0';
}
char* WDNodeTree::strcmpex(const char* src, char* dst, size_t len)
{
    (void*)&len;
    char szTemp[1024];
    char* pc = new char[4096];
    strcpy(pc, src);
    struper(pc, szTemp);
    return strstr(szTemp, dst);
}
WD_NAMESPACE_END