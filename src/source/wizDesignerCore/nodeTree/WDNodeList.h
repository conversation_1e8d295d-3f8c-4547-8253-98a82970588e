#pragma     once
#include    "../node/WDNode.h"
#include    "../common/WDOperationList.h"

WD_NAMESPACE_BEGIN

class WDNode;
/**
 * @brief 节点列表
*/
class WD_API NodeList: public WDNodeObserver
{
public:
    /*
    * NodeList的观察者，用于观察NodeList的状态
    * 如NodeList添加节点、移除节点、清空节点
    */
    class Observer
    {
    public:
        /**
        * @brief NodeList添加节点之前通知
        * @param pNode 需要添加的节点
        * @param sendeer 通知的发送者
        */
        virtual void onAddBefore(WD::WDNode::SharedPtr pNode, WD::NodeList& sender)
        {
            WDUnused(sender);
            WDUnused(pNode);
        }
        /**
        * @brief NodeList添加节点之后通知
        * @param pNode 已添加的节点
        * @param sendeer 通知的发送者
        */
        virtual void onAddAfter(WD::WDNode::SharedPtr pNode, WD::NodeList& sender)
        {
            WDUnused(pNode);
            WDUnused(sender);
        }
        /**
        * @brief NodeList移除节点之前通知
        * @param pNode pNode 需要移除的节点
        * @param sendeer 通知的发送者
        */
        virtual void onRemoveBefore(WD::WDNode::SharedPtr pNode, WD::NodeList& sender)
        {
            WDUnused(pNode);
            WDUnused(sender);
        }
        /**
        * @brief 节点删除通知
        * @param pNode 当前即将被删除的节点
        */
        virtual void onRemoveAfter(WD::WDNode::SharedPtr pNode, WD::NodeList& sender)
        {
            WDUnused(pNode);
            WDUnused(sender);
        }
        /**
         * @brief NodeList清空前通知
         * @param sendeer 清空发送者
        */
        virtual void onClearBefore(WD::NodeList& sender)
        {
            WDUnused(sender);
        }        /**
         * @brief NodeList清空后通知
         * @param sendeer 清空发送者
        */
        virtual void onClearAfter(WD::NodeList& sender)
        {
            WDUnused(sender);
        }
    };
    /**
    * @brief 节点表观察者列表
    */
    using Observers = WD::WDOperationList<Observer*>;
public:
    /**
    * @brief NodeList构造函数
    * @param name 节点列表名称
    */
    NodeList(const std::string& name = "");
    ~NodeList();
public:
    /**
    * @brief 获取NodeList的名称
    * @return  NodeList的名称
    */
    const std::string& name() const
    {
        return _name;
    }
    /**
    * @brief 添加节点
    * @param  pNode 需添加的节点
    */
    bool add(WD::WDNode::SharedPtr pNode);
    /**
    * @brief 添加节点
    * @brief 添加节点
    * @param nodes nodes 需添加的节点集
    * @return 返回添加的个数
    */
    int add(const std::vector<WD::WDNode::SharedPtr>& nodes);
    /**
    * @brief 移除节点
    * @param  pNode 需移除的节点
    */
    bool remove(WD::WDNode::SharedPtr pNode);
    /**
    * @brief 移除节点
    * @param  nodes 需移除的节点集
    * @return 返回移除的个数
    */
    int remove(const std::vector<WD::WDNode::SharedPtr>& nodes);
    /**
    * @brief 移除节点列表的所有节点
    */
    void clear();
    /**
    * @brief 判断节点是否存在
    * @param  pNode 节点
    * @return true:节点列表存在pNode； false:节点列表不存在pNode
    */
    bool exist(WD::WDNode::SharedPtr pNode) const;
    /**
    * @brief 获取节点列表的节点集
    * @return 节点列表的所有节点
    */
    const std::vector<WD::WDNode::SharedPtr>& nodes() const;
    /**
    * @brief 重写WDNOde观察者的删除节点通知
    * @param pNode 将删除的节点
    */
    virtual void onNodeDestroyBefore(WDNode::SharedPtr pNode) override;
    /**
    * @brief 获取节点表观察者列表
    */
    inline Observers& observers()
    { 
        return _observers; 
    }
private:
    // 节点列表的名称
    std::string _name;
    // 按添加顺序保存节点列表对象, 这里可以使用强引用，因为监听到节点被销毁之后，这里会自动执行移除
    std::vector<WD::WDNode::SharedPtr> _nodes;
    // 节点集 用于查找, 这里可以使用强引用，因为监听到节点被销毁之后，这里会自动执行移除
    std::set<WD::WDNode::SharedPtr> _nodeSet;
    // NodeList观察者列表
    Observers _observers;
};

/**
 * @brief 节点列表管理
*/
class WD_API NodeListMgr
{
public:
    /*
    * NodeListMgr的观察者，通知NodeListMgr的观察者NodeListMgr的动作
    * 如NodeListMgr添加添加NodeList对象，移除NodeList对象当前NodeList对象改变
    */
    class Observer
    {
    public:
        /**
         * @brief 创建NodeList之前通知
         * @param pNodeList 将被添加的NodeList对象
        */
        virtual void onCreatBefore(WD::NodeList* pNodeList)
        {
            WDUnused(pNodeList);
        }
        /**
         * @brief 创建NodeList之后通知
         * @param pNodeList 已被添加的NodeList对象
        */
        virtual void onCreatAfter(WD::NodeList* pNodeList)
        {
            WDUnused(pNodeList);
        }
        /**
         * @brief 删除NodeList之前通知
         * @param pNodeList 将被添加的NodeList对象
        */
        virtual void onDeleteBefore(WD::NodeList* pNodeList)
        {
            WDUnused(pNodeList);
        }
        /**
        * @brief 删除NodeList之后通知
        * @param pNodeList 已被删除的NodeList对象
        */
        virtual void onDeleteAfter(WD::NodeList* pNodeList)
        {
            WDUnused(pNodeList);
        }
        /**
        * @brief 当前NodeList改变通知
        * @param preNodeList 改变之前的NodeList
        * @param currNodeList 改变之后的NodeList
        */
        virtual void onCurrentNodeListChanged(WD::NodeList* preNodeList, WD::NodeList* currNodeList)
        {
            WDUnused(preNodeList);
            WDUnused(currNodeList);
        }
        /**
        * @brief 清空通知
        */
        virtual void onClear()
        {
        }
    };
    /**
    * @brief 节点表观察者列表
    */
    using Observers = WD::WDOperationList<Observer*>;
public:
    NodeListMgr();
    ~NodeListMgr();
public:
    /**
    * @brief 添加列表
    * @param name name列表名称（不能为空）
    * @return true：添加成功；false：添加失败（列表已经存在）
    */
    bool add(const std::string& name);
    /**
    * @brief 移除列表
    * @param name列表名称(不能为空)
    * @return true：移除成功；false：移除失败（列表不存在）
    */
    bool remove(const std::string& name);
    /**
    * @brief 判断列表是否存在
    * @param name 列表名称
    * @return true：列表存在；false：列表不能存在
    */
    bool exist(const std::string& name)const;
    /**
    * @brief 获取当前列表的NodeList
    * @return 当前列表的NodeList
    */
    inline NodeList* current()const
    {
        return _pCurrentList;
    }
    /**
     * @brief 获取当前列表包含的所有节点
     * @return 
    */
    inline const std::vector<WDNode::SharedPtr>& currentNodes() const
    {
        if (_pCurrentList == nullptr)
        {
            static const std::vector<WDNode::SharedPtr> emptyNodes;
            return emptyNodes;
        }
        return _pCurrentList->nodes();
    }
    /**
    * @brief 
    * @param name 获取指定列表的NodeList
    * @return 指定列表的NodeList
    */
    NodeList* find(const std::string name)const;
    /**
    * @brief 设置当前列表
    * @param name 列表名称
    * @return 当前列表
    */
    NodeList* setCurrentByName(const std::string& name);
    /**
    * @brief 设置当前列表
    * @param nodeList
    */
    void setCurrent(NodeList* pNodeList);
    /**
    * @brief 获取所有列表的名称
    * @return 
    */
    std::vector<std::string> getAllName()const;
    /**
    * @brief 获取列表管理的观察者
    */
    inline Observers& observers()
    {
        return _observers;
    }
private:
    // 当前节点列表
    NodeList* _pCurrentList = nullptr;
    // 按添加顺序保存节点列表对象
    std::vector<NodeList*> _list;
    // 列表和节点列表的映射关系
    std::map<std::string, NodeList*> _mapList2Nodes;
    // 观察者列表
    Observers _observers;
};
WD_NAMESPACE_END