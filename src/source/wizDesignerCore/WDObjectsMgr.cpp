
#include    "WDObjectsMgr.h"

WD_NAMESPACE_BEGIN

WDObject*   WDObjectsMgr::Objects::query(const std::string& name)
{
    auto    itr     =   find(name);
    if (itr == end())
        return  nullptr;
    else
        return  itr->second.get();
}

bool        WDObjectsMgr::Objects::cache(WDObject* obj,const std::string& name)
{
    std::string strName     =   name;
    if (strName.empty())
    {
        strName    =   std::string(obj->className()) + ":" + obj->name();
    }
    auto    itr =   find(strName);
    if (itr !=  end())
        return  false;
    else
        (*this)[strName]    =   obj->ptr();
    return  true;
}

/**
*   @brief  清除对象缓冲
*/
size_t      WDObjectsMgr::Objects::remove(WDObject* obj)
{
    size_t  cnt   =   0;
    for (auto itr = begin() ; itr != end();)
    {
        if(itr->second.get() != obj)
        {
            ++ itr;
            continue;
        }
        itr =   erase(itr);
        ++cnt;
    }
    return  cnt;
}

size_t      WDObjectsMgr::Objects::remove(const std::string& name)
{
    auto    itr =   find(name);
    if (itr != end())
    {
        erase(itr);
        return  1;
    }
    return  0;
}
/**
*   @brief  清除指定类型的对象
*/
void        WDObjectsMgr::Objects::removeAll()
{
    clear();
}

WDObject*    WDObjectsMgr::query(const WDUuid& classId,const std::string& name)
{
    auto&   objects =   _objectsMap[classId];
    auto    itr     =   objects.find(name);
    if (itr == objects.end())
        return  nullptr;
    else
        return  itr->second.get();
}
/**
*   @brief  查询对象
*   @param  obj: 对象指针
*   @param  name: 对象名称
*   @return false,缓冲的对象名称已经存在，否则缓冲成功
*/
bool    WDObjectsMgr::cache(WDObject* obj,const std::string& name)
{
    auto    itrObjects  =   _objectsMap.find(obj->classId());
    auto    clsId       =   obj->classId();
    /// 这里主要保存类名称，方便调试使用
    if (itrObjects == _objectsMap.end())
    {
        _objectsMap[clsId]._className  =   obj->className();
    }
    auto&       objects     =   _objectsMap[clsId];
    std::string strName     =   name;
    if (strName.empty())
    {
        strName    =   std::string(obj->className()) + ":" + obj->name();
    }
    auto    itr =   objects.find(strName);
    if (itr != objects.end())
        return  false;
    else
        objects[strName]    =   obj->ptr();
    return  true;
}


size_t  WDObjectsMgr::removeCache(WDObject* obj)
{
    auto    itr =   _objectsMap.find(obj->classId());
    if (itr == _objectsMap.end())
        return  false;
   
    return  itr->second.remove(obj);
}


void    WDObjectsMgr::clear(const WDUuid& classId)
{
    auto    itr =   _objectsMap.find(classId);
    if (itr != _objectsMap.end())
        _objectsMap.erase(itr);
}

WD_NAMESPACE_END
