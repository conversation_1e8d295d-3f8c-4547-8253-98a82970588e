#pragma     once

#include "../math/Math.hpp"


WD_NAMESPACE_BEGIN

class WDCore;
class WDContext;
class WDNode;
class WDScene;

class WDPickupParam;
class WDNodeSelection;
class WDNodePickupResult;
class WDFrameSelectParam;

/**
 * @brief 场景节点绘制对象基类
 */
class WD_API WDSceneNodeRender
{
public:
    using NodeSPtr = std::shared_ptr<WDNode>;
public:
    WDSceneNodeRender(WDCore& app, const std::string& name = "SceneNodeRender");
    virtual ~WDSceneNodeRender();
public:
    /**
     * @brief 获取 WDCore
    */
    inline WDCore& app()
    {
        return _app;
    }
    /**
     * @brief 获取aabb包围盒
    */
    inline const DAabb3& aabb() const
    {
        return _aabb;
    }
public:
    /**
     * @brief 节点添加通知
    */
    virtual void onNodeAdd(NodeSPtr pNode) = 0;
    /**
     * @brief 节点移除通知
    */
    virtual void onNodeRemove(NodeSPtr pNode) = 0;
    /**
     * @brief 节点更新通知
    */
    virtual void onNodeUpdate(NodeSPtr pNode) = 0;
    /**
     * @brief 节点清除通知
    */
    virtual void onNodeClear() = 0;
    /**
     * @brief 是否为空,即不包含任何节点
    */
    virtual bool empty() const = 0;
    /**
     * @brief 节点 或 节点的直系祖先 或 节点的后代 是否被添加进场景中
     * @return 只要满足有加入的节点与 node相同 或 是node的直系祖先或后代节点，就需要返回true
    */
    virtual bool containsRelatives(const WDNode& node)const = 0;

    /**
     * @brief 更新包围盒
     *  目前场景的裁剪空间是使用场景包围盒自动重算的，因此每次更新和绘制之前，都需要重新收集并更新场景包围盒
     *  以保证裁剪空间是使用最新的场景包围盒计算的
    */
    virtual void updateAabb(WDContext& context, const WDScene& scene) = 0;
    /**
     * @brief 更新
    */
    virtual void update(WDContext& context, const WDScene& scene) = 0;
    /**
     * @brief 绘制
    */
    virtual void render(WDContext& context, const WDScene& scene) = 0;
    /**
     * @brief 拾取
    */
    virtual bool pickup(const WDPickupParam& param
        , WDNodeSelection& selection
        , WDNodePickupResult& outResult) const;
    /**
     * @brief 框选
    */
    virtual bool frameSelect(const WDFrameSelectParam& param
        , WDNodeSelection& selection
        , std::vector<NodeSPtr>& outNodes) const;
    /**
     * @brief 孔洞开关改变
    */
    virtual void onHolesDrawnChanged();
    /**
     * @brief 需要更新
     *  如果某些更新是使用更新标记的，就需要重写该方法并在方法中设置更新标记
    */
    virtual void needUpdate();
protected:
    /**
     * @brief 获取绘制对象包围盒的引用
     *  由子类获取并设置包围盒的值
    */
    inline DAabb3& aabbRef()
    {
        return _aabb;
    }
private:
    //
    WDCore& _app;
    // 绘制对象名称
    std::string _name;
    // 当前绘制对象的包围盒
    DAabb3 _aabb;
};

WD_NAMESPACE_END



