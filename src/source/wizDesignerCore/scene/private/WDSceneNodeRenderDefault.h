#pragma     once

#include "../WDSceneNodeRender.h"
#include "../WDScene.h"

#include "../../common/WDConfig.h"
#include "../../graphable/WDGraphableInterface.h"

WD_NAMESPACE_BEGIN

namespace CFG
{
    class Value;
}
/**
 * @brief 绘制场景模型
*/
class WDSceneNodeRenderDefaultPrivate;
class KeyPointsRender;
class PLinesRender;
class GLinesRender;
class GTextsRender;
class GMeshRender;

class WDSceneNodeRenderDefault : public WDSceneNodeRender
{
private:
    // 更新标志
    enum UpdateFlag
    {
        //
        UF_None = 0,
        //更新Aabb
        UF_Aabb = 1 << 0,
        //需要材质排序
        UF_MaterialSort = 1 << 2,
    };
    using UpdateFlags = WDFlags<UpdateFlag, unsigned int>;
    UpdateFlags _updateFlags;

    // 节点 set
    using NodeUSet = std::set <WDNode*>;
    //添加的节点列表(这里只包含_geomsMap和_keyPointsMap中包含的节点)
    NodeUSet _nodes;

    // 选中实体颜色
    Color _selectedSolidColor = Color(170, 170, 255);
    // 选中线框颜色
    Color _selectedWireframeColor = Color(0, 255, 255);
    // 选中线框粗细
    float _selectedLineWidth = 2.0f;
    // 常规线框粗细
    float _normalLineWidth = 1.0f;
    // 激活时的颜色
    Color _activedColor = Color(0, 0, 255);
    // 高亮时的颜色
    Color _highlightColor = Color(255, 0, 0);

    // 保温模型透明度
    float _alphaInsu;
    // 软碰撞模型透明度
    float _alphaSoftCollder;


    // 用于绘制点集
    KeyPointsRender* _pKeyPointsRender;
    friend class KeyPointsRender;
    // 用于绘制Pline集
    PLinesRender* _pLinesRender;
    friend class PLinesRender;
    // 用于绘制GLines
    GLinesRender* _pGLinesRender;
    friend class GLinesRender;
    // 用于绘制GTexts
    GTextsRender* _pGTextsRender;
    friend class GTextsRender;
    // 用于绘制几何体实体
    GMeshRender* _pGMeshRender;
    friend class GMeshRender;

    WDSceneNodeRenderDefaultPrivate* _p;
    friend class WDSceneNodeRenderDefaultPrivate;
public:
    WDSceneNodeRenderDefault(WDCore& app);
    ~WDSceneNodeRenderDefault();
public:
    virtual void onNodeAdd(WDNode::SharedPtr pNode) override;
    virtual void onNodeRemove(WDNode::SharedPtr pNode) override;
    virtual void onNodeUpdate(WDNode::SharedPtr pNode) override;
    virtual void onNodeClear() override;
    virtual bool empty() const override;
    virtual bool containsRelatives(const WDNode& node)const override;
    virtual void updateAabb(WDContext& context, const WDScene& scene) override;
    virtual void update(WDContext& context, const WDScene& scene) override;
    virtual void render(WDContext& context, const WDScene& scene) override;
    virtual bool pickup(const WDPickupParam& param
        , WDNodeSelection& selection
        , WDNodePickupResult& outResult) const override;
    virtual bool frameSelect(const WDFrameSelectParam& param
        , WDNodeSelection& selection
        , std::vector<WDNode::SharedPtr>& outNodes) const override;
    virtual void onHolesDrawnChanged() override;
    virtual void needUpdate() override;
private:
    // 更新包围盒
    void updateAabb();

    // 处理图形化对象添加
    bool addGraphable(WDNode* pNode, WDGraphableInterface* pGraphable);
    // 图例图形化对象移除
    bool removeGraphable(WDNode* pNode, WDGraphableInterface* pGraphable);
    // 处理图形化对象清除
    void clearGraphable();
private:
    void updateHolesNodes();
};

WD_NAMESPACE_END



