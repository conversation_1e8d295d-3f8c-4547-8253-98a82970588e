#include    "WDScene.h"

#include    "WDRenderObject.h"
#include    "private/WDSceneNodeRenderDefault.h"

#include    "../viewer/WDViewer.h"
#include    "../selections/WDNodeSelection.h"

#include    <unordered_set>

WD_NAMESPACE_BEGIN

/**
 * @brief 设置或取消设置节点以及其子节点的选中标志
*/
void SetSelectedFlags(WDNode& node, bool bSet)
{
    WDNode::RecursionHelpter(node, [bSet](WDNode& node)
        {
            auto flags = node.flags();
            flags.setFlag(WDNode::Flag::F_Selected, bSet);
            node.setFlags(flags);
        });
}

class WD_API WDSceneSelectedNodeList
{
public:
    /**
     * @brief 过滤器列表
    */
    using Filter    = WDScene::SelectedNodeFilter;
    using Filters   = std::vector<Filter>;
public:
    WDSceneSelectedNodeList(WDScene& scene):_scene(scene)
    {
        _filterOutNodes.reserve(10000);
    }
public:
    /**
     * @brief 节点列表改变通知
    */
    WDScene::NoticeSelectedNodeListChanged& noticeChanged() 
    {
        return _noticeChanged;
    }
    /**
     * @brief 获取过滤器列表
    */
    Filters& filters()
    {
        return _filters;
    }
    /**
     * @brief 添加
     *  如果所有节点均存在于列表，则移除，只要有一个不在列表中，则添加
     * @param nodes 要添加的节点
    */
    void add(const std::vector<WDNode::SharedPtr>& nodes)
    {
        if (nodes.empty())
            return;

        // 执行过滤
        auto tNodes = this->execFilter(nodes);
        if (tNodes.empty())
            return;

        std::vector<std::pair<int, WDNode::SharedPtr> > tmpNodes;
        tmpNodes.reserve(tNodes.size());

        size_t addCnt = 0;
        for (auto pNode : tNodes)
        {
            if (pNode == nullptr)
                continue;

            auto tV = std::make_pair(-1, pNode);
            for (size_t i = 0; i < _nodes.size(); ++i)
            {
                auto pTNode = _nodes[i].lock();
                if (pTNode == nullptr)
                    continue;
                if (pNode != pTNode)
                    continue;
                // 节点已存在了
                tV.first = static_cast<int>(i);
                break;
            }
            // 证明节点不存在，需要添加
            if (tV.first < 0)
                addCnt++;
            tmpNodes.push_back(tV);
        }
        // 需要添加
        if (addCnt > 0)
        {
            // 添加到列表中
            for (const auto& tn : tmpNodes)
            {
                // 索引大于等于0,证明节点已存在，不需要添加
                if (tn.first >= 0)
                    continue;
                auto pNode = tn.second;
                // 设置标志
                SetSelectedFlags(*pNode, true);
                // 添加
                _nodes.push_back(pNode);
            }
        }
        // 需要移除
        else
        {
            // 先将需要移除的标记, 再将标记为false的重新加入到数组
            std::vector<std::pair<bool, WDNode::WeakPtr> > rNodes;
            rNodes.reserve(_nodes.size());
            for (auto node : _nodes)
            {
                rNodes.push_back(std::make_pair(false, node.lock()));
            }
            for (const auto& tn : tmpNodes)
            {
                auto idx = tn.first;
                if (idx < 0 || idx >= rNodes.size())
                    continue;
                // 这些是需要移除的, 取消选择状态
                auto pNode = rNodes[idx].second.lock();
                if (pNode != nullptr)
                    SetSelectedFlags(*pNode, false);
                // 移除标记设置为true
                rNodes[idx].first = true;
            }
            // 清除原数组，并按照标记重新添加
            _nodes.clear();
            _nodes.reserve(tNodes.size());
            for (auto node : rNodes)
            {
                if (node.first)
                    continue;
                _nodes.push_back(node.second);
            }
        }

        _noticeChanged(_scene);
    }
    /**
     * @brief 设置选择的节点列表
     *  相当于清除当前所有选择，然后在添加nodes到列表中
    */
    void set(const std::vector<WDNode::SharedPtr>& nodes)
    {
        // 执行过滤
        auto tNodes = this->execFilter(nodes);

        // 都为空，不继续了
        if (_nodes.empty() && tNodes.empty())
            return;


        std::unordered_map<WDNode::SharedPtr, bool> tMap;
        for (auto pNode : tNodes)
        {
            if (pNode == nullptr)
                continue;
            tMap[pNode] = false;
        }
        size_t existCnt = 0;
        for (auto node : _nodes)
        {
            auto pNode = node.lock();
            if (pNode == nullptr)
                continue;
            // 检查该节点是否将要被添加，如果将要被添加，则不用取消选中标志
            auto fItr = tMap.find(pNode);
            if (fItr != tMap.end())
            {
                fItr->second = true;
                existCnt++;
                continue;
            }
            // 取消设置选中标志
            SetSelectedFlags(*pNode, false);
        }
        // 表明此次设置的列表与之前列表内容一致，不用再进行后续操作了，直接返回
        if (existCnt == _nodes.size() && existCnt == tNodes.size())
            return;

        _nodes.clear();
        _nodes.reserve(tNodes.size());

        // 添加进列表
        for (auto pNode : tNodes)
        {
            if (pNode == nullptr)
                continue;

            // 如果在tMap中找到并且fItr->second为true，表明这个节点存在于旧的列表中，并且选中标志已经设置了，此时不用再重复设置选中标志
            auto fItr = tMap.find(pNode);
            // 这里不用设置选中标志了
            if (fItr != tMap.end() && fItr->second) 
                ;
            // 设置选中标志
            else
                SetSelectedFlags(*pNode, true);

            _nodes.push_back(pNode);
        }

        _noticeChanged(_scene);
    }
    /**
     * @brief 移除
    */
    void remove(const WDNode::Nodes& nodes) 
    {
        if (nodes.empty())
            return;

        std::unordered_set<WDNode::SharedPtr> tNodeSet;
        for (auto pNode: nodes)
            tNodeSet.insert(pNode);
        // 
        std::vector<std::pair<WDNode::SharedPtr, bool> > rNodes;
        rNodes.reserve(_nodes.size());
        bool bNeedRemove = false;
        for (auto node : _nodes) 
        {
            auto pNode = node.lock();
            if (pNode == nullptr)
                continue;

            bNeedRemove = false;
            auto fItr = tNodeSet.find(pNode);
            if (fItr != tNodeSet.end())
            {
                // 在移除列表中直接找到了，需要执行移除
                bNeedRemove = true;
            }
            else
            {
                // 需要校验移除列表中是否存在前选择列表节点的祖先，如果存在，也需要执行移除的哦
                for (auto pTNode : tNodeSet)
                {
                    if (pTNode == nullptr)
                        continue;
                    // 如果没有子，就不用往下判断了
                    if (pTNode->children().empty())
                        continue;
                    if (pTNode->isAncestor(*pNode))
                    {
                        bNeedRemove = true;
                        break;
                    }
                }
            }
            rNodes.push_back(std::make_pair(pNode, bNeedRemove));
        }

        _nodes.clear();
        _nodes.reserve(rNodes.size());
        bool bRemoved = false;
        for (const auto& item : rNodes)
        {
            if (item.second)
            {
                bRemoved = true;
                // 取消选中标志
                SetSelectedFlags(*(item.first), false);
                continue;
            }
            _nodes.push_back(item.first);
        }
        if (bRemoved)
            _noticeChanged(_scene);
    }
    /**
     * @brief 清除选择列表
    */
    void clear()
    {
        if (_nodes.empty())
            return;

        for (auto node : _nodes)
        {
            auto pNode = node.lock();
            if (pNode == nullptr)
                continue;

            // 取消设置选中标志
            SetSelectedFlags(*pNode, false);
        }
        _nodes.clear();

        _noticeChanged(_scene);
    }
    /**
     * @brief 获取选择的节点列表
    */
    std::vector<WDNode::SharedPtr> nodes() const 
    {
        std::vector<WDNode::SharedPtr> rNodes;
        rNodes.reserve(_nodes.size());
        for (auto node : _nodes) 
        {
            if (node.expired())
                continue;
            rNodes.push_back(node.lock());
        }
        return rNodes;
    }
private:
    // 执行过滤
    std::unordered_set<WDNode::SharedPtr> execFilter(const WDNode::Nodes& nodes)
    {
        std::unordered_set<WDNode::SharedPtr> rSet;

        if (_filters.empty())
        {
            for (auto pNode : nodes)
            {
                if (pNode == nullptr)
                    continue;
                rSet.insert(pNode);
            }
            return rSet;
        }

        auto tNodes = nodes;
        for (const auto& filter : _filters)
        {
            _filterOutNodes.clear();
            filter(tNodes, _filterOutNodes);
            if (_filterOutNodes.empty())
                continue;
            tNodes = _filterOutNodes;
        }
        // 因为这里用的是强引用，所以需要把缓存清除掉，以防止节点删除后不会被释放的问题
        _filterOutNodes.clear();

        for (auto pTNode : tNodes)
        {
            if (pTNode == nullptr)
                continue;
            rSet.insert(pTNode);
        }

        return rSet;
    }
    // 执行过滤
    std::vector<WDNode::SharedPtr> execFilter(const std::vector<WDNode*>& nodes)
    {
        std::vector<WDNode::SharedPtr> rNodes;
        // 用于去重
        std::unordered_set<WDNode::SharedPtr> tSet;

        if (_filters.empty())
        {
            rNodes.reserve(nodes.size());
            for (auto pNode : nodes)
            {
                auto pTNode = WDNode::ToShared(pNode);
                if (pTNode == nullptr)
                    continue;
                if (tSet.find(pTNode) != tSet.end())
                    continue;
                tSet.insert(pTNode);
                rNodes.push_back(pTNode);
            }
            return rNodes;
        }

        std::vector<WDNode::SharedPtr> tNodes;
        tNodes.reserve(nodes.size());
        for (auto pNode : nodes)
        {
            if (pNode == nullptr)
                continue;
            tNodes.push_back(WDNode::ToShared(pNode));
        }

        for (const auto& filter : _filters)
        {
            _filterOutNodes.clear();
            filter(tNodes, _filterOutNodes);
            if (_filterOutNodes.empty())
                continue;
            tNodes = _filterOutNodes;
        }
        // 因为这里用的是强引用，所以需要把缓存清除掉，以防止节点删除后不会被释放的问题
        _filterOutNodes.clear();

        rNodes.reserve(tNodes.size());
        for (auto pTNode : tNodes)
        {
            if (pTNode == nullptr)
                continue;
            if (tSet.find(pTNode) != tSet.end())
                continue;
            tSet.insert(pTNode);
            rNodes.push_back(pTNode);
        }
        return rNodes;
    }
private:
    // 场景
    WDScene& _scene;
    // 被选择的节点列表
    std::vector<WDNode::WeakPtr> _nodes;
    // 选择列表改变通知
    WDScene::NoticeSelectedNodeListChanged _noticeChanged;
    // 过滤器列表
    Filters _filters;
    // 过滤使用的输出节点列表, 为了缓存加速
    std::vector<WDNode::SharedPtr> _filterOutNodes;

};


class WDScenePrivate : public WDNodeObserver
{
public:
    // app对象
    WDCore& _app;
    // 场景对象
    WDScene& _d;
    // 绘制模式
    WDScene::RMode _rMode = WDScene::RMode::RM_Solid;
    //这里保存观察的公共根节点列表,方便在清除场景时，取消对所有节点的观察
    std::vector<WDNode::WeakPtr> _obsRoot;
    // 场景包围盒
    DAabb3 _aabb;
    // 场景节点绘制器
    using Renders = std::vector<WDSceneNodeRender*>;
    Renders _renders;
    // 自定义绘制对象列表
    using RenderObjects = std::vector<WDRenderObject*>;
    RenderObjects _renderObjects;
    // 默认的绘制对象
    WDSceneNodeRenderDefault _defaultRender;

    //场景Aabb改变通知
    WDScene::NoticeSceneAabbChanged _noticeSceneAabbChanged;
    // 当前点击坐标改变通知
    WDScene::NoticePickup _noticePickup;

    WDSceneSelectedNodeList _selectedNodeList;


    bool _bModifyInTheSceneFlags;
public:
    WDScenePrivate(WDCore& app, WDScene& d)
        : _app(app)
        , _d(d) 
        , _defaultRender(app)
        , _selectedNodeList(d)
        , _bModifyInTheSceneFlags(true)
    {
        _aabb = WDScene::DefaultAabb();
    }
    virtual ~WDScenePrivate()
    {
    }
public:
    /***********************节点观察者相关*********************/
    virtual void onNodeAddChildAfter(WDNode::SharedPtr pNode, WDNode::SharedPtr pChild) override
    {
        WDUnused(pNode);
        _d.add(pChild);
    }
    virtual void onNodeInsertChildAfter(WDNode::SharedPtr pNode, WDNode::SharedPtr pChild, WDNode::SharedPtr pNextChild) override
    {
        WDUnused(pNode);
        WDUnused(pNextChild);
        _d.add(pChild);
    }
    virtual void onNodeRemoveChildBefore(WDNode::SharedPtr pNode, WDNode::SharedPtr pChild) override
    {
        WDUnused(pNode);
        _d.remove(pChild);
    }
    virtual void onNodeUpdateAfter(WDNode::SharedPtr pNode) override
    {
        for (const auto& pRender : _renders)
        {
            if (pRender == nullptr)
                continue;
            pRender->onNodeUpdate(pNode);
        }
    }
    virtual void onNodeDestroyBefore(WDNode::SharedPtr pNode) override
    {
        _d.remove(pNode);
    }
public:
    /**
     * @brief 节点添加到场景，这里会递归其子节点
    */
    void onNodeAddToScene(WDNode::SharedPtr pNode)
    {
        if (pNode == nullptr)
            return;

        WDNode::RecursionHelpter(*pNode, &WDScenePrivate::onNodeRecursionAdd, this);
        
        //将最靠近跟的节点保存下来，以便清除场景时将所有的节点观察取消
        bool bAdd = true;
        for (auto itr = _obsRoot.begin(); itr != _obsRoot.end(); )
        {
            auto pNodeAdded = (*itr).lock();
            if (pNodeAdded == nullptr)
            {
                assert(false && "无效的节点对象!");
                continue;
            }
            //已加入的节点中存在该节点或存在该节点的祖先，则不用再重复添加
            if (pNodeAdded == pNode || pNodeAdded->isAncestor(*pNode))
            {
                bAdd = false;
                break;
            }
            //将要加入的节点 是某个已加入节点的祖先, 剔除掉该已加入的节点
            else if (pNode->isAncestor(*pNodeAdded))
            {
                itr = _obsRoot.erase(itr);
                continue;
            }
            else
            {
                ++itr;
            }
        }

        if (bAdd)
        {
            _obsRoot.push_back(pNode);
        }
    }
    /**
     * @brief 节点从场景移除，这里会递归其子节点
    */
    void onNodeRemoveFromScene(WDNode::SharedPtr pNode)
    {
        if (pNode == nullptr)
            return;

        WDNode::RecursionHelpter(*pNode, &WDScenePrivate::onNodeRecursionRemove, this);

        //将最靠近跟的节点保存下来，以便清除场景时将所有的节点观察取消
        for (auto itr = _obsRoot.begin(); itr != _obsRoot.end(); )
        {
            auto pNodeAdded = (*itr).lock();
            if (pNodeAdded == nullptr)
            {
                assert(false && "无效的节点对象!");
                continue;
            }
            //要移除的节点 与  已添加的节点 相同 或 要移除节点 是 已添加节点的祖先
            //表明 已添加节点以及子孙节点的 所有观察者已被移除,不在保存了观察列表中
            if (pNodeAdded == pNode || pNode->isAncestor(*pNodeAdded))
            {
                itr = _obsRoot.erase(itr);
                continue;
            }
            //已添加的节点 是 要移除的节点 的祖先,不做处理
            else if (pNodeAdded->isAncestor(*pNode))
            {
                ++itr;
                continue;
            }
            else
            {
                ++itr;
            }
        }
    }
    /**
     * @brief 清除场景中的节点
    */
    void onNodeClearFromScene()
    {
        //调用所有绘制器的清除
        for (const auto& pRender : _renders)
        {
            if (pRender == nullptr)
                continue;
            pRender->onNodeClear();
        }
        // 从保存的根节点列表中开始递归，取消观察该节点以及所有子孙节点
        while(!_obsRoot.empty())
        {
            auto pNode = _obsRoot.back().lock();
            if (pNode != nullptr)
            {
                WDNode::RecursionHelpter(*pNode, &WDScenePrivate::onCancelNodeObserverRecursion, this);
            }
            _obsRoot.pop_back();
        }
    }
private:
    void onNodeRecursionAdd(WDNode& node)
    {

        for (const auto& pRender : _renders)
        {
            if (pRender == nullptr)
                continue;
            pRender->onNodeAdd(WDNode::ToShared(&node));
        }

        // 添加节点观察者
        node.observers() += this;
        // 添加在场景的标志
        if (_bModifyInTheSceneFlags)
        {
            auto flags = node.flags();
            flags.addFlag(WDNode::F_InTheScene);
            node.setFlags(flags);
        }
    }
    void onNodeRecursionRemove(WDNode& node)
    {
        for (const auto& pRender : _renders)
        {
            if (pRender == nullptr)
                continue;
            pRender->onNodeRemove(WDNode::ToShared(&node));
        }
        //移除节点观察者
        node.observers() -= this;
        // 移除在场景的标志
        if (_bModifyInTheSceneFlags)
        {
            auto flags = node.flags();
            flags.removeFlag(WDNode::F_InTheScene);
            node.setFlags(flags);
        }
    }
    void onCancelNodeObserverRecursion(WDNode& node)
    {
        node.observers() -= this;
        // 移除在场景的标志
        if (_bModifyInTheSceneFlags)
        {
            auto flags = node.flags();
            flags.removeFlag(WDNode::F_InTheScene);
            node.setFlags(flags);
        }
    }
};

const Aabb3& WDScene::DefaultAabb()
{
    static const Aabb3 sDef = Aabb3(Vec3(-20000.0), Vec3(20000.0));
    return sDef;
}

WDScene::WDScene(WDCore& app)
{
    _p = new WDScenePrivate(app, *this);

    this->addRender(&(_p->_defaultRender));
}
WDScene::~WDScene()
{
    //清除所有已添加的节点
    this->clear();

    //清除所有绘制器对象
    this->clearRender();

    delete _p;
    _p = nullptr;
}

WDScene::RMode WDScene::rMode() const
{
    return  _p->_rMode;
}
void WDScene::setRMode(RMode mode)
{
    _p->_rMode = mode;
}

void    WDScene::add(WDNode::SharedPtr pNode)
{
    if (pNode == nullptr)
        return;
    _p->onNodeAddToScene(pNode);
}
void    WDScene::remove(WDNode::SharedPtr pNode)
{
    if (pNode == nullptr)
        return;
    _p->onNodeRemoveFromScene(pNode);
    // 从选择列表中移除
    _p->_selectedNodeList.remove({ pNode });
}
void    WDScene::add(const WDNode::Nodes& nodes)
{
    if (nodes.empty())
        return;
    for (auto pNode : nodes) 
    {
        this->add(pNode);
    }
}
void    WDScene::remove(const WDNode::Nodes& nodes)
{
    if (nodes.empty())
        return;

    for (auto pNode : nodes)
    {
        if (pNode == nullptr)
            continue;
        _p->onNodeRemoveFromScene(pNode);
    }
    // 从选择列表中移除
    _p->_selectedNodeList.remove(nodes);
}
void    WDScene::clear()
{
    _p->onNodeClearFromScene();
    // 清除选择列表
    _p->_selectedNodeList.clear();
}
bool    WDScene::empty() const
{
    for (const auto& pRender : _p->_renders)
    {
        if (pRender == nullptr)
            continue;
        if (!pRender->empty())
            return false;
    }
    return true;
}
bool    WDScene::contains(const WDNode& node) const
{
    assert(_p->_bModifyInTheSceneFlags);
    return node.flags().hasFlag(WDNode::F_InTheScene);
}
bool    WDScene::containsRelatives(const WDNode& node) const
{
    for (const auto& pRender : _p->_renders)
    {
        if (pRender == nullptr)
            continue;
        if (pRender->containsRelatives(node))
            return true;
    }
    return false;
}

const DAabb3& WDScene::aabb() const
{
    return _p->_aabb;
}
WDNode::Nodes WDScene::obsRoot() const
{
    WDNode::Nodes obsRoot;
    for (auto pNode : _p->_obsRoot)
    {
        if (pNode.expired())
            continue;
        obsRoot.push_back(pNode.lock());
    }
    return obsRoot;
}

bool    WDScene::pickup(const WDViewer& viewer, const DRay& ray
    , WDNodePickupResult& outResult
    , SelectedListAddMode sMode) const
{
    auto pCamera = viewer.camera();

    WDPickupParam param;
    param.setCamera(pCamera.get());
    param.setRay(ray);

    switch (_p->_rMode)
    {
    case WD::WDScene::RM_Solid:
        param.setPickupMode(WDPickupParam::PickupMode::PM_Normal);
        break;
    case WD::WDScene::RM_WireFrame:
        param.setPickupMode(WDPickupParam::PickupMode::PM_WireFrame);
        break;
    default:
        break;
    }

    WDNodeSelection nodeSelection(nullptr, &(viewer.clip()));
    return this->pickup(param, nodeSelection, outResult, sMode);
}
bool    WDScene::frameSelect(const WDViewer& viewer
    , const IVec2& screenPos0
    , const IVec2& screenPos1
    , WDFrameSelectParam::FrameSelectionType frameSelectionType
    , std::vector<WDNode::SharedPtr>& outNodes
    , SelectedListAddMode sMode) const
{
    auto    pCamera = viewer.camera();

    const DFrustum& srcFrustum = viewer.context()._frustum;

    DVec2 viewerCenter = DVec2(viewer.size()) * 0.5;
    //计算框选视椎体
    DFrustum frustum = srcFrustum;
    DVec3 w0 = pCamera->screenToWorld(DVec2(screenPos0), 1.0);
    DVec3 w1 = pCamera->screenToWorld(DVec2(screenPos1), 1.0);
    frustum[0] = DPlane(srcFrustum[0].normal, w1); //right
    frustum[1] = DPlane(srcFrustum[1].normal, w0); //left
    frustum[2] = DPlane(srcFrustum[2].normal, w1); //bottom
    frustum[3] = DPlane(srcFrustum[3].normal, w0); //top
    //frustum[4] = DPlane(srcFrustum[4].normal, srcFrustum[4].contant); //far
    //frustum[5] = DPlane(srcFrustum[5].normal, srcFrustum[5].contant); //near

    WDFrameSelectParam param;
    param.setCamera(pCamera.get());
    param.setFrameSelectionType(frameSelectionType);
    param.setFrustum(frustum);

    switch (_p->_rMode)
    {
    case WD::WDScene::RM_Solid:
        param.setFrameSelectionMode(WDFrameSelectParam::FrameSelectionMode::FSM_Normal);
        break;
    case WD::WDScene::RM_WireFrame:
        param.setFrameSelectionMode(WDFrameSelectParam::FrameSelectionMode::FSM_WireFrame);
        break;
    default:
        break;
    }
    WDNodeSelection nodeSelection(nullptr, &(viewer.clip()));
    return this->frameSelect(param, nodeSelection, outNodes, sMode);
}
bool    WDScene::pickup(const WDPickupParam& param
    , WDNodeSelection& selection
    , WDNodePickupResult& outResult
    , SelectedListAddMode sMode) const
{
    const WDCamera* pCamera = param.camera();
    if (pCamera == nullptr)
        return false;
    bool bPickup = false;
    double minDisSq = NumLimits<double>::Max;
    for (const auto& pRender : _p->_renders)
    {
        WDNodePickupResult tOutResult;

        if (!param.intersectAabb(pRender->aabb()))
            continue;

        if (!pRender->pickup(param, selection, tOutResult))
            continue;

        double tDisSq = DVec3::DistanceSq(tOutResult.point, pCamera->eye());
        if (tDisSq < minDisSq)
        {
            outResult = tOutResult;
            bPickup = true;
            minDisSq = tDisSq;
        }
    }
    if (bPickup && outResult.node.lock() != nullptr)
    {
        _p->_noticePickup(*(outResult.node.lock()), outResult.point);
    }

    switch (sMode)
    {
    case WD::WDScene::SLAM_None:
    {
    }
    break;
    case WD::WDScene::SLAM_Set:
    {
        if (bPickup && outResult.node.lock() != nullptr)
            _p->_selectedNodeList.set({ outResult.node.lock()});
        else
            _p->_selectedNodeList.set({});
    }
    break;
    case WD::WDScene::SLAM_Append:
    {
        if (bPickup && outResult.node.lock() != nullptr)
            _p->_selectedNodeList.add({ outResult.node.lock()});
    }
    break;
    default:
        break;
    }

    return bPickup;
}
bool    WDScene::frameSelect(const WDFrameSelectParam& param
    , WDNodeSelection& selection
    , std::vector<WDNode::SharedPtr>& outNodes
    , SelectedListAddMode sMode) const
{
    for (const auto& pRender : _p->_renders)
    {
        std::vector<WDNode::SharedPtr> tOutNodes;

        if (!param.intersectAabb(pRender->aabb()))
            continue;

        if (!pRender->frameSelect(param, selection, tOutNodes))
            continue;

        for (size_t i = 0; i < tOutNodes.size(); ++i)
        {
            auto pNode = tOutNodes[i];
            auto itr = std::find(outNodes.begin(), outNodes.end(), pNode);
            if (itr == outNodes.end())
                outNodes.push_back(pNode);
        }
    }

    switch (sMode)
    {
    case WD::WDScene::SLAM_None:
    {

    }
    break;
    case WD::WDScene::SLAM_Set:
    {
        _p->_selectedNodeList.set(outNodes);
    }
    break;
    case WD::WDScene::SLAM_Append:
    {
        _p->_selectedNodeList.add(outNodes);
    }
    break;
    default:
        break;
    }

    return !outNodes.empty();
}

std::vector<WDNode::SharedPtr> WDScene::selectedNodes() const
{
    return _p->_selectedNodeList.nodes();
}

void WDScene::addSelectedNodeFilter(const SelectedNodeFilter& filter)
{
    if (!filter)
        return;
    auto& filters = _p->_selectedNodeList.filters();
    auto fItr = std::find(filters.begin(), filters.end(), filter);
    if (fItr != filters.end())
        return;
    filters.push_back(filter);
}
void WDScene::removeSelectedNodeFilter(const SelectedNodeFilter& filter)
{
    if (!filter)
        return;
    auto& filters = _p->_selectedNodeList.filters();
    auto fItr = std::find(filters.begin(), filters.end(), filter);
    if (fItr == filters.end())
        return;
    filters.erase(fItr);
}

void    WDScene::addRender(WDSceneNodeRender* pRender)
{
    if (pRender == nullptr)
        return;

    if (this->containsRender(pRender))
        return;
    _p->_renders.push_back(pRender);
}
void    WDScene::removeRender(WDSceneNodeRender* pRender)
{
    if (pRender == nullptr)
        return;

    auto fItr = std::find(_p->_renders.begin(), _p->_renders.end(), pRender);
    if (fItr != _p->_renders.end())
    {
        _p->_renders.erase(fItr);
    }
}
bool    WDScene::containsRender(WDSceneNodeRender* pRender)
{
    if (pRender == nullptr)
        return false;

    auto fItr = std::find(_p->_renders.begin(), _p->_renders.end(), pRender);
    return fItr != _p->_renders.end();
}
void    WDScene::clearRender()
{
    _p->_renders.clear();
}

void    WDScene::addRenderObject(WDRenderObject* pRenderObject)
{
    if (this->containsRenderObject(pRenderObject))
        return;
    _p->_renderObjects.push_back(pRenderObject);
}
void    WDScene::removeRenderObject(WDRenderObject* pRenderObject)
{
    auto fItr = std::find(_p->_renderObjects.begin(), _p->_renderObjects.end(), pRenderObject);
    if (fItr == _p->_renderObjects.end())
        return;
    _p->_renderObjects.erase(fItr);
}
bool    WDScene::containsRenderObject(WDRenderObject* pRenderObject)
{
    auto fItr = std::find(_p->_renderObjects.begin(), _p->_renderObjects.end(), pRenderObject);
    return fItr != _p->_renderObjects.end();
}

void    WDScene::needUpdate()
{
    for (const auto& pRender : _p->_renders)
    {
        pRender->needUpdate();
    }
}
void    WDScene::holesDrawnChanged()
{
    for (const auto& pRender : _p->_renders)
    {
        pRender->onHolesDrawnChanged();
    }
}

WDScene::NoticeSceneAabbChanged& WDScene::noticeSceneAabbChanged()
{
    return _p->_noticeSceneAabbChanged;
}
WDScene::NoticePickup& WDScene::noticePickup()
{
    return _p->_noticePickup;
}
WDScene::NoticeSelectedNodeListChanged& WDScene::noticeSelectedNodeListChanged()
{
    return _p->_selectedNodeList.noticeChanged();
}

bool WDScene::modifyInTheSceneFlagEnabled() const
{
    return _p->_bModifyInTheSceneFlags;
}
void WDScene::setModifyInTheSceneFlagEnabled(bool enabled)
{
    _p->_bModifyInTheSceneFlags = enabled;
}

void    WDScene::updateAabb(WDContext& context)
{
    Aabb3 aabb = Aabb3::Null();
    //调用节点绘制对象更新
    for (auto pRender : _p->_renders)
    {
        if (pRender == nullptr)
            continue;
        // 调用更新包围盒
        pRender->updateAabb(context, *this);
        // 获取包围盒并合并到场景中
        const auto& tAabb = pRender->aabb();
        aabb.unions(tAabb);
    }
    //更新自定义绘制对象
    for (auto pObject : _p->_renderObjects)
    {
        if (pObject == nullptr)
            continue;
        // 调用更新
        pObject->updateAabb(context, *this);
        // 获取包围盒并合并到场景中
        const auto& tAabb = pObject->aabb();
        aabb.unions(tAabb);
    }
    // 如果包围盒发生了改变，则通知
    if (!aabb.isNull() && !_p->_aabb.isNull() && aabb != _p->_aabb)
    {
        DAabb3 prevAabb = _p->_aabb;
        _p->_aabb = aabb;
        _p->_noticeSceneAabbChanged(_p->_aabb, prevAabb, *this);
    }
}
void    WDScene::update(WDContext& context)
{
    //调用节点绘制对象更新
    for (auto pRender : _p->_renders)
    {
        if (pRender == nullptr)
            continue;
        // 调用更新
        pRender->update(context, *this);
    }
    //更新自定义绘制对象
    for (auto pObject : _p->_renderObjects)
    {
        if (pObject == nullptr)
            continue;
        // 调用更新
        pObject->update(context, *this);
    }
}
void    WDScene::render(WDContext& context)
{
    //调用节点绘制对象绘制
    for (const auto& pRender : _p->_renders)
    {
        pRender->render(context, *this);
    }
    //调用自定义绘制对象绘制
    for (const auto& var : _p->_renderObjects)
    {
        auto    flg = var->renderLayer();
        if ((flg & context._renderLayer) != 0)
        {
            var->render(context, *this);
        }
    }
}

WD_NAMESPACE_END
