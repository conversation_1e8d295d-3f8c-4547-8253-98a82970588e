#include "WDPerspectiveCamera.h"

WD_NAMESPACE_BEGIN


WDPerspectiveCamera::WDPerspectiveCamera(real fov, real aspect, real zNear, real zFar, real zoom)
    :_fov(fov), _aspect(aspect)
{
    _zNear = zNear;
    _zFar = zFar;
    _zoom = zoom;
    updateProjectMatrix();
}
WDPerspectiveCamera::~WDPerspectiveCamera()
{
}

void WDPerspectiveCamera::updateProjectMatrix()
{
    const real  aspect  =   _viewSize.x/_viewSize.y;
    const real  ymax    =   _zNear * Tan(DegToRad(0.5 * _fov)) /_zoom ;
    const real  xmax    =   ymax * aspect;
    _matProj            =   DMat4::MakePerspective(-xmax, xmax, ymax,-ymax,  _zNear, _zFar);
    _matProjInv         =   _matProj.inverse();
}

WD_NAMESPACE_END

