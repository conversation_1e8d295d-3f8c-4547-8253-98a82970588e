#pragma     once

#include    "WIZDesignerVersion.h"
#include    "common/WDUtils.h"
#include    "common/WDObject.h"

WD_NAMESPACE_BEGIN

class   WDConfig;
class   WDMessage;
class   WDUndoStack;

class   WDObjectCreator;
class   WDObjectsMgr;

class   WDTaskSys;
class   WDTimerSys;

class   WDExtensionMgr;
class   WDNodeTree;
class   WDScene;
class   WDViewer;

class   WDGeometryMgr;
class   WDBMBase;
class   WDBMCatalog;
class   WDBMDesign;
class   WDBMAdmin;

class   IApiDelegate;
class   NodeListMgr;
class   WDBMUser;
class   WDBMUserMgr;
class   WDBMProject;
class   WDBMProjectMgr;
#ifdef WD_USE_PYTHON
class   WDPython;
class   WDPythonOStream;
#endif
class   WDBlockingTask;

class   WDCorePrivate;

/**
* @brief 核心接口对象
*/
class WD_API WDCore
{
public:
    /**
     * @brief 视图对象智能指针
     */
    using ViewerSPtr = std::shared_ptr<WDViewer>;
    /**
     * @brief 场景对象智能指针
     */
    using SceneSPtr = std::shared_ptr<WDScene>;
public:
    /**
     * @brief 孔洞绘制配置
     *  first: 是否开启孔洞
     *  second: 开启孔洞时, 所有带圆弧的基本体圆弧分段使用的圆弧公差(LOD)
    */
    using HolesDrawn = std::pair<bool, float>;
    /**
     * @brief 系统信息
     */
    struct SystemInfo
    {
        // 操作系统名称
        std::string os_name;
        // 操作系统版本
        std::string os_version;
        // 操作系统位数
        std::string os_bit;
        
        // CPU制造商
        std::string cpu_manufacturers;
        // CPU型号
        std::string cpu_model;
        // CPU核心数
        int         cpu_coreCount;
        // CPU主频
        std::string cpu_mainFrequency;

        // GPU制造商
        std::string gpu_manufacturers;
        // GPU型号
        std::string gpu_model;
        // GPU显存
        std::string gpu_videoMemory;

        // 内存大小
        std::string mem_size;
        // 内存速度
        std::string mem_speed;
        // 内存类型
        std::string mem_model;

        // 硬盘类型
        std::string disk_model;
        // 硬盘容量
        std::string disk_capacity;
        // 硬盘使用情况
        std::string disk_usage;

        // 主板制造商
        std::string mab_manufacturers;
        // 主板型号
        std::string mab_model;

        // 网络适配器的制造商
        std::string nic_manufacturers;
        // 网络适配器的型号
        std::string nic_model;
        // 网络适配器的MAC地址
        std::string nic_mac;

        // 声卡制造商
        std::string sc_manufacturers;
        // 声卡型号
        std::string sc_model;

        // 显示器的制造商
        std::string moni_manufacturers;
        // 显示器的型号
        std::string moni_model;
        // 显示器的分辨率
        std::string moni_resolution;
    };
public:
    /**
     * @brief 构造
     * @param exeDirPath 可执行程序路径
     * @param dataPath 配置数据路径,如果指定为"",则默认拼接为 exeDirPath + "/../data/"
    */
    WDCore(const std::string& exeDirPath, const std::string& dataDirPath = "");
    WDCore(const WDCore& right) = delete;
    WDCore(WDCore&& right) = delete;
    WDCore& operator=(const WDCore& right) = delete;
    WDCore& operator=(WDCore&& right) = delete;
    ~WDCore();
public:
    /**
     * @brief 销毁core对象,释放core对象中的资源
    */
    void destroy();
public:
    /**
    * @brief 获取可执行程序路径
    */
    const char* exeDirPath() const;
    /**
    * @brief 获取资源路径
    */
    const char* dataDirPath() const;
    /**
    * @brief 获取日志文件路径名称
    */
    const char* logFileName() const;
public:
    /**
    *   @brief 配置接口
    */
    WDConfig& cfg();
    /**
    * @brief 获取信息管理对象
    */
    WDMessage& message();

    /**
    * @brief 获取任务模块
    */
    WDTaskSys& taskSys();
    /**
    *  @brief 定时器系统
    */
    WDTimerSys& timerSys();
    /**
    *   @brief 对象管理者，通过该接口可以查询系统中缓存的对象
    */
    WDObjectsMgr& objectsMgr();
    /**
    *   @brief 对象管理者，通过该接口可以查询系统中缓存的对象
    */
    const WDObjectsMgr& objectsMgr() const;
    /**
    * @brief 对象创建管理
    */
    WDObjectCreator& objectCreator();
    /**
    * @brief 获取扩展管理对象
    */
    WDExtensionMgr& extensionMgr();
    /**
    * @brief 获取几何体管理对象
    */
    WDGeometryMgr& geometryMgr();
    /**
    * @brief 获取节点树
    */
    WDNodeTree& nodeTree();
    /**
     * @brief 获取节点列表管理
     */
    NodeListMgr& nodeListMgr();
    /**
     * @brief 获取默认视图对象
    */
    WDViewer& viewer();
    /**
     * @brief 获取默认场景对象
     */
    WDScene& scene();
    /**
     * @brief 触发当前所有已激活视图场景的强制重新更新
     */
    void sceneForceUpdate();
    /**
     * @brief 触发当前所有已激活视图的重绘
    */
    void needRepaint();
    /**
    * @brief 获取 undo/redo 栈对象
    */
    WDUndoStack& undoStack();
    /**
    * @brief 获取 undo/redo 栈对象
    */
    const WDUndoStack& undoStack() const;
    /**
    * @brief 设置 undo/redo 栈对象
    *   这里系统启动时会默认设置一个undo/redo栈，因此这个接口不建议调用
    */
    void setUndoStack(WDUndoStack* pUndoStack);
    /**
     * @brief 获取当前的模块名称
     */
    const std::string& moduleType() const;
    /**
     * @brief 设置当前的模块名称
     */
    void setModuleType(const std::string& moduleType);
public:
    /**
    * @brief 获取元件模块对象
    */
    WDBMCatalog& getBMCatalog();
    /**
    * @brief 获取设计模块对象
    */
    WDBMDesign& getBMDesign();
    /**
    * @brief 获取管理模块对象
    */
    WDBMAdmin& getBMAdmin();
    /**
     * @brief 添加业务模块
     * @param pBase 业务模块基类指针
     * @return 是否添加成功
     *  1. 如果业务模块指针为nullptr,则添加失败
     *  2. 如果业务模块对象的名称为空，则添加失败
     *  3. 如果相同名称的业务模块对象已被添加，则添加失败
    */
    bool addBM(WDBMBase* pBase);
    /**
     * @brief 根据名称获取模块对象
     * @param name 业务模块对象名称
     * @return 如果对应名称的模块对象存在，则返回对应指针，否则返回nullptr
    */
    WDBMBase* getBMBase(const std::string_view& name) const;
    /**
     * @brief 获取当前模块对象
     * @return 如果对应名称的模块对象存在，则返回对应指针，否则返回nullptr
    */
    WDBMBase* currentBM() const;
    /**
     * @brief 根据名称获取模块对象
     * @param name 业务模块对象名称
     * @return 如果对应名称的模块对象存在，则返回对应指针，否则返回nullptr
    */
    template <typename TBM>
    TBM* getBM(const std::string_view& name) const
    {
        static_assert(std::is_base_of_v<WDBMBase, TBM>, "TBM is not a subclass of WDBMBase!");
        auto pBase = this->getBMBase(name);
        if (pBase == nullptr)
            return nullptr;
        return dynamic_cast<TBM*>(pBase);
    }
    /**
     * @brief 移除业务模块对象
     * @param name 业务模块对象名称
     *  该接口将自动调用delete，所以请谨慎移除
    */
    void removeBM(const std::string_view& name);
public:
    /**
     * @brief 获取孔洞配置参数
     *  默认值: 
     *      first(是否开启): false 
     *      second(圆弧公差): 10.0f  注意:不开启孔洞绘制时，圆弧公差不会生效
    */
    HolesDrawn& holesDrawn();
    /**
     * @brief 获取孔洞配置参数
     *  默认值:
     *      first(是否开启): false
     *      second(圆弧公差): 10.0f  注意:不开启孔洞绘制时，圆弧公差不会生效
    */
    const HolesDrawn& holesDrawn() const;
    /**
    *  @brief 获取API代理
    */
    IApiDelegate* apiDelegate();
    /**
     * @brief 设置API代理
     */
    bool setApiDelegate(IApiDelegate* ad);
    /**
     * @brief 获取阻塞式任务对象
    */
    WDBlockingTask* blockingTask();
    /**
     * @brief 设置阻塞式任务对象
    */
    void setBlockingTask(WDBlockingTask* pTask);
public:
    /**
    * @brief 获取项目管理
    */
    WDBMProjectMgr& projectMgr();
    /**
    * @brief 获取用户管理
    */
    WDBMUserMgr& userMgr();
    /**
    * @brief 获取当前的项目
    */
    const WDBMProject& project() const;
    /**
    * @brief 获取当前的用户
    */
    const WDBMUser& user() const;

#ifdef WD_USE_PYTHON
    /**
    * @brief 获取python模块
    */
    WDPython& wizDesignerPython();
    /**
    * @brief 获取python输出模块
    */
    WDPythonOStream& wizDesignerPythonOStream();
#endif
    /**
     * @brief 获取系统信息
     */
    const SystemInfo& systemInfo() const;
    /**
     * @brief 获取系统信息
     */
    SystemInfo& systemInfo();
private:
    WDCorePrivate* _p;
};

/**
* @brief 获取 WDCore对象的全局函数
*/
WD_API WDCore& Core();

WD_NAMESPACE_END
