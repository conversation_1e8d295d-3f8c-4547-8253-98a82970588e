#pragma once

#include "WDNodeTRSTransform.h"

WD_NAMESPACE_BEGIN


inline const DVec3& WDNodeTRSTransform::localTranslation() const
{
    return _lT;
}
inline void WDNodeTRSTransform::setLocalTranslation(const DVec3& vec)
{
    _lT = vec;
}
inline const DQuat& WDNodeTRSTransform::localRotation() const
{
    return _lR;
}
inline void WDNodeTRSTransform::setLocalRotation(const DQuat& quat)
{
    _lR = quat;
}
inline void WDNodeTRSTransform::setLocalRotation(const DMat4& rotMat)
{
    this->setLocalRotation(DMat4::ToQuat(rotMat));
}
inline void WDNodeTRSTransform::setLocalRotation(const DMat3& rotMat)
{
    this->setLocalRotation(DMat3::ToQuat(rotMat));
}
inline void WDNodeTRSTransform::setLocalRotation(const DEuler& e)
{
    this->setLocalRotation(DEuler::ToQuat(e));
}
inline const DVec3& WDNodeTRSTransform::localScaling() const
{
    return _lS;
}
inline void WDNodeTRSTransform::setLocalScaling(const DVec3& scaling)
{
    _lS = scaling;
}

inline void WDNodeTRSTransform::setGlobalRotation(const DMat4& rotMat, const WDNodeTransform* pParent)
{
    this->setGlobalRotation(DMat4::ToQuat(rotMat), pParent);
}
inline void WDNodeTRSTransform::setGlobalRotation(const DMat3& rotMat, const WDNodeTransform* pParent)
{
    this->setGlobalRotation(DMat3::ToQuat(rotMat), pParent);
}
inline void WDNodeTRSTransform::setGlobalRotation(const DEuler& e, const WDNodeTransform* pParent)
{
    this->setGlobalRotation(DEuler::ToQuat(e), pParent);
}

WD_NAMESPACE_END