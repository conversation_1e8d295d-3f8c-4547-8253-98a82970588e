#pragma once

#include "WDNodeTransform.h"

WD_NAMESPACE_BEGIN

/**
* @brief 节点业务模块数据对象基类
*/
class WD_API WDNodeTRSTransform: public WDNodeTransform
{
private:
    // 局部位置偏移
    DVec3 _lT;
    // 局部旋转
    DQuat _lR;
    // 局部缩放
    DVec3 _lS;
public:
    WDNodeTRSTransform();
    WDNodeTRSTransform(const WDNodeTRSTransform& right) = default;
    WDNodeTRSTransform(WDNodeTRSTransform&& right) = default;
    WDNodeTRSTransform& operator=(const WDNodeTRSTransform& right) = default;
    WDNodeTRSTransform& operator=(WDNodeTRSTransform&& right) = default;
    virtual ~WDNodeTRSTransform();
public:
    virtual bool isEmpty() const;
    virtual DMat4 localTransform(const WDNode& node) const;
public:
    /**
    * @brief 获取 从父节点到节点的位置变换
    * @return 从父节点到节点的位置变换
    */
    inline const DVec3& localTranslation() const;
    /**
    * @brief 设置 从父节点到节点的位置变换
    *  @param vec 从父节点到节点的位置变换
    */
    inline void setLocalTranslation(const DVec3& vec);
    /**
    * @brief 获取 从父节点到节点的旋转变换
    * @return 从父节点到节点的旋转变换
    */
    inline const DQuat& localRotation() const;
    /**
    * @brief 设置 从父节点到节点的旋转变换
    *  @param Quat 从父节点到节点的旋转变换
    */
    inline void setLocalRotation(const DQuat& quat);
    /**
    * @brief 设置 从父节点到节点的旋转变换
    *  @param rotMat 从父节点到节点的旋转变换
    */
    inline void setLocalRotation(const DMat4& rotMat);
    /**
    * @brief 设置 从父节点到节点的旋转变换
    *  @param rotMat 从父节点到节点的旋转变换
    */
    inline void setLocalRotation(const DMat3& rotMat);
    /**
    * @brief 设置 从父节点到节点的旋转变换
    *  @param e 从父节点到节点的旋转变换
    */
    inline void setLocalRotation(const DEuler& e);
    /**
    * @brief 获取 从父节点到节点的缩放变换
    *  @return 从父节点到节点的缩放变换
    */
    inline const DVec3& localScaling() const;
    /**
    * @brief 设置 从父节点到节点的缩放变换
    *  @param scaling 从父节点到节点的缩放变换
    */
    inline void setLocalScaling(const DVec3& scaling);

    /**
    * @brief 设置 从世界到节点的位置变换
    *  @param vec 从世界到节点的位置变换
    */
    void setGlobalTranslation(const DVec3& vec, const WDNodeTransform* pParent);
    /**
    * @brief 设置 从世界到节点的旋转变换
    *  @param Quat 从世界到节点的旋转变换
    */
    void setGlobalRotation(const DQuat& quat, const WDNodeTransform* pParent);
    /**
    * @brief 设置 从世界到节点的旋转变换
    *  @param rotMat 从世界到节点的旋转变换
    */
    inline void setGlobalRotation(const DMat4& rotMat, const WDNodeTransform* pParent);
    /**
    * @brief 设置 从世界到节点的旋转变换
    *  @param rotMat 从世界到节点的旋转变换
    */
    inline void setGlobalRotation(const DMat3& rotMat, const WDNodeTransform* pParent);
    /**
    * @brief 设置 从世界到节点的旋转变换
    *  @param e 从世界到节点的旋转变换
    */
    inline void setGlobalRotation(const DEuler& e, const WDNodeTransform* pParent);
    /**
    * @brief 设置 从世界到节点的缩放变换
    *  @param s 从世界到节点的缩放变换
    */
    void setGlobalScaling(const DVec3& s, const WDNodeTransform* pParent);
    /**
    * @brief 设置 从世界到节点的变换矩阵
    * @param trans 从世界到节点的变换矩阵
    */
    void setGlobalTransform(const DMat4& trans, const WDNodeTransform* pParent);
public:
    virtual void copy(const WDNodeTransform& right) override;
    virtual WDNodeTransform* clone() override;
protected:
    virtual void moveP(WDNode& node, const DVec3& offset) override;
    virtual void rotateP(WDNode& node, const DVec3& axis, double angle) override;
    virtual void rotateP(WDNode& node, const DVec3& axis, double angle, const DVec3& center) override;
    virtual void mirrorP(WDNode& node, const DVec3& normal, const DVec3& center) override;
    virtual void scaleP(WDNode& node, const DVec3& scaling) override;
};

WD_NAMESPACE_END

#include "WDNodeTRSTransform.inl"