#pragma     once

#include    "../common/WDObject.h"
#include    "../common/WDFlags.h"
#include    "../common/WDOperationList.h"
#include    "../businessModule/typeMgr/WDBMAttrValue.h"

#include    "../graphable/WDKeyPoint.h"
#include    "../graphable/WDPLine.h"

#include    "WDNodeObserver.h"
#include    "WDNodeTransform.h"

WD_NAMESPACE_BEGIN

class WDBMBase;
class WDBMAttrDesc;
class WDBMTypeDesc;
class WDBDBase;

class WDMaterial;

WD_DECL_CLASS_UUID(WDNode,"7F52CBB0-FE15-42A2-9FE4-4E45F37B2A5C");

/**
* @brief 节点对象
*   !注意:
*       所有的节点对象需要以智能指针的方式创建和使用
*       所有继承节点的子类对象均需要以智能指针的方式创建和使用
*/
class WD_API WDNode : public WDObject
{
    WD_DECL_OBJECT(WDNode)
public:
    /**
    * @brief 节点列表
    */
    using   Nodes       =   std::vector<SharedPtr>;
    /**
    * @brief 节点列表 迭代器
    */
    using   NodesItr    =   Nodes::iterator;
    /**
    * @brief 节点列表 const 迭代器
    */
    using   NodesConstItr = Nodes::const_iterator;
    /**
    * @brief 节点观察者列表
    */
    using   Observers   =   WD::WDOperationList<WDNodeObserver*>;
    /**
    * @brief 节点观察者列表 迭代器
    */
    using   ObserversItr = Observers::iterator;
    /**
    * @brief 节点观察者列表 const 迭代器
    */
    using   ObserversConstItr   =   Observers::const_iterator;
    /**
     * @brief 材质对象指针
     */
    using   MaterialSPtr = std::shared_ptr<WDMaterial>;
public:
    /**
    * @brief 节点标志
    */
    enum Flag
    {
        // 无
        F_None          =  0,

        // 更新标记
        F_Update        =  (1 << 0),

        // 可见标记(用于控制是否绘制该节点)
        F_Visible       =  (1 << 1),
        // 锁定标记
        F_Lock          =  (1 << 2),
        // Item 隐藏标记(用于控制模型树item显隐, 当有该标记时，节点和其子节点不会显示在模型树上)
        F_ItemHidden    =  (1 << 3),

        // 线框模式标记, 如果当前节点实体有线框，则以以线框模式绘制，否则将不显示当前实体
        F_WireFrame         =   (1 << 6),
        // 树上的选中标记, 当节点在树上选中时, 将带有此标记, 节点将通过高亮颜色来绘制, 最高优先级
        F_TreeSelected      =   (1 << 7),
        // 选中标记, 当节点在视图(或场景)中被点选或框选且选中时，将带有此标记，节点将通过高亮线框来绘制, 最高优先级
        F_Selected          =   (1 << 8),
        // 自定义颜色高亮标记, 节点将通过自定义的颜色(setCustomHightlightColor)来显示, 优先级仅次于 Flag_Selected
        F_CustomHightlight  =   (1 << 9),
        // 激活标记, 节点将以激活颜色进行绘制, 优先级仅次于 Flag_CustomHightlight
        F_Active            =   (1 << 10),
        // 高亮标记, 节点将以高亮颜色进行绘制, 优先级仅次于 Flag_Active
        F_Highlight         =   (1 << 11),
        
        // 被编辑标记，进入项目后，节点新建或属性被修改之后，需要设置该标记，直到保存时，标记消失
        F_Editted           =  (1 << 14),
        // 进入项目后，节点新建或属性被修改后，需要设置该标记，直到退出项目或协同模式同步数据时，标记消失, 拥有该标记的节点在模型树上的名称会加粗显示
        F_EdittedStatus     =  (1 << 15),

        // 剖切标记
        F_Clip       =  (1 << 19),
        // 被添加到场景的标记
        F_InTheScene =  (1 << 20),
        // 不在可视范围内(一般在相机视口更新后自动计算来填充该标志)
        F_OutView    =  (1 << 21),
        // global变换中是否带有镜像
        F_Mirror     =  (1 << 22),

        // 是否产生阴影
        F_ShadowCast =  (1 << 27),
        // 是否接收阴影
        F_ShadowRecv =  (1 << 28),
        // 是否产生镜面
        F_MirrowCast =  (1 << 29),
        // 是否接收镜面
        F_MirrowRecv =  (1 << 30),
        // 参与Bloom计算
        F_Bloom      =  (1 << 31),
    };
    using Flags = WDFlags<Flag, unsigned>;
public:
    WDNode();
    explicit WDNode(const std::string& name);
    WDNode(const WDNode& right) = delete;
    WDNode(WDNode&& right) = delete;
    WDNode& operator=(const WDNode& right) = delete;
    WDNode& operator=(WDNode&& right) = delete;
    virtual ~WDNode();
public:
    /**
    * @brief 重写获取对象名称
    */
    virtual std::string name() const override final;
public:
    /**
    * @brief 获取节点标志
    */
    inline Flags flags() const;
    /**
    * @brief 设置节点标志
    */
    inline void setFlags(Flags flags);
    /**
    * @brief 获取观察者列表
    */
    inline Observers& observers();
    /**
    * @brief 获取观察者列表
    */
    inline const Observers& observers() const;
public:
    /**
     * @brief 设置节点类型
     *  如果想指定节点的类型，需要设置有效的模块对象指针, 否则类型将设置失败
     * @param pBase 指定节点所属的模块对象
     * @param typeName 类型名称
     * @return 是否设置成功
     *  当模块对象(pBMBase)无效或类型名称(typeName)无效，或模块对象(pBMBase)中未注册typeName时
    */
    bool setType(const WDBMBase& bmBase, const std::string_view& typeName);
    /**
     * @brief 获取节点类型名称
    */
    std::string_view type() const;
    /**
     * @brief 判断当前节点的类型是否是指定类型
     * @param type 类型字符串
     * @return 如果当前节点的类型与指定的类型一致，则返回true，否则返回false
    */
    inline bool isType(const std::string_view& type) const;
    /**
     * @brief 判断当前节点的类型是否是指定类型列表中的其中一种类型
     * @tparam ...Args 类型模板列表
     * @param ...types 类型列表
     * @return 如果当前节点的类型是types中的其中一个则返回true
    */
    template <typename ...Args>
    inline bool isAnyOfType(Args&&... types) const;
    /**
     * @brief 获取节点所属的模块对象
    */
    WDBMBase* getBMBase() const;
    /**
     * @brief 获取节点所属的模块对象并转换到模块子类
    */
    template <typename BMSub>
    inline BMSub* getBMSub() const;
    /**
     * @brief 创建业务(属性)数据对象
    */
    WDBDBase* createBD();
    /**
     * @brief 获取业务(属性)数据
    */
    inline WDBDBase* getBDBase() const;
    /**
     * @brief 销毁业务(属性)数据对象
    */
    void destroyBD();
    /**
     * @brief 获取类型描述对象
    */
    inline const WDBMTypeDesc* getTypeDesc() const;
    /**
     * @brief 指定属性名称，获取属性描述对象
     */
    const WDBMAttrDesc* getAttrDesc(const std::string_view& name) const;
    /**
     * @brief 设置对应名称的属性
     * @param name 属性名称
     * @param value 属性值
     * @return 是否设置成功
    */
    bool setAttribute(const std::string_view& name, const WDBMAttrValue& value);
    /**
     * @brief 获取对应名称的属性
     * @param name 属性名称
     * @return 属性值， 如果获取失败(不存在对应名称属性时)，属性值为 Null
    */
    WDBMAttrValue getAttribute(const std::string_view& name) const;
    /**
     * @brief 获取对应名称的属性值的const引用
     * @param name 属性名称
     * @return 属性值的const引用，如果获取失败(不存在对应名称属性时)，属性值为 Null
    */
    WDBMAttrValueCRef getAttributeCRef(const std::string_view& name) const;
public:
    /**
     * @brief 协同端，uuid 与 int64 的映射id
     */
    inline void setRemoteId(int64_t remtoeId)
    {
        _remoteId = remtoeId;
    }
    /**
     * @brief 协同端，uuid 与 int64 的映射id
     */
    inline int64_t getRemoteId()
    {
        return _remoteId;
    }
    /**
     */
    inline void setTraceId(int64_t traceId)
    {
        _traceId = traceId;
    }
    /**
     * @brief 协同端，uuid 与 int64 的映射id
     */
    inline int64_t getTraceId()
    {
        return _traceId;
    }

    /**
     * @brief 设置节点属性加载信息的页Id
    */
    inline void setPageId(ushort pageId)
    {
        _pageId = pageId;
    }
    /**
     * @brief 获取页Id
    */
    inline ushort pageId() const
    {
        return _pageId;
    }
    /**
     * @brief 设置属性在页中的地址
    */
    inline void setAttrAddr(uint addr)
    {
        _attrAddr = addr;
    }
    /**
     * @brief 获取属性在页中的地址
    */
    inline uint attrAddr() const
    {
        return _attrAddr;
    }
public:
    /**
     * @brief 设置动态属性
     *  如果对应名称的动态属性存在，则覆盖
     *  如果对应名称的动态属性不存在，则添加该属性
     * @param name 属性名称
     * @param value 属性值
    */
    void setDynamicAttribute(const std::string& name, const std::string& value);
    /**
     * @brief 根据名称获取动态属性值
     * @return 如果对应名称的动态属性存在，则返回值，否则返回空字符串
    */
    std::string dynamicAttribute(const std::string& name) const;
    /**
     * @brief 对应名称的动态属性是否存在
    */
    bool containsDynamicAttribute(const std::string& name) const;
    /**
     * @brief 移除指定名称的动态属性
     * @param name 属性名称
    */
    void removeDynamicAttribute(const std::string& name);
    /**
     * @brief 清除所有动态属性
    */
    void clearDynamicAttributes();
    /**
     * @brief 动态属性
     *  first: 属性名称
     *  second: 属性值
    */
    using DynamicAttr = std::pair<std::string, std::string>;
    /**
     * @brief 动态属性列表
    */
    using DynamicAttrs = std::vector<DynamicAttr>;
    /**
     * @brief 获取已添加的动态属性列表(所有动态属性), 其中:
     *  first: 动态属性名称
     *  second: 动态属性值
    */
    const DynamicAttrs& dynamicAttributes() const;
public:
    /**
    * @brief 获取父节点
    */
    inline WDNode::SharedPtr parent() const;
    /**
    * @brief 设置父节点
    */
    void setParent(WDNode::SharedPtr pParent);
    /**
    * @brief 添加子节点(添加到末尾)
    */
    void addChild(WDNode::SharedPtr pNode);
    /**
    * @brief 插入节点到某个节点之前
    * @param pNode 要插入的节点
    * @param pNextNode 表示 pNode 需要插入到 pNextNode 之前，如果 pNextNode= nullptr, 则添加 pNode 到最后
    */
    void insertChild(WDNode::SharedPtr pNode, WDNode::SharedPtr pNextNode);
    /**
    * @brief 移除子节点
    */
    void removeChild(WDNode::SharedPtr pNode);
    /**
    * @brief 子节点个数
    */
    inline size_t childCount() const;
    /**
    * @brief 获取子节点
    */
    inline WDNode::SharedPtr childAt(size_t index) const;
    /**
    * @brief 获取子节点列表
    */
    inline const Nodes& children() const;
    /**
    * @brief 当前节点是否是pParent的子节点
    */
    inline bool isChild(const WDNode& parent) const;
    /**
    * @brief 当前节点是否是pChild的父节点
    */
    inline bool isParent(const WDNode& child) const;
    /**
    * @brief 当前节点是否是pAncestor的祖先节点
    */
    bool isAncestor(const WDNode& ancestor) const;
    /**
    * @brief 当前节点是否是pDescendant的后代节点
    */
    inline bool isDescendant(const WDNode& descendant) const;
    /**
     * @brief 获取当前节点的前一个兄弟节点
    */
    WDNode::SharedPtr prevBrother() const;
    /**
     * @brief 获取当前节点的后一个兄弟节点
    */
    WDNode::SharedPtr nextBrother() const;
    /**
     * @brief 设置基本颜色
    */
    inline void setBasicColor(const Color& color);
    /**
     * @brief 获取基本颜色
    */
    inline const Color& basicColor() const;
    /**
     * @brief 设置自定义高亮颜色
    */
    inline void setCustomHighlightColor(const Color& color);
    /**
     * @brief 获取自定义高亮颜色
    */
    inline const Color& customHighlightColor() const;
    /**
     * @brief 设置材质
    */
    void setMaterial(MaterialSPtr pMaterial);
    /**
     * @brief 获取材质
    */
    MaterialSPtr material() const;
    /**
     * @brief 获取节点深度
    */
    int depth() const;
    /**
     * @brief 子节点重新排序
     * @param func 回调, 子节点列表为空时，不会触发回调
     *  - children 当前节点的子节点列表
     *  - sender 当前节点对象
     * 注意,回调函数中不允许修改子节点列表的内容,只能修改子节点列表的顺序!!!
     * 如果修改了节点列表则会修改失败
     * 如果节点顺序没有变化则直接返回true
    */
    bool reorder(std::function<Nodes(const Nodes& children, WDNode& sender)> func);
public:
    /**
     * @brief 获取节点变换对象指针
    */
    virtual WDNodeTransform* transformObject();
    /**
     * @brief 获取节点变换对象指针
    */
    virtual const WDNodeTransform* transformObject() const;
public:
    /**
    * @brief 获取包围盒数据
    */
    inline const DAabb3& aabb() const;

    /**
    * @brief 获取 从世界到节点的变换矩阵
    */
    inline const DMat4& globalTransform() const;
    /**
    * @brief 获取 从世界到节点的RS变换
    */
    inline DMat4 globalRSTransform() const;

    /**
    * @brief 获取 从世界到节点的位置变换
    */
    inline DVec3 globalTranslation() const;
    /**
    * @brief 获取 从世界到节点的旋转变换
    */
    inline DQuat globalRotation() const;
    /**
    * @brief 获取 从世界到节点的缩放变换
    */
    inline DVec3 globalScaling() const;

    /**
    * @brief 获取 从父节点到节点的变换矩阵
    */
    inline DMat4 localTransform() const;
    /**
    * @brief 获取 从父节点到节点的RS变换矩阵
    */
    inline DMat4 localRSTransform() const;

    /**
    * @brief 移动节点
    * @param offset 移动的偏移量,世界坐标(世界坐标系)
    */
    void move(const DVec3& offset);
    /**
    * @brief 旋转节点
    *   自转
    * @param axis 旋转轴, 世界坐标(世界坐标系)
    * @param angle 旋转角度
    */
    void rotate(const DVec3& axis, double angle);
    /**
    * @brief 指定中心点,旋转节点
    *   绕着指定的中心点公转
    * @param axis 旋转轴, 世界坐标(世界坐标系)
    * @param angle 旋转角度
    * @param center 旋转中心点, 世界坐标(世界坐标系)
    */
    void rotate(const DVec3& axis, double angle, const DVec3& center);
    /**
     * @brief 镜像节点
     * @param normal 镜像平面法线, 世界坐标(世界坐标系)
     * @param center 镜像平面上的任意一点, 世界坐标(世界坐标系)
     */
    void mirror(const DVec3& normal, const DVec3& center);
    /**
    * @brief 缩放节点
    * @param scaling 缩放比例, 局部坐标(父坐标系)
    */
    void scale(const DVec3& scaling);
    /**
    * @brief 移动该节点本身,子节点不会发生改变
    * @param offset 移动偏移量,世界坐标
    */
    void moveSelf(const DVec3& offset);
    /**
    * @brief 旋转该节点本身,子节点不会发生改变
    *   自转
    * @param axis 旋转轴,世界坐标(世界坐标系)
    * @param angle 旋转角
    */
    void rotateSelf(const DVec3& axis, double angle);
    /**
    * @brief 旋转该节点本身,子节点不会发生改变
    *   绕着指定的中心点公转
    * @param axis 旋转轴,世界坐标(世界坐标系)
    * @param angle 旋转角
    */
    void rotateSelf(const DVec3& axis, double angle, const DVec3& center);
public:
    /**
    * @brief 更新节点
    *   当某个节点的(位置/缩放/旋转/其他影响包围盒的数据)被修改之后
    *   必须手动使用此方法(建议在渲染线程调用)，同时会更新该节点的父节点包围盒
    *   如果涉及到批量的节点更新，不建议重复调用此方法，而是获取到修改后节点的公共父节点，再调用一次公共父节点的update()

    *   当节点具有 FLAG_UPDATE 标记时,该方法才会生效
    *   1.更新节点以及子节点矩阵(递归所有子节点)
    *   2.触发节点以及子节点的模型生成(递归所有子节点)
    *   2.更新节点以及子节点的aabb包围盒(递归所有子节点)
    *   3.如果当前节点具有父节点，则更新其对应父节点的包围盒(遍历到根节点)
    *   4.通知更新前和更新后观察者(所有被更新的节点)
    * @param bForceUpdate 是否进行强制更新
    *   如果 bForceUpdate 设置为true,将不会检查节点是否具有FLAG_UPDATE 标记,直接更新当前节点以及其所有子孙节点
    */
    void    update(bool bForceUpdate = false);
    /**
     * @brief 触发更新
     *  当节点具有 FLAG_UPDATE 标记时,该方法才会生效
     *  内部实际还是调用了update(bool bForceUpdate)，但是可能会有不同的触发规则
     *  比如某些节点的属性改变之后，可能还会影响到其父节点的模型数据，因此更新时需要触发父节点的更新
     *  所以大多情况可以使用触发更新来保证节点属性改变之后，所有相关节点都能够正确地刷新模型信息
    */
    void    triggerUpdate(bool bForceUpdate = false);
    /**
    * @brief 只会更新当前节点的矩阵(不会递归子节点)
    *   当某个节点的变换属性(位置/缩放/旋转)被修改之后
    *   可以手动使用此方法来只保证当前节点 global Transform 的更新
    *   不会检测FLAG_UPDATE标志，每次调用总是重新计算
    */
    void    updateTransform();
    /**
     * @brief 只更新当前节点的模型数据(不会递归子节点)
     *  当某个节点的模型数据相关属性被修改之后
     *  可以手动使用此方法来保证当前节点 模型数据 的更新
     *  不会检测FLAG_UPDATE标志，每次调用总是重新计算
    */
    void    updateModel();
public:
    /**
     * @brief 获取点集列表, 这里不包含P0点
     * @return 如果节点没有关键点时，返回nullptr, 否则返回关键点列表
     */
    const WDKeyPoints* keyPoints() const;
    /**
     * @brief 从点集列表中获取指定number的关键点
     * @param number 关键点number
     * @return 如果不存在指定number的关键点，则返回nullptr,否则返回关键点指针
     */
    const WDKeyPoint* keyPoint(int number) const;
    /**
     * @brief 获取PLine线列表
     * @return 如果节点没有PLine线时，返回nullptr, 否则PLine线列表
     */
    const WDPLines* pLines() const;
    /**
     * @brief 从PLine线列表中获取指定key的PLine线
     * @param key PLine线key, 名称不区分大小写, 但一般指定为大写字母
     * @return 如果不存在指定key的PLine线，则返回nullptr,否则返回PLine线指针
     */
    const WDPLine* pLine(const std::string_view& key) const;
public:
    /**
    * @brief 遍历其所有子节点(不包含node自身)
    * @param node 节点对象，将遍历该节点的所有子节点
    * @param callable 可调用对象
    *   具体请参考std::invoke
    * @param args 可调用对象参数
    */
    template <typename Callable, typename ...Args>
    static void ChildrenTraversalHelpter(WDNode& node
        , Callable&& callable
        , Args&& ...args);
    /**
    * @brief 遍历其所有子节点(不包含node自身)
    * @param node 节点对象，将遍历该节点的所有子节点
    * @param callable 可调用对象
    *   具体请参考std::invoke
    * @param args 可调用对象参数
    * @return 可调用对象返回值为bool类型，并且当调用对象返回true时，将跳出遍历
    */
    template <typename Callable, typename ...Args>
    static bool ChildrenTraversalHelpterR(WDNode& node
        , Callable&& callable
        , Args&& ...args);
    /**
    * @brief 递归所有子孙节点(包含node自身),优先回调根节点
    * @param node 节点对象，将遍历该节点以及其所有子孙节点
    * @param callable 可调用对象
    *   具体请参考std::invoke
    * @param args 可调用对象参数
    */
    template<typename Callable, typename ...Args>
    static void RecursionHelpter(WDNode& node
        , Callable&& callable
        , Args&& ...args);
    /**
    * @brief 递归所有子孙节点(包含node自身),优先回调根节点
    * @param node 节点对象，将遍历该节点以及其所有子孙节点
    * @param callable 可调用对象
    *   具体请参考std::invoke
    * @param args 可调用对象参数
    * @return 可调用对象返回值为bool类型，并且当调用对象返回true时，将跳出递归
    */
    template<typename Callable, typename ...Args>
    static bool RecursionHelpterR(WDNode& node
        , Callable&& callable
        , Args&& ...args);
    /**
    * @brief 递归所有子孙节点(包含node自身),优先回调叶子节点,再依次向上递归父节点
    * @param node 节点对象，将遍历该节点以及其所有子孙节点
    * @param callable 可调用对象
    *   具体请参考std::invoke
    * @param args 可调用对象参数
    */
    template<typename Callable, typename ...Args>
    static void RecursionHelpterFirstLeaf(WDNode& node
        , Callable&& callable
        , Args&& ...args);
    /**
    * @brief 递归所有子孙节点(包含node自身),优先回调叶子节点,再依次向上递归父节点
    * @param node 节点对象，将遍历该节点以及其所有子孙节点
    * @param callable 可调用对象
    *   具体请参考std::invoke
    * @param args 可调用对象参数
    * @return 可调用对象返回值为bool类型，并且当调用对象返回true时，将跳出递归
    */
    template<typename Callable, typename ...Args>
    static bool RecursionHelpterFirstLeafR(WDNode& node
        , Callable&& callable
        , Args&& ...args);
public:
    /**
    * @brief 从源对象拷贝数据到当前对象
    *   子类可重写
    *   只拷贝当前节点的数据，不会拷贝子节点的数据
    */
    virtual void copy(const WDObject* pSrcObject) override;
    /**
    * @brief 使用当前对象克隆出一个对象
    *   子类可重写
    *   克隆当前节点的数据，不会克隆子节点的数据
    */
    virtual WDObject::SharedPtr clone() const override;
public:
    /**
     * @brief 发送属性改变通知
    */
    void    sendAttributeValueChanged(const std::string_view& name
        , const WDBMAttrValue& currValue
        , const WDBMAttrValue& prevValue);
    /**
     * @brief 发送即将销毁通知
    */
    void    sendDestroy();
protected:
    /**
    * @brief 节点的原始包围盒,由子类重写
    */
    virtual Aabb3 aabbSrc() const;
    /**
    * @brief 节点更新之前通知，由子类重写
    */
    virtual void onNodeUpdateBefore();
    /**
    * @brief 节点更新之后通知，由子类重写
    */
    virtual void onNodeUpdateAfter();
private:
    /**
    * @brief 递归更新节点信息
    *   这里不会判断节点的 FLAG_UPDATE 标记而是强制更新
    *   包括矩阵信息,包围盒信息以及物理信息,并将每个节点的更新通知给观察者
    */
    void updateDataRecursion();
    /**
    * @brief 更新节点矩阵数据
    */
    void updateMatrixData();
    /**
    * @brief 使用当前节点的 global Transform变换aabbSrc的结果
    */
    DAabb3 transformdAabbSrc() const;
    /**
    * @brief 更新父节点的包围盒
    *   当子节点的包围盒发生改变之后，需要使用子节点当前的包围盒数据去同步更新父节点包围盒
    * @param 当前节点更新之前的包围盒，用于差异比较
    */
    void updateParentAabbData(const DAabb3& aabbBeforeUpdate);
private:
    // 协同端，uuid 与 int64 的映射id
    int64_t _remoteId;
    // 协同端 追踪id
    int64_t _traceId;
    // 父节点
    WDNode::WeakPtr _parent;
    // 子列表
    Nodes _children;

    // 观察者列表
    Observers _observers;

    //aabb包围盒
    Aabb3 _aabb;

    // 节点的类型描述对象
    WDBMTypeDesc* _pTypeDesc;
    // 节点业务模块数据对象基类
    WDBDBase* _pBDBase;

    // 节点的基础颜色
    Color _basicColor;
    // 节点的自定义高亮颜色
    Color _customHighlightColor;

    // 节点标志
    Flags _flags;

    // 懒加载支持,属性在页中的偏移(地址)
    uint _attrAddr;
    // 懒加载支持,属性页Id
    ushort _pageId;
};

WD_NAMESPACE_END

#include "WDNode.inl"