#pragma     once

#include    "../common/WDObject.h"
#include    "../math/Math.hpp"
#include    "../common/WDUuid.h"
#include    "WDGPUDataType.h"
#include    "../texture/WDTexture.h"

WD_NAMESPACE_BEGIN


WD_DECL_CLASS_UUID(UniformData,"307B045B-DB9B-4339-BEBF-285DF826C159");

class   WD_API UniformData :public WDObject
{
public:
    WD_DECL_OBJECT(UniformData)
public:
    /// <summary>
    /// 上下文信息，解决依赖，即组件之间的相互依赖，例如解析节点的时候需要用到组件的信息
    /// 那么用户可以在解析完组件以后，在解析节点，节点中依赖了组件信息，那么需要根据对象Id获取组件对象
    /// </summary>
    using   DataHolder  =   std::function<WDObject*(const WD::WDUuid&)>;
public:
    using   UfmDataPtr  =   std::shared_ptr<UniformData>;
public:
    int     _type;
    int     _length;
public:
    UniformData(int type, int len) : _type(type), _length(len)
    {}
    virtual ~UniformData()
    {}
    std::string         toString() const;
public:
    static  UfmDataPtr  parseData(const char* val,DTYPE type,const DataHolder& holder= {});
};

using   UfmDataPtr  =   std::shared_ptr<UniformData>;
template<class T, int type>
class   TUniformData :public UniformData
{
public:
    T       _data;
public:
    TUniformData(const T& data) 
        : UniformData(type, sizeof(T))
        , _data(data)
    {}
    virtual ~TUniformData()
    {}
    virtual void            copy(const WDObject* src) override
    {
         const TUniformData*    pThis   =   (TUniformData*)src;
        _type   =   pThis->_type;
        _length =   pThis->_length;
        _data   =   pThis->_data;
    }
    virtual WDObject::SharedPtr clone() const override
    {
        auto    p   =   std::shared_ptr< TUniformData >( new TUniformData(_data));
        p->copy(this);
        return  p;
    }
    virtual const void* data()  const
    {
        return  &_data;
    }
    virtual size_t      dataSize()  const 
    {
        return  sizeof(_data);
    }

};

template <> 
struct TUniformData<std::string,T_PATH>  :public UniformData
{
public:
    std::string     _data;
public:
    TUniformData<std::string,T_PATH>(const std::string& data) 
        : UniformData(T_PATH, (uint)data.size())
        , _data(data)
    {}
    virtual ~TUniformData<std::string,T_PATH>()
    {}
    virtual void            copy(const WDObject* src) override
    {
         const TUniformData*    pThis   =   (TUniformData*)src;
        _type   =   pThis->_type;
        _length =   pThis->_length;
        _data   =   pThis->_data;
    }
    virtual WDObject::SharedPtr    clone() const override
    {
        auto    p   =   std::shared_ptr< TUniformData<std::string,T_PATH> >( new TUniformData<std::string,T_PATH>(_data));
        p->copy(this);
        return  p;
    }
    virtual const void* data()  const
    {
        return  _data.data();
    }
    virtual size_t      dataSize()  const 
    {
        return  _data.size();
    }

};

template <> 
struct TUniformData<std::string,T_STRING>  :public UniformData
{
public:
    std::string     _data;
public:
    TUniformData<std::string,T_STRING>(const std::string& data) 
        : UniformData(T_STRING, (uint)data.size())
        , _data(data)
    {}
    virtual ~TUniformData<std::string,T_STRING>()
    {}
    virtual void            copy(const WDObject* src) override
    {
         const TUniformData*    pThis   =   (TUniformData*)src;
        _type   =   pThis->_type;
        _length =   pThis->_length;
        _data   =   pThis->_data;
    }
    virtual WDObject::SharedPtr    clone() const override
    {
        auto    p   =   std::shared_ptr< TUniformData<std::string,T_STRING> >( new TUniformData<std::string,T_STRING>(_data));
        p->copy(this);
        return  p;
    }
    virtual const void* data()  const
    {
        return  _data.data();
    }
    virtual size_t      dataSize()  const 
    {
        return  _data.size();
    }

};


typedef TUniformData<FVec4, T_COLOR>                UfmDataColor;

typedef TUniformData<char,  T_BYTE>                 UfmDataByte;
typedef TUniformData<byte,  T_UBYTE>                UfmDataUByte;
typedef TUniformData<short, T_SHORT>                UfmDataShort;
typedef TUniformData<ushort, T_USHORT>              UfmDataUShort;
typedef TUniformData<ushort, T_HALF>                UfmDataHalf;
typedef TUniformData<USVec2, T_HALF2>               UfmDataHalf2;
typedef TUniformData<USVec3, T_HALF3>               UfmDataHalf3;
typedef TUniformData<USVec4, T_HALF4>               UfmDataHalf4;

typedef TUniformData<int,   T_INT>                  UfmDataInt;
typedef TUniformData<IVec2, T_INT2>                 UfmDataInt2;
typedef TUniformData<IVec3, T_INT3>                 UfmDataInt3;
typedef TUniformData<IVec4, T_INT4>                 UfmDataInt4;

typedef TUniformData<uint, T_UINT>                  UfmDataUint;
typedef TUniformData<UIVec2, T_UINT2>               UfmDataUint2;
typedef TUniformData<UIVec3, T_UINT3>               UfmDataUint3;
typedef TUniformData<UIVec4, T_UINT4>               UfmDataUint4;

typedef TUniformData<float, T_FLOAT>                UfmDataFloat;
typedef TUniformData<FVec2, T_FLOAT2>               UfmDataFloat2;
typedef TUniformData<FVec3, T_FLOAT3>               UfmDataFloat3;
typedef TUniformData<FVec4, T_FLOAT4>               UfmDataFloat4;

typedef TUniformData<double, T_DOUBLE>              UfmDataDouble;
typedef TUniformData<DVec2, T_DOUBLE2>              UfmDataDouble2;
typedef TUniformData<DVec3, T_DOUBLE3>              UfmDataDouble3;
typedef TUniformData<DVec4, T_DOUBLE4>              UfmDataDouble4;

typedef TUniformData<FMat2, T_MAT2F>                UfmDataMat2f;
typedef TUniformData<FMat3, T_MAT3F>                UfmDataMat3f;
typedef TUniformData<FMat4, T_MAT4F>                UfmDataMat4f;

typedef TUniformData<DMat2,         T_MAT2D>            UfmDataMat2d;
typedef TUniformData<DMat3,         T_MAT3D>            UfmDataMat3d;
typedef TUniformData<DMat4,         T_MAT4D>            UfmDataMat4d;
typedef TUniformData<WDTexture1d::SharedPtr, T_TEXTURE_1D>              UfmDataTex1D;
typedef TUniformData<WDTexture2d::SharedPtr, T_TEXTURE_2D>              UfmDataTex2D;
typedef TUniformData<WDTexture3d::SharedPtr, T_TEXTURE_3D>              UfmDataTex3D;
typedef TUniformData<WDTexture2dArray::SharedPtr, T_TEXTURE_2D_ARRAY>   UfmDataTex2DArr;
typedef TUniformData<WDTextureCube::SharedPtr, T_TEXTURE_CUBE>          UfmDataTexCube;

typedef TUniformData<std::string,   T_PATH>             UfmDataPath;
typedef TUniformData<std::string,   T_STRING>           UfmDataString;
typedef TUniformData<WDUuid,        T_OBJECTID>         UfmDataObjectId;




WD_NAMESPACE_END


