#pragma once

#include "../common/WDObject.h"
#include "../common/WDUtils.h"

WD_NAMESPACE_BEGIN

class WD_API WDGLShader : public WDObject
{
public:
    enum ShaderType
    {
        ST_VS = 0x0001,
        ST_FS = 0x0002,
        ST_GS = 0x0004,
    };
private:
    ShaderType _type;
    unsigned int _id;
    std::string _code;
    std::string _log;
    bool _isCompiled;
public:
    WDGLShader(ShaderType type);
    ~WDGLShader();
public:
    bool compile(const std::string& source);
    inline bool isCompiled() const
    {
        return _isCompiled;
    }
    inline std::string log() const
    {
        return _log;
    }
    inline unsigned int id() const
    {
        return _id;
    }
    inline WDGLShader::ShaderType type() const
    {
        return _type;
    }
    inline std::string code() const
    {
        return _code;
    }
};

class WD_API WDGLProgram :public WDObject
{
private:
    //程序句柄
    int _id;
    //shader列表
    std::vector<WDGLShader*> _shaders;
    //
    std::string _log;
public:
    WDGLProgram();
    virtual ~WDGLProgram();
public:
    bool addShader(WDGLShader* pShader);
    void removeShader(WDGLShader* pShader);
    bool link();
    bool use();
    void unuse();
};

WD_NAMESPACE_END
