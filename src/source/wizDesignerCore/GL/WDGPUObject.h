#pragma     once

#include    "../common/WDObject.h"
#include    "../common/WDUtils.h"

WD_NAMESPACE_BEGIN

class WD_API WDGPUObject : public WDObject
{ 
    WD_DECL_OBJECT(WDGPUObject)
public:
    WDGPUObject();
    ~WDGPUObject();
    /**
    *   @brief 对象是否有效
    */
    bool    isValid() const
    {
        return  _bufferId != 0xFFFFFFFF;
    }
    uint    nativeId() const
    {
        return  _bufferId;
    }
protected:
    uint    _bufferId;
};


WD_NAMESPACE_END
