
#include    "WDUniformData.h"
#include    "../WDCore.h"
#include    "../WDObjectsMgr.h"

WD_NAMESPACE_BEGIN


std::string UniformData::toString() const
{
    switch (_type)
    {
    case WD::T_BYTE:
        {
            UfmDataByte*    pData   =   ( UfmDataByte*)this;
            return  std::to_string(pData->_data);
        }
    case WD::T_UBYTE:
        {
            UfmDataUByte*   pData   =   (UfmDataUByte*)this;
            return  std::to_string(pData->_data);
        }
    case WD::T_SHORT:
        {
            UfmDataShort*   pData   =   (UfmDataShort*)this;
            return  std::to_string(pData->_data);
        }
    case WD::T_USHORT:
        {
            UfmDataUShort*  pData   =   (UfmDataUShort*)this;
            return  std::to_string(pData->_data);
        }
    case WD::T_INT:
        {
            UfmDataInt*     pData   =   (UfmDataInt*)this;
            return  std::to_string(pData->_data);
        }
    case WD::T_UINT:
        {
            UfmDataUint*    pData   =   (UfmDataUint*)this;
            return  std::to_string(pData->_data);
        }
        break;
    case WD::T_FLOAT:
        {
            UfmDataFloat*   pData   =   (UfmDataFloat*)this;
            return  std::to_string(pData->_data);
        }
    case WD::T_DOUBLE:
        {
            UfmDataDouble*  pData   =   (UfmDataDouble*)this;
            return  std::to_string(pData->_data);
        }
    case WD::T_TEXTURE_1D:
        {
            UfmDataTex1D*  pData   =   (UfmDataTex1D*)this;
            return  std::string("uuid:/") + pData->_data->uuid().toString();
        }
    case WD::T_TEXTURE_2D:
        {
            UfmDataTex2D*   pData   =   (UfmDataTex2D*)this;
            if(!pData->_data)
                return "";
            return  std::string("uuid:/") + pData->_data->uuid().toString();
        }
    case WD::T_TEXTURE_2D_ARRAY:
        {
            UfmDataTex2DArr*pData   =   (UfmDataTex2DArr*)this;
            return  std::string("uuid:/") + pData->_data->uuid().toString();
        }
    case WD::T_TEXTURE_3D:
        {
            UfmDataTex3D*   pData   =   (UfmDataTex3D*)this;
            return  std::string("uuid:/") + pData->_data->uuid().toString();
        }
    case WD::T_TEXTURE_CUBE:
        {
            UfmDataTexCube* pData   =   (UfmDataTexCube*)this;
            return  std::string("uuid:/") + pData->_data->uuid().toString();
        }
    case WD::T_TEXTURE_1D_DATA:
    case WD::T_TEXTURE_2D_DATA:
    case WD::T_TEXTURE_2D_ARRAY_DATA:
    case WD::T_TEXTURE_3D_DATA:
    case WD::T_TEXTURE_CUBE_DATA:
        return  "";
    case WD::T_COLOR:
        {
            UfmDataColor* pData   =   (UfmDataColor*)this;
            return  pData->_data.toString();
        }
    case WD::T_HALF2:
        {
            UfmDataHalf2* pData   =   (UfmDataHalf2*)this;
            return  pData->_data.toString();
        }
    case WD::T_HALF3:
        {
            UfmDataHalf3* pData   =   (UfmDataHalf3*)this;
            return  pData->_data.toString();
        }
    case WD::T_HALF4:
        {
            UfmDataHalf4* pData   =   (UfmDataHalf4*)this;
            return  pData->_data.toString();
        }
    case WD::T_INT2:
        {
            UfmDataInt2* pData   =   (UfmDataInt2*)this;
            return  pData->_data.toString();
        }
    case WD::T_INT3:
        {
            UfmDataInt3* pData   =   (UfmDataInt3*)this;
            return  pData->_data.toString();
        }
    case WD::T_INT4:
        {
            UfmDataInt4* pData   =   (UfmDataInt4*)this;
            return  pData->_data.toString();
        }
    case WD::T_UINT2:
        {
            UfmDataUint2* pData   =   (UfmDataUint2*)this;
            return  pData->_data.toString();
        }
    case WD::T_UINT3:
        {
            UfmDataUint3* pData   =   (UfmDataUint3*)this;
            return  pData->_data.toString();
        }
    case WD::T_UINT4:
        {
            UfmDataUint4* pData   =   (UfmDataUint4*)this;
            return  pData->_data.toString();
        }
    case WD::T_FLOAT2:
        {
            UfmDataFloat2* pData   =   (UfmDataFloat2*)this;
            return  pData->_data.toString();
        }
    case WD::T_FLOAT3:
        {
            UfmDataFloat3* pData   =   (UfmDataFloat3*)this;
            return  pData->_data.toString();
        }
    case WD::T_FLOAT4:
        {
            UfmDataFloat4* pData   =   (UfmDataFloat4*)this;
            return  pData->_data.toString();
        }
    case WD::T_DOUBLE2:
        {
            UfmDataDouble2* pData   =   (UfmDataDouble2*)this;
            return  pData->_data.toString();
        }
    case WD::T_DOUBLE3:
        {
            UfmDataDouble3* pData   =   (UfmDataDouble3*)this;
            return  pData->_data.toString();
        }
    case WD::T_DOUBLE4:
        {
            UfmDataDouble4* pData   =   (UfmDataDouble4*)this;
            return  pData->_data.toString();
        }
    case WD::T_MAT2F:
        {
            UfmDataMat2f*   pData   =   (UfmDataMat2f*)this;
            return  pData->_data.toString();
        }
    case WD::T_MAT3F:
        {
            UfmDataMat3f*   pData   =   (UfmDataMat3f*)this;
            return  pData->_data.toString();
        }
    case WD::T_MAT4F:
        {
            UfmDataMat4f*   pData   =   (UfmDataMat4f*)this;
            return  pData->_data.toString();
        }
    case WD::T_MAT2D:
        {
            UfmDataMat2d*   pData   =   (UfmDataMat2d*)this;
            return  pData->_data.toString();
        }
    case WD::T_MAT3D:
        {
            UfmDataMat3d*   pData   =   (UfmDataMat3d*)this;
            return  pData->_data.toString();
        }
    case WD::T_MAT4D:
        {
            UfmDataMat4d*   pData   =   (UfmDataMat4d*)this;
            return  pData->_data.toString();
        }
    case WD::T_PATH:
        break;
    case WD::T_OBJECTID:
        {
            UfmDataObjectId*pData   =   (UfmDataObjectId*)this;
            return  pData->_data.toString();
        }
    case WD::T_USER:
        return  "";
    default:
        return  "";
    }
    return  "";
}


UfmDataPtr  UniformData::parseData(const char* val,DTYPE type,const DataHolder& holder)
{
    switch (type)
    {
    case WD::T_NULL:        return  nullptr;
    case WD::T_BYTE:        return  UfmDataPtr(new UfmDataByte( (char)atoi(val)));
    case WD::T_UBYTE:       return  UfmDataPtr(new UfmDataUByte((byte)atoi(val)));
    case WD::T_SHORT:       return  UfmDataPtr(new UfmDataShort((short)atoi(val)));
    case WD::T_USHORT:      return  UfmDataPtr(new UfmDataUShort((ushort)atoi(val)));
    case WD::T_INT:         return  UfmDataPtr(new UfmDataInt((int)atoi(val)));
    case WD::T_UINT:        return  UfmDataPtr(new UfmDataInt((uint)atoi(val)));
    case WD::T_HALF:        return  UfmDataPtr(new UfmDataHalf((ushort)atoi(val)));
    case WD::T_FLOAT:       return  UfmDataPtr(new UfmDataFloat((float)atof(val)));
    case WD::T_DOUBLE:      return  UfmDataPtr(new UfmDataDouble(atof(val)));
   
    case WD::T_COLOR:       return  UfmDataPtr(new UfmDataColor(FVec4::FromString(val)));
    
    case WD::T_PATH:        return  UfmDataPtr(new UfmDataPath(val));
        
    case WD::T_HALF2:       return  UfmDataPtr(new UfmDataHalf2(USVec2::FromString(val)));
    case WD::T_HALF3:       return  UfmDataPtr(new UfmDataHalf3(USVec3::FromString(val)));
    case WD::T_HALF4:       return  UfmDataPtr(new UfmDataHalf4(USVec4::FromString(val)));

    case WD::T_INT2:        return  UfmDataPtr(new UfmDataInt2(IVec2::FromString(val)));
    case WD::T_INT3:        return  UfmDataPtr(new UfmDataInt3(IVec3::FromString(val)));
    case WD::T_INT4:        return  UfmDataPtr(new UfmDataInt4(IVec4::FromString(val)));

    case WD::T_FLOAT2:      return  UfmDataPtr(new UfmDataFloat2(FVec2::FromString(val)));
    case WD::T_FLOAT3:      return  UfmDataPtr(new UfmDataFloat3(FVec3::FromString(val)));
    case WD::T_FLOAT4:      return  UfmDataPtr(new UfmDataFloat4(FVec4::FromString(val)));

    case WD::T_DOUBLE2:     return  UfmDataPtr(new UfmDataDouble2(DVec2::FromString(val)));
    case WD::T_DOUBLE3:     return  UfmDataPtr(new UfmDataDouble3(DVec3::FromString(val)));
    case WD::T_DOUBLE4:     return  UfmDataPtr(new UfmDataDouble4(DVec4::FromString(val)));

    case WD::T_MAT2F:       return  UfmDataPtr(new UfmDataMat2f(FMat2::FromString(val)));
    case WD::T_MAT3F:       return  UfmDataPtr(new UfmDataMat3f(FMat3::FromString(val)));
    case WD::T_MAT4F:       return  UfmDataPtr(new UfmDataMat4f(FMat4::FromString(val)));
                                                  
    case WD::T_MAT2D:       return  UfmDataPtr(new UfmDataMat2d(DMat2::FromString(val)));
    case WD::T_MAT3D:       return  UfmDataPtr(new UfmDataMat3d(DMat3::FromString(val)));
    case WD::T_MAT4D:       return  UfmDataPtr(new UfmDataMat4d(DMat4::FromString(val)));

    case WD::T_TEXTURE_1D:  
        {
            /// 读取纹理文件路径
            if (_strnicmp("data:/", val, 6) == 0)
            {
                std::string imgPath(val);
                imgPath.erase(imgPath.begin(), imgPath.begin() + 6);
                return UfmDataPtr(new UfmDataPath(imgPath));
            }
            /// 根据uid进行查找
            else if (_strnicmp("uuid:/",val,6) == 0)
            {
                std::string uid(val);
                uid.erase(uid.begin(), uid.begin() + 6);
                WDUuid      id  =   WDUuid::FromString(uid);
                WDTexture1d::SharedPtr    tex =   nullptr;
                if (holder)
                {
                    auto    obj =   holder(id);
                    tex         =   obj ? obj->toPtr<WDTexture1d>() : nullptr;
                }
                if (tex.get())
                {
                    return  UfmDataPtr(new UfmDataTex1D(tex));
                }
                else
                {
                    auto queryRes = WDTexture::ToShared(Core().objectsMgr().instance<WDTexture>().query<WDTexture>(uid));
                    if(queryRes == nullptr)
                        return nullptr;
                    return UfmDataPtr(new UfmDataTex1D(WDTexture1d::SharedCast(queryRes)));
                }
            }
            /// 按原值返回
            else
            {
                return  UfmDataPtr(new UfmDataString(val));
            }
        }
        break;
    case WD::T_TEXTURE_2D:
        {
            /// 读取路径并读取文件内容
            if (_strnicmp("data:/", val, 6) == 0)
            {
                std::string imgPath(val);
                imgPath.erase(imgPath.begin(), imgPath.begin() + 6);

                auto fullImgPath = Core().dataDirPath() + imgPath;//图像路径，xx/../xx/xx.png
                WDImage::SharedPtr  pImg = WDImage::MakeShared(fullImgPath);
                if (pImg == nullptr)//读取文件失败
                {
                    //考虑使用imgPath再尝试
                    pImg    =   WDImage::MakeShared(imgPath);
                    if(pImg == nullptr)//实在读不出来就返回空值
                        return nullptr;
                }
                //这里可以不直接绑定，等渲染时会调用纹理的relize接口进行绑定
                auto    tex = WDTexture2d::MakeShared();
                if (tex.get())
                {
                    tex->setData(pImg);
                    return UfmDataPtr(new UfmDataTex2D(tex));
                }
                return nullptr;
            }
            /// 使用uid进行查找
            else if (_strnicmp("uuid:/",val,6) == 0)
            {
                std::string uid(val);
                uid.erase(uid.begin(), uid.begin() + 6);
                WDUuid      id  =   WDUuid::FromString(uid);
                WDTexture2d::SharedPtr    tex =   nullptr;
                if (holder)
                {
                    auto    obj =   holder(id);//在指定字典中进行查找
                    tex         =   obj ? obj->toPtr<WDTexture2d>() : nullptr;
                }
                if (tex.get())
                {
                    return  UfmDataPtr(new UfmDataTex2D(tex));
                }
                else
                {
                    //尝试在系统中查找
                    auto queryRes = WDTexture::ToShared(Core().objectsMgr().instance<WDTexture>().query<WDTexture>(uid));
                    if(queryRes == nullptr)
                        return nullptr;
                    return UfmDataPtr(new UfmDataTex2D(WDTexture2d::SharedCast(queryRes)));
                }
            }
            /// 按原值返回
            else
            {
                return  UfmDataPtr(new UfmDataString(val));
            }
        }
        break;
    case WD::T_TEXTURE_3D:
        {
            if (_strnicmp("data:/", val, 6) == 0)
            {
                std::string imgPath(val);
                imgPath.erase(imgPath.begin(), imgPath.begin() + 6);
                return UfmDataPtr(new UfmDataPath(imgPath));
            }
            else if (_strnicmp("uuid:/",val,6) == 0)
            {
                std::string uid(val);
                uid.erase(uid.begin(), uid.begin() + 6);
                WDUuid      id  =   WDUuid::FromString(uid);
                WDTexture3d::SharedPtr    tex =   nullptr;
                if (holder)
                {
                    auto    obj =   holder(id);
                    tex         =   obj ? obj->toPtr<WDTexture3d>() : nullptr;
                }
                if (tex.get())
                {
                    return  UfmDataPtr(new UfmDataTex3D(tex));
                }
                else
                {
                    auto queryRes = WDTexture::ToShared(Core().objectsMgr().instance<WDTexture>().query<WDTexture>(uid));
                    if(queryRes == nullptr)
                        return nullptr;
                    return UfmDataPtr(new UfmDataTex3D(WDTexture3d::SharedCast(queryRes)));
                }
            }
            else
            {
                return  UfmDataPtr(new UfmDataString(val));
            }
        }
        break;
    case WD::T_TEXTURE_CUBE:
        {
            if (_strnicmp("data:/", val, 6) == 0)
            {
                std::string imgPath(val);
                imgPath.erase(imgPath.begin(), imgPath.begin() + 6);
                return UfmDataPtr(new UfmDataPath(imgPath));
            }
            else if (_strnicmp("uuid:/",val,6) == 0)
            {
                std::string uid(val);
                uid.erase(uid.begin(), uid.begin() + 6);
                WDUuid      id  =   WDUuid::FromString(uid);
                WDTextureCube::SharedPtr  tex =   nullptr;
                if (holder)
                {
                    auto    obj =   holder(id);
                    tex         =   obj ? obj->toPtr<WDTextureCube>() : nullptr;
                    return  UfmDataPtr(new UfmDataObjectId(WDUuid::FromString(uid)));
                }
                if (tex.get())
                {
                    return  UfmDataPtr(new UfmDataTexCube(tex));
                }
                else
                {
                    auto queryRes = WDTexture::ToShared(Core().objectsMgr().instance<WDTexture>().query<WDTexture>(uid));
                    if(queryRes == nullptr)
                        return nullptr;
                    return UfmDataPtr(new UfmDataTexCube(WDTextureCube::SharedCast(queryRes)));
                }
            }
            else
                return  UfmDataPtr(new UfmDataString(val));
        }
        break;
    case WD::T_TEXTURE_1D_DATA:
    case WD::T_TEXTURE_2D_DATA:
    case WD::T_TEXTURE_2D_ARRAY_DATA:
    case WD::T_TEXTURE_2D_ARRAY:
    case WD::T_TEXTURE_3D_DATA:
    case WD::T_TEXTURE_CUBE_DATA:
    case WD::T_STRING:     
        break;

    default:
        break;
    }
    return  nullptr;
}

WD_NAMESPACE_END


