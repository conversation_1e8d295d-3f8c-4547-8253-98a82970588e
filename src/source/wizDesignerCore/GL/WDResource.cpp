#include    "WDResource.h"
#include    "../WDCore.h"

WD_NAMESPACE_BEGIN

WDResource::WDResource() :_device(nullptr)
{

}

WDResource::~WDResource()
{

}

void WDResource::initialize(WDOpenGL* device)
{
    _PROGRAM_P3_UC4.initialize(device);
    _PROGRAM_P3_INSTANCE.initialize(device);
    _PROGRAM_P3_N3_INST.initialize(device);
    _PROGRAM_P3_UV2.initialize(device);
    _PROGRAM_P3_UV2MS.initialize(device);
    _WDUIProgram.initialize(device);
    _PROGRAM_P3_UC4_EX.initialize(device);
    _programBasicColor.initialize(device);
    _programBlinnPhong.initialize(device);
    _programTexture.initialize(device);

    byte    data[16*16*4]   =   {0};
    memset(data,0xFF,sizeof(data));
    _textureWhite   =   WDTexture2d::MakeShared();
    _textureWhite->create(16,16,WDImage::Format_RGBA,WDImage::Type::Type_Byte,WDImage::Format_RGBA,data);
    _textureWhite->unbind();

    std::string prgLibPath  =   Core().dataDirPath();
                prgLibPath  +=  "/material/program.xml";

    _prgLib = WDProgramLib::MakeShared(*device);
    _prgLib->load(prgLibPath.c_str());

}

void WDResource::destroy()
{
    _prgLib =   nullptr;
    _PROGRAM_P3_UC4.destroy();
    _PROGRAM_P3_INSTANCE.destroy();
    _PROGRAM_P3_N3_INST.destroy();
    _PROGRAM_P3_UV2.destroy();
    _PROGRAM_P3_UV2MS.destroy();
    _WDUIProgram.destroy();
    _PROGRAM_P3_UC4_EX.destroy();
    _programBasicColor.destroy();
    _programBlinnPhong.destroy();
    _programTexture.destroy();
}


WD_NAMESPACE_END