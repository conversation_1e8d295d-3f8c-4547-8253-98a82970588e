#pragma     once

#include    "WDUniformData.h"
#include    <map>
#include    <string>
#include    "../texture/WDTexture.h"

WD_NAMESPACE_BEGIN


class   WDOpenGL;
class   WDContext;

WD_DECL_CLASS_UUID(Uniform,"7ECEAF3D-78FF-4FAB-9E6A-A960AC5BB322");
class   WD_API Uniform :public WDObject
{
public:
    WD_DECL_OBJECT(Uniform)
public:
    using   DataHolder   =   std::function<WDObject*(const WDUuid& id)>;
public:
    uint        _uniform    =   0xFFFFFFFF;

    /// 数据类型
    DTYPE       _type       =   T_NULL;
    /// 规格化
    bool        _transpose  =   false;
    
    /// 是否已经编译
    bool        _isCompiled =   false;
    /// 数据个数
    byte        _count      =   0;
    byte        _stage      =   0;
    
    /// 数据可为空
    UfmDataPtr  _data       =   nullptr;
    /// 目前是纹理对象
    WDTexture::SharedPtr _tex        =   nullptr;

    /// 引用可以为空
    const void* _slot       =   nullptr;
    /// 槽名称
    std::string _slotName;
public:
    Uniform(const std::string& name = "")
    {
        setName(name);
    }
    bool    isValid() const
    {
        return  _uniform != 0xFFFFFFFF;
    }
    
    bool    appData(WDContext& ctx);

    bool    appTexture(const DataHolder& holder);

    bool    set(WDTexture1d::SharedPtr tex);
    bool    set(WDTexture2d::SharedPtr tex);
    bool    set(WDTexture3d::SharedPtr tex);
    bool    set(WDTextureCube::SharedPtr tex);
    bool    set(WDTexture2dArray::SharedPtr tex);

    bool    set(float v0);
    bool    set(float v0, float v1);
    bool    set(float v0, float v1, float v2);
    bool    set(float v0, float v1, float v2, float v3);

    bool    set(int v0);
    bool    set(int v0, int v1);
    bool    set(int v0, int v1, int v2);
    bool    set(int v0, int v1, int v2, int v3);

    bool    set(uint v0);
    bool    set(uint v0, uint v1);
    bool    set(uint v0, uint v1, uint v2);
    bool    set(uint v0, uint v1, uint v2, uint v3);

    bool    set(WDUuid uuid);
    bool    set(std::string path);

    bool    set(const UIVec2& val)
    {
        return  set(val[0],val[1]);
    }
    bool    set(const UIVec3& val)
    {
        return  set(val[0],val[1],val[2]);
    }
    bool    set(const UIVec4& val)
    {
        return  set(val[0],val[1],val[2],val[3]);
    }

    bool    set(const IVec2& val)
    {
        return  set(val[0],val[1]);
    }
    bool    set(const IVec3& val)
    {
        return  set(val[0],val[1],val[2]);
    }
    bool    set(const IVec4& val)
    {
        return  set(val[0],val[1],val[2],val[3]);
    }

    bool    set(const FVec2& val)
    {
        return  set(val[0],val[1]);
    }
    bool    set(const FVec3& val)
    {
        return  set(val[0],val[1],val[2]);
    }
    bool    set(const FVec4& val)
    {
        return  set(val[0],val[1],val[2],val[3]);
    }

    bool    set(const FMat2& val)
    {
        return  setMatrix2fv(val.data());
    }
    bool    set(const FMat3& val)
    {
        return  setMatrix3fv(val.data());
    }
    bool    set(const FMat4& val)
    {
        return  setMatrix4fv(val.data());
    }
    bool    setMatrix2fv(const float*value);
    bool    setMatrix3fv(const float*value);
    bool    setMatrix4fv(const float*value);

    bool    get(int& val);
    bool    get(IVec2& val);
    bool    get(IVec3& val);
    bool    get(IVec4& val);

    bool    get(uint& val);
    bool    get(UIVec2& val);
    bool    get(UIVec3& val);
    bool    get(UIVec4& val);

    bool    get(float& val);
    bool    get(FVec2& val);
    bool    get(FVec3& val);
    bool    get(FVec4& val);

    bool    get(FMat2& val);
    bool    get(FMat3& val);
    bool    get(FMat4& val);

    bool    get(WDTexture1d::SharedPtr& val);
    bool    get(WDTexture2d::SharedPtr& val);
    bool    get(WDTexture3d::SharedPtr& val);
    bool    get(WDTexture2dArray::SharedPtr& val);
    bool    get(WDTextureCube::SharedPtr& val);

   
    virtual void        copy(const WDObject* pSrcObject) override;

    virtual WDObject::SharedPtr   clone() const override;
};


using   UfmPtr      =   std::shared_ptr<Uniform>;
using   Uniforms    =   std::map<std::string,UfmPtr>;

template<class T>
class TUniform :public Uniform
{
public:
    static DTYPE    DataType()
    {

        if (typeid(T) == typeid(int))                       return  T_INT;
        else if (typeid(T) == typeid(IVec2))                return  T_INT2;
        else if (typeid(T) == typeid(IVec3))                return  T_INT3;
        else if (typeid(T) == typeid(IVec4))                return  T_INT4;

        else if (typeid(T) == typeid(uint))                 return  T_UINT;
        else if (typeid(T) == typeid(UIVec2))               return  T_UINT2;
        else if (typeid(T) == typeid(UIVec3))               return  T_UINT3;
        else if (typeid(T) == typeid(UIVec4))               return  T_UINT4;

        else if (typeid(T) == typeid(float))                return  T_FLOAT;
        else if (typeid(T) == typeid(FVec2))                return  T_FLOAT2;
        else if (typeid(T) == typeid(FVec3))                return  T_FLOAT3;
        else if (typeid(T) == typeid(FVec4))                return  T_FLOAT4;

        else if (typeid(T) == typeid(FMat2))                return  T_MAT2F;
        else if (typeid(T) == typeid(FMat3))                return  T_MAT3F;
        else if (typeid(T) == typeid(FMat4))                return  T_MAT4F;

        else if (typeid(T) == typeid(DMat2))                return  T_MAT2D;
        else if (typeid(T) == typeid(DMat3))                return  T_MAT3D;
        else if (typeid(T) == typeid(DMat4))                return  T_MAT4D;
        else if (typeid(T) == typeid(WDTexture1d::SharedPtr))       return  T_TEXTURE_1D;
        else if (typeid(T) == typeid(WDTexture2d::SharedPtr))       return  T_TEXTURE_2D;
        else if (typeid(T) == typeid(WDTexture3d::SharedPtr))       return  T_TEXTURE_3D;
        else if (typeid(T) == typeid(WDTexture2dArray::SharedPtr))  return  T_TEXTURE_2D_ARRAY;
        else if (typeid(T) == typeid(WDTextureCube::SharedPtr))     return  T_TEXTURE_CUBE;
      
        return  T_NULL;

    }
public:
    TUniform(const std::string& name,const T& val)
        :Uniform(name)
    {
        _type   =   DataType();
        set(val);
    }
};

using   UfmInt      =   TUniform<int>;
using   UfmInt2     =   TUniform<IVec2>;
using   UfmInt3     =   TUniform<IVec3>;
using   UfmInt4     =   TUniform<IVec4>;


using   UfmUint     =   TUniform<uint>;
using   UfmUint2    =   TUniform<UIVec2>;
using   UfmUint3    =   TUniform<UIVec3>;
using   UfmUint4    =   TUniform<UIVec4>;

using   UfmFloat    =   TUniform<float>;
using   UfmFloat2   =   TUniform<FVec2>;
using   UfmFloat3   =   TUniform<FVec3>;
using   UfmFloat4   =   TUniform<FVec4>;


using   UfmMat2f    =   TUniform<FMat2>;
using   UfmMat3f    =   TUniform<FMat3>;
using   UfmMat4f    =   TUniform<FMat4>;

using   UfmTex1d    =   TUniform<WDTexture1d::SharedPtr>;
using   UfmTex2d    =   TUniform<WDTexture2d::SharedPtr>;
using   UfmTex3d    =   TUniform<WDTexture3d::SharedPtr>;
using   UfmTex2dArr =   TUniform<WDTexture2dArray::SharedPtr>;
using   UfmTexCube  =   TUniform<WDTextureCube::SharedPtr>;




WD_NAMESPACE_END


