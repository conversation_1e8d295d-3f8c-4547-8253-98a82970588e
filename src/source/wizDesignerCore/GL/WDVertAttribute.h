#pragma     once

#include    "../common/WDObject.h"
#include    "WDGPUDataType.h"

WD_NAMESPACE_BEGIN

/**
 * @brief 顶点类型
*/
enum    VertType
{
    INPUT_NONE      = -1,
    INPUT_POSITION      ,
    INPUT_NORMAL        ,
    INPUT_COLOR0        ,
    INPUT_COLOR1        ,
    INPUT_COLOR2        ,
    INPUT_COLOR3        ,
    INPUT_COLOR4        ,
       
    //顶点变形支持,顶点关联的骨头索引
    INPUT_BONE_INDEX    ,
    //顶点变形支持,顶点受骨头影响的权重
    INPUT_BONE_WEIGHT   ,

    INPUT_BINORMAL      ,
    INPUT_TAGENT        ,
    INPUT_TEXTURE0      ,
    INPUT_TEXTURE1      ,
    INPUT_TEXTURE2      ,
    INPUT_TEXTURE3      ,
    INPUT_TEXTURE4      ,
    INPUT_TEXTURE5      ,
    INPUT_TEXTURE6      ,
    INPUT_TEXTURE7      ,

    INPUT_TRANSLATE     ,
    INPUT_ITRANSLATE    ,

    INPUT_MAT2F0        ,
    INPUT_MAT2F1        ,
    INPUT_MAT2F2        ,
    INPUT_MAT2F3        ,

    INPUT_MAT3F0        ,
    INPUT_MAT3F1        ,
    INPUT_MAT3F2        ,
    INPUT_MAT3F3        ,

	INPUT_MAT4F0        ,
    INPUT_MAT4F1        ,
    INPUT_MAT4F2        ,
    INPUT_MAT4F3        ,

    INPUT_VEC2F0        ,
    INPUT_VEC2F1        ,
    INPUT_VEC2F2        ,
    INPUT_VEC2F3        ,

    INPUT_VEC3F0        ,
    INPUT_VEC3F1        ,
    INPUT_VEC3F2        ,
    INPUT_VEC3F3        ,

    INPUT_VEC4F0        ,
    INPUT_VEC4F1        ,
    INPUT_VEC4F2        ,
    INPUT_VEC4F3        ,
    INPUT_VEC4F4        ,
    INPUT_VEC4F5        ,
    INPUT_VEC4F6        ,
    INPUT_VEC4F7        ,
    INPUT_VEC4F8        ,
    INPUT_VEC4F9        ,

    INPUT_MATERIAL0     ,
    INPUT_MATERIAL1     ,
    INPUT_MATERIAL2     ,
    INPUT_MATERIAL3     ,

    INPUT_ID0           ,
    INPUT_ID1           ,
    INPUT_ID2           ,
    INPUT_ID3           ,
};


inline  const char* VertTypeToName(VertType index)
{
    switch (index)
    {
    case INPUT_POSITION:    return  "POSITION";
            
    case INPUT_NORMAL:      return  "NORMAL";
    case INPUT_COLOR0:      return  "COLOR0";
    case INPUT_COLOR1:      return  "COLOR1";
    case INPUT_COLOR2:      return  "COLOR2";
    case INPUT_COLOR3:      return  "COLOR3";
    case INPUT_BINORMAL:    return  "BINORMAL";
    case INPUT_TAGENT:      return  "TAGENT";
                
    case INPUT_BONE_INDEX:  return  "BONE_INDEX";
    case INPUT_BONE_WEIGHT: return  "BONE_WEIGHT";
                
    case INPUT_TEXTURE0:    return  "TEXTURE0";
    case INPUT_TEXTURE1:    return  "TEXTURE1";
    case INPUT_TEXTURE2:    return  "TEXTURE2";
    case INPUT_TEXTURE3:    return  "TEXTURE3";
    case INPUT_TEXTURE4:    return  "TEXTURE4";
    case INPUT_TEXTURE5:    return  "TEXTURE5";
    case INPUT_TEXTURE6:    return  "TEXTURE6";
    case INPUT_TEXTURE7:    return  "TEXTURE7";

    case INPUT_TRANSLATE:   return  "TRANSLATE";
    case INPUT_ITRANSLATE:  return  "ITRANSLATE";

    case INPUT_MAT2F0:      return  "MAT2F0";
    case INPUT_MAT2F1:      return  "MAT2F1";
    case INPUT_MAT2F2:      return  "MAT2F2";
    case INPUT_MAT2F3:      return  "MAT2F3";

    case INPUT_MAT3F0:      return  "MAT3F0";
    case INPUT_MAT3F1:      return  "MAT3F1";
    case INPUT_MAT3F2:      return  "MAT3F2";
    case INPUT_MAT3F3:      return  "MAT3F3";

    case INPUT_MAT4F0:      return  "MAT4F0";
    case INPUT_MAT4F1:      return  "MAT4F1";
    case INPUT_MAT4F2:      return  "MAT4F2";
    case INPUT_MAT4F3:      return  "MAT4F3";

    case INPUT_VEC2F0:      return  "VEC2F0";
    case INPUT_VEC2F1:      return  "VEC2F1";
    case INPUT_VEC2F2:      return  "VEC2F2";
    case INPUT_VEC2F3:      return  "VEC2F3";

    case INPUT_VEC3F0:      return  "VEC3F0";
    case INPUT_VEC3F1:      return  "VEC3F1";
    case INPUT_VEC3F2:      return  "VEC3F2";
    case INPUT_VEC3F3:      return  "VEC3F3";

    case INPUT_VEC4F0:      return  "VEC4F0";
    case INPUT_VEC4F1:      return  "VEC4F1";
    case INPUT_VEC4F2:      return  "VEC4F2";
    case INPUT_VEC4F3:      return  "VEC4F3";
    case INPUT_VEC4F4:      return  "VEC4F4";
    case INPUT_VEC4F5:      return  "VEC4F5";
    case INPUT_VEC4F6:      return  "VEC4F6";
    case INPUT_VEC4F7:      return  "VEC4F7";
    case INPUT_VEC4F8:      return  "VEC4F8";
    case INPUT_VEC4F9:      return  "VEC4F9";

    case INPUT_ID0:         return  "ID0";
    case INPUT_ID1:         return  "ID1";
    case INPUT_ID2:         return  "ID2";
    case INPUT_ID3:         return  "ID3";

    case INPUT_MATERIAL0:   return  "MATERIAL0";
    case INPUT_MATERIAL1:   return  "MATERIAL1";
    case INPUT_MATERIAL2:   return  "MATERIAL2";
    case INPUT_MATERIAL3:   return  "MATERIAL3";

    default:                return  "NONE";
    }
}
inline  VertType    VertTypeFromName(const char* name)
{
    if (strcmp(name, "POSITION") == 0)      return  INPUT_POSITION;
            
    else if (strcmp(name, "NORMAL") == 0)   return  INPUT_NORMAL;
    else if (strcmp(name, "COLOR0") == 0)   return  INPUT_COLOR0;
    else if (strcmp(name, "COLOR1") == 0)   return  INPUT_COLOR1;
    else if (strcmp(name, "COLOR2") == 0)   return  INPUT_COLOR2;
    else if (strcmp(name, "COLOR3") == 0)   return  INPUT_COLOR3;

    else if (strcmp(name, "BONE_INDEX") == 0) return INPUT_BONE_INDEX;
    else if (strcmp(name, "BONE_WEIGHT") == 0) return INPUT_BONE_WEIGHT;

    else if (strcmp(name, "TEXTURE0") == 0) return  INPUT_TEXTURE0;
    else if (strcmp(name, "TEXTURE1") == 0) return  INPUT_TEXTURE1;
    else if (strcmp(name, "TEXTURE2") == 0) return  INPUT_TEXTURE2;
    else if (strcmp(name, "TEXTURE3") == 0) return  INPUT_TEXTURE3;
    else if (strcmp(name, "TEXTURE4") == 0) return  INPUT_TEXTURE4;
    else if (strcmp(name, "TEXTURE5") == 0) return  INPUT_TEXTURE5;
    else if (strcmp(name, "TEXTURE6") == 0) return  INPUT_TEXTURE6;
    else if (strcmp(name, "TEXTURE7") == 0) return  INPUT_TEXTURE7;

    else if (strcmp(name, "BINORMAL") == 0)     return  INPUT_BINORMAL;
    else if (strcmp(name, "TAGENT") == 0)       return  INPUT_TAGENT;

    else if (strcmp(name, "ITRANSLATE") == 0)   return  INPUT_ITRANSLATE;
    else if (strcmp(name, "TRANSLATE") == 0)    return  INPUT_TRANSLATE;

    else if (strcmp(name, "ID0") == 0)          return  INPUT_ID0;
    else if (strcmp(name, "ID1") == 0)          return  INPUT_ID1;
    else if (strcmp(name, "ID2") == 0)          return  INPUT_ID2;
    else if (strcmp(name, "ID3") == 0)          return  INPUT_ID3;

    else if (strcmp(name, "MAT2F0") == 0)       return  INPUT_MAT2F0;
    else if (strcmp(name, "MAT2F1") == 0)       return  INPUT_MAT2F1;
    else if (strcmp(name, "MAT2F2") == 0)       return  INPUT_MAT2F2;
    else if (strcmp(name, "MAT2F3") == 0)       return  INPUT_MAT2F3;

    else if (strcmp(name, "MAT3F0") == 0)       return  INPUT_MAT3F0;
    else if (strcmp(name, "MAT3F1") == 0)       return  INPUT_MAT3F1;
    else if (strcmp(name, "MAT3F2") == 0)       return  INPUT_MAT3F2;
    else if (strcmp(name, "MAT3F3") == 0)       return  INPUT_MAT3F3;

    else if (strcmp(name, "MAT4F0") == 0)       return  INPUT_MAT4F0;
    else if (strcmp(name, "MAT4F1") == 0)       return  INPUT_MAT4F1;
    else if (strcmp(name, "MAT4F2") == 0)       return  INPUT_MAT4F2;
    else if (strcmp(name, "MAT4F3") == 0)       return  INPUT_MAT4F3;

    else if (strcmp(name, "VEC2F0") == 0)       return  INPUT_VEC2F0;
    else if (strcmp(name, "VEC2F1") == 0)       return  INPUT_VEC2F1;
    else if (strcmp(name, "VEC2F2") == 0)       return  INPUT_VEC2F2;
    else if (strcmp(name, "VEC2F3") == 0)       return  INPUT_VEC2F3;

    else if (strcmp(name, "VEC3F0") == 0)       return  INPUT_VEC3F0;
    else if (strcmp(name, "VEC3F1") == 0)       return  INPUT_VEC3F1;
    else if (strcmp(name, "VEC3F2") == 0)       return  INPUT_VEC3F2;
    else if (strcmp(name, "VEC3F3") == 0)       return  INPUT_VEC3F3;

    else if (strcmp(name, "VEC4F0") == 0)       return  INPUT_VEC4F0;
    else if (strcmp(name, "VEC4F1") == 0)       return  INPUT_VEC4F1;
    else if (strcmp(name, "VEC4F2") == 0)       return  INPUT_VEC4F2;
    else if (strcmp(name, "VEC4F3") == 0)       return  INPUT_VEC4F3;
    else if (strcmp(name, "VEC4F4") == 0)       return  INPUT_VEC4F4;
    else if (strcmp(name, "VEC4F5") == 0)       return  INPUT_VEC4F5;
    else if (strcmp(name, "VEC4F6") == 0)       return  INPUT_VEC4F6;
    else if (strcmp(name, "VEC4F7") == 0)       return  INPUT_VEC4F7;
    else if (strcmp(name, "VEC4F8") == 0)       return  INPUT_VEC4F8;
    else if (strcmp(name, "VEC4F9") == 0)       return  INPUT_VEC4F9;

    else if (strcmp(name, "MATERIAL0") == 0)    return  INPUT_MATERIAL0;
    else if (strcmp(name, "MATERIAL1") == 0)    return  INPUT_MATERIAL1;
    else if (strcmp(name, "MATERIAL2") == 0)    return  INPUT_MATERIAL2;
    else if (strcmp(name, "MATERIAL3") == 0)    return  INPUT_MATERIAL3;


    else return INPUT_NONE;
}
struct  WDVertDesc
{
    /// 顶点属性slot
    VertType    _attr   =   INPUT_NONE;
    /// 对应的shader输入资源句柄
    uint        _handle =   0xFFFFFFFF;
    /// 位置信息，可以认为是内存地址
    byte*       _offset =   nullptr;
    DTYPE       _type   =   T_NULL;
    /// stride
    uint        _stride =   0;
    /// 数据大小
    uint        _size   =   0;

    /// 规格化
    bool        _normalize  =   false;
    /// 规格化
    bool        _isCompiled =   false;
    byte        _div        =   0;
    WDObject::SharedPtr   _ptr;
};
/// 描述一个顶点数据的属性

WD_DECL_CLASS_UUID(WDVertAttribute,"5A82E84C-4215-48FC-892A-88FFF5A55FD3");

class   WDVertAttribute 
        : public WDObject
        , public WDVertDesc
{
public:
    WD_DECL_OBJECT(WDVertAttribute)
public:
    WDVertAttribute()
    {
        _handle     =   0xFFFFFFFF;
        _attr       =   INPUT_NONE;
        _offset     =   0;
        _stride     =   0;
        _type       =   T_NULL;
        _normalize  =   0;
        _isCompiled =   0;
        _size       =   0;
        _div        =   0;
    }
    WDVertAttribute& operator=(const WDVertAttribute& right)
    {
        setName(right.name());
        _handle     =   right._handle;
        _attr       =   right._attr;
        _offset     =   right._offset;
        _stride     =   right._stride;
        _type       =   right._type;
        _normalize  =   right._normalize;
        _isCompiled =   right._isCompiled;
        _size       =   right._size;
        _div        =   right._div;
        return *this;
    }
};

using   AttrPtr     =   std::shared_ptr<WDVertAttribute>;
using   AttrPtrs    =   std::map<VertType,AttrPtr>;
using   VDescMap    =   std::map<VertType,WDVertDesc>;

class   WDVertDescSet 
    :public WDObject
    ,public VDescMap
{
public:
    WD_DECL_OBJECT(WDVertDescSet)
};

using   VertDescSetPtr  =   std::shared_ptr<WDVertDescSet>;
using   VDSetMap        =   std::map<WDUuid,VertDescSetPtr>;

class   WD_API  WDVertDescSetInstance
{
public:
    WDVertDescSetInstance();
    static  VertDescSetPtr  v3n3();
    static  VertDescSetPtr  v3n3uv2();
    /// <summary>
    /// 绘制文本使用，比较特殊，文本instance中数据描述中没有id,attr
    /// </summary>
    /// <returns></returns>
    static  VertDescSetPtr  v3uv2Text();
    /// <summary>
    /// 根据类型查询描述信息
    /// </summary>
    /// <returns></returns>
    static  VertDescSetPtr  query(const WDUuid& id);
protected:
    static  VertDescSetPtr  _v3n3;
    static  VertDescSetPtr  _v3n3uv2;
    static  VertDescSetPtr  _v3uv2Text;
public:
    static  VDSetMap        _vdMap;
};

WD_NAMESPACE_END


