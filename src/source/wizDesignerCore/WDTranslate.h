#pragma     once

#include "common/WDTDelegate.hpp"

WD_NAMESPACE_BEGIN


/**
 * @brief 上下文配置项
*/
class TranslateContext
{
public:
    using KeyValues = std::map<std::string, std::string>;
private:
    std::string _name;
    KeyValues _keyValues;
public:
    TranslateContext(const std::string& name = "")
    {
        _name = name;
    }
public:
    // 获取上下文名称
    const std::string& name() const
    {
        return _name;
    }
    // 添加翻译项
    void addTranslage(const std::string& key, const std::string& value)
    {
        if (key.empty() || value.empty())
            return;

        auto itr = _keyValues.find(key);
        if (itr != _keyValues.end())
        {
            //assert(false && "Translate key is exist!");
        }
        _keyValues[key] = value;
    }
    // 查找翻译项
    const std::string* findTranslage(const std::string& key)
    {
        auto itr = _keyValues.find(key);
        if (itr != _keyValues.end())
            return &(itr->second);

        return nullptr;
    }
    // 获取所有翻译项
    const KeyValues& keyValueObjects() const
    {
        return _keyValues;
    }
    // 获取所有翻译项
    KeyValues& keyValueObjects()
    {
        return _keyValues;
    }
};
/**
 * @brief 语言配置项
*/
class TranslateLanguage
{
public:
    using Contexts =  std::map<std::string, TranslateContext>;
private:
    std::string _name;
    Contexts _contexts;
public:
    TranslateLanguage(const std::string& name = "")
    {
        _name = name;
    }
public:
    // 获取语言名称
    const std::string& name() const
    {
        return _name;
    }
    // 添加上下文对象
    TranslateContext& addContext(const std::string& name)
    {
        auto fItr = _contexts.find(name);
        if (fItr != _contexts.end())
        {
            //assert(false && "Translate context name is exist!");
            return fItr->second;
        }
        _contexts[name] = TranslateContext(name);
        return _contexts[name];
    }
    // 查找上下文对象
    TranslateContext* findContext(const std::string& name)
    {
        auto fItr = _contexts.find(name);
        if (fItr != _contexts.end())
            return &(fItr->second);
        
        return nullptr;
    }
    // 执行合并, 将right中的上下文以及翻译项添加到当前语言中
    void merge(const TranslateLanguage& right)
    {
        if (this->name() != right.name())
        {
            assert(false && "不能合并不同名称的语言!");
            return;
        }
        for (auto itr = right._contexts.begin(); itr != right._contexts.end(); ++itr)
        {
            const TranslateContext& cxt = itr->second;
            this->addContext(cxt);
        }
    }
    // 获取所有上下文对象
    const Contexts& contextObjects() const
    {
        return _contexts;
    }
    // 获取所有上下文对象
    Contexts& contextObjects() 
    {
        return _contexts;
    }
private:
    // 添加上下文对象
    bool addContext(const TranslateContext& cxt)
    {
        auto fItr = _contexts.find(cxt.name());
        if (fItr == _contexts.end())
        {
            // 添加上下文
            _contexts[cxt.name()] = cxt;
            return true;
        }
        TranslateContext& lCxt = fItr->second;
        for (const auto& kv: cxt.keyValueObjects())
        {
            lCxt.addTranslage(kv.first, kv.second);
        }
        return true;
    }
};

class WDTranslatePrivate;
/**
* @brief 翻译
*   翻译使用的所有字符串都使用utf-8编码
*/
class WD_API WDTranslate
{    
public:
    using Languages = std::map<std::string, TranslateLanguage>;
public:
    /**
    * @brief 当前语言切换通知
    * @param currentLanguageName 当前语言名称
    * @param prevLanguageName 前一个语言名称
    * @param sender 发送者
    */
    using NoticeCurrentLanguageChanged = WDTMultiDelegate<void(const std::string& currentLanguageName
        , const std::string& prevLanguageName
        , WDTranslate& sender)>;
public:
    WDTranslate();
    WDTranslate(const WDTranslate& right);
    WDTranslate(WDTranslate&& right) noexcept;
    WDTranslate& operator=(const WDTranslate& right);
    WDTranslate& operator=(WDTranslate&& right) noexcept;
    ~WDTranslate();
private:
    static WDTranslate* _pSelf;
    NoticeCurrentLanguageChanged _noticeCurrentLanguageChanged;
    WDTranslatePrivate* _p;
    friend class WDTranslatePrivate;
public:
    /**
    * @brief 获取实例
    */
    static inline WDTranslate* Instance()
    {
        if (_pSelf == nullptr)
            _pSelf = new WDTranslate();
        return _pSelf;
    }
public:
    /**
    * @brief 获取当前语言切换通知
    */
    NoticeCurrentLanguageChanged& noticeCurrentLanguageChanged() 
    {
        return _noticeCurrentLanguageChanged;
    }
    /**
    * @brief 加载语言翻译文件
    * @param fileName 翻译文件名称
    */
    bool load(const std::string& languageFileName);
    /**
    * @brief 加载语言翻译文件列表
    * @param fileNames 翻译文件列表
    * @return 返回对应文件成功或失败,返回值的长度与参数长度一致
    */
    std::vector<bool> load(const std::vector<std::string>& fileNames);
    /**
     * @brief 卸载掉所有已加载的翻译数据
    */
    void unload();
    /**
    * @brief 翻译
    * @param context 上下文名称，通常指定为当前调用translate方法的类、文件名称或者模块名称
    * @param key 翻译源字符串
    * @param disambiguation 消除歧义，帮助消除同一源字符串在相同上下文中的不同翻译
    * @return 翻译结果
    */
    const std::string& translate(const std::string& contextName, const std::string& key, const std::string& disambiguation = "") const;
    /**
    * @brief 获取语言列表 
    */
    const StringVector& languages() const;
    /**
    * @brief 获取当前语言
    */
    const std::string& currentLanguage() const;
    /**
    * @brief 设置当前语言
    * @param currentLanguage 当前语言名称
    */
    void setCurrentLanguage(const std::string& currentLanguageName);
    /**
     * @brief 获取所有语言对象
    */
    const Languages& languageObjects() const;
    /**
     * @brief 获取所有语言对象
    */
    Languages& languageObjects() ;
public:
    /**
    * @brief 开始语境上下文
    * @param contextName 上下文名称
    */
    bool beginContext(const std::string& contextName);
    /**
    * @brief 从当前开始的上下文语境中翻译
    *  ！注意:使用该方法时，确保已经调用了 beginContext() 且设置了正确的上下文名称
    * @param key  翻译源字符串
    * @param disambiguation 消除歧义，帮助消除同一源字符串在相同上下文中的不同翻译
    */
    const std::string& translateUseCurrentContext(const std::string& key, const std::string& disambiguation = "");
    /**
    * @brief 结束语境上下文
    */
    void endContext();

};

/**
* @brief 翻译
*/
static inline const std::string& WDTs(const std::string& contextName, const std::string& key, const std::string& disambiguation = "")
{
    return WDTranslate::Instance()->translate(contextName, key, disambiguation);
}

/**
* @brief 开始语境上下文
*/
static inline bool WDCxtTsBg(const std::string& contextName)
{
    return WDTranslate::Instance()->beginContext(contextName);
}
/**
* @brief 使用当前上下文语境翻译
*  ！注意:使用该方法时，确保已经调用了 WDCxtTsBg() 且设置了正确的上下文名称
*/
static inline const std::string& WDCxtTs(const std::string& key, const std::string& disambiguation = "")
{
    return WDTranslate::Instance()->translateUseCurrentContext(key, disambiguation);
}
/**
* @brief 结束语境上下文
*/
static inline void WDCxtTsEd()
{
    return WDTranslate::Instance()->endContext();
}

WD_NAMESPACE_END
