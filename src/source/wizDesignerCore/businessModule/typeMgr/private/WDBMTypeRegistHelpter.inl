#pragma once
#include "../../../node/WDNode.h"
#include "WDBMTypeRegistHelpter.h"

WD_NAMESPACE_BEGIN

template <typename TTransform>
void PosAttrRegistHelpter(WD::WDBMTypeDesc& typeDesc)
{
    auto FUpdate = WDBMAttrDesc::Flag::F_Update;
    auto FHidden = WDBMAttrDesc::Flag::F_Hidden;

    // 位置 Position
    static constexpr const char* SAName_Pos = "Position"; // 简称 "POS"
    static constexpr const char* SAName_PosWrtOwner = "Position WRT Owner";
    static constexpr const char* SAName_PosWrtWorld = "Position WRT World";
    static constexpr const char* SAName_PosStrWrtOwner = "PositionStr WRT Owner";
    static constexpr const char* SAName_PosStrWrtWorld = "PositionStr WRT World";

    // 默认位置
    typeDesc.add(SAName_Pos
        , WDBMAttrValueType::T_DVec3
        , [](const WDNode& node)->WDBMAttrValue
        {
            auto p = dynamic_cast<const TTransform*>(node.transformObject());
            if (p == nullptr)
            {
                assert(false);
                return WDBMAttrValue();
            }
            return WDBMAttrValue(p->localTranslation());
        }
        , [](WDNode& node, const WDBMAttrValue& value)->bool
        {
            auto p = dynamic_cast<TTransform*>(node.transformObject());
            if (p == nullptr)
            {
                assert(false);
                return false;
            }
            auto pos = DVec3::Zero();
            if (!value.toDVec3(pos))
                return false;
            p->setLocalTranslation(pos);
            return true;
        }
        , "POS"
        , WDBMAttrDesc::Flags(FUpdate));

    // DVec3 类型，相对于父节点
    typeDesc.add(SAName_PosWrtOwner
        , WDBMAttrValueType::T_DVec3
        , [](const WDNode& node)->WDBMAttrValue
        {
            auto p = dynamic_cast<const TTransform*>(node.transformObject());
            if (p == nullptr)
            {
                assert(false);
                return WDBMAttrValue();
            }
            return WDBMAttrValue(p->localTranslation());
        }
        , [](WDNode& node, const WDBMAttrValue& value)->bool
        {
            auto p = dynamic_cast<TTransform*>(node.transformObject());
            if (p == nullptr)
            {
                assert(false);
                return false;
            }
            auto pos = DVec3::Zero();
            if (!value.toDVec3(pos))
                return false;
            p->setLocalTranslation(pos);
            return true;
        }
        , ""
        , WDBMAttrDesc::Flags(FUpdate | FHidden));

    // DVec3 类型，相对于世界坐标
    typeDesc.add(SAName_PosWrtWorld
        , WDBMAttrValueType::T_DVec3
        , [](const WDNode& node)->WDBMAttrValue
        {
            auto p = dynamic_cast<const TTransform*>(node.transformObject());
            if (p == nullptr)
            {
                assert(false);
                return WDBMAttrValue();
            }
            return WDBMAttrValue(p->globalTranslation());
        }
        , [](WDNode& node, const WDBMAttrValue& value)->bool
        {
            auto p = dynamic_cast<TTransform*>(node.transformObject());
            if (p == nullptr)
            {
                assert(false);
                return false;
            }
            auto pos = DVec3::Zero();
            if (!value.toDVec3(pos))
                return false;
            p->setGlobalTranslation(pos, node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
            return true;
        }
        , ""
        , WDBMAttrDesc::Flags(FUpdate | FHidden));

    // string 类型，相对于父节点
    typeDesc.add(SAName_PosStrWrtOwner
        , WDBMAttrValueType::T_String
        , [](const WDNode& node)->WDBMAttrValue
        {
            auto p = dynamic_cast<const TTransform*>(node.transformObject());
            if (p == nullptr)
            {
                assert(false);
                return WDBMAttrValue();
            }
            auto pos = p->localTranslation();
            auto posStr = DPositionParserENU::OutputStringByPosition(pos);
            return WDBMAttrValue(posStr);
        }
        , [](WDNode& node, const WDBMAttrValue& value)->bool
        {
            auto p = dynamic_cast<TTransform*>(node.transformObject());
            if (p == nullptr)
            {
                assert(false);
                return false;
            }
            auto posStr = std::string("");
            if (!value.toString(posStr))
                return false;
            auto pos = DVec3::Zero();
            if (!DPositionParserENU::Position(posStr, pos) && !DPositionParserXYZ::Position(posStr, pos))
                return false;
            p->setLocalTranslation(pos);
            return true;
        }
        , ""
        , WDBMAttrDesc::Flags(FUpdate | FHidden));

    // string 类型，相对于世界坐标
    typeDesc.add(SAName_PosStrWrtWorld
        , WDBMAttrValueType::T_String
        , [](const WDNode& node)->WDBMAttrValue
        {
            auto p = dynamic_cast<const TTransform*>(node.transformObject());
            if (p == nullptr)
            {
                assert(false);
                return WDBMAttrValue();
            }
            auto pos = p->globalTranslation();
            auto posStr = DPositionParserENU::OutputStringByPosition(pos);
            return WDBMAttrValue(posStr);
        }
        , [](WDNode& node, const WDBMAttrValue& value)->bool
        {
            auto p = dynamic_cast<TTransform*>(node.transformObject());
            if (p == nullptr)
            {
                assert(false);
                return false;
            }
            auto posStr = std::string("");
            if (!value.toString(posStr))
                return false;
            auto pos = DVec3::Zero();
            if (!DPositionParserENU::Position(posStr, pos) && !DPositionParserXYZ::Position(posStr, pos))
                return false;
            p->setGlobalTranslation(pos, node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
            return true;
        }
        , ""
        , WDBMAttrDesc::Flags(FUpdate | FHidden));
}
template <typename TTransform>
void OriAttrRegistHelpter(WD::WDBMTypeDesc& typeDesc)
{
    auto FUpdate = WDBMAttrDesc::Flag::F_Update;
    auto FHidden = WDBMAttrDesc::Flag::F_Hidden;

    // 朝向 Orientation
    static constexpr const char* SAName_Ori = "Orientation";  // 简称 "ORI"
    static constexpr const char* SAName_OriWrtOwner = "Orientation WRT Owner";
    static constexpr const char* SAName_OriWrtWorld = "Orientation WRT World";
    static constexpr const char* SAName_OriStrWrtOwner = "OrientationStr WRT Owner";
    static constexpr const char* SAName_OriStrWrtWorld = "OrientationStr WRT World";

    // 默认朝向
    typeDesc.add(SAName_Ori
        , WDBMAttrValueType::T_DQuat
        , [](const WDNode& node)->WDBMAttrValue
        {
            auto p = dynamic_cast<const TTransform*>(node.transformObject());
            if (p == nullptr)
            {
                assert(false);
                return WDBMAttrValue();
            }
            return WDBMAttrValue(p->localRotation());
        }
        , [](WDNode& node, const WDBMAttrValue& value)->bool
        {
            auto p = dynamic_cast<TTransform*>(node.transformObject());
            if (p == nullptr)
            {
                assert(false);
                return false;
            }
            auto rot = DQuat::Identity();
            if (!value.toQuat(rot))
                return false;
            p->setLocalRotation(rot);
            return true;
        }
        , "ORI"
        , WDBMAttrDesc::Flags(FUpdate | FHidden));

    // DQuat 类型，相对于父节点
    typeDesc.add(SAName_OriWrtOwner
        , WDBMAttrValueType::T_DQuat
        , [](const WDNode& node)->WDBMAttrValue
        {
            auto p = dynamic_cast<const TTransform*>(node.transformObject());
            if (p == nullptr)
            {
                assert(false);
                return WDBMAttrValue();
            }
            return WDBMAttrValue(p->localRotation());
        }
        , [](WDNode& node, const WDBMAttrValue& value)->bool
        {
            auto p = dynamic_cast<TTransform*>(node.transformObject());
            if (p == nullptr)
            {
                assert(false);
                return false;
            }
            auto rot = DQuat::Identity();
            if (!value.toQuat(rot))
                return false;
            p->setLocalRotation(rot);
            return true;
        }
        , ""
        , WDBMAttrDesc::Flags(FUpdate | FHidden));

    // DQuat 类型，相对于世界坐标
    typeDesc.add(SAName_OriWrtWorld
        , WDBMAttrValueType::T_DQuat
        , [](const WDNode& node)->WDBMAttrValue
        {
            auto p = dynamic_cast<const TTransform*>(node.transformObject());
            if (p == nullptr)
            {
                assert(false);
                return WDBMAttrValue();
            }
            return WDBMAttrValue(p->globalRotation());
        }
        , [](WDNode& node, const WDBMAttrValue& value)->bool
        {
            auto p = dynamic_cast<TTransform*>(node.transformObject());
            if (p == nullptr)
            {
                assert(false);
                return false;
            }
            auto rot = DQuat::Identity();
            if (!value.toQuat(rot))
                return false;
            p->setGlobalRotation(rot, node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
            return true;
        }
        , ""
        , WDBMAttrDesc::Flags(FUpdate | FHidden));

    // string 类型，相对于父节点
    typeDesc.add(SAName_OriStrWrtOwner
        , WDBMAttrValueType::T_String
        , [](const WDNode& node)->WDBMAttrValue
        {
            auto p = dynamic_cast<const TTransform*>(node.transformObject());
            if (p == nullptr)
            {
                assert(false);
                return WDBMAttrValue();
            }
            auto rot = p->localRotation();
            auto rotStr = DOrientationParserENU::OutputStringByOrientation(rot);
            return WDBMAttrValue(rotStr);
        }
        , [](WDNode& node, const WDBMAttrValue& value)->bool
        {
            auto p = dynamic_cast<TTransform*>(node.transformObject());
            if (p == nullptr)
            {
                assert(false);
                return false;
            }
            auto rotStr = std::string("");
            if (!value.toString(rotStr))
                return false;
            auto rot = DQuat::Identity();
            if (!DOrientationParserENU::Orientation(rotStr, rot) && !DOrientationParserXYZ::Orientation(rotStr, rot))
                return false;
            p->setLocalRotation(rot);
            return true;
        }
        , ""
        , WDBMAttrDesc::Flags(FUpdate));

    // string 类型，相对于世界坐标
    typeDesc.add(SAName_OriStrWrtWorld
        , WDBMAttrValueType::T_String
        , [](const WDNode& node)->WDBMAttrValue
        {
            auto p = dynamic_cast<const TTransform*>(node.transformObject());
            if (p == nullptr)
            {
                assert(false);
                return WDBMAttrValue();
            }
            auto rot = node.globalRotation();
            auto rotStr = DOrientationParserENU::OutputStringByOrientation(rot);
            return WDBMAttrValue(rotStr);
        }
        , [](WDNode& node, const WDBMAttrValue& value)->bool
        {
            auto p = dynamic_cast<TTransform*>(node.transformObject());
            if (p == nullptr)
            {
                assert(false);
                return false;
            }
            auto rotStr = std::string("");
            if (!value.toString(rotStr))
                return false;
            auto rot = DQuat::Identity();
            if (!DOrientationParserENU::Orientation(rotStr, rot) && !DOrientationParserXYZ::Orientation(rotStr, rot))
                return false;
            p->setGlobalRotation(rot, node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
            return true;
        }
        , ""
        , WDBMAttrDesc::Flags(FUpdate | FHidden));
}
template <typename TTransform>
void ScaleAttrRegistHelpter(WDBMTypeDesc& typeDesc)
{
    auto FUpdate = WDBMAttrDesc::Flag::F_Update;
    auto FHidden = WDBMAttrDesc::Flag::F_Hidden;

    // 缩放 Scaling
    static constexpr const char* SAName_Scale = "Scaling";  // 简称 ""
    // 默认朝向
    typeDesc.add(SAName_Scale
        , WDBMAttrValueType::T_DVec3
        , [](const WDNode& node)->WDBMAttrValue
        {
            auto p = dynamic_cast<const TTransform*>(node.transformObject());
            if (p == nullptr)
            {
                assert(false);
                return WDBMAttrValue();
            }
            return WDBMAttrValue(p->localScaling());
        }
        , [](WDNode& node, const WDBMAttrValue& value)->bool
        {
            auto p = dynamic_cast<TTransform*>(node.transformObject());
            if (p == nullptr)
            {
                assert(false);
                return false;
            }
            auto scale = DVec3(1.0);
            if (!value.toDVec3(scale))
                return false;
            p->setLocalScaling(scale);
            return true;
        }
        , ""
        , WDBMAttrDesc::Flags(FUpdate | FHidden));
}

template <typename TTransform>
void HTPosAttrRegistHelpter(WD::WDBMTypeDesc& typeDesc)
{
    auto FUpdate = WDBMAttrDesc::Flag::F_Update;
    auto FHidden = WDBMAttrDesc::Flag::F_Hidden;

    // 头坐标
    {
        static constexpr const char* SAName_HPos = "Hposition";  // 简称 "HPOS"
        static constexpr const char* SAName_HPosWRTOwner = "Hposition WRT Owner";
        static constexpr const char* SAName_HPosWRTWorld = "Hposition WRT World";
        static constexpr const char* SAName_HPosStrWRTOwner = "HpositionStr WRT Owner";
        static constexpr const char* SAName_HPosStrWRTWorld = "HpositionStr WRT World";

        // 默认位置
        typeDesc.add(SAName_HPos
            , WDBMAttrValueType::T_DVec3
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                return p->localHPos();
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto pos = DVec3::Zero();
                if (!value.toDVec3(pos))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setLocalHPos(pos);

                return true;
            }
            , "HPOS"
            , WDBMAttrDesc::Flags(FUpdate));

        // DVec3 类型，相对于父节点
        typeDesc.add(SAName_HPosWRTOwner
            , WDBMAttrValueType::T_DVec3
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                return p->localHPos();
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto pos = DVec3::Zero();
                if (!value.toDVec3(pos))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setLocalHPos(pos);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate | FHidden));

        // DVec3 类型，相对于世界坐标
        typeDesc.add(SAName_HPosWRTWorld
            , WDBMAttrValueType::T_DVec3
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                return p->globalHPos(node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto pos = DVec3::Zero();
                if (!value.toDVec3(pos))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setGlobalHPos(pos, node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate | FHidden));

        // string 类型，相对于父节点
        typeDesc.add(SAName_HPosStrWRTOwner
            , WDBMAttrValueType::T_String
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                auto pos = p->localHPos();
                auto posStr = DPositionParserENU::OutputStringByPosition(pos);
                return WDBMAttrValue(posStr);
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto posStr = std::string("");
                if (!value.toString(posStr))
                    return false;
                auto pos = DVec3::Zero();
                if (!DPositionParserENU::Position(posStr, pos) && !DPositionParserXYZ::Position(posStr, pos))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setLocalHPos(pos);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate | FHidden));

        // string 类型，相对于世界坐标
        typeDesc.add(SAName_HPosStrWRTWorld
            , WDBMAttrValueType::T_String
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                auto pos = p->globalHPos(node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
                auto posStr = DPositionParserENU::OutputStringByPosition(pos);
                return WDBMAttrValue(posStr);
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto posStr = std::string("");
                if (!value.toString(posStr))
                    return false;
                auto pos = DVec3::Zero();
                if (!DPositionParserENU::Position(posStr, pos) && !DPositionParserXYZ::Position(posStr, pos))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setGlobalHPos(pos, node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate | FHidden));
    }

    // 尾坐标
    {
        static constexpr const char* SAName_TPos = "Tposition";  // 简称 "TPOS"
        static constexpr const char* SAName_TPosWRTOwner = "Tposition WRT Owner";
        static constexpr const char* SAName_TPosWRTWorld = "Tposition WRT World";
        static constexpr const char* SAName_TPosStrWRTOwner = "TpositionStr WRT Owner";
        static constexpr const char* SAName_TPosStrWRTWorld = "TpositionStr WRT World";
        // 默认位置
        typeDesc.add(SAName_TPos
            , WDBMAttrValueType::T_DVec3
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                return p->localTPos();
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto pos = DVec3::Zero();
                if (!value.toDVec3(pos))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setLocalTPos(pos);
                return true;
            }
            , "TPOS"
            , WDBMAttrDesc::Flags(FUpdate));

        // DVec3 类型，相对于父节点
        typeDesc.add(SAName_TPosWRTOwner
            , WDBMAttrValueType::T_DVec3
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                return p->localTPos();
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto pos = DVec3::Zero();
                if (!value.toDVec3(pos))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setLocalTPos(pos);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate | FHidden));

        // DVec3 类型，相对于世界坐标
        typeDesc.add(SAName_TPosWRTWorld
            , WDBMAttrValueType::T_DVec3
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                return p->globalTPos(node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto pos = DVec3::Zero();
                if (!value.toDVec3(pos))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setGlobalTPos(pos, node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate | FHidden));

        // string 类型，相对于父节点
        typeDesc.add(SAName_TPosStrWRTOwner
            , WDBMAttrValueType::T_String
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                auto pos = p->localTPos();
                auto posStr = DPositionParserENU::OutputStringByPosition(pos);
                return WDBMAttrValue(posStr);
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto posStr = std::string("");
                if (!value.toString(posStr))
                    return false;
                auto pos = DVec3::Zero();
                if (!DPositionParserENU::Position(posStr, pos) && !DPositionParserXYZ::Position(posStr, pos))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setLocalTPos(pos);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate | FHidden));

        // string 类型，相对于世界坐标
        typeDesc.add(SAName_TPosStrWRTWorld
            , WDBMAttrValueType::T_String
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                auto pos = p->globalTPos(node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
                auto posStr = DPositionParserENU::OutputStringByPosition(pos);
                return WDBMAttrValue(posStr);
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto posStr = std::string("");
                if (!value.toString(posStr))
                    return false;
                auto pos = DVec3::Zero();
                if (!DPositionParserENU::Position(posStr, pos) && !DPositionParserXYZ::Position(posStr, pos))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setGlobalTPos(pos, node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate | FHidden));
    }
}
template <typename TTransform>
void HTDirAttrRegistHelpter(WD::WDBMTypeDesc& typeDesc)
{
    auto FUpdate = WDBMAttrDesc::Flag::F_Update;
    auto FHidden = WDBMAttrDesc::Flag::F_Hidden;

    // 头方向
    {
        static constexpr const char* SAName_HDir = "Hdirection";  // 简称 "HDIR"
        static constexpr const char* SAName_HDirWRTOwner = "Hdirection WRT Owner";
        static constexpr const char* SAName_HDirWRTWorld = "Hdirection WRT World";
        static constexpr const char* SAName_HDirStrWRTOwner = "HdirectionStr WRT Owner";
        static constexpr const char* SAName_HDirStrWRTWorld = "HdirectionStr WRT World";

        // 默认位置
        typeDesc.add(SAName_HDir
            , WDBMAttrValueType::T_DVec3
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                return p->localHDir();
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto dir = DVec3::Zero();
                if (!value.toDVec3(dir))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setLocalHDir(dir);
                return true;
            }
            , "HDIR"
            , WDBMAttrDesc::Flags(FUpdate | FHidden));

        // DVec3 类型，相对于父节点
        typeDesc.add(SAName_HDirWRTOwner
            , WDBMAttrValueType::T_DVec3
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                return p->localHDir();
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto dir = DVec3::Zero();
                if (!value.toDVec3(dir))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setLocalHDir(dir);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate | FHidden));

        // DVec3 类型，相对于世界坐标
        typeDesc.add(SAName_HDirWRTWorld
            , WDBMAttrValueType::T_DVec3
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                return p->globalHDir(node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto dir = DVec3::Zero();
                if (!value.toDVec3(dir))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setGlobalHDir(dir, node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate | FHidden));

        // string 类型，相对于父节点
        typeDesc.add(SAName_HDirStrWRTOwner
            , WDBMAttrValueType::T_String
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                auto dir = p->localHDir();
                auto dirStr = DDirectionParserENU::OutputStringByDirection(dir);
                return WDBMAttrValue(dirStr);
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto dirStr = std::string("");
                if (!value.toString(dirStr))
                    return false;
                auto dir = DVec3::Zero();
                if (!DDirectionParserENU::Direction(dirStr, dir) && !DDirectionParserXYZ::Direction(dirStr, dir))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setLocalHDir(dir);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate));

        // string 类型，相对于世界坐标
        typeDesc.add(SAName_HDirStrWRTWorld
            , WDBMAttrValueType::T_String
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                auto dir = p->globalHDir(node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
                auto dirStr = DDirectionParserENU::OutputStringByDirection(dir);
                return WDBMAttrValue(dirStr);
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto dirStr = std::string("");
                if (!value.toString(dirStr))
                    return false;
                auto dir = DVec3::Zero();
                if (!DDirectionParserENU::Direction(dirStr, dir) && !DDirectionParserXYZ::Direction(dirStr, dir))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setGlobalHDir(dir, node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate | FHidden));

    }
    // 尾方向
    {
        static constexpr const char* SAName_TDir = "Tdirection";  // 简称 "TDIR"
        static constexpr const char* SAName_TDirWRTOwner = "Tdirection WRT Owner";
        static constexpr const char* SAName_TDirWRTWorld = "Tdirection WRT World";
        static constexpr const char* SAName_TDirStrWRTOwner = "TdirectionStr WRT Owner";
        static constexpr const char* SAName_TDirStrWRTWorld = "TdirectionStr WRT World";

        // 默认位置
        typeDesc.add(SAName_TDir
            , WDBMAttrValueType::T_DVec3
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                return p->localTDir();
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto dir = DVec3::Zero();
                if (!value.toDVec3(dir))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setLocalTDir(dir);
                return true;
            }
            , "TDIR"
            , WDBMAttrDesc::Flags(FUpdate | FHidden));

        // DVec3 类型，相对于父节点
        typeDesc.add(SAName_TDirWRTOwner
            , WDBMAttrValueType::T_DVec3
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                return p->localTDir();
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto dir = DVec3::Zero();
                if (!value.toDVec3(dir))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setLocalTDir(dir);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate | FHidden));

        // DVec3 类型，相对于世界坐标
        typeDesc.add(SAName_TDirWRTWorld
            , WDBMAttrValueType::T_DVec3
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                return p->globalTDir(node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto dir = DVec3::Zero();
                if (!value.toDVec3(dir))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setGlobalTDir(dir, node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate | FHidden));

        // string 类型，相对于父节点
        typeDesc.add(SAName_TDirStrWRTOwner
            , WDBMAttrValueType::T_String
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                auto dir = p->localTDir();
                auto dirStr = DDirectionParserENU::OutputStringByDirection(dir);
                return WDBMAttrValue(dirStr);
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto dirStr = std::string("");
                if (!value.toString(dirStr))
                    return false;
                auto dir = DVec3::Zero();
                if (!DDirectionParserENU::Direction(dirStr, dir) && !DDirectionParserXYZ::Direction(dirStr, dir))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setLocalTDir(dir);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate));

        // string 类型，相对于世界坐标
        typeDesc.add(SAName_TDirStrWRTWorld
            , WDBMAttrValueType::T_String
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                auto dir = p->globalTDir(node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
                auto dirStr = DDirectionParserENU::OutputStringByDirection(dir);
                return WDBMAttrValue(dirStr);
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto dirStr = std::string("");
                if (!value.toString(dirStr))
                    return false;
                auto dir = DVec3::Zero();
                if (!DDirectionParserENU::Direction(dirStr, dir) && !DDirectionParserXYZ::Direction(dirStr, dir))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setGlobalTDir(dir, node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate | FHidden));

    }
}

template <typename TTransform>
void SEPosAttrRegistHelpter(WDBMTypeDesc& typeDesc)
{
    auto FUpdate = WDBMAttrDesc::Flag::F_Update;
    auto FHidden = WDBMAttrDesc::Flag::F_Hidden;

    // 起点位置
    {
        static constexpr const char* SAName_PosS = "Posstart";  // 简称 "POSS"
        static constexpr const char* SAName_PosSWRTOwner = "Posstart WRT Owner";
        static constexpr const char* SAName_PosSWRTWorld = "Posstart WRT World";
        static constexpr const char* SAName_PosSStrWRTOwner = "PosstartStr WRT Owner";
        static constexpr const char* SAName_PosSStrWRTWorld = "PosstartStr WRT World";

        // 默认位置
        typeDesc.add(SAName_PosS
            , WDBMAttrValueType::T_DVec3
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                return p->localSPos();
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto pos = DVec3::Zero();
                if (!value.toDVec3(pos))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setLocalSPos(pos);
                return true;
            }
            , "POSS"
            , WDBMAttrDesc::Flags(FUpdate));

        // DVec3 类型，相对于父节点
        typeDesc.add(SAName_PosSWRTOwner
            , WDBMAttrValueType::T_DVec3
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                return p->localSPos();
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto pos = DVec3::Zero();
                if (!value.toDVec3(pos))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setLocalSPos(pos);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate | FHidden));

        // DVec3 类型，相对于世界坐标
        typeDesc.add(SAName_PosSWRTWorld
            , WDBMAttrValueType::T_DVec3
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                return p->globalSPos(node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto pos = DVec3::Zero();
                if (!value.toDVec3(pos))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setGlobalSPos(pos, node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate | FHidden));

        // string 类型，相对于父节点
        typeDesc.add(SAName_PosSStrWRTOwner
            , WDBMAttrValueType::T_String
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                auto pos = p->localSPos();
                auto posStr = DPositionParserENU::OutputStringByPosition(pos);
                return WDBMAttrValue(posStr);
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto posStr = std::string("");
                if (!value.toString(posStr))
                    return false;
                auto pos = DVec3::Zero();
                if (!DPositionParserENU::Position(posStr, pos) && !DPositionParserXYZ::Position(posStr, pos))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setLocalSPos(pos);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate | FHidden));

        // string 类型，相对于世界坐标
        typeDesc.add(SAName_PosSStrWRTWorld
            , WDBMAttrValueType::T_String
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                auto pos = p->globalSPos(node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
                auto posStr = DPositionParserENU::OutputStringByPosition(pos);
                return WDBMAttrValue(posStr);
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto posStr = std::string("");
                if (!value.toString(posStr))
                    return false;
                auto pos = DVec3::Zero();
                if (!DPositionParserENU::Position(posStr, pos) && !DPositionParserXYZ::Position(posStr, pos))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setGlobalSPos(pos, node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate | FHidden));
    }

    // 终点位置
    {
        static constexpr const char* SAName_PosE = "Posend";  // 简称 "POSE"
        static constexpr const char* SAName_PosEWRTOwner = "Posend WRT Owner";
        static constexpr const char* SAName_PosEWRTWorld = "Posend WRT World";
        static constexpr const char* SAName_PosEStrWRTOwner = "PosendStr WRT Owner";
        static constexpr const char* SAName_PosEStrWRTWorld = "PosendStr WRT World";

        // 默认位置
        typeDesc.add(SAName_PosE
            , WDBMAttrValueType::T_DVec3
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                return p->localEPos();
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto pos = DVec3::Zero();
                if (!value.toDVec3(pos))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setLocalEPos(pos);
                return true;
            }
            , "POSE"
            , WDBMAttrDesc::Flags(FUpdate));

        // DVec3 类型，相对于父节点
        typeDesc.add(SAName_PosEWRTOwner
            , WDBMAttrValueType::T_DVec3
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                return p->localEPos();
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto pos = DVec3::Zero();
                if (!value.toDVec3(pos))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setLocalEPos(pos);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate | FHidden));

        // DVec3 类型，相对于世界坐标
        typeDesc.add(SAName_PosEWRTWorld
            , WDBMAttrValueType::T_DVec3
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                return p->globalEPos(node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto pos = DVec3::Zero();
                if (!value.toDVec3(pos))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setGlobalEPos(pos, node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate | FHidden));

        // string 类型，相对于父节点
        typeDesc.add(SAName_PosEStrWRTOwner
            , WDBMAttrValueType::T_String
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                auto pos = p->localEPos();
                auto posStr = DPositionParserENU::OutputStringByPosition(pos);
                return WDBMAttrValue(posStr);
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto posStr = std::string("");
                if (!value.toString(posStr))
                    return false;
                auto pos = DVec3::Zero();
                if (!DPositionParserENU::Position(posStr, pos) && !DPositionParserXYZ::Position(posStr, pos))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setLocalEPos(pos);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate | FHidden));

        // string 类型，相对于世界坐标
        typeDesc.add(SAName_PosEStrWRTWorld
            , WDBMAttrValueType::T_String
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                auto pos = p->globalEPos(node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
                auto posStr = DPositionParserENU::OutputStringByPosition(pos);
                return WDBMAttrValue(posStr);
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                auto posStr = std::string("");
                if (!value.toString(posStr))
                    return false;
                auto pos = DVec3::Zero();
                if (!DPositionParserENU::Position(posStr, pos) && !DPositionParserXYZ::Position(posStr, pos))
                    return false;
                p->setGlobalEPos(pos, node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate | FHidden));
    }
}
template <typename TTransform>
void SEDrnAttrRegistHelpter(WDBMTypeDesc& typeDesc)
{
    auto FUpdate = WDBMAttrDesc::Flag::F_Update;
    auto FHidden = WDBMAttrDesc::Flag::F_Hidden;

    // 起点朝向 
    {
        static constexpr const char* SAName_DrnS = "Drnstart";  // 简称 "DRNS"
        static constexpr const char* SAName_DrnSWRTOwner = "Drnstart WRT Owner";
        static constexpr const char* SAName_DrnSWRTWorld = "Drnstart WRT World";
        static constexpr const char* SAName_DrnSStrWRTOwner = "DrnstartStr WRT Owner";
        static constexpr const char* SAName_DrnSStrWRTWorld = "DrnstartStr WRT World";

        // 默认
        typeDesc.add(SAName_DrnS
            , WDBMAttrValueType::T_DVec3
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                return p->localSDrn();
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto dir = DVec3::Zero();
                if (!value.toDVec3(dir))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setLocalSDrn(dir);
                return true;
            }
            , "DRNS"
            , WDBMAttrDesc::Flags(FUpdate | FHidden));

        // DVec3 类型，相对于父节点
        typeDesc.add(SAName_DrnSWRTOwner
            , WDBMAttrValueType::T_DVec3
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                return p->localSDrn();
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto dir = DVec3::Zero();
                if (!value.toDVec3(dir))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setLocalSDrn(dir);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate | FHidden));

        // DVec3 类型，相对于世界坐标
        typeDesc.add(SAName_DrnSWRTWorld
            , WDBMAttrValueType::T_DVec3
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                // 如果是0向量，则直接返回0向量
                if (p->localSDrn().lengthSq() <= NumLimits<float>::Epsilon)
                    return DVec3::Zero();
                return p->globalSDrn(node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto dir = DVec3::Zero();
                if (!value.toDVec3(dir))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                // 如果是0向量，则直接设置为0向量
                if (dir.lengthSq() <= NumLimits<float>::Epsilon)
                    p->setLocalSDrn(DVec3::Zero());
                else
                    p->setGlobalSDrn(dir, node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate | FHidden));

        // string 类型，相对于父节点
        typeDesc.add(SAName_DrnSStrWRTOwner
            , WDBMAttrValueType::T_String
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                auto dir = p->localSDrn();
                if (dir.lengthSq() <= NumLimits<float>::Epsilon)
                    return WDBMAttrValue(std::string(""));
                auto dirStr = DDirectionParserENU::OutputStringByDirection(dir);
                return WDBMAttrValue(dirStr);
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto dirStr = std::string("");
                if (!value.toString(dirStr))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                // 如果指定的字符串为空，则设置为0向量
                if (dirStr.empty())
                {
                    p->setLocalSDrn(DVec3::Zero());
                    return true;
                }
                auto dir = DVec3::Zero();
                if (!DDirectionParserENU::Direction(dirStr, dir) && !DDirectionParserXYZ::Direction(dirStr, dir))
                    return false;
                p->setLocalSDrn(dir);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate));

        // string 类型，相对于世界坐标
        typeDesc.add(SAName_DrnSStrWRTWorld
            , WDBMAttrValueType::T_String
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                // 如果朝向是0向量，则返回空字符串
                if (p->localSDrn().lengthSq() <= NumLimits<float>::Epsilon)
                    return WDBMAttrValue(std::string(""));
                auto dir = p->globalSDrn(node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
                auto dirStr = DDirectionParserENU::OutputStringByDirection(dir);
                return WDBMAttrValue(dirStr);
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto dirStr = std::string("");
                if (!value.toString(dirStr))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                // 如果指定的字符串为空,则直接设置为0向量
                if (dirStr.empty())
                {
                    p->setLocalSDrn(DVec3::Zero());
                    return true;
                }
                auto dir = DVec3::Zero();
                if (!DDirectionParserENU::Direction(dirStr, dir) && !DDirectionParserXYZ::Direction(dirStr, dir))
                    return false;
                p->setGlobalSDrn(dir, node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate | FHidden));
    }

    // 终点朝向
    {
        static constexpr const char* SAName_DrnE = "Drnend";  // 简称 "DRNE"
        static constexpr const char* SAName_DrnEWRTOwner = "Drnend WRT Owner";
        static constexpr const char* SAName_DrnEWRTWorld = "Drnend WRT World";
        static constexpr const char* SAName_DrnEStrWRTOwner = "DrnendStr WRT Owner";
        static constexpr const char* SAName_DrnEStrWRTWorld = "DrnendStr WRT World";

        // 默认位置
        typeDesc.add(SAName_DrnE
            , WDBMAttrValueType::T_DVec3
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                return p->localEDrn();
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto dir = DVec3::Zero();
                if (!value.toDVec3(dir))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setLocalEDrn(dir);
                return true;
            }
            , "DRNE"
            , WDBMAttrDesc::Flags(FUpdate | FHidden));

        // DVec3 类型，相对于父节点
        typeDesc.add(SAName_DrnEWRTOwner
            , WDBMAttrValueType::T_DVec3
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                return p->localEDrn();
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto dir = DVec3::Zero();
                if (!value.toDVec3(dir))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                p->setLocalEDrn(dir);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate | FHidden));

        // DVec3 类型，相对于世界坐标
        typeDesc.add(SAName_DrnEWRTWorld
            , WDBMAttrValueType::T_DVec3
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                // 如果是0向量，直接返回
                if (p->localEDrn().lengthSq() <= NumLimits<float>::Epsilon)
                    return DVec3::Zero();
                return p->globalEDrn(node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto dir = DVec3::Zero();
                if (!value.toDVec3(dir))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                if (dir.lengthSq() <= NumLimits<float>::Epsilon)
                    p->setLocalEDrn(DVec3::Zero());
                else
                    p->setGlobalEDrn(dir, node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate | FHidden));

        // string 类型，相对于父节点
        typeDesc.add(SAName_DrnEStrWRTOwner
            , WDBMAttrValueType::T_String
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                auto dir = p->localEDrn();
                if (dir.lengthSq() <= NumLimits<float>::Epsilon)
                    return WDBMAttrValue(std::string(""));
                auto dirStr = DDirectionParserENU::OutputStringByDirection(dir);
                return WDBMAttrValue(dirStr);
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto dirStr = std::string("");
                if (!value.toString(dirStr))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                if (dirStr.empty()) 
                {
                    p->setLocalEDrn(DVec3::Zero());
                    return true;
                }
                auto dir = DVec3::Zero();
                if (!DDirectionParserENU::Direction(dirStr, dir) && !DDirectionParserXYZ::Direction(dirStr, dir))
                    return false;
                p->setLocalEDrn(dir);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate));

        // string 类型，相对于世界坐标
        typeDesc.add(SAName_DrnEStrWRTWorld
            , WDBMAttrValueType::T_String
            , [](const WDNode& node)->WDBMAttrValue
            {
                auto p = dynamic_cast<const TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return WDBMAttrValue();
                }
                // 如果是0向量，则直接返回空字符串
                if (p->localEDrn().lengthSq() <= NumLimits<float>::Epsilon)
                    return WDBMAttrValue(std::string(""));
                auto dir = p->globalEDrn(node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
                auto dirStr = DDirectionParserENU::OutputStringByDirection(dir);
                return WDBMAttrValue(dirStr);
            }
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto dirStr = std::string("");
                if (!value.toString(dirStr))
                    return false;
                auto p = dynamic_cast<TTransform*>(node.transformObject());
                if (p == nullptr)
                {
                    assert(false);
                    return false;
                }
                // 如果指定的字符串为空，则直接设置为0向量
                if (dirStr.empty()) 
                {
                    p->setLocalEDrn(DVec3::Zero());
                    return true;
                }
                // 否则解析字符串中表示的方向向量
                auto dir = DVec3::Zero();
                if (!DDirectionParserENU::Direction(dirStr, dir) && !DDirectionParserXYZ::Direction(dirStr, dir))
                    return false;
                p->setGlobalEDrn(dir, node.parent() != nullptr ? node.parent()->transformObject() : nullptr);
                return true;
            }
            , ""
            , WDBMAttrDesc::Flags(FUpdate | FHidden));

    }
}

template <typename TTransform>
void BAngleAttrRegistHelpter(WDBMTypeDesc& typeDesc)
{
    auto FUpdate = WDBMAttrDesc::Flag::F_Update;

    // Beta Angle 
    static constexpr const char* SAName_BAngle = "Bangle"; // 简称 "BANG"
    // 默认
    typeDesc.add(SAName_BAngle
        , WDBMAttrValueType::T_Double
        , [](const WDNode& node)->WDBMAttrValue
        {
            auto p = dynamic_cast<const TTransform*>(node.transformObject());
            if (p == nullptr)
            {
                assert(false);
                return WDBMAttrValue();
            }
            return p->bAngle();
        }
        , [](WDNode& node, const WDBMAttrValue& value)->bool
        {
            auto bAngle = 0.0;
            if (!value.toDouble(bAngle))
                return false;
            auto p = dynamic_cast<TTransform*>(node.transformObject());
            if (p == nullptr)
            {
                assert(false);
                return false;
            }
            p->setBAngle(bAngle);
            return true;
        }
        , "BANG"
        , WDBMAttrDesc::Flags(FUpdate));
}

template <typename TTransform>
void DelpositionAttrRegistHelpter(WDBMTypeDesc& typeDesc)
{
    auto FUpdate = WDBMAttrDesc::Flag::F_Update;
    // Delposition 
    static constexpr const char* SAName_DelPos = "Delposition"; // 简称 "DELP"
    // 默认
    typeDesc.add(SAName_DelPos
        , WDBMAttrValueType::T_DVec3
        , [](const WDNode& node)->WDBMAttrValue
        {
            auto p = dynamic_cast<const TTransform*>(node.transformObject());
            if (p == nullptr)
            {
                assert(false);
                return WDBMAttrValue();
            }
            return p->delPosition();
        }
        , [](WDNode& node, const WDBMAttrValue& value)->bool
        {
            DVec3 delPos = DVec3::Zero();
            if (!value.toDVec3(delPos))
                return false;
            auto p = dynamic_cast<TTransform*>(node.transformObject());
            if (p == nullptr)
            {
                assert(false);
                return false;
            }
            p->setDelPosition(delPos);
            return true;
        }
        , "DELP"
        , WDBMAttrDesc::Flags(FUpdate));
}

WD_NAMESPACE_END

