#include "WDBMAttrEnumDictionary.h"

WD_NAMESPACE_BEGIN;

WDBMAttrValue WDBMAttrEnumDict::queryValue(const std::string& key) const
{
    for (const auto& kv : _keyValues)
    {
		if (kv.first == key)
			return kv.second;
    }
	return WDBMAttrValue();
}
std::string WDBMAttrEnumDict::queryKey(const WDBMAttrValue& value) const
{
    for (const auto& kv : _keyValues)
    {
		if (kv.second == value)
			return kv.first;
    }
	return "";
}

WDBMAttrEnumDictMgr::WDBMAttrEnumDictMgr()
{
}
WDBMAttrEnumDictMgr::~WDBMAttrEnumDictMgr()
{
	while (!_dicts.empty())
	{
		if (_dicts.back() != nullptr)
			delete _dicts.back();
		_dicts.pop_back();
	}
}
WDBMAttrEnumDict& WDBMAttrEnumDictMgr::get(const std::string_view& name)
{
	auto fItr = _map.find(name);
	if (fItr != _map.end())
		return *(fItr->second);
	_dicts.push_back(new WDBMAttrEnumDict(std::string(name)));
	auto pRet = _dicts.back();
	_map[pRet->name()] = pRet;
	return *pRet;
}
const WDBMAttrEnumDict* WDBMAttrEnumDictMgr::query(const std::string_view& name) const
{
	auto fItr = _map.find(name);
	if (fItr == _map.end())
		return nullptr;
	return fItr->second;
}
const WDBMAttrEnumDictMgr::Dicts& WDBMAttrEnumDictMgr::dicts() const
{
	return _dicts;
}

WD_NAMESPACE_END

