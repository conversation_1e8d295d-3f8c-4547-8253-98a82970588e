#pragma     once

#include "WDBMAttrDesc.h"

WD_NAMESPACE_BEGIN

inline bool WDBMAttrDesc::valid() const
{
    return !_name.empty();
}

inline const WDBMAttrValueType& WDBMAttrDesc::type() const
{
    return  _type;
}
inline void WDBMAttrDesc::setType(const WDBMAttrValueType& type)
{
    _type = type;
}

inline const std::string& WDBMAttrDesc::name() const
{
    return _name;
}
inline void WDBMAttrDesc::setName(const std::string& name)
{
    _name = name;
}
inline const std::string& WDBMAttrDesc::sampleName() const
{
    return _sampleName;
}
inline void WDBMAttrDesc::setSampleName(const std::string& sampleName)
{
    _sampleName = sampleName;
}

inline bool WDBMAttrDesc::isCustom() const
{
    return IsCustom(this->name());
}

inline const std::string& WDBMAttrDesc::category() const
{
    return _category;
}
inline void WDBMAttrDesc::setCategory(const std::string& category)
{
    _category = category;
}

inline const WDBMAttrDesc::Flags& WDBMAttrDesc::flags() const
{
    return _flags;
}
inline WDBMAttrDesc::Flags& WDBMAttrDesc::flags()
{
    return _flags;
}

inline const WDBMAttrDesc::Value& WDBMAttrDesc::defaultValue() const
{
    return _defaultValue;
}
inline const WDBMAttrDesc::Value& WDBMAttrDesc::minimumValue() const
{
    return _minimumValue;
}
inline const WDBMAttrDesc::Value& WDBMAttrDesc::maximumValue() const
{
    return _maximumValue;
}
inline const WDBMAttrDesc::Value& WDBMAttrDesc::singleStep() const
{
    return _singleStep;
}

inline void WDBMAttrDesc::setDecimals(int decimals)
{
    _decimals = decimals;
}
inline int WDBMAttrDesc::decimals() const
{
    return _decimals;
}

inline void WDBMAttrDesc::setRegexp(const std::string& regexp)
{
    _regexp = regexp;
}
inline const std::string& WDBMAttrDesc::regexp() const
{
    return _regexp;
}

inline void WDBMAttrDesc::setRefNodeModuleName(const std::string& moduleName)
{
    _refNodeModuleName = moduleName;
}
inline const std::string& WDBMAttrDesc::refNodeModuleName() const
{
    return _refNodeModuleName;
}
inline const WDBMBase* WDBMAttrDesc::refNodeModule() const
{
    return _pBMBase;
}

inline void WDBMAttrDesc::setEnumDictionaryName(const std::string& dictName)
{
    _enumDictName = dictName;
}
inline const std::string& WDBMAttrDesc::enumDictionaryName() const
{
    return _enumDictName;
}

void WDBMAttrDesc::setEnumDict(const WDBMAttrEnumDict* pEnumDict)
{
    _pEnumDict = pEnumDict;
}
inline const WDBMAttrEnumDict* WDBMAttrDesc::enumDict() const
{
    return _pEnumDict;
}

inline WDBMAttrDesc::DataFlags& WDBMAttrDesc::dataFlags()
{
    return _dataFlags;
}
inline const WDBMAttrDesc::DataFlags& WDBMAttrDesc::dataFlags() const
{
    return _dataFlags;
}

inline WDBMAttrDesc::FunctionValueSetBefore& WDBMAttrDesc::functionValueSetBefore()
{
    return _funcValueSetBefore;
}

inline WDBMAttrDesc::FunctionValueSetAfter& WDBMAttrDesc::functionValueSetAfter()
{
    return _funcValueSetAfter;
}

WD_NAMESPACE_END
