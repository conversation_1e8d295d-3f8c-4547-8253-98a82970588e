#pragma once

#include "../../serialize/WDBMNodeAttrsSerialize.h"
#include "../../WDBMBase.h"

WD_NAMESPACE_BEGIN

constexpr size_t Size_1K    = 1024;
constexpr size_t Size_16K   = 16 * Size_1K;
constexpr size_t Size_1M    = 1024 * 1024;
constexpr size_t Size_4M    = 4 * Size_1M;
constexpr size_t Size_8M    = 8 * Size_1M;

class WDNodeTree;
class WDBMBase;
class WDFmtSerialize;

/**
 * @brief 头
*/
class WDFmtHead
{
public:
    // 标识文件头
    char nMagic[3];
    // 产品名称
    char productName[16];
    // 文件版本号
    char fileVersion[16];
    // 最后一次存储的WIZDesigner App版本号
    char appVersion[16];
    // 最后一次存储的平台信息
    char platformInfo[16];


    // 创建日期和时间
    // 创建的用户名称
    // 最后一次修改的日期和时间
    // 最后一次修改的用户名称

    // 索引表首地址
    uint iTableAddr     = 0;
    // 索引表条目个数
    uint iTableCount    = 0;
    // 索引表总数据大小(字节)
    uint iTableSize     = 0;
    // 每个页的大小(字节)
    uint pageSize       = Size_4M;
    // 页的个数
    uint pageCount      = 0;
    // 空洞空间比例
    size_t holeSpace    = 0;
public:
    WDFmtHead()
    {
        this->reset();
    }
    ~WDFmtHead()
    {
    }
public:
    /**
     * @brief 重置头信息(重置为默认值)
    */
    void reset();
};
/**
 * @brief 属性块
 *  一个属性块存储一个节点的所有属性数据
*/
class WDFmtChunk
{
public:
    /**
     * @brief 块信息
    */
    struct Info
    {
        // 数据的大小, 指需要读取的数据的大小
        ushort dataSize = 0;
        // 预留
        ushort aaaa = 0;
    };
private:
    // 节点属性序列化对象
    WDBMNodeAttrsSerialize _serial;
public:
    WDFmtChunk(WDBMBase& bmBase)
        :_serial(bmBase)
    {
    }
public:
    size_t write(const WDNode& node, char* pTar);
    size_t read(WDNode& node, const char* pSrc);
};
/**
 * @brief 数据页
 *  页大小固定，且一个数据页中存放多个属性块, 且尽量保证页被填满
*/
class WDFmtPage
{
private:
    /**
     * @brief 页信息
    */
    struct Info
    {
        // 页id
        uint id = uint(-1);
        // 页已被写入的所有数据的尾
        uint end = 0;
        // 页实际写入文件的大小
        uint writeSize = 0;

        uint c;
        uint d;
        uint e;
    public:
        void reset() 
        {
            this->id        = uint(-1);
            this->end       = 0;
            this->writeSize = 0;
        }
    };
    // 文件头
    const WDFmtHead& _head;
    // 页信息
    Info _info;
    // 页数据
    std::vector<char> _data;
    // 当前页的光标位置
    uint _cursor;

    friend class WDFmtSerialize;
public:
    WDFmtPage(const WDFmtHead& head)
        :_head(head)
    {
        _data.resize(head.pageSize);
        this->reset();
    }
    ~WDFmtPage()
    {
    }
public:
    /**
     * @brief 重置页的信息以及光标
    */
    void reset() 
    {
        // 重置页信息
        _info.reset();
        // 重置游标位置
        _cursor     = 0;
    }
    /**
     * @brief 获取页信息
    */
    const Info& info() const
    {
        return _info;
    }
    /**
     * @brief 获取页的总大小
    */
    inline uint totalSize() const
    {
        return _head.pageSize;
    }
    /**
     * @brief 获取当前光标位置
    */
    inline uint cursor() const
    {
        return _cursor;
    }
    /**
     * @brief 页已被写入的所有数据的尾
    */
    inline uint end() const
    {
        return _info.end;
    }
    /**
     * @brief 从当前游标开始, 检测页是否能够写入/读取size大小的数据
    */
    inline bool check(size_t size) const
    {
        return this->cursor() + size <= this->totalSize();
    }
    /**
     * @brief 从当前游标开始，向后追加数据
     * @param pSrc 源首地址
     * @param writeSize 写入长度
     * @return 是否追加成功
     *  1. 当源首地址非法(nullptr)时，返回false
     *  2. 当写入越界时，返回false
    */
    bool append(const char* pSrc, size_t writeSize);
    /**
     * @brief 指定偏移量获取数据首地址
    */
    inline const char* data(uint offset = 0) const
    {
        return _data.data() + offset;
    }
    /**
     * @brief 指定偏移量获取数据首地址
    */
    inline char* data(uint offset = 0)
    {
        return _data.data() + offset;
    }
    /**
     * @brief 获取 当前页在文件中的偏移量
    */
    inline uint offset() const
    {
        return sizeof(_head) + _info.id * (sizeof(_info) + _head.pageSize);
    }
};


/**
 * @brief 序列化
*/
class WDFmtSerialize
{
private:
    // 文件名称
    std::string _fileName;
    // 文件指针
    FILE* _fp = nullptr;
    // 文件头数据
    WDFmtHead _head;
    // 属性块
    WDFmtChunk _chunk;
    std::vector<char> _chunkBuffer;
    // 读取使用的页缓存
    std::vector<std::pair<long long, WDFmtPage*> > _readPages;
    // 写入使用的页缓存
    WDFmtPage _writePage;

    WDBMBase& _mgr;
public:
    WDFmtSerialize(WDBMBase& mgr);
    ~WDFmtSerialize();
public:
    /**
     * @brief 打开
     * @param fileName 文件名称
     * @return 是否打开成功
    */
    bool open(const char* fileName);
    /**
     * @brief 关闭
    */
    void close();
    /**
     * @brief 保存节点到指定文件中
     *  如果文件不存在，则创建
     *  如果文件存在，则追加
     *  如果文件格式不正确，则保存失败
     * @param fileName 文件名称
     * @param nodes 要保存的根列表
     *  会递归子孙节点，但是如果节点没有属性更新标志时，不会被写入
     * @return 是否保存成功
    */
    bool save(const WDNode::Nodes& roots);
    /**
     * @brief 读取节点树
     * @param fileName 文件名称 
     * @param outRoots 输出的所有节点的根节点列表
     * @return 是否读取成功
    */
    WDNode::Nodes loadTree();
    /**
    * @brief 递归节点以及所有子孙节点，写入数据
    * @param nodes 目标节点
    */
    void write(const WDNode::Nodes& nodes);
    /**
    * @brief 读取节点的属性数据
    *  只读取列表中指定的节点，不会递归子孙节点
    */
    void read(const WDNode::Nodes& nodes);
    /**
     * @brief 删除节点（递归删除节点对应整个分支）
     * @param nodes 
    */
    void remove(const WDNode::Nodes& nodes, const WDNode::Nodes& roots);
    /**
     * @brief 更新节点数据
     * @param nodes 
    */
    void update(const WDNode::Nodes& nodes, const WDNode::Nodes& roots);
    /**
    * @brief 计算空洞比例
    * @return 当前文件的空洞率
    */
    float holeSpaceRatio();
    /**
     * @brief 更新节点数据，包括引用等
     * @param nodes 
    */
    void loadDesignNode(const WDNode::Nodes& nodes);
public:
    /**
     * @brief 当前节点改变，加载当前节点的属性
     * @param currNode 
     * @param prevNode 
     * @param sender 
    */
    inline void onReadNodeAttr(WDNode* currNode, WDNode*, WDNodeTree*)
    {
        if (currNode == nullptr || _mgr.root().get() == currNode)// 根节点跳过
            return ;
        readNode(*currNode);
    }
    /**
     * @brief 节点被添加到场景，加载该节点的节点引用
    */
    inline void onUpdateNodeData(WDNode* currNode, WDNode*, WDNodeTree*)
    {
        if (currNode == nullptr || _mgr.root().get() == currNode)// 根节点跳过
            return ;
        loadDesignNode({currNode->toPtr<WDNode>()});
    }
public:
    /**
    * @brief 重组织文件
    * 由于删除节点等原因，文件中会逐渐出现空洞
    * 如果这些空洞较多则会使文件“虚胖”
    * 因此这种情况下需要重新组织文件结构从而消除空洞
    * @return 
    */
    static bool ReorganizeFile(const char* fileName, WDBMBase& mgrBase);
private:
    /**
     * @brief 写入单个节点的属性数据
     *  如果节点属性未被加载，表明节点属性肯定未被修改，则跳过写入
    */
    void writeNode(WDNode& node);
    /**
     * @brief 读取单个节点的属性数据
    */
    void readNode(WDNode& node);
    /**
     * @brief 删除节点
     * @param node 
    */
    void removeNode(WDNode& node);
    /**
     * @brief 更新节点数据
     * @param node 
    */
    void updateNode(WDNode& node);
    /**
     * @brief 追加数据到当前页，如果当前页已满，则自动换页（新建一页）
     * @param pData 数据首地址
     * @param size 数据大小
     * @param pageId 输出的页的Id
     * @param chunkAddr 输出的数据块在页中的首地址
     * @return 实际的写入的大小
    */
    size_t appendToPage(const char* pData, size_t size, ushort& pageId, uint& chunkAddr);
    /**
     * @brief 换页，当前页已满，更换到下一个页（相当于新建一页并切到这个页上)
    */
    bool createNovPage();
        /**
     * @brief 获取页
     * @param id 页ID
    */
    WDFmtPage* getPage(ushort id);
    /**
     * @brief 回写文件头
    */
    void writeHead();
    /**
     * @brief 写入页
     * @param page 页对象
     * @param bFull 是否写入整个页
     * @return 页的末尾地址
    */
    size_t writePage(WDFmtPage& page, bool bFull);
    /**
     * @brief 写入索引表
    */
    void writeIndexTable(const WDNode::Nodes& nodes);
    /**
     * @brief 写入索引表的某一项
     * @param node 节点
     * @param outCount 统计写入个数
     * @param funcWrite 回调
    */
    void writeIndexTableItem(const WDNode& node
        , uint& outCount
        , const std::function<void(const void* pData, size_t wSize)>& funcWrite);
    /**
     * @brief 读取页
     * @param id 页id
     * @param page 页对象
    */
    void readPage(WDFmtPage& page);
    /**
     * @brief 读取索引表
    */
    WDNode::Nodes readIndexTable();
    /**
     * @brief 获取当前系统时间戳
    */
    long long timeStamp();
};

WD_NAMESPACE_END