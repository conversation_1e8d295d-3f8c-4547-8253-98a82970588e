#include "WDBMRefCollector.h"
#include "../WDCore.h"
#include "dataType/WDBMNodeRef.h"
#include "dataType/WDBMGeometryRef.h"

#include "geometry/WDGeometryMgr.h"
#include "catalog/WDBMCatalog.h"
#include "design/WDBMDesign.h"

WD_NAMESPACE_BEGIN;


void WDBMRefUpdater::addCRef(WDBMNodeRef* pRef)
{
    AddRef(_cRefs, pRef);
}
void WDBMRefUpdater::updateCRefs(const WDBMCatalog& cMgr)
{
    auto pRoot = cMgr.root();
    if (pRoot == nullptr)
        return ;
    UpdateRefs(*pRoot, _cRefs);
}
void WDBMRefUpdater::addDRef(WDBMNodeRef* pRef)
{
    AddRef(_dRefs, pRef);
}
void WDBMRefUpdater::updateDRefs(const WDBMDesign& dMgr)
{
    auto pRoot = dMgr.root();
    if (pRoot == nullptr)
        return ;
    UpdateRefs(*pRoot, _dRefs);
}

void WDBMRefUpdater::addDGeoRef(WDBMGeometryRef* pRef, int gType)
{
    AddGeoRef(_dGeoRefs, pRef, gType);
}
void WDBMRefUpdater::updateDGeoRefs(const WDBMDesign& dMgr)
{
    UpdateGeoRefs(dMgr.core().geometryMgr(), _dGeoRefs);
}

void WDBMRefUpdater::AddRef(RefsMap& map, WDBMNodeRef* pRef)
{
    if (pRef == nullptr || pRef->refNodeId().isNull())
        return;
    auto itr = map.find(pRef->refNodeId());
    if (itr == map.end())
    {
        Refs refs = { pRef };
        map.emplace(pRef->refNodeId(), std::move(refs));
    }
    else
    {
        itr->second.push_back(pRef);
    }
}
void WDBMRefUpdater::UpdateRefs(WDNode& root, RefsMap& map)
{
    if (map.empty())
        return;

    // 递归更新节点引用
    WDNode::RecursionHelpter(root, [](RefsMap& param, WDNode& node)
        {
            const WDUuid& id = node.uuid();
            auto fItr = param.find(id);
            if (fItr != param.end())
            {
                Refs& refs = fItr->second;
                WDNode::SharedPtr pSNode = WDNode::ToShared(&node);
                for (auto pRef : refs)
                {
                    pRef->setRefNode(pSNode);
                }
            }
        }, map);
}

void WDBMRefUpdater::AddGeoRef(GeoRefsMap& map, WDBMGeometryRef* pRef, int type)
{
    if (pRef == nullptr || pRef->refGeoId().isNull())
        return;
    auto key = std::make_pair(pRef->refGeoId(), type);
    auto itr = map.find(key);
    if (itr == map.end())
    {
        GeoRefs refs = { pRef };
        map.emplace(std::move(key), std::move(refs));
    }
    else
    {
        itr->second.push_back( pRef );
    }
}
void WDBMRefUpdater::UpdateGeoRefs(WDGeometryMgr& geoMgr, GeoRefsMap& map)
{
    if (map.empty())
        return;

    // 更新查询缓存
    geoMgr.updatePolyhedronQueryCatch();
    for (auto pItr = map.begin(); pItr != map.end(); ++pItr)
    {
        // 查询uuid对应几何体
        const auto& uuid = pItr->first.first;
        const auto& type = pItr->first.second;
        auto pGeo = geoMgr.queryPolyhedronFromCatch(uuid, (WDGeometryType)(type));
        assert(pGeo != nullptr);
        for (auto& geoRef : pItr->second)
        {
            geoRef->setRefGeo(pGeo);
        }
    }
    // 清除查询缓存
    geoMgr.clearPolyhedronQueryCatch();
}

size_t WDBMRefUpdater::InvalidRefCount(RefsMap& map)
{
    size_t invalidCnt = 0;
    for (const auto& refs : map)
    {
        for (const auto& pRef : refs.second)
        {
            if (pRef->valid())
                continue;
            invalidCnt++;
        }
    }
    return invalidCnt;
}


WD_NAMESPACE_END