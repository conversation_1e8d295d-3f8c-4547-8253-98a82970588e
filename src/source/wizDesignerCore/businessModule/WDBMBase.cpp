#include "WDBMBase.h"
#include "typeMgr/WDBMTypeMgr.h"
#include "WDBMRefCollector.h"
#include "WDBMPermissionMgr.h"
#include "WDBMClaimMgr.h"
#include "WDBMAuditObjectMgr.h"
#include "WDBMColorTable.h"
#include "typeMgr/WDBMAttrEnumDictionary.h"

#include "../common/WDFileInfo.h"
#include "WDRapidjson.h"
#include "WDRapidxml.h"
#include "serialize/private/WDBMNodeSerialize.h"
#include "../WDTranslate.h"
#include <fstream>
#include <filesystem>
#include "serialize/private/WDBMFmtSerialize.h"
#include "../nodeTree/WDNodeTree.h"
#include "../businessModule/WDBMColorIndexMgr.h"

#include "../common/WDTimestamp.hpp"
#include "../log/WDLoggerPort.h"
#include "../undoRedo/WDUndoStack.h"
#include "../scene/WDScene.h"
#include "../viewer/WDViewer.h"
#include "WDBDBase.h"

WD_NAMESPACE_BEGIN;

/**
 * @brief 去重，并且查找列表中是否同时存在节点以及其祖先节点，如果存在，只保留祖先节点
 * @param nodes 
 * @param func 
*/
static void FilterNodes(const WDNode::Nodes& nodes, std::function<void(WDNode::SharedPtr)> func)
{
    if (!func || nodes.empty())
        return ;

    // 节点集合,为了查找加速
    std::set<WDNode::SharedPtr> fNodes;
    for (auto pNode : nodes)
    {
        if (pNode == nullptr)
            continue;
        fNodes.insert(pNode);
    }

    for (int i = 0; i < nodes.size(); ++i)
    {
        auto pNode = nodes[i];
        if (pNode == nullptr)
            continue;

        // 尝试查找集合中的祖先
        WDNode::SharedPtr pTNode = pNode->parent();
        while (pTNode != nullptr)
        {
            auto fItr = fNodes.find(pTNode);
            if (fItr != fNodes.end())
                break;
            pTNode = pTNode->parent();
        }
        // 没有找到集合中的祖先节点
        if (pTNode == nullptr)
            func(pNode);
    }
}

/**
* @brief 判断节点是否为自动生成的
* @param node 
* @return 
*/
static bool IsAutoGen(WDNode& node)
{
    return node.getTypeDesc()->flags().hasFlag(WDBMTypeDesc::F_AutoGeneration);
}
/**
* @brief 获取实际next(跳过自动生成的节点，比如tubi）
* @param node 
* @return 
*/
static WDNode::SharedPtr GetRealNext(WDNode& node)
{
    auto next = node.nextBrother();
    while (next != nullptr && IsAutoGen(*next))
    {
        next = next->nextBrother();
    }
    return next;
}

/**
 * @brief 节点创建命令
*/
class NodeCreateCommand : public WDUndoCommand 
{
private:
    WDBMBase& _bmBase;
    // 当前创建的节点
    WDNode::SharedPtr _pNode;
    // 父节点
    WDNode::WeakPtr _pParent;
    // 下一个节点，用于插入
    WDNode::WeakPtr _pNext;
    // 节点类型名称
    std::string _typeName;
    // 节点名称
    std::string _name;
public:
    NodeCreateCommand(WDBMBase& bmBase
        , WDNode::SharedPtr pParent
        , const std::string_view& typeName
        , const std::string_view& name
        , WDNode::SharedPtr pNext
        , WDUndoCommand* pParentCmd = nullptr)
        : WDUndoCommand("Node Create Command", pParentCmd)
        , _bmBase(bmBase)
        , _pParent(pParent)
        , _typeName(typeName)
        , _name(name)
        , _pNext(pNext)
    {
    }
public:
    virtual void redoP() override 
    {
        // 创建节点
        auto pParent = _pParent.lock();
        if (pParent != nullptr)
        {
            if (_pNode != nullptr)
                _bmBase.setParent(_pNode, pParent, _pNext.lock(), false);
            else
                _pNode = _bmBase.create(pParent, _typeName, _pNext.lock(), _name);
        }

        if (_pNode != nullptr)
            _bmBase.claimMgr().newCreatedNodes().add({ _pNode });

        WDUndoCommand::redoP();
    }
    virtual void undoP() override 
    {
        WDUndoCommand::undoP();

        if (_pNode != nullptr)
            _bmBase.claimMgr().newCreatedNodes().remove({ _pNode });

        // 销毁节点
        if (_pNode != nullptr)
            _bmBase.destroy(_pNode);
    }
};

class NodeCreatedCommand : public WDUndoCommand 
{
private:
    WDBMBase&           _bmBase;
    bool                _bFirstRedo = true;
    struct Data
    {
        WDNode::SharedPtr pNode;
        WDNode::WeakPtr pParent;
        WDNode::WeakPtr pNext;
    };
    std::vector<Data> _nodes;
public:
    NodeCreatedCommand(WDBMBase& bmBase, const WDNode::Nodes& nodes, WDUndoCommand* pParentCmd = nullptr)
        : WDUndoCommand("Node Created Command", pParentCmd)
        , _bmBase(bmBase)
    {
        _nodes.reserve(nodes.size());

        FilterNodes(nodes, [this](WDNode::SharedPtr pNode)
        {
            if (pNode == nullptr)
                return ;
            this->_nodes.push_back({
                  pNode
                , pNode->parent()
                , GetRealNext(*pNode)});
        });
    }
public:
    virtual void redoP() override 
    {
        for (const auto& node : _nodes)
        {
            if (node.pNode != nullptr)
                _bmBase.claimMgr().newCreatedNodes().add({ node.pNode });
        }

        // 第一次直接跳过
        if (_bFirstRedo)
        {
            _bFirstRedo = false;
            return;
        }
        // 设置回原来的位置
        for (const auto& node : _nodes)
        {
            if (node.pNode != nullptr && !node.pParent.expired())
                _bmBase.setParent(node.pNode, node.pParent.lock(), node.pNext.lock(), false);
        }

        WDUndoCommand::redoP();
    }
    virtual void undoP() override 
    {
        WDUndoCommand::undoP();

        for (const auto& node : _nodes)
        {
            if (node.pNode != nullptr)
                _bmBase.claimMgr().newCreatedNodes().remove({ node.pNode });
        }

        // 从系统中移除
        for (const auto& node : _nodes)
        {
            if (node.pNode != nullptr)
                _bmBase.destroy(node.pNode);
        }
        // 创建节点时可能存在父节点已经在场景中的情况
        // 这种情况下addtoscene命令在undo时不会移动节点,此时节点已被销毁,调用场景重绘避免场景不更新导致已被销毁的节点仍然显示在场景中
        _bmBase.core().needRepaint();
    }
};
/**
 * @brief 节点镜像命令
*/
class NodeMirrorCommand : public WDUndoCommand
{
private:
    WDBMBase&   _bmBase;
    std::vector<WDNode::WeakPtr> _nodes;
    DVec3 _normal;
    DVec3 _center;
public:
    /**
    * @brief 
    * @param command 镜像命令
    * @param pNode 节点
    * 一下数据均为世界坐标系
    * @param normal 镜像面法线
    * @param origin 镜像面所在点
    */
    NodeMirrorCommand(WDBMBase& bmBase
        , WDNode::Nodes nodes
        , const DVec3& normal
        , const DVec3& center
        , WDUndoCommand* parent = nullptr)
        : WDUndoCommand("MirrorCommand", parent), _bmBase(bmBase), _normal(normal), _center(center)
    {
        _nodes.reserve(nodes.size());
        for (auto& each : nodes)
        {
            if (each != nullptr)
                _nodes.push_back(each);
        }
    }
    virtual ~NodeMirrorCommand()
    {

    }

    virtual void redoP() override
    {
        for (auto& each : _nodes)
        {
            auto pMirrorNode = each.lock();
            if (pMirrorNode == nullptr)
                return;
            pMirrorNode->mirror(_normal, _center);
            pMirrorNode->triggerUpdate(true);
        }
        _bmBase.core().needRepaint();
    }
    virtual void undoP() override
    {
        for (auto& each : _nodes)
        {
            auto pMirrorNode = each.lock();
            if (pMirrorNode == nullptr)
                return;
            pMirrorNode->mirror(_normal, _center);
            pMirrorNode->triggerUpdate(true);
        }
        _bmBase.core().needRepaint();
    }
};

/**
 * @brief 节点设置属性命令
*/
class NodeSetAttributeCommand : public WDUndoCommand
{
private:
    // 当前设置属性的节点
    WDNode::WeakPtr _pNode;
    // 属性名称
    std::string _attrName;
    // 当前属性值
    WDBMAttrValue _currValue;
    // 设置前的属性值
    WDBMAttrValue _prevValue;
public:
    NodeSetAttributeCommand(WDNode::SharedPtr pNode
        , const std::string_view& attrName
        , const WDBMAttrValue& value
        , WDUndoCommand* pParentCmd = nullptr)
        : WDUndoCommand("Node SetAttribute Command", pParentCmd)
        , _pNode(pNode)
        , _attrName(attrName)
        , _currValue(value)
    {
        // 先获取设置前的属性值保存
        _prevValue = pNode->getAttribute(_attrName);
    }
public:
    virtual void redoP() override
    {
        auto pNode = _pNode.lock();
        if (pNode == nullptr)
            return;

        // 设置当前属性
        pNode->setAttribute(_attrName, _currValue);

        WDUndoCommand::redoP();
    }
    virtual void undoP() override
    {
        WDUndoCommand::undoP();

        auto pNode = _pNode.lock();
        if (pNode == nullptr)
            return;

        // 设置前一次的属性
        pNode->setAttribute(_attrName, _prevValue);
    }
};

class NodeSetedAttributeCommand : public WDUndoCommand
{
private:
    // 当前设置属性的节点
    WDNode::WeakPtr _pNode;
    // 属性名称
    std::string _attrName;
    // 当前属性值
    WDBMAttrValue _currValue;
    // 设置前的属性值
    WDBMAttrValue _prevValue;
    // 第一次redo
    bool _bFirstRedo = true;
public:
    NodeSetedAttributeCommand(WDNode::SharedPtr pNode
        , const std::string_view& attrName
        , const WDBMAttrValue& prevValue
        , WDUndoCommand* pParentCmd = nullptr)
        : WDUndoCommand("Node SetedAttribute Command", pParentCmd)
        , _pNode(pNode)
        , _attrName(attrName)
        , _prevValue(prevValue)
    {
        // 获取设置后的属性值保存
        assert (pNode != nullptr);
        if (pNode != nullptr)
        {
            _currValue = pNode->getAttribute(attrName);
        }
    }
public:
    virtual void redoP() override
    {
        // 第一次直接跳过
        if (_bFirstRedo)
        {
            _bFirstRedo = false;
            return;
        }

        auto pNode = _pNode.lock();
        if (pNode == nullptr)
            return;

        // 设置当前属性
        pNode->setAttribute(_attrName, _currValue);

        WDUndoCommand::redoP();
    }
    virtual void undoP() override
    {
        WDUndoCommand::undoP();

        auto pNode = _pNode.lock();
        if (pNode == nullptr)
            return;

        // 设置前一次的属性
        pNode->setAttribute(_attrName, _prevValue);
    }
};

class NodeSetedAttributesCommand : public WDUndoCommand
{
private:
    // 当前设置属性的节点对应已设置属性列表
    std::vector<WDBMBase::CmdNodeAttributes> _prevNodeAttrs;
    std::vector<WDBMBase::CmdNodeAttributes> _curNodeAttrs;
    // 第一次redo
    bool _bFirstRedo = true;
public:
    NodeSetedAttributesCommand(const std::vector<WDBMBase::CmdNodeAttributes>& prevAttrs
        , WDUndoCommand* pParentCmd = nullptr)
        : WDUndoCommand("Nodes SetedAttributes Command", pParentCmd)
        , _prevNodeAttrs(prevAttrs)
    {
        // 获取设置后的属性值保存
        for (const auto& [pWNode, attrs] : _prevNodeAttrs)
        {
            const auto& pNode = pWNode.lock();
            if (pNode == nullptr)
                continue;

            std::vector<WDBMBase::CmdAttribute> curAttrs;
            for (const auto& [key, value] : attrs)
            {
                const auto& curValue = pNode->getAttribute(key);
                assert(curValue.valid());
                if (!curValue.valid())
                    continue;
                curAttrs.emplace_back(key, curValue);
            }
            _curNodeAttrs.emplace_back(pNode, curAttrs);
        }
    }
public:
    virtual void redoP() override
    {
        // 第一次直接跳过
        if (_bFirstRedo)
        {
            _bFirstRedo = false;
            return;
        }

        for (const auto& [pWNode, attrs] : _curNodeAttrs)
        {
            const auto& pNode = pWNode.lock();
            if (pNode == nullptr)
                continue;

            for (const auto& [key, value] : attrs)
            {
                // 设置当前属性
                pNode->setAttribute(key, value);
            }

            pNode->triggerUpdate(true);
        }

        WDUndoCommand::redoP();
    }
    virtual void undoP() override
    {
        WDUndoCommand::undoP();

        for (const auto& [pWNode, attrs] : _prevNodeAttrs)
        {
            const auto& pNode = pWNode.lock();
            if (pNode == nullptr)
                continue;

            for (const auto& [key, value] : attrs)
            {
                // 设置当前属性
                pNode->setAttribute(key, value);
            }

            pNode->triggerUpdate(true);
        }
    }
};

/**
 * @brief 节点销毁命令
*/
class NodeDestroyCommand : public WDUndoCommand
{
private:
    WDBMBase& _bmBase;
    // 销毁的节点列表
    struct Data
    {
        WDNode::SharedPtr pNode;
        WDNode::WeakPtr pParent;
        WDNode::WeakPtr pNext;
    };
    std::vector<Data> _nodes;

    // 如果有自动生成的节点(例如直管(TUBI))被删除时，这里记录被删除节点所属的父节点，redo时，需要触发这些父节点的更新以再次自动生成这些节点
    std::vector<WDNode::WeakPtr> _autoNodeParents;
public:
    NodeDestroyCommand(WDBMBase& bmBase, const WDNode::Nodes& nodes, WDUndoCommand* pParentCmd = nullptr)
        : WDUndoCommand("Node Destroy Command", pParentCmd)
        , _bmBase(bmBase)
    {
        _nodes.reserve(nodes.size());
        FilterNodes(nodes, [this](WDNode::SharedPtr pNode)
            {
                if (pNode == nullptr)
                    return ;
                // 对于自动生成的节点，例如直管(TUBI)节点，这里不用加到撤销栈中, 因为目前在分支更新后会重新生成一些新的直管
                // 因此这里完成之后这些自动生成的节点将会被自动释放，所以这里发送销毁事件
                if (IsAutoGen(*pNode))
                {
                    pNode->sendDestroy();
                    if (pNode->parent() != nullptr)
                    {
                        // 判断是否已添加到当前数组中，已添加则不再添加
                        bool exitFlag = false;
                        auto pParent = pNode->parent();
                        for(auto pBran : _autoNodeParents)
                        {
                            if( pBran.lock() == pParent)
                            {
                                exitFlag = true;
                                break;
                            }
                        }
                        if(!exitFlag)
                        {
                            _autoNodeParents.push_back(pParent);
                        }
                    }
                    return ;
                }
                this->_nodes.push_back({
                      pNode
                    , pNode->parent()
                    , GetRealNext(*pNode)});
            });
    }
public:
    virtual void redoP() override
    {
        for (const auto& node : _nodes) 
        {
            if (node.pNode != nullptr)
                _bmBase.claimMgr().deletedNodes().add({ node.pNode });
        }

        // 从系统中移除
        for (const auto& node : _nodes) 
        {
            _bmBase.destroy(node.pNode);
        }

        WDUndoCommand::redoP();
        _bmBase.core().needRepaint();
    }
    virtual void undoP() override
    {
        WDUndoCommand::undoP();

        for (const auto& node : _nodes) 
        {
            if (node.pNode != nullptr)
                _bmBase.claimMgr().deletedNodes().remove({ node.pNode });
        }

        // 先触发自动生成节点父节点的更新
        for (const auto& node: _autoNodeParents)
        {
            auto pNode = node.lock();
            if (pNode == nullptr)
                continue;
            pNode->triggerUpdate(true);
        }

        // 添加进系统中
        for (auto itr = _nodes.rbegin(); itr != _nodes.rend(); ++itr)
        {
            auto pParent = itr->pParent.lock();
            if (pParent == nullptr || itr->pNode == nullptr)
                continue;
            _bmBase.setParent(itr->pNode, pParent, itr->pNext.lock(), false);
        }

        _bmBase.core().needRepaint();
    }
};

/**
* @brief 添加节点到场景的命令
*/
class SceneAddCommand : public WDUndoCommand
{
private:
    WDBMBase& _bmBase;
    // 这里记录所有需要添加的根节点
    std::vector<WDNode::WeakPtr> _rootNodes;
    // 这里记录添加节点及其所有子孙节点的状态(是否在场景中),用来判断移除时是否需要一卒后
    std::vector<std::pair<WDNode::WeakPtr, bool>> _nodes;
public:
    SceneAddCommand(WDBMBase& bmBase, const WDNode::Nodes& nodes, WDUndoCommand* pParentCmd = nullptr)
        : WDUndoCommand("Scene Add Command", pParentCmd)
        , _bmBase(bmBase)
    {
        _rootNodes.reserve(nodes.size());
        _nodes.reserve(nodes.size());
        FilterNodes(nodes, [this](WDNode::SharedPtr pNode)
        {
            if (pNode == nullptr)
                return ;
            this->_rootNodes.push_back(pNode);
            // 递归当前节点的所有子孙
            WDNode::RecursionHelpter(*pNode, [this](WDNode& node)
            {
                _nodes.emplace_back(std::make_pair(WDNode::ToShared(&node), node.flags().hasFlag(WDNode::F_InTheScene)));
            });
        });
    }
public:
    virtual void redoP() override
    {
        auto& scene = _bmBase.core().scene();
        for (const auto& node : _rootNodes) 
        {
            auto pNode = node.lock();
            if (pNode == nullptr)
                continue;
            scene.add(pNode);
        }

        WDUndoCommand::redoP();
        _bmBase.core().needRepaint();
    }
    virtual void undoP() override
    {
        WDUndoCommand::undoP();

        auto& scene = _bmBase.core().scene();
        for (const auto& each : _nodes) 
        {
            auto pNode = each.first.lock();
            if (pNode == nullptr)
                continue;
            if (each.second)
            {
                if (!pNode->flags().hasFlag(WDNode::Flag::F_InTheScene))
                    scene.add(pNode);
            }
            else
            {
                if (pNode->flags().hasFlag(WDNode::Flag::F_InTheScene))
                    scene.remove(pNode);
            }
        }

        _bmBase.core().needRepaint();
    }
};
/**
* @brief 从场景移除的命令
*/
class SceneRemoveCommand : public WDUndoCommand
{
private:
    WDBMBase& _bmBase;
    // 这里记录所有需要添加的根节点
    std::vector<WDNode::WeakPtr> _rootNodes;
    // 这里记录添加节点及其所有子孙节点的状态(是否在场景中),用来判断移除时是否需要一卒后
    std::vector<std::pair<WDNode::WeakPtr, bool>> _nodes;
public:
    SceneRemoveCommand(WDBMBase& bmBase, const WDNode::Nodes& nodes, WDUndoCommand* pParentCmd = nullptr)
        : WDUndoCommand("Scene Remove Command", pParentCmd)
        , _bmBase(bmBase)
    {
        _nodes.reserve(nodes.size());
        _rootNodes.reserve(nodes.size());
        FilterNodes(nodes, [this](WDNode::SharedPtr pNode)
        {
            if (pNode == nullptr)
                return;
            this->_rootNodes.push_back(pNode);
            // 递归当前节点的所有子孙
            WDNode::RecursionHelpter(*pNode, [this](WDNode& node)
            {
                this->_nodes.emplace_back(std::make_pair(WDNode::ToShared(&node), node.flags().hasFlag(WDNode::F_InTheScene)));
            });
        });
    }
public:
    virtual void redoP() override
    {
        auto& scene = _bmBase.core().scene();
        for (const auto& node : _rootNodes) 
        {
            auto pNode = node.lock();
            if (pNode == nullptr)
                continue;
            scene.remove(pNode);
        }

        WDUndoCommand::redoP();
        _bmBase.core().needRepaint();
    }
    virtual void undoP() override
    {
        WDUndoCommand::undoP();

        auto& scene = _bmBase.core().scene();
        for (const auto& each : _nodes) 
        {
            auto pNode = each.first.lock();
            if (pNode == nullptr)
                continue;
            if (each.second)
            {
                if (!pNode->flags().hasFlag(WDNode::Flag::F_InTheScene))
                    scene.add(pNode);
            }
            else
            {
                if (pNode->flags().hasFlag(WDNode::Flag::F_InTheScene))
                    scene.remove(pNode);
            }
        }

        _bmBase.core().needRepaint();
    }
};
/**
* @brief 节点移动命令
*/
class NodeHierarchyMoveCommand : public WDUndoCommand
{
private:
    WDBMBase& _bmBase;
    // 这里记录所有需要添加的根节点
    WDBMBase::NodeHierarchyMoveInfos infos;
public:
    NodeHierarchyMoveCommand(WDBMBase& bmBase, const WDBMBase::NodeHierarchyMoveInfos& infos, WDUndoCommand* pParentCmd = nullptr)
        : WDUndoCommand("Scene Remove Command", pParentCmd)
        , _bmBase(bmBase)
        , infos(infos)
    {
    }
public:
    virtual void redoP() override
    {
        for (auto itr = infos.begin(); itr != infos.end(); ++itr)
        {
            auto pNode      = itr->pNode.lock();
            auto pParent    = itr->pTargetParent.lock();
            auto pNextBro   = itr->pTargetNextNode.lock();
            if (pNode == nullptr || pParent == nullptr)
            {
                assert(false);
                continue;
            }
            auto tmpParent = pNode->parent();
            auto tmpNextBro = nextNotAutoGen(pNode);
            _bmBase.setParent(pNode, pParent, pNextBro);
            itr->pTargetParent = tmpParent;
            itr->pTargetNextNode = tmpNextBro;
        }
        WDUndoCommand::redoP();
        _bmBase.core().needRepaint();
    }
    virtual void undoP() override
    {
        WDUndoCommand::undoP();
        for (auto itr = infos.rbegin(); itr != infos.rend(); ++itr)
        {
            auto pNode      = itr->pNode.lock();
            auto pParent    = itr->pTargetParent.lock();
            auto pNextBro   = itr->pTargetNextNode.lock();
            if (pNode == nullptr || pParent == nullptr)
            {
                assert(false);
                continue;
            }
            auto tmpParent = pNode->parent();
            auto tmpNextBro = nextNotAutoGen(pNode);
            _bmBase.setParent(pNode, pParent, pNextBro);
            itr->pTargetParent = tmpParent;
            itr->pTargetNextNode = tmpNextBro;
        }
        _bmBase.core().needRepaint();
    }

private:
    WD::WDNode::SharedPtr nextNotAutoGen(WD::WDNode::SharedPtr pNode)
    {
        if (pNode == nullptr)
            return nullptr;
        auto pNext = pNode->nextBrother();
        while (pNext != nullptr)
        {
            if (!IsAutoGen(*pNext))
                break;
            pNext = pNext->nextBrother();
        }

        return pNext;
    }
};

WDBMBase::WDBMBase(WDCore& core, const std::string& name)
    : _core(core)
    , _name(name)
{
    assert(!_name.empty() && "模块名称不能为空");

    _pAttrEnumDictMgr   = new WDBMAttrEnumDictMgr();
    _pTypeMgr           = new WDBMTypeMgr(*this);
    _pPermissionMgr     = new WDBMPermissionMgr(core);
    _pClaimMgr          = new WDBMClaimMgr(*this);
    _pAuditObjectMgr    = new WDBMAuditObjectMgr();
    _pColorIndexMgr     = new WDBMColorIndexMgr(*this);
    _pColorTable        = new WDBMColorTable();
    _serialize          = new WDBMNodeSerialize(*this);
    _fmtSerialize       = new WDFmtSerialize(*this);

    _tsTypeCxtName      = _name + ":Type";
    _tsAttrCxtName      = _name + ":Attribute";
    _tsEDKeyCxtName     = _name + ":EDKey";
}
WDBMBase::~WDBMBase()
{
    // 清除缓存的数据
    if (_pClaimMgr != nullptr)
    {
        assert(_pClaimMgr->newCreatedNodes().nodes().empty()
            && _pClaimMgr->deletedNodes().nodes().empty()
            && "注意，此次添加的节点没有被自动移除，可能是协同处理流程还未完善，理论上到这里节点列表应该是空的!");
        _pClaimMgr->newCreatedNodes().clear();
        _pClaimMgr->deletedNodes().clear();
    }

    _pRoot = nullptr;

    if (_fmtSerialize != nullptr)
    {
        delete _fmtSerialize;
        _fmtSerialize = nullptr;
    }
    if (_serialize != nullptr)
    {
        delete _serialize;
        _serialize = nullptr;
    }
    if (_pColorTable != nullptr)
    {
        delete _pColorTable;
        _pColorTable = nullptr;
    }
    
    if (_pAuditObjectMgr != nullptr)
    {
        delete _pAuditObjectMgr;
        _pAuditObjectMgr = nullptr;
    }
    if (_pPermissionMgr != nullptr)
    {
        delete _pPermissionMgr;
        _pPermissionMgr = nullptr;
    }
    if (_pClaimMgr != nullptr)
    {
        delete _pClaimMgr;
        _pClaimMgr = nullptr;
    }
    if (_pTypeMgr != nullptr)
    {
        delete _pTypeMgr;
        _pTypeMgr = nullptr;
    }
    if (_pAttrEnumDictMgr != nullptr) 
    {
        delete _pAttrEnumDictMgr;
        _pAttrEnumDictMgr = nullptr;
    }
    if (_pColorIndexMgr != nullptr)
    {
        delete _pColorIndexMgr;
        _pColorIndexMgr = nullptr;
    }
}

bool WDBMBase::init()
{
    // 收集翻译文件列表
    auto trFiles = this->collectTranslationFiles();
    WDTranslate::Instance()->load(trFiles);

    return  this->initP();
}
void WDBMBase::uninit()
{
    this->uninitP();
    _pRoot = nullptr;
}

WDNode::SharedPtr WDBMBase::findParentWithType(WDNode& currNode, const std::string_view& typeName) const
{
    WDNode::SharedPtr pParent = WDNode::ToShared(&currNode);
    while (pParent != nullptr)
    {
        // 校验父类型是否支持挂载当前类型
        auto parType = pParent->type();
        bool bCheck = this->typeMgr().checkParent(typeName, parType);

        if (bCheck)
            return pParent;
        else
            pParent = pParent->parent();
    }
    return nullptr;
}

bool WDBMBase::nameValid(const std::string_view& name) const
{
    if (name.empty())
        return true;
    for (auto itr = name.begin(); itr != name.end(); ++itr) 
    {
        auto ch = *itr;
        switch (ch)
        {
        case ' ':
            return false;
            break;
        case '@':
            return false;
            break;
        case '$':
            return false;
            break;
        default:
            break;
        }
    }
    return true;
}
bool WDBMBase::nameExists(const std::string_view& name) const
{
    if (name.empty())
        return false;
    if (_pRoot == nullptr)
        return false;
    bool bExists = false;
    WDNode::RecursionHelpterR(*_pRoot, [&name, &bExists](WDNode& node)
        {
            if (!node.isNamed())
                return false;
            if (node.srcName() != name)
                return false;
            bExists = true;
            return true;
        });
    return bExists;
}

std::set<std::string_view> WDBMBase::nameExists(const std::vector<std::string>& names)const
{
    std::set<std::string_view> exitNames;

    if (_pRoot == nullptr)
        return exitNames;

    WDNode::RecursionHelpter(*_pRoot, [&names, &exitNames](WDNode& node)
        {
            if (!node.isNamed())
                return;

            // 判断当前节点名称names中是否存在
            for(auto& name : names)
            {
                if (node.srcName() == name)
                {
                    exitNames.insert(name);
                    return;
                }
            }
        }
    );

    return exitNames;
}

WDNode::SharedPtr WDBMBase::create(WDNode::SharedPtr pParent, const std::string_view& typeName
    , const std::string_view& name)
{
    // 指定的父节点不能为空
    if (pParent == nullptr)
    {
        assert(false && "指定的父节点不能为nullptr!");
        return nullptr;
    }
    // 校验指定的父节点是否属于当前模块
    if (pParent->getBMBase() != this)
    {
        assert(false && "指定的父节点不属于当前模块!");
        return nullptr;
    }
    //使用类型节点构建器创建节点
    auto pRNode = this->typeMgr().build(*this, typeName, name, pParent);
    if (pRNode != nullptr)
    {
        pRNode->setFlags(pRNode->flags().addFlags(WDNode::Flag::F_Editted, WDNode::Flag::F_EdittedStatus));
    }
    return pRNode;
}
WDNode::SharedPtr WDBMBase::create(const std::string_view& typeName, const std::string_view& name)
{
    auto pRNode = this->typeMgr().build(*this, typeName, name);
    if (pRNode != nullptr)
        pRNode->setFlags(pRNode->flags().addFlags( WDNode::Flag::F_Editted, WDNode::Flag::F_EdittedStatus ));
    return pRNode;
}
WDNode::SharedPtr WDBMBase::create(WDNode::SharedPtr pParent
    , const std::string_view& typeName
    , WDNode::SharedPtr pNextNode
    , const std::string_view& name)
{
    // 指定的父节点不能为空
    if (pParent == nullptr)
    {
        assert(false && "指定的父节点不能为nullptr!");
        return nullptr;
    }
    // 校验指定的父节点是否属于当前模块
    if (pParent->getBMBase() != this)
    {
        assert(false && "指定的父节点不属于当前模块!");
        return nullptr;
    }
    auto pRNode = this->typeMgr().build(*this, typeName, name, pParent, pNextNode);
    if (pRNode != nullptr)
    {
        pRNode->setFlags(pRNode->flags().addFlags(WDNode::Flag::F_Editted, WDNode::Flag::F_EdittedStatus));
    }

    return pRNode;
}

bool WDBMBase::setParent(WDNode::SharedPtr pNode
    , WDNode::SharedPtr pParentNode
    , WDNode::SharedPtr pNextNode
    , bool update)
{
    if (pNode == nullptr)
    {
        assert(false && "节点为空!");
        return false;
    }
    if (pParentNode == nullptr)
    {
        assert(false && "父节点为空!");
        return false;
    }
    // 校验指定的父节点是否属于当前模块
    if (pParentNode->getBMBase() != this)
    {
        assert(false && "指定的父节点不属于当前模块!");
        return false;
    }
    //校验是否支持挂载
    if (!this->typeMgr().checkParent(pNode->type(), pParentNode->type()))
    {
        assert(false && "父类型不支持挂载当前类型!");
        return false;
    }
    this->onNodeSetParentBefore(pNode->parent());
    //首先从旧的父节点移除
    if (pNode->parent() != nullptr)
    {
        pNode->parent()->removeChild(pNode);
    }
    // 添加或插入
    if (pNextNode == nullptr || !pParentNode->isParent(*pNextNode))
        pParentNode->addChild(pNode);
    else
        pParentNode->insertChild(pNode, pNextNode);

    if (update)
    {
        this->updateRefs(*pNode);
        pNode->update();
    }
    this->onNodeSetParentAfter(pParentNode);

    return true;
}

void WDBMBase::destroy(WDNode::SharedPtr pNode)
{
    if (pNode == nullptr || pNode == _pRoot)
        return ;

    // 发送即将销毁通知
    pNode->sendDestroy();

    this->onNodeDestroyBefore(pNode);

    WD::WDNode::SharedPtr parent = pNode->parent();
    if (parent != nullptr)
    {
        parent->removeChild(pNode);
    }

    this->onNodeDestroyAfter(parent);
}

void WDBMBase::destroy(const WDNode::Nodes& nodes, bool bRemoveDuplicates)
{
    if (nodes.empty())
        return;

    if (!bRemoveDuplicates) 
    {
        for (auto pNode : nodes) 
        {
            this->destroy(pNode);
        }
    }

    // 去重，并且查找列表中是否同时存在节点以及其祖先节点，如果存在，只保留祖先节点
    std::set<WDNode::SharedPtr> fNodes;
    for (auto pNode : nodes)
    {
        if (pNode == nullptr)
            continue;
        fNodes.insert(pNode);
    }

    for (auto pNode : fNodes)
    {
        if (pNode == nullptr)
            continue;

        WDNode::SharedPtr pTNode = pNode->parent();
        while (pTNode != nullptr) 
        {
            auto fItr = fNodes.find(pTNode);
            if (fItr != fNodes.end())
                break;
            pTNode = pTNode->parent();
        }
        // 说明没有找到祖先节点，执行销毁
        if (pTNode == nullptr)
        {
            this->destroy(pNode);
        }
    }
}

WDNode::SharedPtr WDBMBase::clone(WDNode::SharedPtr pSrcNode)
{
    if (pSrcNode == nullptr)
    {
        assert(false && "源节点为空!");
        return nullptr;
    }
    if (pSrcNode == _pRoot)
    {
        assert(false && "根节点不能被克隆!");
        return nullptr;
    }
    WDNode::SharedPtr pTarNode = pSrcNode->cloneT<WDNode>();
    if (pTarNode == nullptr)
    {
        assert(false && "设计模块克隆节点时出现内部错误!");
        return nullptr;
    }
    pTarNode->setFlags(pTarNode->flags().addFlags( WDNode::Flag::F_Editted, WDNode::Flag::F_EdittedStatus ));
    for (size_t i = 0; i < pSrcNode->childCount(); ++i)
    {
        WDNode::SharedPtr pChild = pSrcNode->childAt(i);
        if (pChild == nullptr)
            continue;
        WDNode::SharedPtr pTarChild = this->clone(pChild);
        if (pTarChild == nullptr)
            continue;
        pTarChild->setFlags(pTarChild->flags().addFlags( WDNode::Flag::F_Editted, WDNode::Flag::F_EdittedStatus ));
        pTarNode->addChild(pTarChild);
    }
    return pTarNode;
}
WDNode::SharedPtr WDBMBase::clone(WDNode::SharedPtr pSrcNode, std::map<WD::WDNode::SharedPtr, WD::WDNode::SharedPtr>& map)
{
    if (pSrcNode == nullptr)
    {
        assert(false && "源节点为空!");
        return nullptr;
    }
    if (pSrcNode == _pRoot)
    {
        assert(false && "根节点不能被克隆!");
        return nullptr;
    }
    WDNode::SharedPtr pTarNode = pSrcNode->cloneT<WDNode>();
    // 收集源节点与目标节点的映射
    map[pSrcNode] =  pTarNode;
    if (pTarNode == nullptr)
    {
        assert(false && "设计模块克隆节点时出现内部错误!");
        return nullptr;
    }
    pTarNode->setFlags(pTarNode->flags().addFlags( WDNode::Flag::F_Editted, WDNode::Flag::F_EdittedStatus ));
    for (size_t i = 0; i < pSrcNode->childCount(); ++i)
    {
        WDNode::SharedPtr pChild = pSrcNode->childAt(i);
        if (pChild == nullptr)
            continue;
        WDNode::SharedPtr pTarChild = this->clone(pChild, map);
        if (pTarChild == nullptr)
            continue;
        pTarChild->setFlags(pTarChild->flags().addFlags( WDNode::Flag::F_Editted, WDNode::Flag::F_EdittedStatus ));
        pTarNode->addChild(pTarChild);
    }
    return pTarNode;
}

WDNode::SharedPtr WDBMBase::findNode(const std::string_view& name, const std::string_view& typeName) const
{
    if (_pRoot == nullptr)
        return nullptr;

    WDNode::SharedPtr pRNode = nullptr;
    if (!typeName.empty())
    {
        //从根节点开始递归查找
        WDNode::RecursionHelpterR(*_pRoot, [&pRNode, typeName, name](WDNode& node)
            {
                if (node.type() != typeName)
                    return false;
                if (node.name() != name)
                    return false;
                pRNode = WDNode::ToShared(&node);
                return true;
            });
    }
    else
    {
        //从根节点开始递归查找
        WDNode::RecursionHelpterR(*_pRoot, [&pRNode, name](WDNode& node)
            {
                if (node.name() != name)
                    return false;
                pRNode = WDNode::ToShared(&node);
                return true;
            });
    }
    return pRNode;
}
WDNode::SharedPtr WDBMBase::findNode(const WDUuid& guid, const std::string_view& typeName) const
{
    if (_pRoot == nullptr)
        return nullptr;

    WDNode::SharedPtr pRNode = nullptr;
    if (!typeName.empty())
    {
        //从根节点开始递归查找
        WDNode::RecursionHelpterR(*_pRoot, [&pRNode, typeName, guid](WDNode& node)
            {
                if (node.type() != typeName)
                    return false;
                if (node.uuid() != guid)
                    return false;
                pRNode = WDNode::ToShared(&node);
                return true;
            });
    }
    else
    {
        //从根节点开始递归查找
        WDNode::RecursionHelpterR(*_pRoot, [&pRNode, guid](WDNode& node)
            {
                if (node.uuid() != guid)
                    return false;
                pRNode = WDNode::ToShared(&node);
                return true;
            });
    }
    return pRNode;
}
WDNode::Nodes WDBMBase::findNodes(const std::vector<WDUuid>& guids) const
{
    WDNode::Nodes result;
    auto tarSize = guids.size();
    result.resize(tarSize);

    if (_pRoot == nullptr)
        return result;

    // 临时map，用于结果排序
    std::map<WDUuid, WDNode::SharedPtr> mapResult;
    //从根节点开始递归查找
    WDNode::RecursionHelpterR(*_pRoot, [&mapResult, guids, tarSize](WDNode& node)
        {
            auto& uuid = node.uuid();
            if (std::find(guids.begin(), guids.end(), uuid) == guids.end())
                return false;
            mapResult[uuid] = WDNode::ToShared(&node);

            // 如果已经查找完了则不再继续递归
            if (mapResult.size() == tarSize)
                return true;
            else
                return false;
        });
    // 结果排序
    for (size_t i = 0; i < tarSize; ++i)
    {
        auto fItr = mapResult.find(guids.at(i));
        if (fItr != mapResult.end())
        {
            result.at(i) = fItr->second;
        }
        else
        {
            result.at(i) = nullptr;
        }
    }

    return result;
}

bool WDBMBase::updateRefs(WDNode& node) const
{
    // 递归收集节点引用对象
    WDBMRefUpdater collector;
    WDNode::RecursionHelpter(node, [&collector, this](WDNode& node)
        {
            auto pBase = node.getBDBase();
            if (pBase != nullptr)
            {
                pBase->collectNodeRef(collector);
            }
        });
    // 更新节点引用
    this->updateNodeRef(collector);
    return true;
}
void WDBMBase::updateRefs(const WDNode::Nodes& nodes) const
{
    if (nodes.empty())
        return;
    // 递归收集节点引用对象
    WDBMRefUpdater collector;
    for (auto pNode : nodes)
    {
        if (pNode == nullptr)
            continue;
        WDNode::RecursionHelpter(*pNode, [&collector, this](WDNode& node)
            {
                auto pBase = node.getBDBase();
                if (pBase != nullptr)
                {
                    pBase->collectNodeRef(collector);
                }
            });
    }
    // 更新节点引用
    this->updateNodeRef(collector);
}

WDBMBase::CheckResult WDBMBase::check(const WDNode& node, const CheckOperation& operation, const CheckFlags& flags, bool bAutoTip)
{
    // 优先判断节点是否有权限
    if (flags.hasFlag(CF_Permission))
    {
        std::optional<WDBMPermissionMgr::Operation> operationPer;
        switch (operation)
        {
        case WD::WDBMBase::CO_EditAttribute:
            operationPer = WDBMPermissionMgr::Operation::OP_EditAttribute;
            break;
        case WD::WDBMBase::CO_AddChild:
            operationPer = WDBMPermissionMgr::Operation::OP_AddChild;
            break;
        case WD::WDBMBase::CO_RemoveChild:
            operationPer = WDBMPermissionMgr::Operation::OP_RemoveChild;
            break;
        default:
            break;
        }
        if (operationPer && !_pPermissionMgr->check(node, operationPer.value()))
        {
            if (bAutoTip)
                _checkTipNotice(CR_NoPermission, node, operation, flags, *this);

            return CR_NoPermission;
        }
    }

    if (flags.hasFlag(CF_Lock))
    {
        // 目前锁定只校验属性编辑
        switch (operation)
        {
        case WD::WDBMBase::CO_EditAttribute:
            {
                if (node.flags().hasFlag(WDNode::F_Lock))
                {
                    if (bAutoTip)
                        _checkTipNotice(CR_Locked, node, operation, flags, *this);
                    return CR_Locked;
                }
            }
            break;
        case WD::WDBMBase::CO_AddChild:
            break;
        case WD::WDBMBase::CO_RemoveChild:
            break;
        default:
            break;
        }
    }
    if (bAutoTip)
        _checkTipNotice(CR_Success, node, operation, flags, *this);
    return CR_Success;
}

/**
* @brief 记录节点个数
*/
size_t CountNodes(const WDNode::Nodes& nodes) 
{
    size_t cnt = 0;
    WDBMRefUpdater collector;
    for (auto pNode : nodes)
    {
        if (pNode == nullptr)
            continue;
        WDNode::RecursionHelpter(*pNode, [&cnt](WDNode& )
        {
            cnt++;
        });
    }
    return cnt;
}
bool WDBMBase::save(const std::string_view& fileName)
{
    std::filesystem::path path(fileName);
    if (path.extension() == ".json")
    {
        char tmpFileName[1024] = { 0 };
        sprintf_s(tmpFileName, sizeof(tmpFileName), "%s.tmp", fileName.data());
        std::fstream fs(tmpFileName, std::ios_base::out);
        if (!fs)
        {
            assert(false && "保存失败:文件打开失败!");
            return false;
        }

        JsonDoc doc;
        auto& rootArray = doc.SetArray();

        // 临时优化: 这里针对元件库先做一个特殊处理
        // 因为元件库的所有节点都不具有变换，颜色，可见性属性，因此序列化时不需要写入和读取这些属性
        // 最终优化: 后续将这些属性从节点中分离到对应的类型数据中
        WDBMNodeSerialize::BASFlags flags = WDBMNodeSerialize::BASFlag::BASF_All;
        if (this->name() == "Catalog")
        {
            flags = WDBMNodeSerialize::BASFlag::BASF_None;
        }
        // 保存到json
        _serialize->saveToJson(this->root()->children(), doc, rootArray, flags);

        rapidjson::StringBuffer buffer;
        rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
        if (!doc.Accept(writer))
        {
            assert(false && "保存失败:Json写入失败!");
            return false;
        }

        fs.write(buffer.GetString(), buffer.GetSize());
        fs.close();

        // 重命名临时文件
        try
        {
            std::filesystem::rename(tmpFileName, fileName.data());
        }
        catch (const std::exception&)
        {
            assert(false && "保存失败:临时文件重命名失败!");
            std::filesystem::remove(tmpFileName);
            return false;
        }
        _core.undoStack().clear();
        return true;
    }
    else if (path.extension() == ".wd")
    {
        if (_fmtSerialize == nullptr)
            return false;
        _fmtSerialize->close();

        if (!_fmtSerialize->open(fileName.data()))
        {
            // 考虑到适配目前open的逻辑
            // 这里先删除原来的文件
            if (std::filesystem::exists(fileName))
            {
                if (!std::filesystem::remove(fileName))
                    return false;
            }
            // 再次打开
            if (!_fmtSerialize->open(fileName.data()))
                return false;
        }

        _core.undoStack().clear();
        return _fmtSerialize->save(root()->children());
    }
    else
    {
        assert(false && "保存失败:不支持的文件格式!");
        return false;
    }
}
bool WDBMBase::load(const std::string_view& fileName, const std::string_view& cfgFilePath, FuncProgress funcProgress)
{
    if (funcProgress)
        funcProgress(0.00, WD::WDTs("mainFunc", "the file is being opened").c_str());

    double prevTime = 0.0;
    double allTime = 0.0;
    std::filesystem::path path(fileName);
    // 读取项目颜色配置文件
    std::string colorCfgFile = WDFileInfo::FilePath(fileName) + "/ColorConfig.xml";
    _pColorIndexMgr->fromXml(colorCfgFile.c_str());

    // 读取项目的属性配置文件
    LoadProjectConfigXML(std::string(cfgFilePath), this->typeMgr());
    // 类型管理初始化
    typeMgr().init();
    // 创建根节点
    _pRoot = createRoot();
    if (_pRoot == nullptr)
    {
        assert(false && "创建模块根节点失败!");
        return false;
    }
    // 调用一次根节点的更新
    _pRoot->update();
    // 根节点rid为0
    _pRoot->setRemoteId(0);

    if (path.extension() == ".json")
    {

#if WD_PLATFORM == WD_PLATFORM_WIN32
        LOG_INFO << "文件解析开始: " << local8bit2utf8(fileName.data());
#else
        LOG_INFO << "文件解析开始: " << fileName;
#endif 

        WDTimestamp ttTm;
        prevTime = ttTm.getElapsedSecond();

        WDFileReader fileReader(fileName.data());
        if (fileReader.isBad())
            return false;
        fileReader.readAll();

        auto readTime = ttTm.getElapsedSecond() - prevTime;
        allTime += readTime;
        prevTime = ttTm.getElapsedSecond();

        LOG_INFO << "文件读取完成, 读取花费时间: " << readTime << "秒!";

        WDNode::Nodes nodes;
        // 临时优化: 这里针对元件库先做一个特殊处理
        // 因为元件库的所有节点都不具有变换，颜色，可见性属性，因此序列化时不需要写入和读取这些属性
        // 最终优化: 后续将这些属性从节点中分离到对应的类型数据中
        WDBMNodeSerialize::BASFlags flags = WDBMNodeSerialize::BASFlag::BASF_All;
        if (this->name() == "Catalog")
        {
            flags = WDBMNodeSerialize::BASFlag::BASF_None;
        }
        // 兼容旧版本数据文件
        bool oldVersion = false;
        // 先以旧版本数据解析方式来解析
        JsonDoc docOld;
        JsonValue& rootObject = docOld.SetObject();

        if (funcProgress)
            funcProgress(0.05, WD::WDTs("mainFunc","data is being read").c_str());

        docOld.Parse((char*)fileReader.data(), fileReader.length());

        auto parseTime = ttTm.getElapsedSecond() - prevTime;
        allTime += parseTime;
        prevTime = ttTm.getElapsedSecond();
        LOG_INFO << "文件解析完成, 花费时间: " << parseTime << "秒!";


        if (!docOld.HasParseError())
        {
            oldVersion = true;

            if (funcProgress)
                funcProgress(0.1, WD::WDTs("mainFunc", "data is being parsed").c_str());
            // 加载
            nodes = _serialize->loadFromJson(docOld, rootObject, flags, funcProgress);

            auto loadTime = ttTm.getElapsedSecond() - prevTime;
            allTime += loadTime;
            prevTime = ttTm.getElapsedSecond();

            LOG_INFO << "文件节点以及属性创建完成, 花费时间: " << loadTime << "秒!";
        }
        else
        {
            // 以新版本数据解析方式来解析
            JsonDoc docNew;
            JsonValue& rootArray = docNew.SetArray();

            docNew.Parse((char*)fileReader.data(), fileReader.length());
            if (!docNew.HasParseError())
            {
                // 加载
                if (funcProgress)
                    funcProgress(0.1, WD::WDTs("mainFunc", "data is being parsed").c_str());

                nodes = _serialize->loadFromJson(docNew, rootArray, flags, funcProgress);
            }
            else
            {
                assert(false && "json数据解析失败");
                return false;
            }

            auto loadTime = ttTm.getElapsedSecond() - prevTime;
            allTime += loadTime;
            prevTime = ttTm.getElapsedSecond();

            LOG_INFO << "文件节点以及属性创建完成, 花费时间: " << loadTime << "秒!";
        }
        if (nodes.empty())
            return false;

        // 兼容旧版本数据，去掉根节点
        if (oldVersion)
        {
            auto pFront = nodes.front();
            if (pFront != nullptr)
            {
                if (nodes.size() == 1 && pFront->isType("WORL"))
                {
                    nodes.clear();
                    nodes = pFront->children();
                }
            }
        }

        if (funcProgress)
            funcProgress(0.6, WD::WDTs("mainFunc", "the nodes are being added to the admin module").c_str());
        // 先清除根节点的所有子节点
        auto children = this->root()->children();
        this->destroy(children, false);

        // 再将这些节点挂到根节点上
        for (auto pNode : nodes)
        {
            if (pNode == nullptr)
                continue;
            this->setParent(pNode, this->root(), false);
        }

        auto addTime = ttTm.getElapsedSecond() - prevTime;
        allTime += addTime;
        prevTime = ttTm.getElapsedSecond();

        LOG_INFO << "文件节点向系统中添加完成, 花费时间: " << addTime << "秒!";
        if (funcProgress)
            funcProgress(0.65, WD::WDTs("mainFunc", "collecting citation attributes at present").c_str());
        // 收集引用
        updateRefs(nodes);

        if (funcProgress)
            funcProgress(0.90, WD::WDTs("mainFunc", "update node").c_str());
        auto updateRefTime = ttTm.getElapsedSecond() - prevTime;
        allTime += updateRefTime;
        prevTime = ttTm.getElapsedSecond();

        LOG_INFO << "文件节点收集引用属性完成, 花费时间: " << updateRefTime << "秒!";

        // 调用更新, 这里由于根节点的子均为新加载的节点，因此直接调用根节点的更新
        this->root()->update(true);

        auto updateTime = ttTm.getElapsedSecond() - prevTime;
        allTime += updateTime;
        prevTime = ttTm.getElapsedSecond();

        LOG_INFO << "文件节点数据更新完成, 花费时间: " << updateTime << "秒!";

        LOG_INFO << "文件解析完成, 花费总时长: " << allTime << "秒!";

        // 计算节点总个数
        size_t cnt = CountNodes(nodes);
        LOG_INFO << "文件包含的节点总个数: " << cnt;

        if (funcProgress)
            funcProgress(1.0, WD::WDTs("mainFunc", "loading completed").c_str());

        return true;
    }
    else if (path.extension() == ".wd")
    {
        if (_fmtSerialize == nullptr)
            return false;
        if (!_fmtSerialize->open(fileName.data()))
            return false;
        auto nodes = _fmtSerialize->loadTree();

        // 先清除根节点的所有子节点
        auto children = this->root()->children();
        this->destroy(children, false);

        // 再将这些节点挂到根节点上
        for (auto pNode : nodes)
        {
            if (pNode == nullptr)
                continue;
            this->setParent(pNode, this->root());
        }

        //_app.nodeTree().noticeCurrentNodeChanged() += {_fmtSerialize, &WDFmtSerialize::onUpdateNodeData};

        return true;
    }
    else
    {
        assert(false && "不支持的文件格式!");
        return false;
    }
}

std::string WDBMBase::trA(const std::string& attributeName) const
{
    if (attributeName.empty())
        return attributeName;
    // 判断是否是自定义属性，如果是自定义属性，则需要将前面的':'去掉再做翻译
    if (attributeName[0] == ':') 
    {
        std::string tName = attributeName.substr(1);
        auto tsName = WDTranslate::Instance()->translate(_tsAttrCxtName, tName);
        tsName.insert(tsName.begin(), ':');
        return tsName;
    }
    else
    {
        return WDTranslate::Instance()->translate(_tsAttrCxtName, attributeName);
    }
}
const std::string& WDBMBase::trEDK(const std::string& enumDictionaryName, const std::string& enumDictionaryKey) const
{
    if (enumDictionaryKey.empty())
        return enumDictionaryKey;
    std::string tCxt = _tsEDKeyCxtName + ":" + enumDictionaryName;
    return WDTranslate::Instance()->translate(tCxt, enumDictionaryKey);
}

WDUndoCommand* WDBMBase::makeAttributeSetCommand(WDNode::SharedPtr pNode
    , const std::string_view& attrName
    , const WDBMAttrValue& value
    , WDUndoCommand* pParentCmd)
{
    if (pNode == nullptr)
        return nullptr;
    return new NodeSetAttributeCommand(pNode, attrName, value, pParentCmd);
}
WDUndoCommand* WDBMBase::makeAttributeSetedCommand(WDNode::SharedPtr pNode
    , const std::string_view& attrName
    , const WDBMAttrValue& prevValue
    , WDUndoCommand* pParentCmd)
{
    if (pNode == nullptr)
        return nullptr;
    return new NodeSetedAttributeCommand(pNode, attrName, prevValue, pParentCmd);
}
WDUndoCommand* WDBMBase::makeAttributesSetedCommand(const std::vector<WDBMBase::CmdNodeAttributes>& prevAttrs
    , WDUndoCommand* pParentCmd)
{
    return new NodeSetedAttributesCommand(prevAttrs, pParentCmd);
}
WDUndoCommand* WDBMBase::makeCreateCommand(WDNode::SharedPtr pParentNode
    , const std::string_view& typeName
    , const std::string_view& name
    , WDNode::SharedPtr pNextNode
    , WDUndoCommand* pParentCmd)
{
    if (pParentNode == nullptr)
        return nullptr;
    if (typeName.empty())
        return nullptr;

    return new NodeCreateCommand(*this, pParentNode, typeName, name, pNextNode, pParentCmd);
}

WDUndoCommand* WDBMBase::makeDestroyCommand(const WDNode::Nodes& nodes, WDUndoCommand* pParentCmd)
{
    if (nodes.empty())
        return nullptr;
    return new NodeDestroyCommand(*this, nodes, pParentCmd);
}

WDUndoCommand* WDBMBase::makeSceneAddCommand(const WDNode::Nodes& nodes, WDUndoCommand* pParentCmd)
{
    if (nodes.empty())
        return nullptr;
    return new SceneAddCommand(*this, nodes, pParentCmd);
}

WDUndoCommand* WDBMBase::makeSceneRemoveCommand(const WDNode::Nodes& nodes, WDUndoCommand* pParentCmd)
{
    if (nodes.empty())
        return nullptr;
    return new SceneRemoveCommand(*this, nodes, pParentCmd);
}

WDUndoCommand* WDBMBase::makeNodeHierarchyMoveInfoCommand(const NodeHierarchyMoveInfos& infos, WDUndoCommand* pParentCmd)
{
    if (infos.empty())
        return nullptr;
    return new NodeHierarchyMoveCommand(*this, infos, pParentCmd);
}
WDUndoCommand* WDBMBase::makeCreatedCommand(const WDNode::Nodes& nodes, WDUndoCommand* pParentCmd)
{
    if (nodes.empty())
        return nullptr ;
    return new NodeCreatedCommand(*this, nodes, pParentCmd);
}

WDUndoCommand* WDBMBase::makeMirrorCommand(const WDNode::Nodes& nodes
    , const DVec3& normal
    , const DVec3& center
    , WDUndoCommand* pParentCmd)
{
    if (nodes.empty())
        return nullptr ;
    return new NodeMirrorCommand(*this, nodes, normal, center, pParentCmd);
}
WD_NAMESPACE_END


