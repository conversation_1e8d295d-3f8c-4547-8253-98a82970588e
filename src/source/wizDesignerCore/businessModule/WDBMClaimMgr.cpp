#include "WDBMClaimMgr.h"
#include "WDBMAuditObjectMgr.h"
#include "design/WDBMDesign.h"

WD_NAMESPACE_BEGIN;


struct AddedNodes
{
private:
    std::set<WDNode::SharedPtr> _addedNodes;
public:
    // 不存在则添加成功返回true,存在则返回false
    bool add(WDNode::SharedPtr pNode)
    {
        auto fItr = _addedNodes.find(pNode);
        if (fItr != _addedNodes.end())
            return false;
        _addedNodes.insert(pNode);
        return true;
    }
};

enum AuditType
{
    // 层级节点
    AT_None = 0,
    // 校审对象类型节点
    AT_Audit,
    // 校审对象类型节点的子孙节点
    At_AuditDescendant,
};

AuditType AuditTypeGet(WDBMAuditObjectMgr& aMgr, WDNode& node, WDNode::SharedPtr* pOutAuditNode = nullptr)
{
    std::string tType = std::string(node.type());
    if (aMgr.contains(tType))
    {
        if (pOutAuditNode != nullptr)
            (*pOutAuditNode) = WDNode::ToShared(&node);
        return AuditType::AT_Audit;
    }

    auto pTNode = node.parent();
    while (pTNode != nullptr) 
    {
        tType = std::string(pTNode->type());
        if (aMgr.contains(tType))
        {
            if (pOutAuditNode != nullptr)
                (*pOutAuditNode) = pTNode;
            return AuditType::At_AuditDescendant;
        }
        pTNode = pTNode->parent();
    }

    return AuditType::AT_None;
}
bool IsAuditType(WDBMAuditObjectMgr& aMgr, WDNode& node)
{
    return aMgr.contains(node);
}


WDBMClaimMgr::CheckItem::CheckItem(WDNode::SharedPtr pNode, bool isAudit, bool bRecursionCheckIn)
    : pNode(pNode)
    , isAudit(isAudit)
    , bRecursionCheckIn(bRecursionCheckIn)
{
}
WDBMClaimMgr::AttrCheckItem::AttrCheckItem(WDNode::SharedPtr pNode
    , const AttrNames& atrrNames
    , WDNode::SharedPtr pAttrChangedNode
    , bool isAudit)
    : pNode(pNode)
    , atrrNames(atrrNames)
    , pAttrChangedNode(pAttrChangedNode)
    , isAudit(isAudit)
{
}

WDBMClaimMgr::WDBMClaimMgr(WDBMBase& bmBase)
    : _bmBase(bmBase)
    , _newCreatedNodes(*this)
    , _deletedNodes(*this)
{
    _enabled = false;
}
WDBMClaimMgr::~WDBMClaimMgr()
{
    assert(_nodes.empty() && "还有已被签入的节点未被签出!");
}

bool WDBMClaimMgr::checkAdd(const AddDatas& addDatas, bool bShowMessage)
{
    if (addDatas.empty())
    {
        assert(false);
        return false;
    }

    if (!_enabled)
        return true;

    // 未指定签出回调，不可能成功签入节点
    if (!_funcCheckInBefore)
    {
        assert(false);
        return false;
    }

    // 用于防止节点被重复添加从而出现重复申领
    AddedNodes aNodes;

    CheckItems items;
    items.reserve(addDatas.size() * 2);
    for (const auto& aData : addDatas)
    {
        if (aData.pParentNode == nullptr)
        {
            assert(false);
            continue;
        }
        auto pBMBase = aData.pParentNode->getBMBase();
        if (pBMBase == nullptr)
        {
            assert(false);
            continue;
        }
        WDNode::SharedPtr pAuditNode = nullptr;
        auto aType = AuditTypeGet(_bmBase.auditObjectMgr(), *(aData.pParentNode), &pAuditNode);
        switch (aType)
        {
            // 层级节点，需要申领其父节点和后置兄弟节点
        case WD::AT_None:
            {
                // 判断当前节点的父节点是否被申领
                if (aData.pParentNode != pBMBase->root()) // 如果要签入节点的父节点是根节点，则不用签入父节点
                {
                    if (!aContains(aData.pParentNode) && aNodes.add(aData.pParentNode))
                        items.push_back(CheckItem(aData.pParentNode, false));
                }
                // 再判断后置兄弟节点是否被申领
                if (aData.pNextNode != nullptr)
                {
                    if (!aContains(aData.pNextNode) && aNodes.add(aData.pNextNode))
                    {
                        // 只需要判断后置兄弟节点是否是校审对象类型的节点即可
                        // 这里由于父节点是层级节点，因此后置兄弟节点不可能是校审对象节点的子孙节点
                        items.push_back(CheckItem(aData.pNextNode, IsAuditType(_bmBase.auditObjectMgr(), *(aData.pNextNode))));
                    }
                }
            }
            break;
            // 校审类型的节点， 只需要申领父节点
        case WD::AT_Audit:
            {
                if (!aContains(aData.pParentNode) && aNodes.add(aData.pParentNode))
                    items.push_back(CheckItem(aData.pParentNode, true));
            }
            break;
            // 校审类型的子孙节点，只需要申领校审对象节点
        case WD::At_AuditDescendant:
            {
                if (pAuditNode == nullptr)
                {
                    assert(false);
                    continue;
                }
                if (!aContains(pAuditNode) && aNodes.add(pAuditNode))
                    items.push_back(CheckItem(pAuditNode, true));
            }
            break;
        default:
            break;
        }
    }
    // 需要的节点均已被申领
    if (items.empty())
        return true;

    // 触发申领
    auto rIndices = _funcCheckInBefore(items, bShowMessage);

    // 这里保存申领成功的节点
    size_t successCnt = 0;
    for (auto idx : rIndices)
    {
        if (idx >= items.size())
            continue;

        auto pTNode = items[idx].pNode;
        if (pTNode == nullptr)
            continue;

        successCnt++;
        this->add(pTNode);
    }

    // 如果需要申领的节点全部被申领，则认为申领成功
    return successCnt == items.size();
}
bool WDBMClaimMgr::checkUpdate(const AttrDatas& attrDatas, bool& bCancelModify, bool bShowMessage)
{
    bCancelModify = false;

    if (attrDatas.empty())
    {
        assert(false);
        return false;
    }

    if (!_enabled)
        return true;

    // 未指定签出回调，不可能成功签入节点
    if (!_funcCheckInAttrBefore)
    {
        assert(false);
        return false;
    }

    // 用于防止节点被重复添加从而出现重复申领
    AddedNodes aNodes;

    AttrCheckItems items;
    items.reserve(attrDatas.size());
    for (auto aData : attrDatas)
    {
        if (aData.pNode == nullptr)
        {
            assert(false);
            continue;
        }
        auto pBMBase = aData.pNode->getBMBase();
        if (pBMBase == nullptr)
        {
            assert(false);
            continue;
        }

        WDNode::SharedPtr pAuditNode = nullptr;
        auto aType = AuditTypeGet(_bmBase.auditObjectMgr(), *(aData.pNode), &pAuditNode);
        switch (aType)
        {
            // 如果是层级节点，只需要申领节点本身
        case WD::AT_None:
            {
                // 判断当前节点是否被申领
                if (!aContains(aData.pNode) && aNodes.add(aData.pNode))
                    items.push_back(AttrCheckItem(aData.pNode, aData.attrNames, aData.pNode, false));
            }
            break;
            // 如果是校审对象类型的节点，只需要申领节点本身
        case WD::AT_Audit:
            {
                // 判断当前节点是否被申领
                if (!aContains(aData.pNode) && aNodes.add(aData.pNode))
                    items.push_back(AttrCheckItem(aData.pNode, aData.attrNames, aData.pNode, true));
            }
            break;
            // 如果是校审对象类型的子孙节点，就需要申领校审对象节点了
            // 而且这里对于校审对象类型的子孙节点来说, 需要从校审对象中查找到对应节点并且修改其对应属性，因此这里的参数还需要特殊处理
        case WD::At_AuditDescendant:
            {
                if (pAuditNode == nullptr)
                {
                    assert(false);
                    continue;
                }
                if (!aContains(pAuditNode) && aNodes.add(pAuditNode))
                    items.push_back(AttrCheckItem(pAuditNode, aData.attrNames, aData.pNode, true));
            }
            break;
        default:
            break;
        }
    }

    // 需要的节点均已被申领
    if (items.empty())
        return true;

    // 触发申领
    auto rIndices = _funcCheckInAttrBefore(items, bShowMessage, bCancelModify);

    // 这里保存申领成功的节点
    size_t successCnt = 0;
    for (auto idx : rIndices)
    {
        if (idx >= items.size())
            continue;

        auto pTNode = items[idx].pNode;
        if (pTNode == nullptr)
            continue;

        successCnt++;
        this->add(pTNode);
    }

    // 如果需要申领的节点全部被申领，则认为申领成功
    return successCnt == items.size();
}
bool WDBMClaimMgr::checkMove(const MoveDatas& moveDatas, bool bShowMessage)
{
    if (moveDatas.empty())
    {
        assert(false);
        return false;
    }

    if (!_enabled)
        return true;

    // 未指定签出回调，不可能成功签入节点
    if (!_funcCheckInBefore)
    {
        assert(false);
        return false;
    }

    // 用于防止节点被重复添加从而出现重复申领
    AddedNodes aNodes;

    CheckItems items;
    items.reserve(moveDatas.size() * 5);

    for (auto mData : moveDatas)
    {
        if (mData.pNode == nullptr)
        {
            assert(false);
            continue;
        }
        // 目标父节点，必须指定
        if (mData.pTarParent == nullptr)
        {
            assert(false);
            continue;
        }
        auto pBMBase = mData.pNode->getBMBase();
        if (pBMBase == nullptr)
        {
            assert(false);
            continue;
        }

        // 判断当前节点侧需要申领的节点列表
        WDNode::SharedPtr pAuditNode = nullptr;
        auto aType = AuditTypeGet(_bmBase.auditObjectMgr(), *(mData.pNode), &pAuditNode);
        switch (aType)
        {
            // 如果是层级节点，需要申领当前节点，当前节点的父节点，当前节点的后置兄弟节点
        case WD::AT_None:
        {
            // 判断当前节点的父节点是否被申领
            auto pParentNode = mData.pNode->parent();
            if (pParentNode != nullptr && pParentNode != pBMBase->root()) // 如果要签入节点的父节点是根节点，则不用签入父节点
            {
                if (!aContains(pParentNode) && aNodes.add(pParentNode))
                    items.push_back(CheckItem(pParentNode, false));
            }

            // 判断当前节点是否被申领
            if (!aContains(mData.pNode) && aNodes.add(mData.pNode))
                items.push_back(CheckItem(mData.pNode, false));

            // 再判断后置兄弟节点是否被申领
            auto pNextNode = mData.pNode->nextBrother();
            if (pNextNode != nullptr)
            {
                if (!aContains(pNextNode) && aNodes.add(pNextNode))
                {
                    // 只需要判断后置兄弟节点是否是校审对象类型的节点即可
                    // 这里由于父节点是层级节点，因此后置兄弟节点不可能是校审对象节点的子孙节点
                    items.push_back(CheckItem(pNextNode, IsAuditType(_bmBase.auditObjectMgr(), *(pNextNode))));
                }
            }
        }
        break;
            // 如果是校审对象类型的节点，需要申领当前节点，当前节点的父节点，当前节点的后置兄弟节点
        case WD::AT_Audit:
        {
            // 判断当前节点的父节点是否被申领, 因为当前节点是校审对象类型的节点，因此父节点必然是层级节点
            auto pParentNode = mData.pNode->parent();
            if (pParentNode != nullptr && pParentNode != pBMBase->root()) // 如果要签入节点的父节点是根节点，则不用签入父节点
            {
                if (!aContains(pParentNode) && aNodes.add(pParentNode))
                    items.push_back(CheckItem(pParentNode, false));
            }

            // 判断当前节点是否被申领
            if (!aContains(mData.pNode) && aNodes.add(mData.pNode))
                items.push_back(CheckItem(mData.pNode, true));

            // 再判断后置兄弟节点是否被申领
            auto pNextNode = mData.pNode->nextBrother();
            if (pNextNode != nullptr)
            {
                if (!aContains(pNextNode) && aNodes.add(pNextNode))
                {
                    // 只需要判断后置兄弟节点是否是校审对象类型的节点即可
                    // 这里由于父节点是层级节点，因此后置兄弟节点不可能是校审对象节点的子孙节点
                    items.push_back(CheckItem(pNextNode, IsAuditType(_bmBase.auditObjectMgr(), *(pNextNode))));
                }
            }
        }
        break;
            // 如果是校审对象类型的子孙节点，只需要申领校审对象节点
        case WD::At_AuditDescendant:
        {
            if (pAuditNode == nullptr)
            {
                assert(false);
                continue;
            }
            if (!aContains(pAuditNode) && aNodes.add(pAuditNode))
                items.push_back(CheckItem(pAuditNode, true));
        }
        break;
        default:
            break;
        }

        // 判断目标侧需要申领的节点列表
        WDNode::SharedPtr pTarAuditNode = nullptr;
        auto aTarType = AuditTypeGet(_bmBase.auditObjectMgr(), *(mData.pTarParent), &pTarAuditNode);
        switch (aTarType)
        {
            // 如果目标父节点是层级节点，需要申领目标父节点，目标父节点的后置兄弟节点
        case WD::AT_None:
            {
                // 目标父节点
                if (mData.pTarParent != pBMBase->root()) // 如果要签入节点的父节点是根节点，则不用签入父节点
                {
                    if (!aContains(mData.pTarParent) && aNodes.add(mData.pTarParent))
                        items.push_back(CheckItem(mData.pTarParent, false));
                }
                // 目标后置兄弟节点
                if (mData.pTarNext != nullptr)
                {
                    if (!aContains(mData.pTarNext) && aNodes.add(mData.pTarNext))
                    {
                        // 只需要判断后置兄弟节点是否是校审对象类型的节点即可
                        // 这里由于父节点是层级节点，因此后置兄弟节点不可能是校审对象节点的子孙节点
                        items.push_back(CheckItem(mData.pTarNext, IsAuditType(_bmBase.auditObjectMgr(), *(mData.pTarNext))));
                    }
                }
            }
            break;
            // 如果目标父节点是校审对象节点，只需要申领目标父节点
        case WD::AT_Audit:
            {
                if (!aContains(mData.pTarParent) && aNodes.add(mData.pTarParent))
                    items.push_back(CheckItem(mData.pTarParent, true));
            }
            break;
            // 如果目标父节点是校审对象的子孙节点，只需要申领目标校审对象节点
        case WD::At_AuditDescendant:
            {
                if (pTarAuditNode == nullptr)
                {
                    assert(false);
                    continue;
                }
                if (!aContains(pTarAuditNode) && aNodes.add(pTarAuditNode))
                    items.push_back(CheckItem(pTarAuditNode, true));
            }
            break;
        default:
            break;
        }

    }

    // 需要的节点均已被申领
    if (items.empty())
        return true;

    // 触发申领
    auto rIndices = _funcCheckInBefore(items, bShowMessage);

    // 这里保存申领成功的节点
    size_t successCnt = 0;
    for (auto idx : rIndices)
    {
        if (idx >= items.size())
            continue;

        auto pTNode = items[idx].pNode;
        if (pTNode == nullptr)
            continue;

        successCnt++;
        this->add(pTNode);
    }

    // 如果需要申领的节点全部被申领，则认为申领成功
    return successCnt == items.size();
}
bool WDBMClaimMgr::checkDelete(const DelDatas& delDatas, bool bShowMessage)
{
    if (delDatas.empty())
    {
        assert(false);
        return false;
    }

    if (!_enabled)
        return true;

    // 未指定签出回调，不可能成功签入节点
    if (!_funcCheckInBefore)
    {
        assert(false);
        return false;
    }

    // 用于防止节点被重复添加从而出现重复申领
    AddedNodes aNodes;

    CheckItems items;
    items.reserve(delDatas.size() * 3);
    for (const auto& dData : delDatas)
    {
        auto pNode = dData;
        if (pNode == nullptr)
        {
            assert(false);
            continue;
        }
        auto pBMBase = pNode->getBMBase();
        if (pBMBase == nullptr)
        {
            assert(false);
            continue;
        }
        WDNode::SharedPtr pAuditNode = nullptr;
        auto aType = AuditTypeGet(_bmBase.auditObjectMgr(), *pNode, &pAuditNode);
        switch (aType)
        {
        // 层级节点，需要申领其父节点,后置兄弟节点，当前节点以及当前节点的所有子孙节点
        case WD::AT_None:
        {
            // 判断当前节点的父节点是否被申领
            auto pParent = pNode->parent();
            if (pParent != nullptr && pParent != pBMBase->root()) // 如果要签入节点的父节点是根节点，则不用签入父节点
            {
                if (!aContains(pParent) && aNodes.add(pParent))
                    items.push_back(CheckItem(pParent, false));
            }
            // 判断当前节点的后置兄弟节点是否被申领
            auto pNextBrother = pNode->nextBrother();
            if (pNextBrother != nullptr)
            {
                if (!aContains(pNextBrother) && aNodes.add(pNextBrother))
                {
                    // 只需要判断后置兄弟节点是否是校审对象类型的节点即可
                    // 这里由于父节点是层级节点，因此后置兄弟节点不可能是校审对象节点的子孙节点
                    items.push_back(CheckItem(pNextBrother, IsAuditType(_bmBase.auditObjectMgr(), *pNextBrother)));
                }
            }
            // 判断当前节点以及其所有子孙节点是否均被申领
            if (!RecursionCheck(pNode) && aNodes.add(pNode))
                items.push_back(CheckItem(pNode, false, true));
        }
        break;
        // 校审类型节点，需要申领父节点，后置兄弟节点以及当前节点
        case WD::AT_Audit:
        {
            // 判断当前节点的父节点是否被申领, 由于当前节点是校审对象节点，因此父节点必然是层级节点
            auto pParent = pNode->parent();
            if (pParent != nullptr && pParent != pBMBase->root()) // 如果要签入节点的父节点是根节点，则不用签入父节点
            {
                if (!aContains(pParent) && aNodes.add(pParent))
                    items.push_back(CheckItem(pParent, false));
            }
            // 判断当前节点的后置兄弟节点是否被申领
            auto pNextBrother = pNode->nextBrother();
            if (pNextBrother != nullptr)
            {
                if (!aContains(pNextBrother) && aNodes.add(pNextBrother))
                {
                    // 只需要判断后置兄弟节点是否是校审对象类型的节点即可
                    // 这里由于父节点是层级节点，因此后置兄弟节点不可能是校审对象节点的子孙节点
                    items.push_back(CheckItem(pNextBrother, IsAuditType(_bmBase.auditObjectMgr(), *pNextBrother)));
                }
            }
            // 判断当前节点是否被申领
            if (!aContains(pNode) && aNodes.add(pNode))
                items.push_back(CheckItem(pNode, true));
        }
        break;
        // 校审类型的子孙节点，只需要申领校审对象
        case WD::At_AuditDescendant:
        {
            if (pAuditNode == nullptr)
            {
                assert(false);
                continue;
            }
            if (!aContains(pAuditNode) && aNodes.add(pAuditNode))
                items.push_back(CheckItem(pAuditNode, true));
        }
        break;
        default:
            break;
        }
    }

    // 需要的节点均已被申领
    if (items.empty())
        return true;

    // 触发申领
    auto rIndices = _funcCheckInBefore(items, bShowMessage);
    // 这里保存申领成功的节点
    size_t successCnt = 0;
    for (auto idx : rIndices)
    {
        if (idx >= items.size())
            continue;

        auto pTNode = items[idx].pNode;
        if (pTNode == nullptr)
            continue;

        successCnt++;
        // 如果是删除的节点，这里不用记录被申领的状态，因为节点已经被删除了，不可能再用这个节点以及子孙节点来申领了
        // !TODO： 不过要考虑一个及其特殊的情况，也就是这个节点虽然逻辑上被删除了，但可能还存留在undo-redo栈里面，随时被撤销回来
        //  因此可能至少需要把已删除的节点记录下来，以防止又被撤销回去而被一直占用的情况，这里还没想好怎么处理
        if (items[idx].bRecursionCheckIn)
            continue;

        this->add(pTNode);
    }

    // 如果需要申领的节点全部被申领，则认为申领成功
    return successCnt == items.size();
}

WDNode::Nodes WDBMClaimMgr::checkOut(const WDNode::Nodes& nodes, bool& bCancelCheckOut, bool bShowMessage)
{
    bCancelCheckOut = false;
    if (nodes.empty())
    {
        assert(false);
        return {};
    }

    // 未被启用时，不可能成功签出节点
    if (!_enabled)
        return {};

    // 未指定签出回调，不可能成功签出节点
    if (!_funcCheckOutBefore)
    {
        assert(false);
        return {};
    }

    // 用于防止节点被重复添加从而出现重复申领
    AddedNodes aNodes;

    // 首先要从签入的列表中查找这些节点，如果节点未在签入的列表中，则跳过
    CheckItems items;
    for (auto pNode : nodes) 
    {
        if (pNode == nullptr)
            continue;
        if (!contains(pNode))
            continue;
        if (!aNodes.add(pNode))
            continue;
        auto isAudit = _bmBase.auditObjectMgr().contains(std::string(pNode->type()));
        items.push_back(CheckItem(pNode, isAudit, false));
    }
    // 要签出的节点列表为空，说明已全部签出
    if (items.empty())
        return nodes;

    // 执行签出，并返回签出成功的节点列表
    auto rIndices = _funcCheckOutBefore(items, bShowMessage, bCancelCheckOut);

    WDNode::Nodes rNodes;
    rNodes.reserve(rIndices.size());
    for (auto idx : rIndices)
    {
        if (idx >= items.size())
            continue;

        auto pTNode = items[idx].pNode;
        if (pTNode == nullptr)
            continue;
        // 记录到返回列表
        rNodes.push_back(pTNode);
        // 将已签出的节点执行移除
        this->remove(pTNode);
    }

    return rNodes;
}
bool WDBMClaimMgr::checkOutAll(bool& bCancelCheckOut, bool bShowMessage)
{
    if (_nodes.empty())
        return true;

    WDNode::Nodes nodes;
    nodes.reserve(_nodes.size());
    for (auto pNode : _nodes) 
    {
        if (pNode == nullptr)
            continue;
        nodes.push_back(pNode);
    }

    auto rNodes = this->checkOut(nodes, bCancelCheckOut, bShowMessage);
    return rNodes.size() == nodes.size();
}

void  WDBMClaimMgr::add(WDNode::SharedPtr pNode)
{
    if (pNode == nullptr)
    {
        assert(false);
        return;
    }
    _nodes.insert(pNode);
}
void WDBMClaimMgr::remove(WDNode::SharedPtr pNode)
{
    if (pNode == nullptr)
    {
        assert(false);
        return;
    }
    _nodes.erase(pNode);
}
bool WDBMClaimMgr::contains(WDNode::SharedPtr pNode) const
{
    // 未启用，认定所有节点均被申领, 也就是所有节点均有权操作
    if (!_enabled)
        return true;

    if (pNode == nullptr)
    {
        assert(false);
        return false;
    }
    return _nodes.find(pNode) != _nodes.end();
}

WDBMClaimMgr::PNodeSet::PNodeSet(const WDBMClaimMgr& mgr)
    :_mgr(mgr)
{

}
WDBMClaimMgr::PNodeSet::~PNodeSet()
{

}

void WDBMClaimMgr::PNodeSet::add(const WDNode::Nodes& nodes)
{
    // 如果未启用，则不记录
    if (!_mgr.enabled())
        return;
    for (const auto& pNode : nodes)
    {
        if (pNode == nullptr)
            continue;
        // 不申领校审对象的子孙节点
        if (AuditTypeGet(_mgr._bmBase.auditObjectMgr(), *pNode) == At_AuditDescendant)
            continue;
        // 递归添加当前节点和子孙
        operateNodeSetRecur(pNode, [this](WD::WDNode::SharedPtr pTarNode) 
            { 
                _nodes.emplace(pTarNode);
            });
    }
}
void WDBMClaimMgr::PNodeSet::remove(const WDNode::Nodes& nodes)
{
    // 如果未启用，则不记录
    if (!_mgr.enabled())
        return;
    for (auto pNode : nodes)
    {
        if (pNode == nullptr)
            continue;
        // 递归删除当前节点和子孙
        operateNodeSetRecur(pNode, [this](WD::WDNode::SharedPtr pTarNode) 
            { 
                _nodes.erase(pTarNode);
            });
    }
}
void WDBMClaimMgr::PNodeSet::clear()
{
    _nodes.clear();
}
bool WDBMClaimMgr::PNodeSet::contains(WDNode::SharedPtr pNode) const
{
    // 如果未启用，则肯定不包含
    if (!_mgr.enabled())
        return false;
    if (pNode == nullptr)
        return false;
    return _nodes.find(pNode) != _nodes.end();
}
const WDBMClaimMgr::NodeSet& WDBMClaimMgr::PNodeSet::nodes() const
{
    return _nodes;
}
void WDBMClaimMgr::PNodeSet::operateNodeSetRecur(WDNode::SharedPtr pNode, const std::function<void(WD::WDNode::SharedPtr pTarNode)>& funcOperation)
{
    if (pNode == nullptr)
        return ;

    if (funcOperation)
        funcOperation(pNode);

    // 校审对象不再往下递归
    if (_mgr._bmBase.auditObjectMgr().checkType(*pNode))
        return ;

    for (const auto& pChild : pNode->children())
    {
        operateNodeSetRecur(pChild, funcOperation);
    }
}

bool WDBMClaimMgr::RecursionCheck(WDNode::SharedPtr pNode)
{
    if (pNode == nullptr)
        return false;

    // 如果当前节点是新增的节点，则直接返回true,不用再递归其子节点
    if (this->newCreatedNodes().contains(pNode))
    {
        return true;
    }
    // 当前节点如果是校审对象类型节点，不用再递归其子节点, 仅需要判断当前节点是否被成功签出
    else if (IsAuditType(_bmBase.auditObjectMgr(), *pNode))
    {
        return contains(pNode);
    }
    // 判断当前节点(层级节点)是否被申领，如果未被申领，直接返回false
    else if(!contains(pNode))
    {
        return false;
    }
    // 继续递归其子节点，判断子节点是否全部被申领
    else
    {
        // 子节点中只要有一个未被申领，则直接返回false
        for (auto pChild : pNode->children())
        {
            if (pChild == nullptr)
                continue;
            if (!RecursionCheck(pChild))
                return false;
        }
        // 这里表示所有子孙节点均被申领了
        return true;
    }
}

WD_NAMESPACE_END

