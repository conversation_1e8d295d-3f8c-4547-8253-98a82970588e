#pragma once

#include "../../../WDBDBase.h"
#include "../../private/WDBMDModelHelpter.h"

WD_NAMESPACE_BEGIN
/**
* @brief 通用的截面类型节点，SCTN 是 GENSEC的特殊件
SCTN 是专指那些工字型的，GENSEC 就包含各种外型的截面体
*/
class WD_API WDBMDStruGENSEC : public WDBDBase
    , public WDBMDModelHelpter::MDelegate
{
private:
    WDBMDModelHelpter   _modelHelpter;
public:
    WDBMDStruGENSEC(WDNode& node);
    virtual ~WDBMDStruGENSEC();
public:
    virtual void copy(const WDBDBase& src) override;
    virtual inline WDBDBase* clone(WDNode& node) const override
    {
        auto p = new WDBMDStruGENSEC(node);
        p->copy(*this);
        return p;
    }
public:
    virtual const WDSelectionInterface* selectionSupporter() const override;
    virtual WDGraphableInterface* graphableSupporter() override;
    virtual WDCollisibleInterface* collisibleSupporter();
protected:
    virtual void mData(WDCore& core, WDBMDModelHelpter::MData& data) override;
    virtual WDBMNodeRef pSpref() const override;
    virtual void gVars(WDCore& core, WDBMGVars& gVars) const override;
    virtual bool hasHoles() const override;
    virtual const WDNode* nodePtr() const override
    {
        return &(this->node());
    }
protected:
    virtual void onCollectGVars(WDCore& core, WDBMGVars& outGVars, bool bInsu) override;
    virtual void onModelUpdate(WDCore& core, WDNode& node) override;
};

/**
* @brief 截面
*/
class WD_API WDBMDSctnSCTN : public WDBDBase
    , public WDBMDModelHelpter::MDelegate
{
private:
    WDBMDModelHelpter   _modelHelpter;
public:
    WDBMDSctnSCTN(WDNode& node);
    virtual ~WDBMDSctnSCTN();
public:
    virtual void copy(const WDBDBase& src) override;
    virtual inline WDBDBase* clone(WDNode& node) const override
    {
        auto p = new WDBMDSctnSCTN(node);
        p->copy(*this);
        return p;
    }
public:
    virtual const WDSelectionInterface* selectionSupporter() const override;
    virtual WDGraphableInterface* graphableSupporter() override;
    virtual WDCollisibleInterface* collisibleSupporter() override;
protected:
    virtual void mData(WDCore& core, WDBMDModelHelpter::MData& data) override;
    virtual WDBMNodeRef pSpref() const override;
    virtual void gVars(WDCore& core, WDBMGVars& gVars) const override;
    virtual bool hasHoles() const override;
    virtual const WDNode* nodePtr() const override
    {
        return &(this->node());
    }
protected:
    virtual void onCollectGVars(WDCore& core, WDBMGVars& outGVars, bool bInsu) override;
    virtual void onModelUpdateBeforeChildren(WDCore& core, WDNode& node) override;
    virtual void onModelUpdate(WDCore& core, WDNode& node) override;
};

WD_NAMESPACE_END

