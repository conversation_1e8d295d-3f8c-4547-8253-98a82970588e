#include "WDBMDPipePIPCA.h"
#include "../../../../geometry/WDGeometryMgr.h"
#include "../../../../WDCore.h"

WD_NAMESPACE_BEGIN

WDBMDPipePIPCA::WDBMDPipePIPCA(WDNode& node)
    : WDBDBase(node)
    , _modelHelpter(*this)
{
}
WDBMDPipePIPCA::~WDBMDPipePIPCA()
{
}

WDBDBase* WDBMDPipePIPCA::clone(WDNode& node) const
{
    auto p = new WDBMDPipePIPCA(node);
    p->copy(*this);
    return p;
}
void WDBMDPipePIPCA::copy(const WDBDBase& src)
{
    if (this == &src)
        return;

    const WDBMDPipePIPCA* pSrc = dynamic_cast<const WDBMDPipePIPCA*>(&src);
    if (pSrc == nullptr)
        return;

    WDBDBase::copy(src);

    _modelHelpter.copy(pSrc->_modelHelpter);
}

const WDSelectionInterface* WDBMDPipePIPCA::selectionSupporter() const
{
    return &_modelHelpter;
}
WDGraphableInterface* WDBMDPipePIPCA::graphableSupporter()
{
    return &_modelHelpter;
}
void WDBMDPipePIPCA::mData(WDCore& core, WDBMDModelHelpter::MData& data)
{
    auto pGeom = core.geometryMgr().createSphere(15.0f);
    if (pGeom != nullptr)
        data.geoms.push_back(pGeom);
}
bool WDBMDPipePIPCA::hasHoles() const
{
    return false;
}

void WDBMDPipePIPCA::onModelUpdate(WDCore& core, WDNode& node)
{
    WDUnused(core);
    WDUnused(node);
    _modelHelpter.updateModel(core);
}

WD_NAMESPACE_END