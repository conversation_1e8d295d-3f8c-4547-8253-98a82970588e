#pragma once

#include "../../../WDBDBase.h"
#include "../../private/WDBMDModelHelpter.h"

WD_NAMESPACE_BEGIN

/**
* @brief 直管数据
*/
class WD_API WDBMDPipeTUBI : public WDBDBase
    , public WDBMDModelHelpter::MDelegate
{
private:
    // 直管几何体数据
    WDGeometry::SharedPtr   _pGeomStd;
    // 直管的保温几何体数据
    WDGeometry::SharedPtr   _pGeomInsu;
    // 模型生成
    WDBMDModelHelpter   _modelHelpter;
public:
    WDBMDPipeTUBI(WDNode& node);
    ~WDBMDPipeTUBI();
public:
    static void RegistAttribute(WDBMTypeDesc& des);
public:
    virtual WDBDBase* clone(WDNode& node) const override;
    virtual void copy(const WDBDBase& src) override;
public:
    virtual const WDSelectionInterface* selectionSupporter() const override;
    virtual WDGraphableInterface* graphableSupporter() override;
    virtual WDCollisibleInterface* collisibleSupporter() override;
protected:
    virtual void mData(WDCore& core, WDBMDModelHelpter::MData& data) override;
    virtual WDBMNodeRef pSpref() const override;
    virtual void mCollisions(WDCore& core, WDCollisions& cs) override;
    virtual bool hasHoles() const override;
    virtual const WDNode* nodePtr() const override
    {
        return &(this->node());
    }
protected:
    virtual void onModelUpdate(WDCore& core, WDNode& node) override;
private:
    /**
     * @brief 获取外径
    */
    double diameter() const;
};


WD_NAMESPACE_END

