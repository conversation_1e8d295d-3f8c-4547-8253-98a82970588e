#pragma     once

#include    "../../../node/WDNode.h"
#include    "../../dataType/WDBMGVars.h"

WD_NAMESPACE_BEGIN

class WDCore;
/**
* @brief 管线模块的一些通用方法, 内部接口
*/
class WD_API WDBMDPipeUtils
{
public:
    /**
     * @brief 区间类型
    */
    enum IntervalType
    {
        // 左闭右闭
        IT_LCRC = 0,
        // 左开右闭
        IT_LORC,
        // 左闭右开
        IT_LCRO,
        // 左开右开
        IT_LORO,
    };
    /**
     * @brief 校验 int 类型的 Answer
     * @param node 校验的节点, 可能是 SELE、SPCO等类型
     * @param value 要校验的值
     * @param iType 校验的范围对比方式，针对具有范围值的Answer生效
     *  - node 的Answer值与value相等，返回true
     *  - node 的Maxanswer属性有效，且 value 在 Answer, Maxanswer构成的区间(还可以通过IntervalType来决定区间类型)内时，返回true
    */
    static bool CheckAnswer(const WDNode& node, int value, IntervalType iType = IntervalType::IT_LCRC);
    /**
     * @brief 校验 double 类型的 Answer
     * @param node 校验的节点, 可能是 SELE、SPCO等类型
     * @param value 要校验的值
     * @param iType 校验的范围对比方式，针对具有范围值的Answer生效
     * @return 满足结果的子节点值:
     *  - node 的Answer值与value相等，返回true
     *  - node 的Maxanswer属性有效，且 value 在 Answer, Maxanswer构成的区间(还可以通过IntervalType来决定区间类型)内时，返回true
    */
    static bool CheckAnswer(const WDNode& node, double value, IntervalType iType = IntervalType::IT_LCRC);
    /**
     * @brief 校验 string 类型的 Answer
     * @param node 校验的节点, 可能是 SELE、SPCO等类型
     * @param value 要校验的值
     * @return node 的Answer值与value相等时，返回true
    */
    static bool CheckAnswer(const WDNode& node, const std::string& value);
    /**
     * @brief 校验TAnswer
     * @param node 校验的节点, 可能是 SELE、SPCO等类型
     * @param value 要校验的值 
     * @return node 的 "Tanswer" 属性与指定的 value 一致时，返回true
    */
    static bool CheckTAnswer(const WDNode& node, const std::string& value);
public:
    /**
     * @brief 判断节点是否是管件类型的节点(不包含直管("TUBI"))
     * @param node 节点
     * @return 是返回true，否则返回false
    */
    static bool IsPipeComponent(const WD::WDNode& node);
    /**
    * @brief 获取分支口点索引
    *   例如: 对于三通来说，除了入口点，出口点，还有一个分支口点
    *   这里的获取规则是: 入口点，出口点，分支口点number固定为1,2,3
    *   先判断入口点和出口点占用了1,2,3中的哪两个number,剩下的一个就是分支口点
    * @param node 要获取分支口点的管件节点
    * @return 返回分支口点的number。传入节点无效时，固定返回3
    */
    static int Fork(const WD::WDNode& node);
    /**
    * @brief 从指定SPEC下获取指定管径的管子等级节点, 获取规则:
    *   1. 从指定的SPEC节点下获取 Tanswer 属性为 "TUBE" 的SELE节点
    *   2. 再遍历该SELE节点下的所有子节点, 并获取到公称直径值与bore相等(或范围值包含bore)的SELE子节点
    *       说明: 这里使用bore比较时，先将bore转换为int类型，再将SELE子节点的 Answer、Maxanswer转换为int类型进行比较
    *   3. 获取到有效的SELE子节点后，依次拿第一个子节点，直到拿到叶子节点，如果叶子节点有效且是SPCO类型，证明找到了管子的等级节点, 返回结果
     * @param pSPECNode 等级分组节点
     * @param bore 公称直径
    */
    static WDNode::SharedPtr GetStubeBySPEC(WDNode::SharedPtr pSPECNode, const std::string& bore);
    /**
     * @brief 计算管件(包含直管)的保温参数
     * @param pipeComNode 管件节点
    */
    static std::optional<double> GetPipeComponentInsulation(WDCore& core, const WDNode& pipeComNode);
    /**
     * @brief 指定管件的等级节点，获取管件描述
     *  管件描述 = pSPCONode->detref->Rtext属性值 + pSPCONode->Matxt->Xtext属性
     * @param pSPCONode 管件的等级节点
     * @param gVars 管件的全局变量, 用于生成元件
     * @return 管件描述
    */
    static std::string GetPipeComponentDescription(WDCore& core
        , WDNode::SharedPtr pSPCONode
        , const WDBMGVars& gVars = {});
    /**
     * @brief 获取管件(包含直管:TUBI)的公称直径
     *  默认从引用的元件的第一个参数中获取公称直径
     * @param pSPCONode 管件的等级节点
     * @return 公称直径
    */
    static std::optional<int> GetPipeComponentBore(WDNode::SharedPtr pSPCONode);
    /**
     * @brief 获取管件(包含直管:TUBI)的外径
     *  默认从引用的元件的第二个参数中获取外径
     * @param pSPCONode 管件的等级节点
     * @return 外径
    */
    static std::optional<double> GetPipeComponentDiameter(WDNode::SharedPtr pSPCONode);
    /**
     * @brief 是否是弯通管件
     * @param node 管件节点对象
    */
    static bool IsBendComponent(WD::WDNode& node);
    /**
     * @brief 更新默认的半径为对应元件库全局变量（DDRADIUS）的值
    */
    static void UpdateDefaultRadiusToDDRADIUS(WD::WDNode& node);
public:
    /**
    * @brief 更新桥架分支的所有管件连接
    * @param pBranchNode 桥架分支节点
    */
    static bool UpdateConnectionCt(WDCore& core, WDNode::SharedPtr pCtBran);
    /**
    * @brief 获取分支的所有直管节点
    *   从头到尾按顺序排列
    */
    static std::vector<WDNode::SharedPtr> Tubings(WDNode::SharedPtr pBranchNode);
    /**
    * @brief 获取分支的所有管件节点
    *   从头到尾按顺序排列
    * @param pBranchNode 分支节点
    * @param comTypeFilter 用于过滤掉的管件类型
    *   例如返回的关键列表中不希望包含 弯头和三通， 则 comTypeFilter = {WDBMDesignType::BMDT_ELBO, WDBMDesignType::BMDT_TEE}
    */
    static std::vector<WDNode::SharedPtr> PipeComponents(WDNode::SharedPtr pBranchNode
        , const std::vector<std::string>& comTypeFilter = {});
    /**
    * @brief 获取分支下某个节点之前的一个管件
    * @param pBranchNode 分支节点
    * @param pBranchChildNode 分支下的某个子节点，函数将返回的是该节点的前一个管件节点
    * @param comTypeFilter 用于过滤掉的管件类型
    *   例如管件的逻辑顺序中不希望包含 附点， 则 comTypeFilter = {WDBMDesignType::BMDT_ATTA}
    * @return 如果没有前一个管件时, 返回nullptr
    */
    static WDNode::SharedPtr PrevPipeComponent(WDNode::SharedPtr pBranchNode
        , WDNode::SharedPtr pBranchChildNode
        , const std::vector<std::string>& comTypeFilter = {});
    /**
    * @brief 获取分支下某个节点之后的一个管件
    * @param pBranchNode 分支节点
    * @param pBranchChildNode 分支下的某个子节点，函数将返回的是该节点的后一个管件节点
    * @param comTypeFilter 用于过滤掉的管件类型
    *   例如管件的逻辑顺序中不希望包含 附点， 则 comTypeFilter = {WDBMDesignType::BMDT_ATTA}
    * @return 如果没有后一个管件时, 返回nullptr
    */
    static WDNode::SharedPtr NextPipeComponent(WDNode::SharedPtr pBranchNode
        , WDNode::SharedPtr pBranchChildNode
        , const std::vector<std::string>& comTypeFilter = {});
    /**
    * @brief 获取分支下某个节点临近的一个管件(同时向前和向后查找，直到找到离当前节点最近的一个管件)
    * @param pBranchNode 分支节点
    * @param pBranchChildNode 分支下的某个子节点，函数将返回的是与该节点最临近的一个管件节点
    * @param comTypeFilter 用于过滤掉的管件类型
    *   例如管件的逻辑顺序中不希望包含 附点， 则 comTypeFilter = {WDBMDesignType::BMDT_ATTA}
    * @return 如果没有临近管件时, 返回nullptr
    */
    static WDNode::SharedPtr AdjacentPipeComponent(WDNode::SharedPtr pBranchNode
        , WDNode::SharedPtr pBranchChildNode
        , const std::vector<std::string>& comTypeFilter = {});
    /**
     * @brief 重新调整分支内的管件顺序
     * @param pBranNode 分支节点
     * @param comTypeFilter 用于过滤掉的管件类型
    */
    static void RedressComponent(WDNode::SharedPtr pBranNode, const StringVector& comTypeFilter = {});
    /**
     * @brief 设置分支是否显示流向
     */
    static void SetPipelineDirectionDisplayEnabled(WDNode& branNode, bool enabled);
    /**
     * @brief 获取分支是否显示流向
     */
    static bool GetPipelineDirectionDisplayEnabled(const WDNode& branNode);
};


WD_NAMESPACE_END

