#pragma once

#include "../node/WDNode.h"
#include "../WDTranslate.h"
#include "typeMgr/WDBMAttrDesc.h"

WD_NAMESPACE_BEGIN

class WDCore;
class WDBMTypeMgr;
class WDBMActivityMgr;
class WDBMPermissionMgr;
class WDBMClaimMgr;
class WDBMAuditObjectMgr;
class WDBMAttrEnumDictMgr;
class WDBMRefUpdater;
class WDBMNodeSerialize;
class WDFmtSerialize;
class WDBMColorTable;
class WDBMColorIndexMgr;
class WDUndoCommand;

/**
 * @brief 业务模块基类
 *  WIZDesigner Business Module Base
*/
class WD_API WDBMBase
{
public:
    /**
     * @brief 构造
     * @param app core对象引用 
     * @param name 模块名称，不能为空且不能与已添加的模块名称重复
    */
    WDBMBase(WDCore& core, const std::string& name);
    WDBMBase(const WDBMBase& right) = delete;
    WDBMBase(WDBMBase&& right) = delete;
    WDBMBase& operator=(const WDBMBase& right) = delete;
    WDBMBase& operator=(WDBMBase&& right) = delete;
    virtual ~WDBMBase();
public:
    /**
     * @brief 获取模块名称
    */
    inline const std::string& name() const
    {
        return _name;
    }
    /**
    * @brief 初始化
    * 注意,初始化后根节点不会被创建,需加载项目后根节点才会被创建出来
    */
    bool init();
    /**
     * @brief 反始化
    */
    void uninit();
    /**
     * @brief 获取根节点,根节点会在加载项目后创建
    */
    inline WDNode::SharedPtr root() const
    {
        return _pRoot;
    }
public:
    inline WDCore& core() const
    {
        return _core;
    }
    /**
     * @brief 获取属性值的枚举字典管理
    */
    inline WDBMAttrEnumDictMgr& attrEnumDictMgr() const
    {
        return *_pAttrEnumDictMgr;
    }
    /**
    * @brief 获取数据类型管理
    */
    inline WDBMTypeMgr& typeMgr() const
    {
        return *_pTypeMgr;
    }
    /**
     * @brief 获取权限管理
    */
    inline WDBMPermissionMgr& permissionMgr() const
    {
        return *_pPermissionMgr;
    }
    /**
    * @brief 获取申领管理
    */
    inline WDBMClaimMgr& claimMgr() const
    {
        return *_pClaimMgr;
    }
    /**
     * @brief 获取校审对象管理
    */
    inline WDBMAuditObjectMgr& auditObjectMgr() const
    {
        return *_pAuditObjectMgr;
    }
    /**
     * @brief 获取类型颜色表
     */
    inline WDBMColorTable& colorTable() const 
    {
        return *_pColorTable;
    }
    /**
     * @brief 获取颜色表管理
    */
    inline WDBMColorIndexMgr& colorIndexMgr() const
    {
        return *_pColorIndexMgr;
    }
public:
    /**
     * @brief 判断某个节点是否属于当前模块
     *  即该节点是当前模块根节点的子孙节点
    */
    inline bool isCurrentModuleNode(WDNode& node) const
    {
        return (&node == _pRoot.get()) || (node.getBMBase() == this);
    }
    /**
    * @brief 从currNode开始(包括currNode)依次向上找父节点, 判断父节点是否可挂载type类型节点
    *   直到找到根节点为止, 若没有可挂载的父节点, 则返回空
    * @param currNode 当前节点
    * @param typeName 需要挂载的节点类型名称
    * @return 可挂载的节点
    */
    WDNode::SharedPtr findParentWithType(WDNode& currNode, const std::string_view& typeName) const;
    /**
     * @brief 校验名称是否合法
     *  名称上不支持空格，@和$
     * @param name 预校验的节点名称
     * @return 节点名称合法时返回true, 非法时返回false, 如果传入的节点名称为空字符串，则固定返回true
     */
    bool nameValid(const std::string_view& name) const;
    /**
     * @brief 校验节点名称是否已存在
     * @param name 预校验的节点名称
     * @return 节点名称存在时返回true, 不存在时返回false, 如果传入的节点名称为空字符串，则固定返回false
     */
    bool nameExists(const std::string_view& name) const;
    /**
     * @brief 批量校验名称是否已存在
     * @param names 需要校验的名称组
     * @return names中已存在的名称
    */
    std::set<std::string_view> nameExists(const std::vector<std::string>& names)const;
    /**
    * @brief 创建当前模块类型节点
    * @param pParent 父节点指针,除了根节点传nullptr之外，其他节点应该传有效的(内部会做类型校验)父节点对象
    * @param typeName 节点的类型名称
    * @param name 节点名称
    * @return 是否创建成功,创建失败时返回nullptr
    */
    WDNode::SharedPtr create(WDNode::SharedPtr pParent, const std::string_view& typeName
        , const std::string_view& name = "");
    /**
    * @brief 创建当前模块类型节点（不指定父节点）
    * @param typeName 节点的类型名称
    * @param name 节点名称
    * @return 是否创建成功,创建失败时返回nullptr
    */
    WDNode::SharedPtr create(const std::string_view& typeName
        , const std::string_view& name = "");
    /**
    * @brief 创建当前模块类型节点,插入的方式
    * @param pParent 父节点指针,这里必须传有效的父节点对象，否则创建失败
    * @param typeName 节点的类型名称
    * @param pNextNode 下一个节点数据,用来标识当前节点插入的位置,即pNextNode之前，如果传nullptr,则节点默认添加到最后
    * @param name 节点名称
    * @return 是否创建成功,如果父节点类型约束不正确或传入的当前模块数据对象无效，均有可能创建失败,创建失败时返回nullptr
    */
    WDNode::SharedPtr create(WDNode::SharedPtr pParent
        , const std::string_view& typeName
        , WDNode::SharedPtr pNextNode
        , const std::string_view& name = "");
    /**
    * @brief 设置节点的父节点, 即将某个节点添加到当前模块中
    *   1. 如果该节点或者将被设置的父节点类型不属于当前模块，将会设置失败
    *   2. 如果父节点为空，将会设置失败
    *   3. 如果该节点不能挂载到新的父节点上，将会设置失败
    * @param pNode 节点
    * @param pParentNode 将要被设置的父节点
    * @param pNextNode 如果指定了pNextNode且pNextNode是pParentNode的子节点，则pNode将会插入在pNextNode之前,否则将会默认添加到子列表尾
    * @param 自动更新标志，节点成功加入到当前模块中后，是否自动更新(自动对节点使用updateData方法,且自动调用节点的update方法)
    * @return 是否设置成功
    */
    bool setParent(WDNode::SharedPtr pNode
        , WDNode::SharedPtr pParentNode
        , WDNode::SharedPtr pNextNode
        , bool update = true);
    /**
    * @brief 设置节点的父节点, 即将某个节点添加到当前模块中
    *   这是一个重载方法, 不用指定 pNextNode
    * @return 是否设置成功
    */
    inline bool setParent(WDNode::SharedPtr pNode
        , WDNode::SharedPtr pParentNode
        , bool update = true)
    {
        return setParent(pNode, pParentNode, nullptr, update);
    }
    /**
    * @brief 销毁当前模块的某个节点
    *   如果该节点不属于当前模块，则销毁失败
    * @param pNode 当前模块节点
    * @return 销毁成功返回true 销毁失败返回false
    */
    void destroy(WDNode::SharedPtr pNode);
    /**
     * @brief 批量销毁当前模块的节点列表
     * @param nodes 节点列表
     * @param bRemoveDuplicates 是否自动去重
     *  1. 销毁列表中某个节点被重复添加时， 只保留一份
     *  2. 销毁列表中某个节点以及其祖先节点同时存在时，只保留祖先节点
    */
    void destroy(const WDNode::Nodes& nodes, bool bRemoveDuplicates = true);
    /**
    * @brief 克隆节点
    *   1. 将克隆源节点以及该节点的所有子孙节点
    *   2. 如果是根(WORLD类型)节点，将会克隆失败
    * @param pSrcNode 源节点, 源节点必须属于当前模块
    * @return 成功返回克隆的节点对象，失败返回Nullptr
    */
    WDNode::SharedPtr clone(WDNode::SharedPtr pSrcNode);
    /**
    * @brief 克隆节点
    *   1. 将克隆源节点以及该节点的所有子孙节点
    *   2. 如果是根(WORLD类型)节点，将会克隆失败
    * @param pSrcNode 源节点, 源节点必须属于当前模块
    * @param map 源节点与目标节点的映射关系
    * @return 成功返回克隆的节点对象，失败返回Nullptr
    */
    WDNode::SharedPtr clone(WDNode::SharedPtr pSrcNode, std::map<WD::WDNode::SharedPtr, WD::WDNode::SharedPtr>& map);
    /**
    * @brief 从当前模块根节点开始,根据节点名称查找类型模块的指定类型的节点
    *   将返回第一个查找到的指定名称的节点
    * @param name 要查找的节点名称
    * @param typeName 节点类型名称
    *   指定为 空字符串 时,从所有类型的节点中查找
    *   指定为 某一种确定类型 时,只从该类型的节点中查找
    * @return 如果查找到，则返回对应节点指针，否则返回nullptr
    */
    WDNode::SharedPtr findNode(const std::string_view& name, const std::string_view& typeName = "") const;
    /**
    * @brief 从当前模块根节点开始,根据节点GUID查找当前模块的指定类型的节点
    *   将返回第一个查找到的指定guid的节点
    * @param guid 要查找的节点guid
    * @param type 节点类型
    *   指定为 空字符串 时,从所有类型的节点中查找
    *   指定为 某一种确定类型 时,只从该类型的节点中查找
    * @return 如果查找到，则返回对应节点指针，否则返回nullptr
    */
    WDNode::SharedPtr findNode(const WDUuid& guid, const std::string_view& typeName = "") const;
    /**
    * @brief 从当前模块根节点开始,根据节点GUID查找当前模块的指定类型的节点（批量查找）
    *   将返回所有查找到的指定guid的节点
    * @param guid 要查找的节点guid
    * @return 返回数组大小和顺序与传入数组大小和顺序保持一致，没有找到的用nullptr占位
    */
    WDNode::Nodes findNodes(const std::vector<WDUuid>& guids) const;
    /**
    * @brief 更新节点以及其子孙节点的引用属性(从node开始递归)
    * @param node 要更新引用的节点对象
    */
    bool updateRefs(WDNode& node) const;
    /**
     * @brief 更新节点以及其子孙节点的引用属性(从node开始递归), 批量更新
    * @param nodes 要更新引用的节点对象列表
    */
    void updateRefs(const WDNode::Nodes& nodes) const;
public:
    // 校验结果
    enum CheckResult
    {
        // 未知错误
        CR_Unknown = 0,
        // 校验成功
        CR_Success,
        // 被锁定
        CR_Locked,
        // 无权限
        CR_NoPermission
    };
    // 校验标志
    enum CheckFlag
    {
        // 无
        CF_None = 0,
        // 锁定校验
        CF_Lock = 1 << 0,
        // 权限校验
        CF_Permission = 1 << 1,
        // 全部校验
        CF_All = CF_Lock | CF_Permission,
    };
    using CheckFlags = WDFlags<CheckFlag, uint>;
    // 接受校验的操作
    enum  CheckOperation
    {
        // 编辑属性
        CO_EditAttribute = 0,
        // 添加子
        CO_AddChild,
        // 移除子
        CO_RemoveChild,
    };
    // 校验结果通过回调函数
    using CheckTipNotice = std::function<void(CheckResult ret,const WDNode& node, CheckOperation operation, CheckFlags flags, WDBMBase& sender)>;
    /**
     * @brief 节点校验
     * @param node 节点
     * @param operation 接受校验的操作
     * @param flags 校验标志
     * @param bAutoTip 是否提示
     * @return 校验结果
    */
    CheckResult check(const WDNode& node, const CheckOperation& operation, const CheckFlags& flags = CF_All, bool bAutoTip = false);
    /**
     * @brief 获取校验结果通知回调函数
    */
    inline CheckTipNotice& checkTipNotice()
    {
        return _checkTipNotice;
    }
public:
    /**
     * @brief 进度报告回调
    */
    using FuncProgress = std::function<void(double progress, const char* text)>;
    /**
     * @brief 保存当前模块的节点数据到指定文件
     *  目前支持: .json格式, 根据文件后缀名判断格式
    */
    bool save(const std::string_view& fileName);
    /**
     * @brief 从指定文件加载当前模块的节点数据
     *  目前支持: .json格式, 根据文件后缀名判断格式
    */
    bool load(const std::string_view& fileName, const std::string_view& cfgFilePath = "", FuncProgress funcProgress = FuncProgress());
public:
    /**
     * @brief 获取当前模块某个类型的翻译
     * @param typeName 类型名称
     * @return 翻译结果 utf-8
    */
    inline const std::string& trT(const std::string& typeName) const
    {
        return WDTranslate::Instance()->translate(_tsTypeCxtName, typeName);
    }
    /**
     * @brief 获取当前模块某个类型属性的翻译
     * @param attributeName 属性名称
     * @return 翻译结果 utf-8
    */
    std::string trA(const std::string& attributeName) const;
    /**
     * @brief 获取当前模块某个属性值枚举字典key的翻译
     * @param enumDictionaryName 属性值枚举字典名称, 需要用字典名称自动拼接翻译上下文
     * @param enumDictionaryKey 属性值枚举字典key
     * @return 属性值枚举字典key的翻译结果 utf-8
    */
    const std::string& trEDK(const std::string& enumDictionaryName, const std::string& enumDictionaryKey) const;
public:
    /**
     * @brief 创建节点属性设置命令
     * @param pNode 节点
     * @param attrName 属性名称
     * @param value 要设置的属性值
     * @return 命令对象指针，当参数无效时返回nullptr
    */
    WDUndoCommand* makeAttributeSetCommand(WDNode::SharedPtr pNode
        , const std::string_view& attrName
        , const WDBMAttrValue& value
        , WDUndoCommand* pParentCmd = nullptr);
    /**
    * @brief 创建已设置节点属性命令
    * @param pNode 节点
    * @param attrName 属性名称
    * @param prevValue 设置前的属性值，即在执行属性设置操作之前的旧值
    * @param pParentCmd 父命令
    * @return 命令对象指针，当参数无效时返回nullptr
    */
    WDUndoCommand* makeAttributeSetedCommand(WDNode::SharedPtr pNode
        , const std::string_view& attrName
        , const WDBMAttrValue& prevValue
        , WDUndoCommand* pParentCmd = nullptr);
    // 撤销重做命令属性
    struct CmdAttribute
    {
        // 属性名称
        std::string name;
        // 属性值
        WDBMAttrValue value;
        CmdAttribute(std::string_view v1, const WDBMAttrValue& v2)
            : name(v1)
            , value(v2)
        {
        }
    };
    // 撤销重做命令节点属性
    struct CmdNodeAttributes
    {
        // 节点
        WDNode::WeakPtr pWNode;
        // 属性列表
        std::vector<CmdAttribute> attrs;
        CmdNodeAttributes(WDNode::SharedPtr v1, const std::vector<CmdAttribute>& v2)
            : pWNode(v1)
            , attrs(v2)
        {
        }
    };
    /**
    * @brief 创建批量已设置节点属性命令
    * @param prevNodeAttrs 设置前的属性值，即在执行属性设置操作之前的旧值
    * @param pParentCmd 父命令
    * @return 命令对象指针，当参数无效时返回nullptr
    */
    WDUndoCommand* makeAttributesSetedCommand(const std::vector<CmdNodeAttributes>& prevNodeAttrs
        , WDUndoCommand* pParentCmd = nullptr);
    /**
     * @brief 创建节点创建命令
     * @param pParentNode 节点的父节点，必须指定
     * @param typeName 类型名称,必须指定
     * @param name 节点名称，可以不指定
     * @param pNextNode 下一个节点，指定时代表插入，不指定则默认追加到尾
     * @return 命令对象指针，当参数无效时返回nullptr
    */
    WDUndoCommand* makeCreateCommand(WDNode::SharedPtr pParentNode
        , const std::string_view& typeName
        , const std::string_view& name = ""
        , WDNode::SharedPtr pNextNode = nullptr
        , WDUndoCommand* pParentCmd = nullptr);
    /**
     * @brief 创建节点命令
     * @param nodes 已经被创建出来的节点
     * @param pParentCmd 
     * @return 
    */
    WDUndoCommand* makeCreatedCommand(const WDNode::Nodes& nodes, WDUndoCommand* pParentCmd = nullptr);
    /**
    * @brief 创建节点镜像命令
    * @param nodes 要进行镜像操作的节点列表
    * @param pParentCmd 
    * @return 
    */
    WDUndoCommand* makeMirrorCommand(const WDNode::Nodes& nodes
        , const DVec3& normal
        , const DVec3& center
        , WDUndoCommand* pParentCmd = nullptr);
    /**
     * @brief 创建节点销毁命令
     * @param nodes 要销毁的节点列表
     * @return 命令对象指针，当参数无效时返回nullptr
    */
    WDUndoCommand* makeDestroyCommand(const WDNode::Nodes& nodes, WDUndoCommand* pParentCmd = nullptr);
    /**
     * @brief 创建添加到场景的命令
     * @param nodes 目标节点
     * @return 
    */
    WDUndoCommand* makeSceneAddCommand(const WDNode::Nodes& nodes, WDUndoCommand* pParentCmd = nullptr);
    /**
    * @brief 创建从场景移除的命令
    * @param nodes 目标节点
    * @return 
    */
    WDUndoCommand* makeSceneRemoveCommand(const WDNode::Nodes& nodes, WDUndoCommand* pParentCmd = nullptr);

    struct NodeHierarchyMoveInfo
    {
        // 当前操作节点
        WD::WDNode::WeakPtr pNode;
        // 目标父节点,可以是操作节点的父节点,但是不能为空
        WD::WDNode::WeakPtr pTargetParent;
        // 目标后置兄弟节点, 当目标位置没有后置兄弟节点(所有子列表的最后一个)时, 传入nullptr
        WD::WDNode::WeakPtr pTargetNextNode;
        /**
         * @param pNode 当前操作节点
         * @param pTargetParent 目标父节点,可以是操作节点的父节点,但是不能为空
         * @param pTargetNextNode 目标后置兄弟节点, 当目标位置没有后置兄弟节点(所有子列表的最后一个)时, 传入nullptr
        */
        NodeHierarchyMoveInfo(WD::WDNode::SharedPtr pNode, WD::WDNode::SharedPtr pTargetParent, WD::WDNode::SharedPtr pTargetNextNode = nullptr)
            : pNode(pNode), pTargetParent(pTargetParent), pTargetNextNode(pTargetNextNode)
        {

        }
    };
    using NodeHierarchyMoveInfos = std::vector<NodeHierarchyMoveInfo>;
    /**
    * @brief 创建
    * @param pNode 节点
    * @param attrName 属性名称
    * @param value 要设置的属性值
    * @return 命令对象指针，当参数无效时返回nullptr
    */
    WDUndoCommand* makeNodeHierarchyMoveInfoCommand(const NodeHierarchyMoveInfos& infos, WDUndoCommand* pParentCmd = nullptr);
protected:
    /**
    * @brief 初始化
    *   子类实现
    */
    virtual bool initP() = 0;
    /**
    * @brief 创建根节点
    *   子类实现
    */
    virtual WD::WDNode::SharedPtr createRoot() = 0;
    /**
    * @brief 初始化
    *   子类实现
    */
    virtual void uninitP() = 0;
    /**
     * @brief 收集当前模块类型以及属性的翻译文件名称列表(全路径名称)
    */
    virtual std::vector<std::string> collectTranslationFiles() const 
    {
        return {};
    }
    /**
     * @brief 节点删除之前通知
     * @param pNode 要删除的节点
    */
    virtual void onNodeDestroyBefore(WDNode::SharedPtr pNode) const
    {
        WDUnused(pNode);
    }
    /**
     * @brief 节点删除之后通知
     * @param pParentNode 被删除节点的父节点
    */
    virtual void onNodeDestroyAfter(WDNode::SharedPtr pParentNode) const
    {
        WDUnused(pParentNode);
    }
    /**
     * @brief 节点设置父节点之前通知
     * @param pParentNode 被设置父节点的节点的父节点
    */
    virtual void onNodeSetParentBefore(WDNode::SharedPtr pParentNode) const
    {
        WDUnused(pParentNode);
    }
    /**
     * @brief 节点设置父节点之后通知
     * @param pParentNode 被设置父节点的节点的父节点
    */
    virtual void onNodeSetParentAfter(WDNode::SharedPtr pParentNode) const
    {
        WDUnused(pParentNode);
    }
    /**
     * @brief 更新已经被收集的节点的引用，由子类实现
    */
    virtual void updateNodeRef(WDBMRefUpdater& collector) const = 0;
private:
    // 模块名称
    std::string             _name;
    // 核心对象
    WDCore&                 _core;
    // 根节点
    WDNode::SharedPtr       _pRoot;
    // 属性值的枚举字典管理
    WDBMAttrEnumDictMgr*    _pAttrEnumDictMgr;
    // 类型管理
    WDBMTypeMgr*            _pTypeMgr;
    // 权限管理
    WDBMPermissionMgr*      _pPermissionMgr;
    // 申领管理
    WDBMClaimMgr*           _pClaimMgr;
    // 校审对象管理
    WDBMAuditObjectMgr*     _pAuditObjectMgr;
    // 类型颜色表
    WDBMColorTable*         _pColorTable;
    // 颜色表管理
    WDBMColorIndexMgr*      _pColorIndexMgr;
    // 序列化对象
    WDBMNodeSerialize*      _serialize;
    // 序列化对象
    WDFmtSerialize*         _fmtSerialize;

    // 翻译类型使用的上下文名称
    std::string             _tsTypeCxtName;
    // 翻译属性使用的上下文名称
    std::string             _tsAttrCxtName;
    // 翻译枚举字典key使用的上下文名称(Enum Dictionary Key)
    std::string             _tsEDKeyCxtName;
    // 校验提示回调函数
    CheckTipNotice          _checkTipNotice;
};

WD_NAMESPACE_END

