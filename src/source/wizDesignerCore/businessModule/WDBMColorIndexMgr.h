#pragma once
#include "WDCore.h"
#include "../math/Color.hpp"

WD_NAMESPACE_BEGIN


class WDBMBase;
/**
 * @brief 颜色表
 */
class  WD_API WDBMColorIndexMgr
{
public:
    WDBMColorIndexMgr(WDBMBase& bmBase);
    ~WDBMColorIndexMgr();
public:
    /**
     * @brief 往颜色表中添加颜色
     * @param colorName 颜色名称，不能为空, 不能与已有颜色名称重复
     * @param color 颜色值
     * @return 是否添加成功, 当指定名称为空，或名称已存在时，添加失败
    */
    bool add(const std::string& colorName, const Color& color);
    /**
     * @brief 替换颜色表中的颜色
     * @param colorName 颜色名称
     * @param color 颜色值
     * @return 是否替换成功, 当指定的名称已存在时，替换成功
    */
    bool replace(const std::string& colorName, const Color& color);
    /**
     * @brief 删除颜色表中指定的项
     * @param colorName 颜色名称
    */
    bool remove(const std::string& colorName);
    /**
     * @brief 给定名称查找颜色表中的颜色值
     * @param colorName 颜色名称
     * @return 查找的颜色值
    */
    std::optional<Color> find(const std::string& colorName) const;
    /**
     * @brief 清空颜色表
    */
    inline void clear()
    {
        _colorTableMap.clear();

        resetDict();
    }
    /**
     * @brief 获取颜色表中的所有已添加项
    */
    inline const std::map<std::string, Color>& getColorTable() const
    {
        return _colorTableMap;
    }
public:
    /**
     * @brief 解析xml文件填充颜色表
    */
    bool fromXmlData(const char* pData);
    /**
     * @brief 将颜色表中的数据写到xml文件中
    */
    std::string toXmlData() const;

    /**
     * @brief 解析xml文件填充颜色表
    */
    bool fromXml(const char* fileName);
    /**
     * @brief 将颜色表中的数据写到xml文件中
    */
    bool toXml(const char* fileName) const;
private:
    void resetDict();
private:
    WDBMBase& _bmBase;
    std::map<std::string, Color> _colorTableMap;
};
WD_NAMESPACE_END
