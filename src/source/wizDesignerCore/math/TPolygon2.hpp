#pragma once

#include "TTriangle2.hpp"
#include "TEarcut.hpp"

#include <list>

WD_NAMESPACE_BEGIN

/**
 * @brief 2D多边形类
*/
template <typename T>
class TPolygon2
{
    static_assert(std::is_arithmetic_v<T>, "模板参数类型必须是算数类型!");
public:
    using ValueType     = T;
    using value_type    = ValueType;
    using SizeType      = size_t;
    using size_type     = SizeType;
public:
    /**
     * @brief 顶点列表
    */
    using Vertices  = TVec2Vector<T>;
    /**
     * @brief 顶点列表
    */
    template <class U>
    using UVertices = TVec2Vector<U>;
private:
    Vertices _vs;
public:
    inline TPolygon2(const Vertices& vs = Vertices()) :_vs(vs)
    {}
    inline TPolygon2(Vertices&& vs) : _vs(std::forward<Vertices>(vs))
    {}
    inline TPolygon2(const TPolygon2<T>& right) : _vs(right._vs)
    {}
    inline TPolygon2(TPolygon2<T>&& right)
    {
        _vs = std::forward<Vertices>(right._vs);
    }
public:
    /**
     * @brief 添加点
    */
    inline TPolygon2<T>& add(const TVec2<T>& pt)
    {
        _vs.push_back(pt);
        return *this;
    }
    /**
     * @brief 是否为空(即不包含任何顶点)
    */
    inline bool empty()const
    {
        return _vs.empty();
    }
    /**
     * @brief 顶点个数
    */
    inline size_t size() const
    {
        return _vs.size();
    }
    /**
     * @brief 下标访问
    */
    inline TVec2<T>& at(size_t index)
    {
        return _vs.at(index);
    }
    /**
     * @brief 下标访问
    */
    inline const TVec2<T>& at(size_t index) const
    {
        return _vs.at(index);
    }
    /**
     * @brief 获取对应索引的前一个顶点索引
    */
    inline size_t prevIndex(size_t index) const
    {
        return index == 0 ? this->size() - 1 : index - 1;
    }
    /**
     * @brief 获取对应索引的前一个顶点
    */
    inline TVec2<T>& prev(size_t index)
    {
        return _vs.at(prevIndex(index));
    }
    /**
     * @brief 获取对应索引的前一个顶点
    */
    inline const TVec2<T>& prev(size_t index) const
    {
        return _vs.at(prevIndex(index));
    }
    /**
     * @brief 获取对应索引的后一个顶点索引
    */
    inline size_t nextIndex(size_t index) const
    {
        return (index == (this->size() - 1)) ? 0 : index + 1;
    }
    /**
     * @brief 获取对应索引的后一个顶点
    */
    inline TVec2<T>& next(size_t index)
    {
        return _vs.at(nextIndex(index));
    }
    /**
     * @brief 获取对应索引的后一个顶点
    */
    inline const TVec2<T>& next(size_t index) const
    {
        return _vs.at(nextIndex(index));
    }
    /**
     * @brief 获取顶点列表
    */
    inline const Vertices& vertices() const
    {
        return _vs;
    }
    /**
     * @brief 设置顶点列表
    */
    inline void setVertices(const Vertices& vs)
    {
        _vs = vs;
    }
    /**
     * @brief 设置顶点列表
    */
    inline void setVertices(Vertices&& vs)
    {
        _vs = std::forward<Vertices>(vs);
    }
    /**
     * @brief 设置顶点列表
    */
    template<class U>
    void setVertices(const UVertices<U>& vs)
    {
        _vs.reserve(vs.size());
        for (size_t i = 0; i < vs.size(); ++i)
        {
            _vs.push_back(TVec2<T>(vs[i]));
        }
    }
    /**
     * @brief 指定向量移动所有顶点
    */
    TPolygon2<T>& moveVertices(const TVec2<T>& vec)
    {
        for (size_t i = 0; i < _vs.size(); ++i)
        {
            _vs[i] += vec;
        }
        return *this;
    }
    /**
     * @brief 计算多边形有符号面积
    */
    inline T area() const
    {
        return TPolygon2<T>::Area(_vs);
    }
    /**
     * @brief 顶点是否按照顺时针排列
    */
    inline bool isCW() const
    {
        return TPolygon2<T>::IsCW(_vs);
    }
    /**
     * @brief 顶点是否按照逆时针排列
    */
    inline bool isCCW() const
    {
        return TPolygon2<T>::IsCCW(_vs);
    }
    /**
     * @brief 所有顶点是否在同一条直线上，即不能构成多边形
    */
    inline bool isCollinear() const
    {
        return TPolygon2<T>::IsCollinear(_vs);
    }
    /**
     * @brief 反转所有顶点的顺序 eg:{0,1,2,3,4} => {4,3,2,1,0}
    */
    inline TPolygon2<T>& reverse()
    {
        std::reverse(_vs.begin(), _vs.end());
        return *this;
    }
    /**
     * @brief 指定顶点的索引范围反转多边形顶点顺序
     * @param sIndex 起始索引
     * @param eIndex 结束索引
    */
    inline TPolygon2<T>& reverse(size_t sIndex, size_t eIndex)
    {
        std::reverse(_vs.begin() + sIndex, _vs.end() + eIndex);
        return *this;
    }
    /**
     * @brief 是否是简单多边形
    */
    bool isSample() const
    {
        return TPolygon2<T>::IsSample(_vs);
    }
    /**
     * @brief 是否为凸多边形
    *    !注意:这里多边形顶点顺序需要以逆时针方式排列, 并且必须是简单多边形
    */
    bool isConvex() const
    {
        return TPolygon2<T>::IsConvex(_vs);
    }
    /**
     * @brief 点是否在多边形中
    */
    bool contains(const TVec2<T>& pt)
    {
        return TPolygon2<T>::Contains(_vs, pt);
    }
    /**
     * @brief 计算凸包
     *   !注意:这里多边形顶点顺序需要以逆时针方式排列, 并且必须是简单多边形
    */
    std::vector<size_t> convexHull() const
    {
        return TPolygon2<T>::ConvexHullSamplePolygon(_vs);
    }
    /**
     * @brief 三角剖分
     *  !注意:这里多边形顶点顺序需要以逆时针方式排列, 并且必须是简单多边形
     * @return 返回三角形的索引列表,每三个点构成一个三角形
    */
    template <typename IndexT = size_t>
    std::vector<IndexT> triangulate() const
    {
        std::vector<IndexT> ret;
        if (this->size() < 3)
            return ret;
        if (this->size() == 3)
            return { 0, 1, 2 };

        return TEarcut<TVec2<T>, IndexT>::Exec(_vs);
    }
    /**
     * @brief 俩顶点索引构成的对角线
    */
    using Diagonal = std::pair<size_t, size_t>;
    /**
     * @brief 对角线列表
    */
    using Diagonals = std::vector<Diagonal>;
    /**
     * @brief 三角剖分
     *  !注意:这里多边形顶点顺序需要以逆时针方式排列, 并且必须是简单多边形
     * @return 只返回三角剖分的对角线数据
    */
    Diagonals triangulateDiagonals() const
    {
        Diagonals ret;
        //小于三个顶点，不会产生对角线
        if (this->size() <= 3)
            return ret;
        SubA sub = SubA::FromParent(*this);
        sub.earcut(ret);
        return ret;
    }
    /**
     * @brief 快速次优凸分解,如果是凹多边形，将分解为多个凸多边形
     *  !注意:这里多边形顶点顺序需要以逆时针方式排列, 并且必须是简单多边形
     * @return 返回分割凸多边形的对角线列表
    */
    Diagonals decomp() const
    {
        Diagonals tDia0;
        SubA subA = SubA::FromParent(*this);
        subA.earcut(tDia0);
        SubB subB(*this);
        Diagonals tDia1;
        subB.incorporate(tDia0, tDia1);
        return tDia1;
    }
    /**
     * @brief 构成子多边形的索引
    */
    using SubPolygon = std::vector<size_t>;
    /**
     * @brief 子多边形索引列表
    */
    using SubPolygons = std::vector<SubPolygon>;
    /**
     * @brief 根据指定的对角线列表，将多边形分割成多个子多边形
     *  !注意:这里多边形顶点顺序需要以逆时针方式排列, 并且必须是简单多边形
    */
    SubPolygons decomp(const Diagonals& diagonals) const
    {
        SubPolygons ret;
        SubB sub(*this);
        sub.decomp(diagonals, ret);
        return ret;
    }
    /**
     * @brief 顶点a是否可以看到顶点b
     *  如果顶点a能看到顶点b，则表明:
     *      1.顶点a,b构成的对角线肯定在多边形内
     *      2.顶点a,b构成的对角线不会与其他边相交
    */
    bool canSee(const size_t& a, const size_t& b)
    {
        const TVec2<T>& ptA = at(a);
        const TVec2<T>& ptB = at(b);
        const size_t aPrev = prevIndex(a);
        const size_t aNext = nextIndex(a);
        const TVec2<T>& ptA0 = at(aPrev);
        const TVec2<T>& ptA1 = at(aNext);
        //
        bool bLOn0 = TTriangle2<T>::LeftOn(ptA1, ptA, ptB);
        bool bROn0 = TTriangle2<T>::RightOn(ptA0, ptA, ptB);
        if (bLOn0 && bROn0)
            return false;

        const T dist = TVec2<T>::DistanceSq(ptA, ptB);
        for (size_t i = 0; i < this->size(); ++i) //遍历多边形的每条边
        {
            const size_t iNext = nextIndex(i);
            if (iNext == a || i == a) //忽略顶点的临边
                continue;
            const TVec2<T>& ptI = at(i);
            const TVec2<T>& ptI1 = at(iNext);

            //计算三角形有符号面积
            const T area1 = TTriangle2<T>::Area(ptA, ptB, ptI1);
            const T area2 = TTriangle2<T>::Area(ptA, ptB, ptI);
            //近似等于0,说明: ptA, ptB, ptI1 共线
            if (Eq(area1, T(0)))
            {
                //如果该边线端点到视点的距离小于 dist, 说明视线被遮挡
                const T dist1 = TVec2<T>::DistanceSq(ptA, ptI1);
                if (dist1 < dist)
                    return false;
            }
            //近似等于0,说明: ptA, ptB, ptI 共线
            if (Eq(area2, T(0)))
            {
                //如果该边线端点到视点的距离小于 dist, 说明视线被遮挡
                const T dist1 = TVec2<T>::DistanceSq(ptA, ptI);
                if (dist1 < dist)
                    return false;
            }
            if ((area1 > T(0) && area2 < T(0))) // 说明线段两个端点分布在ptA,ptB所构成直线的两侧,并且在ptA到ptB的视线方向上
            {
                //对角线与某条边的交点
                const TVec2<T> p = LineInt(TLine(ptA, ptB), TLine(ptI, ptI1));
                const T tDist = TVec2<T>::DistanceSq(ptA, p);
                if (tDist < dist) //如果这条边遮挡了顶点a到b的视线
                {
                    return false;
                }
            }
        }

        return true;
    }
public:
    /**
     * @brief 下标访问
    */
    inline TVec2<T>& operator[](size_t index)
    {
        return _vs[index];
    }
    /**
     * @brief 下标访问
    */
    inline const TVec2<T>& operator[](size_t index) const
    {
        return _vs[index];
    }
    /**
     * @brief 赋值运算
    */
    inline TPolygon2<T>& operator=(const TPolygon2<T>& right)
    {
        _vs = right._vs;
    }
    /**
     * @brief 赋值运算
    */
    inline TPolygon2<T>& operator=(TPolygon2<T>&& right)
    {
        _vs = std::forward<Vertices>(right._vs);
    }
    /**
     * @brief == 运算
    */
    friend inline bool operator==(const TPolygon2<T>& left, const TPolygon2<T>& right)
    {
        return left._vs == right._vs;
    }
    /**
     * @brief > 运算
    */
    friend inline bool operator>(const TPolygon2<T>& left, const TPolygon2<T>& right)
    {
        return left._vs > right._vs;
    }
    /**
     * @brief < 运算
    */
    friend inline bool operator<(const TPolygon2<T>& left, const TPolygon2<T>& right)
    {
        return left._vs < right._vs;
    }
    /**
     * @brief != 运算
    */
    friend inline bool operator!=(const TPolygon2<T>& left, const TPolygon2<T>& right)
    {
        return !(left == right);
    }
    /**
     * @brief >= 运算
    */
    friend inline bool operator>=(const TPolygon2<T>& left, const TPolygon2<T>& right)
    {
        return !(left < right);
    }
    /**
     * @brief <= 运算
    */
    friend inline bool operator<= (const TPolygon2<T>& left, const TPolygon2<T>& right)
    {
        return !(left > right);
    }
public:
    /**
    * @brief 计算多边形有符号面积
    */
    template <typename TVec>
    static auto Area(const std::vector<TVec>& contour)
    {
        size_t n    = contour.size();
        T a         = T(0);
        for (size_t p = n - 1, q = 0; q < n; p = q++)
        {
            a += contour[p].x * contour[q].y - contour[q].x * contour[p].y;
        }
        return a * T(0.5);
    }
    /**
    * @brief 是否顺时针排列
    */
    template <typename TVec>
    static inline bool IsCW(const std::vector<TVec>& pts)
    {
        return TPolygon2<T>::Area(pts) < T(0);
    }
    /**
     * @brief 是否逆时针排列
    */
    template <typename TVec>
    static inline bool IsCCW(const std::vector<TVec>& pts)
    {
        return TPolygon2<T>::Area(pts) > T(0);
    }
    /**
     * @brief 所有顶点是否共线
    */
    template <typename TVec>
    static inline bool IsCollinear(const std::vector<TVec>& pts)
    {
        return Abs(TPolygon2<T>::Area(pts)) <= NumLimits<T>::Epsilon;
    }
    /**
     * @brief 计算是否是凹顶点
     * @return 是否是凹顶点:
     *  1. 如果 多边形以逆时针排序, 返回 true 表示该点为凹顶点
     *  2. 如果 多边形以顺时针排序, 返回 false 表示该点为凹顶点
    */
    static inline bool IsReflex(const TVec2<T>& ptPrev, const TVec2<T>& pt, const TVec2<T>& ptNext)
    {
        return TTriangle2<T>::Right(ptPrev, pt, ptNext);
    }
    /**
     * @brief 是否是凸多边形
    */
    static bool IsConvex(const std::vector<TVec2<T> >& pts)
    {
        for (size_t i = 0; i < pts.size(); ++i)
        {
            size_t prevIdx = (i == 0) ? (pts.size() - 1) : i - 1;
            size_t nextIdx = (i == pts.size() - 1) ? 0 : i + 1;

            if (IsReflex(pts[prevIdx], pts[i], pts[nextIdx]))
                return false;
        }
        return true;
    }
    /**
     * @brief 点是否在多边形中
     * @param polygon 多边形顶点列表
     * @param pt 点
    */
    static bool Contains(const std::vector< TVec2<T> >& polygon, const TVec2<T>& pt)
    {
        if (polygon.empty())
            return false;
        size_t polyLen = polygon.size();

        // inPt on polygon contour => immediate success    or
        // toggling of inside/outside at every single! intersection point of an edge
        //  with the horizontal line through inPt, left of inPt
        //  not counting lowerY endpoints of edges and whole edges on that line
        bool inside = false;
        for (size_t p = polyLen - 1, q = 0; q < polyLen; p = q++)
        {
            TVec2<T> edgeLowPt = polygon[p];
            TVec2<T> edgeHighPt = polygon[q];

            T edgeDx = edgeHighPt.x - edgeLowPt.x;
            T edgeDy = edgeHighPt.y - edgeLowPt.y;
            if ((T)(fabs(edgeDy)) > FLT_EPSILON)
            {
                // not parallel
                if (edgeDy < T(0))
                {
                    edgeLowPt = polygon[q]; edgeDx = -edgeDx;
                    edgeHighPt = polygon[p]; edgeDy = -edgeDy;
                }

                if ((pt.y < edgeLowPt.y) || (pt.y > edgeHighPt.y))
                    continue;

                if (pt.y == edgeLowPt.y)
                {
                    if (pt.x == edgeLowPt.x)
                        return	true;		// pt is on contour ?
                    // continue;				// no intersection or edgeLowPt => doesn't count !!!
                }
                else
                {
                    T perpEdge = edgeDy * (pt.x - edgeLowPt.x) - edgeDx * (pt.y - edgeLowPt.y);
                    if (perpEdge == T(0))
                        return	true;		// pt is on contour ?
                    if (perpEdge < T(0))
                        continue;
                    inside = !inside;		// true intersection left of pt
                }

            }
            else
            {
                // parallel or collinear
                if (pt.y != edgeLowPt.y)
                    continue;			// parallel
                // edge lies on the same horizontal line as pt
                if (((edgeHighPt.x <= pt.x) && (pt.x <= edgeLowPt.x))
                    || ((edgeLowPt.x <= pt.x) && (pt.x <= edgeHighPt.x)))
                    return	true;	// pt: Point on contour !
                // continue;
            }
        }
        return	inside;
    }
    /**
     * @brief 是否是简单多边形
     *  !TODO: 目前暂未实现, 总是返回false
    */
    static bool IsSample(const std::vector<TVec2<T> >& pts)
    {
        WDUnused(pts);
        return false;
    }
    /**
     * @brief 计算简单多边形凸包
     *   !注意:这里多边形顶点顺序需要以逆时针方式排列, 并且必须是简单多边形
     *  !TODO: 目前暂未实现
     *  针对简单多边形，有一个时间复杂度是O(n)的算法, 叫 Melkman's Convex Hull Algorithm(莫尔克曼凸包算法), 后续将采用此算法实现
     * @return 构成凸包的顶点索引
    */
    static std::vector<size_t> ConvexHullSamplePolygon(const std::vector<TVec2<T> >& pts)
    {
        return {};
    }
    /**
     * @brief 使用一系列的二维点构造2D多边形
    */
    static TPolygon2<T> MakeFromPoints(const Vertices& pts)
    {
        return TPolygon2<T>(pts);
    }
    /**
     * @brief 使用一系列的二维点构造2D多边形
    */
    static TPolygon2<T> MakeFromPoints(const Vertices&& pts)
    {
        return TPolygon2<T>(std::forward<Vertices>(pts));
    }
public:
    /**
    * @brief !TODO: 转换到字符串
    */
    inline char* toString(char* buf) const
    {
        return buf;
    }
    /**
    * @brief 转换到字符串
    */
    inline std::string toString() const
    {
        char buf[256] = { 0 };
        return this->toString(buf);
    }
    /**
    * @brief !TODO:  从字符串转换
    */
    inline TPolygon2<T>& fromString(const char* str)
    {
        WDUnused(str);
        return *this;
    }
    /**
    * @brief 从字符串转换
    */
    inline TPolygon2<T>& fromString(const std::string& str)
    {
        this->fromString(str.c_str());
        return *this;
    }
    /**
    * @brief 从字符串转换
    */
    static inline TPolygon2<T> FromString(const char* str)
    {
        TPolygon2<T> r;
        r.fromString(str);
        return r;
    }
    /**
    * @brief 从字符串转换
    */
    static inline TPolygon2<T> FromString(const std::string& str)
    {
        return TPolygon2<T>::FromString(str.c_str());
    }
private:
    //两个值是否近似相等
    static inline bool Eq(const T& a, const T& b)
    {
        return Abs(a - b) <= NumLimits<T>::Epsilon;
    }
    //两条直线交点
    using TLine = std::pair<TVec2<T>, TVec2<T> >;
    static TVec2<T> LineInt(const TLine& l1, const TLine& l2)
    {
        TVec2<T> i;
        T a1 = l1.second.y - l1.first.y;
        T b1 = l1.first.x - l1.second.x;
        T c1 = a1 * l1.first.x + b1 * l1.first.y;
        T a2 = l2.second.y - l2.first.y;
        T b2 = l2.first.x - l2.second.x;
        T c2 = a2 * l2.first.x + b2 * l2.first.y;
        T det = a1 * b2 - a2 * b1;
        if (!Eq(det, 0)) // lines are not parallel
        {
            i.setX((b2 * c1 - b1 * c2) / det);
            i.setY((a1 * c2 - a2 * c1) / det);
        }
        return i;
    }

    struct SubA
    {
    private:
        struct Node
        {
            //顶点索引
            size_t index = NumLimits<size_t>::Max;
            //顶点位置
            TVec2<T> point = TVec2<T>::Zero();
            //指向的前一个顶点
            Node* prev = nullptr;
            //指向的后一个顶点
            Node* next = nullptr;
        };
        using Nodes = std::vector<Node>;
    private:
        //顶点的索引列表
        std::vector<size_t> _indices;
        //父多边形,主要用于查询顶点坐标
        const TPolygon2<T>& _parent;
    public:
        /**
         * @brief 构造
        */
        SubA(const TPolygon2<T>& parent, std::vector<size_t>&& indices)
            : _parent(parent)
            , _indices(std::forward<std::vector<size_t> >(indices))
        {
        }
    public:
        /**
         * @brief 通过父多边形生成
        */
        static SubA FromParent(const TPolygon2<T>& parent)
        {
            std::vector<size_t> ids;
            ids.reserve(parent.size());
            for (size_t i = 0; i < parent.size(); ++i)
            {
                ids.push_back(i);
            }
            return SubA(parent, std::move(ids));
        }
    public:
        /**
         * @brief 耳切
         * @param ret 切分的对角线
        */
        void earcut(Diagonals& ret) const
        {
            Nodes nodes(_indices.size());
            for (size_t i = 0; i < nodes.size(); ++i)
            {
                Node& node = nodes[i];
                node.index = _indices[i];
                node.point = _parent[node.index];
                size_t iPrev = i == 0 ? nodes.size() - 1 : i - 1;
                size_t iNext = i == nodes.size() - 1 ? 0 : i + 1;
                node.prev = &nodes[iPrev];
                node.next = &nodes[iNext];
            }
            Node* ear = &nodes[0];
            Node* prev = ear->prev;
            Node* next = ear->next;

            while (prev != next)
            {
                if (isEar(ear))
                {
                    ret.push_back({ prev->index, next->index });

                    next->prev = prev;
                    prev->next = next;

                    // 跳过下一个顶点可以减少生成连带的三角形
                    ear = next->next;

                    prev = ear->prev;
                    next = ear->next;

                    // 这里表明只剩下最后一个三角形了,不会再产生对角线了
                    if (next->next == prev)
                        break;

                }
                else
                {
                    ear = next;

                    prev = ear->prev;
                    next = ear->next;
                }
            }
        }
        /**
         * @brief 耳切
         * @param ret 切分的所有三角形的索引，每三个索引构成一个三角形
        */
        void earcut(std::vector<size_t>& ret) const
        {
            Nodes nodes(_indices.size());
            for (size_t i = 0; i < nodes.size(); ++i)
            {
                Node& node = nodes[i];
                node.index = _indices[i];
                node.point = _parent[node.index];
                size_t iPrev = i == 0 ? nodes.size() - 1 : i - 1;
                size_t iNext = i == nodes.size() - 1 ? 0 : i + 1;
                node.prev = &nodes[iPrev];
                node.next = &nodes[iNext];
            }

            Node* ear = &nodes[0];
            Node* prev = ear->prev;
            Node* next = ear->next;

            while (prev != next)
            {
                if (isEar(ear))
                {
                    ret.emplace_back(prev->index);
                    ret.emplace_back(ear->index);
                    ret.emplace_back(next->index);

                    next->prev = prev;
                    prev->next = next;

                    // 跳过下一个顶点可以减少生成连带的三角形
                    ear = next->next;

                    prev = ear->prev;
                    next = ear->next;
                }
                else
                {
                    ear = next;

                    prev = ear->prev;
                    next = ear->next;
                }
            }
        }
        /**
         * @brief 最优凸分解算法
        */
        Diagonals decomp()
        {
            size_t sz = this->size();
            if (sz < 3)
            {
                assert(false);
                return {};
            }
            //三角形肯定为凸,不做后续计算
            if (sz == 3)
            {
                return {};
            }

            Diagonals min, tmp1, tmp2;
            size_t nDiags = NumLimits<size_t>::Max;

            for (size_t i = 0; i < sz; ++i)
            {
                if (!isReflex(i))
                    continue;

                for (size_t j = 0; j < sz; ++j)
                {
                    if (!canSee(i, j))
                        continue;
                    auto tIds0 = this->subIndices(i, j);
                    SubA sub0(_parent, std::move(tIds0));
                    tmp1 = sub0.decomp();

                    auto tIds1 = this->subIndices(j, i);
                    SubA sub1(_parent, std::move(tIds1));
                    tmp2 = sub1.decomp();

                    tmp1.insert(tmp1.end(), tmp2.begin(), tmp2.end());
                    if (tmp1.size() < nDiags)
                    {
                        min = tmp1;
                        nDiags = tmp1.size();
                        min.push_back(Diagonal(indexAt(i), indexAt(j)));
                    }
                }
            }
            return std::move(min);
        }
    private:
        /**
         * @brief 重新建立顶点的连接关系
        */
        Node* relinkNodes(const Nodes& nodes) const
        {
            for (size_t i = 0; i < nodes.size(); ++i)
            {
                Node& node = nodes[i];
                size_t iPrev = i == 0 ? nodes.size() - 1 : i - 1;
                size_t iNext = i == nodes.size() - 1 ? 0 : i + 1;
                node.prev = &nodes[iPrev];
                node.next = &nodes[iNext];
            }
        }
        /**
         * @brief 判断一个顶点是否是耳
        */
        bool isEar(const Node* ear) const
        {
            const Node* a = ear->prev;
            const Node* b = ear;
            const Node* c = ear->next;

            // 耳点不能是凹点
            if (TTriangle2<T>::Right(a->point, b->point, c->point))
                return false;

            // 耳构成的三角形内不会出现其他顶点
            for (const Node* p = ear->next->next; p != ear->prev; p = p->next)
            {
                // p 是否在 abc构成的三角形内
                bool bInTri = TTriangle2<T>::Contains(p->point, a->point, b->point, c->point);
                if (bInTri)
                    return false;
            }
            return true;
        }
        /**
         * @brief 子多边形顶点个数
        */
        inline size_t size() const
        {
            return _indices.size();
        }
        /**
         * @brief 对应索引下标的顶点坐标值
         * @param index 索引列表的索引
        */
        inline const TVec2<T>& at(const size_t& index) const
        {
            const size_t i = _indices[index];
            return _parent[i];
        }
        /**
         * @brief 根据索引列表的索引获取顶点索引
        */
        inline size_t indexAt(const size_t& index) const
        {
            return _indices[index];
        }
        /**
         * @brief 根据索引列表的索引获取前一个顶点的索引
        */
        inline size_t prevIndex(const size_t& index) const
        {
            const size_t sz = this->size();
            return (index == 0) ? (sz - 1) : (index - 1);
        }
        /**
         * @brief 根据索引列表的索引获取后一个顶点的索引
        */
        inline size_t nextIndex(const size_t& index) const
        {
            const size_t sz = this->size();
            return (index == sz - 1) ? 0 : index + 1;
        }
        /**
         * @brief 根据索引列表的索引判断某个顶点是否凹顶点
        */
        inline bool isReflex(const size_t& i) const
        {
            const size_t prev = prevIndex(i);
            const size_t next = nextIndex(i);
            return TPolygon2<T>::IsReflex(at(prev), at(i), at(next));
        }
        /**
         * @brief 顶点a是否可以看到顶点b
         *  1.顶点a,b构成的对角线肯定在多边形内
         *  2.顶点a,b构成的对角线不会与其他边相交
        */
        bool canSee(const size_t& a, const size_t& b)
        {
            const TVec2<T>& ptA = at(a);
            const TVec2<T>& ptB = at(b);
            const size_t aPrev = prevIndex(a);
            const size_t aNext = nextIndex(a);
            const TVec2<T>& ptA0 = at(aPrev);
            const TVec2<T>& ptA1 = at(aNext);
            //
            bool bLOn0 = TTriangle2<T>::LeftOn(ptA1, ptA, ptB);
            bool bROn0 = TTriangle2<T>::RightOn(ptA0, ptA, ptB);
            if (bLOn0 && bROn0)
                return false;

            const T dist = TVec2<T>::DistanceSq(ptA, ptB);
            for (size_t i = 0; i < this->size(); ++i) //遍历多边形的每条边
            {
                const size_t iNext = nextIndex(i);
                if (iNext == a || i == a) //忽略顶点的临边
                    continue;
                const TVec2<T>& ptI = at(i);
                const TVec2<T>& ptI1 = at(iNext);

                //计算三角形有符号面积
                const T area1 = TTriangle2<T>::Area(ptA, ptB, ptI1);
                const T area2 = TTriangle2<T>::Area(ptA, ptB, ptI);
                //近似等于0,说明: ptA, ptB, ptI1 共线
                if (Eq(area1, T(0)))
                {
                    //如果该边线端点到视点的距离小于 dist, 说明视线被遮挡
                    const T dist1 = TVec2<T>::DistanceSq(ptA, ptI1);
                    if (dist1 < dist)
                        return false;
                }
                //近似等于0,说明: ptA, ptB, ptI 共线
                if (Eq(area2, T(0)))
                {
                    //如果该边线端点到视点的距离小于 dist, 说明视线被遮挡
                    const T dist1 = TVec2<T>::DistanceSq(ptA, ptI);
                    if (dist1 < dist)
                        return false;
                }
                if ((area1 > T(0) && area2 < T(0))) // 说明线段两个端点分布在ptA,ptB所构成直线的两侧,并且在ptA到ptB的视线方向上
                {
                    //对角线与某条边的交点
                    const TVec2<T> p = LineInt(TLine(ptA, ptB), TLine(ptI, ptI1));
                    const T tDist = TVec2<T>::DistanceSq(ptA, p);
                    if (tDist < dist) //如果这条边遮挡了顶点a到b的视线
                    {
                        return false;
                    }
                }
            }

            return true;
        }
        /**
         * @brief 根据当前多边形 以及索引列表索引起止 获取子多边形索引列表
        */
        std::vector<size_t> subIndices(const size_t& from, const size_t& to)
        {
            std::vector<size_t> rs;
            if (from < to)
            {
                rs.insert(rs.begin(), _indices.begin() + from, _indices.begin() + to + 1);
            }
            else
            {
                rs.insert(rs.begin(), _indices.begin() + from, _indices.end());
                rs.insert(rs.end(), _indices.begin(), _indices.begin() + to + 1);
            }
            return rs;
        }
    private:
        //两个值是否近似相等
        static inline bool Eq(const T& a, const T& b)
        {
            return Abs(a - b) <= NumLimits<T>::Epsilon;
        }
        //两条直线交点
        using TLine = std::pair<TVec2<T>, TVec2<T> >;
        static TVec2<T> LineInt(const TLine& l1, const TLine& l2)
        {
            TVec2<T> i;
            T a1 = l1.second.y - l1.first.y;
            T b1 = l1.first.x - l1.second.x;
            T c1 = a1 * l1.first.x + b1 * l1.first.y;
            T a2 = l2.second.y - l2.first.y;
            T b2 = l2.first.x - l2.second.x;
            T c2 = a2 * l2.first.x + b2 * l2.first.y;
            T det = a1 * b2 - a2 * b1;
            if (!Eq(det, 0)) // lines are not parallel
            {
                i.setX((b2 * c1 - b1 * c2) / det);
                i.setY((a1 * c2 - a2 * c1) / det);
            }
            return i;
        }
    };

    struct SubB
    {
    private:
        // 顶点
        struct Node
        {
        public:
            //顶点与另一个顶点的对角线
            struct VDiagonal
            {
                //对角线所指的下一个顶点
                Node* pNext;
                //该对角线是否是边线
                bool isSideLine;
                //被使用标志
                bool bUsed;
                //被删除标志
                bool bDeleted;
                VDiagonal(Node* pNext = nullptr, bool isSideLine = false)
                {
                    this->pNext = pNext;
                    this->isSideLine = isSideLine;
                    this->bUsed = false;
                    this->bDeleted = false;
                }
            };
            using VDiagonals = std::list<VDiagonal>;
        public:
            //顶点索引
            size_t index = NumLimits<size_t>::Max;
            //顶点值
            TVec2<T> point = TVec2<T>::Zero();
            //顶点的对角线列表
            VDiagonals diagonals;
        };
        using Nodes = std::vector<Node>;
        // 顶点列表
        Nodes _vs;
    public:
        SubB(const TPolygon2<T>& polygon)
        {
            // 初始化顶点数组
            _vs.reserve(polygon.size());
            for (size_t i = 0; i < polygon.size(); ++i)
            {
                _vs.push_back(Node());
                //设置顶点索引
                _vs.back().index = i;
                //设置顶点位置
                _vs.back().point = polygon[i];
            }
        }
    public:
        /**
         * @brief 合并多余的对角线
        */
        void incorporate(const Diagonals& diagonals, Diagonals& outDiagonals)
        {
            // 相邻顶点的边线
            for (Node& v : _vs)
            {
                //连接到前一个顶点的边线(对角线)
                size_t prevI = (v.index == 0) ? _vs.size() - 1 : v.index - 1;
                v.diagonals.push_back(typename Node::VDiagonal(&_vs[prevI], true));
                //连接到下一个顶点的边线(对角线)
                size_t nextI = (v.index == _vs.size() - 1) ? 0 : v.index + 1;
                v.diagonals.push_back(typename Node::VDiagonal(&_vs[nextI], true));
            }
            // 非相邻顶点的对角线
            for (const auto& dia : diagonals)
            {
                this->insertDiagonal(dia.first, dia.second);
            }
            outDiagonals.reserve(diagonals.size());
            // 开始根据对角线递归分解
            for (Node& v : _vs)
            {
                // 剔除冗余的对角线
                for (auto& dia : v.diagonals)
                {
                    // 边线肯定不会被删除
                    if (dia.isSideLine)
                        continue;
                    // 这里表示对角线已被校验
                    if (dia.bUsed)
                        continue;
                    // 这里表示对角线已被删除
                    if (dia.bDeleted)
                        continue;
                    auto* pTV = dia.pNext;
                    auto* pTD = otherHalfDiagonal(v, dia);
                    assert(pTD != nullptr);
                    // 分别标记两条对角线已被校验
                    dia.bUsed = true;
                    pTD->bUsed = true;
                    // 查找该顶点对角线的左右对角线
                    if (!isLeftRightReflex(v, dia) && !isLeftRightReflex(*pTV, *pTD))
                    {
                        // 这条对角线可以被删除,标记为已删除
                        dia.bDeleted = true;
                        pTD->bDeleted = true;
                    }
                    else
                    {
                        // 不能被删除，作为结果返回
                        outDiagonals.push_back({ v.index, dia.pNext->index });
                    }
                }
            }
        }
        /**
         * @brief 根据对角线分解多边形
         * @return 输出分解的子多边形索引列表
        */
        void decomp(const Diagonals& diagonals, SubPolygons& ret)
        {
            // 相邻顶点的边线
            for (Node& v : _vs)
            {
                //连接到下一个顶点的边线(对角线)
                size_t nextI = (v.index == _vs.size() - 1) ? 0 : v.index + 1;
                v.diagonals.push_back(typename Node::VDiagonal(&_vs[nextI], true));
            }
            // 非相邻顶点的对角线
            for (const auto& dia : diagonals)
            {
                this->insertDiagonal(dia.first, dia.second);
            }
            // 开始根据对角线递归分解
            for (Node& v : _vs)
            {
                Node* pFirst = &v;
                //从一个顶点和其对应的对角线出发，寻找一个凸多边形
                for (auto& dia : v.diagonals)
                {
                    //只处理未被使用的对角线
                    if (dia.bUsed)
                        continue;
                    //拿到下一个顶点
                    Node* pNext = dia.pNext;
                    //存储子多边形的顶点
                    SubPolygon tRet;
                    tRet.reserve(_vs.size());
                    // 加入当前点
                    tRet.push_back(v.index);
                    // 查找后续点
                    bool bSub = subPolygons(pFirst, pFirst, pNext, tRet);
                    if (bSub)
                    {
                        assert(tRet.size() >= 2);
                        ret.push_back(std::move(tRet));
                        // 标记边线已被使用
                        dia.bUsed = true;
                    }
                }
            }
        }
    private:
        // 插入对角线
        inline void insertDiagonal(const size_t& a, const size_t& b)
        {
            Node& v0 = _vs[a];
            Node& v1 = _vs[b];
            v0.diagonals.push_back(typename Node::VDiagonal(&v1, false));
            v1.diagonals.push_back(typename Node::VDiagonal(&v0, false));
        }
        // 指定一个顶点和一个对角线(半边),查找对角线所指的顶点的指向当前顶点的对角线
        typename Node::VDiagonal* otherHalfDiagonal(const Node& v, const typename Node::VDiagonal& diagonal)
        {
            for (auto& tDia : diagonal.pNext->diagonals)
            {
                if (tDia.pNext == &v)
                {
                    return &tDia;
                }
            }
            return nullptr;
        }
        // 顶点的一条对角线左侧和右侧且与该对角线夹角最小的两条对角线之间的夹角是否大于180度
        bool isLeftRightReflex(const Node& v, const typename Node::VDiagonal& diagonal)
        {
            // 分别存储左侧和右侧最小夹角
            std::pair<T, T> minAngles = std::make_pair(NumLimits<T>::Max, NumLimits<T>::Max);

            const TVec2<T>& vPos = v.point;
            const TVec2<T>& dPos = diagonal.pNext->point;
            TVec2<T> diaVec = dPos - vPos;
            //先查找这条对角线右侧和左侧且与该对角线夹角最小的两条对角线
            for (auto& tDia : v.diagonals)
            {
                // 这里的表示对角线已被删除
                if (tDia.bDeleted)
                    continue;
                if (&tDia == &diagonal)
                    continue;
                const TVec2<T>& tPos = tDia.pNext->point;
                TVec2<T>        tDiaVec = tPos - vPos;
                T angle = TVec2<T>::Angle(diaVec, tDiaVec);
                bool bLeftOn = TTriangle2<T>::LeftOn(vPos, dPos, tPos);
                if (bLeftOn)
                {
                    if (angle < minAngles.first)
                    {
                        minAngles.first = angle;
                    }
                }
                else
                {
                    if (angle < minAngles.second)
                    {
                        minAngles.second = angle;
                    }
                }
            }
            return minAngles.first + minAngles.second > T(180);
        }
        //递归分解
        bool subPolygons(const Node* pFirst, Node* pPrev, Node* pCurr, SubPolygon& ret)
        {
            const TVec2<T>& prevPt = pPrev->point;
            const TVec2<T>& currPt = pCurr->point;

            T minAngle = NumLimits<T>::Max;
            struct Node::VDiagonal* pRDia = nullptr;
            // 查找合法的对角线
            for (auto& dia : pCurr->diagonals)
            {
                //对角线已被使用，跳过
                if (dia.bUsed)
                    continue;

                // 非法路径(又转回来了 eg: V1和V4之间有对角线,这种情况是出现了这样的路径: V1->V4->V1)
                if (dia.pNext == pPrev)
                {
                    assert(!dia.isSideLine); //边线不可能出现这种情况
                    continue;
                }

                const TVec2<T>& nextPt = dia.pNext->point;

                // 校验 前一个点，当前点和下一个点是否构成凸点
                bool bLeftOn = TTriangle2<T>::LeftOn(prevPt, currPt, nextPt);
                if (!bLeftOn)
                    continue;

                // 计算夹角
                const T angle = TVec2<T>::Angle(nextPt - currPt, prevPt - currPt);
                // !注意: 这里夹角不能为0, 如果为0，表示前一个点，当前点，后一个点共线
                //   这里直接跳过这种情况的下一个顶点, 否则会进入死循环
                if (angle <= NumLimits<T>::Epsilon)
                    continue;

                // 如果当前夹角小于之前的夹角, 表明是最小拐角, 则加入
                if (angle < minAngle)
                {
                    minAngle = angle;
                    pRDia = &dia;
                }
            }

            //没有合法的对角线
            if (pRDia == nullptr)
                return false;

            // 一个子多边形查找完成
            if (pRDia->pNext == pFirst)
            {
                //查找完毕,加入当前点
                ret.push_back(pCurr->index);
                //标记对角线已被使用
                pRDia->bUsed = true;

                return true;
            }

            // 加入当前点
            ret.push_back(pCurr->index);
            // 递归，继续从下一个点开始找后续的点
            bool bRet = subPolygons(pFirst, pCurr, pRDia->pNext, ret);
            if (bRet)
            {
                //标记边线为已使用
                pRDia->bUsed = true;
            }
            return bRet;
        }
    };
};



template <class T>
using TPolygon2Vector = std::vector<TPolygon2<T> >;

template <class T, size_t Size>
using TPolygon2Array = std::array<TPolygon2<T>, Size>;



WD_NAMESPACE_END