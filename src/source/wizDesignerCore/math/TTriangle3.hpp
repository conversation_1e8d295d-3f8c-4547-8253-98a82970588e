#pragma once

#include "TVec3.hpp"
#include "TMat4.hpp"

WD_NAMESPACE_BEGIN

/**
 * @brief 3D三角形
 * @tparam T 数值类型
*/
template <typename T>
class TTriangle3
{
    static_assert(std::is_arithmetic_v<T>, "模板参数类型必须是算数类型!");
public:
    using ValueType     = T;
    using value_type    = ValueType;
    using SizeType      = size_t;
    using size_type     = SizeType;
public:
    /**
    * @brief 数据个数
    */
    static constexpr size_t Size = 3;
public:
    // 顶点 a
    TVec3<T> a;
    // 顶点 b
    TVec3<T> b;
    // 顶点 c
    TVec3<T> c;
public:
    /**
     * @brief 构造
    */
    inline TTriangle3()
        : a(TVec3<T>::Zero())
        , b(TVec3<T>::Zero())
        , c(TVec3<T>::Zero())
    {
    }
    /**
     * @brief 构造
    */
    inline TTriangle3(const TTriangle3<T>& right)
        : a(right.a)
        , b(right.b)
        , c(right.c)
    {
    }
    /**
     * @brief 构造
    */
    inline TTriangle3(const TVec3<T>& a, const TVec3<T>& b, const TVec3<T>& c)
        : a(a)
        , b(b)
        , c(c)
    {
    }
    /**
     * @brief 构造
    */
    inline TTriangle3(T ax, T ay, T az, T bx, T by, T bz, T cx, T cy, T cz)
        : a(ax, ay, az)
        , b(bx, by, bz)
        , c(cx, cy, cz)
    {
    }
    /**
    * @brief 指定点列表和索引列表构造
    * @param points 顶点列表首地址
    * @param indices 索引列表首地址
    */
    template<class IndexType>
    inline TTriangle3(const TVec3<T>* points, IndexType* indices)
        : a(points[indices[0]])
        , b(points[indices[1]])
        , c(points[indices[2]])
    {
    }
    /**
    * @brief 指定点列表和索引构造
    * @param points 顶点列表首地址，需保证包含的点个数 >= 3,当个数>3时，取最前的三个值
    */
    template<class IndexType>
    inline TTriangle3(const TVec3<T>* points, IndexType i0, IndexType i1, IndexType i2)
        : a(points[i0])
        , b(points[i1])
        , c(points[i2])
    {
    }
    /**
     * @brief 构造
    */
    template<class U>
    inline explicit TTriangle3(const TTriangle3<U>& right)
    {
        this->a = TVec3<T>(right.a);
        this->b = TVec3<T>(right.b);
        this->c = TVec3<T>(right.c);
    }
public:
    /**
    * @brief 三角形是否有效
    */
    inline bool isVaild() const
    {
        return TTriangle3<T>::IsTriangle(this->a, this->b, this->c);
    }
    /**
    * @brief 获取面积
    */
    inline T area() const
    {
        return TTriangle3<T>::Area(this->a, this->b, this->c);
    }
    /**
    * @brief 获取法线
    */
    inline TVec3<T> normal()  const
    {
        return TTriangle3<T>::Normal(this->a, this->b, this->c);
    }
    /**
    * @brief 获取三角形的中心坐标(center coordinate)
    */
    inline TVec3<T> center() const
    {
        return TTriangle3<T>::Center(this->a, this->b, this->c);
    }
    /**
    * @brief 获取三角形重心坐标(barycentric coordinate)
    */
    inline TVec3<T> baryCoord(const TVec3<T>& point) const
    {
        return TTriangle3<T>::BaryCoord(point, this->a, this->b, this->c);
    }
    /**
    * @brief 获取三角形的内心(Inner coordinate)，内切圆圆心
    */
    inline TVec3<T> innerCoord() const
    {
        return TTriangle3<T>::InnerCoord(this->a, this->b, this->c);
    }
    /**
    * @brief 获取三角形的外心 (outer coordinate),外接圆圆心
    */
    inline TVec3<T> outerCoord() const
    {
        return TTriangle3<T>::OuterCoord(this->a, this->b, this->c);
    }
    /**
    * @brief 点是否在三角形内
    */
    inline bool contains(const TVec3<T>& point) const
    {
        return TTriangle3<T>::Contains(point, this->a, this->b, this->c);
    }
    /**
    * @brief 计算三角形上最靠近给定的point的点
    */
    inline TVec3<T> closestPointToPoint(const TVec3<T>& point) const
    {
        return TTriangle3<T>::ClosestPointToPoint(point, this->a, this->b, this->c);
    }
    /**
    * @brief 使用偏移量变换当前三角形
    */
    inline TTriangle3<T>& translate(const TVec3<T>& offset)
    {
        this->a += offset;
        this->b += offset;
        this->c += offset;
        return *this;
    }
    /**
    * @brief 使用矩阵变换当前三角形
    */
    inline TTriangle3<T>& transform(const TMat4<T>& matrix)
    {
        this->a = matrix * this->a;
        this->b = matrix * this->b;
        this->c = matrix * this->c;
        return *this;
    }
public:
    /**
     * @brief 下标访问
    */
    inline TVec3<T>& at(size_t index)
    {
        TVec3<T>* p = (TVec3<T>*)(&this->a);
        return *(p + index);
    }
    /**
     * @brief 下标访问
    */
    inline const TVec3<T>& at(size_t index) const
    {
        const TVec3<T>* p = (const TVec3<T>*)(&this->a);
        return *(p + index);
    }
    /**
    * @brief 下标访问, 依次为[pointA, pointB, pointC]
    */
    inline TVec3<T>& operator[](size_t i)
    {
        return this->at(i);
    }
    /**
    * @brief 下标访问, 依次为[pointA, pointB, pointC]
    */
    inline const TVec3<T>& operator[](size_t i) const
    {
        return this->at(i);
    }
    /**
    * @brief 赋值运算
    */
    inline TTriangle3<T>& operator=(const TTriangle3<T>& right)
    {
        this->a = right.a;
        this->b = right.b;
        this->c = right.c;

        return *this;
    }
    /**
     * @brief ==
    */
    friend inline bool operator==(const TTriangle3<T>& left, const TTriangle3<T>& right)
    {
        return (left.a == right.a)
            && (left.b == right.b)
            && (left.c == right.c);
    }
    /**
     * @brief !=
    */
    friend inline bool operator!=(const TTriangle3<T>& left, const TTriangle3<T>& right)
    {
        return (left.a != right.a)
            || (left.b != right.b)
            || (left.c != right.c);
    }
public:
    /**
    * @brief 判断三点是否能构成有效三角形
    */
    static inline bool IsTriangle(const TVec3<T>& a, const TVec3<T>& b, const TVec3<T>& c, const T& e = NumLimits<T>::Epsilon)
    {
        const TVec3<T> v = TTriangle3<T>::NormalFast(a, b, c);
        const T d = v.x + v.y + v.z;
        return d < -e || d > e;
    }
    /**
    * @brief 获取三点构成平面的法线
    */
    static TVec3<T> Normal(const TVec3<T>& a, const TVec3<T>& b, const TVec3<T>& c)
    {
        const TVec3<T> v = TTriangle3<T>::NormalFast(a, b, c);

        T vLengthSq = v.lengthSq();
        if (vLengthSq > 0)
        {
            return v * (T(1) / Sqrt(vLengthSq));
        }
        return TVec3<T>::Zero();
    }
    /**
    * @brief 获取三点构成三角形的面积
    */
    static inline T Area(const TVec3<T>& a, const TVec3<T>& b, const TVec3<T>& c)
    {
        const TVec3<T> v = TTriangle3<T>::NormalFast(a, b, c);
        return v.length() * T(0.5);
    }
    /**
    * @brief 获取三点构成三角形的重心坐标(barycentric coordinate)
    */
    static TVec3<T> BaryCoord(const TVec3<T>& point, const TVec3<T>& a, const TVec3<T>& b, const TVec3<T>& c)
    {
        const TVec3<T> v0 = c - a;
        const TVec3<T> v1 = b - a;
        const TVec3<T> v2 = point - a;
        
        const T dot00 = TVec3<T>::Dot(v0, v0);
        const T dot01 = TVec3<T>::Dot(v0, v1);
        const T dot02 = TVec3<T>::Dot(v0, v2);
        const T dot11 = TVec3<T>::Dot(v1, v1);
        const T dot12 = TVec3<T>::Dot(v1, v2);

        const T denom = (dot00 * dot11 - dot01 * dot01);

        // collinear or singular triangle
        if (denom == 0)
        {
            // arbitrary location outside of triangle?
            // not sure if this is the best idea, maybe should be returning undefined
            return TVec3<T>(T(-2), T(-1), T(-1));
        }

        const T invDenom = T(1) / denom;
        const T u = (dot11 * dot02 - dot01 * dot12) * invDenom;
        const T v = (dot00 * dot12 - dot01 * dot02) * invDenom;

        // barycentric coordinates must always sum to 1
        return TVec3<T>(T(1) - u - v, v, u);
    }
    /**
    * @brief 获取三点构成三角形的中心坐标(center coordinate)
    */
    static TVec3<T> Center(const TVec3<T>& a, const TVec3<T>& b, const TVec3<T>& c)
    {
        return (a + b + c) / T(3);
    }
    /**
    * @brief 获取三点构成三角形的内心坐标(Inner coordinate)，内切圆圆心
    */
    static TVec3<T> InnerCoord(const TVec3<T>& a, const TVec3<T>& b, const TVec3<T>& c)
    {
        const T da = TVec3<T>::Distance(b, c);
        const T db = TVec3<T>::Distance(c, a);
        const T dc = TVec3<T>::Distance(a, b);

        constexpr T e = NumLimits<T>::Epsilon;

        if (da <= e || db <= e || dc <= e)
        {///至少有两点重合，无法构成三角形
            return a;
        }
        TVec3<T> v0 = TVec3<T>::Normalize(a - b);
        TVec3<T> v1 = TVec3<T>::Normalize(c - b);
        T d = TVec3<T>::Dot(v0, v1);
        if (d + e >= T(1))
        {///三点共线,无法构成三角形
            return a;
        }
        T abc = da + db + dc;
        T _abc = T(1) / abc;
        T x = (da * a.x + db * b.x + dc * c.x) * _abc;
        T y = (da * a.y + db * b.y + dc * c.y) * _abc;
        T z = (da * a.z + db * b.z + dc * c.z) * _abc;
        return TVec3<T>(x, y, z);
    }
    /**
    * @brief 获取三点构成三角形的外心 (outer coordinate),外接圆圆心
    */
    static TVec3<T> OuterCoord(const TVec3<T>& a, const TVec3<T>& b, const TVec3<T>& c)
    {
        const T m_X1 = a.x;
        const T m_Y1 = a.y;
        const T m_Z1 = a.z;
        const T m_X2 = b.x;
        const T m_Y2 = b.y;
        const T m_Z2 = b.z;
        const T m_X3 = c.x;
        const T m_Y3 = c.y;
        const T m_Z3 = c.z;

        const T A = T(0.5) * (m_X1 * m_X1 - m_X2 * m_X2 + m_Y1 * m_Y1 - m_Y2 * m_Y2 + m_Z1 * m_Z1 - m_Z2 * m_Z2);
        const T B = T(0.5) * (m_X1 * m_X1 - m_X3 * m_X3 + m_Y1 * m_Y1 - m_Y3 * m_Y3 + m_Z1 * m_Z1 - m_Z3 * m_Z3);
        const T a1 = m_X1 - m_X2;
        const T b1 = m_Y1 - m_Y2;
        const T c1 = m_Z1 - m_Z2;

        const T a2 = m_X1 - m_X3;
        const T b2 = m_Y1 - m_Y3;
        const T c2 = m_Z1 - m_Z3;

        const T E = b2 * a1 - b1 * a2;
        const T F = a2 * b1 - a1 * b2;
        const T G = b2 * A - b1 * B;
        const T H = b1 * c2 - b2 * c1;
        const T J = a2 * A - a1 * B;
        const T K = a1 * c2 - a2 * c1;


        const T E1 = a1 * c2 - a2 * c1;
        const T F1 = a2 * c1 - a1 * c2;
        const T G1 = c2 * A - c1 * B;
        const T H1 = b2 * c1 - b1 * c2;
        const T J1 = a2 * A - a1 * B;
        const T K1 = a1 * b2 - a2 * b1;

        const T E2 = b1 * c2 - b2 * c1;
        const T F2 = b2 * c1 - b1 * c2;
        const T G2 = c2 * A - c1 * B;
        const T H2 = a2 * c1 - a1 * c2;
        const T J2 = b2 * A - b1 * B;
        const T K2 = a2 * b1 - a1 * b2;
        T z, y, x;

        constexpr T e   = NumLimits<T>::Epsilon;
        constexpr T one = T(1.0);
        constexpr T two = T(2.0);
        if (Abs(E) > e && Abs(F) > e)
        {
            z = (two * m_X1*H / E + two * m_Y1*K / F + two * m_Z1 - two * G*H / (E*E) - two * J*K / (F*F)) / (two * (H*H / (E*E) + K * K / (F*F) + one));
            x = (G + H * z) / E;
            y = (J + K * z) / F;
        }
        else
        {
            if (Abs(E1) > e && Abs(F1) > e)
            {
                y = (two * m_X1*H1 / E1 + two * m_Z1*K1 / F1 + two * m_Y1 - two * G1*H1 / (E1*E1) - two * J1*K1 / (F1*F1)) / (two * (H1*H1 / (E1*E1) + K1 * K1 / (F1*F1) + one));
                x = (G1 + H1 * y) / E1;
                z = (J1 + K1 * y) / F1;
            }
            else
            {
                if (Abs(E2) > e && Abs(F2) > e)
                {
                    x = (two * m_Y1*H2 / E2 + two * m_Z1*K2 / F2 + two * m_X1 - two * G2*H2 / (E2*E2) - two * J2*K2 / (F2*F2)) / (two * (H2*H2 / (E2*E2) + K2 * K2 / (F2*F2) + one));
                    y = (G2 + H2 * x) / E2;
                    z = (J2 + K2 * x) / F2;
                }
                else
                {
                    //"三点共线");
                    return a;
                }
            }
        }
        return TVec3<T>(x, y, z);
    }
    /**
    * @brief 判断点是否在三角形内
    */
    static inline bool Contains(const TVec3<T>& point, const TVec3<T>& a, const TVec3<T>& b, const TVec3<T>& c)
    {
        const TVec3<T> v = TTriangle3<T>::BaryCoord(point, a, b, c);
        return (v.x >= 0) && (v.y >= 0) && ((v.x + v.y) <= 1);
    }
    /**
    * @brief 判断三点构成的三角形是否为正面(与指定的方向做判断)
    * @param direction,视线方向,如果三角形法线与视线方向反向，则为正面
    */
    static inline bool IsFrontFacing(const TVec3<T>& a, const TVec3<T>& b, const TVec3<T>& c, const TVec3<T>& direction)
    {
        const TVec3<T> v = TTriangle3<T>::NormalFast(a, b, c);
        // strictly front facing
        return (TVec3<T>::Dot(v, direction) < T(0)) ? true : false;
    }
    /**
    * @brief 判断三角形是否为顺时针(右手坐标系三角形顺时针时为反面)
    */
    static inline bool IsClockWise(const TVec3<T>& a, const TVec3<T>& b, const TVec3<T>& c)
    {
        const TVec3<T> v = TTriangle3<T>::NormalFast(a, b, c);
        T d = v.x + v.y + v.z;
        return d < T(0);
    }
    /**
    * @brief 判断三角形是否为正面(基于右手系)
    */
    static inline bool IsFrontFacing(const TVec3<T>& a, const TVec3<T>& b, const TVec3<T>& c)
    {
        return (!IsClockWise(a, b, c));
    }
    /**
    * @brief 计算三角形上最靠近给定的point的点
    */
    static TVec3<T> ClosestPointToPoint(const TVec3<T>& point, const TVec3<T>& a, const TVec3<T>& b, const TVec3<T>& c)
    {
        T v, w;

        const TVec3<T>& p = point;
        // algorithm thanks to Real-Time Collision Detection by Christer Ericson,
        // published by Morgan Kaufmann Publishers, (c) 2005 Elsevier Inc.,
        // under the accompanying license; see chapter 5.1.5 for detailed explanation.
        // basically, we're distinguishing which of the voronoi regions of the triangle
        // the point lies in with the minimum amount of redundant computation.

        const TVec3<T> vab = b - a;
        const TVec3<T> vac = c - a;
        const TVec3<T> vap = p - a;
        const T d1 = TVec3<T>::Dot(vab, vap);
        const T d2 = TVec3<T>::Dot(vac, vap);
        if (d1 <= 0 && d2 <= 0)
        {
            // vertex region of A; barycentric coords (1, 0, 0)
            return a;
        }

        const TVec3<T> vbp = p - b;
        const T d3 = TVec3<T>::Dot(vab, vbp);
        const T d4 = TVec3<T>::Dot(vac, vbp);
        if (d3 >= 0 && d4 <= d3)
        {
            // vertex region of B; barycentric coords (0, 1, 0)
            return b;
        }

        const T vc = d1 * d4 - d3 * d2;
        if (vc <= 0 && d1 >= 0 && d3 <= 0)
        {
            v = d1 / (d1 - d3);
            // edge region of AB; barycentric coords (1-v, v, 0)
            return a + vab * v;
        }

        const TVec3<T> vcp = p - c;
        const T d5 = TVec3<T>::Dot(vab, vcp);
        const T d6 = TVec3<T>::Dot(vac, vcp);
        if (d6 >= 0 && d5 <= d6)
        {
            // vertex region of C; barycentric coords (0, 0, 1)
            return c;
        }

        const T vb = d5 * d2 - d1 * d6;
        if (vb <= 0 && d2 >= 0 && d6 <= 0)
        {
            w = d2 / (d2 - d6);
            // edge region of AC; barycentric coords (1-w, 0, w)
            return a + vac * w;
        }

        const T va = d3 * d6 - d5 * d4;
        if (va <= 0 && (d4 - d3) >= 0 && (d5 - d6) >= 0)
        {
            TVec3<T> vbc = c - b;
            w = (d4 - d3) / ((d4 - d3) + (d5 - d6));
            // edge region of BC; barycentric coords (0, 1-w, w)
            return b + vbc * w;
        }

        // face region
        const T denom = T(1) / (va + vb + vc);
        // u = va * denom
        v = vb * denom;
        w = vc * denom;

        return a + (vab * v) + (vac * w);
    }
public:
    /**
    * @brief 转换到字符串
    */
    inline char* toString(char* buf) const
    {
        PrintArray2D<Size>(buf
            , this->a.x, this->a.y, this->a.z
            , this->b.x, this->b.y, this->b.z
            , this->c.x, this->c.y, this->c.z
        );
        return buf;
    }
    /**
    * @brief 转换到字符串
    */
    inline std::string toString() const
    {
        char buf[256] = { 0 };
        return this->toString(buf);
    }
    /**
    * @brief 从字符串转换
    */
    inline TTriangle3<T>& fromString(const char* str, bool* bOk = nullptr)
    {
        bool bRet = ScanArray2D<Size>(str
            , this->a.x, this->a.y, this->a.z
            , this->b.x, this->b.y, this->b.z
            , this->c.x, this->c.y, this->c.z
        );
        SetValueToBooleanPtr(bOk, bRet);
        return *this;
    }
    /**
    * @brief 从字符串转换
    */
    inline TTriangle3<T>& fromString(const std::string& str, bool* bOk = nullptr)
    {
        this->fromString(str.c_str(), bOk);
        return *this;
    }
    /**
    * @brief 从字符串转换
    */
    static inline TTriangle3<T> FromString(const char* str, bool* bOk = nullptr)
    {
        TTriangle3<T> r;
        r.fromString(str, bOk);
        return r;
    }
    /**
    * @brief 从字符串转换
    */
    static inline TTriangle3<T> FromString(const std::string& str, bool* bOk = nullptr)
    {
        return TTriangle3<T>::FromString(str.c_str(), bOk);
    }
private:
    static inline TVec3<T> NormalFast(const TVec3<T>& a, const TVec3<T>& b, const TVec3<T>& c)
    {
        const TVec3<T> v0 = c - b;
        const TVec3<T> v1 = a - b;
        return TVec3<T>::Cross(v0, v1);
    }
};


/**
 * @brief 3D三角形数组
 * @tparam T 数值类型
*/
template <class T>
using TTriangle3Vector = std::vector<TTriangle3<T> >;

/**
 * @brief 3D三角形数组
 * @tparam T 数值类型
 * @tparam Size 数组大小
*/
template <class T, size_t Size>
using TTriangle3Array = std::array<TTriangle3<T>, Size>;


WD_NAMESPACE_END