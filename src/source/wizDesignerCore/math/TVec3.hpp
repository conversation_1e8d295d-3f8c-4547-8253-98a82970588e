#pragma once

#include "MathUtils.hpp"
#include "TVec2.hpp"

WD_NAMESPACE_BEGIN

/**
 * @brief 3D向量
 * @tparam T 数值类型
*/
template <typename T>
class TVec3
{
    static_assert(std::is_arithmetic_v<T>, "模板参数类型必须是算数类型!");
public:
    using ValueType     = T;
    using value_type    = ValueType;
    using SizeType      = size_t;
    using size_type     = SizeType;
public:
    /**
     * @brief 分量个数
    */
    static constexpr size_t Size = 3;
    /**
    * @brief 各个分量都为0的向量
    */
    static inline const TVec3<T>& Zero()
    {
        static TVec3<T> sZero(T(0));
        return sZero;
    }
    /**
     * @brief X分量为1,其他分量为0的向量
    */
    static inline const TVec3<T>& AxisX()
    {
        static TVec3<T> sAxis(T(1), T(0), T(0));
        return sAxis;
    }
    /**
     * @brief X分量为-1,其他分量为0的向量
    */
    static inline const TVec3<T>& AxisNX()
    {
        static TVec3<T> sAxis(T(-1), T(0), T(0));
        return sAxis;
    }
    /**
     * @brief Y分量为1,其他分量为0的向量
    */
    static inline const TVec3<T>& AxisY()
    {
        static TVec3<T> sAxis(T(0), T(1), T(0));
        return sAxis;
    }
    /**
     * @brief Y分量为-1,其他分量为0的向量
    */
    static inline const TVec3<T>& AxisNY()
    {
        static TVec3<T> sAxis(T(0), T(-1), T(0));
        return sAxis;
    }
    /**
     * @brief Z分量为1,其他分量为0的向量
    */
    static inline const TVec3<T>& AxisZ()
    {
        static TVec3<T> sAxis(T(0), T(0), T(1));
        return sAxis;
    }
    /**
     * @brief Z分量为-1,其他分量为0的向量
    */
    static inline const TVec3<T>& AxisNZ()
    {
        static TVec3<T> sAxis(T(0), T(0), T(-1));
        return sAxis;
    }
    /**
     * @brief 方向E(东)
    */
    static inline const TVec3<T>& DirE()
    {
        return AxisX();
    }
    /**
     * @brief 方向W(西)
    */
    static inline const TVec3<T>& DirW()
    {
        return AxisNX();
    }
    /**
     * @brief 方向N(北)
    */
    static inline const TVec3<T>& DirN()
    {
        return AxisY();
    }
    /**
     * @brief 方向S(南)
    */
    static inline const TVec3<T>& DirS()
    {
        return AxisNY();
    }
    /**
     * @brief 方向U(上)
    */
    static inline const TVec3<T>& DirU()
    {
        return AxisZ();
    }
    /**
     * @brief 方向D(下)
    */
    static inline const TVec3<T>& DirD()
    {
        return AxisNZ();
    }
public:
    // 分量 x
    T x;
    // 分量 y
    T y;
    // 分量 z
    T z;
public:
    /**
     * @brief 构造
    */
    inline TVec3() :
        x(T(0)),
        y(T(0)),
        z(T(0))
    {}
    /**
     * @brief 构造
    */
    inline TVec3(const TVec3<T>& right) :
        x(right.x),
        y(right.y),
        z(right.z)
    {}
    /**
     * @brief 构造
    */
    inline explicit TVec3(const T& s) :
        x(s),
        y(s),
        z(s)
    {}
    /**
     * @brief 构造
    */
    inline TVec3(const T& x, const T& y, const T& z) :
        x(x),
        y(y),
        z(z)
    {}
    /**
     * @brief 构造
    */
    inline explicit TVec3(const TVec2<T>& v, const T& z = T(0)) :
        x(v.x),
        y(v.y),
        z(z)
    {}
    /**
     * @brief 构造
    */
    template<class U>
    inline explicit TVec3(const TVec3<U>& right) :
        x(T(right.x)),
        y(T(right.y)),
        z(T(right.z))
    {

    }
public:
    /**
    * @brief 获取xy
    */
    inline TVec2<T> xy() const
    {
        return TVec2<T>(this->x, this->y);
    }
    /**
    * @brief 设置xy
    */
    inline void setXy(const TVec2<T>& v)
    {
        this->x = v.x;
        this->y = v.y;
    }
    /**
    * @brief 获取长度的平方
    */
    inline T lengthSq() const
    {
        return this->x * this->x + this->y * this->y + this->z * this->z;
    }
    /**
    * @brief 获取长度
    */
    inline T length() const
    {
        T sq = this->lengthSq();
        return (T)(Sqrt(sq));
    }
    /**
    * @brief 单位化
    */
    inline TVec3<T>& normalize()
    {
        T len = this->length();
        T invLen = T(1) / len;
        (*this) *= invLen;
        return *this;
    }
    /**
    * @brief 获取单位向量
    */
    inline TVec3<T> normalized() const
    {
        TVec3<T> r(*this);
        r.normalize();
        return r;
    }
public:
    /**
    * @brief 下标访问
    */
    inline size_t size() const
    {
        return TVec3<T>::Size;
    }
    /**
    * @brief 下标访问
    */
    inline T& operator[](size_t i)
    {
        return (&this->x)[i];
    }
    /**
    * @brief 下标访问
    */
    inline const T& operator[](size_t i) const
    {
        return (&this->x)[i];
    }
    /**
    * @brief 赋值
    */
    inline TVec3<T>& operator=(const TVec3<T>& v)
    {
        this->x = v.x;
        this->y = v.y;
        this->z = v.z;
        return *this;
    }

    /**
    * @brief += 标量
    */
    inline TVec3<T> & operator+=(const T& s)
    {
        this->x += s;
        this->y += s;
        this->z += s;
        return *this;
    }
    /**
    * @brief += 向量
    */
    inline TVec3<T> & operator+=(const TVec3<T>& v)
    {
        this->x += v.x;
        this->y += v.y;
        this->z += v.z;
        return *this;
    }
    /**
    * @brief -= 标量
    */
    inline TVec3<T>& operator-=(const T & s)
    {
        this->x -= s;
        this->y -= s;
        this->z -= s;
        return *this;
    }
    /**
    * @brief -= 向量
    */
    inline TVec3<T>& operator-=(const TVec3<T>& v)
    {
        this->x -= v.x;
        this->y -= v.y;
        this->z -= v.z;
        return *this;
    }
    /**
    * @brief *= 标量
    */
    template <class U>
    inline TVec3<T>& operator*=(const U& s)
    {
        this->x = T(this->x * s);
        this->y = T(this->y * s);
        this->z = T(this->z * s);
        return *this;
    }
    /**
    * @brief *= 向量
    */
    inline TVec3<T>& operator*=(const TVec3<T>& v)
    {
        this->x *= v.x;
        this->y *= v.y;
        this->z *= v.z;
        return *this;
    }
    /**
    * @brief /= 标量
    */
    template <class U>
    inline TVec3<T>& operator/=(const U& s)
    {
        this->x = T(this->x / s);
        this->y = T(this->y / s);
        this->z = T(this->z / s);
        return *this;
    }
    /**
    * @brief /= 向量
    */
    inline TVec3<T>& operator/=(const TVec3<T>& v)
    {
        this->x /= v.x;
        this->y /= v.y;
        this->z /= v.z;
        return *this;
    }
    /**
    * @brief 是否相等
    */
    friend inline bool operator==(const TVec3<T>& v1, const TVec3<T>& v2)
    {
        return (v1.x == v2.x) && (v1.y == v2.y) && (v1.z == v2.z);
    }
    /**
    * @brief 是否不相等
    */
    friend inline bool operator!=(const TVec3<T>& v1, const TVec3<T>& v2)
    {
        return (v1.x != v2.x) || (v1.y != v2.y) || (v1.z != v2.z);
    }

    friend inline TVec3<T> operator+ (const TVec3<T>& v, const T& s)
    {
        return TVec3<T>(
            v.x + s,
            v.y + s,
            v.z + s);
    }

    friend inline TVec3<T> operator+ (const T& s, const TVec3<T>& v)
    {
        return TVec3<T>(
            s + v.x,
            s + v.y,
            s + v.z);
    }

    friend inline TVec3<T> operator+ (const TVec3<T>& v1, const TVec3<T>& v2)
    {
        return TVec3<T>(
            v1.x + v2.x,
            v1.y + v2.y,
            v1.z + v2.z);
    }

    friend inline TVec3<T> operator- (const TVec3<T>& v, const T& s)
    {
        return TVec3<T>(
            v.x - s,
            v.y - s,
            v.z - s);
    }

    friend inline TVec3<T> operator- (const T& s, const TVec3<T>& v)
    {
        return TVec3<T>(
            s - v.x,
            s - v.y,
            s - v.z);
    }

    friend inline TVec3<T> operator- (const TVec3<T>& v1, const TVec3<T>& v2)
    {
        return TVec3<T>(
            v1.x - v2.x,
            v1.y - v2.y,
            v1.z - v2.z);
    }

    template <class U>
    friend inline TVec3<T> operator* (const TVec3<T>& v, const U& s)
    {
        return TVec3<T>(
            T(v.x * s),
            T(v.y * s),
            T(v.z * s));
    }

    template <class U>
    friend inline TVec3<T> operator* (const U& s, const TVec3<T>& v)
    {
        return TVec3<T>(
            T(s * v.x),
            T(s * v.y),
            T(s * v.z));
    }

    friend inline TVec3<T> operator* (const TVec3<T>& v1, const TVec3<T>& v2)
    {
        return TVec3<T>(
            v1.x * v2.x,
            v1.y * v2.y,
            v1.z * v2.z);
    }

    template <class U>
    friend inline TVec3<T> operator/ (const TVec3<T>& v, const U& s)
    {
        return TVec3<T>(
            T(v.x / s),
            T(v.y / s),
            T(v.z / s));
    }

    template <class U>
    friend inline TVec3<T> operator/ (const U& s, const TVec3<T>& v)
    {
        return TVec3<T>(
            T(s / v.x),
            T(s / v.y),
            T(s / v.z));
    }

    friend inline TVec3<T> operator/ (const TVec3<T>& v1, const TVec3<T>& v2)
    {
        return  TVec3<T>(
            v1.x / v2.x,
            v1.y / v2.y,
            v1.z / v2.z
            );
    }

    friend inline TVec3<T> operator- (const TVec3<T>& v)
    {
        return  TVec3<T>(
            -v.x,
            -v.y,
            -v.z
            );
    }

    bool operator>(const TVec3<T>& right) const
    {
        const TVec3<T>& left = (*this);
        for (size_t i = 0; i < TVec3<T>::Size; ++i)
        {
            if (left[i] > right[i])
                return true;
            else if (left[i] < right[i])
                return false;
            else
                continue;
        }
        return false;
    }

    bool operator<(const TVec3<T>& right) const
    {
        const TVec3<T>& left = (*this);
        for (size_t i = 0; i < TVec3<T>::Size; ++i)
        {
            if (left[i] < right[i])
                return true;
            else if (left[i] > right[i])
                return false;
            else
                continue;
        }
        return false;
    }

    friend bool operator>= (const TVec3<T>& left, const TVec3<T>& right)
    {
        return !(left < right);
    }

    friend bool operator<= (const TVec3<T>& left, const TVec3<T>& right)
    {
        return !(left > right);
    }
public:
    /**
    * @brief 获取两个向量的最小值(每个分量求最小)
    */
    static inline TVec3<T> Min(const TVec3<T>& v0, const TVec3<T>& v1)
    {
        return TVec3(WD::Min(v0.x, v1.x), WD::Min(v0.y, v1.y), WD::Min(v0.z, v1.z));
    }
    /**
    * @brief 获取两个向量的最大值(每个分量求最大)
    */
    static inline TVec3<T> Max(const TVec3<T>& v0, const TVec3<T>& v1)
    {
        return TVec3(WD::Max(v0.x, v1.x)
            , WD::Max(v0.y, v1.y)
            , WD::Max(v0.z, v1.z)
        );
    }
    /**
    * @brief clamp (每个分量计算Clamp)
    */
    static inline TVec3<T> Clamp(const TVec3<T>& v, const TVec3<T>& v0, const TVec3<T>& v1)
    {
        return TVec3<T>(WD::Clamp(v.x, v0.x, v1.x)
            , WD::Clamp(v.y, v0.y, v1.y)
            , WD::Clamp(v.z, v0.z, v1.z)
            );
    }
    /**
    * @brief 点投影到向量所在方向向上
    */
    static TVec3<T> ProjectOnVector(const TVec3<T>& v, const TVec3<T>& pt)
    {
        const T denominator = v.lengthSq();
        if (denominator == T(0))
            return TVec3<T>::Zero();
        const T scaler = TVec3<T>::Dot(v, pt) / denominator;
        return v * scaler;
    }
    /**
    * @brief 获取单位向量
    */
    static inline TVec3<T> Normalize(const TVec3<T>& v)
    {
        return v.normalized();
    }
    /**
    * @brief 点积
    */
    static inline T Dot(const TVec3<T>& v0, const TVec3<T>& v1)
    {
        return v0.x * v1.x + v0.y * v1.y + v0.z * v1.z;
    }
    /**
    * @brief 叉乘
    */
    static inline TVec3<T> Cross(const TVec3<T>& v0, const TVec3<T>& v1)
    {
        return  TVec3<T>
            (
                v0.y * v1.z - v1.y * v0.z,
                v0.z * v1.x - v1.z * v0.x,
                v0.x * v1.y - v1.x * v0.y
                );
    }
    /**
    * @brief 获取长度的平方
    */
    static inline T LengthSq(const TVec3<T>& v)
    {
        return v.x * v.x + v.y * v.y + v.z * v.z;
    }
    /**
    * @brief 获取长度
    */
    static inline T Length(const TVec3<T>& v)
    {
        T sq = TVec3<T>::LengthSq();
        return (T)(Sqrt(sq));
    }
    /**
    * @brief 两点距离
    */
    static inline T Distance(const TVec3<T>& p0, const TVec3<T>& p1)
    {
        TVec3<T> v = p1 - p0;
        return v.length();
    }
    /**
    * @brief 两点距离的平方
    */
    static inline T DistanceSq(const TVec3<T>& p0, const TVec3<T>& p1)
    {
        TVec3<T> v = p1 - p0;
        return v.lengthSq();
    }    
    /**
    * @brief 两向量夹角
    */
    static T Angle(const TVec3<T>& v0, const TVec3<T>& v1)
    {
        const T denominator = Sqrt(v0.lengthSq() * v1.lengthSq());

        if (denominator == 0)
            return RadToDeg<T>(T(HalfPI));

        T theta = TVec3<T>::Dot(v0, v1) / denominator;

        // clamp, to handle numerical problems

        theta = WD::Clamp(theta, T(-1), T(1));

        theta = ACos(theta);

        return RadToDeg(theta);
    }
    /**
    * @brief 插值计算
    */
    static TVec3<T>& Lerp(const TVec3<T>& v0, const TVec3<T>& v1, T alpha)
    {
        return WD::Lerp<TVec3<T>, T>(v0, v1, alpha);
    }
    /**
    * @brief 两方向向量是否同向
    *   注意：这里v0和v1必须是单位向量
    */
    static inline bool InTheSameDirection(const TVec3<T>& v0, const TVec3<T>& v1, const T& e = NumLimits<T>::Epsilon)
    {
        return TVec3<T>::Dot(v0, v1) >= T(1) - e;
    }
    /**
    * @brief 两方向向量是否反向
    *   注意：这里v0和v1必须是单位向量
    */
    static inline bool InTheOppositeDirection(const TVec3<T>& v0, const TVec3<T>& v1, const T& e = NumLimits<T>::Epsilon)
    {
        return TVec3<T>::Dot(v0, v1) < T(-1) + e;
    }
    /**
    * @brief 两方向向量是否共线
    *   注意：这里v0和v1必须是单位向量
    */
    static inline bool OnTheSameLine(const TVec3<T>& v0, const TVec3<T>& v1, const T& e = NumLimits<T>::Epsilon)
    {
        return Abs<T>(TVec3<T>::Dot(v0, v1)) >= T(1) - e;
    }
    /**
    * @brief 两方向向量是否垂直
    *   注意：这里v0和v1必须是单位向量 
    */
    static inline bool OnPrpendicular(const TVec3<T>& v0, const TVec3<T>& v1, const T& e = NumLimits<T>::Epsilon)
    {
        return Abs<T>(TVec3<T>::Dot(v0, v1)) <= e;
    }
    /**
    * @brief 两点位置是否相同(接近相同)
    */
    static inline bool SamePosition(const TVec3<T>& p0, const TVec3<T>& p1, const T& e = NumLimits<T>::Epsilon)
    {
        return TVec3<T>::DistanceSq(p0, p1) <= e;
    }
    /**
     * @brief 是否0向量
    */
    static inline bool IsZero(const TVec3<T>& v, const T& e = NumLimits<T>::Epsilon)
    {
        return v.lengthSq() <= e;
    }
    /**
     * @brief 是否单位向量
    */
    static inline bool IsNormalized(const TVec3<T>& v, const T& e = NumLimits<T>::Epsilon)
    {
        return Abs<T>(T(1) - v.lengthSq()) <= e;
    }
public:
    /**
    * @brief 转换到字符串
    */
    inline char* toString(char* buf) const
    {
        PrintArray1D(buf, this->x, this->y, this->z);
        return buf;
    }
    /**
    * @brief 转换到字符串
    */
    inline std::string toString() const
    {
        char buf[256] = { 0 };
        return this->toString(buf);
    }
    /**
    * @brief 从字符串转换
    */
    inline TVec3<T>& fromString(const char* str, bool* bOk = nullptr)
    {
        bool bRet = ScanArray1D(str, this->x, this->y, this->z);
        SetValueToBooleanPtr(bOk, bRet);
        return *this;
    }
    /**
    * @brief 从字符串转换
    */
    inline TVec3<T>& fromString(const std::string& str, bool* bOk = nullptr)
    {
        this->fromString(str.c_str(), bOk);
        return *this;
    }
    /**
    * @brief 从字符串转换
    */
    static inline TVec3<T> FromString(const char* str, bool* bOk = nullptr)
    {
        TVec3<T> r;
        r.fromString(str, bOk);
        return r;
    }
    /**
    * @brief 从字符串转换
    */
    static inline TVec3<T> FromString(const std::string& str, bool* bOk = nullptr)
    {
        return TVec3<T>::FromString(str.c_str(), bOk);
    }
};

/**
 * @brief 3D向量数组
 * @tparam T 数值类型
*/
template <class T>
using TVec3Vector = std::vector<TVec3<T> >;
/**
 * @brief 3D向量数组
 * @tparam T 数值类型
 * @tparam Size 数组大小
*/
template <class T, size_t Size>
using TVec3Array = std::array<TVec3<T>, Size>;

WD_NAMESPACE_END