#pragma once

#include "TVec3.hpp"

WD_NAMESPACE_BEGIN

/**
 * @brief 四元数
 * @tparam T 数值类型
*/
template <typename T>
class TQuat
{
    static_assert(std::is_arithmetic_v<T>, "模板参数类型必须是算数类型!");
public:
    using ValueType     = T;
    using value_type    = ValueType;
    using SizeType      = size_t;
    using size_type     = SizeType;
public:
    /**
    * @brief 数据个数
    */
    static constexpr size_t Size = 4;
    /**
    * @brief 单位四元数
    */
    static const TQuat<T>& Identity()
    {
        static TQuat<T> sIdent(T(1), T(0), T(0), T(0));
        return sIdent;
    }
public:
    // 分量 w
    T w;
    // 分量 x
    T x;
    // 分量 y
    T y;
    // 分量 z
    T z;
public:
    /**
     * @brief 构造
    */
    inline TQuat() :
        w(T(1)),
        x(T(0)),
        y(T(0)),
        z(T(0))
    {}
    /**
     * @brief 构造
    */
    inline TQuat(const TQuat<T>& right) :
        w(right.w),
        x(right.x),
        y(right.y),
        z(right.z)
    {

    }
    /**
     * @brief 构造
    */
    inline TQuat(T w, T x, T y, T z) :
        w(w),
        x(x),
        y(y),
        z(z)
    {}
    /**
     * @brief 构造
    */
    template<class U>
    inline explicit TQuat(const TQuat<U>& right) :
        w(T(right.w)),
        x(T(right.x)),
        y(T(right.y)),
        z(T(right.z))
    {
    }
    /**
     * @brief 析构
    */
    inline ~TQuat()
    {

    }
public:
    /**
    * @brief 共轭
    */
    TQuat<T>& conjugate()
    {
        x *= T(-1);
        y *= T(-1);
        z *= T(-1);

        return *this;
    }
    /**
    * @brief 求逆
    */
    TQuat<T>& invert()
    {
        // quaternion is assumed to have unit length
        return this->conjugate();
    }
    /**
    * @brief 返回当前四元数的逆
    */
    inline TQuat<T> inverse() const
    {
        TQuat<T> invQuat = (*this);
        invQuat.invert();
        return invQuat;
    }
    /**
    * @brief 长度的平方
    */
    inline T lengthSq() const
    {
        return this->x * this->x + this->y * this->y + this->z * this->z + this->w * this->w;
    }
    /**
    * @brief 长度
    */
    inline T length() const
    {
        return Sqrt(this->lengthSq());
    }
    /**
    * @brief 单位化
    */
    TQuat<T>& normalize()
    {
        T len = this->length();

        if (len == 0)
        {
            this->x = 0;
            this->y = 0;
            this->z = 0;
            this->w = 1;
        }
        else
        {
            len = T(1) / len;

            this->x *= len;
            this->y *= len;
            this->z *= len;
            this->w *= len;
        }
        return *this;
    }
    /**
    * @brief 获取单位向量
    */
    inline TQuat<T> normalized() const
    {
        TQuat<T> r(*this);
        r.normalize();
        return r;
    }
    /**
    * @brief 获取四元数的旋转角度
    */
    T angle() const
    {
        return ACos(this->w) * T(2) * T(RAD2DEG);
    }
    /**
    * @brief 获取四元数的旋转轴
    */
    TVec3<T> axis() const
    {
        T   tmp1 = T(1) - this->w * this->w;
        if (tmp1 <= T(0))
        {
            return TVec3<T>(T(0), T(0), T(1));
        }
        T   tmp2 = T(1) / Sqrt(tmp1);

        return TVec3<T>(this->x * tmp2, this->y * tmp2, this->z * tmp2);
    }
public:
    /**
    * @brief 下标访问
    */
    inline T& operator[](size_t i)
    {
        return (&this->w)[i];
    }
    /**
    * @brief 下标访问
    */
    inline const T& operator[](size_t i) const
    {
        return (&this->w)[i];
    }
    /**
    * @brief 赋值运算
    */
    inline TQuat<T>& operator=(const TQuat<T> & q)
    {
        this->w = q.w;
        this->x = q.x;
        this->y = q.y;
        this->z = q.z;
        return *this;
    }
    /**
     * @brief 四元数乘四元数 
    */
    TQuat<T> operator*(const TQuat<T> & q) const
    {
        TQuat<T> ret;
        ret.w = (w * q.w) - (x * q.x) - (y * q.y) - (z * q.z);
        ret.x = (x * q.w) + (w * q.x) + (y * q.z) - (z * q.y);
        ret.y = (y * q.w) + (w * q.y) + (z * q.x) - (x * q.z);
        ret.z = (z * q.w) + (w * q.z) + (x * q.y) - (y * q.x);
        return ret;
    }
    /**
     * @brief 四元数乘顶点
    */
    TVec3<T> operator*(const TVec3<T>& pos) const
    {
        T pW = -(x * pos.x) - (y * pos.y) - (z * pos.z);
        T pX = w * pos.x + (y * pos.z) - (z * pos.y);
        T pY = w * pos.y + (z * pos.x) - (x * pos.z);
        T pZ = w * pos.z + (x * pos.y) - (y * pos.x);
        TVec3<T> ret;
        ret.x = (pX * w) - (pW * x) - (pY * z) + (pZ * y);
        ret.y = (pY * w) - (pW * y) - (pZ * x) + (pX * z);
        ret.z = (pZ * w) - (pW * z) - (pX * y) + (pY * x);
        return ret;
    }
    /**
     * @brief ==
    */
    friend inline bool operator==(const TQuat<T> & q1, const TQuat<T> & q2)
    {
        return (q1.x == q2.x) && (q1.y == q2.y) && (q1.z == q2.z) && (q1.w == q2.w);
    }
    /**
     * @brief !=
    */
    friend inline bool operator!=(const TQuat<T> & q1, const TQuat<T> & q2)
    {
        return (q1.x != q2.x) || (q1.y != q2.y) || (q1.z != q2.z) || (q1.w != q2.w);
    }
    /**
     * @brief >
    */
    bool operator> (const TQuat<T>& right) const
    {
        const TQuat<T>& left = (*this);
        for (size_t i = 0; i < TQuat<T>::Size; ++i)
        {
            if (left[i] > right[i])
                return true;
            else if (left[i] < right[i])
                return false;
            else
                continue;
        }
        return false;
    }
    /**
     * @brief <
    */
    bool operator< (const TQuat<T>& right) const
    {
        const TQuat<T>& left = (*this);
        for (size_t i = 0; i < TQuat<T>::Size; ++i)
        {
            if (left[i] < right[i])
                return true;
            else if (left[i] > right[i])
                return false;
            else
                continue;
        }
        return false;
    }
    /**
     * @brief >=
    */
    friend bool operator>= (const TQuat<T>& left, const TQuat<T>& right)
    {
        return !(left < right);
    }
    /**
     * @brief <=
    */
    friend bool operator<= (const TQuat<T>& left, const TQuat<T>& right)
    {
        return !(left > right);
    }
public:
    /**
    * @brief 点积
    */
    static inline TQuat<T> Dot(const TQuat<T> & q1, const TQuat<T> & q2)
    {
        return q1.x * q2.x + q1.y * q2.y + q1.z * q2.z + q1.w * q2.w;
    }
    /**
     * @brief 线性插值
    */
    static inline TQuat<T> Lerp(const TQuat<T>& q1, const TQuat<T>& q2, T t)
    {
        return WD::Lerp(q1, q2, t);
    }
    /**
    * @brief 球面线性插值
    */
    static TQuat<T> Slerp(const TQuat<T> & q1, const TQuat<T> & q2, T t)
    {
        if (t <= T(0))
            return q1;
        if (t >= T(1))
            return q2;

        const T tx = q1.x;
        const T ty = q1.y;
        const T tz = q1.z;
        const T tw = q1.w;

        // http://www.euclideanspace.com/maths/algebra/realNormedAlgebra/quaternions/slerp/

        const T cosHalfTheta = tw * q2.w + tx * q2.x + ty * q2.y + tz * q2.z;

        TQuat<T> rQ = TQuat<T>::Identity;

        if (cosHalfTheta < T(0))
        {
            rQ.w = -q2.w;
            rQ.x = -q2.x;
            rQ.y = -q2.y;
            rQ.z = -q2.z;

            cosHalfTheta = -cosHalfTheta;
        }
        else
        {
            rQ = q2;
        }

        if (cosHalfTheta >= T(1))
        {
            rQ.w = tw;
            rQ.x = tx;
            rQ.y = ty;
            rQ.z = tz;

            return rQ;
        }

        const T sqrSinHalfTheta = T(1) - cosHalfTheta * cosHalfTheta;

        if (sqrSinHalfTheta <= NumLimits<T>::Epsilon)
        {
            const T s   = T(1) - t;
            rQ.w = s * tw + t * rQ.w;
            rQ.x = s * tx + t * rQ.x;
            rQ.y = s * ty + t * rQ.y;
            rQ.z = s * tz + t * rQ.z;

            rQ.normalize();

            return rQ;
        }

        const T sinHalfTheta  = Sqrt(sqrSinHalfTheta);
        const T halfTheta     = ATan2(sinHalfTheta, cosHalfTheta);
        const T ratioA        = Sin((T(1) - t) * halfTheta) / sinHalfTheta;
        const T ratioB        = Sin(t * halfTheta) / sinHalfTheta;

        rQ.w = tw * ratioA + rQ.w * ratioB;
        rQ.x = tx * ratioA + rQ.x * ratioB;
        rQ.y = ty * ratioA + rQ.y * ratioB;
        rQ.z = tz * ratioA + rQ.z * ratioB;

        return rQ;
    }
public:
    /**
    * @brief 指定轴和旋转角度计算四元数
    */
    static TQuat<T> Rotate(T angle, const TVec3<T>& axis)
    {
        const T halfAngle = DegToRad(angle / T(2));
        const T s         = Sin(halfAngle);

        const TVec3<T> tAxis = axis.normalized();

        return TQuat<T>(Cos(halfAngle)
            , tAxis.x * s
            , tAxis.y * s
            , tAxis.z * s);
    }
    /**
    * @brief 计算向量from到向量to的旋转四元数
    */
    static TQuat<T> FromVectors(const TVec3<T>& from, const TVec3<T>& to)
    {
        const TVec3<T> vFrom = from.normalized();
        const TVec3<T> vTo = to.normalized();

        TQuat<T> rQ = TQuat<T>::Identity();
        // assumes direction vectors vFrom and vTo are normalized

        T r = TVec3<T>::Dot(from, to) + T(1);

        if (r < NumLimits<T>::Epsilon)
        {
            // vFrom and vTo point in opposite directions
            r = T(0);
            if (Abs(vFrom.x) > Abs(vFrom.z))
            {
                rQ.x = -vFrom.y;
                rQ.y = vFrom.x;
                rQ.z = T(0);
                rQ.w = r;
            }
            else
            {
                rQ.x = T(0);
                rQ.y = -vFrom.z;
                rQ.z = vFrom.y;
                rQ.w = r;
            }
        }
        else
        {
            // crossVectors( vFrom, vTo ); // inlined to avoid cyclic dependency on Vector3
            rQ.x = vFrom.y * vTo.z - vFrom.z * vTo.y;
            rQ.y = vFrom.z * vTo.x - vFrom.x * vTo.z;
            rQ.z = vFrom.x * vTo.y - vFrom.y * vTo.x;
            rQ.w = r;
        }

        rQ.normalize();

        return rQ;
    }
public:
    /**
    * @brief 转换到字符串
    */
    inline char* toString(char* buf) const
    {
        PrintArray1D(buf, this->w, this->x, this->y, this->z);
        return buf;
    }
    /**
    * @brief 转换到字符串
    */
    inline std::string toString() const
    {
        char buf[256] = { 0 };
        return this->toString(buf);
    }
    /**
    * @brief 从字符串转换
    */
    inline TQuat<T>& fromString(const char* str, bool* bOk = nullptr)
    {
        bool bRet = ScanArray1D(str, this->w, this->x, this->y, this->z);
        SetValueToBooleanPtr(bOk, bRet);
        return *this;
    }
    /**
    * @brief 从字符串转换
    */
    inline TQuat<T>& fromString(const std::string& str, bool* bOk = nullptr)
    {
        this->fromString(str.c_str(), bOk);
        return *this;
    }
    /**
    * @brief 从字符串转换
    */
    static inline TQuat<T> FromString(const char* str, bool* bOk = nullptr)
    {
        TQuat<T> r;
        r.fromString(str, bOk);
        return r;
    }
    /**
    * @brief 从字符串转换
    */
    static inline TQuat<T> FromString(const std::string& str, bool* bOk = nullptr)
    {
        return TQuat<T>::FromString(str.c_str(), bOk);
    }
};

/**
 * @brief 四元数数组
 * @tparam T 数值类型
*/
template <class T>
using TQuatVector = std::vector<TQuat<T> >;
/**
 * @brief 四元数数组
 * @tparam T 数值类型
 * @tparam Size 数组大小
*/
template <class T, size_t Size>
using TQuatArray = std::array<TQuat<T>, Size>;


WD_NAMESPACE_END
