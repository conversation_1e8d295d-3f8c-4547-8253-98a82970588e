#pragma once

#include "TMat3.hpp"
#include "TVec4.hpp"
#include "TQuat.hpp"

WD_NAMESPACE_BEGIN

/**
 * @brief 4x4矩阵
 * @tparam T 数值类型
*/
template <typename T>
class TMat4
{
    static_assert(std::is_arithmetic_v<T>, "模板参数类型必须是算数类型!");
public:
    using ValueType     = T;
    using value_type    = ValueType;
    using SizeType      = size_t;
    using size_type     = SizeType;
public:
    /**
    * @brief 行数据个数
    */
    static constexpr size_t RowSize = 4;
    /**
    * @brief 列数据个数
    */
    static constexpr size_t ColSize = TVec4<T>::Size;
    /**
    * @brief 单位矩阵
    */
    static const TMat4<T>& Identity()
    {
        static TMat4<T> sIdent(T(1));
        return sIdent;
    }
private:
    TVec4Array<T, RowSize> _value;
public:
    /**
     * @brief 构造
    */
    inline TMat4()
    {
        this->_value[0] = TVec4<T>(T(1), T(0), T(0), T(0));
        this->_value[1] = TVec4<T>(T(0), T(1), T(0), T(0));
        this->_value[2] = TVec4<T>(T(0), T(0), T(1), T(0));
        this->_value[3] = TVec4<T>(T(0), T(0), T(0), T(1));
    }
    /**
     * @brief 构造
    */
    inline TMat4(const T* val)
    {
        memcpy(data(),val,sizeof(T) * 16);
    }
    /**
     * @brief 构造
    */
    inline TMat4(const TMat4<T>& right)
    {
        this->_value = right._value;
    }
    /**
     * @brief 构造
    */
    explicit inline TMat4(T s)
    {
        this->_value[0] = TVec4<T>(s, T(0), T(0), T(0));
        this->_value[1] = TVec4<T>(T(0), s, T(0), T(0));
        this->_value[2] = TVec4<T>(T(0), T(0), s, T(0));
        this->_value[3] = TVec4<T>(T(0), T(0), T(0), T(1));
    }
    /**
     * @brief 构造
    */
    inline TMat4(T x0, T y0, T z0, T w0
        , T x1, T y1, T z1, T w1
        , T x2, T y2, T z2, T w2
        , T x3, T y3, T z3, T w3)
    {
        this->_value[0] = TVec4<T>(x0, y0, z0, w0);
        this->_value[1] = TVec4<T>(x1, y1, z1, w1);
        this->_value[2] = TVec4<T>(x2, y2, z2, w2);
        this->_value[3] = TVec4<T>(x3, y3, z3, w3);
    }
    /**
     * @brief 构造
    */
    inline TMat4(const TVec3<T>& v0, const TVec3<T>& v1, const TVec3<T>& v2)
    {
        this->_value[0] = TVec4<T>(v0, T(0));
        this->_value[1] = TVec4<T>(v1, T(0));
        this->_value[2] = TVec4<T>(v2, T(0));
        this->_value[3] = TVec4<T>(TVec3<T>(0), T(1));
    }
    /**
     * @brief 构造
    */
    inline TMat4(const TMat3<T>& m3, const TVec3<T>& v = TVec3<T>(T(0)))
    {
        this->_value[0] = TVec4<T>(m3[0], T(0));
        this->_value[1] = TVec4<T>(m3[1], T(0));
        this->_value[2] = TVec4<T>(m3[2], T(0));
        this->_value[3] = TVec4<T>(v, T(1));
    }
    /**
     * @brief 构造
    */
    inline TMat4(const TVec4<T>& v0, const TVec4<T>& v1, const TVec4<T>& v2, const TVec4<T>& v3)
    {
        this->_value[0] = v0;
        this->_value[1] = v1;
        this->_value[2] = v2;
        this->_value[3] = v3;
    }
    /**
     * @brief 构造
    */
    template<class U>
    inline explicit TMat4(const TMat4<U>& right)
    {
        this->_value[0] = TVec4<T>(right[0]);
        this->_value[1] = TVec4<T>(right[1]);
        this->_value[2] = TVec4<T>(right[2]);
        this->_value[3] = TVec4<T>(right[3]);
    }
public:
    /**
    * @brief 数据首地址
    */
    inline T* data()
    {
        return &_value[0][0];
    }
    /**
    * @brief 数据首地址
    */
    inline const T* data() const
    {
        return &_value[0][0];
    }
    /**
    * @brief 单位化
    */
    TMat4<T>& identity()
    {
        this->_value[0] = TVec4<T>(T(1), T(0), T(0), T(0));
        this->_value[1] = TVec4<T>(T(0), T(1), T(0), T(0));
        this->_value[2] = TVec4<T>(T(0), T(0), T(1), T(0));
        this->_value[3] = TVec4<T>(T(0), T(0), T(0), T(1));
        return *this;
    }
    /**
    * @brief 转置
    */
    TMat4<T>& transpose()
    {
        T* te = this->data();
        T tmp;

        tmp = te[1]; te[1] = te[4]; te[4] = tmp;
        tmp = te[2]; te[2] = te[8]; te[8] = tmp;
        tmp = te[6]; te[6] = te[9]; te[9] = tmp;

        tmp = te[3]; te[3] = te[12]; te[12] = tmp;
        tmp = te[7]; te[7] = te[13]; te[13] = tmp;
        tmp = te[11]; te[11] = te[14]; te[14] = tmp;

        return *this;
    }
    /**
    * @brief 转置
    */
    TMat4<T> transposed() const
    {
        TMat4<T> tMat = (*this);
        tMat.transpose();
        return tMat;
    }
    /**
    * @brief 行列式
    */
    T determinant() const
    {
        const T* te = this->data();

        T n11 = te[0]; T n12 = te[4]; T n13 = te[8];  T n14 = te[12];
        T n21 = te[1]; T n22 = te[5]; T n23 = te[9];  T n24 = te[13];
        T n31 = te[2]; T n32 = te[6]; T n33 = te[10]; T n34 = te[14];
        T n41 = te[3]; T n42 = te[7]; T n43 = te[11]; T n44 = te[15];

        //TODO: make this more efficient
        //( based on http://www.euclideanspace.com/maths/algebra/matrix/functions/inverse/fourD/index.htm )

        return (
            n41 * (
                +n14 * n23 * n32
                - n13 * n24 * n32
                - n14 * n22 * n33
                + n12 * n24 * n33
                + n13 * n22 * n34
                - n12 * n23 * n34
                ) +
            n42 * (
                +n11 * n23 * n34
                - n11 * n24 * n33
                + n14 * n21 * n33
                - n13 * n21 * n34
                + n13 * n24 * n31
                - n14 * n23 * n31
                ) +
            n43 * (
                +n11 * n24 * n32
                - n11 * n22 * n34
                - n14 * n21 * n32
                + n12 * n21 * n34
                + n14 * n22 * n31
                - n12 * n24 * n31
                ) +
            n44 * (
                -n13 * n22 * n31
                - n11 * n23 * n32
                + n11 * n22 * n33
                + n13 * n21 * n32
                - n12 * n21 * n33
                + n12 * n23 * n31
                )
            );
    }
    /**
    * @brief 求逆
    */
    TMat4<T>& invert()
    {
        T* te = this->data();

        // based on http://www.euclideanspace.com/maths/algebra/matrix/functions/inverse/fourD/index.htm

        T n11 = te[0];  T n21 = te[1];  T n31 = te[2];  T n41 = te[3];
        T n12 = te[4];  T n22 = te[5];  T n32 = te[6];  T n42 = te[7];
        T n13 = te[8];  T n23 = te[9];  T n33 = te[10]; T n43 = te[11];
        T n14 = te[12]; T n24 = te[13]; T n34 = te[14]; T n44 = te[15];

        T t11 = n23 * n34 * n42 - n24 * n33 * n42 + n24 * n32 * n43 - n22 * n34 * n43 - n23 * n32 * n44 + n22 * n33 * n44;
        T t12 = n14 * n33 * n42 - n13 * n34 * n42 - n14 * n32 * n43 + n12 * n34 * n43 + n13 * n32 * n44 - n12 * n33 * n44;
        T t13 = n13 * n24 * n42 - n14 * n23 * n42 + n14 * n22 * n43 - n12 * n24 * n43 - n13 * n22 * n44 + n12 * n23 * n44;
        T t14 = n14 * n23 * n32 - n13 * n24 * n32 - n14 * n22 * n33 + n12 * n24 * n33 + n13 * n22 * n34 - n12 * n23 * n34;

        T det = n11 * t11 + n21 * t12 + n31 * t13 + n41 * t14;

        if (det == 0)
        {
            assert("Mat4 invert error!" && 0);
            static TMat4<T> sIdent(T(1));
            return sIdent;
        }

        T detInv = T(1) / det;

        te[0] = t11 * detInv;
        te[1] = (n24 * n33 * n41 - n23 * n34 * n41 - n24 * n31 * n43 + n21 * n34 * n43 + n23 * n31 * n44 - n21 * n33 * n44) * detInv;
        te[2] = (n22 * n34 * n41 - n24 * n32 * n41 + n24 * n31 * n42 - n21 * n34 * n42 - n22 * n31 * n44 + n21 * n32 * n44) * detInv;
        te[3] = (n23 * n32 * n41 - n22 * n33 * n41 - n23 * n31 * n42 + n21 * n33 * n42 + n22 * n31 * n43 - n21 * n32 * n43) * detInv;

        te[4] = t12 * detInv;
        te[5] = (n13 * n34 * n41 - n14 * n33 * n41 + n14 * n31 * n43 - n11 * n34 * n43 - n13 * n31 * n44 + n11 * n33 * n44) * detInv;
        te[6] = (n14 * n32 * n41 - n12 * n34 * n41 - n14 * n31 * n42 + n11 * n34 * n42 + n12 * n31 * n44 - n11 * n32 * n44) * detInv;
        te[7] = (n12 * n33 * n41 - n13 * n32 * n41 + n13 * n31 * n42 - n11 * n33 * n42 - n12 * n31 * n43 + n11 * n32 * n43) * detInv;

        te[8] = t13 * detInv;
        te[9] = (n14 * n23 * n41 - n13 * n24 * n41 - n14 * n21 * n43 + n11 * n24 * n43 + n13 * n21 * n44 - n11 * n23 * n44) * detInv;
        te[10] = (n12 * n24 * n41 - n14 * n22 * n41 + n14 * n21 * n42 - n11 * n24 * n42 - n12 * n21 * n44 + n11 * n22 * n44) * detInv;
        te[11] = (n13 * n22 * n41 - n12 * n23 * n41 - n13 * n21 * n42 + n11 * n23 * n42 + n12 * n21 * n43 - n11 * n22 * n43) * detInv;

        te[12] = t14 * detInv;
        te[13] = (n13 * n24 * n31 - n14 * n23 * n31 + n14 * n21 * n33 - n11 * n24 * n33 - n13 * n21 * n34 + n11 * n23 * n34) * detInv;
        te[14] = (n14 * n22 * n31 - n12 * n24 * n31 - n14 * n21 * n32 + n11 * n24 * n32 + n12 * n21 * n34 - n11 * n22 * n34) * detInv;
        te[15] = (n12 * n23 * n31 - n13 * n22 * n31 + n13 * n21 * n32 - n11 * n23 * n32 - n12 * n21 * n33 + n11 * n22 * n33) * detInv;

        return *this;
    }
    /**
    * @brief 返回当前矩阵的逆矩阵
    */
    inline TMat4<T> inverse() const
    {
        TMat4<T> invMat = (*this);
        invMat.invert();
        return invMat;
    }
    /**
    * @brief 提取 translation
    */
    inline TVec3<T> extractTranslation() const
    {
        return TMat4<T>::ExtractTranslation(*this);
    }
    /**
    * @brief 提取 rotation
    */
    inline TMat4<T> extractRotation() const
    {
        return TMat4<T>::ExtractRotation(*this);
    }
    /**
    * @brief 提取 旋转四元数
    */
    inline TQuat<T> extractRotationQuat() const
    {
        return TMat4<T>::ExtractRotationQuat(*this);
    }
    /**
    * @brief 提取 scale
    */
    inline TVec3<T> extractScale() const
    {
        return TMat4<T>::ExtractScale(*this);
    }
    /**
    * @brief 获取轴向的最大缩放值
    */
    T maxScaleOnAxis() const
    {
        const T* te = this->data();

        const T scaleXSq = te[0] * te[0] + te[1] * te[1] + te[2] * te[2];
        const T scaleYSq = te[4] * te[4] + te[5] * te[5] + te[6] * te[6];
        const T scaleZSq = te[8] * te[8] + te[9] * te[9] + te[10] * te[10];

        return Sqrt(Max(scaleXSq, scaleYSq, scaleZSq));
    }
    /**
     * @brief 是否单位矩阵
    */
    bool isIdentity(T e = NumLimits<T>::Epsilon) const
    {
        const auto& m = *this;
        constexpr T zero(0);
        constexpr T one(1);
        return Abs(m[0][0] - one)   <= e
            && Abs(m[0][1] - zero)  <= e
            && Abs(m[0][2] - zero)  <= e
            && Abs(m[0][3] - zero)  <= e
            && Abs(m[1][0] - zero)  <= e
            && Abs(m[1][1] - one)   <= e
            && Abs(m[1][2] - zero)  <= e
            && Abs(m[1][3] - zero)  <= e
            && Abs(m[2][0] - zero)  <= e
            && Abs(m[2][1] - zero)  <= e
            && Abs(m[2][2] - one)   <= e
            && Abs(m[2][3] - zero)  <= e
            && Abs(m[3][0] - zero)  <= e
            && Abs(m[3][1] - zero)  <= e
            && Abs(m[3][2] - zero)  <= e
            && Abs(m[3][3] - one)   <= e;
    }
public:
    /**
    * @brief 下标访问
    */
    inline TVec4<T>& operator[](size_t i)
    {
        return this->_value[i];
    }
    /**
    * @brief 下标访问
    */
    inline const TVec4<T>& operator[](size_t i) const
    {
        return this->_value[i];
    }
    /**
    * @brief 赋值运算
    */
    inline TMat4<T>& operator=(const TMat4<T> & m)
    {
        this->_value = m._value;
        return *this;
    }

    inline TMat4<T>& operator+=(const T& s)
    {
        this->_value[0] += s;
        this->_value[1] += s;
        this->_value[2] += s;
        this->_value[3] += s;
        return *this;
    }

    inline TMat4<T>& operator+=(const TMat4<T>& m)
    {
        this->_value[0] += m[0];
        this->_value[1] += m[1];
        this->_value[2] += m[2];
        this->_value[3] += m[3];
        return *this;
    }

    inline TMat4<T>& operator-=(const T& s)
    {
        this->_value[0] -= s;
        this->_value[1] -= s;
        this->_value[2] -= s;
        this->_value[3] -= s;
        return *this;
    }

    inline TMat4<T>& operator-=(const TMat3<T>& m)
    {
        this->_value[0] -= m[0];
        this->_value[1] -= m[1];
        this->_value[2] -= m[2];
        this->_value[3] -= m[2];
        return *this;
    }

    inline TMat4<T>& operator*= (const T& s)
    {
        this->_value[0] *= s;
        this->_value[1] *= s;
        this->_value[2] *= s;
        this->_value[3] *= s;
        return *this;
    }

    inline TMat4<T>& operator*= (const TMat4<T> & m)
    {
        return (*this = *this * m);
    }

    inline TMat4<T> & operator/= (const T& s)
    {
        this->_value[0] /= s;
        this->_value[1] /= s;
        this->_value[2] /= s;
        this->_value[3] /= s;
        return *this;
    }
public:
    friend inline TVec3<T> operator* (const TMat4<T>& mat, const TVec3<T>& v)
    {
        T w = T(1) / (mat[0][3] * v.x + mat[1][3] * v.y + mat[2][3] * v.z + mat[3][3]);
        return  TVec3<T>
            (
                (v.x * mat[0][0] + v.y * mat[1][0] + v.z * mat[2][0] + 1 * mat[3][0]) * w,
                (v.x * mat[0][1] + v.y * mat[1][1] + v.z * mat[2][1] + 1 * mat[3][1]) * w,
                (v.x * mat[0][2] + v.y * mat[1][2] + v.z * mat[2][2] + 1 * mat[3][2]) * w
                );
    }

    friend inline TVec4<T> operator* (const TMat4<T>& m, const TVec4<T>& v)
    {
        return TVec4<T>(
            m[0][0] * v.x + m[1][0] * v.y + m[2][0] * v.z + m[3][0] * v.w,
            m[0][1] * v.x + m[1][1] * v.y + m[2][1] * v.z + m[3][1] * v.w,
            m[0][2] * v.x + m[1][2] * v.y + m[2][2] * v.z + m[3][2] * v.w,
            m[0][3] * v.x + m[1][3] * v.y + m[2][3] * v.z + m[3][3] * v.w);
    }

    friend inline TMat4<T> operator+ (const TMat4<T> & m, T s)
    {
        return TMat4<T>(
            m[0] + s,
            m[1] + s,
            m[2] + s,
            m[3] + s);
    }

    friend inline TMat4<T> operator+ (T s, const TMat4<T>& m)
    {
        return TMat4<T>(
            m[0] + s,
            m[1] + s,
            m[2] + s,
            m[3] + s);
    }

    friend inline TMat4<T> operator+ (const TMat4<T>& m1, const TMat4<T>& m2)
    {
        return TMat4<T>(
            m1[0] + m2[0],
            m1[1] + m2[1],
            m1[2] + m2[2],
            m1[3] + m2[3]);
    }

    friend inline TMat4<T> operator- (const TMat4<T>& m, T s)
    {
        return TMat4<T>(
            m[0] - s,
            m[1] - s,
            m[2] - s,
            m[3] - s);
    }

    friend inline TMat4<T> operator- (T s, const TMat4<T>& m)
    {
        return TMat4<T>(
            s - m[0],
            s - m[1],
            s - m[2],
            s - m[3]);
    }

    friend inline TMat4<T> operator- (const TMat4<T>& m1, const TMat4<T>& m2)
    {
        return TMat4<T>(
            m1[0] - m2[0],
            m1[1] - m2[1],
            m1[2] - m2[2],
            m1[3] - m2[3]);
    }

    friend inline TMat4<T> operator* (const TMat4<T>& m, T s)
    {
        return TMat4<T>(
            m[0] * s,
            m[1] * s,
            m[2] * s,
            m[3] * s);
    }

    friend inline TMat4<T> operator* (T s, const TMat4<T>& m)
    {
        return TMat4<T>(
            m[0] * s,
            m[1] * s,
            m[2] * s,
            m[3] * s);
    }

    friend TMat4<T> operator* (const TMat4<T>& m1, const TMat4<T>& m2)
    {
        const TVec4<T>& srcA0 = m1[0];
        const TVec4<T>& srcA1 = m1[1];
        const TVec4<T>& srcA2 = m1[2];
        const TVec4<T>& srcA3 = m1[3];

        const TVec4<T>& srcB0 = m2[0];
        const TVec4<T>& srcB1 = m2[1];
        const TVec4<T>& srcB2 = m2[2];
        const TVec4<T>& srcB3 = m2[3];

        TMat4<T> res;
        res[0] = srcA0 * srcB0[0] + srcA1 * srcB0[1] + srcA2 * srcB0[2] + srcA3 * srcB0[3];
        res[1] = srcA0 * srcB1[0] + srcA1 * srcB1[1] + srcA2 * srcB1[2] + srcA3 * srcB1[3];
        res[2] = srcA0 * srcB2[0] + srcA1 * srcB2[1] + srcA2 * srcB2[2] + srcA3 * srcB2[3];
        res[3] = srcA0 * srcB3[0] + srcA1 * srcB3[1] + srcA2 * srcB3[2] + srcA3 * srcB3[3];

        return res;
    }

    friend inline TMat4<T> operator/ (const TMat4<T>& m, T s)
    {
        return TMat4<T>(
            m[0] / s,
            m[1] / s,
            m[2] / s,
            m[3] / s);
    }

    friend inline TMat4<T> operator/ (T s, const TMat4<T>& m)
    {
        return TMat4<T>(
            s / m[0],
            s / m[1],
            s / m[2],
            s / m[3]);
    }

    friend inline bool operator==(const TMat4<T> & m1, const TMat4<T>& m2)
    {
        return (m1[0] == m2[0]) && (m1[1] == m2[1]) && (m1[2] == m2[2]) && (m1[3] == m2[3]);
    }

    friend inline bool operator!=(const TMat4<T> & m1, const TMat4<T>& m2)
    {
        return (m1[0] != m2[0]) || (m1[1] != m2[1]) || (m1[2] != m2[2]) || (m1[3] != m2[3]);
    }

    bool operator>(const TMat4<T>& right) const
    {
        const TMat4<T>& left = (*this);
        for (size_t i = 0; i < TMat4<T>::RowSize; ++i)
        {
            if (left[i] > right[i])
                return true;
            else if (left[i] < right[i])
                return false;
            else
                continue;
        }
        return false;
    }

    bool operator<(const TMat4<T>& right) const
    {
        const TMat4<T>& left = (*this);
        for (size_t i = 0; i < TMat4<T>::RowSize; ++i)
        {
            if (left[i] < right[i])
                return true;
            else if (left[i] > right[i])
                return false;
            else
                continue;
        }
        return false;
    }

    friend bool operator>= (const TMat4<T>& left, const TMat4<T>& right)
    {
        return !(left < right);
    }

    friend bool operator<= (const TMat4<T>& left, const TMat4<T>& right)
    {
        return !(left > right);
    }
public:
    /**
    * @brief 使用Mat3矩阵填充
    */
    static inline TMat4<T> FromMat3(const TMat3<T>& m)
    {
        return TMat4<T>(m);
    }
    /**
    * @brief 获取Mat3
    */
    static inline TMat3<T> ToMat3(const TMat4<T>& m)
    {
        return TMat3<T>(m[0].xyz(), m[1].xyz(), m[2].xyz());
    }
    /**
    * @brief 制作translation矩阵
    */
    static inline TMat4<T> MakeTranslation(T x, T y, T z)
    {
        return TMat4<T>(TMat3<T>::Identity(), TVec3<T>(x, y, z));
    }
    /**
    * @brief 制作translation矩阵
    */
    static inline TMat4<T> MakeTranslation(const TVec3<T>& v)
    {
        return TMat4<T>(TMat3<T>::Identity(), v);
    }
    /**
    * @brief 绕X轴的旋转矩阵
    * @param angle 绕X轴的旋转角度,角度制
    */
    static TMat4<T> MakeRotationX(T angle)
    {
        const T theta = DegToRad(angle);
        const T c = Cos(theta);
        const T s = Sin(theta);

        return TMat4<T>(
            T(1), T(0), T(0), T(0),
            T(0), c, s, T(0),
            T(0), -s, c, T(0),
            T(0), T(0), T(0), T(1)
            );
    }
    /**
    * @brief 绕Y轴的旋转矩阵
    * @param angle 绕Y轴的旋转角度,角度制
    */
    static TMat4<T> MakeRotationY(T angle)
    {
        const T theta = DegToRad(angle);
        const T c = Cos(theta);
        const T s = Sin(theta);

        return TMat4<T>(
            c, T(0), -s, T(0),
            T(0), T(1), T(0), T(0),
            s, T(0), c, T(0),
            T(0), T(0), T(0), T(1)
            );
    }
    /**
    * @brief 绕Z轴的旋转矩阵
    * @param angle 绕Z轴的旋转角度,角度制
    */
    static TMat4<T> MakeRotationZ(T angle)
    {
        const T theta = DegToRad(angle);
        const T c = Cos(theta);
        const T s = Sin(theta);

        return TMat4<T>(
            c, s, T(0), T(0),
            -s, c, T(0), T(0),
            T(0), T(0), T(1), T(0),
            T(0), T(0), T(0), T(1)
            );
    }
    /**
    * @brief 绕任意轴的旋转矩阵
    * @param angle 绕任意轴的旋转角度,角度制
    */
    static TMat4<T> MakeRotation(T angle, const TVec3<T>& v)
    {
        const T a = DegToRad(angle);
        const T c = Cos(a);
        const T s = Sin(a);

        TVec3<T> axis = v.normalized();

        TVec3<T> temp = (T(1) - c) * axis;

        TMat4<T> res;
        res[0][0] = c + temp[0] * axis[0];
        res[0][1] = T(0) + temp[0] * axis[1] + s * axis[2];
        res[0][2] = T(0) + temp[0] * axis[2] - s * axis[1];
        res[0][3] = T(0);

        res[1][0] = T(0) + temp[1] * axis[0] - s * axis[2];
        res[1][1] = c + temp[1] * axis[1];
        res[1][2] = T(0) + temp[1] * axis[2] + s * axis[0];
        res[1][3] = T(0);

        res[2][0] = T(0) + temp[2] * axis[0] + s * axis[1];
        res[2][1] = T(0) + temp[2] * axis[1] - s * axis[0];
        res[2][2] = c + temp[2] * axis[2];
        res[2][3] = T(0);

        res[3][0] = T(0);
        res[3][1] = T(0);
        res[3][2] = T(0);
        res[3][3] = T(1);

        return res;
    }
    /**
    * @brief 沿着XYZ轴的缩放矩阵
    */
    static inline TMat4<T> MakeScale(T x, T y, T z)
    {
        return TMat4<T>(
            x, T(0), T(0), T(0),
            T(0), y, T(0), T(0),
            T(0), T(0), z, T(0),
            T(0), T(0), T(0), T(1)
            );
    }
    /**
    * @brief 沿着XYZ轴的缩放矩阵
    */
    static inline TMat4<T> MakeScale(const TVec3<T>& v)
    {
        return MakeScale(v.x, v.y, v.z);
    }
    /**
     * @brief 以任意轴向n为镜像平面法线的镜像矩阵
     */
    static inline TMat4<T> MakeMirror(const TVec3<T>& n)
    {
        constexpr auto d0 = T(0);
        constexpr auto d1 = T(1);
        constexpr auto d2 = T(2);
        return TMat4<T>(
            d1 - d2 * n.x * n.x, -d2 * n.x * n.y, -d2 * n.x * n.z, d0
            , -d2 * n.x * n.y, d1 - d2 * n.y * n.y, -d2 * n.y * n.z, d0
            , -d2 * n.x * n.z, -d2 * n.y * n.z, d1 - d2 * n.z * n.z, d0
            , d0, d0, d0, d1
        );
    }
    /**
     * @brief 以X轴为镜像平面法线的镜像矩阵
     */
    static inline TMat4<T> MakeMirrorX()
    {
        return TMat4<T>(
             T(-1), T(0), T(0), T(0)
            , T(0), T(1), T(0), T(0)
            , T(0), T(0), T(1), T(0)
            , T(0), T(0), T(0), T(1)
        );
    }
    /**
     * @brief 以Y轴为镜像平面法线的镜像矩阵
     */
    static inline TMat4<T> MakeMirrorY()
    {
        return TMat4<T>(
              T(1), T(0), T(0), T(0)
            , T(0), T(-1), T(0), T(0)
            , T(0), T(0), T(1), T(0)
            , T(0), T(0), T(0), T(1)
        );
    }
    /**
     * @brief 以Z轴为镜像平面法线的镜像矩阵
     */
    static inline TMat4<T> MakeMirrorZ()
    {
        return TMat4<T>(
              T(1), T(0), T(0), T(0)
            , T(0), T(1), T(0), T(0)
            , T(0), T(0), T(-1), T(0)
            , T(0), T(0), T(0), T(1)
        );
    }

    /**
    * @brief 切变矩阵
    */
    static inline TMat4<T> MakeShear(T x, T y, T z)
    {
        return TMat4<T>(
            T(1), y, z, T(0),
            x, T(1), z, T(0),
            x, y, T(1), T(0),
            T(0), T(0), T(0), T(1)
            );
    }
    /**
    * @brief 切变矩阵
    */
    static inline TMat4<T> MakeShear(const TVec3<T>& v)
    {
        return MakeShear(v.x, v.y, v.z);
    }
    /**
    * @brief 提取translation
    */
    static inline TVec3<T> ExtractTranslation(const TMat4<T>& m)
    {
        return m[3].xyz();
    }
    /**
    * @brief 提取旋转矩阵
    */
    static TMat4<T> ExtractRotation(const TMat4<T>& m)
    {
        // this method does not support reflection matrices

        TMat4<T> rMat = TMat4<T>::Identity();

        T* te = rMat.data();
        const T* me = m.data();

        T scaleX = T(1) / m[0].xyz().length();
        T scaleY = T(1) / m[1].xyz().length();
        T scaleZ = T(1) / m[2].xyz().length();

        te[0] = me[0] * scaleX;
        te[1] = me[1] * scaleX;
        te[2] = me[2] * scaleX;
        te[3] = 0;

        te[4] = me[4] * scaleY;
        te[5] = me[5] * scaleY;
        te[6] = me[6] * scaleY;
        te[7] = 0;

        te[8] = me[8] * scaleZ;
        te[9] = me[9] * scaleZ;
        te[10] = me[10] * scaleZ;
        te[11] = 0;

        te[12] = 0;
        te[13] = 0;
        te[14] = 0;
        te[15] = 1;

        return rMat;
    }
    /**
    * @brief 提取旋转四元数
    */
    static inline TQuat<T> ExtractRotationQuat(const TMat4<T>& m)
    {
        TMat4<T> rMat = TMat4<T>::ExtractRotation(m);
        return TMat4<T>::ToQuat(rMat);
    }

    /**
    * @brief 提取轴向缩放信息
    */
    static TVec3<T> ExtractScale(const TMat4<T>& m)
    {
        TVec3<T> scale(T(1));
        //
        T sx = TVec3<T>(m[0][0], m[0][1], m[0][2]).length();
        T sy = TVec3<T>(m[1][0], m[1][1], m[1][2]).length();
        T sz = TVec3<T>(m[2][0], m[2][1], m[2][2]).length();

        if (m.determinant() < 0)
        {
            sx = -sx;
        }

        return TVec3<T>(sx, sy, sz);
    }
    /**
    * @brief 四元数转换为旋转矩阵
    */
    static TMat4<T> FromQuat(const TQuat<T>& q)
    {
        return TMat4<T>::Compose(TVec3<T>(T(0)), q, TVec3<T>(T(1)));
    }
    /**
    * @brief 旋转矩阵转换为四元数
    */
    static TQuat<T> ToQuat(const TMat4<T>& m)
    {
        // http://www.euclideanspace.com/maths/geometry/rotations/conversions/matrixToQuaternion/index.htm

        // assumes the upper 3x3 of m is a pure rotation matrix (i.e, unscaled)

        TQuat<T> rQ = TQuat<T>::Identity();

        const T* te = m.data();

        const T& m11 = te[0]; const T& m12 = te[4]; const T& m13 = te[8];
        const T& m21 = te[1]; const T& m22 = te[5]; const T& m23 = te[9];
        const T& m31 = te[2]; const T& m32 = te[6]; const T& m33 = te[10];

        const T trace = m11 + m22 + m33;

        if (trace > T(0))
        {
            const T s = T(0.5) / T(Sqrt(trace + T(1.0)));

            rQ.w = T(0.25) / s;
            rQ.x = (m32 - m23) * s; 
            rQ.y = (m13 - m31) * s;
            rQ.z = (m21 - m12) * s;

        }
        else if (m11 > m22 && m11 > m33)
        {
            const T s = T(2.0) * T(Sqrt(T(1.0) + m11 - m22 - m33));

            rQ.w = (m32 - m23) / s;
            rQ.x = T(0.25) * s;
            rQ.y = (m12 + m21) / s;
            rQ.z = (m13 + m31) / s;
        }
        else if (m22 > m33)
        {
            const T s = T(2.0) * T(Sqrt(T(1.0) + m22 - m11 - m33));

            rQ.w = (m13 - m31) / s;
            rQ.x = (m12 + m21) / s;
            rQ.y = T(0.25) * s;
            rQ.z = (m23 + m32) / s;
        }
        else
        {
            const T s = T(2.0) * T(Sqrt(1.0 + m33 - m11 - m22));

            rQ.w = (m21 - m12) / s;
            rQ.x = (m13 + m31) / s;
            rQ.y = (m23 + m32) / s;
            rQ.z = T(0.25) * s;
        }
        return rQ;
    }
    /**
    * @brief 使用 位置,旋转，轴向缩放 构造TRS矩阵
    */
    static TMat4<T> Compose(const TVec3<T>& position, const TQuat<T>& quaternion, const TVec3<T>& scale)
    {
        TMat4<T> rMat = TMat4<T>::Identity();

        T* te = rMat.data();

        const T x = quaternion.x;
        const T y = quaternion.y;
        const T z = quaternion.z;
        const T w = quaternion.w;

        const T x2 = x + x;   const T y2 = y + y;  const T z2 = z + z;
        const T xx = x * x2;  const T xy = x * y2; const T xz = x * z2;
        const T yy = y * y2;  const T yz = y * z2; const T zz = z * z2;
        const T wx = w * x2;  const T wy = w * y2; const T wz = w * z2;

        const T sx = scale.x;
        const T sy = scale.y;
        const T sz = scale.z;

        te[0] = (T(1) - (yy + zz)) * sx;
        te[1] = (xy + wz) * sx;
        te[2] = (xz - wy) * sx;
        te[3] = T(0);

        te[4] = (xy - wz) * sy;
        te[5] = (T(1) - (xx + zz)) * sy;
        te[6] = (yz + wx) * sy;
        te[7] = T(0);

        te[8] = (xz + wy) * sz;
        te[9] = (yz - wx) * sz;
        te[10] = (T(1) - (xx + yy)) * sz;
        te[11] = T(0);

        te[12] = position.x;
        te[13] = position.y;
        te[14] = position.z;
        te[15] = T(1);

        return rMat;
    }
    /**
    * @brief 使用 位置,旋转 构造TR矩阵
    */
    static TMat4<T> Compose(const TVec3<T>& position, const TQuat<T>& quaternion)
    {
        TMat4<T> rMat = TMat4<T>::Identity();

        T* te = rMat.data();

        const T x = quaternion.x;
        const T y = quaternion.y;
        const T z = quaternion.z;
        const T w = quaternion.w;

        const T x2 = x + x;   const T y2 = y + y;   const T z2 = z + z;
        const T xx = x * x2;  const T xy = x * y2;  const T xz = x * z2;
        const T yy = y * y2;  const T yz = y * z2;  const T zz = z * z2;
        const T wx = w * x2;  const T wy = w * y2;  const T wz = w * z2;

        te[0] = (T(1) - (yy + zz));
        te[1] = (xy + wz);
        te[2] = (xz - wy);
        te[3] = T(0);

        te[4] = (xy - wz);
        te[5] = (T(1) - (xx + zz));
        te[6] = (yz + wx);
        te[7] = T(0);

        te[8] = (xz + wy);
        te[9] = (yz - wx);
        te[10] = (T(1) - (xx + yy));
        te[11] = T(0);

        te[12] = position.x;
        te[13] = position.y;
        te[14] = position.z;
        te[15] = T(1);

        return rMat;
    }
    /**
    * @brief 使用 旋转,轴向缩放 构造RS矩阵
    */
    static TMat4<T> Compose(const TQuat<T>& quaternion, const TVec3<T>& scale)
    {
        TMat4<T> rMat = TMat4<T>::Identity();

        T* te = rMat.data();

        const T x = quaternion.x;
        const T y = quaternion.y;
        const T z = quaternion.z;
        const T w = quaternion.w;

        const T x2 = x + x;   const T y2 = y + y;  const T z2 = z + z;
        const T xx = x * x2;  const T xy = x * y2; const T xz = x * z2;
        const T yy = y * y2;  const T yz = y * z2; const T zz = z * z2;
        const T wx = w * x2;  const T wy = w * y2; const T wz = w * z2;

        const T sx = scale.x;
        const T sy = scale.y;
        const T sz = scale.z;

        te[0] = (T(1) - (yy + zz)) * sx;
        te[1] = (xy + wz) * sx;
        te[2] = (xz - wy) * sx;
        te[3] = T(0);

        te[4] = (xy - wz) * sy;
        te[5] = (T(1) - (xx + zz)) * sy;
        te[6] = (yz + wx) * sy;
        te[7] = T(0);

        te[8] = (xz + wy) * sz;
        te[9] = (yz - wx) * sz;
        te[10] = (T(1) - (xx + yy)) * sz;
        te[11] = T(0);

        return rMat;
    }
    /**
    * @brief 使用 位置,轴向缩放 构造TS矩阵
    */
    static TMat4<T> Compose(const TVec3<T>& position, const TVec3<T>& scale)
    {
        TMat4<T> rMat = TMat4<T>::MakeScale(scale);
        rMat[3].setXyz(position);
        return rMat;
    }

    /**
    * @brief 萃取 TRS矩阵中的 位置，旋转，轴向缩放信息
    */
    static void Decompose(const TMat4<T>& mat, TVec3<T>& position, TQuat<T>& quaternion, TVec3<T>& scale)
    {
        const T* te = mat.data();

        T sx = mat[0].xyz().length();
        T sy = mat[1].xyz().length();
        T sz = mat[2].xyz().length();

        // if determine is negative, we need to invert one scale
        T det = mat.determinant();

        if (det < 0)
        {
            sx = -sx;
        }

        position.x = te[12];
        position.y = te[13];
        position.z = te[14];

        // scale the rotation part
        TMat4<T> tMat = mat;

        T invSX = T(1) / sx;
        T invSY = T(1) / sy;
        T invSZ = T(1) / sz;

        T* tMatData = tMat.data();

        tMatData[0] *= invSX;
        tMatData[1] *= invSX;
        tMatData[2] *= invSX;

        tMatData[4] *= invSY;
        tMatData[5] *= invSY;
        tMatData[6] *= invSY;

        tMatData[8] *= invSZ;
        tMatData[9] *= invSZ;
        tMatData[10] *= invSZ;

        quaternion = ToQuat(tMat);

        scale.x = sx;
        scale.y = sy;
        scale.z = sz;
    }
    /**
    * @brief 生成正交投影矩阵
    */
    static TMat4<T> MakeOrthographic(T left, T right, T top, T bottom, T zNear, T zFar)
    {
        TMat4<T> rMat = TMat4<T>::Identity();

        T* te = rMat.data();

        T w = T(1.0) / (right - left);
        T h = T(1.0) / (top - bottom);
        T p = T(1.0) / (zFar - zNear);

        T x = (right + left) * w;
        T y = (top + bottom) * h;
        T z = (zFar + zNear) * p;

        te[0] = T(2) * w;   te[4] = 0;	        te[8] = 0;          te[12] = -x;
        te[1] = 0;          te[5] = T(2) * h;   te[9] = 0;          te[13] = -y;
        te[2] = 0;          te[6] = 0;          te[10] = -T(2) * p;	te[14] = -z;
        te[3] = 0;          te[7] = 0;          te[11] = 0;         te[15] = 1;

        return rMat;
    }
    /**
    * @brief 生成透视投影矩阵
    */
    static TMat4<T> MakePerspective(T left, T right, T top, T bottom, T zNear, T zFar)
    {
        TMat4<T> rMat = TMat4<T>::Identity();

        T* te = rMat.data();

        T x = T(2) * zNear / (right - left);
        T y = T(2) * zNear / (top - bottom);

        T a = (right + left) / (right - left);
        T b = (top + bottom) / (top - bottom);
        T c = -(zFar + zNear) / (zFar - zNear);
        T d = -T(2) * zFar * zNear / (zFar - zNear);

        te[0] = x;	te[4] = 0;	te[8] = a;	    te[12] = 0;
        te[1] = 0;	te[5] = y;	te[9] = b;	    te[13] = 0;
        te[2] = 0;	te[6] = 0;	te[10] = c;	    te[14] = d;
        te[3] = 0;	te[7] = 0;	te[11] = -T(1);	te[15] = 0;

        return rMat;
    }

    /**
    * @brief look at
    */
    static TMat4<T> LookAt(const TVec3<T>& eye, const TVec3<T>& target, const TVec3<T>& up)
    {
        TMat4<T> rMat = TMat4<T>::Identity();

        T* te = rMat.data();

        TVec3<T> vz = eye - target;

        if (vz.lengthSq() == 0)
        {
            vz.z = T(1);
        }

        vz.normalize();
        TVec3<T> vx = TVec3<T>::Cross(up, vz);

        if (vx.lengthSq() == 0)
        {
            // up and z are parallel
            if (Abs(up.z) == T(1))
            {
                vz.x = vz.x + T(0.0001);
            }
            else
            {
                vz.z = vz.z + T(0.0001);
            }

            vz.normalize();
            vx = TVec3<T>::Cross(up, vz);
        }

        vx.normalize();
        TVec3<T> vy = TVec3<T>::Cross(vz, vx);

        te[0] = vx.x; te[4] = vy.x; te[8] = vz.x;
        te[1] = vx.y; te[5] = vy.y; te[9] = vz.y;
        te[2] = vx.z; te[6] = vy.z; te[10] = vz.z;

        return rMat;
    }
public:
    /**
    * @brief 转换到字符串
    */
    inline char* toString(char* buf) const
    {
        PrintArray2D<RowSize>(buf
            , _value[0][0], _value[0][1], _value[0][2], _value[0][3]
            , _value[1][0], _value[1][1], _value[1][2], _value[1][3]
            , _value[2][0], _value[2][1], _value[2][2], _value[2][3]
            , _value[3][0], _value[3][1], _value[3][2], _value[3][3]
        );
        return buf;
    }
    /**
    * @brief 转换到字符串
    */
    inline std::string toString() const
    {
        char buf[256] = { 0 };
        return this->toString(buf);
    }
    /**
    * @brief 从字符串转换
    */
    inline TMat4<T>& fromString(const char* str, bool* bOk = nullptr)
    {
        bool bRet = ScanArray2D<RowSize>(str
                , _value[0][0], _value[0][1], _value[0][2], _value[0][3]
                , _value[1][0], _value[1][1], _value[1][2], _value[1][3]
                , _value[2][0], _value[2][1], _value[2][2], _value[2][3]
                , _value[3][0], _value[3][1], _value[3][2], _value[3][3]
            );
        SetValueToBooleanPtr(bOk, bRet);
        return *this;
    }
    /**
    * @brief 从字符串转换
    */
    inline TMat4<T>& fromString(const std::string& str, bool* bOk = nullptr)
    {
        this->fromString(str.c_str(), bOk);
        return *this;
    }
    /**
    * @brief 从字符串转换
    */
    static inline TMat4<T> FromString(const char* str, bool* bOk = nullptr)
    {
        TMat4<T> r;
        r.fromString(str, bOk);
        return r;
    }
    /**
    * @brief 从字符串转换
    */
    static inline TMat4<T> FromString(const std::string& str, bool* bOk = nullptr)
    {
        return TMat4<T>::FromString(str.c_str(), bOk);
    }
};

/**
 * @brief 4x4矩阵数组
 * @tparam T 数值类型
*/
template <class T>
using TMat4Vector = std::vector<TMat4<T> >;
/**
 * @brief 4x4矩阵数组
 * @tparam T 数值类型
 * @tparam Size 数组大小
*/
template <class T, size_t Size>
using TMat4Array = std::array<TMat4<T>, Size>;

WD_NAMESPACE_END
