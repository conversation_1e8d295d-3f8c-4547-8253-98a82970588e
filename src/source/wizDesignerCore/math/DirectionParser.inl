#include "DirectionParser.h"
#include "TMat3.hpp"
#include "TPlane.hpp"
#include <regex>

WD_NAMESPACE_BEGIN

/**
 * @brief 轴解析帮助类
*/
class WD_API DirectionParserHelpter
{
public:
    /**
     * @brief 将轴向字符串分割成 {"轴","角度","轴", "角度", "轴"}样式的字符串数组
     *  例如
     *      " - X 45 Y 30 -Z", 将分为: {" - X"," 45"," Y"," 30"," -Z"}
     *      "X MAX(45, 30) - Y" 将分为: {"X", " MAX(45, 30)", " - Y"}
     * @param axisStr 轴向字符
     * @param axisWord 轴字母(XYZ还是ENU或者其他轴表示字母)
     * @return 分割结果, 只有vector的长度为1, 3, 5时，才表明正确分割出了结果
    */
    static std::vector<std::string> Split(const std::string& axisStr, const char* axisWord);
    /**
     * @brief 解析基础轴字符串
     * @param basicAxisStr 基础轴字符串
     * @param xName x轴名称
     * @param yName y轴名称
     * @param zName z轴名称
     * @param nxName -x轴名称
     * @param nyName -y轴名称
     * @param nzName -z轴名称
     * @return 如果是基础轴的字符串，则返回对应朝向，否则返回std::nullopt
    */
    static std::optional<TVec3<double> > BasicAxis(const std::string& basicAxisStr
        , const char* xName
        , const char* yName
        , const char* zName
        , const char* nxName
        , const char* nyName
        , const char* nzName);
    /**
     * @brief 将 Split方法的分割结果 计算成朝向
     * @param strs Split方法的分割结果
     * @param xName x轴名称
     * @param yName y轴名称
     * @param zName z轴名称
     * @param nxName -x轴名称
     * @param nyName -y轴名称
     * @param nzName -z轴名称
     * @return 如果有效，则返回对应朝向，否则返回std::nullopt
    */
    static std::optional<TVec3<double> > ToDirection(const std::vector<std::string>& strs
        , std::function<bool(const std::string& angleStr, double& outAngle)> calcAngle
        , const char* xName
        , const char* yName
        , const char* zName
        , const char* nxName
        , const char* nyName
        , const char* nzName);
    /**
     * @brief 角度输出为字符串
     * @param buf 输出的buf
     * @param bufSize buf大小
     * @param angle 角度值
     * @param decimal 小数点后保留多少位
    */
    static std::string AngleStrPrint(double angle
        , int decimal);
};

template<typename T, typename AxisLabelName>
bool TDirectionParser<T, AxisLabelName>::Direction(const std::string& axisStr
    , TVec3<T>& outDirection
    , const ConstantTable& constantTable)
{
    auto rStrs = DirectionParserHelpter::Split(axisStr, AxisLabelName::AxisWord());
    if (rStrs.empty())
        return false;

    auto rAxis = DirectionParserHelpter::ToDirection(rStrs
        , [&constantTable](const std::string& angleStr, double& outAngle)->bool
        {
            return ParseAngle(angleStr, constantTable, outAngle);
        }
        , AxisLabelName::X()
        , AxisLabelName::Y()
        , AxisLabelName::Z()
        , AxisLabelName::NX()
        , AxisLabelName::NY()
        , AxisLabelName::NZ());
    if (!rAxis)
        return false;

    outDirection = TVec3<T>(rAxis.value());

    return true;
}

template<typename T, typename AxisLabelName>
template<class ExpressObject>
bool TDirectionParser<T, AxisLabelName>::ExpressionDirection(const std::string& axisStr
    , TVec3<T>& outDirection
    , ExpressObject& eObject
    , ExpressFuncion<ExpressObject> eFunction)
{
    auto rStrs = DirectionParserHelpter::Split(axisStr, AxisLabelName::AxisWord());
    if (rStrs.empty())
        return false;

    auto rAxis = DirectionParserHelpter::ToDirection(rStrs
        , [&eObject, &eFunction](const std::string& angleStr, double& outAngle)->bool
        {
            T rAngle;
            if (!eFunction(angleStr, rAngle, eObject))
                return false;
            outAngle = static_cast<double>(rAngle);
            return true;
        }
        , AxisLabelName::X()
        , AxisLabelName::Y()
        , AxisLabelName::Z()
        , AxisLabelName::NX()
        , AxisLabelName::NY()
        , AxisLabelName::NZ());
    if (!rAxis)
        return false;

    outDirection = TVec3<T>(rAxis.value());

    return true;
}

template<typename T, typename AxisLabelName>
std::string TDirectionParser<T, AxisLabelName>::OutputStringByDirection(const TVec3<T>& dir, int decimal, T epsilon)
{
    const TVec3<T>& AxisX = TVec3<T>::AxisX();
    const TVec3<T>& AxisNX = TVec3<T>::AxisNX();
    const TVec3<T>& AxisY = TVec3<T>::AxisY();
    const TVec3<T>& AxisNY = TVec3<T>::AxisNY();
    const TVec3<T>& AxisZ = TVec3<T>::AxisZ();
    const TVec3<T>& AxisNZ = TVec3<T>::AxisNZ();

    static constexpr const T Zero = T(0);
    //首先判断是否是六个标准轴 X, -X, Y, -Y, Z, -Z
    if (TVec3<T>::InTheSameDirection(dir, AxisX, epsilon))
        return AxisLabelName::X();
    else if (TVec3<T>::InTheSameDirection(dir, AxisNX, epsilon))
        return AxisLabelName::NX();
    else if (TVec3<T>::InTheSameDirection(dir, AxisY, epsilon))
        return AxisLabelName::Y();
    else if (TVec3<T>::InTheSameDirection(dir, AxisNY, epsilon))
        return AxisLabelName::NY();
    else if (TVec3<T>::InTheSameDirection(dir, AxisZ, epsilon))
        return AxisLabelName::Z();
    else if (TVec3<T>::InTheSameDirection(dir, AxisNZ, epsilon))
        return  AxisLabelName::NZ();

    char buf[1024] = { 0 };
    T ang = Zero;
    std::string tAxies[2] = { AxisLabelName::X(), AxisLabelName::Y() };
    //再判断是否在某个标准平面上 XOY, XOZ, YOZ
    if (TVec3<T>::OnPrpendicular(dir, AxisZ, epsilon))
    {//说明在XOY平面上
        T dtX = TVec3<T>::Dot(dir, AxisX);
        T dtY = TVec3<T>::Dot(dir, AxisY);
        //第一象限
        if (dtX > Zero && dtY > Zero)
        {
            ang = TVec3<T>::Angle(dir, AxisX);
            tAxies[0] = AxisLabelName::X();
            tAxies[1] = AxisLabelName::Y();
        }
        //第二象限
        else if (dtX < Zero && dtY > Zero)
        {
            ang = TVec3<T>::Angle(AxisNX, dir);
            tAxies[0] = AxisLabelName::NX();
            tAxies[1] = AxisLabelName::Y();
        }
        //第三象限
        else if (dtX < Zero && dtY < Zero)
        {
            ang = TVec3<T>::Angle(dir, AxisNX);
            tAxies[0] = AxisLabelName::NX();
            tAxies[1] = AxisLabelName::NY();
        }
        //第四象限
        else if (dtX > Zero && dtY < Zero)
        {
            ang = TVec3<T>::Angle(AxisX, dir);
            tAxies[0] = AxisLabelName::X();
            tAxies[1] = AxisLabelName::NY();
        }
    }
    else if (TVec3<T>::OnPrpendicular(dir, AxisY, epsilon))
    {//说明在XOZ平面上
        T dtX = TVec3<T>::Dot(dir, AxisX);
        T dtZ = TVec3<T>::Dot(dir, AxisZ);
        //第一象限
        if (dtX > Zero && dtZ > Zero)
        {
            ang = TVec3<T>::Angle(dir, AxisX);
            tAxies[0] = AxisLabelName::X();
            tAxies[1] = AxisLabelName::Z();
        }
        //第二象限
        else if (dtX < Zero && dtZ > Zero)
        {
            ang = TVec3<T>::Angle(AxisNX, dir);
            tAxies[0] = AxisLabelName::NX();
            tAxies[1] = AxisLabelName::Z();
        }
        //第三象限
        else if (dtX < Zero && dtZ < Zero)
        {
            ang = TVec3<T>::Angle(dir, AxisNX);
            tAxies[0] = AxisLabelName::NX();
            tAxies[1] = AxisLabelName::NZ();
        }
        //第四象限
        else if (dtX > Zero && dtZ < Zero)
        {
            ang = TVec3<T>::Angle(AxisX, dir);
            tAxies[0] = AxisLabelName::X();
            tAxies[1] = AxisLabelName::NZ();
        }
    }
    else if (TVec3<T>::OnPrpendicular(dir, AxisX, epsilon))
    {//说明在YOZ平面上
        T dtY = TVec3<T>::Dot(dir, AxisY);
        T dtZ = TVec3<T>::Dot(dir, AxisZ);
        //第一象限
        if (dtY > Zero && dtZ > Zero)
        {
            ang = TVec3<T>::Angle(dir, AxisY);
            tAxies[0] = AxisLabelName::Y();
            tAxies[1] = AxisLabelName::Z();
        }
        //第二象限
        else if (dtY < Zero && dtZ > Zero)
        {
            ang = TVec3<T>::Angle(AxisNY, dir);
            tAxies[0] = AxisLabelName::NY();
            tAxies[1] = AxisLabelName::Z();
        }
        //第三象限
        else if (dtY < Zero && dtZ < Zero)
        {
            ang = TVec3<T>::Angle(dir, AxisNY);
            tAxies[0] = AxisLabelName::NY();
            tAxies[1] = AxisLabelName::NZ();
        }
        //第四象限
        else if (dtY > Zero && dtZ < Zero)
        {
            ang = TVec3<T>::Angle(AxisY, dir);
            tAxies[0] = AxisLabelName::Y();
            tAxies[1] = AxisLabelName::NZ();
        }
    }
    if (ang != Zero)
    {
        AxisStrPrint(buf, sizeof(buf), tAxies[0].c_str(), ang, tAxies[1].c_str(), decimal);
        return buf;
    }

    //不在标准轴上，且不在任何标准平面上
    TPlane<T>  plane(AxisZ, Zero);
    TVec3<T>   tDir = TVec3<T>::Normalize(plane.project(dir));

    T ang0 = Zero;
    T dtX = TVec3<T>::Dot(dir, AxisX);
    T dtY = TVec3<T>::Dot(dir, AxisY);
    //第一象限
    if (dtX > Zero && dtY > Zero)
    {
        ang0 = TVec3<T>::Angle(tDir, AxisX);
        tAxies[0] = AxisLabelName::X();
        tAxies[1] = AxisLabelName::Y();
    }
    //第二象限
    else if (dtX < Zero && dtY > Zero)
    {
        ang0 = TVec3<T>::Angle(AxisNX, tDir);
        tAxies[0] = AxisLabelName::NX();
        tAxies[1] = AxisLabelName::Y();
    }
    //第三象限
    else if (dtX < Zero && dtY < Zero)
    {
        ang0 = TVec3<T>::Angle(tDir, AxisNX);
        tAxies[0] = AxisLabelName::NX();
        tAxies[1] = AxisLabelName::NY();
    }
    //第四象限
    else if (dtX > Zero && dtY < Zero)
    {
        ang0 = TVec3<T>::Angle(AxisX, tDir);
        tAxies[0] = AxisLabelName::X();
        tAxies[1] = AxisLabelName::NY();
    }
    T ang1 = TVec3<T>::Angle(dir, tDir);
    if (TVec3<T>::Dot(AxisZ, dir) < Zero)
    {
        ang1 = -ang1;
    }

    AxisStrPrint(buf, sizeof(buf), tAxies[0].c_str(), ang0, tAxies[1].c_str(), ang1, decimal);

    return buf;
}

template<typename T, typename AxisLabelName>
bool TDirectionParser<T, AxisLabelName>::ParseAngle(const std::string& valueStr
    , const ConstantTable& cTable
    , double& outAngle)
{
    //先从常量表中查值
    auto fItr = cTable.find(valueStr);
    if (fItr != cTable.end())
    {
        outAngle = fItr->second;
        return true;
    }
    //常量表中不存在,可能是数值,直接转换
    double tAngle = 0.0;
    int r = sscanf(valueStr.c_str(), "%lf", &tAngle);
    if (r != 1)
        return false;

    outAngle = tAngle;
    return true;
}

template<typename T, typename AxisLabelName>
void TDirectionParser<T, AxisLabelName>::AxisStrPrint(char* buf
    , size_t bufSize
    , const char* left
    , T angle
    , const char* right
    , int decimal)
{
    // 角度转为字符串
    std::string rAngle = DirectionParserHelpter::AngleStrPrint(static_cast<double>(angle), decimal);

    bool bAngleIsZero = rAngle == "0" || rAngle == "-0";
    if (!bAngleIsZero)
        sprintf_s(buf, bufSize, "%s %s %s", left, rAngle.c_str(), right);
    else
        sprintf_s(buf, bufSize, "%s", left);
}

template<typename T, typename AxisLabelName>
void TDirectionParser<T, AxisLabelName>::AxisStrPrint(char* buf
    , size_t bufSize
    , const char* left
    , T angle0
    , const char* right
    , T angle1
    , int decimal)
{
    // 角度转为字符串
    std::string rAngle0 = DirectionParserHelpter::AngleStrPrint(static_cast<double>(angle0), decimal);
    std::string rAngle1 = DirectionParserHelpter::AngleStrPrint(static_cast<double>(angle1), decimal);
    // 输出结果
    bool bAngle0IsZero = rAngle0 == "0" || rAngle0 == "-0";
    bool bAngle1IsZero = rAngle1 == "0" || rAngle1 == "-0";
    if (!bAngle0IsZero && !bAngle1IsZero)
        sprintf_s(buf, bufSize, "%s %s %s %s %s", left, rAngle0.c_str(), right, rAngle1.c_str(), AxisLabelName::Z());
    else if (!bAngle0IsZero)
        sprintf_s(buf, bufSize, "%s %s %s", left, rAngle0.c_str(), right);
    else if (!bAngle1IsZero)
        sprintf_s(buf, bufSize, "%s %s %s", left, rAngle1.c_str(), AxisLabelName::Z());
    else
        sprintf_s(buf, bufSize, "%s", left);
}


WD_NAMESPACE_END

