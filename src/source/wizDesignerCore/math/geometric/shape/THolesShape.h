#pragma once

#include "../curve/TPathCurve2.h"
#include "../../TPolygon2.hpp"
#include "../../TEarcut.hpp"

WD_NAMESPACE_BEGIN

/**
* @brief 二维形状,带孔洞
*/
template <class T>
class THolesShape
{
public:
    using _This     = THolesShape<T>;
    using Path      = TPathCurve2<T>;
    using Paths     = std::vector<Path>;

    using Point     = typename Path::Point;
    using Points    = typename Path::Points;
public:
    // 形状的外轮廓
    Path  contour;
    // 孔洞
    Paths holes;
public:
    THolesShape(const Path& contour = Path(), const Paths& holes = Paths());
public:
    /**
    * @brief 获取所有顶点列表
    * @return 形状的所有顶点(包括孔洞的顶点)
    *   outPts[0] 当前形状顶点列表
    *   outPts[1 ~ n] 当前形状所包含的孔洞顶点列表
    */
    std::vector<Points> extractPoints() const;
    /**
     * @brief 三角化，返回三角面的索引
     * @param outVeritices 输出的顶点列表
     * @param outIndices 输出的索引列表
    */
    bool triangulate(Points& outVeritices, std::vector<uint>& outIndices) const;
private:
    // 判断轮廓是否顺时针排列
    static bool IsClockWise(const Points& pts);
    // 剔除尾部与首部重复的点
    static void RemoveDupEndPts(Points& pts);
};

WD_NAMESPACE_END

#include "THolesShape.inl"
