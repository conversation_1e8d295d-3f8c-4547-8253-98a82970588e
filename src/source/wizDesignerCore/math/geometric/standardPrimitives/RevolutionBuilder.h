#pragma once

#include "StandardPrimitiveCommon.h"

WD_NAMESPACE_BEGIN

/**
* @brief 构造旋转体
* !TODO: 待实现
*/
class WD_API RevolutionBuilder
{
public:
    /**
    * @brief 生成网格 REVO
    * @param center 旋转中心点, !注意: 这里旋转中心点内部不再使用，而是使用默认值: FVec3::Zero()
    * @param axis 旋转轴, !注意: 这里旋转轴内部不再使用，而是使用默认值: FVec3::AxisX()
    * @param angle 旋转角度,角度制(取值范围为(0.0, 360.0])
    * @param loop 旋转面顶点数组
    *   其中:
    *       loop[i].x:表示顶点位置的x分量
    *       loop[i].y:表示顶点位置的y分量
    *       loop[i].z:表示顶点的圆角半径
    */
    static MeshStruct Mesh(const FVec3& center
        , const FVec3& axis
        , float angle
        , const FVec3Vector& loop
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
    * @brief 生成关键点列表 REVO
    * @param center 旋转中心点, !注意: 这里旋转中心点内部不再使用，而是使用默认值: FVec3::Zero()
    * @param axis 旋转轴, !注意: 这里旋转轴内部不再使用，而是使用默认值: FVec3::AxisX()
    * @param angle 旋转角度,角度制(取值范围为(0.0, 360.0])
    * @param loop 旋转面顶点数组
    */
    static FKeyPoints KeyPoints(const FVec3& center
        , const FVec3& axis
        , float angle
        , const FVec3Vector& loop);
    /**
    * @brief 生成交点列表 REVO
    * @param center 旋转中心点, !注意: 这里旋转中心点内部不再使用，而是使用默认值: FVec3::Zero()
    * @param axis 旋转轴, !注意: 这里旋转轴内部不再使用，而是使用默认值: FVec3::AxisX()
    * @param angle 旋转角度,角度制(取值范围为(0.0, 360.0])
    * @param loop 旋转面顶点数组
    */
    static FVec3Vector IntersectPoints(const FVec3& center
        , const FVec3& axis
        , float angle
        , const FVec3Vector& loop);
    /**
     * @brief 转换 SREV参数 到 REVO参数
     * @param paax       SREV的X轴方向
     * @param paaxPos    SREV的X轴偏移
     * @param pbax       SREV的Y轴方向
     * @param pang       旋转角度
     * @param px         中心点的x坐标值
     * @param py         中心点的y坐标值
     * @param pz         中心点的z坐标值
     * @param vertexesData  顶点数据
     *   其中
     *       vertexesData[i].x:表示顶点位置的x分量 px
     *       vertexesData[i].y:表示顶点位置的y分量 py
     *       vertexesData[i].z:表示顶点的圆角半径  prad
     * @param outTransform 输出的变换信息
     * @param center 输出的旋转中心点, !注意: 这里旋转中心点内部不再使用，而是使用默认值: FVec3::Zero()
     * @param axis 输出的旋转轴, !注意: 这里旋转轴内部不再使用，而是使用默认值: FVec3::AxisX()
     * @param angle 输出的旋转角度,角度制(取值范围为(0.0, 360.0])
     * @param loop 输出的旋转面顶点数组
     *   其中:
     *       loop[i].x:表示顶点位置的x分量
     *       loop[i].y:表示顶点位置的y分量
     *       loop[i].z:表示顶点的圆角半径
     * @return 是否转换成功(当参数无效时,将转换失败)
    */
    using VertexDatas = FVec3Vector;
    static bool SREVToREVO(const FVec3& paax
        , const FVec3& paaxPos
        , const FVec3& pbax
        , const float& pang
        , const float& px
        , const float& py
        , const float& pz
        , const VertexDatas& vertexDatas
        , FMeshTransform& outTransform
        , FVec3& outCenter
        , FVec3& outAxis
        , float& outAngle
        , FVec3Vector& outLoop);
    /**
    * @brief 生成边线 REVO
    * @param center 旋转中心点
    * @param axis 旋转轴
    * @param angle 旋转角度,角度制(取值范围为(0.0, 360.0])
    * @param loop 旋转面顶点数组
    */
    static MeshStruct SideLines(const FVec3& center
        , const FVec3& axis
        , float angle
        , const FVec3Vector& loop
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
    * @brief 生成网格 REVO
    *   简单网格，顶点只包含位置信息，不包含任何法线、UV相关信息，目的是保证顶点位置没有重合
    * @param center 旋转中心点, !注意: 这里旋转中心点内部不再使用，而是使用默认值: FVec3::Zero()
    * @param axis 旋转轴, !注意: 这里旋转轴内部不再使用，而是使用默认值: FVec3::AxisX()
    * @param angle 旋转角度,角度制(取值范围为(0.0, 360.0])
    * @param loop 旋转面顶点数组
    *   其中:
    *       loop[i].x:表示顶点位置的x分量
    *       loop[i].y:表示顶点位置的y分量
    *       loop[i].z:表示顶点的圆角半径
    */
    static MeshStruct SimpleMesh(const FVec3& center
        , const FVec3& axis
        , float angle
        , const FVec3Vector& loop
        , const MeshLODSelection& lodSelection = MeshLODSelection());
};

WD_NAMESPACE_END

