
#include "ModelObject.h"

namespace primitive
{
	CModelObject::CModelObject(void)
	{
		m_id = -1;
		m_pId = -1;
		m_isParent = false;
		m_name = "";
		m_children.clear();
	}

	CModelObject::~CModelObject()
	{
		m_name = "";
		for (size_t i = 0; i < m_children.size(); ++i)
		{
			delete m_children[i];
			m_children[i] = NULL;
		}
		m_children.clear();
	}

	CModelObjectManager::CModelObjectManager(void)
		:m_modelRoot(NULL)
		,m_regex("/")
	{
	}

	CModelObjectManager::~CModelObjectManager(void)
	{
		m_modelRoot = NULL;
	}

	// 获取工程对象 
	CModelObject* CModelObjectManager::GetRegexModelObject(const std::string& name)
	{
		if (name.substr(0, m_regex.length()) != m_regex)
		{
			if (name == GetRoot()->GetName())
			{
				return GetModelObjectByName(name);
			}

			CModelObject* pObj = GetModelObjectByName(name);
			if (pObj == NULL)
			{
				return NULL;
			}

			return GetRegexModelObject(pObj->GetParent()->GetName());
		}

		return GetModelObjectByName(name);
	}

	// 获取叶子节点
	void CModelObjectManager::GetModelObjectList(std::vector<CModelObject*> &result, const CModelObject* pObj)
	{
		if (!pObj->HasChild())
		{
			result.push_back(const_cast<CModelObject*>(pObj));
			return;
		}

		for (int i = 0; i < pObj->Count(); i++)
		{
			GetModelObjectList(result, pObj->Children()[i]);
		}
	}

	// 获取叶子节点
	const void CModelObjectManager::GetModelObjectList(std::vector<CModelObject*> &result, const std::string& name) const
	{
		CModelObject* pObj = (const_cast<CModelObjectManager*>(this))->GetRegexModelObject(name);
		if (pObj != NULL) 
		{
			(const_cast<CModelObjectManager*>(this))->GetModelObjectList(result, pObj);
		}
	}

	void CModelObjectManager::GetModelObjectPath(std::stack<CModelObject*> &result, const CModelObject* pObj)
	{
		if (NULL == pObj)
		{
			return;
		}

		result.push(const_cast<CModelObject*>(pObj));
		pObj = pObj->GetParent();
		GetModelObjectPath(result, pObj);
	}

	// 获取节点路径 
	const void CModelObjectManager::GetModelObjectPath(std::stack<CModelObject*> &result, const std::string& name) const
	{
		(const_cast<CModelObjectManager*>(this))->GetModelObjectPath(result, (const_cast<CModelObjectManager*>(this))->GetRegexModelObject(name));
	}
}





