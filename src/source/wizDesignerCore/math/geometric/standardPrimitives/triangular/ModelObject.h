#ifndef _MODELOBJECT_H_
#define _MODELOBJECT_H_
#include <string>
#include <vector>
#include <stack>
#include <map>

#ifdef LINUX
#include <iostream>
#include <cstring>
#endif

namespace primitive
{
	class CModelObject
	{
	public:
		CModelObject(void);
		~CModelObject(void);

		void SetName(std::string name)
		{
			m_name = name;
		}

		const std::string GetName() const
		{
			return m_name;
		}

		void SetParent(CModelObject* pObj)
		{
			m_parent = pObj;
		}

		const CModelObject* GetParent() const
		{
			return m_parent;
		}

		void AddChild(CModelObject* pObj)
		{
			m_children.push_back(pObj);
		}

		bool HasChild() const
		{
			return m_children.size() > 0;
		}

		size_t Count() const
		{
			return m_children.size();
		}

        size_t Size() const
		{
			return m_name.size();
		}

		const std::vector<CModelObject*>& Children() const
		{
			return m_children;
		}

		// 系列化对象名称，用于处理点选 
		int Serialize(char *buf) const
		{
			int offset = 0;
			//name
			int nameSize = (int)m_name.size();
			memcpy(buf + offset, (void*)&nameSize,sizeof(int));
			offset += sizeof(int);
			memcpy(buf + offset, m_name.c_str(),m_name.size());
			offset += (int)m_name.size();
			
			return offset;
		}

		// 系列化动态树节点，添加子节点个数
		int SerializeTreeNode(char *buf) const
		{
			int offset = 0;
			//name
			int nameSize = (int)m_name.size();
			memcpy(buf + offset, (void*)&nameSize,sizeof(int));
			offset += sizeof(int);
			memcpy(buf + offset, m_name.c_str(),m_name.size());
			offset += (int)m_name.size();

			// children count 
			int childCount = (int)m_children.size();
			memcpy(buf + offset, (void*)&childCount,sizeof(int));
			offset += sizeof(int);

			return offset;
		}

	public:
		int m_id;
		int m_pId;
		bool m_isParent;
		std::string m_name;
		std::vector<CModelObject*> m_children;
		CModelObject* m_parent;
	};

	class CModelObjectManager
	{
	public:
		CModelObjectManager(void);
		~CModelObjectManager(void);

		void SetRoot(CModelObject* pObj)
		{
			m_modelRoot = pObj;
		}

		void SetRegex(std::string regex)
		{
			m_regex = regex;
		}

		const CModelObject* GetRoot() const
		{
			return m_modelRoot;
		}

		bool SetModelObjectMap(const std::string& name, CModelObject* &pModelObj)
		{
			std::map<std::string, CModelObject*>::iterator iter = m_modelObjMap.find(name);
			if (iter == m_modelObjMap.end())
			{
				m_modelObjMap[name] = pModelObj;
				return true;
			}

			return false;
		}

		const CModelObject* GetModelObj(const std::string& name) const 
		{ 
			return (const_cast<CModelObjectManager*>(this))->GetModelObjectByName(name);
		}

		CModelObject* GetModelObjectByName(const std::string& name)
		{
			std::map<std::string, CModelObject*>::iterator iter = m_modelObjMap.find(name);
			if (iter != m_modelObjMap.end())
			{
				return m_modelObjMap[name];
			}

			return NULL;
		}

		CModelObject* GetRegexModelObject(const std::string& name);

		void GetModelObjectList(std::vector<CModelObject*> &result, const CModelObject* pObj);
		const void GetModelObjectList(std::vector<CModelObject*> &result, const std::string& name) const;

		void GetModelObjectPath(std::stack<CModelObject*> &result, const CModelObject* pObj);
		const void GetModelObjectPath(std::stack<CModelObject*> &result, const std::string& name) const;

	private:
		CModelObject* m_modelRoot;
		std::map<std::string, CModelObject*> m_modelObjMap;
		std::string m_regex;
	};
}

#endif 

