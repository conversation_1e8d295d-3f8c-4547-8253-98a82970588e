
#include "DishTriangular.h"
#include "TriangularUtil.h"
namespace tri
{
	CDishTriangular::CDishTriangular(void):	
		m_gama(PI * 0.5f)
	{
		m_invR.Identity();
	}


	CDishTriangular::~CDishTriangular(void)
	{
	}

	bool CDishTriangular::SetParam(const std::vector<FLOAT3D> &parameters)
	{
		if (parameters.size() == 3)
		{
			m_a = parameters[0];
			m_b = parameters[1];
			m_c = parameters[2];
		} else if (parameters.size() == 2)
		{
			m_a = parameters[0];
			m_b = parameters[0];
			m_c = parameters[1];
		} else {
			return false;
		}


		//check parameter
		if (m_a <= EPSILON || m_b <= EPSILON || m_c <= EPSILON)
		{
			return false;
		}

		m_invR.SetRow(0, m_a, 0.0f, 0.0f);
		m_invR.SetRow(1, 0.0f, m_b, 0.0f);
		m_invR.SetRow(2, 0.0f, 0.0f, m_c);
		m_invR = m_invR.Inv().Transpose();
		return true;
	}

	// alpha: 球坐标角, 与Z轴的夹角 
	// beta: 经度
	FLOAT3D3 CDishTriangular::GetVertex(FLOAT3D uw, FLOAT3D vw)
	{
		FLOAT3D sinAlpha = (float)sin(_GetU(uw));
		FLOAT3D cosAlpha = (float)cos(_GetU(uw));
		FLOAT3D sinBeta = (float)sin(_GetV(vw));
		FLOAT3D cosBeta = (float)cos(_GetV(vw));
		return FLOAT3D3(m_a * sinAlpha * cosBeta, m_b * sinAlpha * sinBeta, m_c * cosAlpha);
	}
	FLOAT3D3 CDishTriangular::GetNormal(FLOAT3D3 /*vertex*/, FLOAT3D uw, FLOAT3D vw)
	{
		FLOAT3D sinAlpha = (float)sin(_GetU(uw));
		FLOAT3D cosAlpha = (float)cos(_GetU(uw));
		FLOAT3D sinBeta = (float)sin(_GetV(vw));
		FLOAT3D cosBeta = (float)cos(_GetV(vw));
		FLOAT3D3 normal(sinAlpha * cosBeta, sinAlpha * sinBeta, cosAlpha);
        //!Liuerning 2023-02-20 这里之前返回为 矩阵的逆*法线，导致浮点位精度不够，圆盘顶部的法线都为(0,0,1)
        //这里注释之前的方式（暂时不理解为什么要这么乘），直接对结果法线单位化
        //return m_invR.mul(normal);
        normal.Normalize();
        return normal;
	}

	FLOAT3D3 CDishTriangular::GetBallCap()
	{
		return FLOAT3D3(0.0f, 0.0f, m_c);
	}

	bool CDishTriangular::MeshUnderSurface(int /*u_seg*/, int v_seg)
	{
		Matrix33 R;
		R.Identity();
		R.Set(0, 0, m_a);
		R.Set(1, 1, m_b);
		int vIdx = GetVertexIdx();
        int vidx = vIdx;
		int faceIdx = GetFaceIdx();
		bool ret = CTriangularUtil::MeshDish(v_seg, 1.0f, true, R, FLOAT3D3(0.0f, 0.0f, 0.0f),
			*GetMeshObj(), vIdx, faceIdx);
		SetVertexIdx(vIdx);
		SetFaceIdx(faceIdx);
        vidx += 1;
        int edgeIndex = 0;
        for (int i = 0; i < v_seg; ++i)
        {
            m_pMesh->pEdge[edgeIndex++] = vidx + i;
            m_pMesh->pEdge[edgeIndex++] = vidx + ((i + 1) % v_seg);
        }
        assert(edgeIndex == m_pMesh->edgeCount * 2);
		return ret;
	}
}//end of namespace tri

