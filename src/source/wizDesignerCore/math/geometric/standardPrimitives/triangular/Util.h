#ifndef _UTIL_H_
#define _UTIL_H_
#include <string>
#include <vector>
#include "BasicDef.h"
class Util
{
public:
	Util(void);
	~Util(void);
	static std::string toLower(const char* str);
	static std::string toUpper(const char* str);
	static std::string Trim(const std::string& str);
	static std::string Replace(std::string& str, const char* str1, const char* str2);
	static void split(const std::string &str, char delim, std::vector<std::string> &result);
	static void split(const std::string &str, char delim, std::vector<FLOAT3D> &result);
	static void split(const std::string &str, char delim, std::vector<int> &result);
	static int ParserToken(const std::string& line, std::string& token, int& tokenStart);
	static int ParserToken(const std::string& line, std::string& token, std::string& rightStr, std::string& leftStr);
	static bool Punctuation(char value);
    static void FindFile(std::vector<std::string>& arr, const char* path, const char* suffix, bool subdir = true);
    static bool strEndWidth(const char *str, char endCh);
};
#endif
