#ifndef _TRIANGULAR_H_
#define _TRIANGULAR_H_
#include <vector>
#include "BasicDef.h"
#include "MeshObj.h"

namespace tri
{
	class CTriangular
	{
	protected:
		primitive::CMeshObj *m_pMesh;
		int m_vertexIdx;
		int m_faceIdx;
	public: 
		CTriangular() : m_pMesh(NULL), m_vertexIdx(0), m_faceIdx(0)
        {
        }
		~CTriangular() {}

		//triangular
		virtual bool SetParam(const std::vector<FLOAT3D> &/*parameters*/) = 0;
        virtual bool Mesh(const std::vector<unsigned int> &/*segments*/, bool /*isClose*/) 
        {
            return  false;
        };
		//
		void SetMeshObj(primitive::CMeshObj *pMesh) 
		{
			m_pMesh = pMesh;
			m_vertexIdx = 0;
			m_faceIdx = 0;
		}
		primitive::CMeshObj* GetMeshObj()
		{
			return m_pMesh;
		}
		const primitive::CMeshObj* GetMeshObj() const
		{
			return m_pMesh;
		}

		void Print(const char* filename);

	protected:
		int GetVertexIdx() const 
		{
			return m_vertexIdx;
		}
		void SetVertexIdx(int idx) 
		{
			m_vertexIdx = idx;
		}

		int GetFaceIdx() const 
		{
			return m_faceIdx;
		}
		void SetFaceIdx(int idx) 
		{
			m_faceIdx = idx;
		}
	};
}

#endif // 
