#include "CapBaseTriangular.h"
#include "BasicDef.h"
#include "TriangularUtil.h"
namespace tri
{
	CCapBaseTriangular::CCapBaseTriangular(void)
	{
	}


	CCapBaseTriangular::~CCapBaseTriangular(void)
	{
	}
	bool CCapBaseTriangular::Mesh(const std::vector<unsigned int> &segments, bool isClose)
	{
		if (segments.size() != 2)
		{
			return false;
		}
		UInt32 u_seg = segments[0];
		UInt32 v_seg = segments[1];
		if (u_seg <= 0 || v_seg <= 2)
		{
			return false;
		}
		// u_set >= 1 && v_seg >= 2
		UInt32 vertex_add_count = 0;
		UInt32 face_add_count = 0;
		if (isClose)
		{
			vertex_add_count = v_seg + 1;
			face_add_count = v_seg;
		}
		
		InitMesh(*GetMeshObj()
            , u_seg * v_seg + 1 + vertex_add_count
            , (u_seg - 1) * v_seg * 2 + v_seg + face_add_count
            , v_seg
            , (u_seg + 1) * 4);

		if (!BeforeMesh((int)u_seg, (int)v_seg))
		{
			return false;
		}
		return _DoMesh((int)u_seg, (int)v_seg, isClose);
	}

	// u: 纬度
	// v: 经度
	bool CCapBaseTriangular::_DoMesh(int u_seg, int v_seg, bool isClose)
	{
		// 三角化球冠
		bool ret = MeshBall(u_seg, v_seg, GetBallCap());
		if (ret == false)
		{
			return false;
		}
		//三角化底面
		if (isClose)
		{
			return MeshUnderSurface(u_seg, v_seg);
		}
		return true;
	}

	bool CCapBaseTriangular::MeshBall(int u_seg, int v_seg, FLOAT3D3 ballCap)
	{
		int vertexStartIdx = GetVertexIdx();
		int faceStartIdx = GetFaceIdx();
		primitive::CMeshObj *pMesh = GetMeshObj();
		//formula : [x,y,z] = O + M * [m_R * sin(alpha) * cos(beta), m_R * sin(alpha) * sin(beta), m_R * cos(alpha) + m_h - m_R]
		//1. 生产顶点和法向
		
		pMesh->pVertex[vertexStartIdx] = ballCap;//球顶， 顶点共享， 只生成一个
		pMesh->pNormal[vertexStartIdx] = GetNormal(ballCap, 0.0f, 0.0f);
		pMesh->pNormal[vertexStartIdx].Normalize();
		int vIdx = vertexStartIdx + 1;
		FLOAT3D uw = 0.0f;
        m_sideEdgeIndex0 = 0;
        m_sideEdgeIndex1 = 0;
        m_sideEdgeIndex2 = 0;
        m_sideEdgeIndex3 = 0;
		for (int u = 1; u <= u_seg; ++u)
		{
			uw = (FLOAT3D)u / (FLOAT3D)(u_seg);
			if (u == u_seg)
			{
				uw = 1.0f;
			}
			FLOAT3D vw = 0.0f;
			for (int v = 0; v < v_seg; ++v)
			{
				vw = (FLOAT3D)(v) / (FLOAT3D)(v_seg);
				FLOAT3D3 vertex = GetVertex(uw, vw);
				pMesh->pVertex[vIdx] = vertex;
				pMesh->pNormal[vIdx] = GetNormal(vertex, uw, vw);
                // 生成侧边线index
                if (v == 0)
                {
                    int tmpSIndex0 = 0;//(u_seg + 1) * 2 * 0;
                    if (u == 1) // 每条侧线都以顶点0为起点
                    {
                        m_pMesh->pSideEdge[tmpSIndex0 + m_sideEdgeIndex0] = 0;
                    }
                    else
                    {
                        m_pMesh->pSideEdge[tmpSIndex0 + m_sideEdgeIndex0] = (u - 2) * v_seg + v + 1;
                    }
                    m_sideEdgeIndex0++;
                    m_pMesh->pSideEdge[tmpSIndex0 + m_sideEdgeIndex0] = (u - 1) * v_seg + v + 1;
                    m_sideEdgeIndex0++;
                }
                else if (v == v_seg / 4)
                {
                    int tmpSIndex1 = (u_seg + 1) * 2 * 1;
                    if (u == 1) // 每条侧线都以顶点0为起点
                    {
                        m_pMesh->pSideEdge[tmpSIndex1 + m_sideEdgeIndex1] = 0;
                    }
                    else
                    {
                        m_pMesh->pSideEdge[tmpSIndex1  + m_sideEdgeIndex1] = (u - 2) * v_seg + v + 1;
                    }
                    m_sideEdgeIndex1++;
                    m_pMesh->pSideEdge[tmpSIndex1 + m_sideEdgeIndex1] = (u - 1) * v_seg + v + 1;
                    m_sideEdgeIndex1++;
                }
                else if (v ==  (2 * v_seg) / 4)
                {
                    int tmpSIndex2 = (u_seg + 1) * 2 * 2;
                    if (u == 1) // 每条侧线都以顶点0为起点
                    {
                        m_pMesh->pSideEdge[tmpSIndex2 + m_sideEdgeIndex2] = 0;
                    }
                    else
                    {
                        m_pMesh->pSideEdge[tmpSIndex2 + m_sideEdgeIndex2] = (u - 2) * v_seg + v + 1;
                    }
                    m_sideEdgeIndex2++;
                    m_pMesh->pSideEdge[tmpSIndex2 + m_sideEdgeIndex2] = (u - 1) * v_seg + v + 1;
                    m_sideEdgeIndex2++;
                }
                else if (v == (3 * v_seg) / 4)
                {
                    int tmpSIndex3 = (u_seg + 1) * 2 * 3;
                    if (u == 1) // 每条侧线都以顶点0为起点
                    {
                        m_pMesh->pSideEdge[tmpSIndex3 + m_sideEdgeIndex3] = 0;
                    }
                    else
                    {
                        m_pMesh->pSideEdge[tmpSIndex3 + m_sideEdgeIndex3] = (u - 2) * v_seg + v + 1;
                    }
                    m_sideEdgeIndex3++;
                    m_pMesh->pSideEdge[tmpSIndex3 + m_sideEdgeIndex3] = (u - 1) * v_seg + v + 1;
                    m_sideEdgeIndex3++;
                }

				vIdx++;
			} // end of 'for (UInt32 v = 0; v <= v_seg; ++v)'

		}// end of 'for (UInt32 u = 0; u < u_seg; ++u)'
		SetVertexIdx(vIdx);

		//2.生成面片
		int faceIdx = faceStartIdx;
		//2.1 球冠
		for (int v = 0; v < v_seg; ++v)
		{
			int p1 = v;
			int p2 = p1 + 1;
			if (p2 >= v_seg)
			{
				p2 = 0;
			}
			p1 = p1 + vertexStartIdx + 1;
			p2 = p2 + vertexStartIdx + 1;
			pMesh->pFace[faceIdx++] = vertexStartIdx;
			pMesh->pFace[faceIdx++] = p1;
			pMesh->pFace[faceIdx++] = p2;
		}
		//2.2 剩余部分. 注意, 不要随便修改 faceIdx 的值
		for (int u = 2; u <= u_seg; ++u)
		{
			int u1 = u - 1;
			int u2 = u;
			for (int v = 1; v <= v_seg; ++v)
			{
				int v1 = v - 1;
				int v2 = v;
				if (v2 == v_seg)
				{// 因为是圆， 最后一点和最开始一点重合，所以可以共享
					v2 = 0;
				}

				int p1 = (u1 - 1) * v_seg + v1 + vertexStartIdx + 1;
				int p2 = (u1 - 1) * v_seg + v2 + vertexStartIdx + 1;
				int p3 = (u2 - 1) * v_seg + v1 + vertexStartIdx + 1;
				int p4 = (u2 - 1) * v_seg + v2 + vertexStartIdx + 1;

				pMesh->pFace[faceIdx++] = p1;
				pMesh->pFace[faceIdx++] = p3;
				pMesh->pFace[faceIdx++] = p4;

				pMesh->pFace[faceIdx++] = p1;
				pMesh->pFace[faceIdx++] = p4;
				pMesh->pFace[faceIdx++] = p2;
			}// end of for (UInt32 v = 1; v <= v_seg; ++v){
		}// end of for (UInt32 u = 2; u <= u_seg; ++u){
		SetFaceIdx(faceIdx);
		return true;
	}

	bool CCapBaseTriangular::MeshUnderSurface(int /*u_seg*/, int v_seg)
	{
		int vIdx = GetVertexIdx();
        int vidx = vIdx;
		int faceIdx = GetFaceIdx();
		Matrix33 R;
		R.Identity();
		bool ret = CTriangularUtil::MeshDish(v_seg, GetUnderFaceRadius(), true, R, FLOAT3D3(0.0f, 0.0f, 0.0f),
			*(GetMeshObj()), vIdx, faceIdx);
		SetVertexIdx(vIdx);
		SetFaceIdx(faceIdx);
        vidx += 1;
        int edgeIndex = 0;
        for (int i = 0; i < v_seg; ++i)
        {
            m_pMesh->pEdge[edgeIndex++] = vidx + i;
            m_pMesh->pEdge[edgeIndex++] = vidx + ((i + 1) % v_seg);
        }
        assert(edgeIndex == m_pMesh->edgeCount * 2);
		return ret;
	}
}

