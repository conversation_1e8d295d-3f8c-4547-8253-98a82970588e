
#include "RotaterBaseTriangular.h"
#include "TriangularUtil.h"
namespace tri
{
	CRotaterBaseTriangular::CRotaterBaseTriangular(void)
	{
	}


	CRotaterBaseTriangular::~CRotaterBaseTriangular(void)
	{
	}

	bool CRotaterBaseTriangular::Mesh(const std::vector<unsigned int> &segments, bool isClose)
	{
		if (segments.size() != 2)
		{
			return false;
		}
		int u_seg = (int)segments[0];
		int v_seg = (int)segments[1];
		if (u_seg <= 0 || v_seg <= 2)
		{
			return false;
		}

		return DoMesh(u_seg, v_seg, isClose);
	}

	// u: 纬度
	// v: 经度
	bool CRotaterBaseTriangular::DoMesh(int u_seg, int v_seg, bool isClose)
	{
		// u_set >= 1 && v_seg >= 2
		int vertex_add_count = 0;
		int face_add_count = 0;
        int edgeCount = 0;
		if (isClose)
		{
			vertex_add_count = ((int)v_seg + 1) * 2;
			face_add_count = (int)v_seg * 2;
            edgeCount = (int)v_seg * 2;
		}
		InitMesh(*GetMeshObj()
            , (u_seg + 1) * v_seg + vertex_add_count
            , u_seg * v_seg * 2 + face_add_count
            , edgeCount
            , u_seg * 4);
		// 三角化侧面
		bool ret = MeshBall(u_seg, v_seg);
		if (ret == false)
		{
			return false;
		}
		//三角化底面
		if (isClose)
		{
			return MeshUnderSurface(v_seg);
		}
		return true;
	}

	bool CRotaterBaseTriangular::MeshBall(int u_seg, int v_seg)
	{
		int vertexStartIdx = GetVertexIdx();
		int faceStartIdx = GetFaceIdx();
		primitive::CMeshObj *pMesh = GetMeshObj();
		//formula : [x,y,z] = O + M * [m_R * sin(alpha) * cos(beta), m_R * sin(alpha) * sin(beta), m_R * cos(alpha) + m_h - m_R]
		//1. 生成顶点和法向
		int vIdx = vertexStartIdx;
		FLOAT3D uw = 0.0f;
        int     index0 = 0;
        int     index1 = 0;
        int     index2 = 0;
        int     index3 = 0;
		for (int u = 0; u <= u_seg; ++u)
		{
			uw = (FLOAT3D)u / (FLOAT3D)(u_seg);
			if (u == u_seg)
			{
				uw = 1.0f;
			}
			FLOAT3D vw = 0.0f;
			for (int v = 0; v < v_seg; ++v)
			{
				vw = (FLOAT3D)(v) / (FLOAT3D)(v_seg);
				pMesh->pVertex[vIdx] = GetVertex(uw, vw);
				pMesh->pNormal[vIdx] = GetNormal(pMesh->pVertex[vIdx], uw, vw);
                // 生成侧边线index
                if (u < u_seg)
                {
                    if (v == 0)
                    {
                        m_pMesh->pSideEdge[index0++] = u * v_seg + v;
                        m_pMesh->pSideEdge[index0++] = (u + 1) * v_seg + v;
                    }
                    else if (v == v_seg / 4)
                    {
                        m_pMesh->pSideEdge[u_seg * 2 * 1 + (index1++)] = u * v_seg + v;
                        m_pMesh->pSideEdge[u_seg * 2 * 1 + (index1++)] = (u + 1) * v_seg + v;
                    }
                    else if (v == v_seg / 4 * 2)
                    {
                        m_pMesh->pSideEdge[u_seg * 2 * 2 + (index2++)] = u * v_seg + v;
                        m_pMesh->pSideEdge[u_seg * 2 * 2 + (index2++)] = (u + 1) * v_seg + v;
                    }
                    else if (v == v_seg / 4 * 3)
                    {
                        m_pMesh->pSideEdge[u_seg * 2 * 3 + (index3++)] = u * v_seg + v;
                        m_pMesh->pSideEdge[u_seg * 2 * 3 + (index3++)] = (u + 1) * v_seg + v;
                    }
                }
				vIdx++;
			} // end of 'for (UInt32 v = 0; v <= v_seg; ++v)'

		}// end of 'for (UInt32 u = 0; u < u_seg; ++u)'
		SetVertexIdx(vIdx);

		//2.生成面片
		int faceIdx = faceStartIdx;
		//2.2 剩余部分. 注意, 不要随便修改 faceIdx 的值
		for (int u = 1; u <= u_seg; ++u)
		{
			int u1 = u - 1;
			int u2 = u;
			for (int v = 1; v <= v_seg; ++v)
			{
				int v1 = v - 1;
				int v2 = v;
				if (v2 == v_seg)
				{// 因为是圆， 最后一点和最开始一点重合，所以可以共享
					v2 = 0;
				}

				int p1 = u1 * v_seg + v1;
				int p2 = u1 * v_seg + v2;
				int p3 = u2 * v_seg + v1;
				int p4 = u2 * v_seg + v2;

				pMesh->pFace[faceIdx++] = p1;
				pMesh->pFace[faceIdx++] = p3;
				pMesh->pFace[faceIdx++] = p4;

				pMesh->pFace[faceIdx++] = p1;
				pMesh->pFace[faceIdx++] = p4;
				pMesh->pFace[faceIdx++] = p2;
			}// end of for (UInt32 v = 1; v <= v_seg; ++v){
		}// end of for (UInt32 u = 2; u <= u_seg; ++u){
		SetFaceIdx(faceIdx);
		return true;
	}

	bool CRotaterBaseTriangular::MeshUnderSurface(int count)
	{
		int vIdx = GetVertexIdx();
		int faceIdx = GetFaceIdx();
		Matrix33 R;
		R.Identity();
		bool ret = CTriangularUtil::MeshDish(count, GetUnderFaceRadius(), true, R, FLOAT3D3(0.0f, 0.0f, 0.0f),
			*(GetMeshObj()), vIdx, faceIdx);
		SetVertexIdx(vIdx);
		SetFaceIdx(faceIdx);
		return ret;
	}
}

