#ifndef _CAPTRIANGULAR_H_
#define _CAPTRIANGULAR_H_
#include "CapBaseTriangular.h"

namespace tri
{
	class CCapTriangular :
		public CCapBaseTriangular
	{
	private:
		FLOAT3D m_h; // 高度
		FLOAT3D m_r; // 底面横截圆半径

		// 下面两个参数是计算出来的
		FLOAT3D m_gama; //最大弧度值
		FLOAT3D m_R; //球半径

		//formula : [x,y,z] = O + M * [m_R * sin(alpha) * cos(beta), m_R * sin(alpha) * sin(beta), m_R * cos(alpha) + m_h - m_R]
		// and here, set O = [0,0,0], M = I. 0<=beta< 2*pi, 0<=alpha<=m_gama 
		// m_gama = h < R ? asin(r/R): pi - asin(r/R)
	public:
		CCapTriangular(void);
		~CCapTriangular(void);

		virtual bool SetParam(const std::vector<FLOAT3D> &parameters);

	protected:
		virtual FLOAT3D3 GetVertex(FLOAT3D uw, FLOAT3D vw)
		{
			FLOAT3D sinAlpha = (float)sin(GetU(uw));
			FLOAT3D cosAlpha = (float)cos(GetU(uw));
			FLOAT3D sinBeta = (float)sin(GetV(vw));
			FLOAT3D cosBeta = (float)cos(GetV(vw));
			return FLOAT3D3(m_R * sinAlpha * cosBeta, m_R * sinAlpha * sinBeta, m_R * cosAlpha + m_h - m_R);
		}
		virtual FLOAT3D3 GetNormal(FLOAT3D3 vertex, FLOAT3D /*uw*/, FLOAT3D /*vw*/)
		{
			FLOAT3D3 normal = vertex - FLOAT3D3(0.0f, 0.0f, m_h - m_R);
			normal.Normalize();
			return normal;
		}
		virtual FLOAT3D3 GetBallCap()
		{
			return FLOAT3D3(0.0f, 0.0f, m_h);
		}
		virtual FLOAT3D GetUnderFaceRadius()
		{
			return m_r;
		}
		FLOAT3D GetU(FLOAT3D uw)
		{
			return m_gama * uw;
		}
		FLOAT3D GetV(FLOAT3D vw)
		{
			return float(PI) * 2.0f * vw;
		}
	};
}

#endif
