#ifndef _PARAMOBJECT_H_
#define _PARAMOBJECT_H_
#include "GeometryObject.h"
#include <set>
namespace primitive
{
	class CParamObject :
		public CGeometryObject
	{
	public:
		CParamObject(EnBodyType type);
		~CParamObject(void);
		const std::vector<FLOAT3D>& GetParam() const 
		{
			return m_paramList;
		}
		void PushParam(FLOAT3D param) 
		{
			m_paramList.push_back(param);
		}
		virtual size_t Size(bool withParam) const
		{
			if (withParam)
			{
				return CGeometryObject::Size(withParam) + m_paramList.size() * sizeof(FLOAT3D);
			}
			else
			{
				return CGeometryObject::Size(withParam);
			}
		}
		
		virtual void OnCopy(CParamObject& obj) const
		{
			obj.m_paramList.assign(m_paramList.begin(), m_paramList.end());
		}
		virtual CParamObject* Clone() const{
			CParamObject* pGeoObj = new CParamObject(GetType());
			Copy(*pGeoObj);
			return pGeoObj;
		}
	private:
		//param
		std::vector<FLOAT3D> m_paramList;
	};
}
#endif

