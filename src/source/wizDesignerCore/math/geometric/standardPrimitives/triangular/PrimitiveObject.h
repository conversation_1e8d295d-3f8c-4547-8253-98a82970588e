#ifndef _PRIMITIVEOBJECT_H_
#define _PRIMITIVEOBJECT_H_
#include "GeometryObject.h"

namespace primitive
{
    #define GETR(clr) (((clr) & oxff000000) >> 24)
    #define GETG(clr) (((clr) & ox00ff0000) >> 16)
    #define GETB(clr) (((clr) & ox0000ff00) >> 8 )
    #define GETA(clr) (((clr) & ox000000ff) >> 0 )
    #define SETR(clr, r) (((clr) & ox00ffffff) | ((r) << 24))
    #define SETG(clr, g) (((clr) & oxff00ffff) | ((g) << 16))
    #define SETB(clr, b) (((clr) & oxffff00ff) | ((b) << 8 ))
    #define SETA(clr, a) (((clr) & oxffffff00) | ((a) << 0 ))
    #define SETRGBA(clr, r, g, b, a) ((clr) = ((r) << 24) | ((g) << 16) | ((b) << 8) | ((a) << 0))
	
    
    class CGeometryObject;
	class CPrimitiveObject
	{
    public:
        typedef std::vector<CGeometryObject*>   ArrayObject;
        typedef std::vector<CPrimitiveObject*>  ArrayChild;
	public:
		CPrimitiveObject(void);
		~CPrimitiveObject(void);
		CPrimitiveObject(const char* name, int r, int g, int b, int a);

		void DeepClone(CPrimitiveObject* obj) const
		{
			obj->m_name = this->m_name;
			obj->m_color = this->m_color;
			obj->m_geoObjectList.resize(this->m_geoObjectList.size(), NULL);
			for (size_t i = 0; i < this->m_geoObjectList.size(); ++i)
			{
				obj->m_geoObjectList[i] = this->m_geoObjectList[i]->Clone();
			}
			obj->m_geoObjectList.assign(this->m_geoObjectList.begin(), this->m_geoObjectList.end());
		}

		void Reset()
		{
			m_name = "";
			m_desc = "";
			SetColor(255, 255, 255, 255);
			m_geoObjectList.clear();
		}

		const int GetColor() const 
		{
			return m_color;
		}
		void SetColor(int color)
		{
			m_color = color;
		}
		void SetColor(int r, int g, int b, int a=255)
		{
			SETRGBA(m_color, r, g, b, a);
		}
		void AddGeometryObject(CGeometryObject* obj)
		{
			m_geoObjectList.push_back(obj);
			obj->SetComponentId(m_id);
			m_currentGeoObj = obj;
		}

		ArrayObject& GeoObjList()
		{
			return m_geoObjectList;
		}
		const ArrayObject& GeoObjList() const
		{
			return m_geoObjectList;
		}
		CGeometryObject* GetCurrentGeometryObject()
		{
			return m_currentGeoObj;
		}

		const char* GetName() const 
		{
			return m_name.c_str();
		}
		void SetName(const std::string& name) 
		{
			m_name = name;
		}

		// TODO： 2017/04/08 添加描述
		const char* GetDesc() const
		{
			return m_desc.c_str();
		}
		void SetDesc(const char* desc)
		{
			m_desc = desc;
		}


		// 序列化对象名称 
		int SerializeName(char *buf) const 
		{
			int offset = 0;
			//name
			int nameSize = (int)m_name.size();
			memcpy(buf + offset, (void*)&nameSize,sizeof(int));
			offset += sizeof(int);
			memcpy(buf + offset, m_name.c_str(),m_name.size());
			offset += (int)m_name.size();

			return offset;
		}

		// 序列化对象描述 
		int SerializeDesc(char *buf) const 
		{
			int offset = 0;
			//desc
			int descSize = (int)m_desc.size();
			memcpy(buf + offset, (void*)&descSize,sizeof(int));
			offset += sizeof(int);
			memcpy(buf + offset, m_desc.c_str(),m_desc.size());
			offset += (int)m_desc.size();

			return offset;
		}

		// 序列化对象ID
		int SerializeId(char *buf) const 
		{
			int offset = 0;
			// id
			memcpy(buf + offset, (void*)&m_id, sizeof(int));
			offset += sizeof(int);

			return offset;
		}

		// TODO: 序列化字节数 
		int SerializeObjectSize() const
		{
			int size = 0;
			//name 
			size = size + sizeof(int) + (int)m_name.size();
			//id
			size += sizeof(int);
			//pid
			size += sizeof(int);
			//children.size  
			size += sizeof(int);
			return  size;
		}

		// TODO: 序列化对象名称、Id、PId及子节点数
		int SerializeObject(char *buf) const
		{
			int offset = 0;
			// name
			int nameSize = (int)m_name.size();
			memcpy(buf + offset, (void*)&nameSize,sizeof(int));
			offset += sizeof(int);
			memcpy(buf + offset, m_name.c_str(),m_name.size());
			offset += (int)m_name.size();

			// id
			memcpy(buf + offset, (void*)&m_id, sizeof(int));
			offset += sizeof(int);

			// pid 
			memcpy(buf + offset, (void*)&m_pid, sizeof(int));
			offset += sizeof(int);

			// number of children
			int childCount = (int)m_childObjList.size();
			memcpy(buf + offset, (void*)&childCount,sizeof(int));
			offset += sizeof(int);

			return offset;
		}

		void SetId(int id) 
        {
            m_id = id; 
        }
		int GetId() const 
        { 
            return m_id;
        }

		// TODO: 2017/03/16
		void SetPId(int pid) 
        { 
            m_pid = pid; 
        }
		int GetPId() const 
        { 
            return m_pid;
        }

		void SetParent(CPrimitiveObject* pObj) 
        { 
            m_parentObj = pObj;
        }
		const CPrimitiveObject* GetParent() const 
        { 
            return m_parentObj;
        }
		void AddChild(CPrimitiveObject* pObj) 
        { 
            m_childObjList.push_back(pObj);
        }
		bool HasChild() const 
        { 
            return m_childObjList.size() > 0; 
        } 
		const ArrayChild& GetChildObjList() const
        { 
            return m_childObjList;
        }

        size_t Size(bool withParam) const;
		int Serialize(char *buf, bool withParam) const;
		int UnSerialize(const char *buf, bool withParam);
	private:
		std::string         m_name;
        std::string         m_desc;
        int                 m_id;
        // TODO：扩展对象定义 2017/03/16
		int                 m_pid;
		int                 m_color;
		ArrayObject         m_geoObjectList;
		CGeometryObject*    m_currentGeoObj;

		ArrayChild          m_childObjList;
		CPrimitiveObject*   m_parentObj;
	};

	class CPrimitiveObjectManager
	{
	public:
		CPrimitiveObjectManager();
		~CPrimitiveObjectManager();
		CPrimitiveObject* AddPrimitiveObj()
		{
			CPrimitiveObject* pObj = new CPrimitiveObject();
			m_primitiveList.push_back(pObj);
			pObj->SetId((int)m_primitiveList.size() - 1);
			return pObj;
		}
		
		// TODO: 2017/03/16
		CPrimitiveObject* AddPrimitiveObj(const std::string& name)
		{
			CPrimitiveObject* pObj = new CPrimitiveObject();
			m_primitiveList.push_back(pObj);
			pObj->SetName(name);
			pObj->SetId((int)m_primitiveList.size() - 1);
			return pObj;
		}

		void AddPrimitiveObj(CPrimitiveObject* pObj)
		{
			m_primitiveList.push_back(pObj);
			pObj->SetId((int)m_primitiveList.size() - 1);
		}

		void SetRoot(CPrimitiveObject* pObj)
		{
			m_rootObj = pObj;
		}

		const CPrimitiveObject* GetRoot() const
		{
			return m_rootObj;
		}

		void SetRegex(const std::string& regex)
		{
			m_regex = regex;
		}

		void SetNamed(int named)
		{
			m_named = named;
		}

		const CPrimitiveObject* GetPrimitiveObj(const std::string& name) const 
		{ 
			return (const_cast<CPrimitiveObjectManager*>(this))->GetPrimitiveObj(name);
		}

		CPrimitiveObject* GetPrimitiveObj(const std::string& name) 
		{ 
			std::map<std::string, CPrimitiveObject*>::iterator it = m_primitiveMaps.find(name);
			if (it != m_primitiveMaps.end())
				return m_primitiveMaps[name];
			return NULL;
		}

		void AddPrimitiveMaps(const std::string& name, CPrimitiveObject* pObj)
		{
			m_primitiveMaps[name] = pObj;
		}

		void GetCellObjectList(std::vector<CPrimitiveObject*> &objectList, const CPrimitiveObject* pObj);
		const void GetCellObjectList(std::vector<CPrimitiveObject*> &objectList, const std::string& name, int& named) const;
		const void GetCellObjectList(std::vector<CPrimitiveObject*> &objectList, int& objId, int& named) const;

		void GetObjectTopoRoute(std::stack<CPrimitiveObject*> &topoObjStack, const CPrimitiveObject* pObj);
		const void GetObjectTopoRoute(std::stack<CPrimitiveObject*> &topoObjStack, const std::string& name, int& named) const;

		CPrimitiveObject* GetRegexPrimitiveObj(const std::string& name);

		const CPrimitiveObject* GetRegexPrimitiveObj(const std::string& name) const
		{
			return (const_cast<CPrimitiveObjectManager*>(this))->GetRegexPrimitiveObj(name);
		}

		const CPrimitiveObject* GetPrimitiveObj(int idx) const 
		{ 
			return (const_cast<CPrimitiveObjectManager*>(this))->GetPrimitiveObj(idx);
		}
		
		CPrimitiveObject* GetPrimitiveObj(int idx) 
		{ 
			if (idx >= 0 && idx < (int)m_primitiveList.size())
				return m_primitiveList[idx];
			return NULL;
		}
		
		void Release();
		
		size_t Size() const 
		{
			return m_primitiveList.size();
		}

	private:
		// TODO: 扩展成员 2017/0316
		int m_named;
		std::string m_regex;
		CPrimitiveObject* m_rootObj;
		std::vector<CPrimitiveObject*> m_primitiveList;	
		std::map<std::string, CPrimitiveObject*> m_primitiveMaps; 
	};

}

#endif
