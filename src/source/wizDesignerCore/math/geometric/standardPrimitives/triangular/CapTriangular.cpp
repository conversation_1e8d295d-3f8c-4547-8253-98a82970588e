
#include "CapTriangular.h"

namespace tri
{
	CCapTriangular::CCapTriangular(void)
	{
	}


	CCapTriangular::~CCapTriangular(void)
	{
	}

	bool CCapTriangular::SetParam(const std::vector<FLOAT3D> &parameters)
	{
		if (parameters.size() != 2)
		{
			return false;
		}
		m_r = parameters[0];
		m_h = parameters[1];
		// m_h > 0
		if (m_h <= EPSILON)
		{
			return false;
		}

		// m_r > 0
		if (m_r <= EPSILON)
		{
			return false;
		}

		m_R = (m_r * m_r + m_h * m_h) / (2.0f * m_h);
		FLOAT3D gama = (float)asin(m_r / m_R);
		m_gama = gama;
		if (m_h > m_R)
		{
			m_gama = PI - gama;
		}
		// 0 < m_gama < pi
		if (m_gama - PI >= EPSILON || m_gama <= EPSILON)
		{
			return false;
		}
		return true;
	}
}
