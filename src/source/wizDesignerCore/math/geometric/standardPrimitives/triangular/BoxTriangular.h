#ifndef _BOXTRIANGULAR_H_
#define _BOXTRIANGULAR_H_
#include "Triangular.h"

namespace tri
{
	class CBoxTriangular : public CTriangular  
	{
	private:
		FLOAT3D m_xLength; // 
		FLOAT3D m_yLength; // 
		FLOAT3D m_zLength; //
	public:
		CBoxTriangular(void);
		~CBoxTriangular(void);
		//triangular
		virtual bool SetParam(const std::vector<FLOAT3D> &parameters);
		virtual bool Mesh(const std::vector<unsigned int> &segments, bool isClose);
	private:
		bool _DoMesh();
	};
}

#endif
