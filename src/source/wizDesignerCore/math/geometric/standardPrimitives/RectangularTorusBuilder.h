#pragma once

#include "StandardPrimitiveCommon.h"

WD_NAMESPACE_BEGIN


/**
* @brief 构造方环体
*/
class WD_API RectangularTorusBuilder
{
public:
    /**
    * @brief 生成网格 RTOR
    * @param insideRadius 内环半径
    * @param outsideRadius 外环半径
    * @param height 高度
    * @param angle 环弧角,角度制,取值范围[0.0001, 360.0]
    */
    static MeshStruct Mesh(float insideRadius
        , float outsideRadius 
        , float height
        , float angle
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
    * @brief 生成关键点列表 RTOR
    * @param insideRadius 内环半径
    * @param outsideRadius 外环半径
    * @param height 高度
    * @param angle 环弧角,角度制,取值范围[0.0001, 360.0]
    */
    static FKeyPoints KeyPoints(float insideRadius
        , float outsideRadius 
        , float height
        , float angle);
    /**
    * @brief 生成交点列表 RTOR
    * @param insideRadius 内环半径
    * @param outsideRadius 外环半径
    * @param height 高度
    * @param angle 环弧角,角度制,取值范围[0.0001, 360.0]
    */
    static FVec3Vector IntersectPoints(float insideRadius
        , float outsideRadius 
        , float height
        , float angle);
    /**
     * @brief 转换 SRTO参数 到 RTOR参数
     * @param directionA 截面A的方向向量
     * @param positionA 截面A的中心点
     * @param directionB 截面B的方向向量
     * @param positionB 截面B的中心点
     * @param diameter 环截面宽度
     * @param height 环截面高度
     * @param outTransform 输出的变换信息
     * @param outInsideRadius 输出的内环半径
     * @param outOutsideRadius 输出的外环半径
     * @param outHeight 输出的高度
     * @param outAngle 输出的环弧角,角度制,取值范围[0.0001, 360.0]
     * @return 是否转换成功(当参数无效时,将转换失败)
    */
    static bool SRTOToRTOR(const FVec3& directionA
        , const FVec3& positionA
        , const FVec3& directionB
        , const FVec3& positionB
        , float diameter
        , float height
        , FMeshTransform& outTransform
        , float& outInsideRadius
        , float& outOutsideRadius
        , float& outHeight
        , float& outAngle);
    /**
    * @brief 生成网格 RTOR
    *   简单网格，顶点只包含位置信息，不包含任何法线、UV相关信息，目的是保证顶点位置没有重合
    * @param insideRadius 内环半径
    * @param outsideRadius 外环半径
    * @param height 高度
    * @param angle 环弧角,角度制,取值范围[0.0001, 360.0]
    */
    static MeshStruct SimpleMesh(float insideRadius
        , float outsideRadius
        , float height
        , float angle
        , const MeshLODSelection& lodSelection = MeshLODSelection());
};

WD_NAMESPACE_END

