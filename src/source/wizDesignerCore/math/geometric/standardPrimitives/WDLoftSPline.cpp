#include "WDLoftSPline.h"
#include "ArcBuilder.h"
#include "../../geometric/curve/TQuadraticBezierCurve3.h"

WD_NAMESPACE_BEGIN

WDLoftSPline::Curve::Type WDLoftSPline::Curve::TypeFromString(const char* str)
{
    if (str == nullptr)
        return Type::CT_Line;
    else if (_stricmp("LINE", str) == 0)
        return Type::CT_Line;
    else if (_stricmp("RADI", str) == 0)
        return Type::CT_Radius;
    else if (_stricmp("THRU", str) == 0)
        return Type::CT_Through;
    else if (_stricmp("BULG", str) == 0)
        return Type::CT_BulgeFactor;
    else if (_stricmp("FILL", str) == 0)
        return Type::CT_Fillet;
    else if (_stricmp("CENT", str) == 0)
        return Type::CT_Centre;
    else
        return Type::CT_Line;
}


bool WDLoftSPline::Curve::operator==(const Curve& right) const
{
    return this->type == right.type
        && this->position == right.position
        && this->cPoint == right.cPoint
        && this->radius == right.radius
        && this->radsetFlag == right.radsetFlag
        && this->bulgeFactor == right.bulgeFactor;
}
bool WDLoftSPline::Curve::operator!=(const Curve& right) const
{
    return this->type != right.type
        || this->position != right.position
        || this->cPoint != right.cPoint
        || this->radius != right.radius
        || this->radsetFlag != right.radsetFlag
        || this->bulgeFactor != right.bulgeFactor;
}
bool WDLoftSPline::Curve::operator>(const Curve& right) const
{
#define M_ISMORE(f1, f2) if(f1 != f2) return (f1 > f2);

    M_ISMORE(type, right.type);
    M_ISMORE(position, right.position);
    M_ISMORE(cPoint, right.cPoint);
    M_ISMORE(radius, right.radius);
    M_ISMORE(radsetFlag, right.radsetFlag);
    M_ISMORE(bulgeFactor, right.bulgeFactor);

#undef M_ISMORE
    return false;
}
bool WDLoftSPline::Curve::operator<(const Curve& right) const
{
#define M_ISLESS(f1, f2) if(f1 != f2) return (f1 < f2);

    M_ISLESS(type, right.type);
    M_ISLESS(position, right.position);
    M_ISLESS(cPoint, right.cPoint);
    M_ISLESS(radius, right.radius);
    M_ISLESS(radsetFlag, right.radsetFlag);
    M_ISLESS(bulgeFactor, right.bulgeFactor);

#undef M_ISLESS
    return false;
}
bool WDLoftSPline::Curve::operator>=(const Curve& right) const
{
    return !((*this)<right);
}
bool WDLoftSPline::Curve::operator<=(const Curve& right) const
{
    return !((*this)>right);
}



FVec3Vector WDLoftSPline::vertices(const MeshLODSelection& lodSelection) const
{
    WDUnused(lodSelection);
    FVec3Vector rVector;
    if (_cPoints.size() < 2)
        return rVector;

    // 分别获取点
    for (size_t i = 0; i < _cPoints.size(); ++i)
    {
        const auto& cPoint = _cPoints[i];
        const Point* pPoint = std::get_if<Point>(&cPoint);
        if (pPoint != nullptr)
        {
            // 这里会剔除重复点
            if (rVector.empty())
                rVector.push_back(pPoint->position);
            else if (FVec3::DistanceSq(rVector.back(), pPoint->position) <= NumLimits<float>::Epsilon)
                continue;
            else
                rVector.push_back(pPoint->position);

            continue;
        }
        const Curve* pCurve = std::get_if<Curve>(&cPoint);
        if (pCurve != nullptr)
        {
            // 这里计算圆弧时，需要同时知道前一个点(Point.position)和后一个点(Point.position)的位置
            // 其中前一个点的位置已经添加到 rVector中(rVector.back()),因此如果rVector为空，则跳过
            // 因此这里还需要拿到后一个Point.position, 如果后一个对象不是Point对象，则跳过
            if (rVector.empty())
                continue;
            size_t iNext = i + 1;
            if (iNext >= _cPoints.size())
                continue;
            const auto& cPointNext = _cPoints[iNext];
            const Point* pPointNext = std::get_if<Point>(&cPointNext);
            if (pPointNext == nullptr)
                continue;
            const FVec3& prevPt = rVector.back();
            FVec3 nextPt = pPointNext->position;
            switch (pCurve->type)
            {
            case Curve::CT_Line:
            {
                // 什么也不做
            }
            break;
            case Curve::CT_Radius:
            {
                assert(false && "注意:Curve::CT_Radius生成圆弧暂未处理!");
            }
            break;
            case Curve::CT_Through:
            {
                // 三点画弧
                // 这里三个点都减去第一个点是为了将大坐标化成小坐标，防止可能出现的因为坐标值太大导致的失真
                auto rVs = ArcBuilder::Arc(prevPt - prevPt, pCurve->position - prevPt, nextPt - prevPt, 29);
                if (rVs)
                {
                    for (auto& each : rVs.value())
                        each += prevPt;
                    rVector.insert(rVector.end(), rVs.value().begin() + 1, rVs.value().end());
                    // 这里计算出来的点是用了下一个点的，最后一个点应该是等于下一个点的，但是在某些特殊情况下会导致两个点不相等
                    // 所以这里将最后一个点pop掉，让下一个点走 if (pPoint != nullptr) 的push重新添加
                    // 避免两个本该相同的点以很小的差距添加两次
                    rVector.pop_back();
                }
            }
            break;
            case Curve::CT_BulgeFactor:
            {
                assert(false && "注意:Curve::CT_BulgeFactor生成圆弧暂未处理!");
            }
            break;
            case Curve::CT_Fillet:
            {
                TQuadraticBezierCurve3<float> curve;
                curve.c1 = prevPt;
                curve.c2 = pCurve->position;
                curve.c3 = nextPt;

                // 根据半径计算分段数, 这里角度默认取90,计算一个分段数的近似值
                auto segment = ArcTravel<double>(90.0
                    , Max(FVec3::Distance(curve.c1, curve.c2), FVec3::Distance(curve.c2, curve.c3))
                    , 2.0);

                auto rPoints = curve.points(segment);
                if (!rPoints.empty())
                    rVector.insert(rVector.end(), rPoints.begin() + 1, rPoints.end());
            }
            break;
            case Curve::CT_Centre:
            {
                // 圆弧的圆心点
                const auto& cenPt = pCurve->position;
                // 起点和终点不能重合
                if (FVec3::DistanceSq(prevPt, nextPt) <= NumLimits<float>::Epsilon)
                    break;
                // 两个端点到圆心的向量
                auto vStart     =   prevPt - cenPt;
                if (vStart.lengthSq() <= NumLimits<float>::Epsilon)
                    break;
                auto vEnd       =   nextPt - cenPt;
                if (vEnd.lengthSq() <= NumLimits<float>::Epsilon)
                    break;
                auto vnStart    =   vStart.normalized();
                auto vnEnd      =   vEnd.normalized();
                // 计算旋转角度
                float angle     =   FVec3::Angle(vStart, vEnd);
                // 调节点，用于确定选择优弧还是劣弧
                const auto& conPt = pCurve->cPoint;
                // 使用起点和终点计算一个法向，使用该法向生成的弧默认为劣弧
                auto normal = FVec3::Cross(vnStart, vnEnd).normalized();
                // 根据调节点计算是否是优弧
                auto vCon = conPt - cenPt;
                if (vCon.lengthSq() >= NumLimits<float>::Epsilon)
                {
                    auto vnCon = vCon.normalized();
                    // 先叉乘计算一个方向向量
                    // 再通过该方向向量验证控制点分别在起点方向与终点方向的左侧还是右侧
                    auto csS = FVec3::Cross(vnStart, vnCon).normalized();
                    auto csE = FVec3::Cross(vnCon, vnEnd).normalized();
                    if (FVec3::Dot(csS, normal) < 0.0f || FVec3::Dot(csE, normal) < 0.0f)
                    {
                        angle  = 360.0f - angle;
                        normal = -normal;
                    }
                }
                // 半径
                float radius    = pCurve->radius;
                // 如果半径标志为false,则根据起点重新计算半径
                if (!pCurve->radsetFlag)
                    radius = Max(vStart.length(), vEnd.length());

                // 根据半径计算分段数
                auto segment = ArcTravel<double>(angle, radius, 2.0);

                rVector.reserve(rVector.size() + segment + 1);
                // 中间点
                float segmengF = static_cast<float>(segment);
                auto sVec = vnStart * radius;
                for (unsigned int index = 1; index < segment; ++index)
                {
                    float iF = static_cast<float>(index);
                    float stepAngle = iF / segmengF * angle;
                    // 使用旋转法，生成圆弧顶点
                    FMat3 rMat = FMat3::MakeRotation(stepAngle, normal);
                    auto pt = rMat * sVec;
                    pt += cenPt;
                    rVector.push_back(pt);
                }
            }
            break;
            default:
                break;
            }
            continue;
        }
    }

    return rVector;
}

WD_NAMESPACE_END
