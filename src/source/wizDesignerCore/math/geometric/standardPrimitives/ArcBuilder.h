#pragma once

#include "StandardPrimitiveCommon.h"

WD_NAMESPACE_BEGIN

/**
 * @brief 构造圆弧顶点
*/
class WD_API ArcBuilder
{
public:
    /**
     * @brief 三点画弧线
     * @param pt0 弧线的起始端点
     * @param pt1 在弧线上 且与起始、结束端点不重合的任意一个点
     * @param pt2 弧线的结束端点
     * @param segment 弧线的分段数, 分段数必须大于2, 决定了顶点个数(分段数 =  顶点个数 - 1)
     * @return 弧线的顶点列表, 从 pt0 到 pt2
    */
    static std::optional< FVec3Vector > Arc(const FVec3& pt0
        , const FVec3& pt1
        , const FVec3& pt2
        , uint segment);
};

WD_NAMESPACE_END

