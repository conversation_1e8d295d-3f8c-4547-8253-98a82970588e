#include "EllipsoidBuilder.h"
#include "triangular/MeshObj.h"
#include "triangular/SphereTriangular.h"

WD_NAMESPACE_BEGIN

static FVec2 getVertex(const float& lengthX, const float& lengthY, const float& angle)
{
    float radian = DegToRad<float>(angle);
    if (Abs(radian - PI * 0.5f) <= NumLimits<float>::Epsilon)
    {
        return FVec2(0.0f, lengthX);
    }
    else if (Abs(radian - PI * 1.5f) <= NumLimits<float>::Epsilon)
    {
        return FVec2(0.0f, -lengthX);
    }
    else if (PI * 0.5f < radian && radian < PI * 1.5f)
    {
        float x = (lengthX * lengthY) / Sqrt(lengthY * lengthY + lengthX * lengthX * Tan(radian) * Tan(radian));
        float y = (lengthX * lengthY * Tan(radian)) / Sqrt(lengthY * lengthY + lengthX * lengthX * Tan(radian) * Tan(radian));
        return FVec2(-x, -y);
    }
    else
    {
        float x = (lengthX * lengthY) / Sqrt(lengthY * lengthY + lengthX * lengthX * Tan(radian) * Tan(radian));
        float y = (lengthX * lengthY * Tan(radian)) / Sqrt(lengthY * lengthY + lengthX * lengthX * Tan(radian) * Tan(radian));
        return FVec2(x, y);
    }
}

MeshStruct EllipsoidBuilder::Mesh(float xDiameter
    , float yDiameter
    , float zDiameter
    , const MeshLODSelection& lodSelection)
{
    MeshStruct ret;

    if (xDiameter <= 0.0f || yDiameter <= 0.0f || zDiameter <= 0.0f)
        return ret;

    primitive::CMeshObj         mesh;
    tri::CSphereTriangular      obj;
    std::vector<unsigned int>   segs;
    
    uint uSegments = lodSelection.getSegment(360.0f, xDiameter * 0.5f);
    uint vSegments = lodSelection.getSegment(360.0f, zDiameter * 0.5f);
    segs.push_back(uSegments - 1); // u 代表球纬度分段,纬度分段必须是单数,以保证赤道与 XOY平面重合
    segs.push_back(vSegments); // v 代表球经度分段,经度分段必须是4的倍数(以保证4条测线分别与 X,Y,-X,-Y重合)


    obj.SetMeshObj(&mesh);
    obj.SetParam({
          xDiameter / 2.0f    //X方向直径
        , yDiameter / 2.0f    //Y方向直径
        , zDiameter / 2.0f    //Z方向直径
        });
    obj.Mesh(segs, true);

    ret.fromMesh(mesh);

    return ret;
}

FKeyPoints EllipsoidBuilder::KeyPoints(float xDiameter,  float yDiameter, float zDiameter)
{
    FKeyPoints ret;
    
    if (xDiameter <= 0.0f || yDiameter <= 0.0f || zDiameter <= 0.0f)
        return ret;

    float halfX = xDiameter / 2.0f;
    float halfY = yDiameter / 2.0f;
    float halfZ = zDiameter / 2.0f;

    ret.reserve(6);
    // p1点
    FKeyPoint p1 = FKeyPoint( FVec3(halfX, 0.0f, 0.0f),  FVec3(1.0f, 0.0f, 0.0f));
    ret.push_back(p1);
    // p2点
    FKeyPoint p2 = FKeyPoint(FVec3(0.0f, halfY, 0.0f),  FVec3(0.0f, 1.0f, 0.0f));
    ret.push_back(p2);
    // p3点
    FKeyPoint p3 = FKeyPoint(FVec3(0.0f, 0.0f, halfZ),  FVec3(0.0f, 0.0f, 1.0f));
    ret.push_back(p3);
    // p4点
    FKeyPoint p4 = FKeyPoint(FVec3(-halfX, 0.0f, 0.0f),  FVec3(-1.0f, 0.0f, 0.0f));
    ret.push_back(p4);
    // p5点
    FKeyPoint p5 = FKeyPoint(FVec3(0.0f, -halfY, 0.0f),  FVec3(0.0f, -1.0f, 0.0f));
    ret.push_back(p5);
    // p6点
    FKeyPoint p6 = FKeyPoint(FVec3(0.0f, 0.0f, -halfZ),  FVec3(0.0f, 0.0f, -1.0f));
    ret.push_back(p6);

    return ret;
}

FVec3Vector EllipsoidBuilder::IntersectPoints(float xDiameter, float yDiameter, float zDiameter)
{
    FVec3Vector ret;

    if (xDiameter <= 0.0f || yDiameter <= 0.0f || zDiameter <= 0.0f)
        return ret;

    float halfX = xDiameter / 2.0f;
    float halfY = yDiameter / 2.0f;
    float halfZ = zDiameter / 2.0f;

    ret.reserve(6);
    // p1点
    ret.emplace_back(FVec3(halfX, 0.0f, 0.0f));
    // p2点
    ret.emplace_back(FVec3(0.0f, halfY, 0.0f));
    // p3点
    ret.emplace_back(FVec3(0.0f, 0.0f, halfZ));
    // p4点
    ret.emplace_back(FVec3(-halfX, 0.0f, 0.0f));
    // p5点
    ret.emplace_back(FVec3(0.0f, -halfY, 0.0f));
    // p6点
    ret.emplace_back(FVec3(0.0f, 0.0f, -halfZ));

    return ret;
}

MeshStruct EllipsoidBuilder::SideLines(float xDiameter
    , float yDiameter
    , float zDiameter
    , const MeshLODSelection& lodSelection)
{
    MeshStruct ret;
    uint segmentsX = lodSelection.getSegment(360.0f, xDiameter * 0.5f);
    uint segmentsY = lodSelection.getSegment(360.0f, yDiameter * 0.5f);
    uint segmentsZ = lodSelection.getSegment(360.0f, zDiameter * 0.5f);
    
    std::vector<FVec3> tPositions;
    std::vector<uint>  tSideIndices;

    for (uint i = 0; i < segmentsX; i++)
    {
        float angle = 360.0f * i / segmentsX;
        FVec2 point = getVertex(xDiameter, yDiameter, angle);
        FVec3 vertex(point, 0.0f);
        tPositions.push_back(vertex);
        uint index01 = i;
        uint index02 = index01 + 1;
        if (index02 == segmentsX)
        {
            index02 = 0;
        }
        tSideIndices.push_back(index01);
        tSideIndices.push_back(index02);
    }

    for (uint i = 0; i < segmentsY; i++)
    {
        float angle = 360.0f * i / segmentsY;
        FVec2 point = getVertex(yDiameter, zDiameter, angle);
        FVec3 vertex(0.0f, point.x, point.y);
        uint index01 = static_cast<uint>(tPositions.size());
        uint index02 = index01 + 1;
        tPositions.push_back(vertex);
        if ((uint)index02 == (segmentsX + segmentsY))
        {
            index02 = segmentsX;
        }
        tSideIndices.push_back(index01);
        tSideIndices.push_back(index02);
    }

    for (uint i = 0; i < segmentsZ; i++)
    {
        float angle = 360.0f * i / segmentsZ;
        FVec2 point = getVertex(zDiameter, xDiameter, angle);
        FVec3 vertex(point.y, 0.0f, point.x);
        uint index01 = static_cast<uint>(tPositions.size());
        uint index02 = index01 + 1;
        tPositions.push_back(vertex);
        if ((uint)index02 == (segmentsX + segmentsY + segmentsZ))
        {
            index02 = (uint)segmentsX + (uint)segmentsY;
        }
        tSideIndices.push_back(index01);
        tSideIndices.push_back(index02);
    }

    ret.positions = std::move(tPositions);
    //边线索引
    ret.wireFrames.setDrawElementUIntData(std::move(tSideIndices));
    ret.wireFrames.setPrimitiveType(WDPrimitiveSet::PrimitiveType::PT_Lines);
    return ret;
}

MeshStruct EllipsoidBuilder::SimpleMesh(float xDiameter, float yDiameter, float zDiameter, const MeshLODSelection& lodSelection)
{
    MeshStruct ret;
    if (xDiameter <= 0.0f || yDiameter <= 0.0f || zDiameter <= 0.0f)
        return ret;

    // 构建顶点
    auto xr = xDiameter / 2;
    auto yr = yDiameter / 2;
    auto zr = zDiameter / 2;

    auto segsCircle = lodSelection.getSegment(360.0F, Max<float>(xr, yr));
    auto segsArc = lodSelection.getSegment(180.0F, Max<float>(xr, zr, zr));
    ret.positions.reserve(2 + (segsArc - 1) * segsCircle);

    auto verticalAngleSeg = PI / segsArc;// 180度
    auto horizentalAngleSeg = TwoPI / segsCircle;
    for (uint i = 1; i < segsArc; i++)
    {
        auto ang1 = verticalAngleSeg * i;
        auto zValue = zr * Cos(ang1);
        auto sinV = Sin(ang1);
        auto xValuePre = xr * sinV;
        auto yValuePre = yr * sinV;

        for (uint j = 0; j < segsCircle; j++)
        {
            auto ang2 = horizentalAngleSeg * j;
            auto xValue = Cos(ang2) * xValuePre;
            auto yValue = Sin(ang2) * yValuePre;
            ret.positions.push_back({ float(xValue), float(yValue), float(zValue) });
        }
    }
    ret.positions.push_back({ 0, 0, zr });
    ret.positions.push_back({ 0, 0, -zr });
    auto idxTopPoint = ret.positions.size() - 2;
    auto idxBottomPoint = ret.positions.size() - 1;

    // 三角面索引
    if (ret.positions.size() < WD::NumLimits<byte>::Max)
    {
        std::vector<byte> tris;
        tris.reserve(ret.positions.size() * 2 * 3);

        int circleBeginIdx = 0;
        while (circleBeginIdx + segsCircle < ret.positions.size() - 2)
        {
            for (uint i = 0; i < segsCircle; i++)
            {
                //nextIdx+_segsCircle   currIdx+_segsCircle
                //nextIdx               currIdx
                auto currIdx = circleBeginIdx + i;
                auto nextIdx = circleBeginIdx + (i + 1) % segsCircle;
                tris.push_back(static_cast<byte>(nextIdx + segsCircle));
                tris.push_back(static_cast<byte>(currIdx));
                tris.push_back(static_cast<byte>(currIdx + segsCircle));

                tris.push_back(static_cast<byte>(nextIdx + segsCircle));
                tris.push_back(static_cast<byte>(nextIdx));
                tris.push_back(static_cast<byte>(currIdx));
            }
            circleBeginIdx += segsCircle;
        }
        // 加上第一圈和最后一圈
        for (uint i = 0; i < segsCircle; i++)
        {
            //     1
            // i       i+1
            tris.push_back(static_cast<byte>(i));
            tris.push_back(static_cast<byte>((i + 1) % segsCircle));
            tris.push_back(static_cast<byte>(idxTopPoint));

            tris.push_back(static_cast<byte>(circleBeginIdx + i));
            tris.push_back(static_cast<byte>(idxBottomPoint));
            tris.push_back(static_cast<byte>(circleBeginIdx + (i + 1) % segsCircle));
        }
        ret.triangles.setDrawElementByteData(std::move(tris));
    }
    else if (ret.positions.size() < WD::NumLimits<ushort>::Max)
    {
        std::vector<ushort> tris;
        tris.reserve(ret.positions.size() * 2 * 3);

        int circleBeginIdx = 0;
        while (circleBeginIdx + segsCircle < ret.positions.size() - 2)
        {
            for (uint i = 0; i < segsCircle; i++)
            {
                //nextIdx+_segsCircle   currIdx+_segsCircle
                //nextIdx               currIdx
                auto currIdx = circleBeginIdx + i;
                auto nextIdx = circleBeginIdx + (i + 1) % segsCircle;
                tris.push_back(static_cast<ushort>(nextIdx + segsCircle));
                tris.push_back(static_cast<ushort>(currIdx));
                tris.push_back(static_cast<ushort>(currIdx + segsCircle));

                tris.push_back(static_cast<ushort>(nextIdx + segsCircle));
                tris.push_back(static_cast<ushort>(nextIdx));
                tris.push_back(static_cast<ushort>(currIdx));
            }
            circleBeginIdx += segsCircle;
        }
        // 加上第一圈和最后一圈
        for (uint i = 0; i < segsCircle; i++)
        {
            //     1
            // i       i+1
            tris.push_back(static_cast<ushort>(i));
            tris.push_back(static_cast<ushort>((i + 1) % segsCircle));
            tris.push_back(static_cast<ushort>(idxTopPoint));

            tris.push_back(static_cast<ushort>(circleBeginIdx + i));
            tris.push_back(static_cast<ushort>(idxBottomPoint));
            tris.push_back(static_cast<ushort>(circleBeginIdx + (i + 1) % segsCircle));
        }
        ret.triangles.setDrawElementUShortData(std::move(tris));
    }
    else
    {
        std::vector<uint> tris;
        tris.reserve(ret.positions.size() * 2 * 3);

        int circleBeginIdx = 0;
        while (circleBeginIdx + segsCircle < ret.positions.size() - 2)
        {
            for (uint i = 0; i < segsCircle; i++)
            {
                //nextIdx+_segsCircle   currIdx+_segsCircle
                //nextIdx               currIdx
                auto currIdx = circleBeginIdx + i;
                auto nextIdx = circleBeginIdx + (i + 1) % segsCircle;
                tris.push_back(static_cast<uint>(nextIdx + segsCircle));
                tris.push_back(static_cast<uint>(currIdx));
                tris.push_back(static_cast<uint>(currIdx + segsCircle));

                tris.push_back(static_cast<uint>(nextIdx + segsCircle));
                tris.push_back(static_cast<uint>(nextIdx));
                tris.push_back(static_cast<uint>(currIdx));
            }
            circleBeginIdx += segsCircle;
        }
        // 加上第一圈和最后一圈
        for (uint i = 0; i < segsCircle; i++)
        {
            //     1
            // i       i+1
            tris.push_back(static_cast<uint>(i));
            tris.push_back(static_cast<uint>((i + 1) % segsCircle));
            tris.push_back(static_cast<uint>(idxTopPoint));

            tris.push_back(static_cast<uint>(circleBeginIdx + i));
            tris.push_back(static_cast<uint>(idxBottomPoint));
            tris.push_back(static_cast<uint>(circleBeginIdx + (i + 1) % segsCircle));
        }
        ret.triangles.setDrawElementUIntData(std::move(tris));
    }

    ret.triangles.setPrimitiveType(WDPrimitiveSet::PrimitiveType::PT_Triangles);

    // 边线索引
    auto numCircles = (ret.positions.size() - 2) / segsCircle;
    auto span = segsCircle / 4;

    if (ret.positions.size() < WD::NumLimits<byte>::Max)
    {
        std::vector<byte> wireframes;
        wireframes.reserve(segsCircle * 2 + (2 + numCircles) * 2 * 2);

        // 中间一圈的起始索引
        auto middleCircleStartIdx = 0 + segsCircle * (numCircles / 2 + 1);
        for (uint i = 0; i < segsCircle; i++)
        {
            wireframes.push_back(static_cast<byte>(i + middleCircleStartIdx));
            wireframes.push_back(static_cast<byte>((i + 1) % segsCircle + middleCircleStartIdx));
        }

        std::vector<byte> tempIdx;
        tempIdx.reserve(2 + numCircles);

        // 迎面的竖直方向的线
        tempIdx.push_back(static_cast<byte>(ret.positions.size() - 2));
        for (uint i = 0; i < numCircles; i++)
        {
            tempIdx.push_back(static_cast<byte>(i * segsCircle + span));
        }
        tempIdx.push_back(static_cast<byte>(ret.positions.size() - 1));
        for (int i = int(numCircles - 1); i >= 0; i--)
        {
            tempIdx.push_back(static_cast<byte>(i * segsCircle + span * 3));
        }
        auto sz = tempIdx.size();
        for (uint i = 0; i < sz; i++)
        {
            wireframes.push_back(tempIdx[i]);
            wireframes.push_back(tempIdx[(i + 1) % sz]);
        }

        // 侧面的竖直的线
        tempIdx.clear();
        tempIdx.push_back(static_cast<byte>(ret.positions.size() - 2));
        for (uint i = 0; i < numCircles; i++)
        {
            tempIdx.push_back(static_cast<byte>(i * segsCircle));
        }
        tempIdx.push_back(static_cast<byte>(ret.positions.size() - 1));
        for (int i = int(numCircles - 1); i >= 0; i--)
        {
            tempIdx.push_back(static_cast<byte>(i * segsCircle + span * 2));
        }
        sz = tempIdx.size();
        for (uint i = 0; i < sz; i++)
        {
            wireframes.push_back(tempIdx[i]);
            wireframes.push_back(tempIdx[(i + 1) % sz]);
        }
        ret.wireFrames.setDrawElementByteData(std::move(wireframes));
    }
    else if (ret.positions.size() < WD::NumLimits<ushort>::Max)
    {
        std::vector<ushort> wireframes;
        wireframes.reserve(segsCircle * 2 + (2 + numCircles) * 2 * 2);

        // 中间一圈的起始索引
        auto middleCircleStartIdx = 0 + segsCircle * (numCircles / 2 + 1);
        for (uint i = 0; i < segsCircle; i++)
        {
            wireframes.push_back(static_cast<ushort>(i + middleCircleStartIdx));
            wireframes.push_back(static_cast<ushort>((i + 1) % segsCircle + middleCircleStartIdx));
        }

        std::vector<ushort> tempIdx;
        tempIdx.reserve(2 + numCircles);

        // 迎面的竖直方向的线
        tempIdx.push_back(static_cast<ushort>(ret.positions.size() - 2));
        for (uint i = 0; i < numCircles; i++)
        {
            tempIdx.push_back(static_cast<ushort>(i * segsCircle + span));
        }
        tempIdx.push_back(static_cast<ushort>(ret.positions.size() - 1));
        for (int i = int(numCircles - 1); i >= 0; i--)
        {
            tempIdx.push_back(static_cast<ushort>(i * segsCircle + span * 3));
        }
        auto sz = tempIdx.size();
        for (uint i = 0; i < sz; i++)
        {
            wireframes.push_back(tempIdx[i]);
            wireframes.push_back(tempIdx[(i + 1) % sz]);
        }

        // 侧面的竖直的线
        tempIdx.clear();
        tempIdx.push_back(static_cast<ushort>(ret.positions.size() - 2));
        for (uint i = 0; i < numCircles; i++)
        {
            tempIdx.push_back(static_cast<ushort>(i * segsCircle));
        }
        tempIdx.push_back(static_cast<ushort>(ret.positions.size() - 1));
        for (int i = int(numCircles - 1); i >= 0; i--)
        {
            tempIdx.push_back(static_cast<ushort>(i * segsCircle + span * 2));
        }
        sz = tempIdx.size();
        for (uint i = 0; i < sz; i++)
        {
            wireframes.push_back(tempIdx[i]);
            wireframes.push_back(tempIdx[(i + 1) % sz]);
        }
        ret.wireFrames.setDrawElementUShortData(std::move(wireframes));
    }
    else
    {
        std::vector<uint> wireframes;
        wireframes.reserve(segsCircle * 2 + (2 + numCircles) * 2 * 2);

        // 中间一圈的起始索引
        auto middleCircleStartIdx = 0 + segsCircle * (numCircles / 2 + 1);
        for (uint i = 0; i < segsCircle; i++)
        {
            wireframes.push_back(static_cast<uint>(i + middleCircleStartIdx));
            wireframes.push_back(static_cast<uint>((i + 1) % segsCircle + middleCircleStartIdx));
        }

        std::vector<uint> tempIdx;
        tempIdx.reserve(2 + numCircles);

        // 迎面的竖直方向的线
        tempIdx.push_back(static_cast<uint>(ret.positions.size() - 2));
        for (uint i = 0; i < numCircles; i++)
        {
            tempIdx.push_back(static_cast<uint>(i * segsCircle + span));
        }
        tempIdx.push_back(static_cast<uint>(ret.positions.size() - 1));
        for (int i = int(numCircles - 1); i >= 0; i--)
        {
            tempIdx.push_back(static_cast<uint>(i * segsCircle + span * 3));
        }
        auto sz = tempIdx.size();
        for (uint i = 0; i < sz; i++)
        {
            wireframes.push_back(tempIdx[i]);
            wireframes.push_back(tempIdx[(i + 1) % sz]);
        }

        // 侧面的竖直的线
        tempIdx.clear();
        tempIdx.push_back(static_cast<uint>(ret.positions.size() - 2));
        for (uint i = 0; i < numCircles; i++)
        {
            tempIdx.push_back(static_cast<uint>(i * segsCircle));
        }
        tempIdx.push_back(static_cast<uint>(ret.positions.size() - 1));
        for (int i = int(numCircles - 1); i >= 0; i--)
        {
            tempIdx.push_back(static_cast<uint>(i * segsCircle + span * 2));
        }
        sz = tempIdx.size();
        for (uint i = 0; i < sz; i++)
        {
            wireframes.push_back(tempIdx[i]);
            wireframes.push_back(tempIdx[(i + 1) % sz]);
        }
        ret.wireFrames.setDrawElementUIntData(std::move(wireframes));
    }

    ret.wireFrames.setPrimitiveType(WDPrimitiveSet::PrimitiveType::PT_Lines);
    return ret;
}

WD_NAMESPACE_END