#pragma once

#include "StandardPrimitiveCommon.h"

WD_NAMESPACE_BEGIN

/**
* @brief 构造偏心圆台体
*/
class WD_API SnoutBuilder
{
public:
    /**
    * @brief 生成网格 SNOU
    * @param topDiameter 顶面直径
    * @param bottomDiameter 底面直径
    * @param height 高度
    * @param xOffset X轴方向偏移
    * @param yOffset Y轴方向偏移
    */
    static MeshStruct Mesh(float topDiameter
        , float bottomDiameter 
        , float height
        , float xOffset
        , float yOffset
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
    * @brief 生成关键点列表 SNOU
    * @param topDiameter 顶面直径
    * @param bottomDiameter 底面直径
    * @param height 高度
    * @param xOffset X轴方向偏移
    * @param yOffset Y轴方向偏移
    */
    static FKeyPoints KeyPoints(float topDiameter
        , float bottomDiameter 
        , float height
        , float xOffset
        , float yOffset);
    /**
    * @brief 生成交点列表 SNOU
    * @param topDiameter 顶面直径
    * @param bottomDiameter 底面直径
    * @param height 高度
    * @param xOffset X轴方向偏移
    * @param yOffset Y轴方向偏移
    */
    static FVec3Vector IntersectPoints(float topDiameter
        , float bottomDiameter 
        , float height
        , float xOffset
        , float yOffset);
    /**
     * @brief 转换 LSNO参数 到 SNOU参数
     * @param directionA 上方向
     * @param positionA 底面位置
     * @param directionB 顶面偏移方向
     * @param positionB 顶面偏移位置
     * @param distanceToTop 原点到顶面的距离
     * @param distanceToBottom 原点到底面的距离
     * @param topDiameter 顶面直径
     * @param bottomDiameter 底面直径
     * @param offset 顶面在directionB方向上的偏移
     * @param outTransform 输出的变换信息
     * @param outTopDiameter 输出的顶面直径
     * @param outBottomDiameter 输出的底面直径
     * @param outHeight 输出的高度
     * @param outXOffset 输出的X轴方向偏移
     * @param outYOffset 输出的Y轴方向偏移
     * @return 是否转换成功(当参数无效时,将转换失败)
    */
    static bool LSNOToSNOU(const FVec3& directionA
        , const FVec3& positionA
        , const FVec3& directionB
        , const FVec3& positionB
        , float distanceToTop
        , float distanceToBottom
        , float topDiameter
        , float bottomDiameter
        , float offset
        , FMeshTransform& outTransform
        , float& outTopDiameter
        , float& outBottomDiameter
        , float& outHeight
        , float& outXOffset
        , float& outYOffset);
    /**
    * @brief 生成边线 SNOU
    * @param topDiameter 顶面直径
    * @param bottomDiameter 底面直径
    * @param height 高度
    * @param xOffset X轴方向偏移
    * @param yOffset Y轴方向偏移
    */
    static MeshStruct SideLines(float topDiameter
        , float bottomDiameter 
        , float height
        , float xOffset
        , float yOffset
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
    * @brief 生成网格 SNOU
    *   简单网格，顶点只包含位置信息，不包含任何法线、UV相关信息，目的是保证顶点位置没有重合
    * @param topDiameter 顶面直径
    * @param bottomDiameter 底面直径
    * @param height 高度
    * @param xOffset X轴方向偏移
    * @param yOffset Y轴方向偏移
    */
    static MeshStruct SimpleMesh(float topDiameter
        , float bottomDiameter
        , float height
        , float xOffset
        , float yOffset
        , const MeshLODSelection& lodSelection = MeshLODSelection());
};

WD_NAMESPACE_END

