#pragma once

#include "StandardPrimitiveCommon.h"

WD_NAMESPACE_BEGIN

/**
 * @brief 生成放样的曲线数据
*/
class WD_API WDLoftSPline
{
public:
    /**
     * @brief 控制点
    */
    class Point
    {
    public:
        // 控制点的位置
        FVec3 position = FVec3::Zero();
    public:
        Point() 
        {

        }
        template <typename T>
        Point(const TVec3<T>& pos = TVec3<T>::Zero()) 
            : position(FVec3(pos))
        {
        }
    public:
        inline bool operator==(const Point& right) const
        {
            return this->position == right.position;
        }
        inline bool operator!=(const Point& right) const
        {
            return this->position != right.position;
        }
        inline bool operator>(const Point& right) const
        {
            return this->position > right.position;
        }
        inline bool operator<(const Point& right) const
        {
            return this->position < right.position;
        }
        inline bool operator>=(const Point& right) const
        {
            return this->position >= right.position;
        }
        inline bool operator<=(const Point& right) const
        {
            return this->position <= right.position;
        }
    };
    /**
     * @brief 圆弧曲线控制点
    */
    class Curve
    {
    public:
        /**
         * @brief 曲线类型
        */
        enum Type
        {
            // Wrod: "LINE"
            //  指定相邻POINSP之间的直线，如果两个POINSP元素之间没有定义曲线几何，这是默认选项
            CT_Line,
            // Wrod: "RADI"
            //  指定相邻POINSP之间的单个圆弧。圆弧由radius(半径)和conditioning point(调节点)属性定义。
            //  调节点及其前后点定义了圆弧的平面。选择小圆弧(劣弧?)或大圆弧(优弧?)是由哪个圆弧更接近调节点来决定的。
            //  如果 radsetFlag = false,则忽略指定的半径，计算最小半径，使得曲线在相邻的POINSP之间是半圆。
            CT_Radius,
            // Wrod: "THRU"
            //  指定单个圆弧。圆弧由position(位置)属性定义，该属性被解释为曲线上的一个穿过点。穿过点及其前后点定义了圆弧的平面
            CT_Through,
            // Wrod: "BULG"
            //  指定单个圆弧。圆弧由bulge factor(膨胀因子)和conditioning point(调节点)属性定义。
            //  调节点及其前后点定义了圆弧的平面。选择小圆弧(劣弧?)或大圆弧(优弧?)是由膨胀因子的大小来决定的。
            //  膨胀因子的符号决定了圆弧是向调节点弯曲(正膨胀因子)还是远离调节点弯曲（负膨胀因子)。
            CT_BulgeFactor,
            // Wrod: "FILL"
            //  指定一个弧和最多两个直线的组合。曲线由radius(半径)和position(位置)属性定义,其中position设置被解释为圆弧的极点(两端切线的交点)。
            //  极点及其前后点定义了圆弧的平面。如果 radsetFlag = false, 则忽略指定的半径，并计算能够适应极点的最大半径；一个端点三角形确保圆角至少在一个端点上终止。
            CT_Fillet,
            // Wrod: "CENT"
            //  指定一个弧和最多两个直线的组合。曲线由radius(半径),conditioning point(调节点)和position(位置)属性定义，
            //  其中position设置被解释为圆心点。中心点及其前后点顶一个圆弧的平面。选择小圆弧(劣弧?)或大圆弧(优弧?)是由哪个圆弧更接近调节点来决定的；
            //  调节点不需要位于圆弧平面内。如果 radsetFlag = false, 则忽略指定的半径，并计算最大半径，以确保最近的POINSP到圆心位于圆上；圆弧在此点终止。
            CT_Centre,
        };
    public:
        // 曲线类型
        Type    type = Type::CT_Line;
        // 位置
        FVec3   position = FVec3::Zero();
        // 调节点 conditioning point
        FVec3   cPoint = FVec3::Zero();
        // 半径
        float   radius = 0.0f;
        // 半径标记
        bool    radsetFlag = true;
        // 膨胀因子
        float   bulgeFactor = 0.0f;
    public:
        /**
         * @brief 字符串转换到曲线类型枚举
        */
        static Type TypeFromString(const char* str);
    public:
        bool operator==(const Curve& right) const;
        bool operator!=(const Curve& right) const;
        bool operator>(const Curve& right) const;
        bool operator<(const Curve& right) const;
        bool operator>=(const Curve& right) const;
        bool operator<=(const Curve& right) const;
    };
    /**
     * @brief 一条曲线的控制点
     *  control point
    */
    using CPoint = std::variant<Point, Curve>;
    /**
     * @brief 一条曲线的所有控制点
    */
    using CPoints = std::vector<CPoint>;
private:
    CPoints _cPoints;
public:
    WDLoftSPline()
    {
    }
    WDLoftSPline(const WDLoftSPline& right) = default;
    WDLoftSPline(WDLoftSPline&& right) = default;
    WDLoftSPline& operator=(const WDLoftSPline& right) = default;
    WDLoftSPline& operator=(WDLoftSPline&& right) = default;
    ~WDLoftSPline()
    {
    }
public:
    inline bool operator==(const WDLoftSPline& right) const
    {
        return this->_cPoints == right._cPoints;
    }
    inline bool operator!=(const WDLoftSPline& right) const
    {
        return this->_cPoints != right._cPoints;
    }
    inline bool operator>(const WDLoftSPline& right) const
    {
        return this->_cPoints > right._cPoints;
    }
    inline bool operator<(const WDLoftSPline& right) const
    {
        return this->_cPoints < right._cPoints;
    }
    inline bool operator>=(const WDLoftSPline& right) const
    {
        return this->_cPoints >= right._cPoints;
    }
    inline bool operator<=(const WDLoftSPline& right) const
    {
        return this->_cPoints <= right._cPoints;
    }
public:
    /**
     * @brief 添加控制点
    */
    inline void addPoint(const Point& point)
    {
        _cPoints.push_back(point);
    }
    /**
     * @brief 添加圆弧曲线控制点
    */
    inline void addCurve(const Curve& curve)
    {
        _cPoints.push_back(curve);
    }
    /**
     * @brief 设置控制点列表
     * @param cPoints 控制点列表
    */
    inline void setCPoints(const CPoints& cPoints)
    {
        _cPoints = cPoints;
    }
    /**
     * @brief 设置控制点列表
     * @param cPoints 控制点列表
    */
    inline void setCPoints(CPoints&& cPoints)
    {
        _cPoints = std::forward<CPoints>(cPoints);
    }
    /**
     * @brief 获取控制点列表
     * @return 控制点列表
    */
    inline const CPoints& cPoints() const
    {
        return _cPoints;
    }
    /**
     * @brief 清除控制点列表
    */
    inline void clear()
    {
        _cPoints.clear();
    }
    /**
     * @brief 生成曲线顶点
    */
    FVec3Vector vertices(const MeshLODSelection& lodSelection = MeshLODSelection()) const;
};

WD_NAMESPACE_END

