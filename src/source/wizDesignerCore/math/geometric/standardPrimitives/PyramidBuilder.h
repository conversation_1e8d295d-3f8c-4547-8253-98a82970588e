#pragma once

#include "StandardPrimitiveCommon.h"


WD_NAMESPACE_BEGIN

/**
* @brief 构造棱台体
*/
class WD_API PyramidBuilder
{
public:
    /**
    * @brief 生成网格 PYRA
    * @param xTop 顶面X轴方向长度
    * @param yTop 顶面Y轴方向长度
    * @param xBottom 底面X轴方向长度
    * @param yBottom 底面Y轴方向长度
    * @param height 高度
    * @param xOffset X轴方向偏移
    * @param yOffset Y轴方向偏移
    */
    static MeshStruct Mesh(float xTop
        , float yTop
        , float xBottom
        , float yBottom
        , float height
        , float xOffset
        , float yOffset);
    /**
    * @brief 生成关键点列表 PYRA
    * @param xTop 顶面X轴方向长度
    * @param yTop 顶面Y轴方向长度
    * @param xBottom 底面X轴方向长度
    * @param yBottom 底面Y轴方向长度
    * @param height 高度
    * @param xOffset X轴方向偏移
    * @param yOffset Y轴方向偏移
    */
    static FKeyPoints KeyPoints(float xTop
        , float yTop
        , float xBottom
        , float yBottom
        , float height
        , float xOffset
        , float yOffset);
    /**
    * @brief 生成交点列表 PYRA
    * @param xTop 顶面X轴方向长度
    * @param yTop 顶面Y轴方向长度
    * @param xBottom 底面X轴方向长度
    * @param yBottom 底面Y轴方向长度
    * @param height 高度
    * @param xOffset X轴方向偏移
    * @param yOffset Y轴方向偏移
    */
    static FVec3Vector IntersectPoints(float xTop
        , float yTop
        , float xBottom
        , float yBottom
        , float height
        , float xOffset
        , float yOffset);
    /**
     * @brief 元件库棱台体转化为基本体棱台体
     * @param xAxis X轴向
     * @param xPosition X位置
     * @param yAxis Y轴向
     * @param yPosition Y位置
     * @param zAxis Z轴向
     * @param zPosition Z位置
     * @param xTop 顶面X轴方向长度
     * @param yTop 顶面Y轴方向长度
     * @param xBottom 底面X轴方向长度
     * @param yBottom 底面Y轴方向长度
     * @param distanceToTop 到底面的距离
     * @param distanceToBottom 到顶面的距离
     * @param xOffset X轴方向偏移
     * @param yOffset Y轴方向偏移
     * @param outTransform 输出的变换信息
     * @param outXTop 输出顶面X轴方向长度
     * @param outYTop 输出顶面Y轴方向长度
     * @param outXBottom 输出底面面X轴方向长度
     * @param outYBottom 输出底面面Y轴方向长度
     * @param outHeight 输出高度
     * @param outXOffset 输出X轴方向偏移
     * @param outYOffset 输出Y轴方向偏移
     * @return 
    */
    static bool LPYRToPYRA(const FVec3& xAxis
        , const FVec3& xPosition
        , const FVec3& yAxis
        , const FVec3& yPosition
        , const FVec3& zAxis
        , const FVec3& zPosition
        , float xTop
        , float yTop
        , float xBottom
        , float yBottom
        , float distanceToTop
        , float distanceToBottom
        , float xOffset
        , float yOffset
        , FMeshTransform& outTransform
        , float& outXTop
        , float& outYTop
        , float& outXBottom
        , float& outYBottom
        , float& outHeight
        , float& outXOffset
        , float& outYOffset);
    /**
    * @brief 生成网格
    *   简单网格，顶点只包含位置信息，不包含任何法线、UV相关信息，目的是保证顶点位置没有重合
    * @param xTop 顶面X轴方向长度
    * @param yTop 顶面Y轴方向长度
    * @param xBottom 底面X轴方向长度
    * @param yBottom 底面Y轴方向长度
    * @param height 高度
    * @param xOffset X轴方向偏移
    * @param yOffset Y轴方向偏移
    */
    static MeshStruct SimpleMesh(float xTop
        , float yTop
        , float xBottom
        , float yBottom
        , float height
        , float xOffset
        , float yOffset);
};

WD_NAMESPACE_END

