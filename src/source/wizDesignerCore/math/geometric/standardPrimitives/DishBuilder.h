#pragma once

#include "StandardPrimitiveCommon.h"


WD_NAMESPACE_BEGIN

/**
* @brief 构造圆盘体
*/
class WD_API DishBuilder
{
public:
    /**
    * @brief 生成网格 DISH
    * @param diameter 直径,指的是圆盘底部圆的直径
    * @param radius 半径, 如果值为0,使用球体算法生成圆盘;如果值为非0,使用椭球体算法生成圆盘
    * @param height 高度,指圆盘高度
    */
    static MeshStruct Mesh(float diameter
        , float radius
        , float height
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
    * @brief 生成关键点列表 DISH
    * @param diameter 直径,指的是圆盘底部圆的直径
    * @param radius 半径, 如果值为0,使用球体算法生成圆盘;如果值为非0,使用椭球体算法生成圆盘
    * @param height 高度,指圆盘高度
    */
    static FKeyPoints KeyPoints(float diameter
        , float radius
        , float height);
    /**
    * @brief 生成交点列表 DISH
    * @param diameter 直径,指的是圆盘底部圆的直径
    * @param radius 半径, 如果值为0,使用球体算法生成圆盘;如果值为非0,使用椭球体算法生成圆盘
    * @param height 高度,指圆盘高度
    */
    static FVec3Vector IntersectPoints(float diameter
        , float radius
        , float height);
    /**
     * @brief 转换 SDSH参数 到 DISH参数
     * @param direction 圆盘的上方向
     * @param position 圆盘的底部位置
     * @param distance 圆盘底部位置在圆盘的上方向的偏移量
     * @param height 高度,指圆盘高度
     * @param radius 半径, 如果值为0,使用球体算法生成圆盘;如果值为非0,使用椭球体算法生成圆盘
     * @param diameter 直径,指的是圆盘底部圆的直径
     * @param outTransform 输出的变换信息
     * @param outDiameter 输出的直径,指的是圆盘底部圆的直径
     * @param outRadius 输出的半径, 如果值为0,使用球体算法生成圆盘;如果值为非0,使用椭球体算法生成圆盘
     * @param outHeight 输出的高度,指圆盘高度
     * @return 是否转换成功(当参数无效时,将转换失败)
    */
    static bool SDSHToDISH(const FVec3& direction
        , const FVec3& position
        , float distance
        , float height
        , float radius
        , float diameter
        , FMeshTransform& outTransform
        , float& outDiameter
        , float& outRadius
        , float& outHeight);
    /**
    * @brief 生成网格 DISH
    *   简单网格，顶点只包含位置信息，不包含任何法线、UV相关信息，目的是保证顶点位置没有重合
    * @param diameter 直径,指的是圆盘底部圆的直径
    * @param radius 半径, 如果值为0,使用球体算法生成圆盘;如果值为非0,使用椭球体算法生成圆盘
    * @param height 高度,指圆盘高度
    */
    static MeshStruct SimpleMesh(float diameter
        , float radius
        , float height
        , const MeshLODSelection& lodSelection = MeshLODSelection());
};

WD_NAMESPACE_END

