#pragma once


#include "TArcCurve.h"
#include "TCatmullRomCurve.h"
#include "TCubicBezierCurve2.h"
#include "TCubicBezierCurve3.h"
#include "TPathCurve2.h"
#include "TPathCurve3.h"
#include "TEllipseCurve.h"
#include "TLineCurve2.h"
#include "TLineCurve3.h"
#include "TNURBSCurve.h"
#include "TQuadraticBezierCurve2.h"
#include "TQuadraticBezierCurve3.h"
#include "TSplineCurve.h"

WD_NAMESPACE_BEGIN

/**
 * @brief 2D直线
*/
using DLineCurve2 = TLineCurve2<double>;
using FLineCurve2 = TLineCurve2<float>;
/**
 * @brief 3D直线
*/
using DLineCurve3 = TLineCurve3<double>;
using FLineCurve3 = TLineCurve3<float>;
/**
 * @brief 圆弧曲线(2D)
*/
using DArcCurve = TArcCurve<double>;
using FArcCurve = TArcCurve<float>;
/**
 * @brief 椭圆弧曲线(2D)
*/
using DEllipseCurve = TEllipseCurve<double>;
using FEllipseCurve = TEllipseCurve<float>;

/**
 * @brief 2D二次贝塞尔曲线
*/
using DQuadraticBezierCurve2 = TQuadraticBezierCurve2<double>;
using FQuadraticBezierCurve2 = TQuadraticBezierCurve2<float>;
/**
 * @brief 3D二次贝塞尔曲线
*/
using DQuadraticBezierCurve3 = TQuadraticBezierCurve3<double>;
using FQuadraticBezierCurve3 = TQuadraticBezierCurve3<float>;

/**
 * @brief 2D三次贝塞尔曲线
*/
using DCubicBezierCurve2 = TCubicBezierCurve2<double>;
using FCubicBezierCurve2 = TCubicBezierCurve2<float>;
/**
 * @brief 3D三次贝塞尔曲线
*/
using DCubicBezierCurve3 = TCubicBezierCurve3<double>;
using FCubicBezierCurve3 = TCubicBezierCurve3<float>;

/**
 * @brief 2D样条线
*/
using DSplineCurve = TSplineCurve<double>;
using FSplineCurve = TSplineCurve<float>;

/**
 * @brief 3D Catmull-Rom样条线
*/
using DCatmullRomCurve  = TCatmullRomCurve<double>;
using FCatmullRomCurve  = TCatmullRomCurve<float>;

/**
 * @brief NURBS 曲线(3D)
*/
using DNURBSCurve = TNURBSCurve<double>;
using FNURBSCurve = TNURBSCurve<float>;

/**
 * @brief 2D路径曲线
*/
using DPathCurve2 = TPathCurve2<double>;
using FPathCurve2 = TPathCurve2<float>;

/**
 * @brief 3D路径曲线
*/
using DPathCurve3 = TPathCurve3<double>;
using FPathCurve3 = TPathCurve3<float>;

WD_NAMESPACE_END


