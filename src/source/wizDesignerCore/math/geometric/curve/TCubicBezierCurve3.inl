#pragma once

#include "TCubicBezierCurve3.h"

WD_NAMESPACE_BEGIN

/**
* @brief 3D三次贝塞尔曲线
*/
template<class T>
TCubicBezierCurve3<T>::TCubicBezierCurve3(const Point& c1
    , const Point& c2
    , const Point& c3
    , const Point& c4)
{
    this->c1 = c1;
    this->c2 = c2;
    this->c3 = c3;
    this->c4 = c4;
}

template<class T>
typename TCubicBezierCurve3<T>::Point TCubicBezierCurve3<T>::point(T t) const
{
    return Point(
        BezierUtils<T>::CalcCubicBezier(t, this->c1.x, this->c2.x, this->c3.x, this->c4.x),
        BezierUtils<T>::CalcCubicBezier(t, this->c1.y, this->c2.y, this->c3.y, this->c4.y),
        BezierUtils<T>::CalcCubicBezier(t, this->c1.z, this->c2.z, this->c3.z, this->c4.z)
    );
}

template<class T>
typename TCubicBezierCurve3<T>::Point TCubicBezierCurve3<T>::tangent(T t) const
{
    return Utils::ComputeTangent(*this, t);
}

template<class T>
typename TCubicBezierCurve3<T>::Points TCubicBezierCurve3<T>::points(size_t divisions) const
{
    return Utils::ComputePoints(*this, divisions);
}

template<class T>
T TCubicBezierCurve3<T>::length(size_t divisions) const
{
    return Utils::ComputeLength(*this, divisions);
}

template<class T>
typename TCubicBezierCurve3<T>::Lengths TCubicBezierCurve3<T>::lengths(size_t divisions) const
{
    return Utils::ComputeLengths(*this, divisions);
}

template<class T>
typename TCubicBezierCurve3<T>::Point TCubicBezierCurve3<T>::pointAt(T u, const Lengths& lens) const
{
    T t = Utils::UToMapping(u, lens);
    return this->point(t);
}

template<class T>
typename TCubicBezierCurve3<T>::Point TCubicBezierCurve3<T>::tangentAt(T u, const Lengths& lens) const
{
    T t = Utils::UToMapping(u, lens);
    return this->tangent(t);
}


WD_NAMESPACE_END

