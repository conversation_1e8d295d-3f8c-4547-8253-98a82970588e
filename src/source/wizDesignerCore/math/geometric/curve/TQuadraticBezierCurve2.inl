#pragma once

#include "TQuadraticBezierCurve2.h"

WD_NAMESPACE_BEGIN

template<class T>
TQuadraticBezierCurve2<T>::TQuadraticBezierCurve2(const Point& c1
        , const Point& c2
        , const Point& c3)
    {
        this->c1 = c1;
        this->c2 = c2;
        this->c3 = c3;
    }

template<class T>
typename TQuadraticBezierCurve2<T>::Point TQuadraticBezierCurve2<T>::point(T t) const
{
    return Point(
        BezierUtils<T>::CalcQuadraticBezier(t, this->c1.x, this->c2.x, this->c3.x),
        BezierUtils<T>::CalcQuadraticBezier(t, this->c1.y, this->c2.y, this->c3.y)
    );
}

template<class T>
typename TQuadraticBezierCurve2<T>::Point TQuadraticBezierCurve2<T>::tangent(T t) const
{
    return Utils::ComputeTangent(*this, t);
}

template<class T>
typename TQuadraticBezierCurve2<T>::Points TQuadraticBezierCurve2<T>::points(size_t divisions) const
{
    return Utils::ComputePoints(*this, divisions);
}

template<class T>
T TQuadraticBezierCurve2<T>::length(size_t divisions) const
{
    return Utils::ComputeLength(*this, divisions);
}

template<class T>
typename TQuadraticBezierCurve2<T>::Lengths TQuadraticBezierCurve2<T>::lengths(size_t divisions) const
{
    return Utils::ComputeLengths(*this, divisions);
}

template<class T>
typename TQuadraticBezierCurve2<T>::Point TQuadraticBezierCurve2<T>::pointAt(T u, const Lengths& lens) const
{
    T t = Utils::UToMapping(u, lens);
    return this->point(t);
}

template<class T>
typename TQuadraticBezierCurve2<T>::Point TQuadraticBezierCurve2<T>::tangentAt(T u, const Lengths& lens) const
{
    T t = Utils::UToMapping(u, lens);
    return this->tangent(t);
}

WD_NAMESPACE_END

