#pragma once

#include "TLineCurve3.h"
#include "TCubicBezierCurve3.h"
#include "TQuadraticBezierCurve3.h"
#include "TCatmullRomCurve.h"

WD_NAMESPACE_BEGIN

/**
* @brief 3D路径曲线
*/
template <class T>
class TPathCurve3
{
public:
    using _This         = TPathCurve3<T>;
    using Point         = TVec3<T>;
    using Points        = std::vector<Point>;
    using Lengths       = std::vector<T>;
    using Utils         = TCurveUtils<_This>;
    using FrenetFrame   = typename Utils::FrenetFrame;
    using FrenetFrames  = std::vector<FrenetFrame>;
private:
    using Line              = TLineCurve3<T>;
    using QuadraticBezier   = TQuadraticBezierCurve3<T>;
    using CubicBezier       = TCubicBezierCurve3<T>;
    using CatmullRom        = TCatmullRomCurve<T>;
    /**
     * @brief 曲线对象
    */
    using Curve         = std::variant<
          Line
        , QuadraticBezier
        , CubicBezier
        , CatmullRom>;
    /**
     * @brief 曲线对象的类型, 与Curve中的index对应
    */
    enum CurveType
    {
        CT_Line = 0,
        CT_QuadraticBezier,
        CT_CubicBezier,
        CT_CatmullRom,
    };
    /**
     * @brief 曲线对象以及曲线的细分段数数据
    */
    using DCurve    = std::pair<Curve, uint>;
    /**
     * @brief 曲线列表
    */
    using Curves    = std::vector<DCurve>; 
private:
    // 曲线列表
    Curves  _curves;
    // 当前点
    Point   _currentPoint;
public:
    TPathCurve3(const Points& points = Points());
public:
    /**
     * @brief 指定当前点的位置
     * @param point 位置点
    */
    inline _This& moveTo(const Point& point);
    /**
     * @brief 从当前点开始，绘制一条直线到指定的point位置
     * @param point 位置点
    */
    inline _This& lineTo(const Point& point);
    /**
     * @brief 指定一组点, 从当前点开始, 依次连接相邻两个点为直线, 直到最后一个点
     * @param points 点列表
    */
    _This& linesTo(const Points& points);
    /**
     * @brief 使用当前点, cPt, ePt构造一个二次贝塞尔曲线来绘制路径
     * @param cPt 控制点
     * @param ePt 控制点
     * @param divisions 分割段数
    */
    _This& quadraticCurveTo(const Point& cPt, const Point& ePt
        , int divisions = 20);
    /**
     * @brief 使用当前点, cPt1, cPt2, ePt构造一个三次次贝塞尔曲线来绘制路径
     * @param cPt1 控制点
     * @param cPt2 控制点
     * @param ePt 控制点
     * @param divisions 分割段数
    */
    _This& bezierCurveTo(const Point& cPt1, const Point& cPt2, const Point& ePt
        , int divisions = 20);
    /**
     * @brief 使用当前点以及指定的点列表构造样条线来绘制路径
     * @param pts 点列表
     * @param divisions 分割段数
    */
    _This& catmullRomThru(const Points& pts
        , int divisions = 20);
public:
    /**
     * @brief 获取顶点列表,这里根据加入的每个曲线的细分段数计算顶点个数
    */
    Points points() const;
    /**
     * @brief 获取长度,这里根据加入的每个曲线的长度计算总长度
    */
    T length() const;
    /**
     * @brief 获取所有曲线的长度累加列表
     *  即: 假设曲线列表为: {c0, c1, c2, ...cn}
     *     则返回的长度列表为: {c0.length(), c0.length() + c1.length(), ..., c0.lenght() + ... + cn.length()}
    */
    Lengths curveLengths() const;
public:
    /**
     * @brief 获取对应参数t值位置的点
     * @param t 参数t,取值范围:[0,1]
    */
    Point point(T t) const;
    /**
     * @brief 获取对应参数t值位置的切线
     * @param t 参数t,取值范围:[0,1]
    */
    Point tangent(T t) const;
    /**
     * @brief 指定分段数获取顶点列表
     * @param divisions 分段数
    */
    Points points(size_t divisions);
    /**
     * @brief 计算曲线长度
     * @param divisions 分段数，某些曲线只能用分段近似法计算长度，因此分段数越高，计算的曲线长度越精确
    */
    T length(size_t divisions) const;
    /**
     * @brief 计算开始点到后续每一个点的长度, 因此Lengths::back()为曲线的长度
     * @param divisions 分段数
    */
    Lengths lengths(size_t divisions) const;
    /**
     * @brief 给定参数u(取值范围[0, 1]),来根据等距离重新计算为参数t(取值范围[0,1]),并且使得相同增量的∆t总是能计算到在曲线上等距离的点
     *   eg:
     *      使用一组参数: uArray = {0.0, 0.2, 0.4, 0.6, 0.8, 1.0};
     *      获取到的一组点: ptArray = {p0, p1, p2, p3, p4, p5};
     *      这些点中,两两相邻点之前的曲线距离相等, 即 curveDistance(p0,p1) == curveDistance(p1, p2) == ... == curveDistance(p4, p5);
     * @param u 参数u, 取值范围[0, 1]
     * @param lens 开始点到后续每一个点的长度, 使用 lengths(divisions) 计算得到
     * @return 给定的参数u对应位置的顶点
    */
    Point pointAt(T u, const Lengths& lens) const;
    /**
     * @brief 给定参数u(取值范围[0, 1]),来根据等距离重新计算为参数t(取值范围[0,1]),再使用参数t获取对应参数t位置的切线
     * @param u 参数u, 取值范围[0, 1]
     * @param lens 开始点到后续每一个点的长度, 使用 lengths(divisions) 计算得到
     * @return  给定的参数u对应位置的切线
    */
    Point tangentAt(T u, const Lengths& lens) const;
private:
    // 获取曲线的类型
    static CurveType GetCurveType(const Curve& curve);
    // 计算曲线长度
    static T CurveLength(const Curve& curve, size_t divisions);
    // 计算曲线长度列表
    static Lengths CurveLengths(const Curve& curve, size_t divisions);
    // 获取曲线的点列表
    static Points CurvePoints(const Curve& curve, size_t divisions);
    // 获取曲线上的点
    static Point CurvePoint(const Curve& curve, T t);
    // 获取曲线上的点
    static Point CurvePointAt(const Curve& curve, T u, const Lengths& lens);
};

WD_NAMESPACE_END

#include "TPathCurve3.inl"