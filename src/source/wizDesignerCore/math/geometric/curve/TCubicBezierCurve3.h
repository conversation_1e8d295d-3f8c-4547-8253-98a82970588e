#pragma once

#include "TCurvesUtils.hpp"

WD_NAMESPACE_BEGIN

/**
* @brief 3D三次贝塞尔曲线
*/
template<class T>
class TCubicBezierCurve3
{
public:
    using _This         = TCubicBezierCurve3<T>;
    using Point         = TVec3<T>;
    using Points        = std::vector<Point>;
    using Lengths       = std::vector<T>;
    using Utils         = TCurveUtils<_This>;
    using FrenetFrame   = typename Utils::FrenetFrame;
    using FrenetFrames  = std::vector<FrenetFrame>;
private:
    // 控制点1
    Point c1;
    // 控制点2
    Point c2;
    // 控制点3
    Point c3;
    // 控制点4
    Point c4;
public:
    TCubicBezierCurve3(const Point& c1 = Point()
        , const Point& c2 = Point()
        , const Point& c3 = Point()
        , const Point& c4 = Point());
public:
    /**
     * @brief 获取对应参数t值位置的点
     * @param t 参数t,取值范围:[0,1]
    */
    Point point(T t) const;
    /**
     * @brief 获取对应参数t值位置的切线
     * @param t 参数t,取值范围:[0,1]
    */
    Point tangent(T t) const;
    /**
     * @brief 指定曲线分段数,获取顶点列表
     * @param divisions 分段数
     * @return 根据分段数计算得到的顶点列表, 其中: 列表长度(顶点个数) = divisions + 1
    */
    Points points(size_t divisions) const;
    /**
     * @brief 计算曲线长度
     * @param divisions 分段数，某些曲线只能用分段近似法计算长度，因此分段数越高，计算的曲线长度越精确
    */
    T length(size_t divisions) const;
    /**
     * @brief 计算开始点到后续每一个点的长度, 因此Lengths::back()为曲线的长度
     * @param divisions 分段数
    */
    Lengths lengths(size_t divisions) const;
    /**
     * @brief 给定参数u(取值范围[0, 1]),来根据等距离重新计算为参数t(取值范围[0,1]),并且使得相同增量的∆t总是能计算到在曲线上等距离的点
     *   eg:
     *      使用一组参数: uArray = {0.0, 0.2, 0.4, 0.6, 0.8, 1.0};
     *      获取到的一组点: ptArray = {p0, p1, p2, p3, p4, p5};
     *      这些点中,两两相邻点之前的曲线距离相等, 即 curveDistance(p0,p1) == curveDistance(p1, p2) == ... == curveDistance(p4, p5);
     * @param u 参数u, 取值范围[0, 1]
     * @param lens 开始点到后续每一个点的长度, 使用 lengths(divisions) 计算得到
     * @return 给定的参数u对应位置的顶点
    */
    Point pointAt(T u, const Lengths& lens) const;
    /**
     * @brief 给定参数u(取值范围[0, 1]),来根据等距离重新计算为参数t(取值范围[0,1]),再使用参数t获取对应参数t位置的切线
     * @param u 参数u, 取值范围[0, 1]
     * @param lens 开始点到后续每一个点的长度, 使用 lengths(divisions) 计算得到
     * @return  给定的参数u对应位置的切线
    */
    Point tangentAt(T u, const Lengths& lens) const;
};


WD_NAMESPACE_END


#include "TCubicBezierCurve3.inl"