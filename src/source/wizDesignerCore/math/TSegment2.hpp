#pragma once

#include "TVec2.hpp"
#include "TAabb2.hpp"

WD_NAMESPACE_BEGIN

/**
* @brief 2D线段
*   指定起点和终点构成一条线段
*/
template <typename T>
class TSegment2
{
    static_assert(std::is_arithmetic_v<T>, "模板参数类型必须是算数类型!");
public:
    using ValueType     = T;
    using value_type    = ValueType;
    using SizeType      = size_t;
    using size_type     = SizeType;
public:
    /**
    * @brief 数据个数
    */
    static constexpr size_t Size = 2;
public:
    // 线段起点
    TVec2<T> start;
    // 线段终点
    TVec2<T> end;
public:
    /**
     * @brief 构造
    */
    inline TSegment2()
    {
    }
    /**
     * @brief 构造
    */
    inline TSegment2(const TSegment2<T>& right)
    {
        this->start = right.start;
        this->end = right.end;
    }
    /**
     * @brief 构造
    */
    inline TSegment2(const TVec2<T>& start, const TVec2<T>& end)
    {
        this->start = start;
        this->end = end;
    }
    /**
     * @brief 构造
    */
    inline TSegment2(T sx, T sy, T ex, T ey)
        : start(sx, sy)
        , end(ex, ey)
    {
    }
    /**
     * @brief 构造
    */
    template<class U>
    inline explicit TSegment2(const TSegment2<T>& right)
    {
        this->start = TVec2<T>(right.start);
        this->end = TVec2<T>(right.end);
    }
public:
    /**
    * @brief 是否有效的线段
    */
    inline bool isVaild(const T& e = NumLimits<T>::Epsilon) const
    {
        return TSegment2<T>::IsSegment(this->start, this->end, e);
    }
    /**
    * @brief 获取起始点到终止点的方向向量(单位向量)
    */
    inline TVec2<T> direction() const
    {
        return TSegment2<T>::DeltaNormal(this->start, this->end);
    }
    /**
    * @brief 获取中心点
    */
    inline TVec2<T> center() const
    {
        return (this->start + this->end) / T(2);
    }
    /**
    * @brief 获取从 start 到 end 的方向向量(非单位向量)
    */
    inline TVec2<T> delta() const
    {
        return TSegment2<T>::Delta(this->start, this->end);
    }
    /**
    * @brief 获取长度的平方
    */
    inline T lengthSq()const
    {
        return TVec2<T>::DistanceSq(this->start, this->end);
    }
    /**
    * @brief 获取长度
    */
    inline T length() const
    {
        return TVec2<T>::Distance(this->start, this->end);
    }
public:
    /**
    * @brief 根据参数 t 获取线段上的点 [t >= 0 && t <= 1]
    *   当 t = 0 时， 结果点为 this->start
    *   当 t = 1 时， 结果点为 this->end
    */
    inline TVec2<T> at(T t) const
    {
        return TSegment2<T>::At(t, this->start, this->end);
    }
    /**
    * @brief 判断点是否在线段上
    */
    inline bool contains(const TVec2<T>& point, const T& e = NumLimits<T>::Epsilon) const
    {
        return TSegment2<T>::Contains(point, this->start, this->end, e);
    }
    /**
    * @brief 计算线段上到point的最近点的参数t
    * @param clampToSegment 是否将结果限制在 [0,1] 之间
    */
    inline T closestPointToPointParameter(const TVec2<T>& point, bool clampToSegment = true) const
    {
        return TSegment2<T>::ClosestPointToPointParameter(point, this->start, this->end, clampToSegment);
    }
    /**
    * @brief 计算线段上到point的最近点
    * @param clampToSegment 是否将结果限制在 线段的起点和终点之间
    */
    inline TVec2<T> closestPointToPoint(const TVec2<T>& point, bool clampToSegment = true) const
    {
        return TSegment2<T>::ClosestPointToPoint(point, this->start, this->end, clampToSegment);
    }
    /**
       * @brief 线段与包围盒相交 (Slabs Method 方式)
       * @param  startPos 线段的端点
       * @param  endPos 线段的端点
       * @param  aabb aabb包围盒
       * @return 返回值分别表示:
       *   - 交点个数: 取值范围[0,2]
       *       - = 0 时,表示不相交
       *       - = 1 时,表示只有一个交点(或者两个交点重合)
       *       - = 2 时,表示有两个交点(或者两个交点重合)
       *   - 近交点距离: 当值 < 0 时，表示无效
       *       - 交点个数 = 0 时,该值无效
       *       - 交点个数 >= 1 时,数组的第一个值
       *   - 远交点距离: 当值 < 0 时，表示无效
       *       - 交点个数 = 0 时,该值无效
       *       - 交点个数 >= 1 时,数组的第二个值
    */
    inline std::pair<uint, std::pair<T, T> >  intersect(const TAabb2<T>& aabb) const
    {
        return TSegment2<T>::Intersect(start, end, aabb);
    }
public:
    /**
    * @brief 下标访问, 依次为[startPoint, endPoint]
    */
    inline TVec2<T>& operator[](size_t i)
    {
        TVec2<T>* p = (TVec2<T>*)(&this->start);
        return *(p + i);
    }
    /**
    * @brief 下标访问, 依次为[startPoint, endPoint]
    */
    inline const TVec2<T>& operator[](size_t i) const
    {
        const TVec2<T>* p = (const TVec2<T>*)(&this->start);
        return *(p + i);
    }
    /**
    * @brief 赋值运算
    */
    inline TSegment2<T>& operator=(const TSegment2<T>& right)
    {
        this->start = right.start;
        this->end   = right.end;
        return *this;
    }
    /**
    * @brief == 运算
    */
    friend inline bool operator==(const TSegment2<T>& left, const TSegment2<T>& right)
    {
        return (left.start == right.start) && (left.end == right.end);
    }
    /**
    * @brief != 运算
    */
    friend inline bool operator!=(const TSegment2<T>& left, const TSegment2<T>& right)
    {
        return (left.start != right.start) || (left.end != right.end);
    }
public:
    /**
    * @brief 判断两点是否能构成线段
    */
    static inline bool IsSegment(const TVec2<T>& start, const TVec2<T>& end, const T& e = NumLimits<T>::Epsilon)
    {
        return TVec2<T>::DistanceSq(start, end) > e;
    }
    /**
    * @brief 根据参数 t 获取两点构成的线段上的点 [t >= 0 && t <= 1]
    *   当 t = 0 时， 结果点为 start
    *   当 t = 1 时， 结果点为 end
    */
    static inline TVec2<T> At(T t, const TVec2<T>& start, const TVec2<T>& end)
    {
        TVec2<T> v = TSegment2<T>::Delta(start, end);
        return start + (v * t);
    }
    /**
    * @brief 判断点是否在线段上
    */
    static bool Contains(const TVec2<T>& point
        , const TVec2<T>& start
        , const TVec2<T>& end
        , const T& e = NumLimits<T>::Epsilon)
    {
        const TVec2<T> nor  = TSegment2<T>::DeltaNormal(start, end);
        const TVec2<T> v    = (point - start);
        const TVec2<T> norV = v.normalized();
        if (!TVec2<T>::InTheSameDirection(nor, norV, e))
            return false;
        return v.lengthSq() <= TVec2<T>::DistanceSq(start, end) + e;
    }
    /**
    * @brief 计算两点构成的线段上到point的最近点的参数t
    * @param clampToLine 是否将结果限制在 [0,1] 之间
    */
    static T ClosestPointToPointParameter(const TVec2<T>& point
        , const TVec2<T>& start, const TVec2<T>& end
        , bool clampToLine = true)
    {
        const TVec2<T> startP     = point - start;
        const TVec2<T> startEnd   = end - start;

        const T startEnd2         = TVec2<T>::Dot(startEnd, startEnd);
        const T startEnd_startP   = TVec2<T>::Dot(startEnd, startP);

        const T t   = startEnd_startP / startEnd2;

        if (clampToLine)
            return Clamp(t, T(0), T(1));
        else
            return t;
    }
    /**
    * @brief 计算两点构成的线段上到point的最近点
    * @param clampToLine 是否将结果限制在 线段的起点和终点之间
    */
    static inline TVec2<T> ClosestPointToPoint(const TVec2<T>& point
        , const TVec2<T>& start, const TVec2<T>& end
        , bool clampToLine = true)
    {
        const T t = TSegment2<T>::ClosestPointToPointParameter(point, start, end, clampToLine);
        return TSegment2<T>::At(t, start, end);
    }
    /**
       * @brief 线段与包围盒相交 (Slabs Method 方式)
       * @param  startPos 线段的端点
       * @param  endPos 线段的端点
       * @param  aabb aabb包围盒
       * @return 返回值分别表示:
       *   - 交点个数: 取值范围[0,2]
       *       - = 0 时,表示不相交
       *       - = 1 时,表示只有一个交点(或者两个交点重合)
       *       - = 2 时,表示有两个交点(或者两个交点重合)
       *   - 近交点距离: 当值 < 0 时，表示无效
       *       - 交点个数 = 0 时,该值无效
       *       - 交点个数 >= 1 时,数组的第一个值
       *   - 远交点距离: 当值 < 0 时，表示无效
       *       - 交点个数 = 0 时,该值无效
       *       - 交点个数 >= 1 时,数组的第二个值
    */
    static std::pair<uint, std::pair<T, T> >  Intersect(const TVec2<T>& startPos
        , const TVec2<T>& endPos
        , const TAabb2<T>& aabb)
    {
        T tmin;
        T tmax;
        T tymin;
        T tymax;

        if (aabb.contains(startPos) && aabb.contains(endPos))
        {
            return std::make_pair(0, std::make_pair(T(-1), T(-1)));
        }

        const auto direction = endPos - startPos;

        const T invdirx = T(1) / direction.x;
        const T invdiry = T(1) / direction.y;

        const TVec2<T>& tOrigin = startPos;

        if (invdirx >= T(0))
        {
            tmin = (aabb.min.x - tOrigin.x) * invdirx;
            tmax = (aabb.max.x - tOrigin.x) * invdirx;

        }
        else
        {
            tmin = (aabb.max.x - tOrigin.x) * invdirx;
            tmax = (aabb.min.x - tOrigin.x) * invdirx;
        }

        if (invdiry >= 0)
        {
            tymin = (aabb.min.y - tOrigin.y) * invdiry;
            tymax = (aabb.max.y - tOrigin.y) * invdiry;
        }
        else
        {
            tymin = (aabb.max.y - tOrigin.y) * invdiry;
            tymax = (aabb.min.y - tOrigin.y) * invdiry;
        }

        if ((tmin > tymax) || (tymin > tmax))
            return std::make_pair(0, std::make_pair(T(-1), T(-1)));

        // These lines also handle the case where tmin or tmax is NaN
        // (result of 0 * Infinity). x !== x returns true if x is NaN

        if (tymin > tmin || tmin != tmin)
            tmin = tymin;

        if (tymax < tmax || tmax != tmax)
            tmax = tymax;

        //return point closest to the ray (positive side)

        if (tmax < T(0) || tmin > T(1))
            return std::make_pair(0, std::make_pair(T(-1), T(-1)));
        if (tmin < T(0) || tmax > T(1))
            return std::make_pair(1, std::make_pair(tmax, tmax));

        return std::make_pair(2, std::make_pair(tmin, tmax));
    }
    /**
     * @brief 计算点pos是否在有向线段(start, end)所构成直线的右侧
     * @param start 线段起点
     * @param end 线段终点
     * @param pos 要计算的点
     * @return 在右侧(或落在线段所在直线上)时, 返回true
     */
    static inline bool IsRight(const TVec2<T>& start, const TVec2<T>& end, const TVec2<T>& pos
        , const T& e = NumLimits<T>::Epsilon)
    {
        return (end.x - start.x) * (pos.y - start.y) - (end.y - start.y) * (pos.x - start.x) >= e;
    }
private:
    static inline TVec2<T> Delta(const TVec2<T>& start, const TVec2<T>& end)
    {
        return end - start;
    }
    static inline TVec2<T> DeltaNormal(const TVec2<T>& start, const TVec2<T>& end)
    {
        return TSegment2<T>::Delta(start, end).normalized();
    }
public:
    /**
    * @brief 转换到字符串
    */
    inline char* toString(char* buf) const
    {
        PrintArray2D<Size>(buf
            , this->start.x, this->start.y
            , this->end.x, this->end.y
        );
        return buf;
    }
    /**
    * @brief 转换到字符串
    */
    inline std::string toString() const
    {
        char buf[256] = { 0 };
        return this->toString(buf);
    }
    /**
    * @brief 从字符串转换
    */
    inline TSegment2<T>& fromString(const char* str, bool* bOk = nullptr)
    {
        bool bRet = ScanArray2D<Size>(str
            , this->start.x, this->start.y
            , this->end.x, this->end.y
        );
        SetValueToBooleanPtr(bOk, bRet);
        return *this;
    }
    /**
    * @brief 从字符串转换
    */
    inline TSegment2<T>& fromString(const std::string& str, bool* bOk = nullptr)
    {
        this->fromString(str.c_str(), bOk);
        return *this;
    }
    /**
    * @brief 从字符串转换
    */
    static inline TSegment2<T> FromString(const char* str, bool* bOk = nullptr)
    {
        TSegment2<T> r;
        r.fromString(str, bOk);
        return r;
    }
    /**
    * @brief 从字符串转换
    */
    static inline TSegment2<T> FromString(const std::string& str, bool* bOk = nullptr)
    {
        return TSegment2<T>::FromString(str.c_str(), bOk);
    }
};


template <class T>
using TSegment2Vector = std::vector<TSegment2<T> >;

template <class T, size_t Size>
using TSegment2Array = std::array<TSegment2<T>, Size>;

WD_NAMESPACE_END
