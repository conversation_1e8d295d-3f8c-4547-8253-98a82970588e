#pragma once

#include "TVec3.hpp"
#include "TTriangle3.hpp"

WD_NAMESPACE_BEGIN

/**
* @brief 直线
*   L = P + t * D  // 其中 P为直线上任意一点， t为直线上点的参数(t∈[-∞,+∞]), D为直线的方向向量
*/
template <typename T>
class TLine3
{
    static_assert(std::is_arithmetic_v<T>, "模板参数类型必须是算数类型!");
public:
    using ValueType     = T;
    using value_type    = ValueType;
    using SizeType      = size_t;
    using size_type     = SizeType;
public:
    // 直线上任意一点
    TVec3<T> point;
    // 直线方向
    TVec3<T> direction;
public:
    /**
    * @brief 构造
    */
    inline TLine3()
    {
    }
    /**
    * @brief 构造
    */
    inline TLine3(const TLine3<T>& right)
    {
        this->point = right.point;
        this->direction = right.direction;
    }
    /**
    * @brief 构造
    */
    inline TLine3(const TVec3<T>& point, const TVec3<T>& direction)
    {
        this->point = point;
        this->direction = direction;
    }
    /**
    * @brief 构造
    */
    template<class U>
    inline explicit TLine3(const TLine3<T>& right)
    {
        this->point     = TVec3<T>(right.point);
        this->direction = TVec3<T>(right.direction);
    }
public:
    /**
    * @brief 是否有效的直线
    */
    inline bool isVaild() const
    {
        return this->direction.lengthSq() > NumLimits<T>::Epsilon;
    }
public:
    /**
    * @brief 根据参数 t 获取线段上的点 t∈[-∞,+∞]
    */
    inline TVec3<T> at(T t) const
    {
        return this->point + t * this->direction;
    }
public:
    /**
    * @brief 赋值运算
    */
    inline TLine3<T>& operator=(const TLine3<T>& right)
    {
        if (this == &right)
            return *this;
        this->point         = right.point;
        this->direction     = right.direction;
        return *this;
    }
public:
    /**
    * @brief 直线与三角形相交
    */
    inline std::pair<bool, T> intersect(const TTriangle3<T>& triangle, bool backfaceCulling = false)
    {
        return TLine3<T>::Intersect(*this, triangle, backfaceCulling);
    }
public:
    /**
     * @brief 计算两条直线相交
     *  计算过程证明(说明:以下证明中, 如果"*"和"·"左右都为向量时, "*" 表示向量叉乘 "·" 表示向量点乘):
     *  首先，两条直线的参数方程为: L = P + t * D;// 其中 P为直线上任意一点， t为直线上点的参数(t∈[-∞,+∞]), D为直线的方向向量
     *  所以:
     *      L1 = P1 + t1 * D1;
     *      L2 = P2 + t2 * D2;
     *  由于计算两条直线的交点,因此联立方程得:
     *      P1 + t1 * D1    =   P2 + t2 * D2;
     *      => t1 * D1      =   P2 + t2 * D2 - P1;
     *      两边同时 " * D2"(叉乘D2)
     *      => (t1 * D1) * D2   = (P2 + t2 * D2 - P1) * D2;
     *      => t1 * (D1 * D2)   = (P2 - P1) * D2 + t2 * D2 * D2;
     *      由于 D2 * D2 = 0(相同向量的叉乘结果为0向量)
     *      => t1 * (D1 * D2)   = (P2 - P1) * D2 + t2 * 0;
     *      => t1 * (D1 * D2)   = (P2 - P1) * D2;
     *      两边同时 " · (D1 * D2)" (点乘 (D1 叉乘 D2))
     *      => t1 * (D1 * D2) · (D1 * D2) = ((P2 - P1) * D2) · (D1 * D2);
     *      由于 D1 * D2(D1叉乘D2) 会得到一个向量 Dcross, 且 Dot(Dcross, Dcorss) == Dcross.lengthSq();
     *      于是
     *      => t1 * (D1 * D2).lengthSq() = ((P2 - P1) * D2) · (D1 * D2);
     *      => t1 =  ((P2 - P1) * D2) · (D1 * D2) / (D1 * D2).lengthSq();
     *      同理
     *      => t2 =  ((P2 - P1) * D1) · (D1 * D2) / (D1 * D2).lengthSq();
     *  如果两条直线平行或重合，D1 * D2为0向量，因此以上结论中分母 (D1 * D2).lengthSq()为0,
     *      即两条直线没有交点(或(两条直线有重合部分时将)有无数个交点)。
     *  分别将 t1带入到直线L1, t2带入到直线L2，则可以计算出直线L1与直线L2上的点 Pt1 和 Pt2
     *      其中:
     *          Pt1 =   Pt2 时,表示两条直线共面, 且交点是Pt1或者Pt2
     *          Pt1 !=  Pt2 时,表示两条直线不共面, 且Pt1和Pt2是两条直线的最近点(即Pt1,Pt2构成两条直线的公垂线段)
     * @param p1 直线L1上的一点
     * @param d1 直线L1的方向
     * @param p2 直线L2上的一点
     * @param d2 直线L2的方向
     * @param e 精度误差
     * @return 如果optional为std::nullopt时，表示两条直线平行或重合
     *      否则:
     *          first:  表示left直线上的点参数 t1
     *          second: 表示right直线上的点参数 t2
    */
    static std::optional<std::pair<T, T> > Intersect(const TVec3<T>& p1
        , const TVec3<T>& d1
        , const TVec3<T>& p2
        , const TVec3<T>& d2
        , const T& e = NumLimits<T>::Epsilon)
    {
        const TVec3<T>  c   = TVec3<T>::Cross(d1, d2);
        const T cLenSq      = c.lengthSq();
        // 两条线段平行或共线, 没有交点(或者说有无数个交点,比如两条线段有重合的情况)
        if (cLenSq <= e)
            return std::nullopt;

        const T cLenSqInv   = T(1) / cLenSq;
        const TVec3<T> v    = p2 - p1;

        // 直接带公式的结果
        const T t1 = TVec3<T>::Dot(TVec3<T>::Cross(v, d2), c) * cLenSqInv;
        const T t2 = TVec3<T>::Dot(TVec3<T>::Cross(v, d1), c) * cLenSqInv;

        return std::make_pair(t1, t2);
    }
    /**
     * @brief 两条直线的交点
    */
    static inline  std::optional<std::pair<T, T> > Intersect(const TLine3<T>& left
        , const TLine3<T>& right
        , const T& e = NumLimits<T>::Epsilon)
    {
        return TLine3<T>::Intersect(left.point, left.direction, right.point, right.direction, e);
    }
    /**
     * @brief 两条直线的交点
     * @param p1 直线L1上的一点
     * @param d1 直线L1的方向
     * @param p2 直线L2上的一点
     * @param d2 直线L2的方向
     * @param e 精度误差
     * @return 如果optional为std::nullopt时，表示两条之间平行或重合，无交点(或有无数个交点)
     *  否则，返回具体的交点值
     *  
    */
    static std::optional<TVec3<T> > IntersectPoint(const TVec3<T>& p1
        , const TVec3<T>& d1
        , const TVec3<T>& p2
        , const TVec3<T>& d2
        , const T& e = NumLimits<T>::Epsilon)
    {
        std::optional<std::pair<T, T> > ret = TLine3<T>::Intersect(p1, d1, p2, d2, e);
        if (!ret)
            return std::nullopt;
        const T& t1 = ret.value().first;
        const T& t2 = ret.value().second;
        // 获取到两个点
        const auto rP1 = p1 + t1 * d1;
        const auto rP2 = p2 + t2 * d2;
        // 两点不重合，表示两条直线不共面
        if (TVec3<T>::DistanceSq(rP1, rP2) > e)
            return std::nullopt;
        // 否则返回任意一个点作为交点的结果
        return rP1;
    }
    /**
     * @brief 两条直线的交点
    */
    static inline std::optional<TVec3<T> > IntersectPoint(const TLine3<T>& left
        , const TLine3<T>& right
        , const T& e = NumLimits<T>::Epsilon)
    {
        return TLine3<T>::IntersectPoint(left.point, left.direction, right.point, right.direction, e);
    }
    /**
     * @brief 两条直线的公垂线
     * @param p1 直线L1上的一点
     * @param d1 直线L1的方向
     * @param p2 直线L2上的一点
     * @param d2 直线L2的方向
     * @param e 精度误差
     * @return 如果optional为std::nullopt时，表示两条之间平行或重合
     *  否则，返回公垂线的两个端点
     *      first: 落在当前直线上的端点
     *      second: 落在right直线上的端点
     *  注意: 这里返回的两个端点可能重合，表示这两条直线相交于一点
    */
    static std::optional<std::pair<TVec3<T>, TVec3<T> > > CommonPerpendicular(const TVec3<T>& p1
        , const TVec3<T>& d1
        , const TVec3<T>& p2
        , const TVec3<T>& d2
        , const T& e = NumLimits<T>::Epsilon)
    {
        std::optional<std::pair<T, T> > ret = TLine3<T>::Intersect(p1, d1, p2, d2, e);
        if (!ret)
            return std::nullopt;
        const T& t1     = ret.value().first;
        const T& t2     = ret.value().second;
        // 获取到两个点
        const auto rP1  = p1 + t1 * d1;
        const auto rP2  = p2 + t2 * d2;
        return std::make_pair(rP1, rP2);
    }
    /**
     * @brief 两条直线的公垂线
    */
    static inline std::optional<std::pair<TVec3<T>, TVec3<T> > > CommonPerpendicular(const TLine3<T>& left
        , const TLine3<T>& right
        , const T& e = NumLimits<T>::Epsilon)
    {
        return TLine3<T>::CommonPerpendicular(left.point, left.direction, right.point, right.direction, e);
    }
    /**
    * @brief 直线与三角形相交
    */
    static std::pair<bool, T> Intersect(const TLine3<T>& line, const TTriangle3<T>& triangle, bool backfaceCulling = false)
    {
        TVec3<T> edge1 = triangle.b - triangle.a;
        TVec3<T> edge2 = triangle.c - triangle.a;
        const TVec3<T> normal = TVec3<T>::Cross(edge1, edge2);

        T DdN = TVec3<T>::Dot(line.direction, normal);
        T sign;

        if (DdN > 0)
        {
            if (backfaceCulling)
                return std::make_pair(false, T(-1));
            sign = T(1);
        }
        else if (DdN < 0)
        {
            sign = T(-1);
            DdN = -DdN;

        }
        else
        {
            return std::make_pair(false, T(-1));
        }

        const TVec3<T> diff = line.point - triangle.a;
        edge2 = TVec3<T>::Cross(diff, edge2);
        const T DdQxE2 = sign * TVec3<T>::Dot(line.direction, edge2);

        // b1 < 0, no intersection
        if (DdQxE2 < 0)
        {
            return std::make_pair(false, T(-1));
        }

        edge1 = TVec3<T>::Cross(edge1, diff);
        T DdE1xQ = sign * TVec3<T>::Dot(line.direction, edge1);

        // b2 < 0, no intersection
        if (DdE1xQ < 0)
        {
            return std::make_pair(false, T(-1));
        }

        // b1+b2 > 1, no intersection
        if (DdQxE2 + DdE1xQ > DdN)
        {
            return std::make_pair(false, T(-1));
        }

        // Line intersects triangle.
        T QdN = -sign * TVec3<T>::Dot(diff, normal);

        return std::make_pair(true, QdN / DdN);
    }
public:
    /**
    * @brief 转换到字符串
    */
    inline char* toString(char* buf) const
    {
        PrintArray2D<2>(buf
            , this->point.x, this->point.y, this->point.z
            , this->direction.x, this->direction.y, this->direction.z
        );
        return buf;
    }
    /**
    * @brief 转换到字符串
    */
    inline std::string toString() const
    {
        char buf[256] = { 0 };
        return this->toString(buf);
    }
    /**
    * @brief 从字符串转换
    */
    inline TLine3<T>& fromString(const char* str, bool* bOk = nullptr)
    {
        bool bRet = ScanArray2D<2>(str
            , this->point.x, this->point.y, this->point.z
            , this->direction.x, this->direction.y, this->direction.z
        );
        SetValueToBooleanPtr(bOk, bRet);
        return *this;
    }
    /**
    * @brief 从字符串转换
    */
    inline TLine3<T>& fromString(const std::string& str, bool* bOk = nullptr)
    {
        this->fromString(str.c_str(), bOk);
        return *this;
    }
    /**
    * @brief 从字符串转换
    */
    static inline TLine3<T> FromString(const char* str, bool* bOk = nullptr)
    {
        TLine3<T> r;
        r.fromString(str, bOk);
        return r;
    }
    /**
    * @brief 从字符串转换
    */
    static inline TLine3<T> FromString(const std::string& str, bool* bOk = nullptr)
    {
        return TLine3<T>::FromString(str.c_str(), bOk);
    }
};

/**
 * @brief 直线数组
 * @tparam T 数值类型
*/
template <class T>
using TLine3Vector = std::vector<TLine3<T> >;

/**
 * @brief 直线数组
 * @tparam T 数值类型
 * @tparam Size 数组大小
 * @tparam Size 数组大小
*/
template <class T, size_t Size>
using TLine3Array = std::array<TLine3<T>, Size>;

WD_NAMESPACE_END
