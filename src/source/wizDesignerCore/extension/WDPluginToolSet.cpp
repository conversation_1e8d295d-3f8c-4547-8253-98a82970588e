#include    "WDPluginToolSet.h"
#include    "WDExtensionMgr.h"
#include    "WDPluginTool.h"

#include    "../events/WDMouseEvent.h"
#include    "../events/WDWheelEvent.h"
#include    "../events/WDKeyEvent.h"
#include    "../events/WDHoverEvent.h"
#include    "../events/WDResizeEvent.h"
#include    "../events/WDCloseEvent.h"
#include    "../events/WDPaintEvent.h"
#include    "../events/WDUpdateEvent.h"
#include    "../events/WDToolSelectEvent.h"
#include    "../common/WDContext.h"

WD_NAMESPACE_BEGIN

WDPluginToolSet::WDPluginToolSet(WDContext& context, WDExtensionMgr & extMgr)
    :_context(context), _extMgr(extMgr)
{
    _currentTool = nullptr;
}

WDPluginToolSet::~WDPluginToolSet()
{
    //卸载所有工具
    this->uninstallAllTool();
} 

bool WDPluginToolSet::installTool(const char* toolName)
{
    //如果对应名称的工具已存在，直接返回true,表示安装成功
    WDPluginTool*   pTool = this->findToolByName(toolName);
    if (pTool != nullptr)
        return true;

    //创建工具扩展
    pTool = _extMgr.createExtensionT<WDPluginTool>(toolName);
    if (pTool == nullptr)
        return false;
    //通知工具安装
    pTool->install(_context);
    //添加到工具列表中
    _tools.push_back(pTool);

    return true;
}
bool WDPluginToolSet::uninstallTool(const char* toolName)
{
    for (auto itr = _tools.begin(); itr != _tools.end(); ++itr)
    {
        WDPluginTool* pTool = (*itr);
        if (strcmp(toolName, pTool->name().c_str()) == 0)
        {
            //通知工具卸载
            pTool->uninstall(_context);
            //销毁扩展项
            _extMgr.destroyExtension(pTool);
            //从工具列表中移除
            _tools.erase(itr);

            return true;
        }
    }
    return false;
}
void WDPluginToolSet::uninstallAllTool()
{
    _currentTool = nullptr;
    while (!_tools.empty())
    {
        WDPluginTool* pTool = _tools.back();
        //通知工具卸载
        pTool->uninstall(_context);
        //销毁扩展项
        _extMgr.destroyExtension(pTool);
        //从工具列表中移除
        _tools.pop_back();
    }
}
WDPluginTool* WDPluginToolSet::findToolByName(const char* toolName)
{
    for (size_t i = 0; i < _tools.size(); ++i)
    {
        if (strcmp(toolName, _tools.at(i)->name().c_str()) == 0)
            return _tools.at(i);
    }
    return nullptr;
}
WDPluginTool* WDPluginToolSet::setCurrentTool(const char* toolName)
{
    //首先根据名称查找工具是否存在
    WDPluginTool* pTmpTool = findToolByName(toolName);
    //不存在，设置失败
    if (pTmpTool == nullptr)
        return nullptr;

    if (_currentTool != nullptr)
    {
        WDToolSelectEvent e(WDToolSelectEvent::TST_Unselect);
        e.setContext(&_context);
        this->onEvent(&e);
    }

    _currentTool = pTmpTool;

    if (_currentTool != nullptr)
    {
        WDToolSelectEvent e(WDToolSelectEvent::TST_Select);
        e.setViewer(&_context._viewer);
        e.setContext(&_context);
        this->onEvent(&e);
    }

    return _currentTool;
}
bool WDPluginToolSet::setCurrentTool(WDPluginTool* pTool)
{
    if (_currentTool != nullptr)
    {
        WDToolSelectEvent e(WDToolSelectEvent::TST_Unselect);
        e.setContext(&_context);
        this->onEvent(&e);
    }

    auto itr = std::find(_tools.begin(), _tools.end(), pTool);
    _currentTool = (itr != _tools.end()) ? pTool : nullptr;

    if (_currentTool != nullptr)
    {
        WDToolSelectEvent e(WDToolSelectEvent::TST_Select);
        e.setContext(&_context);
        this->onEvent(&e);
        return true;
    }
    return false;
}
void WDPluginToolSet::onEvent(WDEvent* evt)
{
    if (_currentTool == nullptr)
        return;

    WDEvent::EventType type = evt->type();
    switch (type)
    {
    case WD::WDEvent::ET_None:
        break;
    case WD::WDEvent::ET_UpdateEvent:
        {
            WDUpdateEvent* e = static_cast<WDUpdateEvent*>(evt);
            _currentTool->updateEvent(e);
        }
        break;
    case WD::WDEvent::ET_PaintEvent:
        {
            WDPaintEvent* e = static_cast<WDPaintEvent*>(evt);
            _currentTool->paintEvent(e);
        }
        break;
    case WD::WDEvent::ET_CloseEvent:
        break;
    case WD::WDEvent::ET_ResizeEvent:
        {
            WDResizeEvent* e = static_cast<WDResizeEvent*>(evt);
            _currentTool->resizeEvent(e);
        }
        break;
    case WD::WDEvent::ET_InputEvent:
        break;
    case WD::WDEvent::ET_MouseEvent:
        {
            WDMouseEvent* e = static_cast<WDMouseEvent*>(evt);
            WDMouseEvent::MouseState state = e->mouseState();
            switch (state)
            {
            case WD::WDMouseEvent::MS_MouseButtonPress:
                _currentTool->mousePressEvent(e);
                break;
            case WD::WDMouseEvent::MS_MouseButtonRelease:
                _currentTool->mouseReleaseEvent(e);
                break;
            case WD::WDMouseEvent::MS_MouseMove:
                _currentTool->mouseMoveEvent(e);
                break;
            case WD::WDMouseEvent::MS_MouseButtonDblClick:
                _currentTool->mouseDoubleClickEvent(e);
                break;
            default:
                break;
            }
        }
        break;
    case WD::WDEvent::ET_WheelEvent:
        {
            WDWheelEvent* e = static_cast<WDWheelEvent*>(evt);
            _currentTool->wheelEvent(e);
        }
        break;
    case WD::WDEvent::ET_KeyEvent:
        {
            WDKeyEvent* e = static_cast<WDKeyEvent*>(evt);
            WDKeyEvent::KeyState state = e->keyState();
            switch (state)
            {
            case WD::WDKeyEvent::KS_KeyPress:
                _currentTool->keyPressEvent(e);
                break;
            case WD::WDKeyEvent::KS_KeyRelease:
                _currentTool->keyReleaseEvent(e);
                break;
            default:
                break;
            }
        }
        break;
    case WD::WDEvent::ET_TouchEvent:
        break;
    case WD::WDEvent::ET_HoverEvent:
        break;
    case WD::WDEvent::ET_DropEvent:
        break;
    case WD::WDEvent::ET_DragMoveEvent:
        break;
    case WD::WDEvent::ET_DragEnterEvent:
        break;
    case WD::WDEvent::ET_DragLeaveEvent:
        break;
    case WD::WDEvent::ET_ToolSelectEvent:
        {
            WDToolSelectEvent* e = static_cast<WDToolSelectEvent*>(evt);
            _currentTool->selectEvent(e);
        }
        break;
    default:
        break;
    }
}

WD_NAMESPACE_END


