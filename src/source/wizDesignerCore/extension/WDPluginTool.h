/**
* @file WDPluginTool.h
* @brief 工具基类
* <AUTHOR>
* @date 0000-01-01
*/
#pragma     once
#include    "WDPlugin.h"
#include    "../common/WDOperationList.h"

WD_NAMESPACE_BEGIN

class   WDContext;
class   WDToolSelectEvent;
class   WDResizeEvent;
class   WDMouseEvent;
class   WDWheelEvent;
class   WDKeyEvent;
class   WDUpdateEvent;
class   WDPaintEvent;
class   WDPluginTool;

/**
* @brief 工具状态观察者
*/
class WDPluginToolObserver
{
public:
    /**
    * @brief 工具被选择之后通知
    */
    virtual void onSelected(const WDPluginTool& sender) = 0;
    /**
    * @brief 工具被取消选择之前通知
    */
    virtual void onUnselected(const WDPluginTool& sender) = 0;
    /**
    * @brief 工具操作结果通知
    *   某些工具可能在操作过程中产生结果需要反馈到调用端，用于界面显示或记录
    *   因此这类工具可以在产生结果之后，通过观察者通知给调用端
    * @param pResult 工具操作结果数据指针
    *   具体数据结构以对应工具而定
    * @param sender 结果发送者
    */
    virtual void onToolResult(const void* pResult, const WDPluginTool& sender) = 0;
};

/**
* @brief 工具扩展
*   可接受 鼠标键盘以及绘制消息
*   但同一时刻只有当前被选择的工具能正常接受这类消息
*   其他未被选择的工具不会接受该消息
*/
class WD_API WDPluginTool : public WDPlugin
{
    WD_DECL_OBJECT(WDPluginTool)
public:
    using   Result          =   std::map<std::string,double>;
    using   Event           =   WDTDelegate<void(WDPluginTool*,const Result&)>;
    /// 如果代理函數返回true,會自動刪除代理函數
    using   SelectEvent     =   WDTDelegate<bool(WDPluginTool*,bool)>;

    using   EventSet        =   std::vector<Event>;
    using   SelectEventSet  =   std::vector<SelectEvent>;

    using   Observers       =   WDOperationList<WDPluginToolObserver*>;
public:
    static constexpr const char* ExtensionType = "plugin.tool";
protected:
    EventSet        _resultEventSet;
    SelectEventSet  _selectEventSet;
private:
    Observers       _observers;
public:
    /**
    *   @brief 绑定事件，测量结果通知
    */
    void    bindResultEvent(const Event& evt);
    /**
    *   @brief 取消绑定，测量结果通知
    */
    void    unBindResultEvent(const Event& evt);
    /**
    *   @brief 
    */
    void    bindSelectEvent(const SelectEvent& evt);
    /**
    *   @brief 
    */
    void    unBindSelectEvent(const SelectEvent& evt);

public:
    /**
    * @brief 构造
    */
    WDPluginTool(WDCore& app);
    /**
    * @brief 析构
    */
    virtual ~WDPluginTool();
public:
    /**
    * @brief 获取观察者列表
    */
    Observers & observers()
    {
        return _observers;
    }
public:
    /**
    * @brief 工具安装
    */
    virtual void install(WDContext& context);
    /**
    * @brief 工具卸载
    */
    virtual void uninstall(WDContext& context);
    /**
    * @brief 工具被选择事件
    */
    virtual void selectEvent(WDToolSelectEvent* e);
    /**
    * @brief 视图大小改变事件
    */
    virtual void resizeEvent(WDResizeEvent* e);
    /**
    * @brief 鼠标按下事件
    */
    virtual void mousePressEvent(WDMouseEvent* e);
    /**
    * @brief 鼠标抬起事件
    */
    virtual void mouseReleaseEvent(WDMouseEvent* e);
    /**
    * @brief 鼠标双击事件
    */
    virtual void mouseDoubleClickEvent(WDMouseEvent *event);
    /**
    * @brief 鼠标移动事件
    */
    virtual void mouseMoveEvent(WDMouseEvent* e);
    /**
    * @brief 滚轮事件
    */
    virtual void wheelEvent(WDWheelEvent* e);
    /**
    * @brief 键按下事件
    */
    virtual void keyPressEvent(WDKeyEvent *e);
    /**
    * @brief 键抬起事件
    */
    virtual void keyReleaseEvent(WDKeyEvent *e);
    /**
    * @brief 更新事件
    */
    virtual void updateEvent(WDUpdateEvent* e);
    /**
    * @brief 绘制事件
    */
    virtual void paintEvent(WDPaintEvent* e);
    /**
    * @brief 工具执行命令,用于向工具传递参数来初始化工具
    *   具体命令结构 需要查看对应工具扩展说明
    */
    virtual bool execCommand(const void* pCmdData);
protected:
    void    fireSelectEvent(bool bSelect);
    void    fireResultEvent(const Result&);
    /**
    * @brief 通知工具结果
    *   如果子类操作过程中产生结果，使用此接口发送结果
    */
    void    noticeResult(const void* pResult);
};

WD_NAMESPACE_END


