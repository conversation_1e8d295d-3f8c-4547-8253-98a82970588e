#include "WDStringConvert.h"
#include "WDFormatArgs.h"

WD_NAMESPACE_BEGIN

/**
 * @brief 字符串转换, 私有类
 */
class __StringConvert
{
private:
	/**
	 * @brief T是否是bool类型
	 */
	template <typename T>
	struct TIsBooleanType
	{
		static constexpr bool V = std::is_same_v<T, bool>;
	};
	/**
	 * @brief T是否是基本数值类型(不包含bool类型)
	 */
	template <typename T>
	struct TIsBasicType
	{
		static constexpr bool V = std::is_arithmetic_v<T> && (!std::is_same_v<T, bool>);
	};
	/**
	 * @brief T是否是数学库基本类型
	 */
	template <typename T>
	struct TIsMathBasicType
	{
	public:
		static constexpr bool V = WDTIsAnyOfV < T

			, TVec2<unsigned char>
			, TVec2<short>
			, TVec2<ushort>
			, TVec2<int>
			, TVec2<uint>
			, TVec2<float>
			, TVec2<double>
			, TVec2<long double>
			, TVec2<long>
			, TVec2<long long>
			, TVec2<unsigned long long>

			, TVec3<unsigned char>
			, TVec3<short>
			, TVec3<ushort>
			, TVec3<int>
			, TVec3<uint>
			, TVec3<float>
			, TVec3<double>
			, TVec3<long double>
			, TVec3<long>
			, TVec3<long long>
			, TVec3<unsigned long long>

			, TVec4<unsigned char>
			, TVec4<short>
			, TVec4<ushort>
			, TVec4<int>
			, TVec4<uint>
			, TVec4<float>
			, TVec4<double>
			, TVec4<long double>
			, TVec4<long>
			, TVec4<long long>
			, TVec4<unsigned long long>

			, TMat2<unsigned char>
			, TMat2<short>
			, TMat2<ushort>
			, TMat2<int>
			, TMat2<uint>
			, TMat2<float>
			, TMat2<double>
			, TMat2<long double>
			, TMat2<long>
			, TMat2<long long>
			, TMat2<unsigned long long>

			, TMat3<unsigned char>
			, TMat3<short>
			, TMat3<ushort>
			, TMat3<int>
			, TMat3<uint>
			, TMat3<float>
			, TMat3<double>
			, TMat3<long double>
			, TMat3<long>
			, TMat3<long long>
			, TMat3<unsigned long long>

			, TMat4<unsigned char>
			, TMat4<short>
			, TMat4<ushort>
			, TMat4<int>
			, TMat4<uint>
			, TMat4<float>
			, TMat4<double>
			, TMat4<long double>
			, TMat4<long>
			, TMat4<long long>
			, TMat4<unsigned long long>

			, TQuat<unsigned char>
			, TQuat<short>
			, TQuat<ushort>
			, TQuat<int>
			, TQuat<uint>
			, TQuat<float>
			, TQuat<double>
			, TQuat<long double>
			, TQuat<long>
			, TQuat<long long>
			, TQuat<unsigned long long>

			, TEuler<unsigned char>
			, TEuler<short>
			, TEuler<ushort>
			, TEuler<int>
			, TEuler<uint>
			, TEuler<float>
			, TEuler<double>
			, TEuler<long double>
			, TEuler<long>
			, TEuler<long long>
			, TEuler<unsigned long long>

			, TAabb2<unsigned char>
			, TAabb2<short>
			, TAabb2<ushort>
			, TAabb2<int>
			, TAabb2<uint>
			, TAabb2<float>
			, TAabb2<double>
			, TAabb2<long double>
			, TAabb2<long>
			, TAabb2<long long>
			, TAabb2<unsigned long long>

			, TAabb3<unsigned char>
			, TAabb3<short>
			, TAabb3<ushort>
			, TAabb3<int>
			, TAabb3<uint>
			, TAabb3<float>
			, TAabb3<double>
			, TAabb3<long double>
			, TAabb3<long>
			, TAabb3<long long>
			, TAabb3<unsigned long long>

			, TSphere<unsigned char>
			, TSphere<short>
			, TSphere<ushort>
			, TSphere<int>
			, TSphere<uint>
			, TSphere<float>
			, TSphere<double>
			, TSphere<long double>
			, TSphere<long>
			, TSphere<long long>
			, TSphere<unsigned long long>

			, TPlane<unsigned char>
			, TPlane<short>
			, TPlane<ushort>
			, TPlane<int>
			, TPlane<uint>
			, TPlane<float>
			, TPlane<double>
			, TPlane<long double>
			, TPlane<long>
			, TPlane<long long>
			, TPlane<unsigned long long>

			, TRay<unsigned char>
			, TRay<short>
			, TRay<ushort>
			, TRay<int>
			, TRay<uint>
			, TRay<float>
			, TRay<double>
			, TRay<long double>
			, TRay<long>
			, TRay<long long>
			, TRay<unsigned long long>

			, TSegment3<unsigned char>
			, TSegment3<short>
			, TSegment3<ushort>
			, TSegment3<int>
			, TSegment3<uint>
			, TSegment3<float>
			, TSegment3<double>
			, TSegment3<long double>
			, TSegment3<long>
			, TSegment3<long long>
			, TSegment3<unsigned long long>

			, TTriangle2<unsigned char>
			, TTriangle2<short>
			, TTriangle2<ushort>
			, TTriangle2<int>
			, TTriangle2<uint>
			, TTriangle2<float>
			, TTriangle2<double>
			, TTriangle2<long double>
			, TTriangle2<long>
			, TTriangle2<long long>
			, TTriangle2<unsigned long long>

			, TTriangle3<unsigned char>
			, TTriangle3<short>
			, TTriangle3<ushort>
			, TTriangle3<int>
			, TTriangle3<uint>
			, TTriangle3<float>
			, TTriangle3<double>
			, TTriangle3<long double>
			, TTriangle3<long>
			, TTriangle3<long long>
			, TTriangle3<unsigned long long>

			, TFrustum<unsigned char>
			, TFrustum<short>
			, TFrustum<ushort>
			, TFrustum<int>
			, TFrustum<uint>
			, TFrustum<float>
			, TFrustum<double>
			, TFrustum<long double>
			, TFrustum<long>
			, TFrustum<long long>
			, TFrustum<unsigned long long>

			, TPolygon2<unsigned char>
			, TPolygon2<short>
			, TPolygon2<ushort>
			, TPolygon2<int>
			, TPolygon2<uint>
			, TPolygon2<float>
			, TPolygon2<double>
			, TPolygon2<long double>
			, TPolygon2<long>
			, TPolygon2<long long>
			, TPolygon2<unsigned long long>

			, TKeyPoint<unsigned char>
			, TKeyPoint<short>
			, TKeyPoint<ushort>
			, TKeyPoint<int>
			, TKeyPoint<uint>
			, TKeyPoint<float>
			, TKeyPoint<double>
			, TKeyPoint<long double>
			, TKeyPoint<long>
			, TKeyPoint<long long>
			, TKeyPoint<unsigned long long>

			, TPLine<unsigned char>
			, TPLine<short>
			, TPLine<ushort>
			, TPLine<int>
			, TPLine<uint>
			, TPLine<float>
			, TPLine<double>
			, TPLine<long double>
			, TPLine<long>
			, TPLine<long long>
			, TPLine<unsigned long long>
		> ;
	};
	/**
	 * @brief T是否是WDUuid类型
	 */
	template <typename T>
	struct TIsUuidType
	{
		static constexpr bool V = std::is_same_v<T, WD::WDUuid>;
	};
	/**
	 * @brief T是否是Color类型
	 */
	template <typename T>
	struct TIsColorType
	{
		static constexpr bool V = std::is_same_v<T, WD::Color>;
	};
	/**
	 * @brief T是否被当前转换支持(或实现)
	 */
	template <typename T>
	struct TIsStringCastSupport
	{
		static constexpr bool V =
			TIsBooleanType<T>::V
			|| TIsBasicType<T>::V
			|| TIsMathBasicType<T>::V
			|| TIsUuidType<T>::V
			|| TIsColorType<T>::V;
	};
public:
	/**
	 * @brief 值转换到字符串
	*/
	template <typename T>
	static inline std::string Exec(const T& v)
	{
		static_assert(TIsStringCastSupport<T>::V, "当前类型不支持转换到字符串!");
		char buf[2048] = { 0 };
		Exec<T>(v, buf);
		return std::string(buf);
	}
	/**
	 * @brief 值转换到字符串
	*/
	template <typename T>
	static char* Exec(const T& v, char* buf)
	{
		static_assert(TIsStringCastSupport<T>::V, "当前类型不支持转换到字符串!");
		// bool 类型
		if constexpr (TIsBooleanType<T>::V) 
		{
			if (v)
				sprintf(buf, "true");
			else
				sprintf(buf, "false");
		}
		// 基本类型
		else if constexpr (TIsBasicType<T>::V) 
		{
			sprintf(buf, TFormatArg<T>::ValueCStr, v);
		}
		// 数学库基本类型
		else if constexpr (TIsMathBasicType<T>::V) 
		{
			v.toString(buf);
		}
		// WDUuid类型
		else if constexpr (TIsUuidType<T>::V) 
		{
			v.toString(buf);
		}
		// 颜色类型
		else if constexpr (TIsColorType<T>::V) 
		{
			v.toString(buf);
		}

		return buf;
	}
	/**
	 * @brief 字符串转换到值
	*/
	template <typename T>
	static T Exec(const std::string& str, bool* bOk)
	{
		static_assert(TIsStringCastSupport<T>::V, "当前类型不支持从字符串转换!");
		return Exec<T>(str.c_str(), bOk);
	}
	/**
	 * @brief 字符串转换到值
	*/
	template <typename T>
	static T Exec(const char* str, bool* bOk)
	{
		static_assert(TIsStringCastSupport<T>::V, "当前类型不支持从字符串转换!");
		T rValue;
		bool bRet = false;
		// bool 类型
		if constexpr (TIsBooleanType<T>::V)
		{
			if (_stricmp(str, "true") == 0)
			{
				rValue = true;
				bRet = true;
			}
			else if (_stricmp(str, "false") == 0)
			{
				rValue = false;
				bRet = true;
			}
			else
			{
				int v = 0;
				int r = sscanf(str, TFormatArg<int>::ValueCStr, &v);
				rValue = (v != 0);
				bRet = (r == 1);
			}
		}
		// 基本类型
		else if constexpr (TIsBasicType<T>::V)
		{
			int r = sscanf(str, TFormatArg<T>::ValueCStr, &rValue);
			bRet = (r == 1);
		}
		// 数学库基本类型
		else if constexpr (TIsMathBasicType<T>::V)
		{
			rValue.fromString(str, &bRet);
		}
		// WDUuid类型
		else if constexpr (TIsUuidType<T>::V)
		{
			rValue.fromString(str, &bRet);
		}
		// 颜色类型
		else if constexpr (TIsColorType<T>::V)
		{
			rValue.fromString(str, &bRet);
		}
		SetValueToBooleanPtr(bOk, bRet);
		return rValue;
	}
};

/**
 * @brief 特化 std::string
*/
template <>
inline std::string __StringConvert::Exec(const std::string& v)
{
	return v;
}
/**
 * @brief 特化 char*
*/
template <>
inline std::string __StringConvert::Exec(char* const& v)
{
	return std::string(v);
}

/**
 * @brief 特化 std::string
*/
template <>
inline char* __StringConvert::Exec(const std::string& v, char* buf)
{
	strcpy(buf, v.c_str());
	return buf;
}
/**
 * @brief 特化 char*
*/
template <>
inline char* __StringConvert::Exec(char* const& v, char* buf)
{
	strcpy(buf, v);
	return buf;
}

/**
 * @brief 特化 std::string
*/
template <>
inline std::string __StringConvert::Exec<std::string>(const std::string& str, bool* bOk)
{
	SetValueToBooleanPtr(bOk, true);
	return str;
}
/**
 * @brief 特化 std::string
*/
template <>
inline std::string __StringConvert::Exec<std::string>(const char* str, bool* bOk)
{
	SetValueToBooleanPtr(bOk, true);
	return std::string(str);
}

template <typename T>
inline std::string ToString(const T& v)
{
	return __StringConvert::Exec<T>(v);
}
template <typename T>
inline char* ToString(const T& v, char* buf)
{
	return __StringConvert::Exec<T>(v, buf);
}
template <typename T>
inline T FromString(const std::string& str, bool* bOk)
{
	return __StringConvert::Exec<T>(str, bOk);
}
template <typename T>
inline T FromString(const char* str, bool* bOk)
{
	return __StringConvert::Exec<T>(str, bOk);
}
template <typename T>
T FromHexString(const std::string& hexString)
{
    if (hexString.substr(0, 2) != "0x")
        return T();
    std::string realStr = hexString;
    auto tStr = realStr.erase(0, 2).c_str();
    // 确保hexString的长度是正确的
    if (strlen(tStr) != sizeof(T) * 2)
        return 0.0;

    // 创建一个字符数组来存储值的内存表示
    unsigned char szDataReversed[sizeof(T)];
    // 使用sscanf将十六进制字符串转换回字节数据
    for (size_t i = 0; i < sizeof(T); ++i)
    {
        sscanf(tStr + i * 2, "%2hhx", &szDataReversed[sizeof(T) - 1 - i]);
    }

    // 将字节数据复制回T变量
    T value;
    memcpy(&value, szDataReversed, sizeof(value));

    return value;
}
template <typename T>
std::string ToHexString(T value)
{
    // 创建一个字符数组来存储值的内存表示
    char szData[sizeof(value)];
    // 预留空间：前缀2个字符 + 每个字节2个字符 + 结尾的null字符
    char hexString[2 + sizeof(value) * 2 + 1];

    // 使用memcpy将值的内存内容复制到szData数组中
    memcpy(szData, &value, sizeof(value));

    // 添加前缀 "0xFF"
    sprintf(hexString, "0xFF");
    // 遍历szData数组，将每个字节的十六进制表示添加到hexString中
    for (size_t i = 0; i < sizeof(value); ++i)
    {
        sprintf(hexString + 2 + i * 2, "%02x", static_cast<unsigned char>(szData[i]));
    }

    return hexString;
}


WD_NAMESPACE_END