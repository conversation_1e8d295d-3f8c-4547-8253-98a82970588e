
#include    "WDTimer.h"
#include    "WDTimerSys.h"
#include    "WDCore.h"

WD_NAMESPACE_BEGIN


WDTimer::WDTimer(uint64_t micros)
{
    if (micros != std::numeric_limits<uint64_t>::max())
    {
        _interval   =   micros;
    }
}

void    WDTimer::start(uint64_t nano)
{
    _stop       =   false;
    _interval   =   nano;
    _timePoint  =   std::chrono::steady_clock::now() + std::chrono::nanoseconds(_interval.value());

    Core().timerSys().addTimer(toPtr<WDTimer>());
}

void    WDTimer::start()
{
    _stop       =   false;
    _timePoint  =   std::chrono::steady_clock::now() + std::chrono::nanoseconds(_interval.value());
    Core().timerSys().addTimer(toPtr<WDTimer>());
}
    
void    WDTimer::stop()
{
    _stop   =   true;
}

bool    WDTimer::checkTimeout()
{
    if (!_interval.has_value())
        return  false;
    if (_stop)
        return  false;

    auto    now     =   std::chrono::steady_clock::now();
    if (_timePoint >= now)
        return  false;
    if (!_timeoutFun)
        return  false;
    _timeoutFun(*this);

    _timePoint  =   std::chrono::steady_clock::now() + std::chrono::nanoseconds(_interval.value());

    return  false;

}

bool    WDTimer::stoped() const
{
    return  _stop;
}


WD_NAMESPACE_END
