#pragma     once
#include    <mutex>
#include    "WDThread.h"
#include    "WDTDelegate.hpp"
#include    "WDSmartPointer.hpp"
#include    <any>

WD_NAMESPACE_BEGIN

/**
 * @brief 任务
 */
class WDTask : public WDSmartPointer
{
    WD_DECL_SMARTPOINTER(WDTask)
public:
    // 用户数据
    using   UDataMap = std::map<std::string, std::any>;
public:
    using   ArrayObject     =   std::vector<WDObject::SharedPtr>;
    using   EventExec       =   std::function<bool(WDTask&)>;
    using   EventProgress   =   std::function<void(float,const std::string& msg)>;
protected:
    // 任务进度
    float               _progress   =   0.0f;
    // 任务文本
    std::string         _text;

    // 任务执行事件
    EventExec           _exec;
    // 任务开始事件
    EventExec           _evtStart;
    // 任务完成事件
    EventExec           _evtFinished;
    // 设置任务进度事件
    EventProgress       _evtProgress;
    // 结果
    ArrayObject         _result;
    // 线程锁
    mutable std::mutex  _mutex;
    /// 当前任务执行的线程
    WDThread::SharedPtr _thread;
    //用户数据
    UDataMap            _userDataMap;
public:
    WDTask()
    {}
    virtual ~WDTask()
    {}

    /**
     * @brief 任务结果
    */
    ArrayObject&    result()
    {
        return _result;
    }

    /**
     * @brief 任务执行事件
    */
    EventExec&      execEvt()
    {
        return _exec;
    }
    /**
    * @brief 任务开始事件
    */
    EventExec&      startEvt()
    {
        return _evtStart;
    }
    /**
     * @brief 任务完成事件
    */
    EventExec&      finishedEvt()
    {
        return _evtFinished;
    }
    /**
     * @brief 任务进度事件
    */
    EventProgress&  progressEvt()
    {
        return _evtProgress;
    }
    /**
    *   @brief 设置线程
    */
    void    setThread(WDThread::SharedPtr thread)
    {
        _thread = thread;
    }
    /**
    *   @brief 获取线程
    */
    WDThread::SharedPtr thread()
    {
        return _thread;
    }
    /**
    *   @brief 进度
    */
    float   progress() 
    {
        return  _progress;
    }
    /**
    *   @brief 进度设置
    */
    virtual void    setProgress(float prg)
    {
        _progress = prg;
        if (_evtProgress != nullptr)
            _evtProgress(_progress, _text.c_str());
    }
    /**
    *   @brief 进度文本
    */
    const std::string&   text() 
    {
        return _text;
    }
    /**
    *   @brief 设置进度文本
    */
    virtual void    setText(const std::string& text)
    {
        _text = text;
    }
    virtual bool    exec(WDThread::SharedPtr thread)
    {
        bool res = false;
        if (_evtStart != nullptr)
            _evtStart(*this);
        if (_exec != nullptr)
            res = _exec(*this);
        if (_evtFinished != nullptr)
            _evtFinished(*this);
        return res;
    }
public:
    /**
    * @brief 设置用户数据
    */
    inline void setUserData(const std::any& data, const std::string& key)
    {
        _userDataMap[key] = data;
    }
    /**
     * @brief 移除对应key的用户数据
    */
    inline void removeUserData(const std::string& key)
    {
        auto itr = _userDataMap.find(key);
        if (itr != _userDataMap.end())
            _userDataMap.erase(itr);
    }
    /**
    * @brief 获取用户数据
    */
    inline std::any userData(const std::string& key) const
    {
        auto itr = _userDataMap.find(key);
        if (itr != _userDataMap.end())
            return itr->second;

        return std::any();
    }
    /**
     * @brief 获取所有的用户数据
    */
    inline  const UDataMap& userDataMap() const
    {
        return _userDataMap;
    }
    /**
     * @brief 获取所有的用户数据
    */
    inline UDataMap& userDataMap()
    {
        return _userDataMap;
    }
};


WD_NAMESPACE_END