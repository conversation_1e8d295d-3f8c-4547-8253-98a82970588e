#pragma once

#include "WDPlatform.hpp"
WD_NAMESPACE_BEGIN

/**
 * @brief 输出到buffer(1维数组)
 *	eg:
 *		3个int的输出格式为:"[10,20,30]"
 * @tparam ...Args 要输入的数据模板参数表, 只能是基本数值类型
 * @param buf 目标buffer
 * @param args 输出的数据列表
 * @return 是否输出成功
*/
template <typename ...Args>
bool PrintArray1D(char* buf, const Args& ...args);
/**
 * @brief 从buffer(1维数组)输入
 *	eg:
 *		3个int的输入格式为:"[10,20,30]" 或 "10,20,30"
 *		也可以在中间加任意的空格:" [  10, 20,  30]" 或 "10,  20,   30 "
 * @tparam ...Args 要输出的数据模板参数表, 只能是基本数值类型
 * @param buf 目标buffer
 * @param args 输入的数据列表
 * @return 是否输入成功
*/
template <typename ...Args>
bool ScanArray1D(const char* buf, Args& ...args);
/**
 * @brief 输出到buffer(2维数组)
 *	eg:
 *		6个int的输出为两行三列格式为:"[[10,20,30],[40,50,60]"
 * @tparam GroupCount 分组个数
 *  例如:6个值时
 *      如果分组个数是2,则结果是[[n,n,n],[n,n,n]]
 *      如果分组个数是3,则结果是[[n,n],[n,n],[n,n]]
 *      如果分组个数是4,则结果是[[n,n,n,n]], 后续个数不足时将会被截断
 * @tparam ...Args 要输出的数据模板参数表, 只能是基本数值类型
 * @param buf 目标buffer
 * @param args 输出的数据列表
 * @return 是否输出成功
*/
template <size_t GroupCount, typename ...Args>
bool PrintArray2D(char* buf, const Args& ...args);
/**
 * @brief 从buffer(二维数组)输入
 *	eg:
 *		6个两行三列的int的输入格式为:"[[10,20,30],[40,50,60]]" 或 "[10,20,30],[40,50,60]"
 *		也可以在中间加任意的空格:"[ [ 10,  20, 30],[ 40,50,60]] " 或 "[ 10,20, 30],[40, 50, 60] "
 * @tparam GroupCount 分组个数
 *  例如:6个值时
 *      如果分组个数是2,则结果是[[n,n,n],[n,n,n]]
 *      如果分组个数是3,则结果是[[n,n],[n,n],[n,n]]
 *      如果分组个数是4,则结果是[[n,n,n,n]], 后续个数不足时将会被截断
 * @tparam ...Args 要输入的数据模板参数表, 只能是基本数值类型
 * @param buf 目标buffer
 * @param args 输入的数据列表
 * @return 是否输入成功
*/
template <size_t GroupCount, typename ...Args>
bool ScanArray2D(const char* buf, Args& ...args);
/**
 * @brief 用于处理静态字符串以及静态字符串连接(编译器字符串连接)
*/
enum class StaticStringType
{
    SST_Literal = 0,
    SST_Concat,
};
template <size_t N, StaticStringType T>
struct StaticString;
template <size_t N>
struct StaticString <N, StaticStringType::SST_Literal>
{
    constexpr StaticString(const char(&s)[N + 1])
        : value(s)
    {

    }

    constexpr size_t size() const
    {
        return N;
    }
    const char(&value)[N + 1];
};
template <size_t N>
struct StaticString <N, StaticStringType::SST_Concat>
{
    template <size_t N1, StaticStringType T1, size_t N2, StaticStringType T2>
    constexpr StaticString(const StaticString<N1, T1>& s1, const StaticString<N2, T2>& s2)
        :StaticString(s1
            , std::make_index_sequence<N1>{}
    , s2
        , std::make_index_sequence<N2>{})
    {}

    template <size_t N1, StaticStringType T1, size_t ...I1, size_t N2, StaticStringType T2, size_t ...I2>
    constexpr StaticString(const StaticString<N1, T1>& s1
        , std::index_sequence<I1...>
        , const StaticString<N2, T2>& s2
        , std::index_sequence<I2...>)
        : value{ s1.value[I1]..., s2.value[I2]..., '\0' }
    {
        static_assert(N == N1 + N2, "static_string length error!");
    }

    constexpr size_t size() const
    {
        return N;
    }

    char value[N + 1];
};


/**
 * @brief 计算静态字符串对象长度
 * @return 静态字符串对象长度
*/
template <size_t N>
constexpr size_t StaticStringLength(const char(&)[N])
{
    return (N - 1);
}
/**
 * @brief 初始化生成静态字符串对象
 * @tparam N 长度
 * @param s 字符串对象
 * @return 静态字符串对象
*/
template <size_t N>
constexpr auto StaticStringLiteral(const char(&s)[N])
{
    return StaticString<N - 1, StaticStringType::SST_Literal>(s);
}
/**
 * @brief 静态字符串对象拼接
*/
template <size_t N1, StaticStringType T1, size_t N2, StaticStringType T2>
constexpr auto StaticStringConcat(const StaticString<N1, T1>& s1, const StaticString<N2, T2>& s2)
{
    return StaticString<N1 + N2, StaticStringType::SST_Concat>(s1, s2);
}


/**
 * @brief 基本数值类型用于 sscanf 和 sprintf的 format
 * @tparam T 
*/
template <typename T>
struct TFormatArg;

/**
 * @brief char类型用于 sscanf 和 sprintf的 format
 * @tparam T
*/
template <>
struct TFormatArg<char>
{
    static constexpr auto Value = StaticStringLiteral("%hhd");
    static constexpr auto ValueCStr = Value.value;
};
/**
 * @brief unsigned char类型用于 sscanf 和 sprintf的 format
 * @tparam T
*/
template <>
struct TFormatArg<unsigned char>
{
    static constexpr auto Value = StaticStringLiteral("%hhu");
    static constexpr auto ValueCStr = Value.value;
};
/**
 * @brief short类型用于 sscanf 和 sprintf的 format
 * @tparam T
*/
template <>
struct TFormatArg<short>
{
    static constexpr auto Value = StaticStringLiteral("%hd");
    static constexpr auto ValueCStr = Value.value;
};
/**
 * @brief unsigned short类型用于 sscanf 和 sprintf的 format
 * @tparam T
*/
template <>
struct TFormatArg<unsigned short>
{
    static constexpr auto Value = StaticStringLiteral("%hu");
    static constexpr auto ValueCStr = Value.value;
};
/**
 * @brief int类型用于 sscanf 和 sprintf的 format
 * @tparam T
*/
template <>
struct TFormatArg<int>
{
    static constexpr auto Value = StaticStringLiteral("%d");
    static constexpr auto ValueCStr = Value.value;
};
/**
 * @brief unsigned int类型用于 sscanf 和 sprintf的 format
 * @tparam T
*/
template <>
struct TFormatArg<unsigned int>
{
    static constexpr auto Value = StaticStringLiteral("%u");
    static constexpr auto ValueCStr = Value.value;
};
/**
 * @brief long类型用于 sscanf 和 sprintf的 format
 * @tparam T
*/
template <>
struct TFormatArg<long>
{
    static constexpr auto Value = StaticStringLiteral("%ld");
    static constexpr auto ValueCStr = Value.value;
};
/**
 * @brief unsigned long类型用于 sscanf 和 sprintf的 format
 * @tparam T
*/
template <>
struct TFormatArg<unsigned long>
{
    static constexpr auto Value = StaticStringLiteral("%lu");
    static constexpr auto ValueCStr = Value.value;
};
/**
 * @brief long long类型用于 sscanf 和 sprintf的 format
 * @tparam T
*/
template <>
struct TFormatArg<long long>
{
    static constexpr auto Value = StaticStringLiteral("%lld");
    static constexpr auto ValueCStr = Value.value;
};
/**
 * @brief unsigned long long类型用于 sscanf 和 sprintf的 format
 * @tparam T
*/
template <>
struct TFormatArg<unsigned long long>
{
    static constexpr auto Value = StaticStringLiteral("%llu");
    static constexpr auto ValueCStr = Value.value;
};
/**
 * @brief float类型用于 sscanf 和 sprintf的 format
 * @tparam T
*/
template <>
struct TFormatArg<float>
{
    static constexpr auto Value = StaticStringLiteral("%f");
    static constexpr auto ValueCStr = Value.value;
};
/**
 * @brief double类型用于 sscanf 和 sprintf的 format
 * @tparam T
*/
template <>
struct TFormatArg<double>
{
    static constexpr auto Value = StaticStringLiteral("%lf");
    static constexpr auto ValueCStr = Value.value;
};
/**
 * @brief long double类型用于 sscanf 和 sprintf的 format
 * @tparam T
*/
template <>
struct TFormatArg<long double>
{
    static constexpr auto Value = StaticStringLiteral("%Lf");
    static constexpr auto ValueCStr = Value.value;
};

WD_NAMESPACE_END

#include "WDFormatArgs.inl"