#pragma once
#include "../common/WDUtils.h"

#include <list>

WD_NAMESPACE_BEGIN

/**
 * @brief list, 增加了 += ， -=， << 等运算符的冲在
 */
template<class T>
class WDOperationList : public std::list<T>
{
public:
    inline WDOperationList& operator+=(const T& t)
    {
        auto itr = std::find(this->begin(), this->end(), t);
        if (itr != this->end())
            return *this;
        this->push_back(t);
        return *this;
    }
    inline WDOperationList& operator+=(const T&& t)
    {
        auto itr = std::find(this->begin(), this->end(), t);
        if (itr != this->end())
            return *this;
        this->push_back(t);
        return *this;
    }
    inline WDOperationList& operator+=(const WDOperationList& right)
    {
        this->insert(this->end(), right.begin(), right.end());
        return *this;
    }
    inline WDOperationList& operator+=(const WDOperationList&& right)
    {
        this->merge(right);
        return *this;
    }
    inline WDOperationList& operator<<(const T& t)
    {
        (*this) += t;
        return *this;
    }
    inline WDOperationList& operator<<(const T&& t)
    {
        (*this) += t;
        return (*this);
    }
    inline WDOperationList& operator<<(const WDOperationList& right)
    {
        (*this) += right;
        return (*this);
    }
    inline WDOperationList& operator<<(const WDOperationList&& right)
    {
        (*this) += right;
        return (*this);
    }
    inline WDOperationList& operator-=(const T& t)
    {
        auto itr = std::find(this->begin(), this->end(), t);
        if (itr != this->end())
        {
            itr = this->erase(itr);
        }
        return *this;
    }
    inline bool contains(const T& t) const
    {
        auto itr = std::find(this->begin(), this->end(), t);
        return itr != this->end();
    }
};

WD_NAMESPACE_END

