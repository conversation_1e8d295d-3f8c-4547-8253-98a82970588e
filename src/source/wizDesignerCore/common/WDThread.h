#pragma     once
#include    <thread>
#include    "../WDCore.h"
#include    "WDSmartPointer.hpp"

WD_NAMESPACE_BEGIN

/**
 * @brief 线程对象
 */
class WD_API WDThread : public WDSmartPointer
{
    WD_DECL_SMARTPOINTER(WDThread)

    using   Objects     =   std::map<std::string, WDObject::SharedPtr>;
protected:
    std::thread _thread;
    Objects     _datas;
public:
    WDThread()
    {}
    virtual ~WDThread()
    {}
    std::thread&    thread()
    {
        return  _thread;
    }
    const Objects&  threadDatas() const
    {
        return  _datas;
    }
    Objects&        threadDatas() 
    {
        return  _datas;
    }
    void            setThreadData(const char* name, WDObject::SharedPtr ptr)
    {
        _datas[name]    =   ptr;
    }
    WDObject::SharedPtr threadData(const char* name)
    {
        auto    itr     =   _datas.find(name);
        if(itr  != _datas.end())
            return  itr->second;
        else
            return  nullptr;
    }
};

WD_NAMESPACE_END