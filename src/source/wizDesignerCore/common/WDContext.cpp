#include "WDContext.h"
#include "../viewer/WDViewer.h"


WD_NAMESPACE_BEGIN

WDContext::WDContext(WDViewer& viewer) :
      _viewer(viewer)
    , _app(viewer.app())
    , _resource(viewer.resource())
    , _device(viewer.device())
    , _camera(viewer.camera())
    , _clip(viewer.clip())
{
    _renderLayer.setFlag(RL_Background);
    _renderLayer.addFlag(RL_Scene);
    _renderLayer.addFlag(RL_Overlay);

    _width      =   0;
    _height     =   0;
}

void WDContext::update()
{

    _width          =   (int)(camera().viewSize().x);
    _height         =   (int)(camera().viewSize().y);
    //
    _matScreenPrj   =   Mat4::MakeOrthographic(0.0, real(_width), real(_height), 0.0, -1000.0, 1000.0);
    // 投影矩阵
    _matProject     =   camera().projectMatrix();
    // 模型矩阵
    _matModel       =   Mat4::Identity();
    // 视图矩阵
    _matView        =   camera().viewMatrix();
    // 模型-视图-投影 矩阵
    _matMvp         =   _matProject * _matView * _matModel;
    // 视图-投影 矩阵
    _matVp          =   _matProject * _matView;
    // 视椎体
    _frustum.fromProjectionMatrix(_matVp);

    updateToShared();
}

/**
*   @brief 同步当前数据到GPU中
*/
void    WDContext::updateToShared()
{
    _gpuShared._matScreenPrjF = FMat4(_matScreenPrj);
    _gpuShared._matProjectF = FMat4(_matProject);
    _gpuShared._matModelF = FMat4(_matModel);
    _gpuShared._matViewF = FMat4(_matView);
    _gpuShared._matMvpF = FMat4(_matMvp);
    _gpuShared._matVpF = FMat4(_matVp);

    _gpuShared._cameraPos = FVec4(float(camera().eye().x)
        , float(camera().eye().y)
        , float(camera().eye().z)
        , 0.0f);

    const DVec3 uDir = camera().upDir().normalized();
    const DVec3 rDir = camera().rightDir().normalized();
    _gpuShared._cameraUp = FVec4(float(uDir.x)
        , float(uDir.y)
        , float(uDir.z)
        , 0.0f);
    _gpuShared._cameraRight = FVec4(float(rDir.x)
        , float(rDir.y)
        , float(rDir.z)
        , 0.0f);
    _gpuShared._cameraFront = FVec4(float(camera().frontDir().x)
        , float(camera().frontDir().y)
        , float(camera().frontDir().z)
        , 0.0f);

    _gpuShared._lightDir = FVec4(float(camera().frontDir().x)
        , float(camera().frontDir().y)
        , float(camera().frontDir().z)
        , 0.0f);
#if 1
    // 更新原始的中心点信息
    _gpuShared._clipCenter = FVec4(FVec3(_clip.center()), 0.0f);
    // 剖切面数据
    uint clipPanelIndex = 0;
    const auto& clipPlaneDatas = _clip.planeDatas();
    for (const auto& dt: clipPlaneDatas)
    {
        if (!dt.enabled)
            continue;
        auto& left  = _gpuShared._clipPanel[clipPanelIndex];
        left        = FVec4(dt.plane.normal, dt.plane.constant);
        ++clipPanelIndex;
    }
    // 剖切的标志位
    _gpuShared._clipFlagsX.x = _clip.flags().data();
    // 被启用面的数量
    _gpuShared._clipFlagsX.y = clipPanelIndex;
#else
    _gpuShared._clip[0].normal = FVec3(_clip[0].normal);
    _gpuShared._clip[0].constant = (float)(_clip[0].constant);

    _gpuShared._clip[1].normal = FVec3(_clip[1].normal);
    _gpuShared._clip[1].constant = (float)(_clip[1].constant);

    _gpuShared._clip[2].normal = FVec3(_clip[2].normal);
    _gpuShared._clip[2].constant = (float)(_clip[2].constant);

    _gpuShared._clip[3].normal = FVec3(_clip[3].normal);
    _gpuShared._clip[3].constant = (float)(_clip[3].constant);

    _gpuShared._clip[4].normal = FVec3(_clip[4].normal);
    _gpuShared._clip[4].constant = (float)(_clip[4].constant);

    _gpuShared._clip[5].normal = FVec3(_clip[5].normal);
    _gpuShared._clip[5].constant = (float)(_clip[5].constant);

    _gpuShared._clipMethod = _clipMethod.data();
#endif

    _gpuShared._width = float(_width);
    _gpuShared._height = float(_height);
}


WD_NAMESPACE_END