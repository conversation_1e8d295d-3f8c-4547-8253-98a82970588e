#pragma once

#include    "WDFileReader.hpp"

WD_NAMESPACE_BEGIN

class   WDObject;
/**
 * @brief 内存数据读取
 */
class   WDMemReader :public WDReader
{
public:
    const char* _buffer;
    size_t      _length;
    size_t      _cur;
public:
    WDMemReader(const void* buf,size_t nLen)
    {
        _buffer =   (const char*)buf;
        _length =   nLen;
        _cur    =   0;
    }
    ~WDMemReader()
    {
    }
    virtual  void   seek(size_t pos) override
    {
        _cur    =   std::min(_length,pos);

    }
    virtual void    seekToEnd() override
    {
        _cur    =   _length;
    }
    virtual  void   seekToStart() override
    {
        _cur    =   0;
    }
    virtual size_t  tell() const override
    {
        return  _cur;
    }
    
    virtual bool    isEnd() const override
    {
        return  _cur >= _length;
    }
    virtual size_t  readBuffer(void* buffer, size_t size) override
    {
        if (isEnd())
            return  0;
        if (_cur + size >= _length)
        {
            size    =   _length - _cur;
        }
        memcpy(buffer,_buffer + _cur,size);
        _cur    +=  size;
        return  size;
    }
    virtual bool    isVirtual() const override
    {
        return  false;
    }
    inline size_t   read(void* buffer, size_t size)
    {
        return  readBuffer(buffer, size);
    }
public:
    inline  const void* data() const
    {
        return  _buffer;
    }
    inline  size_t  size() const
    {
        return  _length;
    }
    inline  size_t  length() const
    {
        return  _length;
    }
};

WD_NAMESPACE_END