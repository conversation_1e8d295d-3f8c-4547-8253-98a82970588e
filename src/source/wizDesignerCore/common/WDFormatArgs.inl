#pragma once

#include "WDPlatform.hpp"

WD_NAMESPACE_BEGIN

#if WD_PLATFORM == WD_PLATFORM_WIN32

enum class FormatArgType
{
	FAT_Print = 0,
	FAT_Scan,
};

template <FormatArgType type>
struct FormatArgParams;
template <>
struct FormatArgParams<FormatArgType::FAT_Print>
{
	// 用于sprintf的 左括号
	static constexpr auto LeftBracket = StaticStringLiteral("[");
	// 用于sprintf的 右括号
	static constexpr auto RightBracket = StaticStringLiteral("]");
	// 用于sprintf的 逗号
	static constexpr auto Separator = StaticStringLiteral(",");
};
template <>
struct FormatArgParams<FormatArgType::FAT_Scan>
{
	// 用于sscanf的 左括号
	static constexpr auto LeftBracket = StaticStringLiteral("");
	// 用于sscanf的 右括号
	static constexpr auto RightBracket = StaticStringLiteral("");
	// 用于sscanf的 逗号
	static constexpr auto Separator = StaticStringLiteral(",");
};

/**
 * @brief 基本数值类型列表生成一维字符串数组
 * @tparam T 基本数值类型
 * @tparam N 数组长度
*/


template <typename T, size_t N>
struct TFormatArgArray1D
{
	static_assert(std::is_arithmetic_v<T>, "模板参数类型必须是算数类型!");
	static_assert(N > 1, "数组长度必须大于1!");
public:
	static constexpr auto ElementCount = N;
private:
	static constexpr auto Fmt = TFormatArg<T>::Value;

	static constexpr auto NFirst = 0;
	static constexpr auto NLast = N - 1;

template <FormatArgType type, size_t I>
struct FmtCat
{
	static constexpr auto Value()
	{
		constexpr auto s0 = StaticStringConcat(Fmt, FormatArgParams<type>::Separator);
		return StaticStringConcat(s0, FmtCat<type, I + 1>::Value());
	}
};
	template <FormatArgType type>
	struct FmtCat<type, NFirst>
	{
		static constexpr auto Value()
		{
			constexpr auto s0 = StaticStringConcat(FormatArgParams<type>::LeftBracket, Fmt);
			constexpr auto s1 = StaticStringConcat(s0, FormatArgParams<type>::Separator);
			return StaticStringConcat(s1, FmtCat<type, 1>::Value());
		}
	};
	template <FormatArgType type>
	struct FmtCat<type, NLast>
	{
		static constexpr auto Value()
		{
			return StaticStringConcat(Fmt, FormatArgParams<type>::RightBracket);
		}
	};
public:
	static constexpr auto FmtPrint = FmtCat<FormatArgType::FAT_Print, 0>::Value();
	static constexpr auto FmtScan = FmtCat<FormatArgType::FAT_Scan, 0>::Value();
public:
	/**
	 * @brief 输出到buffer
	 *	eg:
	 *		3个int的输出格式为:"[10,20,30]"
	 * @tparam ...Args 要输出的数据模板参数表
	 * @param buf 目标buffer
	 * @param args 输出的数据列表
	 * @return 是否输出成功
	*/
	template <typename ...Args>
	static bool Print(char* buf, const Args& ...args)
	{
		static_assert(sizeof...(args) == ElementCount, "指定的参数个数应该与数组长度一致!");
		int r = sprintf(buf, FmtPrint.value, args...);
		return r == ElementCount;
	}
	/**
	 * @brief 从buffer输入
	 *	eg:
	 *		3个int的输入格式为:"[10,20,30]" 或 "10,20,30"
	 *		也可以在中间加任意的空格:" [  10, 20,  30]" 或 "10,  20,   30 "
	 * @tparam ...Args 要输出的数据模板参数表
	 * @param buf 目标buffer
	 * @param args 输入的数据列表，每个数据应该传入其地址,类似与sscanf的可变长参数的写法
	 * @return 是否输入成功
	*/
	template <typename ...Args>
	static bool Scan(const char* buf, Args& ...args)
	{
		// 将所有 方括号和空格替换为 ""(空字符)
		auto rStr = std::regex_replace(std::string(buf), std::regex("[\\[\\] ]"), "");

		static_assert(sizeof...(args) == ElementCount, "指定的参数个数应该与数组长度一致!");
		int r = sscanf(rStr.c_str(), FmtScan.value, &args...);
		return r == ElementCount;
	}
};
/**
 * @brief 基本数值类型列表生成二维字符串数据
 * @tparam T 基本数值类型
 * @tparam Row 行个数
 * @tparam Col 列个数
*/
template <typename T, size_t Row, size_t Col>
struct TFormatArgArray2D
{
public:
	static constexpr auto ElementCount = Row * Col;
	static constexpr auto RowCount = Row;
	static constexpr auto ColCount = Col;

	static_assert(std::is_arithmetic_v<T>, "模板参数类型必须是算数类型!");
	static_assert(RowCount > 1 && ColCount > 1, "数组长度必须大于1!");
private:
	static constexpr auto RFirst = 0;
	static constexpr auto RLast = Row - 1;

	template <FormatArgType type>
	struct ColFmt;
	template <>
	struct ColFmt<FormatArgType::FAT_Print>
	{
		static constexpr auto Value = TFormatArgArray1D<T, Col>::FmtPrint;
	};
	template <>
	struct ColFmt<FormatArgType::FAT_Scan>
	{
		static constexpr auto Value = TFormatArgArray1D<T, Col>::FmtScan;
	};

	template <FormatArgType type, size_t RI>
	struct FmtCat
	{
		static constexpr auto Value()
		{
			constexpr auto s0 = ColFmt<type>::Value;
			constexpr auto s1 = StaticStringConcat(s0, FormatArgParams<type>::Separator);
			return StaticStringConcat(s1, FmtCat<type, RI + 1>::Value());
		}
	};
	template <FormatArgType type>
	struct FmtCat<type, RFirst>
	{
		static constexpr auto Value()
		{
			constexpr auto s0 = ColFmt<type>::Value;
			constexpr auto s1 = StaticStringConcat(FormatArgParams<type>::LeftBracket, s0);
			constexpr auto s2 = StaticStringConcat(s1, FormatArgParams<type>::Separator);
			return StaticStringConcat(s2, FmtCat<type, 1>::Value());
		}
	};
	template <FormatArgType type>
	struct FmtCat<type, RLast>
	{
		static constexpr auto Value()
		{
			constexpr auto s0 = ColFmt<type>::Value;
			return StaticStringConcat(s0, FormatArgParams<type>::RightBracket);
		}
	};

	static constexpr auto FmtPrint = FmtCat<FormatArgType::FAT_Print, 0>::Value();
	static constexpr auto FmrScan = FmtCat<FormatArgType::FAT_Scan, 0>::Value();
public:
	/**
	 * @brief 输出到buffer
	 *	eg:
	 *		6个int的输出为两行三列格式为:"[[10,20,30],[40,50,60]"
	 * @tparam ...Args 要输出的数据模板参数表
	 * @param buf 目标buffer
	 * @param args 输出的数据列表
	 * @return 是否输出成功
	*/
	template <typename ...Args>
	static bool Print(char* buf, const Args& ...args)
	{
		static_assert(sizeof...(args) == ElementCount, "指定的参数个数应该与数组长度一致!");
		int r = sprintf(buf, FmtPrint.value, args...);
		return r == ElementCount;
	}
	/**
	 * @brief 从buffer输入
	 *	eg:
	 *		6个两行三列的int的输入格式为:"[[10,20,30],[40,50,60]]" 或 "[10,20,30],[40,50,60]"
	 *		也可以在中间加任意的空格:"[ [ 10,  20, 30],[ 40,50,60]] " 或 "[ 10,20, 30],[40, 50, 60] "
	 * @tparam ...Args 要输出的数据模板参数表
	 * @param buf 目标buffer
	 * @param args 输入的数据列表
	 * @return 是否输入成功
	*/
	template <typename ...Args>
	static bool Scan(const char* buf, Args& ...args)
	{
		// 将所有 方括号([,])和空格替换为 ""(空字符)
		auto rStr = std::regex_replace(std::string(buf), std::regex("[\\[\\] ]"), "");
		// 校验参数长度
		static_assert(sizeof...(args) == ElementCount, "指定的参数个数应该与数组长度一致!");
		int r = sscanf(rStr.c_str(), FmrScan.value, &args...);
		return r == ElementCount;
	}
};
#else

enum class FormatArgType
{
	FAT_Print = 0,
	FAT_Scan,
};

template <FormatArgType type>
struct FormatArgParams;
template <>
struct FormatArgParams<FormatArgType::FAT_Print>
{
	// 用于sprintf的 左括号
	static constexpr auto LeftBracket = StaticStringLiteral("[");
	// 用于sprintf的 右括号
	static constexpr auto RightBracket = StaticStringLiteral("]");
	// 用于sprintf的 逗号
	static constexpr auto Separator = StaticStringLiteral(",");
};
template <>
struct FormatArgParams<FormatArgType::FAT_Scan>
{
	// 用于sscanf的 左括号
	static constexpr auto LeftBracket = StaticStringLiteral("");
	// 用于sscanf的 右括号
	static constexpr auto RightBracket = StaticStringLiteral("");
	// 用于sscanf的 逗号
	static constexpr auto Separator = StaticStringLiteral(",");
};

/**
 * @brief 基本数值类型列表生成一维字符串数组
 * @tparam T 基本数值类型
 * @tparam N 数组长度
*/


template <typename T, size_t N>
struct TFormatArgArray1D
{
	static_assert(std::is_arithmetic_v<T>, "模板参数类型必须是算数类型!");
	static_assert(N > 1, "数组长度必须大于1!");
public:
	static constexpr auto ElementCount = N;
private:
	static constexpr auto Fmt = TFormatArg<T>::Value;

	static constexpr auto NFirst = 0;
	static constexpr auto NLast = N - 1;

template <FormatArgType type, size_t I>
struct FmtCat
{
	static constexpr auto Value()
	{
		constexpr auto s0 = StaticStringConcat(Fmt, FormatArgParams<type>::Separator);
		return StaticStringConcat(s0, FmtCat<type, I + 1>::Value());
	}
};
	template <FormatArgType type>
	struct FmtCat<type, NFirst>
	{
		static constexpr auto Value()
		{
			constexpr auto s0 = StaticStringConcat(FormatArgParams<type>::LeftBracket, Fmt);
			constexpr auto s1 = StaticStringConcat(s0, FormatArgParams<type>::Separator);
			return StaticStringConcat(s1, FmtCat<type, 1>::Value());
		}
	};
	template <FormatArgType type>
	struct FmtCat<type, NLast>
	{
		static constexpr auto Value()
		{
			return StaticStringConcat(Fmt, FormatArgParams<type>::RightBracket);
		}
	};
public:
	static constexpr auto FmtPrint = FmtCat<FormatArgType::FAT_Print, 0>::Value();
	static constexpr auto FmtScan = FmtCat<FormatArgType::FAT_Scan, 0>::Value();
public:
	/**
	 * @brief 输出到buffer
	 *	eg:
	 *		3个int的输出格式为:"[10,20,30]"
	 * @tparam ...Args 要输出的数据模板参数表
	 * @param buf 目标buffer
	 * @param args 输出的数据列表
	 * @return 是否输出成功
	*/
	template <typename ...Args>
	static bool Print(char* buf, const Args& ...args)
	{
		static_assert(sizeof...(args) == ElementCount, "指定的参数个数应该与数组长度一致!");
		int r = sprintf(buf, FmtPrint.value, args...);
		return r == ElementCount;
	}
	/**
	 * @brief 从buffer输入
	 *	eg:
	 *		3个int的输入格式为:"[10,20,30]" 或 "10,20,30"
	 *		也可以在中间加任意的空格:" [  10, 20,  30]" 或 "10,  20,   30 "
	 * @tparam ...Args 要输出的数据模板参数表
	 * @param buf 目标buffer
	 * @param args 输入的数据列表，每个数据应该传入其地址,类似与sscanf的可变长参数的写法
	 * @return 是否输入成功
	*/
	template <typename ...Args>
	static bool Scan(const char* buf, Args& ...args)
	{
		// 将所有 方括号和空格替换为 ""(空字符)
		auto rStr = std::regex_replace(std::string(buf), std::regex("[\\[\\] ]"), "");

		static_assert(sizeof...(args) == ElementCount, "指定的参数个数应该与数组长度一致!");
		int r = sscanf(rStr.c_str(), FmtScan.value, &args...);
		return r == ElementCount;
	}
};
#endif

template <typename ...Args>
bool PrintArray1D(char* buf, const Args& ...args)
{

#if WD_PLATFORM == WD_PLATFORM_WIN32
	using T = std::common_type_t<Args...>;
	static constexpr size_t N = sizeof...(args);

	static_assert(std::is_arithmetic_v<T>, "模板参数类型必须是算数类型!");
	static_assert(N > 1, "数组长度必须大于1!");

	return TFormatArgArray1D<T, N>::Print(buf, args...);
#else 
	using T = std::common_type_t<Args...>;
	static constexpr size_t N = sizeof...(args);

	static_assert(std::is_arithmetic_v<T>, "模板参数类型必须是算数类型!");
	static_assert(N > 1, "数组长度必须大于1!");

	return TFormatArgArray1D<T, N>::Print(buf, args...);
#endif 
}
template <typename ...Args>
bool ScanArray1D(const char* buf, Args& ...args)
{
#if WD_PLATFORM == WD_PLATFORM_WIN32
	using T = std::common_type_t<Args...>;
	static constexpr size_t N = sizeof...(args);

	static_assert(std::is_arithmetic_v<T>, "模板参数类型必须是算数类型!");
	static_assert(N > 1, "数组长度必须大于1!");

	return TFormatArgArray1D<T, N>::Scan(buf, args...);
#else 
	using T = std::common_type_t<Args...>;
	static constexpr size_t N = sizeof...(args);

	static_assert(std::is_arithmetic_v<T>, "模板参数类型必须是算数类型!");
	static_assert(N > 1, "数组长度必须大于1!");
	
	return TFormatArgArray1D<T, N>::Scan(buf, args...);
#endif 
}
template <size_t GroupCount, typename ...Args>
bool PrintArray2D(char* buf, const Args& ...args)
{
#if WD_PLATFORM == WD_PLATFORM_WIN32
	using T = std::common_type_t<Args...>;
	static constexpr size_t N			= sizeof...(args);
	static constexpr size_t RowCount	= GroupCount;
	static constexpr size_t ColCount	= N / RowCount;

	static_assert(std::is_arithmetic_v<T>, "模板参数类型必须是算数类型!");
	static_assert(RowCount > 1 && ColCount > 1, "数组长度必须大于1!");

	return TFormatArgArray2D<T, RowCount, ColCount>::Print(buf, args...);
#else 
	using T = std::common_type_t<Args...>;
	static constexpr size_t N			= sizeof...(args);
	static constexpr size_t RowCount	= GroupCount;
	static constexpr size_t ColCount	= N / RowCount;

	static_assert(std::is_arithmetic_v<T>, "模板参数类型必须是算数类型!");
	static_assert(RowCount > 1 && ColCount > 1, "数组长度必须大于1!");
	return false;
#endif
}
template <size_t GroupCount, typename ...Args>
bool ScanArray2D(const char* buf, Args& ...args)
{

#if WD_PLATFORM == WD_PLATFORM_WIN32
	using T = std::common_type_t<Args...>;
	static constexpr size_t N			= sizeof...(args);
	static constexpr size_t RowCount	= GroupCount;
	static constexpr size_t ColCount	= N / RowCount;

	static_assert(std::is_arithmetic_v<T>, "模板参数类型必须是算数类型!");
	static_assert(RowCount > 1 && ColCount > 1, "数组长度必须大于1!");

	return TFormatArgArray2D<T, RowCount, ColCount>::Scan(buf, args...); 
#else 
	using T = std::common_type_t<Args...>;
	static constexpr size_t N			= sizeof...(args);
	static constexpr size_t RowCount	= GroupCount;
	static constexpr size_t ColCount	= N / RowCount;

	static_assert(std::is_arithmetic_v<T>, "模板参数类型必须是算数类型!");
	static_assert(RowCount > 1 && ColCount > 1, "数组长度必须大于1!");

	return false;
#endif 
}
WD_NAMESPACE_END
