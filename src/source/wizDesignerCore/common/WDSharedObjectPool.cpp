#include "WDSharedObjectPool.h"

WD_NAMESPACE_BEGIN

WDSharedObject::WDSharedObject()
    : _pPool(nullptr)
    , _pKeyPtr(nullptr)
{

}
WDSharedObject::~WDSharedObject()
{
    if (_pPool != nullptr)
    {
        _pPool->remove(*this);
        _pPool = nullptr;
    }
    _pKeyPtr = nullptr;
}

WDSharedObjectPool::WDSharedObjectPool()
{
}
WDSharedObjectPool::~WDSharedObjectPool()
{
}

WD_NAMESPACE_END

