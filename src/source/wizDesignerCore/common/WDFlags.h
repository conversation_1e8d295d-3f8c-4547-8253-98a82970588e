/**
* @file WDFlags.h
* @brief WDFlags
* <AUTHOR>
* @date 0000-01-01
*/
#pragma once

#include "WDUtils.h"

WD_NAMESPACE_BEGIN

/**
* @brief 标志类
*   EnumType:枚举类型
*   DataType:保存标志的数据类型(通常为int)
*   eg:
*       enum MyFlag
*       {
*           MF_value0 = 1 << 0,
*           MF_value1 = 1 << 1,
*           MF_value2 = 1 << 2,
*       }
*       using MyFlags = WDFlags<MyFlag, int>;
*/
template<class EnumType, class DataType>
class WDFlags
{
private:
    DataType _flags;
public:
    inline WDFlags()
    {
        _flags = DataType(0);
    }
    inline WDFlags(EnumType flag)
    {
        _flags = flag;
    }
    inline explicit WDFlags(DataType flag)
    {
        _flags = flag;
    }
    WDFlags(std::initializer_list<EnumType> flags)
    {
        _flags = DataType(0);
        for (auto flag : flags)
        {
            addFlag(flag);
        }
    }
    WDFlags(std::initializer_list<DataType> flags)
    {
        _flags = DataType(0);
        for (auto flag : flags)
        {
            addFlag(flag);
        }
    }
    inline WDFlags(const WDFlags<EnumType, DataType>& other)
    {
        _flags = other._flags;
    }
    inline WDFlags<EnumType, DataType>& operator=(const WDFlags<EnumType, DataType>& other)
    {
        _flags = other._flags;
        return *this;
    }
    inline ~WDFlags()
    {
    }
public:
    /**
    * @brief 获取 flags
    */
    inline DataType data() const
    {
        return _flags;
    }
    /**
    * @brief 添加flag
    */
    inline WDFlags<EnumType, DataType>& addFlag(EnumType flag)
    {
        _flags |= flag;
        return *this;
    }
    /**
    * @brief 添加flag
    */
    inline WDFlags<EnumType, DataType>& addFlag(DataType flag)
    {
        _flags |= flag;
        return *this;
    }
    /**
    * @brief 移除flag
    */
    inline WDFlags<EnumType, DataType>& removeFlag(EnumType flag)
    {
        _flags &= ~flag;
        return *this;
    }
    /**
    * @brief 移除flag
    */
    inline WDFlags<EnumType, DataType>& removeFlag(DataType flag)
    {
        _flags &= ~flag;
        return *this;
    }
    /**
    * @brief 设置标记
    * @param bOn 如果为true，添加flag，否则移除flag
    */
    inline WDFlags<EnumType, DataType>& setFlag(EnumType flag, bool bOn = true)
    {
        if (bOn)
            return addFlag(flag);
        else
            return removeFlag(flag);
    }
    /**
    * @brief 是否包含某个 flag
    */
    inline bool hasFlag(EnumType flag) const
    {
        return  (_flags & flag) == DataType(flag);
    }
    /**
    * @brief 是否包含某个 flag
    */
    inline bool hasFlag(DataType flag) const
    {
        return  (_flags & flag) == DataType(flag);
    }
    /**
     * @brief 添加指定的所有 flags
     * @tparam ...TFlags 标志类型列表
     * @param ...flags 标志值列表
    */
    template <typename ...TFlags>
    WDFlags<EnumType, DataType>& addFlags(const TFlags& ...flags)
    {
        (this->addFlag(flags), ...);
        return *this;
    }
    /**
     * @brief 移除指定的所有 flags
     * @tparam ...TFlags 标志类型列表
     * @param ...flags 标志值列表
    */
    template <typename ...TFlags>
    WDFlags<EnumType, DataType>& removeFlags(const TFlags& ...flags)
    {
        (this->removeFlag(flags), ...);
        return *this;
    }
    /**
     * @brief 是否包含 所有flags
     * @tparam ...TFlags 标志类型列表
     * @param ...flags 标志值列表
     * @return 所有指定的 flag 均被包含时返回 true, 否则返回false
    */
    template <typename ...TFlags>
    bool hasFlags(const TFlags& ...flags) const
    {
         return (hasFlag(flags) && ...);
    }
    /**
     * @brief 是否包含 flags中的其中一个
     * @tparam ...TFlags 标志类型列表
     * @param ...flags 标志值列表
     * @return 所有指定的 flag 中，至少一个被包含时返回 true, 否则返回false
    */
    template <typename ...TFlags>
    bool hasAnyOfFlags(const TFlags& ...flags) const
    {
        return (hasFlag(flags) || ...);
    }
    /**
    * @brief 获取已设置标记位的个数
    */
    size_t flagCount() const
    {
        size_t dataSize = sizeof(DataType) * 8;
        size_t flagCount = 0;
        for (size_t i = 0; i < dataSize; ++i)
        {
            DataType d = 1 << i;
            if (hasFlag(d))
                flagCount++;
        }
        return flagCount;
    }

    /**
    * @brief 定义到bool的隐式转换
    */
    inline bool operator !() const
    {
        return _flags == DataType(0);
    }

    /**
    * @brief ~ 运算
    */
    inline WDFlags<EnumType, DataType> operator~() const
    {
        return WDFlags<EnumType, DataType>(~_flags);
    }

    /**
    * @brief & 运算
    */
    inline WDFlags<EnumType, DataType> operator&(EnumType flag) const
    {
        return WDFlags<EnumType, DataType>(this->_flags & flag);
    }
    /**
    * @brief & 运算
    */
    inline WDFlags<EnumType, DataType> operator&(DataType flag) const
    {
        return WDFlags<EnumType, DataType>(this->_flags & flag);
    }
    /**
    * @brief & 运算
    */
    inline WDFlags<EnumType, DataType> operator&(const WDFlags<EnumType, DataType>& other) const
    {
        return WDFlags<EnumType, DataType>(this->_flags & other._flags);
    }
    /**
    * @brief &= 运算
    */
    inline WDFlags<EnumType, DataType>& operator&=(EnumType flag)
    {
        this->_flags &= flag;
        return *this;
    }
    /**
    * @brief &= 运算
    */
    inline WDFlags<EnumType, DataType>& operator&=(DataType flag)
    {
        this->_flags &= flag;
        return *this;
    }
    /**
    * @brief &= 运算
    */
    inline WDFlags<EnumType, DataType>& operator&=(const WDFlags<EnumType, DataType>& other)
    {
        this->_flags &= other._flags;
        return *this;
    }

    /**
    * @brief | 运算
    */
    inline WDFlags<EnumType, DataType> operator|(EnumType flag) const
    {
        return WDFlags<EnumType, DataType>(this->_flags | flag);
    }
    /**
    * @brief | 运算
    */
    inline WDFlags<EnumType, DataType> operator|(DataType flag) const
    {
        return WDFlags<EnumType, DataType>(this->_flags | flag);
    }
    /**
    * @brief | 运算
    */
    inline WDFlags<EnumType, DataType> operator|(const WDFlags<EnumType, DataType>& other) const
    {
        return WDFlags<EnumType, DataType>(this->_flags | other._flags);
    }
    /**
    * @brief |= 运算
    */
    inline WDFlags<EnumType, DataType>& operator|=(EnumType flag)
    {
        this->_flags |= flag;
        return *this;
    }
    /**
    * @brief |= 运算
    */
    inline WDFlags<EnumType, DataType>& operator|=(DataType flag)
    {
        this->_flags |= flag;
        return *this;
    }
    /**
    * @brief |= 运算
    */
    inline WDFlags<EnumType, DataType>& operator|=(const WDFlags<EnumType, DataType>& other)
    {
        this->_flags |= other._flags;
        return *this;
    }

    /**
    * @brief ^ 运算
    */
    inline WDFlags<EnumType, DataType> operator^(EnumType flag) const
    {
        return WDFlags<EnumType, DataType>(this->_flags ^ flag);
    }
    /**
    * @brief ^ 运算
    */
    inline WDFlags<EnumType, DataType> operator^(DataType flag) const
    {
        return WDFlags<EnumType, DataType>(this->_flags ^ flag);
    }
    /**
    * @brief ^ 运算
    */
    inline WDFlags<EnumType, DataType> operator^(const WDFlags<EnumType, DataType>& other) const
    {
        return WDFlags<EnumType, DataType>(this->_flags ^ other._flags);
    }
    /**
    * @brief ^ 运算
    */
    inline WDFlags<EnumType, DataType>& operator^=(EnumType flag)
    {
        this->_flags ^= flag;
        return *this;
    }
    /**
    * @brief ^ 运算
    */
    inline WDFlags<EnumType, DataType>& operator^=(DataType flag)
    {
        this->_flags ^= flag;
        return *this;
    }
    /**
    * @brief ^ 运算
    */
    inline WDFlags<EnumType, DataType>& operator^=(const WDFlags<EnumType, DataType>& other)
    {
        this->_flags ^= other._flags;
        return *this;
    }

    /**
    * @brief == 运算
    */
    inline bool operator==(EnumType flag) const
    {
        return this->_flags == (DataType)(flag);
    }
    /**
    * @brief == 运算
    */
    inline bool operator==(DataType flag) const
    {
        return this->_flags == flag;
    }
    /**
    * @brief == 运算
    */
    inline bool operator==(const WDFlags<EnumType, DataType>& other) const
    {
        return this->_flags == other._flags;
    }

    /**
    * @brief != 运算
    */
    inline bool operator!=(EnumType flag) const
    {
        return this->_flags != (DataType)(flag);
    }
    /**
    * @brief != 运算
    */
    inline bool operator!=(DataType flag) const
    {
        return this->_flags != flag;
    }
    /**
    * @brief != 运算
    */
    inline bool operator!=(const WDFlags<EnumType, DataType>& other) const
    {
        return this->_flags != other._flags;
    }
};

WD_NAMESPACE_END


