
#pragma     once

#include    "../cameras/WDCamera.h"
#include    "WDRenderLayer.h"
#include    "../material/renderState/WDRenderStateStack.h"

WD_NAMESPACE_BEGIN

class WDViewer;
class WDCore;
class WDResource;
class WDOpenGL;
class WDCamera;
class WDClip;

/**
 * @brief 视图对象上下文, 用于存储视图一帧绘制所需要的数据以及相关对象
 */
class WD_API WDContext
{
public:

#ifdef _WIN32
    #define STD140  __declspec(align(16))
#else
    #define STD140  __attribute__((aligned(16)))
#endif

    /**
     * @brief 剖切面最大支持个数
    */
    static constexpr int ClipPanelMaxCount = 64;
    /**
     * @brief  结构被GPU绘制使用,该结构使用16字节对齐
     *   数据会同步到显卡,绘制之前调用updatToGPU();
    */
    struct  GPUShared
    {
        // 屏幕坐标投影矩阵
        FMat4   _matScreenPrjF;
        // 投影矩阵
        FMat4   _matProjectF;
        // 模型矩阵
        FMat4   _matModelF;
        // 视图矩阵
        FMat4   _matViewF;
        // 模型-视图-投影 矩阵 mvp = _project * _view * _model
        FMat4   _matMvpF;
        // 视图-投影 矩阵 vp = _project * _view
        FMat4   _matVpF;
        // 像机位置(仅使用xyz,w保留-目的是对齐)
        FVec4   _cameraPos;
        // 像机up (仅使用xyz,w保留-目的是对齐)
        FVec4   _cameraUp;
        // 像机right (仅使用xyz,w保留-目的是对齐)
        FVec4   _cameraRight;
        // 像机look (仅使用xyz,w保留-目的是对齐)
        FVec4   _cameraFront;
        // 默认的灯方向(仅使用xyz,w保留-目的是对齐)
        FVec4   _lightDir;
        // 剖切中心点
        FVec4   _clipCenter;
        // 剖切面
        FVec4   _clipPanel[ClipPanelMaxCount];
        // 剖切标志
        IVec4   _clipFlagsX;
        // 视口的宽度/常规绘制==窗口大小
        float   _width;
        // 视口的高度/常规绘制==窗口大小
        float   _height;
    };
public:
    // 当前视口
    WDViewer&       _viewer;
    // core app
    WDCore&         _app;
    // resource
    WDResource&     _resource;
    // device
    WDOpenGL&       _device;
    
    // 当前屏幕宽度
    uint            _width;
    // 当前屏幕高度
    uint            _height;
    // 屏幕投影矩阵
    DMat4           _matScreenPrj;
    // 投影矩阵
    DMat4           _matProject;
    // 模型矩阵
    DMat4           _matModel;
    // 视图矩阵
    DMat4           _matView;
    // 模型-视图-投影 矩阵: mvp = _project * _view * _model
    DMat4           _matMvp; 
    // 视图-投影 矩阵: vp = _project * _view
    DMat4           _matVp;
    // 视椎体
    Frustum         _frustum;
    // 当前的图层
    WDRLFlag        _renderLayer;
    // GPUShared
    GPUShared       _gpuShared;
    // 状态栈
    WDRenderStateStack _rStateStack;
    // 统计一帧更新需要的时间(毫秒)
    size_t          _updateFrameTime;
    // 统计一帧绘制需要的时间(毫秒)
    size_t          _renderFrameTime;
    // 统计一帧需要的总时间(毫秒)
    size_t          _frameTime;
protected:
    // 当前相机对象
    WDCamera::SharedPtr     _camera;
    /// 当前拾取的坐标点
    DVec3                   _pickupPoint;
    // 剖切对象
    const WDClip&           _clip;
private:
    friend class WDViewer;
    WDContext(WDViewer& viewer);
public:
    void        setPickupPoint(const Vec3& pt )
    {
        _pickupPoint    =   pt;
    }
    const Vec3& pickupPoint() const
    {
        return  _pickupPoint;
    }
    /**
    *   @brief 数据同步,设计一个double精度的，一个float精度的，主要是在shader中使用
    */
    void        update();
    /**
    *   @brief 设置像机对象
    */
    void        setCamera(WDCamera::SharedPtr camera) 
    {
        _camera =   camera;
    }
    WDCamera::SharedPtr   cameraPtr()
    {
        return  _camera;
    }
    /**
    *   @brief 像机对象
    */
    WDCamera&   camera()
    {
        return  *_camera;
    }
private:
    /**
    *   @brief 同步当前数据到_gpuShared中
    */
    void    updateToShared();
};

WD_NAMESPACE_END