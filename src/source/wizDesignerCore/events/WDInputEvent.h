/**
* @file WDInputEvent.h
* @brief 事件对象
* <AUTHOR>
* @date 0000-01-01
*/
#pragma once
#include "WDEvent.h"

WD_NAMESPACE_BEGIN

/**
* @brief 输入事件
*/
class WDEventAdapter;
class WD_API WDInputEvent : public WDEvent
{
public:
    /**
    * @brief 按键修饰
    */
    enum KeyBoardModifier
    {
        KBM_None = 0,
        //Shift 键处于按下状态
        KBM_Shift = 0x02,
        //Ctrl 键处于按下状态
        KBM_Control = 0x04,
        //Alt 键处于按下状态
        KBM_Alt = 0x08,
        //Meta 键处于按下状态
        KBM_Meta = 0x10,
        //Keypad 键处于按下状态
        KBM_Keypad = 0x20,
        //GroupSwitch 键处于按下状态
        KBM_GroupSwitch = 0x40,
        //
    };
    typedef WDFlags<KeyBoardModifier, uint> KeyBoardModifiers;
private:
    KeyBoardModifiers _keyBoardModifiers;
    size_t _timeStamp;
    friend class WDEventAdapter;
public:
    /**
    * @brief 构造
    */
    WDInputEvent(WDEvent::EventType type = ET_InputEvent
        , KeyBoardModifiers keyBoardModifiers = KeyBoardModifier::KBM_None);
    /**
    * @brief 析构
    */
    virtual ~WDInputEvent();
public:
    /**
    * @brief 获取按键修饰,标识组合键中那些键处于按下状态
    */
    inline KeyBoardModifiers keyBoardModifiers() const
    {
        return _keyBoardModifiers;
    }
    /**
    * @brief 系统时间戳
    */
    inline size_t timeStamp()const
    {
        return _timeStamp;
    }
};

WD_NAMESPACE_END


