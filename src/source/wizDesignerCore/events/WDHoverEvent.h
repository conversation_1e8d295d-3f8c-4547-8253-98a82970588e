/**
* @file WDCloseEvent.h
* @brief 事件对象
* <AUTHOR>
* @date 0000-01-01
*/
#pragma once
#include "WDInputEvent.h"

WD_NAMESPACE_BEGIN

/**
* @brief 鼠标悬停事件
*/
class WD_API WDHoverEvent : public WDInputEvent
{
public:
    /**
    * @brief 类型
    */
    enum HoverType
    {
        //进入
        HT_HoverEnter = 1,
        //离开
        HT_HoverLeave,
        //移动
        HT_HoverMove,
    };
private:
    //类型
    HoverType _hoverType;
    //当前点(鼠标位置)
    IVec2 _pos;
    //之前的点(鼠标位置)
    IVec2 _oldPos;
public:
    /**
    * @brief 构造
    */
    inline WDHoverEvent(HoverType type, const IVec2& pos, const IVec2& oldPos
        , WDInputEvent::KeyBoardModifiers keyboardModifiers = WDInputEvent::KeyBoardModifier::KBM_None)

        :WDInputEvent(ET_HoverEvent, keyboardModifiers)
    {
        _hoverType = type;
        _pos = pos;
        _oldPos = oldPos;
    }
    /**
    * @brief 析构
    */
    inline ~WDHoverEvent()
    {
    }
public:
    /**
    * @brief 获取类型
    */
    inline HoverType hoverType()const
    {
        return _hoverType;
    }
    /**
    * @brief 获取当前点(鼠标位置)
    */
    inline const IVec2& pos() const
    {
        return _pos;
    }
    /**
    * @brief 获取之前的点(鼠标位置)
    */
    inline const IVec2& oldPos() const
    {
        return _oldPos;
    }
};


WD_NAMESPACE_END

