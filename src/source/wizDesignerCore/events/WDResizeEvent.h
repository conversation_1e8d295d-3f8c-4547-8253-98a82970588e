/**
* @file WDResizeEvent.h
* @brief 事件对象
* <AUTHOR>
* @date 0000-01-01
*/
#pragma once
#include "WDEvent.h"
#include "../math/Math.hpp"

WD_NAMESPACE_BEGIN

/**
* @brief 视口尺寸改变事件
*/
class WD_API WDResizeEvent : public WDEvent
{
private:
    IVec2 _size;
    IVec2 _oldSize;
public:
    /**
    * @brief 构造
    */
    inline WDResizeEvent(const IVec2& size, const IVec2& oldSize)
        : WDEvent(ET_ResizeEvent)
    {
        _size = size;
        _oldSize = oldSize;
    }
    /**
    * @brief 析构
    */
    inline ~WDResizeEvent()
    {
    }
public:
    /**
    * @brief 获取当前大小
    */
    inline const IVec2& size() const
    {
        return _size;
    }
    /**
    * @brief 获取之前的大小
    */
    inline const IVec2& oldSize() const
    {
        return _oldSize;
    }
};

WD_NAMESPACE_END


