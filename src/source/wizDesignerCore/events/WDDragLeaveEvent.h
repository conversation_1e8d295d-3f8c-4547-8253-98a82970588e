/**
* @file WDDragLeaveEvent.h
* @brief 事件对象
* <AUTHOR>
* @date 0000-01-01
*/
#pragma once
#include "WDEvent.h"

WD_NAMESPACE_BEGIN

/**
* @brief 拖拽离开事件
*/
class WD_API WDDragLeaveEvent : public WDEvent
{
private:

public:
    /**
    * @brief 构造
    */
    inline WDDragLeaveEvent()
        :WDEvent(ET_DragLeaveEvent)
    {
    }
    /**
    * @brief 析构
    */
    inline ~WDDragLeaveEvent()
    {
    }
public:
};

WD_NAMESPACE_END


