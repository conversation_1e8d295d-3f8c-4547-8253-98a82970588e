/**
* @file WDTouchEvent.h
* @brief 事件对象
* <AUTHOR>
* @date 0000-01-01
*/
#pragma once
#include "WDInputEvent.h"
#include "../math/Math.hpp"

WD_NAMESPACE_BEGIN


/**
* @brief 触屏事件
*/
class WD_API WDTouchEvent : public WDInputEvent
{
public:
    /**
    * @brief 触摸点
    */
    class WD_API WDTouchPoint
    {
    public:
        enum TouchPointState
        {
            //无
            TPS_None = 0x00,
            //触摸点按下
            TPS_Pressed = 0x01,
            //触摸点移动
            TPS_Moved = 0x02,
            //触摸点没有发生移动
            TPS_Stationary = 0x04,
            //触摸点抬起
            TPS_Released = 0x08,
        };
    private:
        //点ID
        size_t _id;
        //按下时的位置
        IVec2 _startPos;
        //前一次更新的位置
        IVec2 _prevPos;
        //当前位置
        IVec2 _pos;
        //按下时的时间戳
        size_t _startTimeStamp;
        //前一次更新位置的时间戳
        size_t _prevTimeStamp;
        //当前位置时间戳
        size_t _timeStamp;
        //当前点的状态
        TouchPointState _state;
        friend class WDTouchEvent;
        friend class WDEventAdapter;
    public:
        inline WDTouchPoint(size_t id, const IVec2& pos, TouchPointState state, size_t timeStamp)
        {
            _id = id;

            _startPos = pos;
            _prevPos = pos;
            _pos = pos;

            _startTimeStamp = timeStamp;
            _prevTimeStamp = timeStamp;
            _timeStamp = timeStamp;
            _state = state;
        }
        ~WDTouchPoint()
        {
        }
    public:
        /**
        * @brief 获取点ID
        */
        inline size_t id() const
        {
            return _id;
        }
        /**
        * @brief 获取开始(按下时)的位置
        */
        inline const IVec2& startPos() const
        {
            return _startPos;
        }
        /**
        * @brief 获取前一次更新的位置
        */
        inline const IVec2& prevPos() const
        {
            return _prevPos;
        }
        /**
        * @brief 获取当前位置
        */
        inline const IVec2& pos() const
        {
            return _pos;
        }
        /**
        * @brief 获取开始(按下时)的时间戳
        */
        inline size_t startTimeStamp() const
        {
            return _startTimeStamp;
        }
        /**
        * @brief 获取前一次更新位置的时间戳
        */
        inline size_t prevTimeStamp()const
        {
            return _prevTimeStamp;
        }
        /**
        * @brief 获取当前位置的时间戳
        */
        inline size_t timeStamp() const
        {
            return _timeStamp;
        }
        /**
        * @brief 获取点在当前事件中的状态
        */
        inline TouchPointState state() const
        {
            return _state;
        }
    };
    /**
    * @brief 触摸点的状态集合
    */
    typedef WDFlags<WDTouchPoint::TouchPointState, uint> TouchPointStates;

    /**
    * @brief 触摸点列表
    */
    typedef std::vector<WDTouchPoint> WDTouchPoints;
private:
    //当前触发事件的点
    WDTouchPoint _point;
    //当前按下的点列表
    WDTouchPoints _points;
    //当前所有点的状态集合
    TouchPointStates _states;
public:
    /**
    * @brief 构造
    */
    inline WDTouchEvent(const WDTouchPoint& point
        , const WDTouchPoints& points
        , TouchPointStates states = WDTouchPoint::TouchPointState::TPS_None
        , WDInputEvent::KeyBoardModifiers keyboardModifiers = WDInputEvent::KeyBoardModifier::KBM_None)

        : WDInputEvent(ET_TouchEvent, keyboardModifiers)
        , _point(point)
    {
        _points = points;
        _states = states;
    }
    /**
    * @brief 析构
    */
    inline ~WDTouchEvent()
    {
    }
public:
    /**
    * @brief 获取当前事件响应的点
    */
    inline const WDTouchPoint& point() const
    {
        return _point;
    }
    /**
    * @brief 获取所有按下的点
    */
    inline const WDTouchPoints& points() const
    {
        return _points;
    }
    /**
    * @brief 获取当前时间发生时，所有点的状态的集合
    */
    inline TouchPointStates touchStates() const
    {
        return _states;
    }
public:
    /**
    * @brief 向列表中添加点
    * @param point 要添加的点对象
    * @param points 点对象列表
    * @return 添加成功返回添加之后点对象的地址， 添加失败(对应ID的点已存在)返回nullptr
    */
    static WDTouchPoint* AddPoint(const WDTouchPoint& point, WDTouchPoints& points);
    /**
    * @brief 查找点列表中是存在id为pointId的点
    * @param pointId 点的ID
    * @param points 点列表
    * @param 存在返回该点的指针，不存在返回nullptr
    */
    static WDTouchPoint* FindPoint(size_t pointId, WDTouchPoints& points);
    /**
    * @brief 查找点列表中是存在id为pointId的点
    * @param pointId 点的ID
    * @param points 点列表
    * @param 存在返回该点的指针，不存在返回nullptr
    */
    static const WDTouchPoint* FindPoint(size_t pointId, const WDTouchPoints& points);
    /**
    * @brief 移除列表中对应ID的点
    * @param pointId 点的ID
    * @param points 点列表
    */
    static void RemovePoint(size_t pointId, WDTouchPoints& points);
};

WD_NAMESPACE_END


