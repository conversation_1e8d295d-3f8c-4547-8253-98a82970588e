/**
* @file WDRenderEvent.h
* @brief 事件对象
* <AUTHOR>
* @date 0000-01-01
*/
#pragma once
#include "WDEvent.h"

WD_NAMESPACE_BEGIN

/**
* @brief 绘制(目前为每帧调用)事件
*/
class WD_API WDRenderEvent : public WDEvent
{
private:

public:
    /**
    * @brief 构造
    */
    inline WDRenderEvent()
        :WDEvent(ET_RenderEvent)
    {
    }
    /**
    * @brief 析构
    */
    inline virtual ~WDRenderEvent()
    {
    }
public:
};

WD_NAMESPACE_END

