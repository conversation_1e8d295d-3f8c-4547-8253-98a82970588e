/**
* @file WDToolSelectEvent.h
* @brief 事件对象
* <AUTHOR>
* @date 0000-01-01
*/
#pragma once
#include "WDEvent.h"

WD_NAMESPACE_BEGIN

/**
* @brief 工具选择事件
*/
class WD_API WDToolSelectEvent : public WDEvent
{
public:
    /**
    * @brief 工具选择类型
    */
    enum ToolSelectType
    {
        //未知(未定义)类型
        TST_None = 0,
        //选择
        TST_Select,
        //取消选择
        TST_Unselect,
    };
private:
    ToolSelectType _tstType;
public:
    /**
    * @brief 构造
    */
    inline WDToolSelectEvent(ToolSelectType type)
        :WDEvent(ET_ToolSelectEvent)
    {
        _tstType = type;
    }
    /**
    * @brief 析构
    */
    inline virtual ~WDToolSelectEvent()
    {
    }
public:
    /**
    * @brief 选择类型
    */
    ToolSelectType toolSelectType() const
    {
        return _tstType;
    }
};

WD_NAMESPACE_END


