#include "WDBrowseAxisMgr.h"

WD_NAMESPACE_BEGIN

class WDBrowseAxisMgrPrivate
{
public:
	std::map<std::string, WDBrowseAxisTransform> _mapBrowseAxis;

	WDBrowseAxisMgr& _d;
	friend class WDBrowseAxisMgr;
public:
	WDBrowseAxisMgrPrivate(WDBrowseAxisMgr& d) :_d(d)
	{
	}
	~WDBrowseAxisMgrPrivate()
	{
	}
public:
	// 绘制浏览轴
	void renderBrowseAxis(WDContext& context)
	{
		for (auto& bAxis : _mapBrowseAxis)
		{
			WDBrowseAxisTransform& browseAxis = bAxis.second;
			browseAxis.update(context);
			browseAxis.render(context);
		}
	}
	/**
	*   @brief 获取浏览轴对象
	*/
	WDBrowseAxisTransform& browseAxis(const std::string& name)
	{
		// 查找浏览轴组内,是否有同名的轴
		std::map<std::string, WDBrowseAxisTransform>::iterator iter;
		iter = _mapBrowseAxis.find(name);
		// 有同名的浏览轴直接返回轴
		if (iter != _mapBrowseAxis.end())
			return iter->second;
		// 没有则新建浏览轴并缓存进_mapBrowseAxis
		WDBrowseAxisTransform browseAxis;
		auto instItr = _mapBrowseAxis.insert({ name, std::move(browseAxis)});
		return instItr.first->second;
	}
	/**
	 * @brief 销毁浏览轴对象
	*/
	void destroyBrowseAxis(const std::string& name)
	{
		// 查找浏览轴组内,是否有同名的轴
		std::map<std::string, WDBrowseAxisTransform>::const_iterator iter;
		iter = _mapBrowseAxis.find(name);
		// 有同名的浏览轴则删除对应名称的轴
		if (iter != _mapBrowseAxis.end())
			_mapBrowseAxis.erase(iter);
		return;
	}
};


WDBrowseAxisMgr::WDBrowseAxisMgr(WDViewer& viewer)
	: _viewer(viewer)
{
	_p = new WDBrowseAxisMgrPrivate(*this);
}
WDBrowseAxisMgr::~WDBrowseAxisMgr()
{
	delete _p;
	_p = nullptr;
}

WDBrowseAxisTransform& WDBrowseAxisMgr::get(const std::string& name)
{
	return _p->browseAxis(name);
}

void WDBrowseAxisMgr::put(const std::string& name) const
{
	_p->destroyBrowseAxis(name);
}

void WDBrowseAxisMgr::update(WDContext& context)
{
	WDUnused(context);
}
void WDBrowseAxisMgr::render(WDContext& context)
{
	_p->renderBrowseAxis(context);
}



WD_NAMESPACE_END


