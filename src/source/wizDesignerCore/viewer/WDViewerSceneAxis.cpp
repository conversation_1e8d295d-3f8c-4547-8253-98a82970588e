#include "WDViewerSceneAxis.h"
#include "../viewer/WDViewer.h"
#include "../material/WDDrawHelpter.h"
#include "../math/geometric/standardPrimitives/ConeBuilder.h"

WD_NAMESPACE_BEGIN

WDViewerSceneAxis::WDViewerSceneAxis(WDViewer& viewer) :_viewer(viewer)
{
    _visible = true;
    _axisNames = { "X", "Y", "Z" };
    _textRender.setRenderType(WDText2DRender::RT_Screen);

    //生成坐标轴顶点
    calcVertices();

    _pRStateLineWidth = WDRenderStateLineWidth::MakeShared(4.0f); 
}
WDViewerSceneAxis::~WDViewerSceneAxis()
{
}

void    WDViewerSceneAxis::update(WDContext& context)
{
    if (!_visible)
        return;
    //计算位置
    float size = float(_srcAabb.size().length() * 0.6f);
    FVec3 pos = FVec3(size, size, 0.0f);

    WDCamera& camera = context.camera();
    _matTransform = FMat4::Compose(pos, FQuat(camera.globalRotation().inverse()));

    // 更新文本绘制对象
    _textRender.reset();
    _textRender.add(
        stringToWString(_axisNames[0]),
        FVec3(IVec3(_matTransform * (FVec3(1.0f, 0.0f, 0.0f) * _arSize * 1.1f + _axesEnd[0]))),
        Color::red,
        20
    );
    _textRender.add(
        stringToWString(_axisNames[1]),
        FVec3(IVec3(_matTransform * (FVec3(0.0f, 1.0f, 0.0f) * _arSize * 1.1f + _axesEnd[1]))),
        Color::lime,
        20
    );
    _textRender.add(
        stringToWString(_axisNames[2]),
        FVec3(IVec3(_matTransform * (FVec3(0.0f, 0.0f, 1.0f) * _arSize * 1.1f + _axesEnd[2]))),
        Color::blue,
        20
    );
}


void    WDViewerSceneAxis::render(WDContext& ctx)
{
    if (!_visible)
        return;

    FMat4 oldView               = ctx._gpuShared._matViewF;
    FMat4 oldPrj                = ctx._gpuShared._matProjectF;
    ctx._gpuShared._matViewF    = FMat4::Identity();
    ctx._gpuShared._matProjectF = FMat4(ctx._matScreenPrj);
    ctx._viewer.updateToGPU();

    WDMaterialColor materialColor;
    materialColor.setColor(Color::white);

    WDInstance inst;
    inst._local = _matTransform;

    ctx._rStateStack.push(_pRStateLineWidth);
    // X
    {
        inst._color = Color::red;
        WDDrawHelpter::Guard dg(ctx, materialColor);
        // 轴线
        dg.drawInstance({ inst }, _meshAxes[0], WDMesh::WireFrame);
        // 箭头
        dg.drawInstance({ inst }, _meshArrows[0], WDMesh::Solid);
    }
    // Y
    {
        inst._color = Color::lime;
        WDDrawHelpter::Guard dg(ctx, materialColor);
        // 轴线
        dg.drawInstance({ inst }, _meshAxes[1], WDMesh::WireFrame);
        // 箭头
        dg.drawInstance({ inst }, _meshArrows[1], WDMesh::Solid);
    }
    // Z
    {
        inst._color = Color::blue;
        WDDrawHelpter::Guard dg(ctx, materialColor);
        // 轴线
        dg.drawInstance({ inst }, _meshAxes[2], WDMesh::WireFrame);
        // 箭头
        dg.drawInstance({ inst }, _meshArrows[2], WDMesh::Solid);
    }
    ctx._rStateStack.pop();

    ctx._gpuShared._matViewF    =   oldView;
    ctx._gpuShared._matProjectF =   oldPrj;
    ctx._viewer.updateToGPU();

    //画x/y/z
    _textRender.render(ctx);

}

void WDViewerSceneAxis::calcVertices()
{
    static FVec3 sAxisX = FVec3(1.0f, 0.0f, 0.0f);
    static FVec3 sAxisY = FVec3(0.0f, 1.0f, 0.0f);
    static FVec3 sAxisZ = FVec3(0.0f, 0.0f, 1.0f);

    _axesEnd = { sAxisX * _length , sAxisY * _length , sAxisZ * _length };
    std::array<FMat3, 3> axesRMat =
    {
        FMat3::FromQuat(FQuat::FromVectors(sAxisZ, sAxisX)),
        FMat3::FromQuat(FQuat::FromVectors(sAxisZ, sAxisY)),
        FMat3::FromQuat(FQuat::FromVectors(sAxisZ, sAxisZ)),
    };

    _srcAabb = Aabb3::Null();

    // X, Y, Z轴线
    for (size_t i = 0; i < _meshAxes.size(); ++i)
    {
        _meshAxes[i].setPositions({ FVec3::Zero(), _axesEnd[i] });
        _meshAxes[i].addPrimitiveSet(WDPrimitiveSet::FromData(0, 2, WDPrimitiveSet::PrimitiveType::PT_Lines)
            , WDMesh::WireFrame);
        _srcAabb.unions(_meshAxes[i].computeAabb());
    }
    WD::MeshStruct rMeshStru = ConeBuilder::Mesh(0.0f, _arRadius * 2, _arSize);
    // X, Y, Z箭头
    for (size_t i = 0; i < _meshArrows.size(); ++i)
    {
        _meshArrows[i].setPositions(rMeshStru.positions);
        _meshArrows[i].addPrimitiveSet(rMeshStru.triangles, WDMesh::Solid);
        for (auto& pos : _meshArrows[i].positions())
        {
            pos = _axesEnd[i] + axesRMat[i] * pos;
        }
        for (auto& nor : _meshArrows[i].normals())
        {
            nor = axesRMat[i] * nor;
        }
        _srcAabb.unions(_meshArrows[i].computeAabb());
    }
}

WD_NAMESPACE_END

