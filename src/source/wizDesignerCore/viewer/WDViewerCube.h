#pragma     once

#include    "../node/WDNode.h"


WD_NAMESPACE_BEGIN

class   WDContext;
class   WDTexture;
class   WDMouseEvent;
class   WDNode;
class   WDViewer;

/**
 * @brief 视图盒子
 */
class WD_API WDViewerCube 
{
public:
    enum CubeObjectType
    {
        //6个面
        FT_Front = 0,
        FT_Back,
        FT_Left,
        FT_Right,
        FT_Up,
        FT_Down,
        //12条边
        ST_FrontUp,
        ST_FrontDown,
        ST_FrontLeft,
        ST_FrontRignt,
        ST_BackUp,
        ST_BackDown,
        ST_BackLeft,
        ST_BackRight,
        ST_LeftUp,
        ST_LeftDown,
        ST_RightUp,
        ST_RightDown,
        //8个顶点
        VT_FrontUpLeft,
        VT_FrontUPRight,
        VT_FrontDownLeft,
        VT_FrontDownRight,
        VT_BackUpLeft,
        VT_BackUPRight,
        VT_BackDownLeft,
        VT_BackDownRight,
        //无效
        OT_Invaild = NumLimits<short>::Max,
    };
    /**
     * @brief 节点包围盒的获取函数
    */
    using NodeAabbGetFunction = std::function<DAabb3(WDNode& node)>;
    using NodeAabbGetFunctions = std::vector<NodeAabbGetFunction>;
private:
    struct V3N2
    {
        FVec3 _position;
        FVec2 _uv;
    public:
        V3N2(const FVec3& position, const FVec2& uv)
        {
            _position = position;
            _uv = uv;
        }
    };
private:
    // 视图对象
    WDViewer& _viewer;
    friend class WDViewer;

    // 顶点坐标,每四个点表示一个四角面
    std::vector<V3N2> _vertices;
    // 贴图,分别存储:前，后，左，右，上，下，边缘
    std::vector<WDTexture*> _textures;
    // 变换矩阵
    FMat4   _matTransform;
    // 原始包围盒
    Aabb3   _srcAabb;
    // 可见性
    bool    _visible;
    // 节点包围盒获取函数
    NodeAabbGetFunctions _nodeAAbbGetFunctions;
    // 某个对象的四角面索引列表
    using QuadIndices = std::vector<int>;
    // 所有对象索引
    std::vector<QuadIndices> _objects;
    // 拾取时使用,屏幕坐标的顶点
    std::vector<FVec3> _screenVertices;
    // 屏幕坐标顶点的包围盒
    FAabb3 _screenAabb;
    // 当前高亮的对象(枚举)
    CubeObjectType _hoveredObject;

    // 鼠标是否按下
    bool _bPressed;
    // 鼠标按下的位置是否是视图盒子对象
    bool _bPressedOnCube;
private:
    //盒子大小
    static inline float cubeSize = 48.0f;
    //盒子边缘大小
    static inline float cubeSideSize = 8.0f;
public:
    WDViewerCube(WDViewer& viewer);
    WDViewerCube(const WDViewerCube& right) = delete;
    WDViewerCube(WDViewerCube&& right) = delete;
    WDViewerCube& operator=(const WDViewerCube& right) = delete;
    WDViewerCube& operator=(WDViewerCube&& right) = delete;
    ~WDViewerCube();
public:
    /**
     * @brief 设置可见性
    */
    inline void setVisible(bool visible)
    {
        _visible = visible;
    }
    /**
     * @brief 获取可见性
    */
    inline bool visible() const
    {
        return _visible;
    }
    /**
     * @brief 设置节点aabb的获取函数
    */
    inline void addNodeAabbGetFunction(const std::function<DAabb3(WDNode& node)>& func)
    {
        _nodeAAbbGetFunctions.push_back(func);
    }
    /**
    *   @brief 场景视角定位
    *   @param dir:方向
    *   @param aabb 包围盒
    */
    bool lookAt(CubeObjectType dir, const Aabb3& aabb);
private:
    /**
    * @brief 鼠标按下
    */
    bool mousePress(const IVec2& mousePos);
    /**
    * @brief 鼠标抬起
    */
    bool mouseRelease(const IVec2& mousePos);
    /**
    * @brief 鼠标移动
    */
    bool mouseMove(const IVec2& mousePos);
    /**
    * @brief 更新
    */
    void update(WDContext& context);
    /**
    * @brief 绘制
    */
    void render(WDContext& context);
protected:
    //计算视图盒子顶点
    void calcVertices();
private:
    //拾取计算,拿到某个四边形的索引
    CubeObjectType pickup(const IVec2& screen, const WDContext& context);
};

WD_NAMESPACE_END


