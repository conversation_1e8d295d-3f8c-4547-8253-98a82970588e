#pragma once

#include    "../../material/WDMaterial.h"

WD_NAMESPACE_BEGIN

class WDStdPriRenderPrivate;
/**
 * @brief 标准基本体绘制对象
 *  Standard Primitives Render
 *  用于快速绘制常用样式的标准基本体
*/
class WD_API WDStdPriRender
{
private:
    WDStdPriRenderPrivate* _p;
public:
    WDStdPriRender();
    WDStdPriRender(const WDStdPriRender& right) = delete;
    WDStdPriRender(WDStdPriRender&& right) = delete;
    WDStdPriRender& operator=(const WDStdPriRender& right) = delete;
    WDStdPriRender& operator=(WDStdPriRender&& right) = delete;
    ~WDStdPriRender();
public:
    /**
     * @brief 重置数据
     *  1. 清空所有已经添加的简单基本体对象
     *  2. 清空包围盒
    */
    void reset();
    /**
     * @brief 是否为空
    */
    bool empty() const;

    /**
     * @brief 添加立方体
     * @param xLen 长, x方向长度
     * @param yLen 宽, y方向长度
     * @param zLen 高, z方向长度
     * @param color 颜色
     * @param transform 变换矩阵
    */
    WDStdPriRender& addBox(float xLen, float yLen, float zLen
        , Color color = Color::red
        , const FMat4& transform = FMat4::Identity()
        , InstanceAttrs instAttrs = InstanceAttr::IA_None);
    /**
     * @brief 添加圆锥
     * @param bottomDiameter 底面直径
     * @param height 高度
     * @param color 颜色
     * @param transform 变换矩阵
    */
    WDStdPriRender& addCone(float bottomDaimeter, float height
        , Color color = Color::red
        , const FMat4& transform = FMat4::Identity()
        , InstanceAttrs instAttrs = InstanceAttr::IA_None);
    /**
     * @brief 添加圆柱
     * @param diameter 直径
     * @param height 高度
     * @param color 颜色
     * @param transform 变换矩阵
    */
    WDStdPriRender& addCylinder(float daimeter, float height
        , Color color = Color::red
        , const FMat4& transform = FMat4::Identity()
        , InstanceAttrs instAttrs = InstanceAttr::IA_None);
    /**
     * @brief 添加球
     * @param diameter 直径
     * @param color 颜色
     * @param transform 变换矩阵
    */
    WDStdPriRender& addSphere(float diameter
        , Color color = Color::red
        , const FMat4& transform = FMat4::Identity()
        , InstanceAttrs instAttrs = InstanceAttr::IA_None);
    /**
     * @brief 添加椭球
     * @param xDiameter x方向直径
     * @param yDiameter y方向直径
     * @param zDiameter z方向直径
     * @param color 颜色
     * @param transform 变换矩阵
    */
    WDStdPriRender& addEllipsoid(float xDiameter, float yDiameter, float zDiameter
        , Color color = Color::red
        , const FMat4& transform = FMat4::Identity()
        , InstanceAttrs instAttrs = InstanceAttr::IA_None);
public:
    /**
     * @brief 获取已添加的所有线的顶点构成的包围盒
     *  !注意: 未包含变换信息，只是原始顶点的包围盒
    */
    const FAabb3& aabb() const;
    /**
     * @brief 绘制所有添加的文本
     * @param context 上下文
     * @param depthTest 深度测试
    */
    void  render(WDContext& context, bool depthTest = true);
};

WD_NAMESPACE_END
