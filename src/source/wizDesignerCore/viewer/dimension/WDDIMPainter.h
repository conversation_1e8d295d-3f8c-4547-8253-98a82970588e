#pragma once

#include "WDDIMStyle.h"

WD_NAMESPACE_BEGIN

/**
 * @brief 标注基础图元绘制对象
*/
class WD_API WDDIMPainter
{
public:
    WDDIMPainter();
    WDDIMPainter(const WDDIMPainter& right) = delete;
    WDDIMPainter(WDDIMPainter&& right) = delete;
    WDDIMPainter& operator=(const WDDIMPainter& right) = delete;
    WDDIMPainter& operator=(WDDIMPainter&& right) = delete;
    ~WDDIMPainter();
public:
    /**
     * @brief 指定文字样式以及文本，测量文字尺寸, 文本的编码格式为 utf8
    */
    virtual DVec2 calcTextSize(const std::string& text
        , const WDDIMFontStyle& style = WDDIMFontStyle()) = 0;
    /**
     * @brief 指定多行文字的文本,文字样式以及对其方式, 测量文字尺寸, 文本的编码格式为 utf8
     *  高度取每一行文字高度的累加，宽度取每一行文字宽度的最大值
    */
    using Texts = std::vector<std::pair<std::string, WDDIMFontStyle> >;
    DVec2 calcTextsSize(const Texts& texts);
public:
    /**
     * @brief 指定一个点列表，绘制一系列点
     * @param points 点列表
     * @param style 点样式
    */
    virtual void drawPoints(const std::vector<DVec3>& points
        , const WDDIMPointStyle& style = WDDIMPointStyle()) = 0;
    /**
     * @brief 指定起始点和结束点绘制线段
     * @param sPos 起始点
     * @param ePos 结束点
     * @param style 线样式
    */
    virtual void drawLine(const DVec3& sPos, const DVec3& ePos
        , const WDDIMLineStyle& style = WDDIMLineStyle()) = 0;
    /**
     * @brief 指定一个点列表, 每两个点绘制一条线段
     * @param points 点列表
     *  其中:
     *      列表长度为大于0的双数(length > 0 && length % 2 == 0)
     *      如果点的个数是单数，则自动抛弃最后一个点
     * @param style 线样式
    */
    virtual void drawLines(const std::vector<DVec3>& points
        , const WDDIMLineStyle& style = WDDIMLineStyle()) = 0;
    /**
     * @brief 指定一个点列表，使用直线连接所有相邻点，绘制一条折线
     * @param points 点列表
     * @param style 线样式
    */
    virtual void drawBrokenLine(const std::vector<DVec3>& points
        , const WDDIMLineStyle& style = WDDIMLineStyle()
        , WD::Color fillColor = WD::Color(0, 0, 0, 0)) = 0;
    /**
     * @brief 指定一个点列表，使用直线连接所有相邻点以及首尾点，绘制一个闭合的环
     * @param points 点列表
     * @param style 线样式
    */
    virtual void drawLoopLine(const std::vector<DVec3>& points
        , const WDDIMLineStyle& style = WDDIMLineStyle()
        , WD::Color fillColor = WD::Color(0, 0, 0, 0)) = 0;
    /**
     * @brief 指定一个二维点列表，使用直线连接所有相邻点以及首尾点，绘制一个闭合的环
     * @param points 点列表
     * @param style 线样式
    */
    virtual void drawLoopLine2D(const std::vector<DVec2>& points
        , const WDDIMLineStyle& style = WDDIMLineStyle()
        , WD::Color fillColor = WD::Color(0, 0, 0, 0))
    {
        WDUnused3(points, style, fillColor);
    }
    /**
     * @brief 绘制一条弧线
     * @param center 弧所在的圆心
     * @param radius 半径
     * @param sDirection 起始方向
     * @param eDirection 结束方向
     * @param isCCW 旋转方向(顺时针还是逆时针)
     * @param style 线样式
    */
    virtual void drawArc(const DVec3& center
        , double radius
        , const DVec3& sDirection
        , const DVec3& eDirection
        , const WDDIMLineStyle& style = WDDIMLineStyle()) = 0;
    /**
     * @brief 绘制一个矩形边框
     * @param center 中心点
     * @param size 长宽(分别对应x轴,y轴)
     * @param xAxis x轴方向
     * @param planeNormal 矩形所在平面法线
     * @param style 线样式
    */
    virtual void drawRect(const DVec3& center
        , const DVec2& size
        , const DVec3& xAxis
        , const DVec3& planeNormal
        , const WDDIMLineStyle& style = WDDIMLineStyle()) = 0;
    /**
     * @brief 绘制一个填充矩形
     * @param center 中心点
     * @param size 长宽(分别对应x轴,y轴)
     * @param xAxis x轴方向
     * @param planeNormal 矩形所在平面法线
     * @param style 线样式
    */
    virtual void fillRect(const DVec3& center
        , const DVec2& size
        , const DVec3& xAxis
        , const DVec3& planeNormal
        , const WDDIMShapeFillStyle& style = WDDIMShapeFillStyle()) = 0;
    /**
     * @brief 绘制一个形状边框
     * @param center 圆心
     * @param radius 半径
     * @param planeNormal 圆所在的平面法线
     * @param style 线样式
    */
    virtual void drawCircle(const DVec3& center
        , double radius
        , const DVec3& planeNormal
        , const WDDIMLineStyle& style = WDDIMLineStyle()) = 0;
    /**
     * @brief 绘制一个填充圆
     * @param center 中心点
     * @param radius 半径
     * @param planeNormal 圆所在的平面法线
     * @param xAxis x轴方向，也表示其填充方向; 不指定该方向时，将自动计算一个填充方向，并且这个方向是无法预期的
     * @param style 填充样式
    */
    virtual void fillCircle(const DVec3& center
        , double radius
        , const DVec3& planeNormal
        , const WDDIMShapeFillStyle& style = WDDIMShapeFillStyle()
        , const std::optional<DVec3>& xAxis = std::nullopt) = 0;
    /**
     * @brief 绘制一个三角形
     * @param vertices 三角形顶点
     * @param style 线样式
    */
    virtual void drawTriangle(const std::array<DVec3, 3>& vertices
        , const WDDIMLineStyle& style = WDDIMLineStyle()) = 0;
    /**
     * @brief 绘制一个填充三角形
     * @param vertices 三角形顶点
     * @param style 填充样式
     * @param xAxis x轴方向，也表示其填充方向; 不指定该方向时，将使用三角形的第一个点和第二个点自动计算一个填充方向
    */
    virtual void fillTriangle(const std::array<DVec3, 3>& vertices
        , const WDDIMShapeFillStyle& style = WDDIMShapeFillStyle()
        , const std::optional<DVec3>& xAxis = std::nullopt) = 0;
    /**
     * @brief 绘制文本
     * @param text 文本, 文本的编码格式为 utf8
     * @param position 文本位置 ,世界坐标
     * @param rightDir 文本右方向,世界坐标
     * @param upDir 文本上方向,世界坐标
     * @param style 文字样式
     * @param textAlign 文字对齐方式
     * @return 文本的尺寸,世界坐标
    */
    virtual DVec2 drawText(const std::string& text
        , const DVec3& position
        , const DVec3& rightDir
        , const DVec3& upDir
        , const WDDIMFontStyle& style = WDDIMFontStyle()
        , const WDDIMAlign& textAlign = { WDDIMAlign::HAlign::HA_Center, WDDIMAlign::VAlign::VA_Center }) = 0;
    /**
     * @brief 绘制文本
     * @param text 文本, 文本的编码格式为 utf8
     * @param position 文本位置 ,二维坐标
     * @param textAlign 文字对齐方式
    * @return 文本的尺寸,世界坐标
    */
    virtual void drawText2D(const std::string& text
        , const DVec2& position
        , const WDDIMFontStyle& style = WDDIMFontStyle()
        , const WDDIMAlign& textAlign = { WDDIMAlign::HAlign::HA_Center, WDDIMAlign::VAlign::VA_Center })
    {
        WDUnused4(text, position, style, textAlign);
    }
    /**
     * @brief 绘制多行文本
     * @param pos 文本位置
     * @param rightDir 文本的右方向
     * @param upDir 文本的上方向
     * @param texts 多行文本, 数组中的每一项代表一行文本, 文本的编码格式为 utf8
     * @param align 文本的对齐方式,结合文本位置使用
     * @param rowAlign 文本的行对齐方式
     * @return 所有文本的包围尺寸
    */
    virtual DVec2 drawTexts(const DVec3& pos
        , const DVec3& rightDir
        , const DVec3& upDir
        , std::vector<std::pair<std::string, WDDIMFontStyle> > texts
        , WDDIMAlign align = { WDDIMAlign::HAlign::HA_Center, WDDIMAlign::VAlign::VA_Center }
    , WDDIMAlign::HAlign rowAlign = WDDIMAlign::HAlign::HA_Left) = 0;
};

static int numberMark = 0;
/**
 * @brief 自动编号
 */
class WD_API NumberGenerater
{
public:
    static inline NumberGenerater Instance()
    {
        static NumberGenerater logger;
        return logger;
    }
    ~NumberGenerater()
    {

    }

public:
    enum class WD_API NumberType
    {
        // 自动编号
        NT_None,
        // 管线
        NT_Pipe,
    };
    int GetNumber(NumberType type = NumberType::NT_None)
    {
        switch (type)
        {
        case NumberType::NT_Pipe:
            return 0;
            break;
        default:
            break;
        }
        ++numberMark;
        return numberMark;
    }
    void clear()
    {
        numberMark = 0;
    }
private:
    NumberGenerater()
    {

    }
};

/**
 * @brief 标注的碰撞计算(目前只支持文本的碰撞计算)
*/
class WD_API WDDIMCollision
{
public:
    /**
     * @brief 线段对象
    */
    class CSegment
    {
    public:
        // 起点
        DVec3 sPos;
        // 终点
        DVec3 ePos;
        // 样式
        WDDIMLineStyle style;
    };
    /**
     * @brief 矩形对象
    */
    class CRect
    {
    public:
        // 中心点
        DVec3 center;
        // 尺寸
        DVec2 size;
        // x轴向
        DVec3 xAxis;
        // 矩形所在平面的法线
        DVec3 planeNormal;
        // 边框线样式
        WDDIMLineStyle style;
    };
    /**
     * @brief 文本对象
    */
    class CText
    {
    public:
        // 文本, 文本的编码格式为 utf8
        std::string text;
        // 位置
        DVec3 position;
        // 文本右方向
        DVec3 rightDir;
        // 文本上方向
        DVec3 upDir;
        // 文字样式
        WDDIMFontStyle style = WDDIMFontStyle();
        // 文本的对齐方式
        WDDIMAlign textAlign = { WDDIMAlign::HAlign::HA_Center, WDDIMAlign::VAlign::VA_Center };
    };
    /**
     * @brief 多行文本对象
    */
    class CTexts
    {
    public:
        // 文本位置
        DVec3 pos;
        // 文本右方向
        DVec3 rightDir;
        // 文本上方向
        DVec3 upDir;
        // 文本以及样式列表, 文本的编码格式为 utf8
        std::vector<std::pair<std::string, WDDIMFontStyle> > texts;
        // 文本的对齐方式
        WDDIMAlign align = { WDDIMAlign::HAlign::HA_Center, WDDIMAlign::VAlign::VA_Center };
        // 行对齐方式
        WDDIMAlign::HAlign rowAlign = WDDIMAlign::HAlign::HA_Left;
    };
    /**
     * @brief 圆形对象
    */
    class CCircle
    {
    public:
        // 中心点
        DVec3 center;
        // 尺寸
        double radius;
        // 圆形所在平面的法线
        DVec3 planeNormal;
        // 边框线样式
        WDDIMLineStyle style;
    };
    /**
     * @brief 碰撞对象
     */
    using CObject = std::variant<std::monostate
        , CSegment
        , CRect
        , CText
        , CTexts
        , CCircle>;
    /**
     * @brief 碰撞项
     */
    struct CItem 
    {
    public:
        /**
         * @brief 碰撞对象
         */
        CObject object;
        /**
         * @brief 碰撞对象的组id  
         *  当groupId小于0时, 应该认为该对象不参与碰撞计算
         *  当两个碰撞对象的groupId相等时，应该认为这两个对象属于一组，不参与碰撞计算
         */
        int groupId = -1;
        /**
         * @brief 碰撞对象的类型id
         *  自行指定，以区别不同的碰撞对象，以便于重写碰撞计算方法时，针对不同的类型，做不同的处理
         */
        int typeId = -1;
        /**
         * @brief 根据碰撞对象计算的aabb包围盒
         *  重写addObject和checkObject方法时，需要重新计算该包围盒
         */
        DAabb2 aabb = DAabb2::Null();
        /**
         * @brief 根据碰撞对象计算的obb包围盒
         *  重写addObject和checkObject方法时，需要重新计算该包围盒
         */
        DObb2 obb = DObb2::Null();
    public:
        CItem() = default;
        CItem(const CObject& object, int groupId = -1, int typeId = -1) 
            : object(object)
            , groupId(groupId)
            , typeId(typeId)
        {

        }
        CItem(CObject&& object, int groupId = -1, int typeId = -1)
            : object(object)
            , groupId(groupId)
            , typeId(typeId)
        {

        }
        CItem(const CItem& right) = default;
        CItem(CItem&& right) = default;
        CItem& operator=(const CItem& right) = default;
        CItem& operator=(CItem&& right) = default;
        ~CItem() = default;
    };
public:
    /**
     * @brief 添加碰撞项
     * @param item 碰撞项
     * @return 是否添加成功
    */
    virtual bool addObject(const CItem& item)
    {
        WDUnused(item);
        return false;
    }
    /**
     * @brief 使用碰撞项与已添加的所有项进行碰撞检测
     * @param item 碰撞项
     * @param pOutItem 可选项，是否获取与item碰撞的项
     * @return 是否与已添加的某个项发生了碰撞，返回true，否则返回false
    */
    virtual bool checkObject(const CItem& item, CItem* pOutItem = nullptr) const
    {
        WDUnused(item);
        WDUnused(pOutItem);
        return false;
    }
public:
    /**
     * @brief 添加碰撞项
     */
    template <typename TObject>
    inline bool addTObject(const TObject& object, int groupId = -1, int typeId = -1)
    {
        return addObject(CItem(CObject(object), groupId, typeId));
    }
    /**
     * @brief 检查碰撞项
     */
    template <typename TObject>
    inline bool checkTObject(const TObject& object, int groupId = -1, int typeId = -1, CItem* pOutItem = nullptr) const
    {
        return checkObject(CItem(CObject(object), groupId, typeId), pOutItem);
    }
public:
    bool addLines(const std::vector<DVec3>& points
        , int groupId
        , const WDDIMLineStyle& style = WDDIMLineStyle()
        , int typeId = -1);
    bool addBrokenLine(const std::vector<DVec3>& points
        , int groupId
        , const WDDIMLineStyle& style = WDDIMLineStyle()
        , int typeId = -1);
    bool addLoopLine(const std::vector<DVec3>& points
        , int groupId
        , const WDDIMLineStyle& style = WDDIMLineStyle()
        , int typeId = -1);
    bool addRect(const DVec3& center
        , const DVec2& size
        , const DVec3& xAxis
        , const DVec3& planeNormal
        , const WDDIMLineStyle& style
        , int groupId
        , int typeId = -1);
    bool addCircle(const DVec3& center
        , const double& redius
        , const DVec3& planeNormal
        , const WDDIMLineStyle& style
        , int groupId
        , int typeId = -1);
    bool checkLines(const std::vector<DVec3>& points
        , int groupId
        , const WDDIMLineStyle& style = WDDIMLineStyle()
        , int typeId = -1
        , CItem* pOutItem = nullptr) const;
    bool checkBrokenLine(const std::vector<DVec3>& points
        , int groupId
        , const WDDIMLineStyle& style = WDDIMLineStyle()
        , int typeId = -1) const;
    bool checkLoopLine(const std::vector<DVec3>& points
        , int groupId
        , const WDDIMLineStyle& style = WDDIMLineStyle()
        , int typeId = -1) const;
    bool checkRect(const DVec3& center
        , const DVec2& size
        , const DVec3& xAxis
        , const DVec3& planeNormal
        , const WDDIMLineStyle& style
        , int groupId
        , int typeId = -1) const;
    bool checkCircle(const DVec3& center
        , const double& radius
        , const DVec3& planeNormal
        , const WDDIMLineStyle& style
        , int groupId
        , int typeId = -1) const;
};


WD_NAMESPACE_END
