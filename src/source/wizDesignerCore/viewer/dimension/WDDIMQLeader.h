#pragma once

#include "WDDIMObject.h"

WD_NAMESPACE_BEGIN

/**
 * @brief 引线标注
*/
class WD_API WDDIMQLeader : public WDDIMObject
{
public:
    // 文本对象列表
    using Texts = std::vector<std::pair<std::string, WDDIMFontStyle> >;
    // 引线线段的碰撞类型id(随便取的值), 用于检测引线线段之间的交叉避让
    static constexpr int CSegmentTypeId = 5535;
public:
    int _id = -1;
    // 标注点(即引线的开始点)
    DVec3 pos = DVec3::Zero();
    // 引线标注点列表（按顺序分别是开始点、中转点、中转点...）
    // 按顺序连接起来，然后连向texTpos，就是一个折线的引线。如果列表只有一个点，那么就是一条直线的引线
    //std::vector<DVec3> _posList;
    // 多行文本, 数组中的每一项代表一行文本
    Texts texts = {};
    // 文本位置
    DVec3 textPos = DVec3::Zero();
    // 文本的右方向
    DVec3 textRightDir = DVec3::AxisX();
    // 文本的上方向
    DVec3 textUpDir = DVec3::AxisY();
    // 文本的对齐方式,结合文本位置使用
    WDDIMAlign textAlign = WDDIMAlign();
    // 文本的每一行的对齐方式
    WDDIMAlign::HAlign rowAlign = WDDIMAlign::HAlign::HA_Left;
    // 引线结束点的位置索引,索引的取值范围[-1,texts.size()]
    // !注意: 包含text.size()的值, 当index为-1时，表示不画引线，当index为text.size()时，表示引线指向文本的最后一行末尾
    int leaderIndex = 1;
    // 引线的线样式
    WDDIMLineStyle lineStyle = WDDIMLineStyle();
    // 引线的箭头样式
    WDDIMArrowStyle arrowStyle = WDDIMArrowStyle();

    // 用户参数, 用于标签避让之后，重新计算标注以及文本的位置
    std::any uParam = {};
    bool _supMirrorImage = false;
public:
    WDDIMQLeader();
    virtual ~WDDIMQLeader();
public:
    /**
     * @brief 计算连接引线的可能的最近点(即获取包围文本的盒子的四个顶点，然后计算与pos(标注点)最近的顶点即为结果点
     * @param painter 绘制对象，用于计算文字大小
     * @param pos 标注点(即引线的开始点)
     * @param textPos 文本位置
     * @param texts 多行文本, 数组中的每一项代表一行文本
     * @param textRightDir 文本的右方向
     * @param textUpDir 文本的上方向
     * @param align  文本的对齐方式,结合文本位置使用
     * @param pOutLeaderIndex 输出的引线索引，可选项
     * @return 最近引线点
     */
    static DVec3 CalcNearestLeadPoint(WDDIMPainter& painter
        , const DVec3& pos
        , const DVec3& textPos
        , const Texts& texts
        , const DVec3& textRightDir
        , const DVec3& textUpDir
        , const WDDIMAlign& textAlign
        , int* pOutLeaderIndex = nullptr);
    /**
     * @brief 指定引线索引, 计算连接引线的可能的最近点
     * @param painter 绘制对象，用于计算文字大小
     * @param pos 标注点(即引线的开始点)
     * @param textPos 文本位置
     * @param texts 多行文本, 数组中的每一项代表一行文本
     * @param qleadIndex 引线索引, 取值范围[0, texts.size()]
     * @param textRightDir 文本的右方向
     * @param textUpDir 文本的上方向
     * @param align 文本的对齐方式,结合文本位置使用
     * @return 最近引线点
     */
    static DVec3 CalcNearestLeadPoint(WDDIMPainter& painter
        , const DVec3& pos
        , const DVec3& textPos
        , const Texts& texts
        , int qleadIndex
        , const DVec3& textRightDir
        , const DVec3& textUpDir
        , const WDDIMAlign& textAlign);
protected:
    virtual void onDraw(WDDIMPainter& painter) override;
    virtual bool onCheckCObject(const WDDIMCollision& collision) const override;
    virtual bool onAddCObject(WDDIMCollision& collision)override;
private:
    // 指定计算好的文字尺寸，计算引线点
    static DVec3 CalcNearestLeadPoint(const DVec2& allSz
        , const DVec3& pos
        , const DVec3& textPos
        , const Texts& texts
        , int qleadIndex
        , const DVec3& textRightDir
        , const DVec3& textUpDir
        , const WDDIMAlign& textAlign);
private:
    DVec2 _textsAllSz = DVec2::Zero();
};

WD_NAMESPACE_END
