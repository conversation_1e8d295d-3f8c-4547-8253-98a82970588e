#pragma once

#include "WDDIMObject.h"
#include "../../font/WDFont.h"

WD_NAMESPACE_BEGIN

/**
 * @brief 半径标注
*/
class WD_API WDDIMRadius : public WDDIMObject
{
private:
    // 起点
    DVec3   _sPos;
    // 终点
    DVec3   _ePos;
    // 引线, 表示 显示距离文本对象T 在 起点和终点构成线段对象A的哪一侧，并且指定了T到A的距离
    DVec3   _lead;
    // 字体对象
    WDFont::SharedPtr _pFont;
public:
    WDDIMRadius();
    virtual ~WDDIMRadius();
};

WD_NAMESPACE_END
