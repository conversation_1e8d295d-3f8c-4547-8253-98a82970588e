#pragma     once

#include    "../math/Math.hpp"
#include    "../GL/WDOpenGL.h"
#include    "../GL/WDResource.h"
#include    "../GL/WDGLContext.h"
#include    "../common/WDContext.h"
#include    "../cameras/WDOrthographicCamera.h"
#include    "../cameras/WDPerspectiveCamera.h"
#include    "../extension/WDInput.h"
#include    "../scene/WDScene.h"
#include    "../GL/WDFrameBufferObject.h"
#include    "WDClip.h"
#include    "WDViewerCube.h"
#include    "capturePositioning/WDCapturePositioning.h"
#include    "objectAxisEditor/WDObjectAxisEditor.h"
#include    "WDSingleMoveAxisMgr.h"
#include    "WDBrowseAxisMgr.h"
#include    "WDViewerCube.h"
#include    "WDViewerSceneAxis.h"
#include    "../input/WDInputListener.h"

WD_NAMESPACE_BEGIN

class   WDCore;

class   WDEvent;
class   WDResizeEvent;
class   WDUpdateEvent;
class   WDPaintEvent;
class   WDMouseEvent;
class   WDWheelEvent;
class   WDKeyEvent;
class   WDPluginToolSet;
class   WDEditAxisMove;
class   WDEditAxisRotate;

WD_DECL_CLASS_UUID(WDViewer,"67FF8E6F-D271-48F0-9EA4-29B702C96A6A");

/**
 * @brief 视图对象，三维视图
 */
class WD_API WDViewer 
    : public WDObject
    , public WDInput
{

public:
    WD_DECL_OBJECT(WDViewer)
public:
    /**
    * @brief 需要重绘通知,收到该通知后,由ui层处理重绘
    */
    using NativeNeedRepaint =   WDTDelegate<void(WDViewer* pSender)>;
    using NativeMakeCurrent =   WDTDelegate<void(WDViewer* pSender)>;
    
    /**
    *   @brief 需要支持输入(鼠标键盘)消息的对象需要实现该接口，并加入到viewer中
    */
    using   Inputs          =   std::vector<WDInput*>;
    using   FBOPtr          =   std::shared_ptr<WD::WDFrameBufferObject>;

    /**
     * @brief 指定场景中所有对象的多边形绘制模式，指定多边形以何种方式绘制
    */
    enum PolygonMode
    {
        // 填充
        PM_Fill = 0,
        // 线框
        PM_Line,
        // 点
        PM_Point,
    };
    /**
     * @brief 指定场景中所有对象的绘制面的方式
    */
    enum DrawFaceMode
    {
        // 只绘制正面
        DFM_FrontSide = 0,
        // 只绘制反面
        DFM_BackSide, 
        // 双面绘制
        DFM_DoubleSide,
    };
private:
    //
    WDContext*          _context;
    //core app
    WDCore&             _app;
    // 视图大小
    IVec2               _viewSize;
    // 场景对象
    WDScene::SharedPtr  _pScene;
    WDCamera::SharedPtr _camera;
    //浏览目标点
    Vec3                _browseTarget;
    // 绘制上下文
    WDGLContext         _glContext;
    // OpenGL对象
    WDOpenGL            _device;
    // shader资源
    WDResource          _resource;
    // 工具集
    WDPluginToolSet*    _toolSet;
    //需要重绘通知
    NativeNeedRepaint   _nativeNeedRepaint;
    NativeMakeCurrent   _makeCurrent;
    //背景色
    Color               _backColor;
    real                _testRadio;
    Inputs              _inputs;
    /// gpu 共享数据
    UniformBuffer::SharedPtr    _gpuSharead;
    /// 每一个viewer 都有一个clip对象
    WDClip              _clip;
    bool                _actived    =   false;
    /// 界面
    FBOPtr              _layerOverlay;
    int                 _multiSample    = 0;

    PolygonMode         _polygonMode = PolygonMode::PM_Fill;
    DrawFaceMode        _drawFaceMode = DrawFaceMode::DFM_FrontSide;
    FBOPtr              _fboForSnapshot;
    // 视图盒子
    WDViewerCube         _viewerCube;
    // 场景轴
    WDViewerSceneAxis    _viewerSceneAxis;
    // 捕捉定位工具
    WDCapturePositioning _capturePositioning;
    // 编辑轴管理工具
    WDObjectAxisEditor  _objectAxisEditor;
    // 浏览轴管理
    WDBrowseAxisMgr     _browseAxisMgr;
    // 单移动轴管理
    WDSingleMoveAxisMgr _singleMoveAxisMgr;
    // 鼠标键盘输入监听者管理对象
    WDInputListenerMgr  _inputListenerMgr;

    FBOPtr              _mainFBO    =   nullptr;

    // 是否激活, 未激活时，将不会响应任何 输入事件(鼠标，键盘) 和 更新事件
    bool                _enabled    =   true;
    // 图形界限包围盒， 如果设置了图形界限包围盒
    //  则绘制模型时，将该包围盒外的部分裁剪掉
    std::optional<DAabb3> _limitsAabb =   std::nullopt;
public:
    WDViewer(WDCore& app,int ms);
    WDViewer(const WDViewer& right) = delete;
    WDViewer(WDViewer&& right) = delete;
    WDViewer& operator=(const WDViewer& right) = delete;
    WDViewer& operator=(WDViewer&& right) = delete;
    virtual ~WDViewer();
public:
    /**
    * @brief 获取core app
    */
    inline WDCore& app()
    {
        return _app;
    }
    /**
    * @brief 获取context
    */
    inline WDContext& context()
    {
        return *_context;
    }
    /**
     * @brief 初始化
    */
    void    init();
    /**
     * @brief 激活
    */
    void    active();
    /**
     * @brief 去激活
    */
    void    deactive();
    /**
     * @brief 是否激活
    */
    bool    isActived() const
    {
        return  _actived;
    }
    /**
     * @brief 设置是否激活， 默认为true
     *  非激活状态时，将不会响应任何 输入事件(鼠标、键盘)和更新绘制事件
     * @param enabled 是否激活
    */
    inline void setEnabled(bool enabled)
    {
        _enabled = enabled;
    }
    /**
     * @brief 获取是否激活
    */
    inline bool enabled() const
    {
        return _enabled;
    }
    /**
     * @brief 多重采样个数
     */
    inline int multiSample() const 
    {
        return _multiSample;
    }
    /**
     * @brief FBO
     */
    FBOPtr  snapshotFBO()
    {
        return  _fboForSnapshot;
    }
    /**
     * @brief 设置锚点
    */
    void setBrowseTarget(const Vec3& target)
    {
        _browseTarget   =  target; 
    }
    /**
     * @brief 获取锚点
    */
    Vec3 browseTarget() const
    {
        return  _browseTarget;
    }
    /**
     * @brief 剖切对象
    */
    WDClip& clip()
    {
        return  _clip;
    }
    /**
     * @brief 剖切对象
    */
    const WDClip& clip() const
    {
        return  _clip;
    }
    /**
     * @brief 设置视图大小
    */
    void resize(const IVec2& size);
    /**
     * @brief 获取视图大小
    */
    inline const IVec2& size() const
    {
        return _viewSize;
    }
    /**
     * @brief 设置多边形绘制模式
    */
    inline void setPolygonMode(PolygonMode mode)
    {
        _polygonMode = mode;
    }
    /**
    * @brief 获取多边形绘制模式
    */
    inline PolygonMode polyginMode() const 
    {
        return _polygonMode;
    }
    /**
    * @brief 设置多边形绘制模式
    */
    inline void setDrawFaceMode(DrawFaceMode mode)
    {
        _drawFaceMode = mode;
    }
    /**
    * @brief 获取多边形绘制模式
    */
    inline DrawFaceMode drawFaceMode() const 
    {
        return _drawFaceMode;
    }
    /**
     * @brief 设置背景色
    */
    inline void setBackColor(const Color& color)
    {
        _backColor = color;
    }
    /**
     * @brief 获取背景色
    */
    inline const Color& backColor() const
    {
        return _backColor;
    }
    /**
    * @brief 获取context
    */
    inline const WDContext& context() const
    {
        return *_context;
    }
    /**
    * @brief 绘制上下文
    */
    inline WDGLContext& glContext()
    {
        return _glContext;
    }
    /**
    * @brief 获取OpenGL对象
    */
    inline WDOpenGL& device()
    {
        return _device;
    }
    /**
    * @brief 获取shader资源
    */
    inline WDResource& resource()
    {
        return _resource;
    }
    /**
    * @brief 获取shader资源
    */
    inline const WDResource& resource() const
    {
        return _resource;
    }
    /**
    * @brief 获取摄像机
    */
    inline WDCamera::SharedPtr& camera()
    {
        return  _camera;
    }
    /**
    * @brief 获取摄像机
    */
    inline const WDCamera::SharedPtr& camera() const
    {
        return  _camera;
    }

    /**
    * @brief 设置场景对象
    */
    void setScene(WDScene::SharedPtr scene);
    /**
    * @brief 获取场景对象
    */
    inline WDScene::SharedPtr scene() const
    {
        return _pScene;
    }
    /**
    * @brief 获取工具集对象
    */
    inline WDPluginToolSet& toolSet()
    {
        return *_toolSet;
    }
    /**
    * @brief 获取工具集对象
    */
    inline const WDPluginToolSet& toolSet() const
    {
        return *_toolSet;
    }
    /**
    * @brief 触发重绘
    */
    void repaint();
    /**
    * @brief 发送重绘通知, 从而触发重绘
    */
    void needRepaint();
    /**
    *   @brief 添加输入对象
    */
    bool    addInput(WDInput* input);
    /**
    *   @brief 移除输入对象
    */
    bool    removeInput(WDInput* input);
    /**
    *   @brief 清除所有输入对象
    */
    void    clearInput();
    /**
    *   @brief 获取对象引用
    */
    Inputs& inputs();
    /**
    *   @brief 获取对象引用(只读)
    */
    const Inputs& inputs() const;
    /**
    *   @brief 调用该函数，同步WDContext::_gpuShared数据到gpu
    *   函数默认在每一帧绘制之前同步,考虑到修改像机/视口大小等情况，提供手动同步方法
    */
    void    updateToGPU();
    /**
     * @brief 获取视图盒子
    */
    inline WDViewerCube& viewerCube()
    {
        return _viewerCube;
    }
    /**
     * @brief 获取视图场景轴
    */
    inline WDViewerSceneAxis& viewerSceneAxis()
    {
        return _viewerSceneAxis;
    }
    /**
     * @brief 获取捕捉定位工具
    */
    inline WDCapturePositioning& capturePositioning()
    {
        return _capturePositioning;
    }
    /**
     * @brief 获取编辑轴管理工具
    */
    inline WDObjectAxisEditor& objectAxisEditor()
    {
        return _objectAxisEditor;
    }
    /**
     * @brief 获取浏览轴轴管理对象
    */
    inline WDBrowseAxisMgr& browseAxisMgr()
    {
        return _browseAxisMgr;
    }
    /**
     * @brief 获取单移动轴管理
    */
    inline WDSingleMoveAxisMgr& singleMoveAxisMgr()
    {
        return _singleMoveAxisMgr;
    }
    /**
    * @brief 获取鼠标键盘输入监听者管理对象
    */
    inline WDInputListenerMgr& inputListenerMgr()
    {
        return _inputListenerMgr;
    }
public:
    /**
    * @brief 移动视角
    */
    void moveView(const IVec2& prevMousePos, const IVec2& currMousePos);
    /**
    * @brief 旋转视角
    */
    void rotateView(const IVec2& prevMousePos, const IVec2& currMousePos);
    /**
    * @brief 缩放视角
    */
    void zoomView(int mouseDelta);
    /**
    * @brief 缩放视角
    */
    void zoomView(float persent);
    /**
    * @brief 指定摄像机朝向
    */
    void lookAt(const Vec3& target, const Vec3& up);
    /**
    * @brief 指定摄像机朝向
    * @param aabb 包围盒
    * @param bLimits 是否设置当前包围盒为绘制范围限定包围盒, 如果是，则场景绘制时，将采用该包围盒来裁剪三维模型的绘制效果
    */
    void lookAtAabb(const Aabb3& aabb, bool bLimits = false);
    /**
     * @brief 决定视角
    */
    enum LookAtDir
    {
        LAD_Front = 0,
        LAD_Back,
        LAD_Left,
        LAD_Right,
        LAD_Top,
        LAD_Bottom,

        LAD_FrontUp,
        LAD_FrontDown,
        LAD_FrontLeft,
        LAD_FrontRignt,

        LAD_BackUp,
        LAD_BackDown,
        LAD_BackLeft,
        LAD_BackRight,

        LAD_LeftUp,
        LAD_LeftDown,
        LAD_LeftLeft,
        LAD_LeftRight,

        LAD_RightUp,
        LAD_RightDown,
        LAD_RightLeft,
        LAD_RightRight,

        LAD_FrontUpLeft,
        LAD_FrontUPRight,
        LAD_FrontDownLeft,
        LAD_FrontDownRight,

        LAD_BackUpLeft,
        LAD_BackUPRight,
        LAD_BackDownLeft,
        LAD_BackDownRight,

        LAD_North,
        LAD_South,
        LAD_East,
        LAD_West,

        LAD_ISO1,
        LAD_ISO2,
        LAD_ISO3,
        LAD_ISO4,
    };
    /**
    * @brief 指定摄像机朝向
    * @param aabb 包围盒 
    * @param dir 视角方向
    * @param bLimits 是否设置当前包围盒为绘制范围限定包围盒, 如果是，则场景绘制时，将采用该包围盒来裁剪三维模型的绘制效果
    */
    void lookAtAabb(const Aabb3& aabb, LookAtDir dir, bool bLimits = false);
    /**
     * @brief 是否有绘制范围限定包围盒
     *  这个值通常由
     *      lookAtAabb 的 bLimits参数指定
     */
    bool hasLimitsAabb() const;
public:
    /**
    * @brief 指定视图窗口坐标区域, 截取GL窗口场景
    * @param x 目标区域左下角坐标X(原因是视图原点坐标在左下角)
    * @param y 目标区域左下角坐标Y(原因是视图原点坐标在左下角)
    * @param w 目标区域宽
    * @param h 目标区域高
    * @readBufferId 当前默认的read缓冲区
    *   如果在外部传入，则需要将view makeCurrent()
    *   同时传入qopengl的defaultFramebufferObject()获取的buffId
    * @return 图片对象, 图片对象无效时表示截图失败，请检查参数
    */
    WDImage getSnapShotImage(int x
        , int y
        , int w
        , int h
        , int readBufferId = -1);
    /**
     * @brief 指定相机定位的参数，截取GL窗口场景
     * @param aabb 包围盒, 用于计算视口居中, 如果值无效(空包围盒)，不做相机定位
     * @param frontDir 摄像机的前方向, 如果值无效(0向量)，不做相机定位
     * @param upDir 摄像机的上方向, 如果值无效(0向量)，不做相机定位
     * @param recover 是否截图后恢复视口
     * @readBufferId 当前默认的read缓冲区
     *   如果在外部传入，则需要将view makeCurrent()
     *   同时传入qopengl的defaultFramebufferObject()获取的buffId
     * @return 图片对象, 图片对象无效时表示截图失败，请检查参数
    */
    WDImage getSnapShotImage(const DAabb3& aabb
        , const DVec3& frontDir
        , const DVec3& upDir
        , bool recover = true
        , int readBufferId = -1);
public:
    /**
    * @brief 获取需要重绘通知
    */
    inline NativeNeedRepaint & nativeNeedRepaint()
    {
        return _nativeNeedRepaint;
    }
    /**
     * @brief 切换当前上下文通知
    */
    inline NativeMakeCurrent&  makeCurrent()
    {
        return  _makeCurrent;
    }
public:
    /**
    * @brief 事件响应
    */
    virtual bool onEvent(WDEvent* evt) override;
protected:
    /**
    *   @brief 事件分发
    */
    void    dispachEvent(WDEvent* e);
    /**
    *   @brief 视图大小改变事件
    */
    void    resizeEvent(WDResizeEvent* e);
    /**
    *   @brief 更新事件
    */
    void    updateEvent(WDUpdateEvent* e);
    /**
    *   @brief 绘制事件
    */
    void    paintEvent(WDPaintEvent* e);
private:
    // 
    void drawTexture(WDContext& ctx, WDTexture* tex, const IVec2& pos, const IVec2& size);
    // 场景包围盒改变通知响应
    void onSceneAabbChanged(const DAabb3& currAabb);
    // 获取绘图范围包围盒，用于实现居中定位后的裁剪效果
    DAabb3 limitsAabb() const;
};

WD_NAMESPACE_END


