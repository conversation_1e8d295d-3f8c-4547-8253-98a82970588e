#include    "WDViewer.h"

#include    "../events/WDMouseEvent.h"
#include    "../events/WDWheelEvent.h"
#include    "../events/WDKeyEvent.h"
#include    "../events/WDHoverEvent.h"
#include    "../events/WDResizeEvent.h"
#include    "../events/WDCloseEvent.h"
#include    "../events/WDPaintEvent.h"
#include    "../events/WDUpdateEvent.h"
#include    "../extension/WDPluginToolSet.h"
#include    "../scene/WDScene.h"
#include    "../WDCore.h"
#include    "../cameras/WDCamera.h"
#include    "../common/WDDir.h"
#include    "../input/WDKey.h"
#include    "../cameras/WDOrthographicCamera.h"
#include    "../axis/WDEditAxisMove.h"
#include    "../axis/WDEditAxisRotate.h"
#include    "../font/WDFont.h"

#include    "../math/DirectionParser.h"
#include    "log/WDLoggerPort.h"


#include    "../GL/WDGLHeader.h"
#ifdef LINUX 
#include   "GL/glx.h"
#elif  WD_PLATFORM == WD_PLATFORM_APPLE
#include   "GL/glx.h"
#endif

#include    "../nodeTree/WDNodeTree.h"
#include    "../common/WDConfig.h"
#include    "../WDObjectsMgr.h"

#include    <chrono>

WD_NAMESPACE_BEGIN


extern  bool    checkError();

/**
 * @brief 获取场景的包围盒数据
 *  如果场景对象为nullptr,则返回场景的默认包围盒
*/
const DAabb3& GetSceneAabb(WDScene::SharedPtr pScene) 
{
    if (pScene == nullptr)
        return WDScene::DefaultAabb();
    return pScene->aabb();
}

/**
 * @brief 使用指定包围盒重新更新相机参数, 包围盒不能为空, 否则不会更新
 *  注意: 目前只针对正交投影相机; 由于正交投影相机的眼睛不会决定模型大小(不像透视投影，有近大远小的效果);
 *      但是眼睛位置会参与裁剪，因此眼睛过近时，部分模型虽然在视口内，但是被裁剪掉了；
 *      因此，这里直接将眼睛计算到离包围盒半径的位置，以保证眼睛位置不会裁剪包围盒内的任何物体
 * @param pCamera 相机对象
 * @param aabb 包围盒
 * @param viewSize 视图窗口尺寸
 * @param frontDir 更新完成后，相机默认的前方向
 * @param upDir 相机默认的上方向
*/
void CalcOrthoCameraByAabb(WDCamera::SharedPtr pCamera
    , const DAabb3& aabb
    , const IVec2& viewSize
    , const DVec3& frontDir
    , const DVec3& upDir)
{
    if (aabb.isNull())
    {
        assert(false);
        return;
    }
    auto pOrtho = WDOrthographicCamera::SharedCast(pCamera);
    if (pOrtho == nullptr)
    {
        assert(false);
        return;
    }

    real size       = aabb.size().length();
    real halfSize   = size * 0.5;

    real aspect     = 1.0;
    if (viewSize.lengthSq() > 0)
        aspect = real(viewSize.y) / real(viewSize.x);

    pOrtho->setLeft(-halfSize);
    pOrtho->setRight(halfSize);
    pOrtho->setTop(halfSize * aspect);
    pOrtho->setBottom(-halfSize * aspect);
    pOrtho->setZoom(1.0);
    pOrtho->setZFar(size);

    // 这里根据包围盒尺寸自动设置相机的近裁剪面距离（经验值，随便写的, 后续可根据实际情况调整)
    if (size < 10)
        pOrtho->setZNear(0.0001);
    else if (size < 1000.0)
        pOrtho->setZNear(0.001);
    else if (size < 10000)
        pOrtho->setZNear(0.01);
    else if (size < 100000)
        pOrtho->setZNear(0.1);
    else if (size < 1000000)
        pOrtho->setZNear(1);
    else if (size < 10000000)
        pOrtho->setZNear(10.0);
    else if (size < 100000000)
        pOrtho->setZNear(100.0);
    else
        pOrtho->setZNear(1000.0);

    DVec3 target    = aabb.center();
    DVec3 eye       = target - frontDir * halfSize;

    pOrtho->lookAt(eye, target, upDir);

    pOrtho->update();
}
/**
 * @brief 指定相机定位到Aabb，保证Aabb的中心点在视图窗口中心，根据指定的front和up dir 改变相机朝向
 * @param pCamera 相机对象
 * @param sceneAabb 场景包围盒，用于判定指定的包围盒是否被场景包围盒包含，如果未被包含，则需要扩充包围盒重新计算相机参数
 * @param viewSize 视图尺寸
 * @param aabb 包围盒
 * @param frontDir 前方向
 * @param upDir 上方向
*/
void CalcOrthoCameraLookAt(WDCamera::SharedPtr pCamera
    , const DAabb3& sceneAabb
    , const IVec2& viewSize
    , const DAabb3& aabb
    , const DVec3& frontDir
    , const DVec3& upDir)
{
    auto pOrtho = WDOrthographicCamera::SharedCast(pCamera);
    if (pOrtho == nullptr)
        return;

    // 这里需要保证场景的包围盒一定包含指定的aabb，如果不包含，则强制包含进去，重新计算相机参数
    if (!sceneAabb.contains(aabb))
    {
        auto tAabb = sceneAabb;
        tAabb.unions(aabb);
        CalcOrthoCameraByAabb(pCamera, tAabb, viewSize, frontDir, upDir);
    }

    DVec3 target = aabb.center();
    real size = aabb.size().length();
    real zoom = (pOrtho->top() - pOrtho->bottom()) / size;
    pOrtho->setZoom(zoom);

    DPlane plane(pOrtho->frontDir(), pOrtho->eye());
    auto dis = Abs(plane.distance(target));
    DVec3 eye = target - frontDir * dis;
    pOrtho->lookAt(eye, target, upDir);

    pOrtho->update();
}
/**
 * @brief 指定相机定位到Aabb，保证Aabb的中心点在视图窗口中心，不会改变相机朝向
 * @param pCamera 相机对象
 * @param sceneAabb 场景包围盒，用于判定指定的包围盒是否被场景包围盒包含，如果未被包含，则需要扩充包围盒重新计算相机参数
 * @param aabb 包围盒
*/
void CalcOrthoCameraLookAt(WDCamera::SharedPtr pCamera
    , const DAabb3& sceneAabb
    , const IVec2& viewSize
    , const DAabb3& aabb)
{
    if (pCamera == nullptr)
        return;
    CalcOrthoCameraLookAt(pCamera, sceneAabb, viewSize, aabb, pCamera->frontDir(), pCamera->upDir());
}


// 获取到轴使用的鼠标按键
WDEditAxis::MouseButton GetEditAxisMouseButton(WDMouseEvent::MouseButton btn)
{
    if (btn & WDMouseEvent::MouseButton::MB_LeftButton)
        return WDEditAxis::MB_Left;
    else if (btn & WDMouseEvent::MouseButton::MB_MiddleButton)
        return WDEditAxis::MB_Middle;
    else if (btn & WDMouseEvent::MouseButton::MB_RightButton)
        return WDEditAxis::MB_Right;
    else
        return WDEditAxis::MB_None;
}

WDViewer::WDViewer(WDCore& app,int ms) 
    : _app(app)
    , _clip(app)
    , _viewerCube(*this)
    , _viewerSceneAxis(*this)
    , _capturePositioning(*this)
    , _objectAxisEditor(*this)
    , _browseAxisMgr(*this)
    , _singleMoveAxisMgr(*this)
    , _inputListenerMgr()
{
    _pScene             =   nullptr;
    _multiSample        =   ms;
    _context            =   new WDContext(*this);
    _resource._context  =   _context;
    _backColor          =   Color(204,204,204);
    
    _toolSet            =   new WDPluginToolSet(*_context, app.extensionMgr());

    //初始化相机参数
    _camera             =   WDOrthographicCamera::MakeShared();
    CalcOrthoCameraByAabb(_camera, WDScene::DefaultAabb(), _viewSize, DVec3::AxisY(), DVec3::AxisZ());
    _context->setCamera(_camera);

    // 背景颜色
    app.cfg().get<std::string>("scene")
        .get<Color>("backgroud.color", _backColor)
        .setDisplayText("Background color")
        .bindValuePtr(&_backColor);
}
WDViewer::~WDViewer()
{
    if (_toolSet != nullptr)
    {
        delete _toolSet;
        _toolSet = nullptr;
    }
    this->setScene(nullptr);
}

void    WDViewer::init()
{
    LOG_INFO << "WDViewer 初始化 开始";
#ifdef WIN32
    _glContext._hRC = wglGetCurrentContext();
    _glContext._hDC = wglGetCurrentDC();
#else
    _glContext._hRC = glXGetCurrentContext();
    _glContext._hDC = glXGetCurrentDisplay();
#endif
    LOG_INFO << "WDViewer initExt";
    _glContext.initExt();


    _gpuSharead =   UniformBuffer::MakeShared();
    _gpuSharead->create(sizeof(_context->_gpuShared),&_context->_gpuShared);
    _gpuSharead->setBindPoint(0);
    _gpuSharead->unBind();
    
    
    LOG_INFO << "WDViewer 初始化 shader include ";
    /// 初始化 shader include 

    std::string shaderDir   =   std::string(_app.exeDirPath()) + "../data/shader/";

    WD::WDDir   dir;
    dir.loadEvent().bind([](const char* , const char* fullName, const char* , const char* )->bool
        {
            WDProgram::addInclude(fullName);
            return  true;
        }
    );
    //dir.findFile(shaderDir.c_str(),"glsl");
    
    
    LOG_INFO << "WDViewer 初始化设备";
    glEnable(GL_LINE_STIPPLE);
    // 初始化设备
    _resource.initialize(&_device);

    checkError();

    WDFont* pDefFont    = _app.objectsMgr().query<WDFont>("font:default");
    if (pDefFont == nullptr)
    {
        std::string fontFile    = std::string(_app.exeDirPath()) + "../data/font/simhei.ttf";
        auto pFont              = WDFont::MakeShared(fontFile, "font:default");
        _app.objectsMgr().cache(pFont.get(), pFont->name());
    }

    /// 初始化layer
    _layerOverlay   =   WDFrameBufferObject::MakeShared();
    if (_multiSample == 0)
    {
        /// 创建纹理
        /// 
        auto        colorObject =   WDTexture2d::MakeShared();
        colorObject->create( 16
                            ,16
                            ,WDTexture::Format::Format_RGBA
                            ,WDTexture::Type::Type_Byte
                            ,WDTexture::Format::Format_RGBA
                            ,nullptr);

        auto        depthObject =   WDTexture2d::MakeShared();
        depthObject->create( 16
                            ,16
                            ,WDTexture::Format::Format_Depth32
                            ,WDTexture::Type::Type_Float
                            ,WDTexture::Format::Format_Depth32
                            ,nullptr);

        WDFrameBufferObject::Attach aColor;
        aColor._obj     =   colorObject->toPtr<WDGPUObject>();
        aColor._layer   =   0;
        aColor._level   =   0;
        aColor._page    =   0;

        WDFrameBufferObject::Attach aDepth;
        aDepth._obj     =   depthObject->toPtr<WDGPUObject>();
        aDepth._layer   =   0;
        aDepth._level   =   0;
        aDepth._page    =   0;

        _layerOverlay->attachColorBufferr(aColor);
        _layerOverlay->attachDepthBuffer(aDepth);
        _layerOverlay->bindAttach();
    }
    else
    {
        auto        colorObject =   WDTexture2dMS::MakeShared();
        colorObject->create( 16
                            ,16
                            ,WDTexture::Format::Format_RGBA
                            ,_multiSample);

        auto        depthObject =   WDTexture2dMS::MakeShared();
        depthObject->create( 16
                            ,16
                            ,WDTexture::Format::Format_Depth32
                            ,_multiSample);

        WDFrameBufferObject::Attach aColor;
        aColor._obj     =   colorObject->toPtr<WDGPUObject>();
        aColor._layer   =   0;
        aColor._level   =   0;
        aColor._page    =   0;

        WDFrameBufferObject::Attach aDepth;
        aDepth._obj     =   depthObject->toPtr<WDGPUObject>();
        aDepth._layer   =   0;
        aDepth._level   =   0;
        aDepth._page    =   0;

        _layerOverlay->attachColorBufferr(aColor);
        _layerOverlay->attachDepthBuffer(aDepth);
        _layerOverlay->bindAttach();
        
    }
    /// 快照使用的 frame buffer object 
    {
        _fboForSnapshot =   WD::WDFrameBufferObject::MakeShared();

       
        auto        colorObject =   WDTexture2d::MakeShared();
        colorObject->create( 64
                            ,64
                            ,WDTexture::Format::Format_RGBA
                            ,WDTexture::Type::Type_Byte
                            ,WDTexture::Format::Format_RGBA
                            ,nullptr);

        auto        depthObject =   WDTexture2d::MakeShared();
        depthObject->create( 64
                            ,64
                            ,WDTexture::Format::Format_Depth32
                            ,WDTexture::Type::Type_Float
                            ,WDTexture::Format::Format_Depth32
                            ,nullptr);

        WDFrameBufferObject::Attach aColor;
        aColor._obj     =   colorObject->toPtr<WDGPUObject>();

        WDFrameBufferObject::Attach aDepth;
        aDepth._obj     =   depthObject->toPtr<WDGPUObject>();

        _fboForSnapshot->attachColorBufferr(aColor);
        _fboForSnapshot->attachDepthBuffer(aDepth);
        _fboForSnapshot->bindAttach();
    }

    LOG_INFO << "WDViewer 初始化 完成";
}

void    WDViewer::active()
{
    _actived    =   true;
}
void    WDViewer::deactive()
{
    _actived    =   false;
}

void    WDViewer::setScene(WDScene::SharedPtr pScene)
{
    // 记录之前的aabb
    const auto prevAabb = GetSceneAabb(_pScene);

    _pScene = pScene;

    const auto& currAabb = GetSceneAabb(_pScene);

    // 如果包围盒发生了改变，则重新计算相机参数
    if (!prevAabb.isNull() && !currAabb.isNull() && prevAabb != currAabb)
    {
        // 使用新的包围盒数据，重新更新相机
        this->onSceneAabbChanged(currAabb);
        // 更新context对象
        _context->update();
    }

    this->needRepaint();
}

void    WDViewer::resize(const IVec2 & size)
{
    if (size == _viewSize)
        return;

    IVec2 oldSize = _viewSize;
    _viewSize = size;
    if (_camera.get())
    {
        _camera->setViewSize(_viewSize);
        _camera->update(true);
    }

    if (_layerOverlay.get())
    {
        _layerOverlay->resize(_viewSize.x,_viewSize.y,0);
    }
    WDResizeEvent e(size, oldSize);
    this->onEvent(&e);
}

bool    WDViewer::onEvent(WDEvent* evt)
{
    evt->setContext(_context);
    evt->setViewer(this);

    //事件是否继续通知
    bool noticeEvent = true;

    WDEvent::EventType type = evt->type();
    switch (type)
    {
    case WD::WDEvent::ET_None:
        break;
    case WD::WDEvent::ET_UpdateEvent:
        {
            if (!_enabled)
            {
                noticeEvent = false;
                break;
            }

            WDUpdateEvent* e = static_cast<WDUpdateEvent*>(evt);
            this->updateEvent(e);
        }
        break;
    case WD::WDEvent::ET_PaintEvent:
        {
            if (!_enabled)
            {
                noticeEvent = false;
                break;
            }

            WDPaintEvent* e = static_cast<WDPaintEvent*>(evt);
            this->paintEvent(e);
        }
        break;
    case WD::WDEvent::ET_CloseEvent:
        break;
    case WD::WDEvent::ET_ResizeEvent:
        {
            WDResizeEvent* e = static_cast<WDResizeEvent*>(evt);
            this->resizeEvent(e);
        }
        break;
    case WD::WDEvent::ET_MouseEvent:
        {
            if (!_enabled)
            {
                noticeEvent = false;
                break;
            }

            WDMouseEvent* e = static_cast<WDMouseEvent*>(evt);
            auto mouseState = e->mouseState();
            IVec2 mousePos  = e->pos();
            switch (mouseState)
            {
            case WD::WDMouseEvent::MS_MouseButtonPress:
                {
                    // 视图盒子响应任何鼠标按键
                    if (noticeEvent)
                        noticeEvent = !(_viewerCube.mousePress(mousePos));

                    auto mouseBtn = GetEditAxisMouseButton(e->button());

                    if (noticeEvent)
                        noticeEvent = !(_objectAxisEditor.mousePress(mousePos, mouseBtn));

                    if (noticeEvent)
                        noticeEvent = !(_singleMoveAxisMgr.mousePress(mousePos, mouseBtn));

                    if (e->button() & WDMouseEvent::MouseButton::MB_LeftButton)
                    {
                        if (noticeEvent)
                            noticeEvent = !(_capturePositioning.mousePress(mousePos));
                        if (noticeEvent)
                            noticeEvent = _inputListenerMgr.mouseLButtonPress(*_context, mousePos);
                    }
                }
                break;
            case WD::WDMouseEvent::MS_MouseButtonDblClick:
                {
                    auto mouseBtn = GetEditAxisMouseButton(e->button());

                    if (noticeEvent)
                        noticeEvent = !(_objectAxisEditor.mouseDoubleClicked(mousePos, mouseBtn));
                    if (noticeEvent)
                        noticeEvent = !(_singleMoveAxisMgr.mouseDoubleClicked(mousePos, mouseBtn));
                }
                break;
            case WD::WDMouseEvent::MS_MouseButtonRelease:
                {
                    // 视图盒子响应任何鼠标按键
                    if (noticeEvent)
                        noticeEvent = !(_viewerCube.mouseRelease(mousePos));

                    auto mouseBtn = GetEditAxisMouseButton(e->button());

                    if (noticeEvent)
                        noticeEvent = !(_objectAxisEditor.mouseRelease(mousePos, mouseBtn));

                    if (noticeEvent)
                        noticeEvent = !(_singleMoveAxisMgr.mouseButtonRelease(mousePos, mouseBtn));

                    if (e->button() & WDMouseEvent::MouseButton::MB_LeftButton)
                    {
                        if (noticeEvent)
                            noticeEvent = !(_capturePositioning.mouseRelease(mousePos));

                        if (noticeEvent)
                            noticeEvent = _inputListenerMgr.mouseLButtonRelease(*_context, mousePos);
                    }
                }
                break;
            case WD::WDMouseEvent::MS_MouseMove:
                {
                    if (noticeEvent)
                        noticeEvent = !(_viewerCube.mouseMove(mousePos));

                    if (noticeEvent)
                        noticeEvent = !(_objectAxisEditor.mouseMove(mousePos));

                    if (noticeEvent)
                        noticeEvent = !(_singleMoveAxisMgr.mouseMove(mousePos));

                    if (noticeEvent)
                        noticeEvent = !(_capturePositioning.mouseMove(mousePos));

                    if (noticeEvent)
                        noticeEvent = _inputListenerMgr.mouseMove(*_context, mousePos);
                }
                break;
            default:
                break;
            }
        }
        break;
    case WD::WDEvent::ET_KeyEvent:
        {
            if (!_enabled)
            {
                noticeEvent = false;
                break;
            }

            WDKeyEvent*     e = static_cast<WDKeyEvent*>(evt);
            switch (e->keyState())
            {
            case WD::WDKeyEvent::KS_KeyPress:
                {
                    switch (e->key())
                    {
                    case WDKey::Key_F8:
                        {
                            auto pScene = this->scene();
                            if (pScene != nullptr)
                            {
                                auto rMode = pScene->rMode();
                                switch (rMode)
                                {
                                case WDScene::RM_Solid:
                                    pScene->setRMode(WD::WDScene::RM_WireFrame);
                                    break;
                                case WDScene::RM_WireFrame:
                                    pScene->setRMode(WD::WDScene::RM_Solid);
                                    break;
                                default:
                                    break;
                                }
                                pScene->needUpdate();
                                this->repaint();
                            }
                        }
                        break;
                    default:
                        break;
                    }

                    // 鼠标键盘输入监听者管理接收
                    _inputListenerMgr.keyPress(*_context, e->key());
                }
                break;
            case WD::WDKeyEvent::KS_KeyRelease:
                {
                    switch (e->key())
                    {
                    case WDKey::Key_F8:
                        break;
                    }

                    // 鼠标键盘输入监听者管理接收
                    _inputListenerMgr.keyRelease(*_context, e->key());
                }
                break;
            }
        }
        //触摸事件
    case WD::WDEvent::ET_TouchEvent:
        {
            if (!_enabled)
            {
                noticeEvent = false;
                break;
            }
        }
        break;
    case WD::WDEvent::ET_HoverEvent:
        {
            if (!_enabled)
            {
                noticeEvent = false;
                break;
            }
        }
        break;
    case WD::WDEvent::ET_DropEvent:
        {
            if (!_enabled)
            {
                noticeEvent = false;
                break;
            }
        }
        break;
    case WD::WDEvent::ET_DragMoveEvent:
        {
            if (!_enabled)
            {
                noticeEvent = false;
                break;
            }
        }
        break;
    case WD::WDEvent::ET_DragEnterEvent:
        {
            if (!_enabled)
            {
                noticeEvent = false;
                break;
            }
        }
        break;
    case WD::WDEvent::ET_DragLeaveEvent:
        {
            if (!_enabled)
            {
                noticeEvent = false;
                break;
            }
        }
        break;
    case WD::WDEvent::ET_RenderEvent:
        {
            if (!_enabled)
            {
                noticeEvent = false;
                break;
            }
        }
        break;
    default:
        break;
    }
    
    if (!noticeEvent)
        return false;

    // 组件事件处理
    dispachEvent(evt);
    // 工具分发:通知当前工具集
    _toolSet->onEvent(evt);
    // 事件监听分发
    _inputListenerMgr.onEvent(evt);

    return true;
}

void    WDViewer::repaint()
{
    WDUpdateEvent updateEvt;
    onEvent(&updateEvt);

    WDPaintEvent paintEvt;
    onEvent(&paintEvt);
}
void    WDViewer::needRepaint()
{
    _nativeNeedRepaint(this);
}

void    WDViewer::updateToGPU()
{
    if (_gpuSharead.get())
    {
        _gpuSharead->bind();
        _gpuSharead->update(0,sizeof(_context->_gpuShared),&_context->_gpuShared);
        _gpuSharead->unBind();
    }
}

bool    WDViewer::addInput(WDInput* input)
{
    auto itr = std::find(_inputs.begin(),_inputs.end(),input);
    if (itr != _inputs.end())
        return  false;
    _inputs.push_back(input);
    return  true;
}

bool    WDViewer::removeInput(WDInput* input)
{
    auto itr = std::find(_inputs.begin(),_inputs.end(),input);
    if (itr == _inputs.end())
        return  false;
    _inputs.erase(itr);
    return  true;
}

void    WDViewer::clearInput()
{
    _inputs.clear();
}

WDViewer::Inputs& WDViewer::inputs()
{
    return  _inputs;
}

const   WDViewer::Inputs& WDViewer::inputs() const
{
    return  _inputs;
}

void    WDViewer::moveView(const IVec2& prevMousePos, const IVec2& currMousePos)
{
    Vec3 pt0 = _camera->unProject(Vec2(prevMousePos), 0, _viewSize);
    Vec3 pt1 = _camera->unProject(Vec2(currMousePos), 0, _viewSize);

    Vec3 off = pt0 - pt1;

    _camera->moveView(off);
    _browseTarget += off;
    _camera->update();

    this->needRepaint();
}
void    WDViewer::rotateView(const IVec2& prevMousePos, const IVec2& currMousePos)
{
    Vec2 off = Vec2(currMousePos) - Vec2(prevMousePos);

    real anglePitch = -off.y * 0.3;
    Vec3 axisPitch = _camera->rightDir();

    real angleYaw = -off.x * 0.3;
    Vec3 axisYaw = Vec3(0, 0, 1);

    _camera->rotateView(anglePitch, axisPitch);
    _camera->rotateView(angleYaw, axisYaw);
    _camera->update();

    const auto lAabb    = limitsAabb();

    real size           = lAabb.size().length() * 0.5;
    const DVec3& dir    = _camera->frontDir();
    const DVec3 eye     = lAabb.center() - size * dir;

    DPlane plane(dir, eye);
    DVec3 tEye          = plane.project(_browseTarget);
    _camera->setEye(tEye);
    _camera->update();

    this->needRepaint();
}
void    WDViewer::zoomView(int mouseDelta)
{
    real zoom = 1.0;

    if (mouseDelta > 0)
        zoom = 1.1;
    else
        zoom = 0.9;

    _camera->setZoom(_camera->zoom() * zoom);
    _camera->update();

    this->needRepaint();
}
void    WDViewer::zoomView(float persent)
{
    _camera->setZoom(_camera->zoom() * persent);
    _camera->update();

    this->needRepaint();
}

void    WDViewer::lookAt(const Vec3& target, const Vec3& up)
{
    //校验参数有效性
    const DVec3& eye = _camera->eye();
    const DVec3& vDir = target - eye;
    if (vDir.lengthSq() < NumLimits<float>::Epsilon
        || DVec3::OnTheSameLine(vDir, up))
    {
        assert(false && "无效的 LookAt 参数!");
        return;
    }
    _camera->lookAt(target, up);
    _camera->update();
    _browseTarget = target;
    this->needRepaint();
}
void    WDViewer::lookAtAabb(const Aabb3& aabb, bool bLimits)
{
    //校验参数有效性
    if (aabb.isNull())
    {
        assert(false && "无效的 AABB包围盒对象!");
        return;
    }
    // 设置范围限定包围盒
    if (bLimits)
        _limitsAabb = aabb;
    else
        _limitsAabb = std::nullopt;

    DVec3 frontDir      = _camera->frontDir();
    DVec3 upDir         = _camera->upDir();

    // 根据绘制范围包围盒重新计算相机参数
    const auto lAabb    = limitsAabb();
    CalcOrthoCameraByAabb(_camera, lAabb, _viewSize, frontDir.normalized(), upDir.normalized());
    // 定位到包围盒
    CalcOrthoCameraLookAt(_camera, lAabb, _viewSize, aabb);
    // 设置锚点
    _browseTarget = aabb.center();
    // 触发重绘
    this->needRepaint();
}
void    WDViewer::lookAtAabb(const Aabb3& aabb, LookAtDir dir, bool bLimits)
{
    //校验参数有效性
    if (aabb.isNull())
    {
        assert(false && "无效的 AABB包围盒对象!");
        return;
    }
    // 设置范围限定包围盒
    if (bLimits)
        _limitsAabb = aabb;
    else
        _limitsAabb = std::nullopt;

    DVec3 frontDir  = - _camera->frontDir();
    DVec3 upDir     = _camera->upDir();
    switch (dir)
    {
    case WD::WDViewer::LAD_Front:
        {
            frontDir = DVec3(0.0, -1.0, 0.0);
            upDir = Vec3(0.0, 0.0, 1.0);
        }
        break;
    case WD::WDViewer::LAD_Top:
        {
            frontDir = Vec3(0.0, 0.0, 1.0);
            upDir = Vec3(0.0, 1.0, 0.0);
        }
        break;
    case WD::WDViewer::LAD_Back:
        {
            frontDir = Vec3(0.0, 1.0, 0.0);
            upDir = Vec3(0.0, 0.0, 1.0);
        }
        break;
    case WD::WDViewer::LAD_Bottom:
        {
            frontDir = Vec3(0.0, 0.0, -1.0);
            upDir = Vec3(0.0, -1.0, 0.0);
        }
        break;
    case WD::WDViewer::LAD_Right:
        {
            frontDir = Vec3(1.0, 0.0, 0.0);
            upDir = Vec3(0.0, 0.0, 1.0);
        }
        break;
    case WD::WDViewer::LAD_Left:
        {
            frontDir = Vec3(-1.0, 0.0, 0.0);
            upDir = Vec3(0.0, 0.0, 1.0);
        }
        break;

    case WD::WDViewer::LAD_FrontUp:
        {
            frontDir = Vec3(0.0, -1.0, 1.0);
            upDir = Vec3(0.0, 0.0, 1.0);
        }
        break;
    case WD::WDViewer::LAD_FrontDown:
        {
            frontDir = Vec3(0.0, -1.0, -1.0);
            upDir = Vec3(0.0, 0.0, 1.0);
        }
        break;
    case WD::WDViewer::LAD_FrontLeft:
        {
            frontDir = Vec3(-1.0, -1.0, 0.0);
            upDir = Vec3(0.0, 0.0, 1.0);
        }
        break;
    case WD::WDViewer::LAD_FrontRignt:
        {
            frontDir = Vec3(1.0, -1.0, 0.0);
            upDir = Vec3(0.0, 0.0, 1.0);
        }
        break;

    case WD::WDViewer::LAD_BackUp:
        {
            frontDir = Vec3(0.0, 1.0, 1.0);
            upDir = Vec3(0.0, 0.0, 1.0);
        }
        break;
    case WD::WDViewer::LAD_BackDown:
        {
            frontDir = Vec3(0.0, 1.0, -1.0);
            upDir = Vec3(0.0, 0.0, 1.0);
        }
        break;
    case WD::WDViewer::LAD_BackLeft:
        {
            frontDir = Vec3(1.0, 1.0, 0.0);
            upDir = Vec3(0.0, 0.0, 1.0);
        }
        break;
    case WD::WDViewer::LAD_BackRight:
        {
            frontDir = Vec3(-1.0, 1.0, 0.0);
            upDir = Vec3(0.0, 0.0, 1.0);
        }
        break;

    case WD::WDViewer::LAD_LeftUp:
        {
            frontDir = Vec3(-1.0, 0.0, 1.0);
            upDir = Vec3(0.0, 0.0, 1.0);
        }
        break;
    case WD::WDViewer::LAD_LeftDown:
        {
            frontDir = Vec3(-1.0, 0.0, -1.0);
            upDir = Vec3(0.0, 0.0, 1.0);
        }
        break;
    case WD::WDViewer::LAD_LeftLeft:
        {
            frontDir = Vec3(-1.0, 0.0, 1.0);
            upDir = Vec3(0.0, 0.0, 1.0);
        }
        break;
    case WD::WDViewer::LAD_LeftRight:
        {
            frontDir = Vec3(-1.0, 0.0, 1.0);
            upDir = Vec3(0.0, 0.0, 1.0);
        }
        break;

    case WD::WDViewer::LAD_RightUp:
        {
            frontDir = Vec3(1.0, 0.0, 1.0);
            upDir = Vec3(0.0, 0.0, 1.0);
        }
        break;
    case WD::WDViewer::LAD_RightDown:
        {
            frontDir = Vec3(1.0, 0.0, -1.0);
            upDir = Vec3(0.0, 0.0, 1.0);
        }
        break;
    case WD::WDViewer::LAD_RightLeft:
        {
            frontDir = Vec3(1.0, 0.0, 1.0);
            upDir = Vec3(0.0, 0.0, 1.0);
        }
        break;
    case WD::WDViewer::LAD_RightRight:
        {
            frontDir = Vec3(1.0, 0.0, 1.0);
            upDir = Vec3(0.0, 0.0, 1.0);
        }
        break;

    case WD::WDViewer::LAD_FrontUpLeft:
        {
            frontDir = Vec3(-1.0, -1.0, 1.0);
            upDir = Vec3(0.0, 0.0, 1.0);
        }
        break;
    case WD::WDViewer::LAD_FrontUPRight:
        {
            frontDir = Vec3(1.0, -1.0, 1.0);
            upDir = Vec3(0.0, 0.0, 1.0);
        }
        break;
    case WD::WDViewer::LAD_FrontDownLeft:
        {
            frontDir = Vec3(-1.0, -1.0, -1.0);
            upDir = Vec3(0.0, 0.0, 1.0);
        }
        break;
    case WD::WDViewer::LAD_FrontDownRight:
        {
            frontDir = Vec3(1.0, -1.0, -1.0);
            upDir = Vec3(0.0, 0.0, 1.0);
        }
        break;

    case WD::WDViewer::LAD_BackUpLeft:
        {
            frontDir = Vec3(1.0, 1.0, 1.0);
            upDir = Vec3(0.0, 0.0, 1.0);
        }
        break;
    case WD::WDViewer::LAD_BackUPRight:
        {
            frontDir = Vec3(-1.0, 1.0, 1.0);
            upDir = Vec3(0.0, 0.0, 1.0);
        }
        break;
    case WD::WDViewer::LAD_BackDownLeft:
        {
            frontDir = Vec3(1.0, 1.0, -1.0);
            upDir = Vec3(0.0, 0.0, 1.0);
        }
        break;
    case WD::WDViewer::LAD_BackDownRight:
        {
            frontDir = Vec3(-1.0, 1.0, -1.0);
            upDir = Vec3(0.0, 0.0, 1.0);
        }
        break;

    case WD::WDViewer::LAD_North:
        {
            frontDir = Vec3(0.0, 0.0, 1.0);
            upDir = Vec3(0.0, 1.0, 0.0);
        }
        break;
    case WD::WDViewer::LAD_South:
        {
            frontDir = Vec3(0.0, 0.0, 1.0);
            upDir = Vec3(0.0, -1.0, 0.0);
        }
        break;
    case WD::WDViewer::LAD_East:
        {
            frontDir = Vec3(0.0, 0.0, 1.0);
            upDir = Vec3(1.0, 0.0, 0.0);
        }
        break;
    case WD::WDViewer::LAD_West:
        {
            frontDir = Vec3(0.0, 0.0, 1.0);
            upDir = Vec3(-1.0, 0.0, 0.0);
        }
        break;
    case WD::WDViewer::LAD_ISO1:
        {
            DDirectionParser::Direction("-Y 45 -X 35 -Z", frontDir);
            frontDir = -frontDir;
            upDir = WD::DVec3(0.0, 0.0, 1.0);
            auto tRight = DVec3::Cross(frontDir, upDir);
            upDir = DVec3::Cross(tRight, frontDir).normalized();
        }
        break;
    case WD::WDViewer::LAD_ISO2:
        {
            DDirectionParser::Direction("-X 45 Y 35 -Z", frontDir);
            frontDir = -frontDir;
            upDir = WD::DVec3(0.0, 0.0, 1.0);
            auto tRight = DVec3::Cross(frontDir, upDir);
            upDir = DVec3::Cross(tRight, frontDir).normalized();
        }
        break;
    case WD::WDViewer::LAD_ISO3:
        {
            DDirectionParser::Direction("X 45 Y 35 -Z", frontDir);
            frontDir = -frontDir;
            upDir = WD::DVec3(0.0, 0.0, 1.0);
            auto tRight = DVec3::Cross(frontDir, upDir);
            upDir = DVec3::Cross(tRight, frontDir).normalized();
        }
        break;
    case WD::WDViewer::LAD_ISO4:
        {
            DDirectionParser::Direction("-Y 45 X 35 -Z", frontDir);
            frontDir = -frontDir;
            upDir = WD::DVec3(0.0, 0.0, 1.0);
            auto tRight = DVec3::Cross(frontDir, upDir);
            upDir = DVec3::Cross(tRight, frontDir).normalized();
        }
        break;
    default:
        break;
    }

    // 根据绘制范围包围盒重新计算相机参数
    const auto lAabb = limitsAabb();
    CalcOrthoCameraByAabb(_camera, lAabb, _viewSize, -frontDir.normalized(), upDir.normalized());
    // 定位到包围盒
    CalcOrthoCameraLookAt(_camera, lAabb, _viewSize, aabb);
    // 设置锚点
    _browseTarget = aabb.center();

    this->needRepaint();
}
bool    WDViewer::hasLimitsAabb() const
{
    return _limitsAabb.has_value();
}

WDImage WDViewer::getSnapShotImage(int x, int y, int w, int h, int bufferId)
{
    WDImage rImage;
    if (scene() == nullptr)
        return rImage;

    //切换上下文
    makeCurrent()(this);

    if (_mainFBO == nullptr)
    {
        if (bufferId == -1)
        {
            //获取当前默认的read缓冲区
            glGetIntegerv(GL_READ_FRAMEBUFFER_BINDING, &bufferId);
        }
        _mainFBO = WDFrameBufferObject::MakeShared(bufferId);
    }
    //从缓存中读取窗口显示数据
    auto    vSize = size();
    auto    ssFBO = snapshotFBO();
    ssFBO->resize(vSize[0], vSize[1], 0);

    _mainFBO->begin(WDFrameBufferObject::For_Read);
    ssFBO->begin(WDFrameBufferObject::For_Draw);

    // 将read帧缓冲区的数据（像素块）复制到绘制draw帧缓冲区
    _mainFBO->copyTo(IVec2(0, 0), vSize, IVec2(0, 0), vSize, WDFrameBufferObject::C_COLOR, WDFrameBufferObject::Linear);

    // 读取缓冲取内容到图片对象
    ssFBO->readToImage(rImage, x, y, w, h, 0);

    _mainFBO->end();
    ssFBO->end();

    return rImage;
}
WDImage WDViewer::getSnapShotImage(const DAabb3& aabb, const DVec3& frontDir, const DVec3& upDir, bool recover, int bufferId)
{
    if (_camera == nullptr)
        return WDImage();

    auto pOrthoCamera = WDOrthographicCamera::SharedCast(_camera);
    if (pOrthoCamera == nullptr)
        return WDImage();

    // 保存截图之前的状态
    auto left   = pOrthoCamera->left();
    auto right  = pOrthoCamera->right();
    auto top    = pOrthoCamera->top();
    auto bottom = pOrthoCamera->bottom();
    auto zNear  = pOrthoCamera->zNear();
    auto zFar   = pOrthoCamera->zFar();
    auto zoom   = pOrthoCamera->zoom();
    auto eye    = pOrthoCamera->eye();
    auto up     = pOrthoCamera->upDir();

    auto target = _browseTarget;

    bool valid = !aabb.isNull() && frontDir.lengthSq() >= NumLimits<float>::Epsilon && upDir.lengthSq() >= NumLimits<float>::Epsilon;

    int x = 0;
    int y = 0;
    int w = this->size().x;
    int h = this->size().y;
    // 如果定位参数有效，则先定位相机
    if (valid)
    {
        // 调整相机看向aabb
        const auto lAabb = limitsAabb();
        CalcOrthoCameraLookAt(_camera, lAabb, _viewSize, aabb, frontDir, upDir);
        _browseTarget = aabb.center();
        this->repaint();

        DVec3 corners[8];
        aabb.corners(corners);
        DAabb2 aabbScreen = DAabb2::Null();
        for (size_t i = 0; i < 8; ++i)
        {
            auto pt = _camera->worldToScreen(corners[i]);
            aabbScreen.expandByPoint(pt);
        }
        x = static_cast<int>(aabbScreen.min.x);
        y = static_cast<int>(aabbScreen.min.y);
        auto sz = aabbScreen.size();
        w = static_cast<int>(sz.x);
        h = static_cast<int>(sz.y);
    }

    auto rImage = this->getSnapShotImage(x, y, w, h, bufferId);

    if (valid && recover)
    {
        // 恢复
        _browseTarget = target;

        pOrthoCamera->setLeft(left);
        pOrthoCamera->setRight(right);
        pOrthoCamera->setTop(top);
        pOrthoCamera->setBottom(bottom);
        pOrthoCamera->setZNear(zNear);
        pOrthoCamera->setZFar(zFar);
        pOrthoCamera->setZoom(zoom);

        pOrthoCamera->lookAt(eye, target, up);
        pOrthoCamera->update();

        this->repaint(); 
    }

    return rImage;
}

void    WDViewer::dispachEvent(WDEvent* e)
{
    if (_pScene == nullptr)
    {
        return;
    }
    for (auto& var : _inputs)
    {
        if (var == nullptr)
            continue;
        if (!var->onEvent(e))
            break;
    }
}

void    WDViewer::resizeEvent(WDResizeEvent* e)
{
    _device.setViewPort(0, 0, e->size().x, e->size().y);
    _camera->setViewSize(_viewSize);
    real aspect = real(_viewSize.y) / real(_viewSize.x);

    const auto lAabb = limitsAabb();
    real sceneHalfSize = lAabb.size().length() * 0.5;
    auto pOrth = WDOrthographicCamera::SharedCast(_camera);
    if (pOrth != nullptr)
    {
        pOrth->setLeft(-sceneHalfSize);
        pOrth->setRight(sceneHalfSize);
        pOrth->setTop(sceneHalfSize * aspect);
        pOrth->setBottom(-sceneHalfSize * aspect);
    }
    _camera->update();
    this->needRepaint();
}

void    WDViewer::updateEvent(WDUpdateEvent* e)
{
    WDUnused(e);

    auto startTm = std::chrono::high_resolution_clock::now();

    // 开始更新和绘制
    this->glContext().makeCurrent();

    // 更新context对象
    _context->update();

    // 更新场景包围盒
    if (_pScene != nullptr)
    {
        // 记录更新之前的aabb
        const auto prevAabb = GetSceneAabb(_pScene);
        _pScene->updateAabb(*_context);
        const auto& currAabb = GetSceneAabb(_pScene);
        // 如果包围盒发生了改变，则重新计算相机参数
        if (!prevAabb.isNull() && !currAabb.isNull() && prevAabb != currAabb)
        {
            // 使用新的包围盒数据，重新更新相机
            this->onSceneAabbChanged(currAabb);
            // 更新context对象
            _context->update();
        }
    }

    // 编辑轴管理工具更新
    _objectAxisEditor.update(*_context);
    // 捕捉工具更新
    _capturePositioning.update(*_context);
    // 浏览轴更新
    _browseAxisMgr.update(*_context);
    // 单移动轴更新
    _singleMoveAxisMgr.update(*_context);

    // 视图盒子更新
    _viewerCube.update(*_context);
    // 视图场景轴更新
    _viewerSceneAxis.update(*_context);


    // 再次更新场景,用最新的context数据再次更新场景
    if (_pScene != nullptr)
        _pScene->update(*_context);

    // 计算一帧更新使用的时间
    auto endTm = std::chrono::high_resolution_clock::now();
    auto durationTm = std::chrono::duration_cast<std::chrono::milliseconds>(endTm - startTm);
    _context->_updateFrameTime = durationTm.count();
}

void    WDViewer::paintEvent(WDPaintEvent* e)
{
    auto startTm = std::chrono::high_resolution_clock::now();

    WDUnused(e);
    WDOpenGL&   device  =   this->device();

    int _preId;
    glGetIntegerv(GL_FRAMEBUFFER_BINDING,       (int*)&_preId);

    device.enableRenderState(GL_BLEND);
    device.blendFunction(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);

    device.enableRenderState(GL_LINE_SMOOTH);
    device.enableRenderState(GL_MULTISAMPLE);

    device.enableRenderState(GL_STENCIL_TEST);
    device.enableRenderState(GL_DEPTH_TEST);
    device.enableRenderState(GL_CULL_FACE);
    device.stencilMask(0xFF);

    device.clearColor(_backColor.rF(), _backColor.gF(), _backColor.bF(), _backColor.aF());
    device.clear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT | GL_STENCIL_BUFFER_BIT);
    device.setViewPort(0,0,_context->_width,_context->_height);

    /// 同步数据到显卡
    updateToGPU();

    if (_gpuSharead.get())
        _gpuSharead->bind();

    _context->_renderLayer  =   WDRenderLayer::RL_Background;
    _context->_renderLayer  |=  WDRenderLayer::RL_Scene;

    if (_pScene.get() != nullptr)
    {
        // 多边形绘制模式: 填充，线框还是点
        switch (_polygonMode)
        {
        case WD::WDViewer::PM_Fill:
            device.setPolygonMode(DrawMode::DM_FILL);
            break;
        case WD::WDViewer::PM_Line:
            device.setPolygonMode(DrawMode::DM_LINE);
            break;
        case WD::WDViewer::PM_Point:
            device.setPolygonMode(DrawMode::DM_POINT);
            break;
        default:
            break;
        }
        // 正反面模式
        switch (_drawFaceMode)
        {
        case WD::WDViewer::DFM_FrontSide:
            {
                glFrontFace(GL_CCW);
                device.enableRenderState(GL_CULL_FACE);
            }
            break;
        case WD::WDViewer::DFM_BackSide:
            {
                glFrontFace(GL_CW);
                device.enableRenderState(GL_CULL_FACE);
            }
            break;
        case WD::WDViewer::DFM_DoubleSide:
            {
                device.disableRenderState(GL_CULL_FACE);
            }
            break;
        default:
            break;
        }
        
        _pScene->render(*_context);

        // 还原状态
        device.setPolygonMode(DrawMode::DM_FILL);

        glFrontFace(GL_CCW);
        device.enableRenderState(GL_CULL_FACE);

    }


    // 编辑轴管理工具绘制
    _objectAxisEditor.render(*_context);
    // 捕捉工具绘制
    _capturePositioning.render(*_context); 
    // 浏览轴绘制
    _browseAxisMgr.render(*_context);
    // 单移动轴绘制
    _singleMoveAxisMgr.render(*_context);

    _layerOverlay->begin();

    {
        device.clearColor(0,0,0,0);
        device.setViewPort(0,0,_context->_width,_context->_height);
        _layerOverlay->clearBuffer(WDFrameBufferObject::Buffers(WDFrameBufferObject::C_COLOR | WDFrameBufferObject::C_DEPTH));

        _context->_renderLayer = WDRenderLayer::RL_Overlay;
        // 视图盒子绘制
        _viewerCube.render(*_context);
        // 视图场景轴绘制
        _viewerSceneAxis.render(*_context);

        // 场景绘制
        if (_pScene.get() != nullptr)
        {
            _pScene->render(*_context);
        }
    }

    _layerOverlay->end();
    
    WDTexture::SharedPtr  tex =   _layerOverlay->colorBuffer(0)->_obj->toPtr<WDTexture>();
    IVec2   pos(0,0);
    IVec2   size(_context->_width,_context->_height);
    device.enableRenderState(GL_BLEND);
    device.blendFunction(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);
    drawTexture(*_context,tex.get(),pos,size);

    this->glContext().swapBuffer();

    //这里校验一下状态栈，应该是空
    assert(_context->_rStateStack.empty() && "一帧渲染完成之后状态栈不为空，请检查状态push和pop的次数是否对应!");
    //清空状态栈
    while (!_context->_rStateStack.empty())
    {
        _context->_rStateStack.pop();
    }

    // 计算一帧绘制使用的时间
    auto endTm = std::chrono::high_resolution_clock::now();
    auto durationTm = std::chrono::duration_cast<std::chrono::milliseconds>(endTm - startTm);
    _context->_renderFrameTime = durationTm.count();

    // 一帧使用的总时间=更新使用的时间+绘制使用的时间
    _context->_frameTime = _context->_updateFrameTime + _context->_renderFrameTime;
}

void    WDViewer::drawTexture(WDContext& ctx,WDTexture* tex,const IVec2& pos,const IVec2& size)
{
    struct  V3N2
    {
        float   x,y,z;
        float   u,v;
    };
    bool            bMulti  =   false;
    PROGRAM_P3_UV2* prg     =   nullptr;
    
    if (dynamic_cast<WDTexture2dMS*>(tex))
    {
        /// 对于多采样纹理
        /// shader将执行每采样执行，对应shader中使用gl_SampleID区分
        /// 首先启用，然后设置采样率如果是1.0则全部执行，如果是0.5则是执行一半
        ctx._device.enableRenderState(GL_SAMPLE_SHADING);
        ctx._device.minSampleShading(1.0f);
        bMulti  =   true;
        prg     =   &ctx._resource._PROGRAM_P3_UV2MS;
    }
    else
    {
        prg     =   &ctx._resource._PROGRAM_P3_UV2;
    }
    V3N2    data[4] =
    {
        { (float)(pos.x),           (float)(pos.y),             0.0f, 0.0f, 0.0f},
        { (float)(pos.x + size.x),  (float)(pos.y),             0.0f, 1.0f, 0.0f},
        { (float)(pos.x + size.x),  (float)(pos.y + size.y),    0.0f, 1.0f, 1.0f},
        { (float)(pos.x),           (float)(pos.y + size.y),    0.0f, 0.0f, 1.0f},
    };
    ctx._device.bindVertexBuffer(0);
    ctx._device.bindIndexBuffer(0);

    
    WDInstance  inst;
    inst._color         =   Color(255,255,255,255);
    inst._instanceAttr  =   0;
    inst._instanceId    =   0;
    inst._local         =   FMat4();
    char*   pData       =   (char*)(&inst);
    
    tex->bind();
    ctx._device.disableRenderState(GL_CULL_FACE); 
    ctx._device.disableRenderState(GL_DEPTH_TEST); 
    ctx._device.enableRenderState(GL_BLEND); 
    ctx._device.blendFunction(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);

    prg->begin();
    {
        
        prg->setUniform1i(prg->_2d, 1);
        prg->setUniform1i(prg->_texture,0);
        prg->attributePointer(prg->_position,     3, GL_FLOAT,        GL_FALSE,   sizeof(data[0]), &data[0].x);
        prg->attributePointer(prg->_uv,           2, GL_FLOAT,        GL_FALSE,   sizeof(data[0]), &data[0].u);

        prg->attributePointer(prg->_local + 0,    4, GL_FLOAT,        GL_FALSE,   sizeof(WDInstance), pData + WD_OFFSET_STRUCT(WDInstance,_local));
        prg->attributePointer(prg->_local + 1,    4, GL_FLOAT,        GL_FALSE,   sizeof(WDInstance), pData + WD_OFFSET_STRUCT(WDInstance,_local) + 4 * 4 );
        prg->attributePointer(prg->_local + 2,    4, GL_FLOAT,        GL_FALSE,   sizeof(WDInstance), pData + WD_OFFSET_STRUCT(WDInstance,_local) + 8 * 4 );
        prg->attributePointer(prg->_local + 3,    4, GL_FLOAT,        GL_FALSE,   sizeof(WDInstance), pData + WD_OFFSET_STRUCT(WDInstance,_local) + 12 * 4 );
        prg->attributePointer(prg->_color,        4, GL_UNSIGNED_BYTE,GL_TRUE,    sizeof(WDInstance), pData + WD_OFFSET_STRUCT(WDInstance,_color));

       
        prg->drawArraysInstanced(GL_QUADS, 0, 4,1);
    }
    prg->end(); 
    tex->unbind();
    /// 绘制完成后恢复采样状态
    if (bMulti)
    {
        ctx._device.disableRenderState(GL_SAMPLE_SHADING);
    }
}
void    WDViewer::onSceneAabbChanged(const DAabb3& currAabb)
{
    WDUnused(currAabb);
    auto pOrtho = WDOrthographicCamera::SharedCast(_camera);
    if (pOrtho == nullptr)
        return;
    const DVec3& oldEye = pOrtho->eye();
    real oldZoom        = pOrtho->zoom();
    real oldVSize       = (pOrtho->right() - pOrtho->left());

    const auto lAabb = limitsAabb();
    CalcOrthoCameraByAabb(_camera, lAabb, _viewSize,  _camera->frontDir(), _camera->upDir());

    // 计算zoom，以保证视口大小不变
    real currVSize      = (pOrtho->right() - pOrtho->left());
    real vOff           = currVSize / oldVSize;
    pOrtho->setZoom(oldZoom * vOff);
    // 需要重新计算眼睛位置, 以保证看向场景的位置与之前的位置在同一直线上
    DPlane plane(pOrtho->frontDir(), pOrtho->eye());
    DVec3 eye           = plane.project(oldEye);
    pOrtho->setEye(eye);
    pOrtho->update();

}
DAabb3  WDViewer::limitsAabb() const
{
    // 如果设置了范围限定包围盒，直接返回
    if (_limitsAabb)
        return _limitsAabb.value();
    // 否则从场景中获取包围盒
    return GetSceneAabb(_pScene);
}
WD_NAMESPACE_END
