#include    "./boolean3.h"
#include    "./parallel.h"
#include    "manifold/polygon.h"
#include    "manifold/manifold.h"
#include    "./CsgNode.h"
#include    "./impl.h"
#include    "./parallel.h"
#include    "./collider.h"
#include    "./hashtable.h"

#include    <atomic>
#include    <set>
#include    <functional>
#include    <map>
#include    <set>
#include    <unordered_set>
#include    <limits>

using namespace manifold;

namespace manifold
{

    // These two functions (Interpolate and Intersect) are the only places where
    // floating-point operations take place in the whole Boolean function. These
    // are carefully designed to minimize rounding error and to eliminate it at edge
    // cases to ensure consistency.

    vec2 Interpolate(vec3 pL, vec3 pR, double x)
    {
        const double  dxL = x - pL.x;
        const double  dxR = x - pR.x;
        DEBUG_ASSERT(dxL * dxR <= 0, logicErr, "Boolean manifold error: not in domain");
        const bool    useL = fabs(dxL) < fabs(dxR);
        const vec3    dLR = pR - pL;
        const double lambda = (useL ? dxL : dxR) / dLR.x;
        if (!std::isfinite(lambda) || !std::isfinite(dLR.y) || !std::isfinite(dLR.z))
            return vec2(pL.y, pL.z);
        vec2 yz;
        yz[0] = fma(lambda, dLR.y, useL ? pL.y : pR.y);
        yz[1] = fma(lambda, dLR.z, useL ? pL.z : pR.z);
        return yz;
    }

    vec4 Intersect(const vec3& pL, const vec3& pR, const vec3& qL, const vec3& qR)
    {
        const double dyL = qL.y - pL.y;
        const double dyR = qR.y - pR.y;
        DEBUG_ASSERT(dyL * dyR <= 0, logicErr, "Boolean manifold error: no intersection");
        const bool useL = fabs(dyL) < fabs(dyR);
        const double dx = pR.x - pL.x;
        double lambda = (useL ? dyL : dyR) / (dyL - dyR);
        if (!std::isfinite(lambda))
            lambda = 0.0;
        vec4 xyzz;
        xyzz.x = fma(lambda, dx, useL ? pL.x : pR.x);
        const double pDy = pR.y - pL.y;
        const double qDy = qR.y - qL.y;
        const bool useP = fabs(pDy) < fabs(qDy);
        xyzz.y = fma(lambda, useP ? pDy : qDy,
            useL ? (useP ? pL.y : qL.y) : (useP ? pR.y : qR.y));
        xyzz.z = fma(lambda, pR.z - pL.z, useL ? pL.z : pR.z);
        xyzz.w = fma(lambda, qR.z - qL.z, useL ? qL.z : qR.z);
        return xyzz;
    }

    template <const bool inverted>
    struct CopyFaceEdges
    {
        const SparseIndices& _p1q1;
        // x can be either vert or edge (0 or 1).
        SparseIndices& _pXq1;
        TArrayView<const Halfedge>_halfedgesQ;
        const size_t              _offset;

        void operator()(const size_t i)
        {
            int idx = int(3 * (i + _offset));
            int pX = _p1q1.get(i, inverted);
            int q2 = _p1q1.get(i, !inverted);

            for (const int j : {0, 1, 2})
            {
                const int         q1 = 3 * q2 + j;
                const Halfedge    edge = _halfedgesQ[q1];
                int               a = pX;
                int               b = edge.isForward() ? q1 : edge._pairedHalfedge;
                if (inverted)
                    std::swap(a, b);
                _pXq1.set(idx + static_cast<size_t>(j), a, b);
            }
        }
    };

    SparseIndices Filter11(const Manifold::Impl& inP
        , const Manifold::Impl& inQ
        , const SparseIndices& p1q2
        , const SparseIndices& p2q1)
    {
        ZoneScoped;
        SparseIndices p1q1(3 * p1q2.size() + 3 * p2q1.size());
        for_each_n(autoPolicy(p1q2.size(), size_t(1e5)), countAt(0_uz), p1q2.size(),
            CopyFaceEdges<false>({ p1q2, p1q1, inQ._halfedge, 0_uz }));
        for_each_n(autoPolicy(p2q1.size(), (size_t)1e5), countAt(0_uz), p2q1.size(),
            CopyFaceEdges<true>({ p2q1, p1q1, inP._halfedge, p1q2.size() }));
        p1q1.uniques();
        return p1q1;
    }

    inline bool Shadows(double p, double q, double dir)
    {
        return p == q ? dir < 0 : p < q;
    }

    inline std::pair<int, vec2> Shadow01(const int p0
        , const int q1
        , TArrayView<const vec3>    vertPosP
        , TArrayView<const vec3>    vertPosQ
        , TArrayView<const Halfedge>halfedgeQ
        , const double expandP
        , TArrayView<const vec3> normalP
        , const bool reverse)
    {
        const int     q1s = halfedgeQ[q1]._startVert;
        const int     q1e = halfedgeQ[q1]._endVert;
        const double  p0x = vertPosP[p0].x;
        const double  q1sx = vertPosQ[q1s].x;
        const double  q1ex = vertPosQ[q1e].x;
        int s01 = reverse ? Shadows(q1sx, p0x, expandP * normalP[q1s].x) - Shadows(q1ex, p0x, expandP * normalP[q1e].x)
            : Shadows(p0x, q1ex, expandP * normalP[p0].x) - Shadows(p0x, q1sx, expandP * normalP[p0].x);
        vec2 yz01(NAN);

        if (s01 != 0)
        {
            yz01 = Interpolate(vertPosQ[q1s], vertPosQ[q1e], vertPosP[p0].x);
            if (reverse)
            {
                vec3 diff = vertPosQ[q1s] - vertPosP[p0];
                const double start2 = la::dot(diff, diff);
                diff = vertPosQ[q1e] - vertPosP[p0];
                const double end2 = la::dot(diff, diff);
                const double dir = start2 < end2 ? normalP[q1s].y : normalP[q1e].y;
                if (!Shadows(yz01[0], vertPosP[p0].y, expandP * dir)) s01 = 0;
            }
            else
            {
                if (!Shadows(vertPosP[p0].y, yz01[0], expandP * normalP[p0].y)) s01 = 0;
            }
        }
        return std::make_pair(s01, yz01);
    }

    // https://github.com/scandum/binary_search/blob/master/README.md
    // much faster than standard binary search on large arrays
    size_t monobound_quaternary_search(TArrayView<const int64_t> array, int64_t key)
    {
        if (array.size() == 0)
        {
            return std::numeric_limits<size_t>::max();
        }
        size_t bot = 0;
        size_t top = array.size();
        while (top >= 65536)
        {
            size_t mid = top / 4;
            top -= mid * 3;
            if (key < array[bot + mid * 2])
            {
                if (key >= array[bot + mid])
                {
                    bot += mid;
                }
            }
            else
            {
                bot += mid * 2;
                if (key >= array[bot + mid])
                {
                    bot += mid;
                }
            }
        }

        while (top > 3)
        {
            size_t mid = top / 2;
            if (key >= array[bot + mid])
            {
                bot += mid;
            }
            top -= mid;
        }

        while (top--)
        {
            if (key == array[bot + top])
            {
                return bot + top;
            }
        }
        return size_t(-1);
    }

    struct Kernel11
    {
        TArrayView<vec4>              _xyzz;
        TArrayView<int>               _s;
        TArrayView<const vec3>        _vertPosP;
        TArrayView<const vec3>        _vertPosQ;
        TArrayView<const Halfedge>    _halfedgeP;
        TArrayView<const Halfedge>    _halfedgeQ;
        const double                  _expandP;
        TArrayView<const vec3>        _normalP;
        const SparseIndices& _p1q1;

        void operator()(const size_t idx)
        {
            const int p1 = _p1q1.get(idx, false);
            const int q1 = _p1q1.get(idx, true);
            vec4& xyzz11 = _xyzz[idx];
            int& s11 = _s[idx];

            // For pRL[k], qRL[k], k==0 is the left and k==1 is the right.
            int k = 0;
            vec3 pRL[2], qRL[2];
            // Either the left or right must shadow, but not both. This ensures the
            // intersection is between the left and right.
            bool shadows = false;
            s11 = 0;

            const int p0[2] = { _halfedgeP[p1]._startVert, _halfedgeP[p1]._endVert };
            for (int i : {0, 1})
            {
                const auto    syz01 = Shadow01(p0[i], q1, _vertPosP, _vertPosQ, _halfedgeQ, _expandP, _normalP, false);
                const int     s01 = syz01.first;
                const vec2    yz01 = syz01.second;
                // If the value is NaN, then these do not overlap.
                if (std::isfinite(yz01[0]))
                {
                    s11 += s01 * (i == 0 ? -1 : 1);
                    if (k < 2 && (k == 0 || (s01 != 0) != shadows))
                    {
                        shadows = s01 != 0;
                        pRL[k] = _vertPosP[p0[i]];
                        qRL[k] = vec3(pRL[k].x, yz01.x, yz01.y);
                        ++k;
                    }
                }
            }

            const int q0[2] = { _halfedgeQ[q1]._startVert, _halfedgeQ[q1]._endVert };
            for (int i : {0, 1})
            {
                const auto syz10 = Shadow01(q0[i], p1, _vertPosQ, _vertPosP, _halfedgeP, _expandP, _normalP, true);
                const int s10 = syz10.first;
                const vec2 yz10 = syz10.second;
                // If the value is NaN, then these do not overlap.
                if (std::isfinite(yz10[0]))
                {
                    s11 += s10 * (i == 0 ? -1 : 1);
                    if (k < 2 && (k == 0 || (s10 != 0) != shadows))
                    {
                        shadows = s10 != 0;
                        qRL[k] = _vertPosQ[q0[i]];
                        pRL[k] = vec3(qRL[k].x, yz10.x, yz10.y);
                        ++k;
                    }
                }
            }
            // No intersection
            if (s11 == 0)
            {
                xyzz11 = vec4(NAN);
            }
            else
            {
                DEBUG_ASSERT(k == 2, logicErr, "Boolean manifold error: s11");
                xyzz11 = Intersect(pRL[0], pRL[1], qRL[0], qRL[1]);

                const int p1s = _halfedgeP[p1]._startVert;
                const int p1e = _halfedgeP[p1]._endVert;
                vec3      diff = _vertPosP[p1s] - vec3(xyzz11);
                const double start2 = la::dot(diff, diff);
                diff = _vertPosP[p1e] - vec3(xyzz11);
                const double end2 = la::dot(diff, diff);
                const double dir = start2 < end2 ? _normalP[p1s].z : _normalP[p1e].z;

                if (!Shadows(xyzz11.z, xyzz11.w, _expandP * dir)) s11 = 0;
            }
        }
    };

    std::tuple<TArray<int>, TArray<vec4>> Shadow11(SparseIndices& p1q1,
        const Manifold::Impl& inP,
        const Manifold::Impl& inQ,
        double expandP)
    {
        ZoneScoped;
        TArray<int> s11(p1q1.size());
        TArray<vec4> xyzz11(p1q1.size());

        for_each_n(autoPolicy(p1q1.size(), (size_t)1e4), countAt(0_uz), p1q1.size(),
            Kernel11({ xyzz11, s11, inP._vertPos, inQ._vertPos, inP._halfedge,
                      inQ._halfedge, expandP, inP._vertNormal, p1q1 }));

        p1q1.KeepFinite(xyzz11, s11);

        return std::make_tuple(s11, xyzz11);
    };

    struct Kernel02
    {
        TArrayView<int>               _s;
        TArrayView<double>            _z;
        TArrayView<const vec3>        _vertPosP;
        TArrayView<const Halfedge>    _halfedgeQ;
        TArrayView<const vec3>        _vertPosQ;
        const double                  _expandP;
        TArrayView<const vec3>        _vertNormalP;
        const SparseIndices& _p0q2;
        const bool                    _forward;

        void operator()(const size_t idx) {
            const int   p0 = _p0q2.get(idx, !_forward);
            const int   q2 = _p0q2.get(idx, _forward);
            int& s02 = _s[idx];
            double& z02 = _z[idx];

            // For yzzLR[k], k==0 is the left and k==1 is the right.
            int k = 0;
            vec3 yzzRL[2];
            // Either the left or right must shadow, but not both. This ensures the
            // intersection is between the left and right.
            bool shadows = false;
            int closestVert = -1;
            double minMetric = std::numeric_limits<double>::infinity();
            s02 = 0;

            const vec3 posP = _vertPosP[p0];
            for (const int i : {0, 1, 2})
            {
                const int q1 = 3 * q2 + i;
                const Halfedge edge = _halfedgeQ[q1];
                const int q1F = edge.isForward() ? q1 : edge._pairedHalfedge;

                if (!_forward)
                {
                    const int   qVert = _halfedgeQ[q1F]._startVert;
                    const vec3  diff = posP - _vertPosQ[qVert];
                    const double metric = la::dot(diff, diff);
                    if (metric < minMetric)
                    {
                        minMetric = metric;
                        closestVert = qVert;
                    }
                }

                const auto syz01 = Shadow01(p0, q1F, _vertPosP, _vertPosQ, _halfedgeQ, _expandP, _vertNormalP, !_forward);
                const int s01 = syz01.first;
                const vec2 yz01 = syz01.second;
                // If the value is NaN, then these do not overlap.
                if (std::isfinite(yz01[0])) {
                    s02 += s01 * (_forward == edge.isForward() ? -1 : 1);
                    if (k < 2 && (k == 0 || (s01 != 0) != shadows))
                    {
                        shadows = s01 != 0;
                        yzzRL[k++] = vec3(yz01[0], yz01[1], yz01[1]);
                    }
                }
            }

            if (s02 == 0)
            {  // No intersection
                z02 = NAN;
            }
            else
            {
                DEBUG_ASSERT(k == 2, logicErr, "Boolean manifold error: s02");
                vec3 vertPos = _vertPosP[p0];
                z02 = Interpolate(yzzRL[0], yzzRL[1], vertPos.y)[1];
                if (_forward)
                {
                    if (!Shadows(vertPos.z, z02, _expandP * _vertNormalP[p0].z)) s02 = 0;
                }
                else
                {
                    // DEBUG_ASSERT(closestVert != -1, topologyErr, "No closest vert");
                    if (!Shadows(z02, vertPos.z, _expandP * _vertNormalP[closestVert].z))
                        s02 = 0;
                }
            }
        }
    };

    std::tuple<TArray<int>, TArray<double>> Shadow02(const Manifold::Impl& inP,
        const Manifold::Impl& inQ,
        SparseIndices& p0q2, bool forward,
        double expandP) {
        ZoneScoped;
        TArray<int> s02(p0q2.size());
        TArray<double> z02(p0q2.size());

        auto vertNormalP = forward ? inP._vertNormal : inQ._vertNormal;
        for_each_n(autoPolicy(p0q2.size(), (size_t)1e4), countAt(0_uz), p0q2.size(),
            Kernel02({ s02, z02, inP._vertPos, inQ._halfedge, inQ._vertPos,
                      expandP, vertNormalP, p0q2, forward }));

        p0q2.KeepFinite(z02, s02);

        return std::make_tuple(s02, z02);
    };

    struct Kernel12
    {
        TArrayView<int> x;
        TArrayView<vec3> v;
        TArrayView<const int64_t> p0q2;
        TArrayView<const int> s02;
        TArrayView<const double> z02;
        TArrayView<const int64_t> p1q1;
        TArrayView<const int> s11;
        TArrayView<const vec4> xyzz11;
        TArrayView<const Halfedge> halfedgesP;
        TArrayView<const Halfedge> halfedgesQ;
        TArrayView<const vec3> _vertPosP;
        const bool forward;
        const SparseIndices& p1q2;

        void operator()(const size_t id)
        {
            int p1 = p1q2.get(id, !forward);
            int q2 = p1q2.get(id, forward);
            int& x12 = x[id];
            vec3& v12 = v[id];

            // For xzyLR-[k], k==0 is the left and k==1 is the right.
            int k = 0;
            vec3 xzyLR0[2];
            vec3 xzyLR1[2];
            // Either the left or right must shadow, but not both. This ensures the
            // intersection is between the left and right.
            bool shadows = false;
            x12 = 0;

            const Halfedge edge0 = halfedgesP[p1];

            for (int vert : {edge0._startVert, edge0._endVert})
            {
                const int64_t key = forward ? SparseIndices::EncodePQ(vert, q2) : SparseIndices::EncodePQ(q2, vert);
                const size_t idx = monobound_quaternary_search(p0q2, key);
                if (idx != std::numeric_limits<size_t>::max())
                {
                    const int s = s02[idx];
                    x12 += s * ((vert == edge0._startVert) == forward ? 1 : -1);
                    if (k < 2 && (k == 0 || (s != 0) != shadows))
                    {
                        shadows = s != 0;
                        xzyLR0[k] = _vertPosP[vert];
                        std::swap(xzyLR0[k].y, xzyLR0[k].z);
                        xzyLR1[k] = xzyLR0[k];
                        xzyLR1[k][1] = z02[idx];
                        k++;
                    }
                }
            }

            for (const int i : {0, 1, 2})
            {
                const int         q1 = 3 * q2 + i;
                const Halfedge    edge = halfedgesQ[q1];
                const int         q1F = edge.isForward() ? q1 : edge._pairedHalfedge;
                const int64_t     key = forward ? SparseIndices::EncodePQ(p1, q1F) : SparseIndices::EncodePQ(q1F, p1);
                const size_t      idx = monobound_quaternary_search(p1q1, key);
                if (idx != std::numeric_limits<size_t>::max())
                {
                    // s is implicitly zero for
                    // anything not found
                    const int s = s11[idx];
                    x12 -= s * (edge.isForward() ? 1 : -1);
                    if (k < 2 && (k == 0 || (s != 0) != shadows))
                    {
                        shadows = s != 0;
                        auto  xyzz = xyzz11[idx];
                        xzyLR0[k][0] = xyzz.x;
                        xzyLR0[k][1] = xyzz.z;
                        xzyLR0[k][2] = xyzz.y;
                        xzyLR1[k] = xzyLR0[k];
                        xzyLR1[k][1] = xyzz.w;
                        if (!forward)
                            std::swap(xzyLR0[k][1], xzyLR1[k][1]);
                        k++;
                    }
                }
            }

            if (x12 == 0)
            {
                // No intersection
                v12 = vec3(NAN);
            }
            else
            {
                DEBUG_ASSERT(k == 2, logicErr, "Boolean manifold error: v12");
                auto xzyy = Intersect(xzyLR0[0], xzyLR0[1], xzyLR1[0], xzyLR1[1]);
                v12.x = xzyy[0];
                v12.y = xzyy[2];
                v12.z = xzyy[1];
            }
        }
    };

    std::tuple<TArray<int>, TArray<vec3>> Intersect12(
        const Manifold::Impl& inP
        , const Manifold::Impl& inQ
        , const TArray<int>& s02
        , const SparseIndices& p0q2
        , const TArray<int>& s11
        , const SparseIndices& p1q1
        , const TArray<double>& z02
        , const TArray<vec4>& xyzz11
        , SparseIndices& p1q2
        , bool forward)
    {
        ZoneScoped;
        TArray<int> x12(p1q2.size());
        TArray<vec3> v12(p1q2.size());

        for_each_n(
            autoPolicy(p1q2.size(), (size_t)1e4), countAt(0_uz), p1q2.size(),
            Kernel12({ x12, v12, p0q2.asVec64(), s02, z02, p1q1.asVec64(), s11, xyzz11,
                      inP._halfedge, inQ._halfedge, inP._vertPos, forward, p1q2 }));

        p1q2.KeepFinite(v12, x12);

        return std::make_tuple(x12, v12);
    };

    TArray<int> Winding03(const Manifold::Impl& inP, TArray<int>& _vertices, TArray<int>& s02, bool reverse)
    {
        ZoneScoped;
        // verts that are not shadowed (not in p0q2) have winding number zero.
        TArray<int> w03(inP.NumVert(), 0);
        if (_vertices.size() <= 1e5)
        {
            for_each_n(ExecutionPolicy::Seq, countAt(0), s02.size(),
                [&w03, &_vertices, &s02, reverse](const int i) {
                    w03[_vertices[i]] += s02[i] * (reverse ? -1 : 1);
                });
        }
        else
        {
            for_each_n(ExecutionPolicy::Par, countAt(0), s02.size(),
                [&w03, &_vertices, &s02, reverse](const int i)
                {
                    atomicAdd(w03[_vertices[i]], s02[i] * (reverse ? -1 : 1));
                });
        }
        return w03;
    };



    Boolean3::Boolean3(const Manifold::Impl& inP, const Manifold::Impl& inQ, OpType op)
        : _inP(inP)
        , _inQ(inQ)
        , _expandP(op == OpType::Add ? 1.0 : -1.0)
    {
        // Symbolic perturbation:
        // Union -> expand inP
        // Difference, Intersection -> contract inP


        if (inP.IsEmpty() || inQ.IsEmpty() || !inP._bBox.DoesOverlap(inQ._bBox))
        {
            PRINT("No overlap, early out");
            _w03.resize(inP.NumVert(), 0);
            _w30.resize(inQ.NumVert(), 0);
            return;
        }

        // Level 3
        // Find edge-triangle overlaps (broad phase)
        _p1q2 = _inQ.EdgeCollisions(_inP);
        _p2q1 = _inP.EdgeCollisions(_inQ, true);  // inverted

        _p1q2.sort();
        PRINT("p1q2 size = " << _p1q2.size());

        _p2q1.sort();
        PRINT("p2q1 size = " << _p2q1.size());

        // Level 2
        // Find _vertices that overlap _faces in XY-projection
        SparseIndices p0q2 = inQ.VertexCollisionsZ(inP._vertPos);
        p0q2.sort();
        PRINT("p0q2 size = " << p0q2.size());

        SparseIndices p2q0 = inP.VertexCollisionsZ(inQ._vertPos, true);  // inverted
        p2q0.sort();
        PRINT("p2q0 size = " << p2q0.size());

        // Find involved edge pairs from Level 3
        SparseIndices p1q1 = Filter11(_inP, _inQ, _p1q2, _p2q1);
        PRINT("p1q1 size = " << p1q1.size());


        // Level 2
        // Build up XY-projection intersection of two edges, including the z-value for
        // each edge, keeping only those whose intersection exists.
        TArray<int> s11;
        TArray<vec4> xyzz11;
        std::tie(s11, xyzz11) = Shadow11(p1q1, inP, inQ, _expandP);
        PRINT("s11 size = " << s11.size());

        // Build up Z-projection of _vertices onto triangles, keeping only those that
        // fall inside the triangle.
        TArray<int> s02;
        TArray<double> z02;
        std::tie(s02, z02) = Shadow02(inP, inQ, p0q2, true, _expandP);
        PRINT("s02 size = " << s02.size());

        TArray<int> s20;
        TArray<double> z20;
        std::tie(s20, z20) = Shadow02(inQ, inP, p2q0, false, _expandP);
        PRINT("s20 size = " << s20.size());

        // Level 3
        // Build up the intersection of the edges and triangles, keeping only those
        // that intersect, and record the direction the edge is passing through the
        // triangle.
        std::tie(_x12, _v12) =
            Intersect12(inP, inQ, s02, p0q2, s11, p1q1, z02, xyzz11, _p1q2, true);
        PRINT("x12 size = " << _x12.size());

        std::tie(_x21, _v21) =
            Intersect12(inQ, inP, s20, p2q0, s11, p1q1, z20, xyzz11, _p2q1, false);
        PRINT("x21 size = " << _x21.size());

        s11.clear();
        xyzz11.clear();
        z02.clear();
        z20.clear();

        TArray<int> p0 = p0q2.copy(false);
        p0q2.resize(0);
        TArray<int> q0 = p2q0.copy(true);
        p2q0.resize(0);
        // Sum up the winding numbers of all _vertices.
        _w03 = Winding03(inP, p0, s02, false);

        _w30 = Winding03(inQ, q0, s20, true);

    }
}


template <>
struct std::hash<std::pair<int, int>>
{
    size_t operator()(const std::pair<int, int>& p) const
    {
        return std::hash<int>()(p.first) ^ std::hash<int>()(p.second);
    }
};

namespace manifold
{
    struct  AbsSum
    {
        inline    int operator()(int a, int b) const { return abs(a) + abs(b); }
    };

    struct  DuplicateVerts
    {
        TArrayView<vec3>          _vertPosR;
        TArrayView<const int>     _inclusion;
        TArrayView<const int>     _vertR;
        TArrayView<const vec3>    _vertPosP;

        inline    void operator()(size_t vert)
        {
            const int n = std::abs(_inclusion[vert]);
            for (int i = 0; i < n; ++i)
            {
                _vertPosR[_vertR[vert] + i] = _vertPosP[vert];
            }
        }
    };

    template <bool atomic>
    struct  CountVerts
    {
        TArrayView<Halfedge>    _halfedges;
        TArrayView<int>         _count;
        TArrayView<const int>   _inclusion;

        void operator()(size_t i)
        {
            if (atomic)
                atomicAdd(_count[i / 3], std::abs(_inclusion[_halfedges[i]._startVert]));
            else
                _count[i / 3] += std::abs(_inclusion[_halfedges[i]._startVert]);
        }
    };

    template <const bool inverted, const bool atomic>
    struct  CountNewVerts
    {
        TArrayView<int>             _countP;
        TArrayView<int>             _countQ;
        TArrayView<const int>       _i12;
        const SparseIndices& _pq;
        TArrayView<const Halfedge>  _halfedges;

        void operator()(size_t idx)
        {
            int edgeP = _pq.get(idx, inverted);
            int faceQ = _pq.get(idx, !inverted);
            int inclusion = std::abs(_i12[idx]);

            if (atomic)
            {
                atomicAdd(_countQ[faceQ], inclusion);
                const Halfedge half = _halfedges[edgeP];
                atomicAdd(_countP[edgeP / 3], inclusion);
                atomicAdd(_countP[half._pairedHalfedge / 3], inclusion);
            }
            else
            {
                _countQ[faceQ] += inclusion;
                const Halfedge half = _halfedges[edgeP];
                _countP[edgeP / 3] += inclusion;
                _countP[half._pairedHalfedge / 3] += inclusion;
            }
        }
    };

    using   ArrayInt = TArray<int>;
    std::tuple<ArrayInt, ArrayInt> SizeOutput(
        Manifold::Impl& outR
        , const Manifold::Impl& inP
        , const Manifold::Impl& inQ
        , const ArrayInt& i03
        , const ArrayInt& i30
        , const ArrayInt& i12
        , const ArrayInt& i21
        , const SparseIndices& p1q2
        , const SparseIndices& p2q1
        , bool invertQ)
    {
        ZoneScoped;
        ArrayInt sidesPerFacePQ(inP.NumTri() + inQ.NumTri(), 0);
        // note: numFaceR <= facePQ2R.size() = sidesPerFacePQ.size() + 1

        auto sidesPerFaceP = sidesPerFacePQ.view(0, inP.NumTri());
        auto sidesPerFaceQ = sidesPerFacePQ.view(inP.NumTri(), inQ.NumTri());

        if (inP._halfedge.size() >= 1e5)
        {
            for_each(ExecutionPolicy::Par, countAt(0_uz), countAt(inP._halfedge.size()),
                CountVerts<true>({ inP._halfedge, sidesPerFaceP, i03 }));
            for_each(ExecutionPolicy::Par, countAt(0_uz), countAt(inQ._halfedge.size()),
                CountVerts<true>({ inQ._halfedge, sidesPerFaceQ, i30 }));
        }
        else
        {
            for_each(ExecutionPolicy::Seq, countAt(0_uz), countAt(inP._halfedge.size()),
                CountVerts<false>({ inP._halfedge, sidesPerFaceP, i03 }));
            for_each(ExecutionPolicy::Seq, countAt(0_uz), countAt(inQ._halfedge.size()),
                CountVerts<false>({ inQ._halfedge, sidesPerFaceQ, i30 }));
        }

        if (i12.size() >= 1e5)
        {
            for_each_n(ExecutionPolicy::Par, countAt(0), i12.size(),
                CountNewVerts<false, true>(
                    { sidesPerFaceP, sidesPerFaceQ, i12, p1q2, inP._halfedge }));
            for_each_n(ExecutionPolicy::Par, countAt(0), i21.size(),
                CountNewVerts<true, true>(
                    { sidesPerFaceQ, sidesPerFaceP, i21, p2q1, inQ._halfedge }));
        }
        else
        {
            for_each_n(ExecutionPolicy::Seq
                , countAt(0)
                , i12.size()
                , CountNewVerts<false, false>({ sidesPerFaceP, sidesPerFaceQ, i12, p1q2, inP._halfedge }));
            for_each_n(ExecutionPolicy::Seq
                , countAt(0)
                , i21.size()
                , CountNewVerts<true, false>({ sidesPerFaceQ, sidesPerFaceP, i21, p2q1, inQ._halfedge }));
        }

        ArrayInt facePQ2R(inP.NumTri() + inQ.NumTri() + 1, 0);
        auto keepFace = TransformItr(sidesPerFacePQ.begin(),
            [](int x) { return x > 0 ? 1 : 0; });

        inclusive_scan(keepFace, keepFace + sidesPerFacePQ.size(),
            facePQ2R.begin() + 1);
        int numFaceR = facePQ2R.back();
        facePQ2R.resize(inP.NumTri() + inQ.NumTri());

        outR._faceNormal.resize(numFaceR);

        TArray<size_t> tmpBuffer(outR._faceNormal.size());
        auto faceIds = TransformItr(countAt(0_uz), [&sidesPerFacePQ](size_t i)
            {
                if (sidesPerFacePQ[i] > 0) return i;
        return std::numeric_limits<size_t>::max();
            });

        auto next = copy_if(faceIds, faceIds + inP._faceNormal.size(), tmpBuffer.begin(),
            [](size_t v)
            {
                return v != std::numeric_limits<size_t>::max();
            });

        gather(tmpBuffer.begin(), next, inP._faceNormal.begin(), outR._faceNormal.begin());

        auto faceIdsQ = TransformItr(countAt(0_uz), [&sidesPerFacePQ, &inP](size_t i)
            {
                if (sidesPerFacePQ[i + inP._faceNormal.size()] > 0) return i;
        return std::numeric_limits<size_t>::max();
            });
        auto end =
            copy_if(faceIdsQ, faceIdsQ + inQ._faceNormal.size(), next,
                [](size_t v)
                {
                    return v != std::numeric_limits<size_t>::max();
                });

        if (invertQ)
        {
            gather(next
                , end
                , TransformItr(inQ._faceNormal.begin(), Negate<vec3>())
                , outR._faceNormal.begin() + std::distance(tmpBuffer.begin(), next));
        }
        else
        {
            gather(next
                , end
                , inQ._faceNormal.begin()
                , outR._faceNormal.begin() + std::distance(tmpBuffer.begin(), next));
        }

        auto newEnd = remove(sidesPerFacePQ.begin(), sidesPerFacePQ.end(), 0);
        ArrayInt faceEdge(newEnd - sidesPerFacePQ.begin() + 1, 0);
        inclusive_scan(sidesPerFacePQ.begin(), newEnd, faceEdge.begin() + 1);
        outR._halfedge.resize(faceEdge.back());

        return std::make_tuple(faceEdge, facePQ2R);
    }

    struct EdgePos
    {
        int       vert;
        double    edgePos;
        bool      isStart;
    };

    // thread sanitizer doesn't really know how to check when there are too many
    // mutex
#if defined(__has_feature)
#if __has_feature(thread_sanitizer)
    __attribute__((no_sanitize("thread")))
#endif
#endif

        /// we need std::map because we will be adding things concurrently
        void AddNewEdgeVerts(
            std::map<int, std::vector<EdgePos>>& edgesP
            , std::map<std::pair<int, int>, std::vector<EdgePos>>& edgesNew
            , const SparseIndices& p1q2, const ArrayInt& i12
            , const ArrayInt& v12R
            , const TArray<Halfedge>& _halfedgeP
            , bool forward)
    {
        ZoneScoped;
        // For each edge of P that intersects a face of Q (p1q2), add this vertex to
        // P's corresponding edge vector and to the two new edges, which are
        // intersections between the face of Q and the two _faces of P attached to the
        // edge. The direction and duplicity are given by i12, while v12R remaps to
        // the output vert index. When forward is false, all is reversed.
        auto process = [&](std::function<void(size_t)> lock, std::function<void(size_t)> unlock, size_t i)
        {
            const int edgeP = p1q2.get(i, !forward);
            const int faceQ = p1q2.get(i, forward);
            const int vert = v12R[i];
            const int inclusion = i12[i];

            Halfedge halfedge = _halfedgeP[edgeP];
            std::pair<int, int> keyRight = { halfedge._pairedHalfedge / 3, faceQ };
            if (!forward) std::swap(keyRight.first, keyRight.second);

            std::pair<int, int> keyLeft = { edgeP / 3, faceQ };
            if (!forward) std::swap(keyLeft.first, keyLeft.second);

            bool direction = inclusion < 0;
            std::hash<std::pair<int, int>> pairHasher;
            std::array<std::tuple<bool, size_t, std::vector<EdgePos>*>, 3> edges =
            {
                std::make_tuple(direction, std::hash<int>{}(edgeP), &edgesP[edgeP]),
                std::make_tuple(direction ^ !forward,  // revert if not forward
                    pairHasher(keyRight), &edgesNew[keyRight]),
                    std::make_tuple(direction ^ forward,  // revert if forward
                        pairHasher(keyLeft), &edgesNew[keyLeft])
            };
            for (const auto& tuple : edges)
            {
                lock(std::get<1>(tuple));
                for (int j = 0; j < std::abs(inclusion); ++j)
                    std::get<2>(tuple)->push_back({ vert + j, 0.0, std::get<0>(tuple) });
                unlock(std::get<1>(tuple));
                direction = !direction;
            }
        };
        auto processFun = std::bind(process, [](size_t) {}, [](size_t) {}, std::placeholders::_1);
        for (size_t i = 0; i < p1q2.size(); ++i) processFun(i);
    }

    std::vector<Halfedge> PairUp(std::vector<EdgePos>& edgePos)
    {
        // Pair start _vertices with end _vertices to form edges. The choice of pairing
        // is arbitrary for the manifoldness guarantee, but must be ordered to be
        // geometrically valid. If the order does not go start-end-start-end... then
        // the input and output are not geometrically valid and this algorithm becomes
        // a heuristic.
        DEBUG_ASSERT(edgePos.size() % 2 == 0, topologyErr,
            "Non-manifold edge! Not an even number of points.");
        size_t nEdges = edgePos.size() / 2;
        auto middle = std::partition(edgePos.begin(), edgePos.end(), [](EdgePos x) { return x.isStart; });
        DEBUG_ASSERT(static_cast<size_t>(middle - edgePos.begin()) == nEdges, topologyErr, "Non-manifold edge!");
        auto cmp = [](EdgePos a, EdgePos b) { return a.edgePos < b.edgePos; };
        std::stable_sort(edgePos.begin(), middle, cmp);
        std::stable_sort(middle, edgePos.end(), cmp);
        std::vector<Halfedge> edges;
        for (size_t i = 0; i < nEdges; ++i)
            edges.push_back({ edgePos[i].vert, edgePos[i + nEdges].vert, -1 });
        return edges;
    }

    void AppendPartialEdges(Manifold::Impl& outR, TArray<char>& wholeHalfedgeP,
        ArrayInt& facePtrR,
        std::map<int, std::vector<EdgePos>>& edgesP,
        TArray<TriRef>& halfedgeRef, const Manifold::Impl& inP,
        const ArrayInt& i03, const ArrayInt& vP2R,
        const ArrayInt::IterC faceP2R, bool forward)
    {
        ZoneScoped;
        // Each edge in the map is partially retained; for each of these, look up
        // their original verts and include them based on their winding number (i03),
        // while remapping them to the output using vP2R. Use the verts position
        // projected along the edge vector to pair them up, then distribute these
        // edges to their _faces.
        TArray<Halfedge>& _halfedgeR = outR._halfedge;
        const TArray<vec3>& vertPosP = inP._vertPos;
        const TArray<Halfedge>& _halfedgeP = inP._halfedge;

        for (auto& value : edgesP)
        {
            const int edgeP = value.first;
            std::vector<EdgePos>& edgePosP = value.second;

            const Halfedge& halfedge = _halfedgeP[edgeP];
            wholeHalfedgeP[edgeP] = false;
            wholeHalfedgeP[halfedge._pairedHalfedge] = false;

            const int vStart = halfedge._startVert;
            const int vEnd = halfedge._endVert;
            const vec3 edgeVec = vertPosP[vEnd] - vertPosP[vStart];
            // Fill in the edge positions of the old points.
            for (EdgePos& edge : edgePosP)
            {
                edge.edgePos = la::dot(outR._vertPos[edge.vert], edgeVec);
            }

            int     _inclusion = i03[vStart];
            EdgePos edgePos =
            {
                vP2R[vStart]
                ,la::dot(outR._vertPos[vP2R[vStart]], edgeVec)
                ,_inclusion > 0
            };
            for (int j = 0; j < std::abs(_inclusion); ++j)
            {
                edgePosP.push_back(edgePos);
                ++edgePos.vert;
            }

            _inclusion = i03[vEnd];
            edgePos = { vP2R[vEnd], la::dot(outR._vertPos[vP2R[vEnd]], edgeVec),_inclusion < 0 };
            for (int j = 0; j < std::abs(_inclusion); ++j)
            {
                edgePosP.push_back(edgePos);
                ++edgePos.vert;
            }

            // sort edges into start/end pairs along length
            std::vector<Halfedge> edges = PairUp(edgePosP);

            // add _halfedges to result
            const int faceLeftP = edgeP / 3;
            const int faceLeft = faceP2R[faceLeftP];
            const int faceRightP = halfedge._pairedHalfedge / 3;
            const int faceRight = faceP2R[faceRightP];
            // Negative _inclusion means the _halfedges are reversed, which means our
            // reference is now to the _endVert instead of the _startVert, which is one
            // position advanced isCCW. This is only valid if this is a retained vert; it
            // will be ignored later if the vert is new.
            const TriRef forwardRef = { forward ? 0 : 1, -1, faceLeftP };
            const TriRef backwardRef = { forward ? 0 : 1, -1, faceRightP };

            for (Halfedge e : edges)
            {
                const int forwardEdge = facePtrR[faceLeft]++;
                const int backwardEdge = facePtrR[faceRight]++;

                e._pairedHalfedge = backwardEdge;
                _halfedgeR[forwardEdge] = e;
                halfedgeRef[forwardEdge] = forwardRef;

                std::swap(e._startVert, e._endVert);
                e._pairedHalfedge = forwardEdge;
                _halfedgeR[backwardEdge] = e;
                halfedgeRef[backwardEdge] = backwardRef;
            }
        }
    }

    void AppendNewEdges(
        Manifold::Impl& outR
        , ArrayInt& facePtrR
        , std::map<std::pair<int, int>
        , std::vector<EdgePos>> &edgesNew
        , TArray<TriRef>& halfedgeRef
        , const ArrayInt& facePQ2R
        , const int numFaceP)
    {
        ZoneScoped;
        // Pair up each edge's verts and distribute to _faces based on indices in key.
        TArray<Halfedge>& _halfedgeR = outR._halfedge;
        TArray<vec3>& vertPosR = outR._vertPos;

        for (auto& value : edgesNew)
        {
            const int faceP = value.first.first;
            const int faceQ = value.first.second;
            std::vector<EdgePos>& edgePos = value.second;

            Box bbox;
            for (auto& edge : edgePos)
            {
                bbox.Union(vertPosR[edge.vert]);
            }
            const vec3 size = bbox.Size();
            // Order the points along their longest dimension.
            const int i = (size.x > size.y && size.x > size.z) ? 0
                : size.y > size.z ? 1
                : 2;
            for (auto& edge : edgePos)
            {
                edge.edgePos = vertPosR[edge.vert][i];
            }

            // sort edges into start/end pairs along length.
            std::vector<Halfedge> edges = PairUp(edgePos);

            // add _halfedges to result
            const int faceLeft = facePQ2R[faceP];
            const int faceRight = facePQ2R[numFaceP + faceQ];
            const TriRef forwardRef = { 0, -1, faceP };
            const TriRef backwardRef = { 1, -1, faceQ };
            for (Halfedge e : edges)
            {
                const int forwardEdge = facePtrR[faceLeft]++;
                const int backwardEdge = facePtrR[faceRight]++;

                e._pairedHalfedge = backwardEdge;
                _halfedgeR[forwardEdge] = e;
                halfedgeRef[forwardEdge] = forwardRef;

                std::swap(e._startVert, e._endVert);
                e._pairedHalfedge = forwardEdge;
                _halfedgeR[backwardEdge] = e;
                halfedgeRef[backwardEdge] = backwardRef;
            }
        }
    }

    struct DuplicateHalfedges
    {
        TArrayView<Halfedge>          halfedgesR;
        TArrayView<TriRef>            halfedgeRef;
        TArrayView<int>               facePtr;
        TArrayView<const char>        wholeHalfedgeP;
        TArrayView<const Halfedge>    halfedgesP;
        TArrayView<const int>         i03;
        TArrayView<const int>         vP2R;
        TArrayView<const int>         faceP2R;
        const bool forward;

        void operator()(const int idx)
        {
            if (!wholeHalfedgeP[idx])
                return;
            Halfedge halfedge = halfedgesP[idx];
            if (!halfedge.isForward())
                return;

            const int _inclusion = i03[halfedge._startVert];
            if (_inclusion == 0)
                return;
            if (_inclusion < 0)
            {  // reverse
                std::swap(halfedge._startVert, halfedge._endVert);
            }
            halfedge._startVert = vP2R[halfedge._startVert];
            halfedge._endVert = vP2R[halfedge._endVert];
            const int faceLeftP = idx / 3;
            const int newFace = faceP2R[faceLeftP];
            const int faceRightP = halfedge._pairedHalfedge / 3;
            const int faceRight = faceP2R[faceRightP];
            // Negative _inclusion means the _halfedges are reversed, which means our
            // reference is now to the _endVert instead of the _startVert, which is one
            // position advanced isCCW.
            const TriRef forwardRef = { forward ? 0 : 1, -1, faceLeftP };
            const TriRef backwardRef = { forward ? 0 : 1, -1, faceRightP };

            for (int i = 0; i < std::abs(_inclusion); ++i)
            {
                int forwardEdge = atomicAdd(facePtr[newFace], 1);
                int backwardEdge = atomicAdd(facePtr[faceRight], 1);
                halfedge._pairedHalfedge = backwardEdge;

                halfedgesR[forwardEdge] = halfedge;
                halfedgesR[backwardEdge] = { halfedge._endVert, halfedge._startVert,forwardEdge };
                halfedgeRef[forwardEdge] = forwardRef;
                halfedgeRef[backwardEdge] = backwardRef;

                ++halfedge._startVert;
                ++halfedge._endVert;
            }
        }
    };

    void AppendWholeEdges(Manifold::Impl& outR, ArrayInt& facePtrR,
        TArray<TriRef>& halfedgeRef, const Manifold::Impl& inP,
        const TArray<char> wholeHalfedgeP, const ArrayInt& i03,
        const ArrayInt& vP2R, TArrayView<const int> faceP2R,
        bool forward)
    {
        ZoneScoped;
        for_each_n(
            autoPolicy(inP._halfedge.size()), countAt(0), inP._halfedge.size(),
            DuplicateHalfedges({ outR._halfedge, halfedgeRef, facePtrR, wholeHalfedgeP,inP._halfedge, i03, vP2R, faceP2R, forward }));
    }

    struct  MapTriRef
    {
        TArrayView<const TriRef> triRefP;
        TArrayView<const TriRef> triRefQ;
        const int offsetQ;

        void operator()(TriRef& triRef)
        {
            const int tri = triRef.tri;
            const bool PQ = triRef.meshID == 0;
            triRef = PQ ? triRefP[tri] : triRefQ[tri];
            if (!PQ)
                triRef.meshID += offsetQ;
        }
    };

    void UpdateReference(Manifold::Impl& outR, const Manifold::Impl& inP, const Manifold::Impl& inQ, bool invertQ)
    {
        const int offsetQ = Manifold::Impl::_meshIDCounter;
        for_each_n(
            autoPolicy(outR.NumTri(), (size_t)1e5), outR._meshRelation.triRef.begin(),
            outR.NumTri(),
            MapTriRef({ inP._meshRelation.triRef, inQ._meshRelation.triRef, offsetQ }));

        for (const auto& pair : inP._meshRelation.meshIDtransform)
        {
            outR._meshRelation.meshIDtransform[pair.first] = pair.second;
        }
        for (const auto& pair : inQ._meshRelation.meshIDtransform)
        {
            outR._meshRelation.meshIDtransform[pair.first + offsetQ] = pair.second;
            outR._meshRelation.meshIDtransform[pair.first + offsetQ].backSide ^= invertQ;
        }
    }

    struct  BarycentricTri
    {
        TArrayView<vec3>              _uvw;
        TArrayView<const TriRef>      _ref;
        TArrayView<const vec3>        _vertPosP;
        TArrayView<const vec3>        _vertPosQ;
        TArrayView<const vec3>        _vertPosR;
        TArrayView<const Halfedge>    _halfedgeP;
        TArrayView<const Halfedge>    _halfedgeQ;
        TArrayView<const Halfedge>    _halfedgeR;
        const double                  epsilon;

        void operator()(const int tri)
        {
            const TriRef refPQ = _ref[tri];
            if (_halfedgeR[3 * tri]._startVert < 0) return;

            const int   triPQ = refPQ.tri;
            const bool  PQ = refPQ.meshID == 0;
            const auto& vertPos = PQ ? _vertPosP : _vertPosQ;
            const auto& halfedge = PQ ? _halfedgeP : _halfedgeQ;

            mat3 triPos;
            for (const int j : {0, 1, 2})
                triPos[j] = vertPos[halfedge[3 * triPQ + j]._startVert];

            for (const int i : {0, 1, 2})
            {
                const int vert = _halfedgeR[3 * tri + i]._startVert;
                _uvw[3 * tri + i] = GetBarycentric(_vertPosR[vert], triPos, epsilon);
            }
        }
    };

    void CreateProperties(Manifold::Impl& outR, const Manifold::Impl& inP, const Manifold::Impl& inQ)
    {
        using   Entry = std::pair<ivec3, int>;
        using   Entrys = std::vector<Entry>;
        using   Entry2s = std::vector<Entrys>;
        using   ints = std::vector<int>;

        ZoneScoped;
        const int numPropP = (int)inP.NumProp();
        const int numPropQ = (int)inQ.NumProp();
        const int numProp = (int)std::max(numPropP, numPropQ);
        outR._meshRelation.numProp = numProp;
        if (numProp == 0)
            return;

        const int numTri = (int)outR.NumTri();
        outR._meshRelation.triProperties.resize(numTri);

        TArray<vec3> bary(outR._halfedge.size());
        for_each_n(autoPolicy(numTri, (size_t)1e4), countAt(0), numTri,
            BarycentricTri({ bary, outR._meshRelation.triRef, inP._vertPos,
                inQ._vertPos, outR._vertPos, inP._halfedge,
                inQ._halfedge, outR._halfedge, outR._epsilon }));

        int         idMissProp = (int)outR.NumVert();
        Entry2s     propIdx(outR.NumVert() + 1);
        ints        propMissIdx[2];
        propMissIdx[0].resize(inQ.NumPropVert(), -1);
        propMissIdx[1].resize(inP.NumPropVert(), -1);

        outR._meshRelation.properties.reserve(outR.NumVert() * numProp);
        int idx = 0;

        for (int tri = 0; tri < numTri; ++tri)
        {
            // Skip collapsed triangles
            if (outR._halfedge[3 * tri]._startVert < 0)
                continue;

            const TriRef  ref = outR._meshRelation.triRef[tri];
            const bool    PQ = ref.meshID == 0;
            const int     oldNumProp = PQ ? numPropP : numPropQ;
            const auto& properties = PQ ? inP._meshRelation.properties : inQ._meshRelation.properties;
            const ivec3& triProp = oldNumProp == 0 ? ivec3(-1)
                : PQ ? inP._meshRelation.triProperties[ref.tri]
                : inQ._meshRelation.triProperties[ref.tri];

            for (const int i : {0, 1, 2}) {
                const int vert = outR._halfedge[3 * tri + i]._startVert;
                const vec3& _uvw = bary[3 * tri + i];

                ivec4 key(PQ, idMissProp, -1, -1);
                if (oldNumProp > 0)
                {
                    int edge = -2;
                    for (const int j : {0, 1, 2})
                    {
                        if (_uvw[j] == 1)
                        {
                            // On a retained vert, the propVert must also match
                            key[2] = triProp[j];
                            edge = -1;
                            break;
                        }
                        if (_uvw[j] == 0) edge = j;
                    }
                    if (edge >= 0)
                    {
                        // On an edge, both propVerts must match
                        const int p0 = triProp[next3(edge)];
                        const int p1 = triProp[prev3(edge)];
                        key[1] = vert;
                        key[2] = std::min(p0, p1);
                        key[3] = std::max(p0, p1);
                    }
                    else if (edge == -2)
                    {
                        key[1] = vert;
                    }
                }

                if (key.y == idMissProp && key.z >= 0)
                {
                    // only key.x/key.z matters
                    auto& entry = propMissIdx[key.x][key.z];
                    if (entry >= 0)
                    {
                        outR._meshRelation.triProperties[tri][i] = entry;
                        continue;
                    }
                    entry = idx;
                }
                else
                {
                    auto& bin = propIdx[key.y];
                    bool bFound = false;
                    for (const auto& b : bin)
                    {
                        if (b.first == ivec3(key.x, key.z, key.w))
                        {
                            bFound = true;
                            outR._meshRelation.triProperties[tri][i] = b.second;
                            break;
                        }
                    }
                    if (bFound) continue;
                    bin.push_back(std::make_pair(ivec3(key.x, key.z, key.w), idx));
                }

                outR._meshRelation.triProperties[tri][i] = idx++;
                for (int p = 0; p < numProp; ++p)
                {
                    if (p < oldNumProp)
                    {
                        vec3 oldProps;
                        for (const int j : {0, 1, 2})
                            oldProps[j] = properties[oldNumProp * triProp[j] + p];
                        outR._meshRelation.properties.push_back(la::dot(_uvw, oldProps));
                    }
                    else
                    {
                        outR._meshRelation.properties.push_back(0);
                    }
                }
            }
        }
    }

    Manifold::Impl Boolean3::Result(OpType op) const
    {
        DEBUG_ASSERT((_expandP > 0) == (op == OpType::Add), logicErr, "Result op type not compatible with constructor op type.");
        const int c1 = op == OpType::Intersect ? 0 : 1;
        const int c2 = op == OpType::Add ? 1 : 0;
        const int c3 = op == OpType::Intersect ? 1 : -1;

        if (_inP._status != Manifold::Error::NoError)
        {
            auto _impl = Manifold::Impl();
            _impl._status = _inP._status;
            return _impl;
        }
        if (_inQ._status != Manifold::Error::NoError)
        {
            auto _impl = Manifold::Impl();
            _impl._status = _inQ._status;
            return _impl;
        }

        if (_inP.IsEmpty())
        {
            if (!_inQ.IsEmpty() && op == OpType::Add)
            {
                return _inQ;
            }
            return Manifold::Impl();
        }
        else if (_inQ.IsEmpty())
        {

            if (op == OpType::Intersect)
            {
                return Manifold::Impl();
            }
            return _inP;
        }

        const bool invertQ = op == OpType::Subtract;

        // Convert winding numbers to _inclusion values based on operation type.
        ArrayInt i12(_x12.size());
        ArrayInt i21(_x21.size());
        ArrayInt i03(_w03.size());
        ArrayInt i30(_w30.size());

        transform(_x12.begin(), _x12.end(), i12.begin(), [c3](int v) { return c3 * v; });
        transform(_x21.begin(), _x21.end(), i21.begin(), [c3](int v) { return c3 * v; });
        transform(_w03.begin(), _w03.end(), i03.begin(), [c1, c3](int v) { return c1 + c3 * v; });
        transform(_w30.begin(), _w30.end(), i30.begin(), [c2, c3](int v) { return c2 + c3 * v; });

        ArrayInt vP2R(_inP.NumVert());
        exclusive_scan(i03.begin(), i03.end(), vP2R.begin(), 0, AbsSum());
        int numVertR = AbsSum()(vP2R.back(), i03.back());
        const int nPv = numVertR;

        ArrayInt vQ2R(_inQ.NumVert());
        exclusive_scan(i30.begin(), i30.end(), vQ2R.begin(), numVertR, AbsSum());
        numVertR = AbsSum()(vQ2R.back(), i30.back());
        const int nQv = numVertR - nPv;

        ArrayInt v12R(_v12.size());
        if (_v12.size() > 0)
        {
            exclusive_scan(i12.begin(), i12.end(), v12R.begin(), numVertR, AbsSum());
            numVertR = AbsSum()(v12R.back(), i12.back());
        }
        const int n12 = numVertR - nPv - nQv;

        ArrayInt v21R(_v21.size());
        if (_v21.size() > 0)
        {
            exclusive_scan(i21.begin(), i21.end(), v21R.begin(), numVertR, AbsSum());
            numVertR = AbsSum()(v21R.back(), i21.back());
        }
        const int n21 = numVertR - nPv - nQv - n12;
        (void*)&n21;
        // Create the output Manifold
        Manifold::Impl outR;

        if (numVertR == 0) return outR;

        outR._epsilon = std::max(_inP._epsilon, _inQ._epsilon);
        outR._tolerance = std::max(_inP._tolerance, _inQ._tolerance);

        outR._vertPos.resize(numVertR);
        // Add _vertices, duplicating for _inclusion numbers not in [-1, 1].
        // Retained _vertices from P and Q:
        for_each_n(autoPolicy(_inP.NumVert(), (size_t)1e4), countAt(0), _inP.NumVert(),
            DuplicateVerts({ outR._vertPos, i03, vP2R, _inP._vertPos }));
        for_each_n(autoPolicy(_inQ.NumVert(), (size_t)1e4), countAt(0), _inQ.NumVert(),
            DuplicateVerts({ outR._vertPos, i30, vQ2R, _inQ._vertPos }));
        // New _vertices created from intersections:
        for_each_n(autoPolicy(i12.size(), (size_t)1e4), countAt(0), i12.size(),
            DuplicateVerts({ outR._vertPos, i12, v12R, _v12 }));
        for_each_n(autoPolicy(i21.size(), (size_t)1e4), countAt(0), i21.size(),
            DuplicateVerts({ outR._vertPos, i21, v21R, _v21 }));

        PRINT(nPv << " verts from inP");
        PRINT(nQv << " verts from inQ");
        PRINT(n12 << " new verts from edgesP -> facesQ");
        PRINT(n21 << " new verts from facesP -> edgesQ");

        // Build up new polygonal _faces from triangle intersections. at this point the
        // calculation switches from parallel to serial.

        // Level 3

        // This key is the forward halfedge index of P or Q. Only includes intersected
        // edges.
        std::map<int, std::vector<EdgePos>> edgesP, edgesQ;
        // This key is the face index of <P, Q>
        std::map<std::pair<int, int>, std::vector<EdgePos>> edgesNew;

        AddNewEdgeVerts(edgesP, edgesNew, _p1q2, i12, v12R, _inP._halfedge, true);
        AddNewEdgeVerts(edgesQ, edgesNew, _p2q1, i21, v21R, _inQ._halfedge, false);

        v12R.clear();
        v21R.clear();

        // Level 4
        ArrayInt faceEdge;
        ArrayInt facePQ2R;
        std::tie(faceEdge, facePQ2R) =
            SizeOutput(outR, _inP, _inQ, i03, i30, i12, i21, _p1q2, _p2q1, invertQ);

        i12.clear();
        i21.clear();

        // This gets incremented for each halfedge that's added to a face so that the
        // next one knows where to slot in.
        ArrayInt facePtrR = faceEdge;
        // Intersected _halfedges are marked false.
        TArray<char> wholeHalfedgeP(_inP._halfedge.size(), true);
        TArray<char> wholeHalfedgeQ(_inQ._halfedge.size(), true);
        // The halfedgeRef contains the data that will become triRef once the _faces
        // are triangulated.
        TArray<TriRef> halfedgeRef(2 * outR.NumEdge());

        AppendPartialEdges(outR, wholeHalfedgeP, facePtrR, edgesP, halfedgeRef, _inP,
            i03, vP2R, facePQ2R.begin(), true);
        AppendPartialEdges(outR, wholeHalfedgeQ, facePtrR, edgesQ, halfedgeRef, _inQ,
            i30, vQ2R, facePQ2R.begin() + _inP.NumTri(), false);

        edgesP.clear();
        edgesQ.clear();

        AppendNewEdges(outR, facePtrR, edgesNew, halfedgeRef, facePQ2R, (int)_inP.NumTri());

        edgesNew.clear();

        AppendWholeEdges(outR, facePtrR, halfedgeRef, _inP, wholeHalfedgeP, i03, vP2R,
            facePQ2R.cview(0, _inP.NumTri()), true);
        AppendWholeEdges(outR, facePtrR, halfedgeRef, _inQ, wholeHalfedgeQ, i30, vQ2R,
            facePQ2R.cview(_inP.NumTri(), _inQ.NumTri()), false);

        wholeHalfedgeP.clear();
        wholeHalfedgeQ.clear();
        facePtrR.clear();
        facePQ2R.clear();
        i03.clear();
        i30.clear();
        vP2R.clear();
        vQ2R.clear();

        // Level 6

        if (ManifoldParams().intermediateChecks)
        {
            DEBUG_ASSERT(outR.IsManifold(), logicErr, "polygon mesh is not manifold!");
        }
        outR.Face2Tri(faceEdge, halfedgeRef);
        halfedgeRef.clear();
        faceEdge.clear();

        if (ManifoldParams().intermediateChecks)
        {
            DEBUG_ASSERT(outR.IsManifold(), logicErr, "triangulated mesh is not manifold!");
        }
        CreateProperties(outR, _inP, _inQ);

        UpdateReference(outR, _inP, _inQ, invertQ);

        outR.SimplifyTopology();
        outR.RemoveUnreferencedVerts();

        if (ManifoldParams().intermediateChecks)
        {
            DEBUG_ASSERT(outR.Is2Manifold(), logicErr, "simplified mesh is not 2-manifold!");
        }

        outR.Finish();
        outR.IncrementMeshIDs();

        return outR;
    }

}



namespace manifold
{
    /// <summary>
    /// 通过创建切向量来构造输入网格的平滑版本。如果网格已经包含切向量，则此方法将抛出异常。
    /// 实际的三角形分辨率保持不变；请使用<c>Refine()</c>方法来插值到更高分辨率的曲线。
    /// 
    /// 默认情况下，每条边都会为了最大平滑度（非常近似）进行计算，尝试最小化最大平均曲率的大小。
    /// 不考虑高阶导数，因为插值是每个三角形独立的，仅在边界上共享约束。
    /// 
    /// </summary>
    /// <param name="meshGL">输入的MeshGL网格。</param>
    /// <param name="sharpenedEdges">（可选）一个包含锐化半边的向量，这通常应该是所有半边的一个小子集。
    /// 条目的顺序不重要，因为每个条目都指定了所需的平滑度（介于0和1之间，未指定的半边默认为1）
    /// 和半边索引（3 * 三角形索引 + [0,1,2]，其中0是triVert 0和1之间的边，依此类推）。
    /// 
    /// 平滑度值为0时，将形成锐利的折痕。平滑度沿每条边进行插值，因此指定的值应视为平均值。
    /// 当两个锐化边在顶点处精确相交时，它们的切向量会旋转为共线，以使锐化边连续。
    /// 只有一条锐化边的顶点是完全平滑的，允许锐化边在终止处平滑消失。
    /// 通过锐化所有入射边，可以使单个顶点锐化，从而形成锥体。
    /// </param>
    /// <remarks>
    /// 此方法用于生成输入网格的平滑版本，其中通过计算切向量来实现平滑效果。
    /// 请注意，如果网格已经包含切向量，则此方法将抛出异常。
    /// 此外，此方法不会改变网格的实际三角形分辨率；如果需要更高分辨率的曲线，
    /// 请使用<c>Refine()</c>方法进行插值。
    /// 
    /// 在计算平滑度时，该方法会尝试最小化最大平均曲率的大小，但不考虑高阶导数。
    /// 插值是每个三角形独立的，但在边界上共享约束。
    /// 
    /// 通过提供可选的<c>sharpenedEdges</c>参数，用户可以指定哪些半边应该被锐化。
    /// 这通常用于在网格上创建锐利的折痕或特征线。平滑度值介于0和1之间，
    /// 其中0表示锐化的折痕，1表示默认的平滑度（对于未指定的半边）。
    /// 当两个锐化边在顶点处相交时，它们的切向量会进行特殊处理以确保连续性。
    /// 只有一条锐化边的顶点将保持完全平滑。
    /// </remarks>
    /// <exception cref="InvalidOperationException">如果输入的网格已经包含切向量，则抛出此异常。</exception>
    using  ArrayInt = TArray<int>;
    using  ArrayIVec3 = TArray<ivec3>;

    Manifold Manifold::Smooth(const MeshGL& meshGL, const std::vector<Smoothness>& sharpenedEdges)
    {
        DEBUG_ASSERT(meshGL.halfedgeTangent.empty(), std::runtime_error,
            "when supplying tangents, the normal constructor should be used "
            "rather than Smooth().");

        std::shared_ptr<Impl> _impl = std::make_shared<Impl>(meshGL);
        _impl->CreateTangents(_impl->UpdateSharpenedEdges(sharpenedEdges));
        return Manifold(_impl);
    }

    Manifold Manifold::Smooth(const MeshGL64& meshGL64, const std::vector<Smoothness>& sharpenedEdges)
    {
        DEBUG_ASSERT(meshGL64.halfedgeTangent.empty(), std::runtime_error,
            "when supplying tangents, the normal constructor should be used "
            "rather than Smooth().");

        std::shared_ptr<Impl> _impl = std::make_shared<Impl>(meshGL64);
        _impl->CreateTangents(_impl->UpdateSharpenedEdges(sharpenedEdges));
        return Manifold(_impl);
    }

    Manifold Manifold::Tetrahedron()
    {
        return Manifold(std::make_shared<Impl>(Impl::Shape::Tetrahedron));
    }

    Manifold Manifold::Cube(vec3 size, bool center)
    {
        if (size.x < 0.0 || size.y < 0.0 || size.z < 0.0 || la::length(size) == 0.)
        {
            return Invalid();
        }
        mat3x4 m({ {size.x, 0.0, 0.0}, {0.0, size.y, 0.0}, {0.0, 0.0, size.z} }, center ? (-size / 2.0) : vec3(0.0));
        return Manifold(std::make_shared<Impl>(Manifold::Impl::Shape::Cube, m));
    }

    Manifold Manifold::Cylinder(double height
        , double radiusLow
        , double radiusHigh
        , int circularSegments
        , bool center)
    {
        if (height <= 0.0 || radiusLow <= 0.0)
        {
            return Invalid();
        }
        const double scale = radiusHigh >= 0.0 ? radiusHigh / radiusLow : 1.0;
        const double radius = fmax(radiusLow, radiusHigh);
        const int n = circularSegments > 2 ? circularSegments : Quality::getCircularSegments(radius);

        SimplePolygon     circle(n);
        const double      dPhi = 360.0 / n;
        for (int i = 0; i < n; ++i)
        {
            circle[i] = { radiusLow * cosd(dPhi * i), radiusLow * sind(dPhi * i) };
        }

        Manifold cylinder = Manifold::Extrude({ circle }, height, 0, 0.0, vec2(scale));
        if (center)
            cylinder = cylinder.Translate(vec3(0.0, 0.0, -height / 2.0)).AsOriginal();
        return cylinder;
    }


    Manifold Manifold::Sphere(double radius, int circularSegments) {
        if (radius <= 0.0)
        {
            return Invalid();
        }
        int n = circularSegments > 0 ? (circularSegments + 3) / 4 : Quality::getCircularSegments(radius) / 4;
        auto _pImpl = std::make_shared<Impl>(Impl::Shape::Octahedron);
        _pImpl->Subdivide([n](vec3, vec4, vec4)
            {
                return n - 1;
            });
        for_each_n(autoPolicy(_pImpl->NumVert(), (size_t)1e5), _pImpl->_vertPos.begin(), _pImpl->NumVert(), [radius](vec3& v)
            {
                v = la::cos(kHalfPi * (1.0 - v));
        v = radius * la::normalize(v);
        if (std::isnan(v.x)) v = vec3(0.0);
            });
        _pImpl->Finish();
        // Ignore preceding octahedron.
        _pImpl->InitializeOriginal();
        return Manifold(_pImpl);
    }

    Manifold Manifold::Extrude(const Polygons& crossSection, double height, int nDivisions, double twistDegrees, vec2 scaleTop)
    {
        ZoneScoped;
        if (crossSection.size() == 0 || height <= 0.0)
        {
            return Invalid();
        }

        scaleTop.x = std::max(scaleTop.x, 0.0);
        scaleTop.y = std::max(scaleTop.y, 0.0);

        auto _pImpl = std::make_shared<Impl>();
        ++nDivisions;
        auto& vertPos = _pImpl->_vertPos;
        ArrayIVec3 triVertsDH;
        auto& triVerts = triVertsDH;
        int nCrossSection = 0;
        bool isCone = scaleTop.x == 0.0 && scaleTop.y == 0.0;
        size_t idx0 = 0;
        PolygonsIdx polygonsIndexed;
        for (auto& poly : crossSection)
        {
            nCrossSection += (int)poly.size();
            SimplePolygonIdx simpleIndexed;
            for (const vec2& polyVert : poly)
            {
                vertPos.push_back({ polyVert.x, polyVert.y, 0.0 });
                simpleIndexed.push_back({ polyVert, static_cast<int>(idx0++) });
            }
            polygonsIndexed.push_back(simpleIndexed);
        }
        for (int i = 1; i < nDivisions + 1; ++i)
        {
            double  alpha = i / double(nDivisions);
            double  phi = alpha * twistDegrees;
            vec2    scale = la::lerp(vec2(1.0), scaleTop, alpha);
            mat2    rotation({ cosd(phi), sind(phi) }, { -sind(phi), cosd(phi) });
            mat2    transform = mat2({ scale.x, 0.0 }, { 0.0, scale.y }) * rotation;
            size_t  j = 0;
            size_t  idx = 0;
            for (const auto& poly : crossSection)
            {
                for (size_t vert = 0; vert < poly.size(); ++vert)
                {
                    size_t offset = idx + nCrossSection * i;
                    size_t thisVert = vert + offset;
                    size_t lastVert = (vert == 0 ? poly.size() : vert) - 1 + offset;
                    if (i == nDivisions && isCone)
                    {
                        triVerts.push_back(ivec3(nCrossSection * i + int(j),
                            (int)(lastVert - nCrossSection),
                            (int)(thisVert - nCrossSection)));
                    }
                    else
                    {
                        vec2 pos = transform * poly[vert];
                        vertPos.push_back({ pos.x, pos.y, height * alpha });
                        triVerts.push_back(ivec3((int)thisVert, (int)lastVert, (int)(thisVert - nCrossSection)));
                        triVerts.push_back(ivec3((int)lastVert, (int)(lastVert - nCrossSection), (int)(thisVert - nCrossSection)));
                    }
                }
                ++j;
                idx += poly.size();
            }
        }
        if (isCone)
            for (size_t j = 0; j < crossSection.size(); ++j)  // Duplicate vertex for Genus
                vertPos.push_back({ 0.0, 0.0, height });
        std::vector<ivec3> top = TriangulateIdx(polygonsIndexed, 1e-8);
        for (const ivec3& tri : top)
        {
            triVerts.push_back({ tri[0], tri[2], tri[1] });
            if (!isCone)
                triVerts.push_back(tri + nCrossSection * nDivisions);
        }

        _pImpl->CreateHalfedges(triVertsDH);
        _pImpl->Finish();
        _pImpl->InitializeOriginal();
        _pImpl->CreateFaces();
        return Manifold(_pImpl);
    }

    Manifold Manifold::Revolve(const Polygons& crossSection, int circularSegments, double revolveDegrees)
    {
        ZoneScoped;

        Polygons polygons;
        double radius = 0;
        for (const SimplePolygon& poly : crossSection)
        {
            size_t i = 0;
            while (i < poly.size() && poly[i].x < 0)
            {
                ++i;
            }
            if (i == poly.size())
            {
                continue;
            }
            polygons.push_back({});
            const size_t start = i;
            do {
                if (poly[i].x >= 0)
                {
                    polygons.back().push_back(poly[i]);
                    radius = std::max(radius, poly[i].x);
                }
                const size_t next = i + 1 == poly.size() ? 0 : i + 1;
                if ((poly[next].x < 0) != (poly[i].x < 0))
                {
                    const double y = poly[next].y + poly[next].x *
                        (poly[i].y - poly[next].y) /
                        (poly[i].x - poly[next].x);
                    polygons.back().push_back({ 0, y });
                }
                i = next;
            } while (i != start);
        }

        if (polygons.empty())
        {
            return Invalid();
        }

        if (revolveDegrees > 360.0)
        {
            revolveDegrees = 360.0;
        }
        const bool isFullRevolution = revolveDegrees == 360.0;

        const int nDivisions =
            circularSegments > 2
            ? circularSegments
            : Quality::getCircularSegments(radius) * int(revolveDegrees / 360);

        auto _pImpl = std::make_shared<Impl>();
        auto& vertPos = _pImpl->_vertPos;
        ArrayIVec3 triVertsDH;
        auto& triVerts = triVertsDH;

        std::vector<int> startPoses;
        std::vector<int> endPoses;

        const double dPhi = revolveDegrees / nDivisions;
        // first and last slice are distinguished if not a full revolution.
        const int nSlices = isFullRevolution ? nDivisions : nDivisions + 1;

        for (const auto& poly : polygons)
        {
            std::size_t nPosVerts = 0;
            std::size_t nRevolveAxisVerts = 0;
            for (auto& pt : poly)
            {
                if (pt.x > 0)
                {
                    nPosVerts++;
                }
                else
                {
                    nRevolveAxisVerts++;
                }
            }

            for (size_t polyVert = 0; polyVert < poly.size(); ++polyVert)
            {
                const size_t startPosIndex = vertPos.size();

                if (!isFullRevolution) startPoses.push_back((int)startPosIndex);

                const vec2 currPolyVertex = poly[polyVert];
                const vec2 prevPolyVertex =
                    poly[polyVert == 0 ? poly.size() - 1 : polyVert - 1];

                const int prevStartPosIndex =
                    (int)startPosIndex +
                    (polyVert == 0 ? (int)nRevolveAxisVerts + (nSlices * (int)nPosVerts) : 0) +
                    (prevPolyVertex.x == 0.0 ? -1 : -nSlices);

                for (int slice = 0; slice < nSlices; ++slice)
                {
                    const double phi = slice * dPhi;
                    if (slice == 0 || currPolyVertex.x > 0)
                    {
                        vertPos.push_back({ currPolyVertex.x * cosd(phi),currPolyVertex.x * sind(phi), currPolyVertex.y });
                    }

                    if (isFullRevolution || slice > 0) {
                        const int lastSlice = (slice == 0 ? nDivisions : slice) - 1;
                        if (currPolyVertex.x > 0.0)
                        {
                            triVerts.push_back(ivec3(
                                (int)(startPosIndex + slice), (int)(startPosIndex + lastSlice),
                                // "Reuse" vertex of first slice if it lies on the revolve axis
                                (prevPolyVertex.x == 0.0 ? prevStartPosIndex
                                    : prevStartPosIndex + lastSlice)));
                        }

                        if (prevPolyVertex.x > 0.0)
                        {
                            triVerts.push_back(
                                ivec3(prevStartPosIndex + lastSlice
                                    , prevStartPosIndex + slice
                                    , (currPolyVertex.x == 0.0 ? (int)startPosIndex : (int)startPosIndex + slice)));
                        }
                    }
                }
                if (!isFullRevolution) endPoses.push_back((int)vertPos.size() - 1);
            }
        }

        // Add front and back triangles if not a full revolution.
        if (!isFullRevolution)
        {
            std::vector<ivec3> frontTriangles = Triangulate(polygons, _pImpl->_epsilon);
            for (auto& t : frontTriangles)
            {
                triVerts.push_back({ startPoses[t.x], startPoses[t.y], startPoses[t.z] });
            }

            for (auto& t : frontTriangles)
            {
                triVerts.push_back({ endPoses[t.z], endPoses[t.y], endPoses[t.x] });
            }
        }

        _pImpl->CreateHalfedges(triVertsDH);
        _pImpl->Finish();
        _pImpl->InitializeOriginal();
        _pImpl->CreateFaces();
        return Manifold(_pImpl);
    }

    Manifold Manifold::Compose(const std::vector<Manifold>& manifolds)
    {
        std::vector<std::shared_ptr<CsgLeafNode>> children;
        for (const auto& manifold : manifolds)
        {
            children.push_back(manifold.pNode_->ToLeafNode());
        }
        return Manifold(CsgLeafNode::Compose(children));
    }

    std::vector<Manifold> Manifold::Decompose() const
    {
        ZoneScoped;
        UnionFind<> uf((int)NumVert());
        // Graph graph;
        auto _pImpl = GetCsgLeafNode().GetImpl();
        for (const Halfedge& halfedge : _pImpl->_halfedge)
        {
            if (halfedge.isForward())
                uf.unionXY(halfedge._startVert, halfedge._endVert);
        }
        std::vector<int> componentIndices;
        const int numComponents = uf.connectedComponents(componentIndices);

        if (numComponents == 1)
        {
            std::vector<Manifold> meshes(1);
            meshes[0] = *this;
            return meshes;
        }
        ArrayInt vertLabel(componentIndices);

        const size_t numVert = NumVert();
        std::vector<Manifold> meshes;
        for (int i = 0; i < numComponents; ++i)
        {
            auto _impl = std::make_shared<Impl>();
            // inherit original object's precision
            _impl->_epsilon = _pImpl->_epsilon;
            _impl->_tolerance = _pImpl->_tolerance;

            ArrayInt vertNew2Old(numVert);
            auto nVert = copy_if(countAt(0)
                , countAt(numVert)
                , vertNew2Old.begin()
                , [i, &vertLabel](int v) { return vertLabel[v] == i; }) - vertNew2Old.begin();
            _impl->_vertPos.resize(nVert);
            vertNew2Old.resize(nVert);
            gather(vertNew2Old.begin(), vertNew2Old.end(), _pImpl->_vertPos.begin(),
                _impl->_vertPos.begin());

            ArrayInt faceNew2Old(NumTri());
            const auto& halfedge = _pImpl->_halfedge;

            const auto  nFace = copy_if(countAt(0_uz)
                , countAt(NumTri())
                , faceNew2Old.begin(),
                [i, &vertLabel, &halfedge](int face)
                {
                    return vertLabel[halfedge[3 * face]._startVert] == i;
                }) - faceNew2Old.begin();

                if (nFace == 0)
                    continue;
                faceNew2Old.resize(nFace);

                _impl->GatherFaces(*_pImpl, faceNew2Old);
                _impl->ReindexVerts(vertNew2Old, _pImpl->NumVert());
                _impl->Finish();

                meshes.push_back(Manifold(_impl));
        }
        return meshes;
    }
}



////////////////////////////////////////////////////////////////////////////////////////////////////////////
// edge_op.cpp
////////////////////////////////////////////////////////////////////////////////////////////////////////////

namespace manifold
{
    ivec3 TriOf(int edge)
    {
        ivec3 triEdge;
        triEdge[0] = edge;
        triEdge[1] = NextHalfedge(triEdge[0]);
        triEdge[2] = NextHalfedge(triEdge[1]);
        return triEdge;
    }

    bool Is01Longest(vec2 v0, vec2 v1, vec2 v2)
    {
        const vec2 e[3] = { v1 - v0, v2 - v1, v0 - v2 };
        double l[3];
        for (int i : {0, 1, 2}) l[i] = la::dot(e[i], e[i]);
        return l[0] > l[1] && l[0] > l[2];
    }

    struct DuplicateEdge
    {
        const Halfedge* sortedHalfedge;

        bool operator()(int edge) {
            const Halfedge& halfedge = sortedHalfedge[edge];
            const Halfedge& nextHalfedge = sortedHalfedge[edge + 1];
            return halfedge._startVert == nextHalfedge._startVert &&
                halfedge._endVert == nextHalfedge._endVert;
        }
    };

    struct ShortEdge
    {
        TArrayView<const Halfedge> halfedge;
        TArrayView<const vec3> vertPos;
        const double tolerance;

        bool operator()(int edge) const {
            if (halfedge[edge]._pairedHalfedge < 0) return false;
            // Flag short edges
            const vec3 delta =
                vertPos[halfedge[edge]._endVert] - vertPos[halfedge[edge]._startVert];
            return la::dot(delta, delta) < tolerance * tolerance;
        }
    };

    struct FlagEdge
    {
        TArrayView<const Halfedge> halfedge;
        TArrayView<const TriRef> triRef;

        bool operator()(int edge) const {
            if (halfedge[edge]._pairedHalfedge < 0) return false;
            // Flag redundant edges - those where the _startVert is surrounded by only
            // two original triangles.
            const TriRef ref0 = triRef[edge / 3];
            int current = NextHalfedge(halfedge[edge]._pairedHalfedge);
            const TriRef ref1 = triRef[current / 3];
            while (current != edge) {
                current = NextHalfedge(halfedge[current]._pairedHalfedge);
                int tri = current / 3;
                const TriRef _ref = triRef[tri];
                if (!_ref.SameFace(ref0) && !_ref.SameFace(ref1)) return false;
            }
            return true;
        }
    };

    struct SwappableEdge
    {
        TArrayView<const Halfedge> halfedge;
        TArrayView<const vec3> vertPos;
        TArrayView<const vec3> triNormal;
        const double tolerance;

        bool operator()(int edge) const {
            if (halfedge[edge]._pairedHalfedge < 0) return false;

            int tri = edge / 3;
            ivec3 triEdge = TriOf(edge);
            mat2x3 projection = GetAxisAlignedProjection(triNormal[tri]);
            vec2 v[3];
            for (int i : {0, 1, 2})
                v[i] = projection * vertPos[halfedge[triEdge[i]]._startVert];
            if (isCCW(v[0], v[1], v[2], tolerance) > 0 || !Is01Longest(v[0], v[1], v[2]))
                return false;

            // Switch to neighbor's projection.
            edge = halfedge[edge]._pairedHalfedge;
            tri = edge / 3;
            triEdge = TriOf(edge);
            projection = GetAxisAlignedProjection(triNormal[tri]);
            for (int i : {0, 1, 2})
                v[i] = projection * vertPos[halfedge[triEdge[i]]._startVert];
            return isCCW(v[0], v[1], v[2], tolerance) > 0 ||
                Is01Longest(v[0], v[1], v[2]);
        }
    };

    struct SortEntry {
        int start;
        int end;
        size_t index;
        inline bool operator<(const SortEntry& other) const {
            return start == other.start ? end < other.end : start < other.start;
        }
    };




    /**
    * Duplicates just enough verts to covert an even-manifold to a proper
    * 2-manifold, splitting non-manifold verts and edges with too many triangles.
    */
    void Manifold::Impl::CleanupTopology()
    {
        if (!_halfedge.size()) return;

        // In the case of a very bad triangulation, it is possible to create pinched
        // verts. They must be removed before edge collapse.
        SplitPinchedVerts();

        while (1)
        {
            ZoneScopedN("DedupeEdge");

            const size_t nbEdges = _halfedge.size();
            size_t numFlagged = 0;

            TArray<SortEntry> entries;
            entries.reserve(nbEdges / 2);
            for (size_t i = 0; i < nbEdges; ++i) {
                if (_halfedge[i].isForward()) {
                    entries.push_back({ _halfedge[i]._startVert, _halfedge[i]._endVert, i });
                }
            }

            stable_sort(entries.begin(), entries.end());
            for (size_t i = 0; i < entries.size() - 1; ++i)
            {
                const int h0 = (int)entries[i].index;
                const int h1 = (int)entries[i + 1].index;
                if (_halfedge[h0]._startVert == _halfedge[h1]._startVert &&
                    _halfedge[h0]._endVert == _halfedge[h1]._endVert)
                {
                    DedupeEdge((int)entries[i].index);
                    numFlagged++;
                }
            }

            if (numFlagged == 0) break;

        }
    }

    /**
    * Collapses degenerate triangles by removing edges shorter than _tolerance and
    * any edge that is preceeded by an edge that joins the same two face relations.
    * It also performs edge swaps on the long edges of degenerate triangles, though
    * there are some configurations of degenerates that cannot be removed this way.
    *
    * Before collapsing edges, the mesh is checked for duplicate edges (more than
    * one pair of triangles sharing the same edge), which are removed by
    * duplicating one vert and adding two triangles. These degenerate triangles are
    * likely to be collapsed again in the subsequent simplification.
    *
    * Note when an edge collapse would result in something non-manifold, the
    * _vertices are duplicated in such a way as to remove handles or separate
    * meshes, thus decreasing the Genus(). It only increases when meshes that have
    * collapsed to just a pair of triangles are removed entirely.
    *
    * Rather than actually removing the edges, this step merely marks them for
    * removal, by setting vertPos to NaN and halfedge to {-1, -1, -1, -1}.
    */
    void Manifold::Impl::SimplifyTopology()
    {
        if (!_halfedge.size()) return;

        CleanupTopology();

        if (!ManifoldParams().cleanupTriangles)
        {
            return;
        }

        const size_t nbEdges = _halfedge.size();
        auto policy = autoPolicy(nbEdges, (size_t)1e5);
        size_t numFlagged = 0;
        TArray<uint8_t> bFlags(nbEdges);

        std::vector<int> scratchBuffer;
        scratchBuffer.reserve(10);
        {
            ZoneScopedN("CollapseShortEdge");
            numFlagged = 0;
            ShortEdge se{ _halfedge, _vertPos, _epsilon };
            for_each_n(policy, countAt(0_uz), nbEdges, [&](size_t i)
                {
                    bFlags[i] = se((int)i);
                });
            for (size_t i = 0; i < nbEdges; ++i)
            {
                if (bFlags[i])
                {
                    CollapseEdge(int(i), scratchBuffer);
                    scratchBuffer.resize(0);
                    numFlagged++;
                }
            }
        }


        {
            ZoneScopedN("CollapseFlaggedEdge");
            numFlagged = 0;
            FlagEdge se{ _halfedge, _meshRelation.triRef };
            for_each_n(policy, countAt(0_uz), nbEdges, [&](size_t i)
                {
                    bFlags[i] = se((int)i);
                });
            for (size_t i = 0; i < nbEdges; ++i)
            {
                if (bFlags[i])
                {
                    CollapseEdge(int(i), scratchBuffer);
                    scratchBuffer.resize(0);
                    numFlagged++;
                }
            }
        }


        {
            ZoneScopedN("RecursiveEdgeSwap");
            numFlagged = 0;
            SwappableEdge se{ _halfedge, _vertPos, _faceNormal, _tolerance };
            for_each_n(policy, countAt(0_uz), nbEdges, [&](size_t i)
                {
                    bFlags[i] = se((int)i);
                });
            std::vector<int> edgeSwapStack;
            std::vector<int> visited(_halfedge.size(), -1);
            int tag = 0;
            for (size_t i = 0; i < nbEdges; ++i)
            {
                if (bFlags[i])
                {
                    numFlagged++;
                    tag++;
                    RecursiveEdgeSwap((int)i, tag, visited, edgeSwapStack, scratchBuffer);
                    while (!edgeSwapStack.empty())
                    {
                        int last = edgeSwapStack.back();
                        edgeSwapStack.pop_back();
                        RecursiveEdgeSwap(last, tag, visited, edgeSwapStack, scratchBuffer);
                    }
                }
            }
        }

    }

    // Deduplicate the given 4-manifold edge by duplicating _endVert, thus making the
    // edges distinct. Also duplicates _startVert if it becomes pinched.
    void Manifold::Impl::DedupeEdge(const int edge)
    {
        // Orbit _endVert
        const int _startVert = _halfedge[edge]._startVert;
        const int _endVert = _halfedge[edge]._endVert;
        int current = _halfedge[NextHalfedge(edge)]._pairedHalfedge;
        while (current != edge) {
            const int vert = _halfedge[current]._startVert;
            if (vert == _startVert) {
                // Single topological unit needs 2 _faces added to be split
                const int newVert = (int)_vertPos.size();
                _vertPos.push_back(_vertPos[_endVert]);
                if (_vertNormal.size() > 0) _vertNormal.push_back(_vertNormal[_endVert]);
                current = _halfedge[NextHalfedge(current)]._pairedHalfedge;
                const int opposite = _halfedge[NextHalfedge(edge)]._pairedHalfedge;

                UpdateVert(newVert, current, opposite);

                int newHalfedge = (int)_halfedge.size();
                int newFace = newHalfedge / 3;
                int oldFace = current / 3;
                int outsideVert = _halfedge[current]._startVert;
                _halfedge.push_back({ _endVert, newVert, -1 });
                _halfedge.push_back({ newVert, outsideVert, -1 });
                _halfedge.push_back({ outsideVert, _endVert, -1 });
                PairUp(newHalfedge + 2, _halfedge[current]._pairedHalfedge);
                PairUp(newHalfedge + 1, current);
                if (_meshRelation.triRef.size() > 0)
                    _meshRelation.triRef.push_back(_meshRelation.triRef[oldFace]);
                if (_meshRelation.triProperties.size() > 0)
                    _meshRelation.triProperties.push_back(
                        _meshRelation.triProperties[oldFace]);
                if (_faceNormal.size() > 0) _faceNormal.push_back(_faceNormal[oldFace]);

                newHalfedge += 3;
                ++newFace;
                oldFace = opposite / 3;
                outsideVert = _halfedge[opposite]._startVert;
                _halfedge.push_back({ newVert, _endVert, -1 });
                _halfedge.push_back({ _endVert, outsideVert, -1 });
                _halfedge.push_back({ outsideVert, newVert, -1 });
                PairUp(newHalfedge + 2, _halfedge[opposite]._pairedHalfedge);
                PairUp(newHalfedge + 1, opposite);
                PairUp(newHalfedge, newHalfedge - 3);
                if (_meshRelation.triRef.size() > 0)
                    _meshRelation.triRef.push_back(_meshRelation.triRef[oldFace]);
                if (_meshRelation.triProperties.size() > 0)
                    _meshRelation.triProperties.push_back(
                        _meshRelation.triProperties[oldFace]);
                if (_faceNormal.size() > 0) _faceNormal.push_back(_faceNormal[oldFace]);

                break;
            }

            current = _halfedge[NextHalfedge(current)]._pairedHalfedge;
        }

        if (current == edge)
        {
            // Separate topological unit needs no new _faces to be split
            const int newVert = (int)_vertPos.size();
            _vertPos.push_back(_vertPos[_endVert]);
            if (_vertNormal.size() > 0) _vertNormal.push_back(_vertNormal[_endVert]);

            ForVert(NextHalfedge(current), [this, newVert](int e)
                {
                    _halfedge[e]._startVert = newVert;
            _halfedge[_halfedge[e]._pairedHalfedge]._endVert = newVert;
                });
        }

        // Orbit _startVert
        const int pair = _halfedge[edge]._pairedHalfedge;
        current = _halfedge[NextHalfedge(pair)]._pairedHalfedge;
        while (current != pair) {
            const int vert = _halfedge[current]._startVert;
            if (vert == _endVert) {
                break;  // Connected: not a pinched vert
            }
            current = _halfedge[NextHalfedge(current)]._pairedHalfedge;
        }

        if (current == pair) {
            // Split the pinched vert the previous split created.
            const int newVert = (int)_vertPos.size();
            _vertPos.push_back(_vertPos[_endVert]);
            if (_vertNormal.size() > 0)
                _vertNormal.push_back(_vertNormal[_endVert]);

            ForVert(NextHalfedge(current), [this, newVert](int e)
                {
                    _halfedge[e]._startVert = newVert;
            _halfedge[_halfedge[e]._pairedHalfedge]._endVert = newVert;
                });
        }
    }

    void Manifold::Impl::PairUp(int edge0, int edge1)
    {
        _halfedge[edge0]._pairedHalfedge = edge1;
        _halfedge[edge1]._pairedHalfedge = edge0;
    }

    // Traverses CW around startEdge._endVert from startEdge to endEdge
    // (edgeEdge._endVert must == startEdge._endVert), updating each edge to point
    // to vert instead.
    void Manifold::Impl::UpdateVert(int vert, int startEdge, int endEdge)
    {
        int current = startEdge;
        while (current != endEdge)
        {
            _halfedge[current]._endVert = vert;
            current = NextHalfedge(current);
            _halfedge[current]._startVert = vert;
            current = _halfedge[current]._pairedHalfedge;
            DEBUG_ASSERT(current != startEdge, logicErr, "infinite loop in decimator!");
        }
    }

    // In the event that the edge collapse would create a non-manifold edge,
    // instead we duplicate the two verts and attach the manifolds the other way
    // across this edge.
    void Manifold::Impl::FormLoop(int current, int end) {
        int _startVert = (int)_vertPos.size();
        _vertPos.push_back(_vertPos[_halfedge[current]._startVert]);
        int _endVert = (int)_vertPos.size();
        _vertPos.push_back(_vertPos[_halfedge[current]._endVert]);

        int oldMatch = _halfedge[current]._pairedHalfedge;
        int newMatch = _halfedge[end]._pairedHalfedge;

        UpdateVert(_startVert, oldMatch, newMatch);
        UpdateVert(_endVert, end, current);

        _halfedge[current]._pairedHalfedge = newMatch;
        _halfedge[newMatch]._pairedHalfedge = current;
        _halfedge[end]._pairedHalfedge = oldMatch;
        _halfedge[oldMatch]._pairedHalfedge = end;

        RemoveIfFolded(end);
    }

    void Manifold::Impl::CollapseTri(const ivec3& triEdge) {
        if (_halfedge[triEdge[1]]._pairedHalfedge == -1) return;
        int pair1 = _halfedge[triEdge[1]]._pairedHalfedge;
        int pair2 = _halfedge[triEdge[2]]._pairedHalfedge;
        _halfedge[pair1]._pairedHalfedge = pair2;
        _halfedge[pair2]._pairedHalfedge = pair1;
        for (int i : {0, 1, 2}) {
            _halfedge[triEdge[i]] = { -1, -1, -1 };
        }
    }

    void Manifold::Impl::RemoveIfFolded(int edge) {
        const ivec3 tri0edge = TriOf(edge);
        const ivec3 tri1edge = TriOf(_halfedge[edge]._pairedHalfedge);
        if (_halfedge[tri0edge[1]]._pairedHalfedge == -1) return;
        if (_halfedge[tri0edge[1]]._endVert == _halfedge[tri1edge[1]]._endVert) {
            if (_halfedge[tri0edge[1]]._pairedHalfedge == tri1edge[2]) {
                if (_halfedge[tri0edge[2]]._pairedHalfedge == tri1edge[1]) {
                    for (int i : {0, 1, 2})
                        _vertPos[_halfedge[tri0edge[i]]._startVert] = vec3(NAN);
                }
                else {
                    _vertPos[_halfedge[tri0edge[1]]._startVert] = vec3(NAN);
                }
            }
            else {
                if (_halfedge[tri0edge[2]]._pairedHalfedge == tri1edge[1]) {
                    _vertPos[_halfedge[tri1edge[1]]._startVert] = vec3(NAN);
                }
            }
            PairUp(_halfedge[tri0edge[1]]._pairedHalfedge,
                _halfedge[tri1edge[2]]._pairedHalfedge);
            PairUp(_halfedge[tri0edge[2]]._pairedHalfedge,
                _halfedge[tri1edge[1]]._pairedHalfedge);
            for (int i : {0, 1, 2}) {
                _halfedge[tri0edge[i]] = { -1, -1, -1 };
                _halfedge[tri1edge[i]] = { -1, -1, -1 };
            }
        }
    }

    // Collapses the given edge by removing _startVert. May split the mesh
    // topologically if the collapse would have resulted in a 4-manifold edge. Do
    // not collapse an edge if _startVert is pinched - the vert will be marked NaN,
    // but other edges may still be pointing to it.
    void Manifold::Impl::CollapseEdge(const int edge, std::vector<int>& edges) {
        TArray<TriRef>& triRef = _meshRelation.triRef;
        TArray<ivec3>& triProp = _meshRelation.triProperties;

        const Halfedge toRemove = _halfedge[edge];
        if (toRemove._pairedHalfedge < 0) return;

        const int _endVert = toRemove._endVert;
        const ivec3 tri0edge = TriOf(edge);
        const ivec3 tri1edge = TriOf(toRemove._pairedHalfedge);

        const vec3 pNew = _vertPos[_endVert];
        const vec3 pOld = _vertPos[toRemove._startVert];
        const vec3 delta = pNew - pOld;
        const bool shortEdge = la::dot(delta, delta) < _tolerance * _tolerance;

        // Orbit _endVert
        int current = _halfedge[tri0edge[1]]._pairedHalfedge;
        while (current != tri1edge[2]) {
            current = NextHalfedge(current);
            edges.push_back(current);
            current = _halfedge[current]._pairedHalfedge;
        }

        // Orbit _startVert
        int start = _halfedge[tri1edge[1]]._pairedHalfedge;
        if (!shortEdge) {
            current = start;
            TriRef refCheck = triRef[toRemove._pairedHalfedge / 3];
            vec3 pLast = _vertPos[_halfedge[tri1edge[1]]._endVert];
            while (current != tri0edge[2]) {
                current = NextHalfedge(current);
                vec3 pNext = _vertPos[_halfedge[current]._endVert];
                const int tri = current / 3;
                const TriRef _ref = triRef[tri];
                const mat2x3 projection = GetAxisAlignedProjection(_faceNormal[tri]);
                // Don't collapse if the edge is not redundant (this may have changed due
                // to the collapse of neighbors).
                if (!_ref.SameFace(refCheck)) {
                    refCheck = triRef[edge / 3];
                    if (!_ref.SameFace(refCheck)) {
                        return;
                    }
                    else {
                        // Don't collapse if the edges separating the _faces are not colinear
                        // (can happen when the two _faces are coplanar).
                        if (isCCW(projection * pOld, projection * pLast, projection * pNew,
                            _epsilon) != 0)
                            return;
                    }
                }

                // Don't collapse edge if it would cause a triangle to invert.
                if (isCCW(projection * pNext, projection * pLast, projection * pNew,
                    _epsilon) < 0)
                    return;

                pLast = pNext;
                current = _halfedge[current]._pairedHalfedge;
            }
        }

        // Remove toRemove._startVert and replace with _endVert.
        _vertPos[toRemove._startVert] = vec3(NAN);
        CollapseTri(tri1edge);

        // Orbit _startVert
        const int tri0 = edge / 3;
        const int tri1 = toRemove._pairedHalfedge / 3;
        const int triVert0 = (edge + 1) % 3;
        const int triVert1 = toRemove._pairedHalfedge % 3;
        current = start;
        while (current != tri0edge[2]) {
            current = NextHalfedge(current);

            if (triProp.size() > 0) {
                // Update the shifted triangles to the vertBary of _endVert
                const int tri = current / 3;
                const int vIdx = current - 3 * tri;
                if (triRef[tri].SameFace(triRef[tri0])) {
                    triProp[tri][vIdx] = triProp[tri0][triVert0];
                }
                else if (triRef[tri].SameFace(triRef[tri1])) {
                    triProp[tri][vIdx] = triProp[tri1][triVert1];
                }
            }

            const int vert = _halfedge[current]._endVert;
            const int next = _halfedge[current]._pairedHalfedge;
            for (size_t i = 0; i < edges.size(); ++i) {
                if (vert == _halfedge[edges[i]]._endVert) {
                    FormLoop(edges[i], current);
                    start = next;
                    edges.resize(i);
                    break;
                }
            }
            current = next;
        }

        UpdateVert(_endVert, start, tri0edge[2]);
        CollapseTri(tri0edge);
        RemoveIfFolded(start);
    }

    void Manifold::Impl::RecursiveEdgeSwap(const int edge, int& tag,
        std::vector<int>& visited,
        std::vector<int>& edgeSwapStack,
        std::vector<int>& edges)
    {
        TArray<TriRef>& triRef = _meshRelation.triRef;

        if (edge < 0) return;
        const int pair = _halfedge[edge]._pairedHalfedge;
        if (pair < 0) return;

        // avoid infinite recursion
        if (visited[edge] == tag && visited[pair] == tag) return;

        const ivec3 tri0edge = TriOf(edge);
        const ivec3 tri1edge = TriOf(pair);
        const ivec3 perm0 = TriOf(edge % 3);
        const ivec3 perm1 = TriOf(pair % 3);

        mat2x3 projection = GetAxisAlignedProjection(_faceNormal[edge / 3]);
        vec2 v[4];
        for (int i : {0, 1, 2})
            v[i] = projection * _vertPos[_halfedge[tri0edge[i]]._startVert];
        // Only operate on the long edge of a degenerate triangle.
        if (isCCW(v[0], v[1], v[2], _tolerance) > 0 || !Is01Longest(v[0], v[1], v[2]))
            return;

        // Switch to neighbor's projection.
        projection = GetAxisAlignedProjection(_faceNormal[pair / 3]);
        for (int i : {0, 1, 2})
            v[i] = projection * _vertPos[_halfedge[tri0edge[i]]._startVert];
        v[3] = projection * _vertPos[_halfedge[tri1edge[2]]._startVert];

        auto SwapEdge = [&]() {
            // The 0-verts are swapped to the opposite 2-verts.
            const int v0 = _halfedge[tri0edge[2]]._startVert;
            const int v1 = _halfedge[tri1edge[2]]._startVert;
            _halfedge[tri0edge[0]]._startVert = v1;
            _halfedge[tri0edge[2]]._endVert = v1;
            _halfedge[tri1edge[0]]._startVert = v0;
            _halfedge[tri1edge[2]]._endVert = v0;
            PairUp(tri0edge[0], _halfedge[tri1edge[2]]._pairedHalfedge);
            PairUp(tri1edge[0], _halfedge[tri0edge[2]]._pairedHalfedge);
            PairUp(tri0edge[2], tri1edge[2]);
            // Both triangles are now subsets of the neighboring triangle.
            const int tri0 = tri0edge[0] / 3;
            const int tri1 = tri1edge[0] / 3;
            _faceNormal[tri0] = _faceNormal[tri1];
            triRef[tri0] = triRef[tri1];
            const double l01 = la::length(v[1] - v[0]);
            const double l02 = la::length(v[2] - v[0]);
            const double a = std::max(0.0, std::min(1.0, l02 / l01));
            // Update properties if applicable
            if (_meshRelation.properties.size() > 0) {
                TArray<ivec3>& triProp = _meshRelation.triProperties;
                TArray<double>& prop = _meshRelation.properties;
                triProp[tri0] = triProp[tri1];
                triProp[tri0][perm0[1]] = triProp[tri1][perm1[0]];
                triProp[tri0][perm0[0]] = triProp[tri1][perm1[2]];
                const int numProp = (int)NumProp();
                const int newProp = (int)prop.size() / numProp;
                const int propIdx0 = triProp[tri1][perm1[0]];
                const int propIdx1 = triProp[tri1][perm1[1]];
                for (int p = 0; p < numProp; ++p)
                {
                    prop.push_back(a * prop[numProp * propIdx0 + p] + (1 - a) * prop[numProp * propIdx1 + p]);
                }
                triProp[tri1][perm1[0]] = newProp;
                triProp[tri0][perm0[2]] = newProp;
            }

            // if the new edge already exists, duplicate the verts and split the mesh.
            int current = _halfedge[tri1edge[0]]._pairedHalfedge;
            const int _endVert = _halfedge[tri1edge[1]]._endVert;
            while (current != tri0edge[1]) {
                current = NextHalfedge(current);
                if (_halfedge[current]._endVert == _endVert) {
                    FormLoop(tri0edge[2], current);
                    RemoveIfFolded(tri0edge[2]);
                    return;
                }
                current = _halfedge[current]._pairedHalfedge;
            }
        };

        // Only operate if the other triangles are not degenerate.
        if (isCCW(v[1], v[0], v[3], _tolerance) <= 0) {
            if (!Is01Longest(v[1], v[0], v[3])) return;
            // Two facing, long-edge degenerates can swap.
            SwapEdge();
            const vec2 e23 = v[3] - v[2];
            if (la::dot(e23, e23) < _tolerance * _tolerance) {
                tag++;
                CollapseEdge(tri0edge[2], edges);
                edges.resize(0);
            }
            else {
                visited[edge] = tag;
                visited[pair] = tag;
                edgeSwapStack.insert(edgeSwapStack.end(), { tri1edge[1], tri1edge[0],
                    tri0edge[1], tri0edge[0] });
            }
            return;
        }
        else if (isCCW(v[0], v[3], v[2], _tolerance) <= 0 ||
            isCCW(v[1], v[2], v[3], _tolerance) <= 0) {
            return;
        }
        // Normal path
        SwapEdge();
        visited[edge] = tag;
        visited[pair] = tag;
        edgeSwapStack.insert(edgeSwapStack.end(),
            { _halfedge[tri1edge[0]]._pairedHalfedge,
            _halfedge[tri0edge[1]]._pairedHalfedge });
    }

    void Manifold::Impl::SplitPinchedVerts()
    {
        ZoneScoped;
        std::vector<bool> vertProcessed(NumVert(), false);
        std::vector<bool> halfedgeProcessed(_halfedge.size(), false);
        for (size_t i = 0; i < _halfedge.size(); ++i)
        {
            if (halfedgeProcessed[i]) continue;
            int vert = _halfedge[i]._startVert;
            if (vertProcessed[vert])
            {
                _vertPos.push_back(_vertPos[vert]);
                vert = int(NumVert() - 1);
            }
            else
            {
                vertProcessed[vert] = true;
            }
            ForVert(int(i), [this, &halfedgeProcessed, vert](int current)
                {
                    halfedgeProcessed[current] = true;
            _halfedge[current]._startVert = vert;
            _halfedge[_halfedge[current]._pairedHalfedge]._endVert = vert;
                });
        }
    }
}



/// face_op


namespace manifold
{
    using GeneralTriangulation = std::function<std::vector<ivec3>(int)>;
    using AddTriangle = std::function<void(int, ivec3, vec3, TriRef)>;
    /// <summary>
    /// 
    /// Triangulates the _faces. In this case, the _halfedge vector is not yet a set
    /// of triangles as required by this data structure, but is instead a set of
    /// general _faces with the input faceEdge vector having length of the number of
    /// _faces + 1. The values are indicies into the _halfedge vector for the first
    /// edge of each face, with the final value being the length of the _halfedge
    /// vector itself. Upon return, _halfedge has been lengthened and properly
    /// represents the mesh as a set of triangles as usual. In this process the
    /// _faceNormal values are retained, repeated as necessary.
    /// </summary>

    /// <summary>
    /// 对_faces进行三角剖分。在此情况下，_halfedge向量尚未按照此数据结构的要求被组织成一组三角形，
    /// 而是表示为一组更通用的面（_faces）。输入的faceEdge向量长度等于面的数量加一。faceEdge向量中的值
    /// 是指向_halfedge向量中每个面的第一条边的索引，而最后一个值则是_halfedge向量的长度。
    /// 函数返回时，_halfedge向量会被扩展并正确地重新组织，以表示一个由三角形组成的网格。
    /// 在此过程中，_faceNormal（面的法向量）值会被保留，并在必要时重复，以适应三角剖分后的结构。
    /// </summary>
    void Manifold::Impl::Face2Tri(const Ints& faceEdge, const TriRefs& halfedgeRef)
    {
        ZoneScoped;
        TArray<ivec3> triVerts;
        TArray<vec3> triNormal;
        TriRefs& triRef = _meshRelation.triRef;
        triRef.resize(0);
        auto processFace = [&](GeneralTriangulation general, AddTriangle addTri,
            int face) {
                const int firstEdge = faceEdge[face];
                const int lastEdge = faceEdge[face + 1];
                const int numEdge = lastEdge - firstEdge;
                DEBUG_ASSERT(numEdge >= 3, topologyErr, "face has less than three edges.");
                const vec3 normal = _faceNormal[face];

                if (numEdge == 3)
                {  // Single triangle
                    int mapping[3] = { _halfedge[firstEdge]._startVert,
                        _halfedge[firstEdge + 1]._startVert,
                        _halfedge[firstEdge + 2]._startVert };
                    ivec3 tri(_halfedge[firstEdge]._startVert,
                        _halfedge[firstEdge + 1]._startVert,
                        _halfedge[firstEdge + 2]._startVert);
                    ivec3 ends(_halfedge[firstEdge]._endVert, _halfedge[firstEdge + 1]._endVert,
                        _halfedge[firstEdge + 2]._endVert);
                    if (ends[0] == tri[2]) {
                        std::swap(tri[1], tri[2]);
                        std::swap(ends[1], ends[2]);
                    }
                    DEBUG_ASSERT(ends[0] == tri[1] && ends[1] == tri[2] && ends[2] == tri[0], topologyErr, "These 3 edges do not form a triangle!");

                    addTri(face, tri, normal, halfedgeRef[firstEdge]);
                }
                else if (numEdge == 4)
                {
                    // Pair of triangles
                    int mapping[4] = { _halfedge[firstEdge]._startVert,
                        _halfedge[firstEdge + 1]._startVert,
                        _halfedge[firstEdge + 2]._startVert,
                        _halfedge[firstEdge + 3]._startVert };
                    const mat2x3 projection = GetAxisAlignedProjection(normal);
                    auto triCCW = [&projection, this](const ivec3 tri)
                    {
                        return isCCW(projection * this->_vertPos[tri[0]],
                            projection * this->_vertPos[tri[1]],
                            projection * this->_vertPos[tri[2]], _epsilon) >= 0;
                    };

                    ivec3 tri0(_halfedge[firstEdge]._startVert, _halfedge[firstEdge]._endVert, -1);
                    ivec3 tri1(-1, -1, tri0[0]);
                    for (const int i : {1, 2, 3})
                    {
                        if (_halfedge[firstEdge + i]._startVert == tri0[1])
                        {
                            tri0[2] = _halfedge[firstEdge + i]._endVert;
                            tri1[0] = tri0[2];
                        }
                        if (_halfedge[firstEdge + i]._endVert == tri0[0])
                        {
                            tri1[1] = _halfedge[firstEdge + i]._startVert;
                        }
                    }
                    DEBUG_ASSERT(la::all(la::gequal(tri0, ivec3(0))) && la::all(la::gequal(tri1, ivec3(0))), topologyErr, "non-manifold quad!");
                    bool firstValid = triCCW(tri0) && triCCW(tri1);
                    tri0[2] = tri1[1];
                    tri1[2] = tri0[1];
                    bool secondValid = triCCW(tri0) && triCCW(tri1);

                    if (!secondValid)
                    {
                        tri0[2] = tri1[0];
                        tri1[2] = tri0[0];
                    }
                    else if (firstValid)
                    {
                        vec3 firstCross = _vertPos[tri0[0]] - _vertPos[tri1[0]];
                        vec3 secondCross = _vertPos[tri0[1]] - _vertPos[tri1[1]];
                        if (la::dot(firstCross, firstCross) < la::dot(secondCross, secondCross))
                        {
                            tri0[2] = tri1[0];
                            tri1[2] = tri0[0];
                        }
                    }

                    for (const auto& tri : { tri0, tri1 })
                    {
                        addTri(face, tri, normal, halfedgeRef[firstEdge]);
                    }
                }
                else
                {
                    // General triangulation
                    for (const auto& tri : general(face))
                    {
                        addTri(face, tri, normal, halfedgeRef[firstEdge]);
                    }
                }
        };
        auto generalTriangulation = [&](int face)
        {
            const vec3          normal = _faceNormal[face];
            const mat2x3        projection = GetAxisAlignedProjection(normal);

            const PolygonsIdx   polys = Face2Polygons(_halfedge.cbegin() + faceEdge[face]
                , _halfedge.cbegin() + faceEdge[face + 1]
                , projection);
            return TriangulateIdx(polys, _epsilon);
        };

        triVerts.reserve(faceEdge.size());
        triNormal.reserve(faceEdge.size());
        triRef.reserve(faceEdge.size());
        auto processFace2 = std::bind(processFace, generalTriangulation,
            [&](size_t, ivec3 tri, vec3 normal, TriRef r)
            {
                triVerts.push_back(tri);
        triNormal.push_back(normal);
        triRef.push_back(r);
            },
            std::placeholders::_1);
        for (size_t face = 0; face < faceEdge.size() - 1; ++face)
        {
            processFace2(face);
        }

        _faceNormal = std::move(triNormal);
        CreateHalfedges(triVerts);
    }

    /// <summary>
    /// Returns a set of 2D polygons formed by the input projection of the _vertices
    /// of the list of Halfedges, which must be an even-manifold, meaning each vert
    /// must be referenced the same number of times as a _startVert and _endVert.
    /// </summary>

    /// <summary>
    /// 根据Halfedges列表中顶点的输入投影，返回一组由这些顶点形成的2D多边形。
    /// Halfedges列表必须是一个偶流形（even-manifold），这意味着每个顶点必须作为_startVert（起始顶点）
    /// 和_endVert（结束顶点）被引用相同次数。
    /// </summary>
    PolygonsIdx Manifold::Impl::Face2Polygons(TArrayView<Halfedge>::IterC start, TArrayView<Halfedge>::IterC end, mat2x3 projection) const
    {
        std::multimap<int, int> vert_edge;
        for (auto edge = start; edge != end; ++edge)
        {
            vert_edge.emplace(
                std::make_pair(edge->_startVert, static_cast<int>(edge - start)));
        }

        PolygonsIdx polys;
        int startEdge = 0;
        int thisEdge = startEdge;
        while (1)
        {
            if (thisEdge == startEdge)
            {
                if (vert_edge.empty())
                    break;
                startEdge = vert_edge.begin()->second;
                thisEdge = startEdge;
                polys.push_back({});
            }
            int vert = (start + thisEdge)->_startVert;
            polys.back().push_back({ projection * _vertPos[vert], vert });
            const auto result = vert_edge.find((start + thisEdge)->_endVert);
            DEBUG_ASSERT(result != vert_edge.end(), topologyErr, "non-manifold edge");
            thisEdge = result->second;
            vert_edge.erase(result);
        }
        return polys;
    }

    Polygons Manifold::Impl::Slice(double height) const {
        Box plane = _bBox;
        plane.min.z = plane.max.z = height;
        Boxs query;
        query.push_back(plane);
        const SparseIndices collisions = _collider.Collisions<false, false>(query.cview());

        std::unordered_set<int> tris;
        for (size_t i = 0; i < collisions.size(); ++i) {
            const int tri = collisions.get(i, 1);
            double min = std::numeric_limits<double>::infinity();
            double max = -std::numeric_limits<double>::infinity();
            for (const int j : {0, 1, 2})
            {
                const double z = _vertPos[_halfedge[3 * tri + j]._startVert].z;
                min = std::min(min, z);
                max = std::max(max, z);
            }

            if (min <= height && max > height)
            {
                tris.insert(tri);
            }
        }

        Polygons polys;
        while (!tris.empty())
        {
            const int startTri = *tris.begin();
            SimplePolygon poly;

            int k = 0;
            for (const int j : {0, 1, 2})
            {
                if (_vertPos[_halfedge[3 * startTri + j]._startVert].z > height &&
                    _vertPos[_halfedge[3 * startTri + next3(j)]._startVert].z <= height)
                {
                    k = next3(j);
                    break;
                }
            }

            int tri = startTri;
            do {
                tris.erase(tris.find(tri));
                if (_vertPos[_halfedge[3 * tri + k]._endVert].z <= height)
                {
                    k = next3(k);
                }

                Halfedge up = _halfedge[3 * tri + k];
                const vec3 below = _vertPos[up._startVert];
                const vec3 above = _vertPos[up._endVert];
                const double a = (height - below.z) / (above.z - below.z);
                poly.push_back(vec2(la::lerp(below, above, a)));

                const int pair = up._pairedHalfedge;
                tri = pair / 3;
                k = next3(pair % 3);
            } while (tri != startTri);

            polys.push_back(poly);
        }

        return polys;
    }

    Polygons Manifold::Impl::Project() const {
        const mat2x3 projection = GetAxisAlignedProjection({ 0, 0, 1 });
        TArray<Halfedge> cusps(NumEdge());
        cusps.resize(
            copy_if(
                _halfedge.cbegin(), _halfedge.cend(), cusps.begin(),
                [&](Halfedge edge)
                {
                    return _faceNormal[_halfedge[edge._pairedHalfedge]._pairedHalfedge / 3].z >= 0 && _faceNormal[edge._pairedHalfedge / 3].z < 0;
                }) - cusps.begin());

        PolygonsIdx polysIndexed =
            Face2Polygons(cusps.cbegin(), cusps.cend(), projection);

        Polygons polys;
        for (const auto& poly : polysIndexed)
        {
            SimplePolygon simple;
            for (const PolyVert& polyVert : poly)
            {
                simple.push_back(polyVert.pos);
            }
            polys.push_back(simple);
        }

        return polys;
    }
}

/// <summary>
/// sdf.cpp
/// </summary>
namespace   manifold
{
    ivec3   TetTri0(int i) {
        constexpr ivec3 tetTri0[16] = { {-1, -1, -1},  //
            {0, 3, 4},     //
            {0, 1, 5},     //
            {1, 5, 3},     //
            {1, 4, 2},     //
            {1, 0, 3},     //
            {2, 5, 0},     //
            {5, 3, 2},     //
            {2, 3, 5},     //
            {0, 5, 2},     //
            {3, 0, 1},     //
            {2, 4, 1},     //
            {3, 5, 1},     //
            {5, 1, 0},     //
            {4, 3, 0},     //
            {-1, -1, -1} };
        return tetTri0[i];
    }

    ivec3   TetTri1(int i) {
        constexpr ivec3 tetTri1[16] = { {-1, -1, -1},  //
            {-1, -1, -1},  //
            {-1, -1, -1},  //
            {3, 4, 1},     //
            {-1, -1, -1},  //
            {3, 2, 1},     //
            {0, 4, 2},     //
            {-1, -1, -1},  //
            {-1, -1, -1},  //
            {2, 4, 0},     //
            {1, 2, 3},     //
            {-1, -1, -1},  //
            {1, 4, 3},     //
            {-1, -1, -1},  //
            {-1, -1, -1},  //
            {-1, -1, -1} };
        return tetTri1[i];
    }

    ivec4   Neighbor(ivec4 base, int i) {
        constexpr ivec4 neighbors[14] = { {0, 0, 0, 1},     //
            {1, 0, 0, 0},     //
            {0, 1, 0, 0},     //
            {0, 0, 1, 0},     //
            {-1, 0, 0, 1},    //
            {0, -1, 0, 1},    //
            {0, 0, -1, 1},    //
            {-1, -1, -1, 1},  //
            {-1, 0, 0, 0},    //
            {0, -1, 0, 0},    //
            {0, 0, -1, 0},    //
            {0, -1, -1, 1},   //
            {-1, 0, -1, 1},   //
            {-1, -1, 0, 1} };
        ivec4 neighborIndex = base + neighbors[i];
        if (neighborIndex.w == 2) {
            neighborIndex += 1;
            neighborIndex.w = 0;
        }
        return neighborIndex;
    }

    size_t  EncodeIndex(ivec4 gridPos, ivec3 gridPow)
    {
        return static_cast<Uint64>(gridPos.w) | static_cast<Uint64>(gridPos.z) << 1 |
            static_cast<Uint64>(gridPos.y) << (1 + gridPow.z) |
            static_cast<Uint64>(gridPos.x) << (1 + gridPow.z + gridPow.y);
    }

    ivec4   DecodeIndex(size_t idx, ivec3 gridPow)
    {
        ivec4 gridPos;
        gridPos.w = idx & 1;
        idx = idx >> 1;
        gridPos.z = idx & ((1 << gridPow.z) - 1);
        idx = idx >> gridPow.z;
        gridPos.y = idx & ((1 << gridPow.y) - 1);
        idx = idx >> gridPow.y;
        gridPos.x = idx & ((1 << gridPow.x) - 1);
        return gridPos;
    }

    vec3    Position(ivec4 gridIndex, vec3 origin, vec3 spacing)
    {
        return origin + spacing * (vec3(gridIndex) + (gridIndex.w == 1 ? 0.0 : -0.5));
    }

    vec3    Bound(vec3 pos, vec3 origin, vec3 spacing, ivec3 gridSize)
    {
        return min(max(pos, origin), origin + spacing * (vec3(gridSize) - 1));
    }

    double  BoundedSDF(ivec4 gridIndex, vec3 origin, vec3 spacing, ivec3 gridSize,
        double level, std::function<double(vec3)> sdf) {
        const ivec3 xyz(gridIndex);
        const int lowerBoundDist = minelem(xyz);
        const int upperBoundDist = minelem(gridSize - xyz);
        const int boundDist = std::min(lowerBoundDist, upperBoundDist - gridIndex.w);

        if (boundDist < 0)
        {
            return 0.0;
        }
        const double d = sdf(Position(gridIndex, origin, spacing)) - level;
        return boundDist == 0 ? std::min(d, 0.0) : d;
    }

    // Simplified ITP root finding algorithm - same worst-case performance as
    // bisection, better average performance.
    inline vec3 FindSurface(vec3 pos0, double d0, vec3 pos1, double d1, double tol, double level, std::function<double(vec3)> sdf)
    {
        if (d0 == 0) {
            return pos0;
        }
        else if (d1 == 0) {
            return pos1;
        }

        // Sole tuning parameter, k: (0, 1) - smaller value gets better median
        // performance, but also hits the worst case more often.
        const double k = 0.1;
        const double check = 2 * tol / la::length(pos0 - pos1);
        double frac = 1;
        double biFrac = 1;
        while (frac > check) {
            const double t = la::lerp(d0 / (d0 - d1), 0.5, k);
            const double r = biFrac / frac - 0.5;
            const double x = la::abs(t - 0.5) < r ? t : 0.5 - r * (t < 0.5 ? 1 : -1);

            const vec3 mid = la::lerp(pos0, pos1, x);
            const double d = sdf(mid) - level;

            if ((d > 0) == (d0 > 0)) {
                d0 = d;
                pos0 = mid;
                frac *= 1 - x;
            }
            else {
                d1 = d;
                pos1 = mid;
                frac *= x;
            }
            biFrac /= 2;
        }

        return la::lerp(pos0, pos1, d0 / (d0 - d1));
    }

    /**
    * Each GridVert is connected to 14 others, and in charge of 7 of these edges
    * (see Neighbor() above). Each edge that changes sign contributes one vert,
    * unless the GridVert is close enough to the surface, in which case it
    * contributes only a single movedVert and all crossing edgeVerts refer to that.
    */
    struct GridVert
    {
        double distance = NAN;
        int movedVert = kNone;
        int edgeVerts[7] = { kNone, kNone, kNone, kNone, kNone, kNone, kNone };

        inline bool HasMoved() const { return movedVert >= 0; }

        inline bool SameSide(double dist) const {
            return (dist > 0) == (distance > 0);
        }

        inline int Inside() const { return distance > 0 ? 1 : -1; }

        inline int NeighborInside(int i) const {
            return Inside() * (edgeVerts[i] == kNone ? 1 : -1);
        }
    };

    struct NearSurface
    {
        TArrayView<vec3> vertPos;
        TArrayView<int> vertIndex;
        HashTableD<GridVert> gridVerts;
        TArrayView<const double> voxels;
        const std::function<double(vec3)> sdf;
        const vec3 origin;
        const ivec3 gridSize;
        const ivec3 gridPow;
        const vec3 spacing;
        const double level;
        const double tol;

        inline void operator()(Uint64 index) {
            ZoneScoped;
            if (gridVerts.full()) return;

            const ivec4 gridIndex = DecodeIndex(index, gridPow);

            if (la::any(la::greater(ivec3(gridIndex), gridSize))) return;

            GridVert gridVert;
            gridVert.distance = voxels[EncodeIndex(gridIndex + kVoxelOffset, gridPow)];

            bool keep = false;
            double vMax = 0;
            int closestNeighbor = -1;
            int opposedVerts = 0;
            for (int i = 0; i < 7; ++i) {
                const double val =
                    voxels[EncodeIndex(Neighbor(gridIndex, i) + kVoxelOffset, gridPow)];
                const double valOp = voxels[EncodeIndex(
                    Neighbor(gridIndex, i + 7) + kVoxelOffset, gridPow)];

                if (!gridVert.SameSide(val)) {
                    gridVert.edgeVerts[i] = kCrossing;
                    keep = true;
                    if (!gridVert.SameSide(valOp)) {
                        ++opposedVerts;
                    }
                    // Approximate bound on vert movement.
                    if (la::abs(val) > kD * la::abs(gridVert.distance) &&
                        la::abs(val) > la::abs(vMax)) {
                        vMax = val;
                        closestNeighbor = i;
                    }
                }
                else if (!gridVert.SameSide(valOp) &&
                    la::abs(valOp) > kD * la::abs(gridVert.distance) &&
                    la::abs(valOp) > la::abs(vMax)) {
                    vMax = valOp;
                    closestNeighbor = i + 7;
                }
            }

            // This is where we collapse all the crossing edge verts into this GridVert,
            // speeding up the algorithm and avoiding poor quality triangles. Without
            // this step the result is guaranteed 2-manifold, but with this step it can
            // become an even-manifold with kissing verts. These must be removed in a
            // post-process: CleanupTopology().
            if (closestNeighbor >= 0 && opposedVerts <= kMaxOpposed) {
                const vec3 gridPos = Position(gridIndex, origin, spacing);
                const ivec4 neighborIndex = Neighbor(gridIndex, closestNeighbor);
                const vec3 pos = FindSurface(gridPos, gridVert.distance,
                    Position(neighborIndex, origin, spacing),
                    vMax, tol, level, sdf);
                // Bound the delta of each vert to ensure the tetrahedron cannot invert.
                if (la::all(la::less(la::abs(pos - gridPos), kS * spacing))) {
                    const int idx = atomicAdd(vertIndex[0], 1);
                    vertPos[idx] = Bound(pos, origin, spacing, gridSize);
                    gridVert.movedVert = idx;
                    for (int j = 0; j < 7; ++j) {
                        if (gridVert.edgeVerts[j] == kCrossing) gridVert.edgeVerts[j] = idx;
                    }
                    keep = true;
                }
            }
            else {
                for (int j = 0; j < 7; ++j) gridVert.edgeVerts[j] = kNone;
            }

            if (keep) gridVerts.insert(index, gridVert);
        }
    };

    struct ComputeVerts {
        TArrayView<vec3> vertPos;
        TArrayView<int> vertIndex;
        HashTableD<GridVert> gridVerts;
        TArrayView<const double> voxels;
        const std::function<double(vec3)> sdf;
        const vec3 origin;
        const ivec3 gridSize;
        const ivec3 gridPow;
        const vec3 spacing;
        const double level;
        const double tol;

        void operator()(int idx0)
        {
            ZoneScoped;
            Uint64 baseKey = gridVerts.keyAt(idx0);
            if (baseKey == kOpen)
                return;

            GridVert& gridVert = gridVerts.at(idx0);

            if (gridVert.HasMoved()) return;

            const ivec4 gridIndex = DecodeIndex(baseKey, gridPow);

            const vec3 position = Position(gridIndex, origin, spacing);

            // These seven edges are uniquely owned by this gridVert; any of them
            // which intersect the surface create a vert.
            for (int i = 0; i < 7; ++i)
            {
                const ivec4       neighborIndex = Neighbor(gridIndex, i);
                const GridVert& neighbor = gridVerts[EncodeIndex(neighborIndex, gridPow)];

                const double val = std::isfinite(neighbor.distance) ? neighbor.distance : voxels[EncodeIndex(neighborIndex + kVoxelOffset, gridPow)];
                if (gridVert.SameSide(val))
                    continue;

                if (neighbor.HasMoved())
                {
                    gridVert.edgeVerts[i] = neighbor.movedVert;
                    continue;
                }

                const int     idx = atomicAdd(vertIndex[0], 1);
                const vec3    pos = FindSurface(position
                    , gridVert.distance
                    , Position(neighborIndex, origin, spacing)
                    , val
                    , tol
                    , level
                    , sdf);
                vertPos[idx] = Bound(pos, origin, spacing, gridSize);
                gridVert.edgeVerts[i] = idx;
            }
        }
    };

    struct BuildTris
    {
        TArrayView<ivec3> triVerts;
        TArrayView<int> triIndex;
        const HashTableD<GridVert> gridVerts;
        const ivec3 gridPow;

        void CreateTri(const ivec3& tri, const int edges[6]) {
            if (tri[0] < 0) return;
            const ivec3 verts(edges[tri[0]], edges[tri[1]], edges[tri[2]]);
            if (verts[0] == verts[1] || verts[1] == verts[2] || verts[2] == verts[0])
                return;
            int idx = atomicAdd(triIndex[0], 1);
            triVerts[idx] = verts;
        }

        void CreateTris(const ivec4& tet, const int edges[6]) {
            const int i = (tet[0] > 0 ? 1 : 0) + (tet[1] > 0 ? 2 : 0) +
                (tet[2] > 0 ? 4 : 0) + (tet[3] > 0 ? 8 : 0);
            CreateTri(TetTri0(i), edges);
            CreateTri(TetTri1(i), edges);
        }

        void operator()(int idx) {
            ZoneScoped;
            Uint64 baseKey = gridVerts.keyAt(idx);
            if (baseKey == kOpen) return;

            const GridVert& base = gridVerts.at(idx);
            const ivec4 baseIndex = DecodeIndex(baseKey, gridPow);

            ivec4 leadIndex = baseIndex;
            if (leadIndex.w == 0)
                leadIndex.w = 1;
            else {
                leadIndex += 1;
                leadIndex.w = 0;
            }

            // This GridVert is in charge of the 6 tetrahedra surrounding its edge in
            // the (1,1,1) direction (edge 0).
            ivec4 tet(base.NeighborInside(0), base.Inside(), -2, -2);
            ivec4 thisIndex = baseIndex;
            thisIndex.x += 1;

            GridVert thisVert = gridVerts[EncodeIndex(thisIndex, gridPow)];

            tet[2] = base.NeighborInside(1);
            for (const int i : {0, 1, 2}) {
                thisIndex = leadIndex;
                --thisIndex[prev3(i)];
                // Indices take unsigned input, so check for negatives, given the
                // decrement. If negative, the vert is outside and only connected to other
                // outside verts - no edgeVerts.
                GridVert nextVert = thisIndex[prev3(i)] < 0
                    ? GridVert()
                    : gridVerts[EncodeIndex(thisIndex, gridPow)];
                tet[3] = base.NeighborInside(prev3(i) + 4);

                const int edges1[6] = { base.edgeVerts[0],
                    base.edgeVerts[i + 1],
                    nextVert.edgeVerts[next3(i) + 4],
                    nextVert.edgeVerts[prev3(i) + 1],
                    thisVert.edgeVerts[i + 4],
                    base.edgeVerts[prev3(i) + 4] };
                thisVert = nextVert;
                CreateTris(tet, edges1);

                thisIndex = baseIndex;
                ++thisIndex[next3(i)];
                nextVert = gridVerts[EncodeIndex(thisIndex, gridPow)];
                tet[2] = tet[3];
                tet[3] = base.NeighborInside(next3(i) + 1);

                const int edges2[6] = { base.edgeVerts[0],
                    edges1[5],
                    thisVert.edgeVerts[i + 4],
                    nextVert.edgeVerts[next3(i) + 4],
                    edges1[3],
                    base.edgeVerts[next3(i) + 1] };
                thisVert = nextVert;
                CreateTris(tet, edges2);

                tet[2] = tet[3];
            }
        }
    };



    Manifold Manifold::LevelSet(std::function<double(vec3)> sdf, Box bounds,
        double edgeLength, double level, double tolerance,
        bool canParallel) {
        if (tolerance <= 0) {
            tolerance = std::numeric_limits<double>::infinity();
        }

        auto _pImpl = std::make_shared<Impl>();
        auto& vertPos = _pImpl->_vertPos;

        const vec3 dim = bounds.Size();
        const ivec3 gridSize(dim / edgeLength + 1.0);
        const vec3 spacing = dim / (vec3(gridSize - 1));

        const ivec3 gridPow(la::log2(gridSize + 2) + 1);
        const Uint64 maxIndex = EncodeIndex(ivec4(gridSize + 2, 1), gridPow);

        // Parallel policies violate will crash language runtimes with runtime locks
        // that expect to not be called back by unregistered threads. This allows
        // bindings use LevelSet despite being compiled with MANIFOLD_PAR
        // active.
        const auto pol = canParallel ? autoPolicy(maxIndex) : ExecutionPolicy::Seq;

        const vec3 origin = bounds.min;
        TArray<double> voxels(maxIndex);
        for_each_n(
            pol, countAt(0_uz), maxIndex,
            [&voxels, sdf, level, origin, spacing, gridSize, gridPow](size_t idx) {
                voxels[idx] = BoundedSDF(DecodeIndex(idx, gridPow) - kVoxelOffset,
                origin, spacing, gridSize, level, sdf);
            });

        size_t tableSize = std::min(
            2 * maxIndex, static_cast<Uint64>(10 * la::pow(maxIndex, 0.667)));
        HashTable<GridVert> gridVerts(tableSize);
        vertPos.resize(gridVerts.size() * 7);

        while (1)
        {
            TArray<int> index(1, 0);
            for_each_n(pol, countAt(0_uz), EncodeIndex(ivec4(gridSize, 1), gridPow),
                NearSurface({ vertPos, index, gridVerts.D(), voxels, sdf, origin,
                    gridSize, gridPow, spacing, level, tolerance }));

            if (gridVerts.full())
            {  // Resize HashTable
                const vec3 lastVert = vertPos[index[0] - 1];
                const Uint64 lastIndex =
                    EncodeIndex(ivec4(ivec3((lastVert - origin) / spacing), 1), gridPow);
                const double ratio = static_cast<double>(maxIndex) / lastIndex;

                if (ratio > 1000)  // do not trust the ratio if it is too large
                    tableSize *= 2;
                else
                    tableSize *= (size_t)ratio;
                gridVerts = HashTable<GridVert>(tableSize);
                vertPos = TArray<vec3>(gridVerts.size() * 7);
            }
            else
            {  // Success
                for_each_n(
                    pol, countAt(0), gridVerts.size(),
                    ComputeVerts({ vertPos, index, gridVerts.D(), voxels, sdf, origin,
                        gridSize, gridPow, spacing, level, tolerance }));
                vertPos.resize(index[0]);
                break;
            }
        }

        TArray<ivec3> triVerts(gridVerts.entries() * 12);  // worst case

        TArray<int> index(1, 0);
        for_each_n(pol, countAt(0), gridVerts.size(),
            BuildTris({ triVerts, index, gridVerts.D(), gridPow }));
        triVerts.resize(index[0]);

        _pImpl->CreateHalfedges(triVerts);
        _pImpl->CleanupTopology();
        _pImpl->RemoveUnreferencedVerts();
        _pImpl->Finish();
        _pImpl->InitializeOriginal();
        return Manifold(_pImpl);
    }
}

/// <summary>
///  polygon.cpp
/// </summary>

namespace   manifold
{
    static ExecutionParams  params;

    constexpr double kBest = -std::numeric_limits<double>::infinity();

    // it seems that MSVC cannot optimize la::determinant(mat2(a, b))
    constexpr double determinant2x2(vec2 a, vec2 b) {
        return a.x * b.y - a.y * b.x;
    }


    /// <summary>
    /// Tests if the input polygons are convex by searching for any reflex _vertices.
    /// Exactly colinear edges and zero-length edges are treated conservatively as
    /// reflex. Does not check for overlaps.
    /// </summary>

    /// <summary>
    /// 测试输入的多边形是否为凸多边形。
    /// </summary>
    /// <remarks>
    /// 通过查找任何反射顶点来判断。将完全共线的边和零长度的边保守地视为反射边。
    /// 不检查多边形是否有重叠。
    /// </remarks>
    /// <returns>如果输入的多边形是凸多边形，则返回true；否则返回false。</returns>
    /// 
    bool IsConvex(const PolygonsIdx& polys, double epsilon)
    {
        for (const SimplePolygonIdx& poly : polys)
        {
            const vec2 firstEdge = poly[0].pos - poly[poly.size() - 1].pos;
            // Zero-length edges comes out NaN, which won't trip the early return, but
            // it's okay because that zero-length edge will also get tested
            // non-normalized and will trip det == 0.
            vec2 lastEdge = la::normalize(firstEdge);
            for (size_t v = 0; v < poly.size(); ++v)
            {
                vec2    edge = v + 1 < poly.size() ? poly[v + 1].pos - poly[v].pos : firstEdge;
                double  det = determinant2x2(lastEdge, edge);
                if (det <= 0 || (std::abs(det) < epsilon && la::dot(lastEdge, edge) < 0))
                    return false;
                lastEdge = la::normalize(edge);
            }
        }
        return true;
    }

    /**
    * Triangulates a set of convex polygons by alternating instead of a fan, to
    * avoid creating high-degree _vertices.
    */
    std::vector<ivec3> TriangulateConvex(const PolygonsIdx& polys)
    {
        size_t numTri = manifold::transform_reduce(polys.begin()
            , polys.end()
            , 0_uz
            , [](size_t a, size_t b) { return a + b; }
        , [](const SimplePolygonIdx& poly) { return poly.size() - 2; }
        );
        std::vector<ivec3> triangles;
        triangles.reserve(numTri);
        for (const SimplePolygonIdx& poly : polys)
        {
            size_t  i = 0;
            size_t  k = poly.size() - 1;
            bool    right = true;
            while (i + 1 < k)
            {
                const size_t j = right ? i + 1 : k - 1;
                triangles.push_back({ poly[i].idx, poly[j].idx, poly[k].idx });
                if (right)
                {
                    i = j;
                }
                else
                {
                    k = j;
                }
                right = !right;
            }
        }
        return triangles;
    }

    /// <summary>
    /// Ear-clipping triangulator based on David Eberly's approach from Geometric
    /// Tools, but adjusted to handle epsilon-valid polygons, and including a
    /// fallback that ensures a manifold triangulation even for overlapping polygons.
    /// This is an O(n^2) algorithm, but hopefully this is not a big problem as the
    /// number of edges in a given polygon is generally much less than the number of
    /// triangles in a mesh, and relatively few _faces even need triangulation.
    /// 
    /// The main adjustments for robustness involve clipping the sharpest ears first
    /// (a known technique to get higher triangle quality), and doing an exhaustive
    /// search to determine ear convexity exactly if the first geometric result is
    /// within epsilon.
    /// </summary>
    /// <summary>
    /// 基于David Eberly在《Geometric Tools》中提出的耳切法（Ear-clipping）三角剖分算法，
    /// 但经过调整，能够处理ε-有效多边形（epsilon-valid polygons），并包含一个回退机制，
    /// 确保即使对于重叠多边形也能生成流形（manifold）三角剖分。
    /// 此算法的时间复杂度为O(n^2)，但由于给定多边形的边数通常远少于网格中的三角形数量，
    /// 且需要三角剖分的面相对较少，因此这通常不是一个大问题。
    /// 
    /// 为了提高算法的稳健性，主要做了以下调整：
    /// - 首先裁剪最尖锐的耳朵（这是一种提高三角形质量的已知技术）。
    /// - 如果初步的几何判断结果在ε范围内，则进行穷举搜索，以精确确定耳朵的凸性。
    /// </summary>
    class EarClip
    {
    public:
        EarClip(const PolygonsIdx& polys, double epsilon)
            :_epsilon(epsilon)
        {
            ZoneScoped;
            size_t numVert = 0;
            for (const SimplePolygonIdx& poly : polys)
            {
                numVert += poly.size();
            }
            _polygon.reserve(numVert + 2 * polys.size());

            std::vector<VertItr> starts = Initialize(polys);

            for (VertItr v = _polygon.begin(); v != _polygon.end(); ++v)
            {
                ClipIfDegenerate(v);
            }

            for (const VertItr first : starts)
            {
                FindStart(first);
            }
        }

        std::vector<ivec3> Triangulate()
        {
            ZoneScoped;
            for (const VertItr start : _holes)
            {
                CutKeyhole(start);
            }

            for (const VertItr start : _simples)
            {
                TriangulatePoly(start);
            }
            return triangles_;
        }

        double GetPrecision() const { return _epsilon; }

    private:
        struct    Vert;
        typedef   std::vector<Vert>::iterator VertItr;
        typedef   std::vector<Vert>::const_iterator VertItrC;
        struct  MaxX
        {
            bool operator()(const VertItr& a, const VertItr& b) const
            {
                return a->pos.x > b->pos.x;
            }
        };
        struct  MinCost
        {
            bool operator()(const VertItr& a, const VertItr& b) const
            {
                return a->cost < b->cost;
            }
        };
        typedef std::set<VertItr, MinCost>::iterator qItr;

        // The flat list where all the Verts are stored. Not used much for traversal.
        std::vector<Vert>               _polygon;
        // The set of right-most starting points, one for each negative-area contour.
        std::multiset<VertItr, MaxX>    _holes;
        // The set of starting points, one for each positive-area contour.
        std::vector<VertItr>            _outers;
        // The set of starting points, one for each simple polygon.
        std::vector<VertItr>            _simples;
        // Maps each hole (by way of starting point) to its bounding box.
        std::map<VertItr, Rect>         _hole2BBox;
        // A priority queue of valid ears - the multiset allows them to be updated.
        std::multiset<VertItr, MinCost> _earsQueue;
        // The output triangulation.
        std::vector<ivec3>              triangles_;
        // Bounding box of the entire set of polygons
        Rect                            _bBox;
        // Working epsilon: max of float error and input value.
        double                          _epsilon;

        struct IdxCollider {
            Collider collider;
            std::vector<VertItr> itr;
            SparseIndices ind;
        };

        // A circularly-linked list representing the polygon(s) that still need to be
        // triangulated. This gets smaller as ears are clipped until it degenerates to
        // two points and terminates.
        struct Vert {
            int mesh_idx;
            double cost;
            qItr ear;
            vec2 pos, rightDir;
            VertItr left, right;

            // Shorter than half of epsilon, to be conservative so that it doesn't
            // cause CW triangles that exceed epsilon due to rounding error.
            bool IsShort(double epsilon) const {
                const vec2 edge = right->pos - pos;
                return la::dot(edge, edge) * 4 < epsilon * epsilon;
            }

            // Like isCCW, returns 1 if v is on the inside of the angle formed at this
            // vert, -1 on the outside, and 0 if it's within epsilon of the boundary.
            // Ensure v is more than epsilon from pos, as case this will not return 0.
            int Interior(vec2 v, double epsilon) const {
                const vec2 diff = v - pos;
                if (la::dot(diff, diff) < epsilon * epsilon) {
                    return 0;
                }
                return isCCW(pos, left->pos, right->pos, epsilon) +
                    isCCW(pos, right->pos, v, epsilon) + isCCW(pos, v, left->pos, epsilon);
            }

            // Returns true if Vert is on the inside of the edge that goes from tail to
            // tail->right. This will walk the edges if necessary until a clear answer
            // is found (beyond epsilon). If toLeft is true, this Vert will walk its
            // edges to the left. This should be chosen so that the edges walk in the
            // same general direction - tail always walks to the right.
            bool InsideEdge(VertItr tail, double epsilon, bool toLeft) const {
                const double p2 = epsilon * epsilon;
                VertItr nextL = left->right;
                VertItr nextR = tail->right;
                VertItr center = tail;
                VertItr last = center;

                while (nextL != nextR && tail != nextR &&
                    nextL != (toLeft ? right : left)) {
                    const vec2 edgeL = nextL->pos - center->pos;
                    const double l2 = la::dot(edgeL, edgeL);
                    if (l2 <= p2) {
                        nextL = toLeft ? nextL->left : nextL->right;
                        continue;
                    }

                    const vec2 edgeR = nextR->pos - center->pos;
                    const double r2 = la::dot(edgeR, edgeR);
                    if (r2 <= p2) {
                        nextR = nextR->right;
                        continue;
                    }

                    const vec2 vecLR = nextR->pos - nextL->pos;
                    const double lr2 = la::dot(vecLR, vecLR);
                    if (lr2 <= p2) {
                        last = center;
                        center = nextL;
                        nextL = toLeft ? nextL->left : nextL->right;
                        if (nextL == nextR) break;
                        nextR = nextR->right;
                        continue;
                    }

                    int convexity = isCCW(nextL->pos, center->pos, nextR->pos, epsilon);
                    if (center != last) {
                        convexity += isCCW(last->pos, center->pos, nextL->pos, epsilon) +
                            isCCW(nextR->pos, center->pos, last->pos, epsilon);
                    }
                    if (convexity != 0) return convexity > 0;

                    if (l2 < r2) {
                        center = nextL;
                        nextL = toLeft ? nextL->left : nextL->right;
                    }
                    else {
                        center = nextR;
                        nextR = nextR->right;
                    }
                    last = center;
                }
                // The whole polygon is degenerate - consider this to be convex.
                return true;
            }

            // A major key to robustness is to only clip convex ears, but this is
            // difficult to determine when an edge is folded back on itself. This
            // function walks down the kinks in a degenerate portion of a polygon until
            // it finds a clear geometric result. In the vast majority of cases the loop
            // will only need one or two iterations.
            bool IsConvex(double epsilon) const {
                const int convexity = isCCW(left->pos, pos, right->pos, epsilon);
                if (convexity != 0) {
                    return convexity > 0;
                }
                if (la::dot(left->pos - pos, right->pos - pos) <= 0) {
                    return true;
                }
                return left->InsideEdge(left->right, epsilon, true);
            }

            // Subtly different from !IsConvex because IsConvex will return true for
            // colinear non-folded verts, while IsReflex will always check until actual
            // certainty is determined.
            bool IsReflex(double epsilon) const {
                return !left->InsideEdge(left->right, epsilon, true);
            }

            // Returns the x-value on this edge corresponding to the start.y value,
            // returning NAN if the edge does not cross the value from below to above,
            // right of start - all within a epsilon tolerance. If onTop != 0, this
            // restricts which end is allowed to terminate within the epsilon band.
            double InterpY2X(vec2 start, int onTop, double epsilon) const {
                if (la::abs(pos.y - start.y) <= epsilon) {
                    if (right->pos.y <= start.y + epsilon || onTop == 1) {
                        return NAN;
                    }
                    else {
                        return pos.x;
                    }
                }
                else if (pos.y < start.y - epsilon) {
                    if (right->pos.y > start.y + epsilon) {
                        return pos.x + (start.y - pos.y) * (right->pos.x - pos.x) /
                            (right->pos.y - pos.y);
                    }
                    else if (right->pos.y < start.y - epsilon || onTop == -1) {
                        return NAN;
                    }
                    else {
                        return right->pos.x;
                    }
                }
                else {
                    return NAN;
                }
            }

            // This finds the cost of this vert relative to one of the two closed sides
            // of the ear. Points are valid even when they touch, so long as their edge
            // goes to the outside. No need to check the other side, since all verts are
            // processed in the EarCost loop.
            double SignedDist(VertItr v, vec2 unit, double epsilon) const {
                double d = determinant2x2(unit, v->pos - pos);
                if (std::abs(d) < epsilon) {
                    double dR = determinant2x2(unit, v->right->pos - pos);
                    if (std::abs(dR) > epsilon) return dR;
                    double dL = determinant2x2(unit, v->left->pos - pos);
                    if (std::abs(dL) > epsilon) return dL;
                }
                return d;
            }

            // Find the cost of Vert v within this ear, where openSide is the unit
            // vector from Verts right to left - passed in for reuse.
            double Cost(VertItr v, vec2 openSide, double epsilon) const
            {
                double cost1 = std::min(SignedDist(v, rightDir, epsilon), SignedDist(v, left->rightDir, epsilon));

                const double openCost = determinant2x2(openSide, v->pos - right->pos);
                return std::min(cost1, openCost);
            }

            // For verts outside the ear, apply a cost based on the Delaunay condition
            // to aid in prioritization and produce cleaner triangulations. This doesn't
            // affect robustness, but may be adjusted to improve output.
            static double DelaunayCost(vec2 diff, double scale, double epsilon) {
                return -epsilon - scale * la::dot(diff, diff);
            }

            // This is the expensive part of the algorithm, checking this ear against
            // every Vert to ensure none are inside. The Collider brings the total
            // triangulator cost down from O(n^2) to O(nlogn) for most large polygons.
            //
            // Think of a cost as vaguely a distance metric - 0 is right on the edge of
            // being invalid. cost > epsilon is definitely invalid. Cost < -epsilon
            // is definitely valid, so all improvement costs are designed to always give
            // values < -epsilon so they will never affect validity. The first
            // totalCost is designed to give priority to sharper angles. Any cost < (-1
            // - epsilon) has satisfied the Delaunay condition.
            double EarCost(double epsilon, IdxCollider& collider) const {
                vec2 openSide = left->pos - right->pos;
                const vec2 center = 0.5 * (left->pos + right->pos);
                const double scale = 4 / la::dot(openSide, openSide);
                const double radius = la::length(openSide) / 2;
                openSide = la::normalize(openSide);

                double totalCost = la::dot(left->rightDir, rightDir) - 1 - epsilon;
                if (isCCW(pos, left->pos, right->pos, epsilon) == 0) {
                    // Clip folded ears first
                    return totalCost;
                }

                Box earBox = Box{ vec3(center.x - radius, center.y - radius, 0),
                    vec3(center.x + radius, center.y + radius, 0) };
                earBox.Union(vec3(pos, 0));
                collider.collider.Collisions(TArrayView<const Box>(&earBox, 1),
                    collider.ind);

                const int lid = left->mesh_idx;
                const int rid = right->mesh_idx;

                totalCost = transform_reduce(
                    countAt(0), countAt(collider.ind.size()), totalCost,
                    [](double a, double b) { return std::max(a, b); },
                    [&](size_t i) {
                        const VertItr test = collider.itr[collider.ind.get(i, true)];
                if (!Clipped(test) && test->mesh_idx != mesh_idx &&
                    test->mesh_idx != lid &&
                    test->mesh_idx != rid) {  // Skip duplicated verts
                    double cost = Cost(test, openSide, epsilon);
                    if (cost < -epsilon) {
                        cost = DelaunayCost(test->pos - center, scale, epsilon);
                    }
                    return cost;
                }
                return std::numeric_limits<double>::lowest();
                    });
                collider.ind.clear();
                return totalCost;
            }

            void PrintVert() const
            {
            }
        };

        static vec2 SafeNormalize(vec2 v) {
            vec2 n = la::normalize(v);
            return std::isfinite(n.x) ? n : vec2(0, 0);
        }

        // This function and JoinPolygons are the only functions that affect the
        // circular list data structure. This helps ensure it remains circular.
        static void Link(VertItr left, VertItr right) {
            left->right = right;
            right->left = left;
            left->rightDir = SafeNormalize(right->pos - left->pos);
        }

        // When an ear vert is clipped, its neighbors get linked, so they get unlinked
        // from it, but it is still linked to them.
        static bool Clipped(VertItr v) { return v->right->left != v; }

        // Apply func to each un-clipped vert in a polygon and return an un-clipped
        // vert.
        VertItrC Loop(VertItr first, std::function<void(VertItr)> func) const {
            VertItr v = first;
            do {
                if (Clipped(v)) {
                    // Update first to an un-clipped vert so we will return to it instead
                    // of infinite-looping.
                    first = v->right->left;
                    if (!Clipped(first)) {
                        v = first;
                        if (v->right == v->left) {
                            return _polygon.end();
                        }
                        func(v);
                    }
                }
                else {
                    if (v->right == v->left) {
                        return _polygon.end();
                    }
                    func(v);
                }
                v = v->right;
            } while (v != first);
            return v;
        }

        // Remove this vert from the circular list and output a corresponding
        // triangle.
        void ClipEar(VertItrC ear) {
            Link(ear->left, ear->right);
            if (ear->left->mesh_idx != ear->mesh_idx &&
                ear->mesh_idx != ear->right->mesh_idx &&
                ear->right->mesh_idx != ear->left->mesh_idx) {
                // Filter out topological degenerates, which can form in bad
                // triangulations of polygons with holes, due to vert duplication.
                triangles_.push_back(
                    { ear->left->mesh_idx, ear->mesh_idx, ear->right->mesh_idx });
            }
            else {
                PRINT("Topological degenerate!");
            }
        }

        // If an ear will make a degenerate triangle, clip it early to avoid
        // difficulty in key-holing. This function is recursive, as the process of
        // clipping may cause the neighbors to degenerate. Reflex degenerates *must
        // not* be clipped, unless they have a short edge.
        void ClipIfDegenerate(VertItr ear) {
            if (Clipped(ear)) {
                return;
            }
            if (ear->left == ear->right) {
                return;
            }
            if (ear->IsShort(_epsilon) ||
                (isCCW(ear->left->pos, ear->pos, ear->right->pos, _epsilon) == 0 &&
                    la::dot(ear->left->pos - ear->pos, ear->right->pos - ear->pos) > 0 &&
                    ear->IsConvex(_epsilon))) {
                ClipEar(ear);
                ClipIfDegenerate(ear->left);
                ClipIfDegenerate(ear->right);
            }
        }

        // Build the circular list polygon structures.
        std::vector<VertItr> Initialize(const PolygonsIdx& polys) {
            std::vector<VertItr> starts;
            for (const SimplePolygonIdx& poly : polys) {
                auto vert = poly.begin();
                _polygon.push_back({ vert->idx, 0.0, _earsQueue.end(), vert->pos });
                const VertItr first = std::prev(_polygon.end());

                _bBox.Union(first->pos);
                VertItr last = first;
                // This is not the real rightmost start, but just an arbitrary vert for
                // now to identify each polygon.
                starts.push_back(first);

                for (++vert; vert != poly.end(); ++vert) {
                    _bBox.Union(vert->pos);

                    _polygon.push_back({ vert->idx, 0.0, _earsQueue.end(), vert->pos });
                    VertItr next = std::prev(_polygon.end());

                    Link(last, next);
                    last = next;
                }
                Link(last, first);
            }

            if (_epsilon < 0) _epsilon = _bBox.Scale() * kPrecision;

            // Slightly more than enough, since each hole can cause two extra triangles.
            triangles_.reserve(_polygon.size() + 2 * starts.size());
            return starts;
        }

        // Find the actual rightmost starts after degenerate removal. Also calculate
        // the polygon bounding boxes.
        void FindStart(VertItr first) {
            const vec2 origin = first->pos;

            VertItr start = first;
            double maxX = -std::numeric_limits<double>::infinity();
            Rect bBox;
            // Kahan summation
            double area = 0;
            double areaCompensation = 0;

            auto AddPoint = [&](VertItr v) {
                bBox.Union(v->pos);
                const double area1 =
                    determinant2x2(v->pos - origin, v->right->pos - origin);
                const double t1 = area + area1;
                areaCompensation += (area - t1) + area1;
                area = t1;

                if (v->pos.x > maxX) {
                    maxX = v->pos.x;
                    start = v;
                }
            };

            if (Loop(first, AddPoint) == _polygon.end()) {
                // No polygon left if all ears were degenerate and already clipped.
                return;
            }

            area += areaCompensation;
            const vec2 size = bBox.Size();
            const double minArea = _epsilon * std::max(size.x, size.y);

            if (std::isfinite(maxX) && area < -minArea) {
                _holes.insert(start);
                _hole2BBox.insert({ start, bBox });
            }
            else {
                _simples.push_back(start);
                if (area > minArea) {
                    _outers.push_back(start);
                }
            }
        }

        // All holes must be key-holed (attached to an outer polygon) before ear
        // clipping can commence. Instead of relying on sorting, which may be
        // incorrect due to epsilon, we check for polygon edges both ahead and
        // behind to ensure all valid options are found.
        void CutKeyhole(const VertItr start) {
            const Rect bBox = _hole2BBox[start];
            const int onTop = start->pos.y >= bBox.max.y - _epsilon ? 1
                : start->pos.y <= bBox.min.y + _epsilon ? -1
                : 0;
            VertItr connector = _polygon.end();

            auto CheckEdge = [&](VertItr edge) {
                const double x = edge->InterpY2X(start->pos, onTop, _epsilon);
                if (std::isfinite(x) && start->InsideEdge(edge, _epsilon, true) &&
                    (connector == _polygon.end() ||
                        isCCW({ x, start->pos.y }, connector->pos, connector->right->pos,
                            _epsilon) == 1 ||
                        (connector->pos.y < edge->pos.y
                            ? edge->InsideEdge(connector, _epsilon, false)
                            : !connector->InsideEdge(edge, _epsilon, false)))) {
                    connector = edge;
                }
            };

            for (const VertItr first : _outers) {
                Loop(first, CheckEdge);
            }

            if (connector == _polygon.end()) {
                PRINT("hole did not find an outer contour!");
                _simples.push_back(start);
                return;
            }

            connector = FindCloserBridge(start, connector);

            JoinPolygons(start, connector);

        }

        // This converts the initial guess for the keyhole location into the final one
        // and returns it. It does so by finding any reflex verts inside the triangle
        // containing the best connection and the initial horizontal line.
        VertItr FindCloserBridge(VertItr start, VertItr edge) {
            VertItr connector =
                edge->pos.x < start->pos.x ? edge->right
                : edge->right->pos.x < start->pos.x ? edge
                : edge->right->pos.y - start->pos.y > start->pos.y - edge->pos.y
                ? edge
                : edge->right;
            if (la::abs(connector->pos.y - start->pos.y) <= _epsilon) {
                return connector;
            }
            const double above = connector->pos.y > start->pos.y ? 1 : -1;

            auto CheckVert = [&](VertItr vert) {
                const double inside =
                    above * isCCW(start->pos, vert->pos, connector->pos, _epsilon);
                if (vert->pos.x > start->pos.x - _epsilon &&
                    vert->pos.y * above > start->pos.y * above - _epsilon &&
                    (inside > 0 || (inside == 0 && vert->pos.x < connector->pos.x)) &&
                    vert->InsideEdge(edge, _epsilon, true) && vert->IsReflex(_epsilon)) {
                    connector = vert;
                }
            };

            for (const VertItr first : _outers) {
                Loop(first, CheckVert);
            }

            return connector;
        }

        // Creates a keyhole between the start vert of a hole and the connector vert
        // of an outer polygon. To do this, both verts are duplicated and reattached.
        // This process may create degenerate ears, so these are clipped if necessary
        // to keep from confusing subsequent key-holing operations.
        void JoinPolygons(VertItr start, VertItr connector) {
            _polygon.push_back(*start);
            const VertItr newStart = std::prev(_polygon.end());
            _polygon.push_back(*connector);
            const VertItr newConnector = std::prev(_polygon.end());

            start->right->left = newStart;
            connector->left->right = newConnector;
            Link(start, connector);
            Link(newConnector, newStart);

            ClipIfDegenerate(start);
            ClipIfDegenerate(newStart);
            ClipIfDegenerate(connector);
            ClipIfDegenerate(newConnector);
        }

        // Recalculate the cost of the Vert v ear, updating it in the queue by
        // removing and reinserting it.
        void ProcessEar(VertItr v, IdxCollider& collider) {
            if (v->ear != _earsQueue.end()) {
                _earsQueue.erase(v->ear);
                v->ear = _earsQueue.end();
            }
            if (v->IsShort(_epsilon)) {
                v->cost = kBest;
                v->ear = _earsQueue.insert(v);
            }
            else if (v->IsConvex(2 * _epsilon)) {
                v->cost = v->EarCost(_epsilon, collider);
                v->ear = _earsQueue.insert(v);
            }
            else {
                v->cost = 1;  // not used, but marks reflex verts for debug
            }
        }

        // Create a collider of all _vertices in this polygon, each expanded by
        // _epsilon. Each ear uses this BVH to quickly find a subset of _vertices to
        // check for cost.
        IdxCollider VertCollider(VertItr start) const {
            Boxs vertBox;
            TArray<uint32_t> vertMorton;
            std::vector<VertItr> itr;
            const Box box(vec3(_bBox.min, 0), vec3(_bBox.max, 0));

            Loop(start, [&vertBox, &vertMorton, &itr, &box, this](VertItr v) {
                itr.push_back(v);
            const vec3 pos(v->pos, 0);
            vertBox.push_back({ pos - _epsilon, pos + _epsilon });
            vertMorton.push_back(Collider::MortonCode(pos, box));
                });

            if (itr.empty()) {
                return { Collider(), itr };
            }

            const int numVert = (int)itr.size();
            TArray<int> vertNew2Old(numVert);
            sequence(vertNew2Old.begin(), vertNew2Old.end());

            stable_sort(vertNew2Old.begin(), vertNew2Old.end(),
                [&vertMorton](const int a, const int b) {
                    return vertMorton[a] < vertMorton[b];
                });
            permute(vertMorton, vertNew2Old);
            permute(vertBox, vertNew2Old);
            permute(itr, vertNew2Old);

            return { Collider(vertBox, vertMorton), itr };
        }

        // The main ear-clipping loop. This is called once for each simple polygon -
        // all holes have already been key-holed and joined to an outer polygon.
        void TriangulatePoly(VertItr start) {
            ZoneScoped;

            IdxCollider vertCollider = VertCollider(start);

            if (vertCollider.itr.empty()) {
                PRINT("Empty poly");
                return;
            }

            // A simple polygon always creates two fewer triangles than it has verts.
            int numTri = -2;
            _earsQueue.clear();

            auto QueueVert = [&](VertItr v) {
                ProcessEar(v, vertCollider);
                ++numTri;
                v->PrintVert();
            };

            VertItrC v = Loop(start, QueueVert);
            if (v == _polygon.end())
                return;
            dump(v);

            while (numTri > 0)
            {
                const qItr ear = _earsQueue.begin();
                if (ear != _earsQueue.end())
                {
                    v = *ear;
                    // Cost should always be negative, generally < -epsilon.
                    v->PrintVert();
                    _earsQueue.erase(ear);
                }
                else
                {
                    PRINT("No ear found!");
                }

                ClipEar(v);
                --numTri;

                ProcessEar(v->left, vertCollider);
                ProcessEar(v->right, vertCollider);
                // This is a backup vert that is used if the queue is empty (geometrically
                // invalid polygon), to ensure manifoldness.
                v = v->right;
            }

            DEBUG_ASSERT(v->right == v->left, logicErr, "Triangulator error!");
            PRINT("Finished poly");
        }

        void dump(VertItrC start) const
        {
            (void*)&start;
        }
    };


    /**
    * @brief Triangulates a set of &epsilon;-valid polygons. If the input is not
    * &epsilon;-valid, the triangulation may overlap, but will always return a
    * manifold result that matches the input edge directions.
    *
    * @param polys The set of polygons, wound isCCW and representing multiple
    * polygons and/or holes. These have 2D-projected positions as well as
    * references back to the original _vertices.
    * @param epsilon The value of &epsilon;, bounding the uncertainty of the
    * input.
    * @return std::vector<ivec3> The triangles, referencing the original
    * vertex indicies.
    */
    std::vector<ivec3>  TriangulateIdx(const PolygonsIdx& polys, double epsilon)
    {
        std::vector<ivec3> triangles;
        double updatedEpsilon = epsilon;

        // fast path
        if (IsConvex(polys, epsilon))
        {
            triangles = TriangulateConvex(polys);
        }
        else
        {
            EarClip triangulator(polys, epsilon);
            triangles = triangulator.Triangulate();
            updatedEpsilon = triangulator.GetPrecision();
        }
        return triangles;
    }

    /**
    * @brief Triangulates a set of &epsilon;-valid polygons. If the input is not
    * &epsilon;-valid, the triangulation may overlap, but will always return a
    * manifold result that matches the input edge directions.
    *
    * @param polygons The set of polygons, wound isCCW and representing multiple
    * polygons and/or holes.
    * @param epsilon The value of &epsilon;, bounding the uncertainty of the
    * input.
    * @return std::vector<ivec3> The triangles, referencing the original
    * polygon points in order.
    */
    std::vector<ivec3>  Triangulate(const Polygons& polygons, double epsilon)
    {
        int idx = 0;
        PolygonsIdx polygonsIndexed;
        for (const auto& poly : polygons)
        {
            SimplePolygonIdx simpleIndexed;
            for (const vec2& polyVert : poly)
            {
                simpleIndexed.push_back({ polyVert, idx++ });
            }
            polygonsIndexed.push_back(simpleIndexed);
        }
        return TriangulateIdx(polygonsIndexed, epsilon);
    }

    ExecutionParams& PolygonParams() { return params; }

}

/// <summary>
/// sort
/// </summary>

namespace manifold
{
    constexpr uint32_t kNoCode = 0xFFFFFFFFu;

    uint32_t MortonCode(vec3 position, Box bBox)
    {
        // Unreferenced _vertices are marked NaN, and this will sort them to the end
        // (the Morton code only uses the first 30 of 32 bits).
        if (std::isnan(position.x)) return kNoCode;

        return Collider::MortonCode(position, bBox);
    }

    struct Reindex
    {
        TArrayView<const int> indexInv;

        void operator()(Halfedge& edge)
        {
            if (edge._startVert < 0) return;
            edge._startVert = indexInv[edge._startVert];
            edge._endVert = indexInv[edge._endVert];
        }
    };

    struct MarkProp
    {
        TArrayView<int> keep;
        void operator()(ivec3 triProp)
        {
            for (const int i : {0, 1, 2})
            {
                reinterpret_cast<std::atomic<int>*>(&keep[triProp[i]])->store(1, std::memory_order_relaxed);
            }
        }
    };

    struct ReindexProps
    {
        TArrayView<const int> old2new;

        void operator()(ivec3& triProp)
        {
            for (const int i : {0, 1, 2}) {
                triProp[i] = old2new[triProp[i]];
            }
        }
    };

    struct ReindexFace
    {
        TArrayView<Halfedge> halfedge;
        TArrayView<vec4> halfedgeTangent;
        TArrayView<const Halfedge> oldHalfedge;
        TArrayView<const vec4> oldHalfedgeTangent;
        TArrayView<const int> faceNew2Old;
        TArrayView<const int> faceOld2New;

        void operator()(int newFace)
        {
            const int oldFace = faceNew2Old[newFace];
            for (const int i : {0, 1, 2})
            {
                const int oldEdge = 3 * oldFace + i;
                Halfedge edge = oldHalfedge[oldEdge];
                const int pairedFace = edge._pairedHalfedge / 3;
                const int offset = edge._pairedHalfedge - 3 * pairedFace;
                edge._pairedHalfedge = 3 * faceOld2New[pairedFace] + offset;
                const int newEdge = 3 * newFace + i;
                halfedge[newEdge] = edge;
                if (!oldHalfedgeTangent.empty())
                {
                    halfedgeTangent[newEdge] = oldHalfedgeTangent[oldEdge];
                }
            }
        }
    };

    template <typename Precision, typename I>
    bool MergeMeshGLP(MeshGLP<Precision, I>& mesh)
    {
        ZoneScoped;
        std::multiset<std::pair<int, int>> openEdges;

        std::vector<int> merge(mesh.NumVert());
        std::iota(merge.begin(), merge.end(), 0);
        for (size_t i = 0; i < mesh.mergeFromVert.size(); ++i)
        {
            merge[mesh.mergeFromVert[i]] = (int)mesh.mergeToVert[i];
        }

        const auto numVert = (int)mesh.NumVert();
        const auto numTri = (int)mesh.NumTri();
        const int next[3] = { 1, 2, 0 };
        for (int tri = 0; tri < numTri; ++tri)
        {
            for (int i : {0, 1, 2})
            {
                auto edge = std::make_pair(merge[mesh.triVerts[3 * tri + next[i]]],
                    merge[mesh.triVerts[3 * tri + i]]);
                auto it = openEdges.find(edge);
                if (it == openEdges.end())
                {
                    std::swap(edge.first, edge.second);
                    openEdges.insert(edge);
                }
                else
                {
                    openEdges.erase(it);
                }
            }
        }
        if (openEdges.empty())
        {
            return false;
        }
        const auto numOpenVert = openEdges.size();
        Ints openVerts(numOpenVert);
        int ix = 0;
        for (const auto& edge : openEdges)
        {
            const int vert = edge.first;
            openVerts[ix++] = vert;
        }
        TArray<Precision> vertPropD(mesh.vertProperties);
        Box bBox;
        for (const int i : {0, 1, 2})
        {
            auto iPos =
                StridedRange(vertPropD.begin() + i, vertPropD.end(), mesh.numProp);
            auto minMax = manifold::transform_reduce(
                iPos.begin(), iPos.end(),
                std::make_pair(std::numeric_limits<double>::infinity(),
                    -std::numeric_limits<double>::infinity()),
                [](auto a, auto b)
                {
                    return std::make_pair(std::min(a.first, b.first), std::max(a.second, b.second));
                },
                [](double f)
                {
                    return std::make_pair(f, f);
                });
            bBox.min[i] = minMax.first;
            bBox.max[i] = minMax.second;
        }

        const double tolerance = std::max(static_cast<double>(mesh.tolerance),
            (std::is_same<Precision, float>::value
                ? std::numeric_limits<float>::epsilon()
                : kPrecision) *
            bBox.Scale());

        auto policy = autoPolicy(numOpenVert, (size_t)1e5);
        Boxs vertBox(numOpenVert);
        TArray<uint32_t> vertMorton(numOpenVert);

        for_each_n(policy, countAt(0), numOpenVert,
            [&vertMorton, &vertBox, &openVerts, &bBox, &mesh, tolerance](size_t i)
            {
                int  vert = openVerts[i];

        const vec3   center(mesh.vertProperties[mesh.numProp * vert],
            mesh.vertProperties[mesh.numProp * vert + 1],
            mesh.vertProperties[mesh.numProp * vert + 2]);

        vertBox[i].min = center - tolerance / 2.0;
        vertBox[i].max = center + tolerance / 2.0;

        vertMorton[i] = MortonCode(center, bBox);
            });

        Ints vertNew2Old(numOpenVert);
        sequence(vertNew2Old.begin(), vertNew2Old.end());

        stable_sort(vertNew2Old.begin(), vertNew2Old.end(),
            [&vertMorton](const int& a, const int& b) {
                return vertMorton[a] < vertMorton[b];
            });

        permute(vertMorton, vertNew2Old);
        permute(vertBox, vertNew2Old);
        permute(openVerts, vertNew2Old);
        Collider collider(vertBox, vertMorton);
        SparseIndices toMerge = collider.Collisions<true>(vertBox.cview());

        UnionFind<> uf(numVert);
        for (size_t i = 0; i < mesh.mergeFromVert.size(); ++i)
        {
            uf.unionXY(static_cast<int>(mesh.mergeFromVert[i]),
                static_cast<int>(mesh.mergeToVert[i]));
        }
        for (size_t i = 0; i < toMerge.size(); ++i)
        {
            uf.unionXY(openVerts[toMerge.get(i, false)],
                openVerts[toMerge.get(i, true)]);
        }

        mesh.mergeToVert.clear();
        mesh.mergeFromVert.clear();
        for (size_t v = 0; v < numVert; ++v)
        {
            const UnionFind<>::IType mergeTo = uf.find((UnionFind<>::IType)v);
            if (mergeTo != v)
            {
                mesh.mergeFromVert.push_back((I)v);
                mesh.mergeToVert.push_back(mergeTo);
            }
        }

        return true;
    }




    /**
    * Once _halfedge has been filled in, this function can be called to create the
    * rest of the internal data structures. This function also removes the verts
    * and _halfedges flagged for removal (NaN verts and -1 _halfedges).
    */
    void Manifold::Impl::Finish() {
        if (_halfedge.size() == 0) return;

        CalculateBBox();
        SetEpsilon(_epsilon);
        if (!_bBox.IsFinite()) {
            // Decimated out of existence - early out.
            MarkFailure(Error::NoError);
            return;
        }

        SortVerts();
        Boxs faceBox;
        TArray<uint32_t> faceMorton;
        GetFaceBoxMorton(faceBox, faceMorton);
        SortFaces(faceBox, faceMorton);
        if (_halfedge.size() == 0) return;
        CompactProps();

        DEBUG_ASSERT(_halfedge.size() % 6 == 0, topologyErr,
            "Not an even number of faces after sorting faces!");

        DEBUG_ASSERT(_meshRelation.triRef.size() == NumTri() ||
            _meshRelation.triRef.size() == 0,
            logicErr, "Mesh Relation doesn't fit!");
        DEBUG_ASSERT(_faceNormal.size() == NumTri() || _faceNormal.size() == 0,
            logicErr,
            "faceNormal size = " + std::to_string(_faceNormal.size()) +
            ", NumTri = " + std::to_string(NumTri()));
        CalculateNormals();
        _collider = Collider(faceBox, faceMorton);

        DEBUG_ASSERT(Is2Manifold(), logicErr, "mesh is not 2-manifold!");
    }

    /**
    * Sorts the _vertices according to their Morton code.
    */
    void Manifold::Impl::SortVerts() {
        ZoneScoped;
        const auto numVert = NumVert();
        TArray<uint32_t> vertMorton(numVert);
        auto policy = autoPolicy(numVert, (size_t)1e5);
        for_each_n(policy, countAt(0), numVert, [this, &vertMorton](size_t vert)
            {
                vertMorton[vert] = MortonCode(_vertPos[vert], _bBox);
            });

        Ints vertNew2Old(numVert);
        sequence(vertNew2Old.begin(), vertNew2Old.end());

        stable_sort(vertNew2Old.begin(), vertNew2Old.end(),
            [&vertMorton](const int& a, const int& b) {
                return vertMorton[a] < vertMorton[b];
            });

        ReindexVerts(vertNew2Old, numVert);

        // Verts were flagged for removal with NaNs and assigned kNoCode to sort
        // them to the end, which allows them to be removed.
        const auto newNumVert = std::find_if(vertNew2Old.begin(), vertNew2Old.end(),
            [&vertMorton](const int vert) {
                return vertMorton[vert] == kNoCode;
            }) -
            vertNew2Old.begin();

            vertNew2Old.resize(newNumVert);
            permute(_vertPos, vertNew2Old);

            if (_vertNormal.size() == numVert) {
                permute(_vertNormal, vertNew2Old);
            }
    }

    /**
    * Updates the _halfedges to point to new vert indices based on a mapping,
    * vertNew2Old. This may be a subset, so the total number of original verts is
    * also given.
    */
    void Manifold::Impl::ReindexVerts(const Ints& vertNew2Old,
        size_t oldNumVert) {
        ZoneScoped;
        Ints vertOld2New(oldNumVert);
        scatter(countAt(0), countAt(NumVert()), vertNew2Old.begin(), vertOld2New.begin());
        for_each(autoPolicy(oldNumVert, (size_t)1e5), _halfedge.begin(), _halfedge.end(), Reindex({ vertOld2New }));
    }

    /**
    * Removes unreferenced property verts and reindexes triProperties.
    */
    void Manifold::Impl::CompactProps()
    {
        ZoneScoped;
        if (_meshRelation.numProp == 0)
            return;

        const auto numVerts = _meshRelation.properties.size() / _meshRelation.numProp;
        Ints keep(numVerts, 0);
        auto policy = autoPolicy(numVerts, (size_t)1e5);

        for_each(policy
            , _meshRelation.triProperties.cbegin()
            , _meshRelation.triProperties.cend()
            , MarkProp({ keep }));
        Ints propOld2New(numVerts + 1, 0);
        inclusive_scan(keep.begin(), keep.end(), propOld2New.begin() + 1);

        TArray<double> oldProp = _meshRelation.properties;
        const int numVertsNew = propOld2New[numVerts];
        const int numProp = _meshRelation.numProp;
        auto& properties = _meshRelation.properties;
        properties.resize(numProp * numVertsNew);
        for_each_n(
            policy
            , countAt(0)
            , numVerts
            , [&properties, &oldProp, &propOld2New, &keep, &numProp](const int oldIdx)
            {
                if (keep[oldIdx] == 0) return;
        for (int p = 0; p < numProp; ++p)
        {
            properties[propOld2New[oldIdx] * numProp + p] = oldProp[oldIdx * numProp + p];
        }
            });
        for_each_n(policy, _meshRelation.triProperties.begin(), NumTri(), ReindexProps({ propOld2New }));
    }

    /**
    * Fills the faceBox and faceMorton input with the bounding boxes and Morton
    * codes of the _faces, respectively. The Morton code is based on the center of
    * the bounding box.
    */
    void Manifold::Impl::GetFaceBoxMorton(Boxs& faceBox, TArray<uint32_t>& faceMorton) const
    {
        ZoneScoped;
        faceBox.resize(NumTri());
        faceMorton.resize(NumTri());
        for_each_n(autoPolicy(NumTri(), (size_t)1e5), countAt(0), NumTri(),
            [this, &faceBox, &faceMorton](size_t face)
            {
                // Removed tris are marked by all _halfedges having _pairedHalfedge
                // = -1, and this will sort them to the end (the Morton code only
                // uses the first 30 of 32 bits).
                if (_halfedge[3 * face]._pairedHalfedge < 0)
                {
                    faceMorton[face] = kNoCode;
                    return;
                }

        vec3 center(0.0);

        for (const int i : {0, 1, 2}) {
            const vec3 pos = _vertPos[_halfedge[3 * face + i]._startVert];
            center += pos;
            faceBox[face].Union(pos);
        }
        center /= 3;

        faceMorton[face] = MortonCode(center, _bBox);
            });
    }

    /**
    * Sorts the _faces of this manifold according to their input Morton code. The
    * bounding box and Morton code arrays are also sorted accordingly.
    */
    void Manifold::Impl::SortFaces(Boxs& faceBox, TArray<uint32_t>& faceMorton) {
        ZoneScoped;
        Ints faceNew2Old(NumTri());
        sequence(faceNew2Old.begin(), faceNew2Old.end());

        stable_sort(faceNew2Old.begin(), faceNew2Old.end(),
            [&faceMorton](const int& a, const int& b) {
                return faceMorton[a] < faceMorton[b];
            });

        // Tris were flagged for removal with _pairedHalfedge = -1 and assigned kNoCode
        // to sort them to the end, which allows them to be removed.
        const auto newNumTri = std::find_if(faceNew2Old.begin(), faceNew2Old.end(),
            [&faceMorton](const int face)
            {
                return faceMorton[face] == kNoCode;
            }) - faceNew2Old.begin();
            faceNew2Old.resize(newNumTri);

            permute(faceMorton, faceNew2Old);
            permute(faceBox, faceNew2Old);
            GatherFaces(faceNew2Old);
    }

    /**
    * Creates the _halfedge vector for this manifold by copying a set of _faces from
    * another manifold, given by oldHalfedge. Input faceNew2Old defines the old
    * _faces to gather into this.
    */
    void Manifold::Impl::GatherFaces(const Ints& faceNew2Old) {
        ZoneScoped;
        const auto numTri = faceNew2Old.size();
        if (_meshRelation.triRef.size() == NumTri())
            permute(_meshRelation.triRef, faceNew2Old);
        if (_meshRelation.triProperties.size() == NumTri())
            permute(_meshRelation.triProperties, faceNew2Old);
        if (_faceNormal.size() == NumTri()) permute(_faceNormal, faceNew2Old);

        TArray<Halfedge> oldHalfedge(std::move(_halfedge));
        TArray<vec4> oldHalfedgeTangent(std::move(_halfedgeTangent));
        Ints faceOld2New(oldHalfedge.size() / 3);
        auto policy = autoPolicy(numTri, (size_t)1e5);
        scatter(countAt(0_uz), countAt(numTri), faceNew2Old.begin(), faceOld2New.begin());

        _halfedge.resize(3 * numTri);
        if (oldHalfedgeTangent.size() != 0) _halfedgeTangent.resize(3 * numTri);
        for_each_n(policy
            , countAt(0)
            , numTri
            , ReindexFace({ _halfedge, _halfedgeTangent, oldHalfedge,oldHalfedgeTangent, faceNew2Old, faceOld2New }));
    }

    void Manifold::Impl::GatherFaces(const Impl& old, const Ints& faceNew2Old) {
        ZoneScoped;
        const auto numTri = faceNew2Old.size();

        _meshRelation.triRef.resize(numTri);
        gather(faceNew2Old.begin(), faceNew2Old.end(),
            old._meshRelation.triRef.begin(), _meshRelation.triRef.begin());

        for (const auto& pair : old._meshRelation.meshIDtransform) {
            _meshRelation.meshIDtransform[pair.first] = pair.second;
        }

        if (old._meshRelation.triProperties.size() > 0) {
            _meshRelation.triProperties.resize(numTri);
            gather(faceNew2Old.begin(), faceNew2Old.end(),
                old._meshRelation.triProperties.begin(),
                _meshRelation.triProperties.begin());
            _meshRelation.numProp = old._meshRelation.numProp;
            _meshRelation.properties = old._meshRelation.properties;
        }

        if (old._faceNormal.size() == old.NumTri()) {
            _faceNormal.resize(numTri);
            gather(faceNew2Old.begin(), faceNew2Old.end(), old._faceNormal.begin(),
                _faceNormal.begin());
        }

        Ints faceOld2New(old.NumTri());
        scatter(countAt(0_uz), countAt(numTri), faceNew2Old.begin(),
            faceOld2New.begin());

        _halfedge.resize(3 * numTri);
        if (old._halfedgeTangent.size() != 0) _halfedgeTangent.resize(3 * numTri);
        for_each_n(autoPolicy(numTri, (size_t)1e5), countAt(0), numTri,
            ReindexFace({ _halfedge, _halfedgeTangent, old._halfedge,
                old._halfedgeTangent, faceNew2Old, faceOld2New }));
    }

    /**
    * Updates the mergeFromVert and mergeToVert vectors in order to create a
    * manifold solid. If the MeshGL is already manifold, no change will occur and
    * the function will return false. Otherwise, this will merge verts along open
    * edges within tolerance (the maximum of the MeshGL tolerance and the
    * baseline bounding-box tolerance), keeping any from the existing merge
    * vectors, and return true.
    *
    * There is no guarantee the result will be manifold - this is a best-effort
    * helper function designed primarily to aid in the case where a manifold
    * multi-material MeshGL was produced, but its merge vectors were lost due to
    * a round-trip through a file format. Constructing a Manifold from the result
    * will report an error status if it is not manifold.
    */
    template <>
    bool MeshGL::Merge()
    {
        return MergeMeshGLP(*this);
    }

    template <>
    bool MeshGL64::Merge()
    {
        return MergeMeshGLP(*this);
    }
}