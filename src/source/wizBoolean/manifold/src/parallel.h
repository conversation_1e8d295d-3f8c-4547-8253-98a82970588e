#pragma     once


#include    <algorithm>
#include    <numeric>



#include    <iterator>
#include    <type_traits>

namespace manifold 
{
    template <typename F, typename Iter>
    struct  TransformItr 
    {
    private:
        Iter      _iter;
        F         _f;

    public:
        using pointer             =   void;
        using reference           =   std::invoke_result_t<F, typename std::iterator_traits<std::remove_const_t<Iter>>::value_type>;
        using difference_type     =   typename std::iterator_traits<std::remove_const_t<Iter>>::difference_type;
        using value_type          =   reference;
        using iterator_category   =   typename std::iterator_traits<std::remove_const_t<Iter>>::iterator_category;

        constexpr TransformItr(Iter _iter, F f) : _iter(_iter), _f(f) {}

        TransformItr& operator=(const TransformItr& other) 
        {
            if (this == &other) return *this;
            // don't copy function, should be the same
            _iter = other._iter;
            return *this;
        }

        constexpr reference operator*() const { return _f(*_iter); }

        constexpr reference operator[](size_t i) const { return _f(_iter[i]); }

        // prefix increment
        TransformItr& operator++() 
        {
            _iter += 1;
            return *this;
        }

        // postfix
        TransformItr operator++(int) 
        {
            auto old = *this;
            operator++();
            return old;
        }

        // prefix increment
        TransformItr& operator--() 
        {
            _iter -= 1;
            return *this;
        }

        // postfix
        TransformItr operator--(int) 
        {
            auto old = *this;
            operator--();
            return old;
        }

        constexpr TransformItr operator+(size_t n) const 
        {
            return TransformItr(_iter + n, _f);
        }

        TransformItr& operator+=(size_t n) 
        {
            _iter += n;
            return *this;
        }

        constexpr TransformItr operator-(size_t n) const 
        {
            return TransformItr(_iter - n, _f);
        }

        TransformItr& operator-=(size_t n) 
        {
            _iter -= n;
            return *this;
        }

        constexpr bool operator==(TransformItr other) const 
        {
            return _iter == other._iter;
        }

        constexpr bool operator!=(TransformItr other) const 
        {
            return !(_iter == other._iter);
        }

        constexpr bool operator<(TransformItr other) const 
        {
            return _iter < other._iter;
        }

        constexpr difference_type operator-(TransformItr other) const 
        {
            return _iter - other._iter;
        }

        constexpr operator TransformItr<F, const Iter>() const 
        {
            return TransformItr(_f, _iter);
        }
    };

    template <typename T>
    struct  CountingItr 
    {
    private:
        T     _counter;
    public:
        using pointer             =   void;
        using reference           =   T;
        using difference_type     =   std::make_signed_t<T>;
        using value_type          =   T;
        using iterator_category   =   std::random_access_iterator_tag;

        constexpr CountingItr(T counter) : _counter(counter) {}

        constexpr value_type operator*()      const { return _counter; }
        constexpr value_type operator[](T i)  const { return _counter + i; }

        // prefix increment
        CountingItr& operator++() 
        {
            _counter += 1;
            return *this;
        }

        // postfix
        CountingItr operator++(int) 
        {
            auto old = *this;
            operator++();
            return old;
        }

        // prefix increment
        CountingItr& operator--() 
        {
            _counter -= 1;
            return *this;
        }

        // postfix
        CountingItr operator--(int) 
        {
            auto old = *this;
            operator--();
            return old;
        }

        constexpr CountingItr operator+(T n) const 
        {
            return CountingItr(_counter + n);
        }

        CountingItr& operator+=(T n) 
        {
            _counter += n;
            return *this;
        }

        constexpr CountingItr operator-(T n) const 
        {
            return CountingItr(_counter - n);
        }

        CountingItr& operator-=(T n) 
        {
            _counter -= n;
            return *this;
        }

        constexpr friend bool operator==(CountingItr a, CountingItr b) 
        {
            return a._counter == b._counter;
        }

        constexpr friend bool operator!=(CountingItr a, CountingItr b) 
        {
            return a._counter != b._counter;
        }

        constexpr friend bool operator<(CountingItr a, CountingItr b)
        {
            return a._counter < b._counter;
        }

        constexpr friend difference_type operator-(CountingItr a,CountingItr b) 
        {
            return a._counter - b._counter;
        }

        constexpr operator CountingItr<const T>() const 
        {
            return CountingItr(_counter);
        }
    };

    constexpr CountingItr<size_t> countAt(size_t i) 
    {
        return CountingItr(i);
    }

    template <typename Iter>
    struct  StridedRange 
    {
    private:
        struct StridedRangeItr 
        {
        private:
            Iter    _iter;
            size_t  _stride;

        public:
            using pointer           =   typename std::iterator_traits<std::remove_const_t<Iter>>::pointer;
            using reference         =   typename std::iterator_traits<std::remove_const_t<Iter>>::reference;
            using difference_type   =   typename std::iterator_traits<std::remove_const_t<Iter>>::difference_type;
            using value_type        =   typename std::iterator_traits<std::remove_const_t<Iter>>::value_type;
            using iterator_category =   typename std::iterator_traits<std::remove_const_t<Iter>>::iterator_category;

            constexpr StridedRangeItr(Iter _iter, int _stride)
                : _iter(_iter), _stride(_stride) 
            {}

            inline  constexpr reference operator*() { return *_iter; }

            inline  constexpr std::add_const_t<reference> operator*() const { return *_iter; }

            inline  constexpr reference operator[](size_t i) { return _iter[i * _stride]; }

            inline  constexpr std::add_const_t<reference> operator[](size_t i) const 
            {
                return _iter[i * _stride];
            }

            // prefix increment
            inline  StridedRangeItr& operator++() 
            {
                _iter += _stride;
                return *this;
            }

            // postfix
            inline  StridedRangeItr operator++(int) 
            {
                auto old = *this;
                operator++();
                return old;
            }

            // prefix increment
            inline  StridedRangeItr& operator--()
            {
                _iter -= _stride;
                return *this;
            }

            // postfix
            inline  StridedRangeItr operator--(int)
            {
                auto old = *this;
                operator--();
                return old;
            }

            inline  constexpr StridedRangeItr operator+(size_t n) const 
            {
                return StridedRangeItr(_iter + n * _stride, (int)_stride);
            }

            inline  StridedRangeItr& operator+=(size_t n) 
            {
                _iter += n * _stride;
                return *this;
            }

            inline  constexpr StridedRangeItr operator-(size_t n) const 
            {
                return StridedRangeItr(_iter - n * _stride, _stride);
            }

            inline  StridedRangeItr& operator-=(size_t n) 
            {
                _iter -= n * _stride;
                return *this;
            }

            inline  constexpr friend bool operator==(StridedRangeItr a, StridedRangeItr b) 
            {
                return a._iter == b._iter;
            }

            inline  constexpr friend bool operator!=(StridedRangeItr a, StridedRangeItr b) 
            {
                return !(a._iter == b._iter);
            }

            inline  constexpr friend bool operator<(StridedRangeItr a, StridedRangeItr b) 
            {
                return a._iter < b._iter;
            }

            inline  constexpr friend difference_type operator-(StridedRangeItr a,StridedRangeItr b) 
            {
                // note that this is not well-defined if a._stride != b._stride...
                return (a._iter - b._iter) / a._stride;
            }
        };
        Iter            _start;
        Iter            _end;
        const size_t    _stride;

    public:
        constexpr StridedRange(Iter start, Iter end, size_t _stride)
            : _start(start)
            , _end(end)
            , _stride(_stride) 
        {}

        constexpr StridedRangeItr begin() const 
        {
            return StridedRangeItr(_start, (int)_stride);
        }

        constexpr StridedRangeItr end() const 
        {
            return StridedRangeItr(_start, (int)_stride) +
                ((std::distance(_start, _end) + ((int)_stride - 1)) / (int)_stride);
        }
    };
}


namespace   manifold 
{
    constexpr size_t kSeqThreshold = (size_t)1e4;

    enum class ExecutionPolicy
    {
        Par,
        Seq,
    };

    
    // ExecutionPolicy:
    // - Sequential for small workload,
    // - Parallel (CPU) for medium workload,
    inline constexpr ExecutionPolicy autoPolicy(size_t size,size_t threshold = kSeqThreshold) 
    {
      if (size <= threshold) 
      {
        return ExecutionPolicy::Seq;
      }
      return ExecutionPolicy::Par;
    }

    template <typename Iter,typename Dummy = std::enable_if_t<!std::is_integral_v<Iter>>>
    inline constexpr ExecutionPolicy autoPolicy(Iter first, Iter last,size_t threshold = kSeqThreshold) 
    {
      if (static_cast<size_t>(std::distance(first, last)) <= threshold) {
        return ExecutionPolicy::Seq;
      }
      return ExecutionPolicy::Par;
    }

    template <typename InputIter, typename OutputIter>
    void    copy(ExecutionPolicy policy, InputIter first, InputIter last,OutputIter d_first);
    template <typename InputIter, typename OutputIter>
    void    copy(InputIter first, InputIter last, OutputIter d_first);

    // Applies the function `f` to each element in the range `[first, last)`
    template <typename Iter, typename F>
    void for_each(ExecutionPolicy policy, Iter first, Iter last, F f) 
    {
        (void*)&policy;
        static_assert(std::is_convertible_v<typename std::iterator_traits<Iter>::iterator_category,std::random_access_iterator_tag>,
                      "You can only parallelize RandomAccessIterator.");

        std::for_each(first, last, f);
    }

    // Applies the function `f` to each element in the range `[first, last)`
    template <typename Iter, typename F>
    void    for_each_n(ExecutionPolicy policy, Iter first, size_t n, F f) 
    {
        static_assert(std::is_convertible_v<typename std::iterator_traits<Iter>::iterator_category,std::random_access_iterator_tag>,
                        "You can only parallelize RandomAccessIterator.");
        for_each(policy, first, first + n, f);
    }

    // Reduce the range `[first, last)` using a binary operation `f` with an initial
    // value `init`.
    //
    // The binary operation should be commutative and associative. Otherwise, the
    // result is non-deterministic.
    template <typename InputIter, typename BinaryOp,
              typename T = typename std::iterator_traits<InputIter>::value_type>
    T reduce(ExecutionPolicy policy, InputIter first, InputIter last, T init,BinaryOp f)
    {
        (void*)&policy;
        static_assert(std::is_convertible_v<
                        typename std::iterator_traits<InputIter>::iterator_category,
                        std::random_access_iterator_tag>,
                    "You can only parallelize RandomAccessIterator.");

        return std::reduce(first, last, init, f);
    }

    // Reduce the range `[first, last)` using a binary operation `f` with an initial
    // value `init`.
    //
    // The binary operation should be commutative and associative. Otherwise, the
    // result is non-deterministic.
    template <typename InputIter, typename BinaryOp,typename T = typename std::iterator_traits<InputIter>::value_type>
    T   reduce(InputIter first, InputIter last, T init, BinaryOp f) 
    {
      return reduce(autoPolicy(first, last, (size_t)1e5), first, last, init, f);
    }

    // Transform and reduce the range `[first, last)` by first applying a unary
    // function `g`, and then combining the results using a binary operation `f`
    // with an initial value `init`.
    //
    // The binary operation should be commutative and associative. Otherwise, the
    // result is non-deterministic.
    template <typename InputIter, typename BinaryOp, typename UnaryOp,
              typename T = std::invoke_result_t<UnaryOp, typename std::iterator_traits<InputIter>::value_type>>
    T   transform_reduce(ExecutionPolicy policy, InputIter first, InputIter last,T init, BinaryOp f, UnaryOp g) 
    {
      return reduce(policy, TransformItr(first, g), TransformItr(last, g),init, f);
    }

    // Transform and reduce the range `[first, last)` by first applying a unary
    // function `g`, and then combining the results using a binary operation `f`
    // with an initial value `init`.
    //
    // The binary operation should be commutative and associative. Otherwise, the
    // result is non-deterministic.
    template <typename InputIter, typename BinaryOp, typename UnaryOp,
              typename T = std::invoke_result_t<UnaryOp, typename std::iterator_traits<InputIter>::value_type>>
    T   transform_reduce(InputIter first, InputIter last, T init, BinaryOp f,UnaryOp g) 
    {
      return manifold::reduce(TransformItr(first, g),TransformItr(last, g), init, f);
    }

    // Compute the inclusive prefix sum for the range `[first, last)`
    // using the summation operator, and store the result in the range
    // starting from `d_first`.
    //
    // The input range `[first, last)` and
    // the output range `[d_first, d_first + last - first)`
    // must be equal or non-overlapping.
    template <typename InputIter, typename OutputIter,
              typename T = typename std::iterator_traits<InputIter>::value_type>
    void    inclusive_scan(ExecutionPolicy policy, InputIter first, InputIter last,OutputIter d_first) 
    {
        (void*)&policy;
        static_assert(std::is_convertible_v<
                        typename std::iterator_traits<InputIter>::iterator_category,
                        std::random_access_iterator_tag>,
                        "You can only parallelize RandomAccessIterator.");

        static_assert(  std::is_convertible_v<
                        typename std::iterator_traits<OutputIter>::iterator_category,
                        std::random_access_iterator_tag>,
                        "You can only parallelize RandomAccessIterator.");

        std::inclusive_scan(first, last, d_first);
    }

    // Compute the inclusive prefix sum for the range `[first, last)` using the
    // summation operator, and store the result in the range
    // starting from `d_first`.
    //
    // The input range `[first, last)` and
    // the output range `[d_first, d_first + last - first)`
    // must be equal or non-overlapping.
    template <typename InputIter, typename OutputIter,typename T = typename std::iterator_traits<InputIter>::value_type>
    void inclusive_scan(InputIter first, InputIter last, OutputIter d_first) 
    {
      return inclusive_scan(autoPolicy(first, last, (size_t)1e5), first, last, d_first);
    }

    // Compute the inclusive prefix sum for the range `[first, last)` using the
    // binary operator `f`, with initial value `init` and
    // identity element `identity`, and store the result in the range
    // starting from `d_first`.
    //
    // This is different from `exclusive_scan` in the sequential algorithm by
    // requiring an identity element. This is needed so that each block can be
    // scanned in parallel and combined later.
    //
    // The input range `[first, last)` and
    // the output range `[d_first, d_first + last - first)`
    // must be equal or non-overlapping.
    template <typename InputIter, typename OutputIter,
              typename BinOp = decltype(std::plus<typename std::iterator_traits<
                                            InputIter>::value_type>()),
              typename T = typename std::iterator_traits<InputIter>::value_type>
    void exclusive_scan(  ExecutionPolicy policy
                        , InputIter first
                        , InputIter last
                        , OutputIter d_first
                        , T init = static_cast<T>(0)
                        , BinOp f = std::plus<T>()
                        , T identity = static_cast<T>(0)) 
    {

        (void*)&policy;
        (void*)&identity;
        static_assert(  std::is_convertible_v<typename std::iterator_traits<InputIter>::iterator_category,std::random_access_iterator_tag>,
                        "You can only parallelize RandomAccessIterator.");
        static_assert(  std::is_convertible_v<typename std::iterator_traits<OutputIter>::iterator_category,std::random_access_iterator_tag>,
                        "You can only parallelize RandomAccessIterator.");

        std::exclusive_scan(first, last, d_first, init, f);
    }

    // Compute the inclusive prefix sum for the range `[first, last)` using the
    // binary operator `f`, with initial value `init` and
    // identity element `identity`, and store the result in the range
    // starting from `d_first`.
    //
    // This is different from `exclusive_scan` in the sequential algorithm by
    // requiring an identity element. This is needed so that each block can be
    // scanned in parallel and combined later.
    //
    // The input range `[first, last)` and
    // the output range `[d_first, d_first + last - first)`
    // must be equal or non-overlapping.
    template <typename InputIter, typename OutputIter,
              typename BinOp = decltype(std::plus<typename std::iterator_traits<
                                            InputIter>::value_type>()),
              typename T = typename std::iterator_traits<InputIter>::value_type>
    void exclusive_scan(InputIter first, InputIter last, OutputIter d_first,
                        T init = static_cast<T>(0), BinOp f = std::plus<T>(),
                        T identity = static_cast<T>(0)) {
      exclusive_scan(autoPolicy(first, last, (size_t)1e5), first, last, d_first, init, f,
                     identity);
    }

    // Apply function `f` on the input range `[first, last)` and store the result in
    // the range starting from `d_first`.
    //
    // The input range `[first, last)` and
    // the output range `[d_first, d_first + last - first)`
    // must be equal or non-overlapping.
    template <typename InputIter, typename OutputIter, typename F>
    void transform(ExecutionPolicy policy, InputIter first, InputIter last,OutputIter d_first, F f) 
    {
        (void*)&policy;
        static_assert(std::is_convertible_v<
                          typename std::iterator_traits<InputIter>::iterator_category,
                          std::random_access_iterator_tag>,
                      "You can only parallelize RandomAccessIterator.");
        static_assert(
            std::is_convertible_v<
                typename std::iterator_traits<OutputIter>::iterator_category,
                std::random_access_iterator_tag>,
            "You can only parallelize RandomAccessIterator.");

        std::transform(first, last, d_first, f);
    }

    // Apply function `f` on the input range `[first, last)` and store the result in
    // the range starting from `d_first`.
    //
    // The input range `[first, last)` and
    // the output range `[d_first, d_first + last - first)`
    // must be equal or non-overlapping.
    template <typename InputIter, typename OutputIter, typename F>
    void transform(InputIter first, InputIter last, OutputIter d_first, F f) {
      transform(autoPolicy(first, last, (size_t)1e5), first, last, d_first, f);
    }

    // Copy the input range `[first, last)` to the output range
    // starting from `d_first`.
    //
    // The input range `[first, last)` and
    // the output range `[d_first, d_first + last - first)`
    // must not overlap.
    template <typename InputIter, typename OutputIter>
    void copy(ExecutionPolicy policy, InputIter first, InputIter last,OutputIter d_first) 
    {
        (void*)&policy;
        static_assert(std::is_convertible_v<
                          typename std::iterator_traits<InputIter>::iterator_category,
                          std::random_access_iterator_tag>,
                      "You can only parallelize RandomAccessIterator.");
        static_assert(
            std::is_convertible_v<
                typename std::iterator_traits<OutputIter>::iterator_category,
                std::random_access_iterator_tag>,
            "You can only parallelize RandomAccessIterator.");

        std::copy(first, last, d_first);
    }

    // Copy the input range `[first, last)` to the output range
    // starting from `d_first`.
    //
    // The input range `[first, last)` and
    // the output range `[d_first, d_first + last - first)`
    // must not overlap.
    template <typename InputIter, typename OutputIter>
    void copy(InputIter first, InputIter last, OutputIter d_first) {
      copy(autoPolicy(first, last, (size_t)1e6), first, last, d_first);
    }

    // Copy the input range `[first, first + n)` to the output range
    // starting from `d_first`.
    //
    // The input range `[first, first + n)` and
    // the output range `[d_first, d_first + n)`
    // must not overlap.
    template <typename InputIter, typename OutputIter>
    void copy_n(ExecutionPolicy policy, InputIter first, size_t n,OutputIter d_first) 
    {
        copy(policy, first, first + n, d_first);
    }

    // Copy the input range `[first, first + n)` to the output range
    // starting from `d_first`.
    //
    // The input range `[first, first + n)` and
    // the output range `[d_first, d_first + n)`
    // must not overlap.
    template <typename InputIter, typename OutputIter>
    void copy_n(InputIter first, size_t n, OutputIter d_first) 
    {
        copy(autoPolicy(n, (size_t)1e6), first, first + n, d_first);
    }

    // Fill the range `[first, last)` with `value`.
    template <typename OutputIter, typename T>
    void fill(ExecutionPolicy policy, OutputIter first, OutputIter last, T value) 
    {
        (void*)&policy;
        static_assert(
                    std::is_convertible_v<typename std::iterator_traits<OutputIter>::iterator_category,std::random_access_iterator_tag>,
                    "You can only parallelize RandomAccessIterator.");
      std::fill(first, last, value);
    }

    // Fill the range `[first, last)` with `value`.
    template <typename OutputIter, typename T>
    void    fill(OutputIter first, OutputIter last, T value)
    {
        fill(autoPolicy(first, last, (size_t)5e5), first, last, value);
    }

    // Count the number of elements in the input range `[first, last)` satisfying
    // predicate `pred`, i.e. `pred(x) == true`.
    template <typename InputIter, typename P>
    size_t count_if(ExecutionPolicy policy, InputIter first, InputIter last,P pred) 
    {
        (void*)&policy;
        return std::count_if(first, last, pred);
    }

    // Count the number of elements in the input range `[first, last)` satisfying
    // predicate `pred`, i.e. `pred(x) == true`.
    template <typename InputIter, typename P>
    size_t count_if(InputIter first, InputIter last, P pred) 
    {
      return count_if(autoPolicy(first, last, (size_t)1e4), first, last, pred);
    }

    // Check if all elements in the input range `[first, last)` satisfy
    // predicate `pred`, i.e. `pred(x) == true`.
    template <typename InputIter, typename P>
    bool all_of(ExecutionPolicy policy, InputIter first, InputIter last, P pred) 
    {
        (void*)&policy;
        static_assert(std::is_convertible_v<
                        typename std::iterator_traits<InputIter>::iterator_category,
                        std::random_access_iterator_tag>,
                        "You can only parallelize RandomAccessIterator.");

        return std::all_of(first, last, pred);
    }

    // Check if all elements in the input range `[first, last)` satisfy
    // predicate `pred`, i.e. `pred(x) == true`.
    template <typename InputIter, typename P>
    bool all_of(InputIter first, InputIter last, P pred) 
    {
      return all_of(autoPolicy(first, last, (size_t)1e5), first, last, pred);
    }

    // Copy values in the input range `[first, last)` to the output range
    // starting from `d_first` that satisfies the predicate `pred`,
    // i.e. `pred(x) == true`, and returns `d_first + n` where `n` is the number of
    // times the predicate is evaluated to true.
    //
    // This function is stable, meaning that the relative order of elements in the
    // output range remains unchanged.
    //
    // The input range `[first, last)` and
    // the output range `[d_first, d_first + last - first)`
    // must not overlap.
    template <typename InputIter, typename OutputIter, typename P>
    OutputIter copy_if(ExecutionPolicy policy, InputIter first, InputIter last,OutputIter d_first, P pred) 
    {
        (void*)&policy;
        static_assert(std::is_convertible_v<
                          typename std::iterator_traits<InputIter>::iterator_category,
                          std::random_access_iterator_tag>,
                        "You can only parallelize RandomAccessIterator.");
        static_assert(
            std::is_convertible_v<
                typename std::iterator_traits<OutputIter>::iterator_category,
                std::random_access_iterator_tag>,
                "You can only parallelize RandomAccessIterator.");

        return std::copy_if(first, last, d_first, pred);
    }

    // Copy values in the input range `[first, last)` to the output range
    // starting from `d_first` that satisfies the predicate `pred`, i.e. `pred(x) ==
    // true`, and returns `d_first + n` where `n` is the number of times the
    // predicate is evaluated to true.
    //
    // This function is stable, meaning that the relative order of elements in the
    // output range remains unchanged.
    //
    // The input range `[first, last)` and
    // the output range `[d_first, d_first + last - first)`
    // must not overlap.
    template <typename InputIter, typename OutputIter, typename P>
    OutputIter copy_if(InputIter first, InputIter last, OutputIter d_first,P pred) 
    {
        return copy_if(autoPolicy(first, last, (size_t)1e5), first, last, d_first, pred);
    }

    // Remove values in the input range `[first, last)` that satisfies
    // the predicate `pred`, i.e. `pred(x) == true`, and returns `first + n`
    // where `n` is the number of times the predicate is evaluated to false.
    //
    // This function is stable, meaning that the relative order of elements that
    // remained are unchanged.
    //
    // Only trivially destructable types are supported.
    template <typename Iter, typename P,
              typename T = typename std::iterator_traits<Iter>::value_type>
    Iter    remove_if(ExecutionPolicy policy, Iter first, Iter last, P pred) 
    {
        (void*)&policy;
        static_assert(std::is_convertible_v<
                          typename std::iterator_traits<Iter>::iterator_category,
                          std::random_access_iterator_tag>,
                      "You can only parallelize RandomAccessIterator.");
        static_assert(std::is_trivially_destructible_v<T>,
                      "Our simple implementation does not support types that are "
                      "not trivially destructable.");

        return std::remove_if(first, last, pred);
    }

    // Remove values in the input range `[first, last)` that satisfies
    // the predicate `pred`, i.e. `pred(x) == true`, and
    // returns `first + n` where `n` is the number of times the predicate is
    // evaluated to false.
    //
    // This function is stable, meaning that the relative order of elements that
    // remained are unchanged.
    //
    // Only trivially destructable types are supported.
    template <typename Iter, typename P,
              typename T = typename std::iterator_traits<Iter>::value_type>
    Iter    remove_if(Iter first, Iter last, P pred) 
    {
      return remove_if(autoPolicy(first, last, (size_t)1e4), first, last, pred);
    }

    // Remove values in the input range `[first, last)` that are equal to `value`.
    // Returns `first + n` where `n` is the number of values
    // that are not equal to `value`.
    //
    // This function is stable, meaning that the relative order of elements that
    // remained are unchanged.
    //
    // Only trivially destructable types are supported.
    template <typename Iter,
              typename T = typename std::iterator_traits<Iter>::value_type>
    Iter    remove(ExecutionPolicy policy, Iter first, Iter last, T value) 
    {
        (void*)&policy;
        static_assert(std::is_convertible_v<
                          typename std::iterator_traits<Iter>::iterator_category,
                          std::random_access_iterator_tag>,
                      "You can only parallelize RandomAccessIterator.");
        static_assert(std::is_trivially_destructible_v<T>,
                      "Our simple implementation does not support types that are "
                      "not trivially destructable.");

        return std::remove(first, last, value);
    }

    // Remove values in the input range `[first, last)` that are equal to `value`.
    // Returns `first + n` where `n` is the number of values
    // that are not equal to `value`.
    //
    // This function is stable, meaning that the relative order of elements that
    // remained are unchanged.
    //
    // Only trivially destructable types are supported.
    template <typename Iter,
              typename T = typename std::iterator_traits<Iter>::value_type>
    Iter    remove(Iter first, Iter last, T value) 
    {
        return remove(autoPolicy(first, last, (size_t)1e4), first, last, value);
    }

    // For each group of consecutive elements in the range `[first, last)` with the
    // same value, unique removes all but the first element of the group. The return
    // value is an iterator `new_last` such that no two consecutive elements in the
    // range `[first, new_last)` are equal.
    //
    // This function is stable, meaning that the relative order of elements that
    // remained are unchanged.
    //
    // Only trivially destructable types are supported.
    template <typename Iter,
              typename T = typename std::iterator_traits<Iter>::value_type>
    Iter unique(ExecutionPolicy policy, Iter first, Iter last) 
    {
        (void*)&policy;
        static_assert(std::is_convertible_v<
                          typename std::iterator_traits<Iter>::iterator_category,
                          std::random_access_iterator_tag>,
                      "You can only parallelize RandomAccessIterator.");
        static_assert(std::is_trivially_destructible_v<T>,
                      "Our simple implementation does not support types that are "
                      "not trivially destructable.");

        return std::unique(first, last);
    }

    // For each group of consecutive elements in the range `[first, last)` with the
    // same value, unique removes all but the first element of the group. The return
    // value is an iterator `new_last` such that no two consecutive elements in the
    // range `[first, new_last)` are equal.
    //
    // This function is stable, meaning that the relative order of elements that
    // remained are unchanged.
    //
    // Only trivially destructable types are supported.
    template <typename Iter,typename T = typename std::iterator_traits<Iter>::value_type>
    Iter    unique(Iter first, Iter last) 
    {
        return unique(autoPolicy(first, last, (size_t)1e4), first, last);
    }

    // Sort the input range `[first, last)` in ascending order.
    //
    // This function is stable, meaning that the relative order of elements that are
    // incomparable remains unchanged.
    //
    // Only trivially destructable types are supported.
    template <typename Iterator,typename T = typename std::iterator_traits<Iterator>::value_type>
    void    stable_sort(ExecutionPolicy policy, Iterator first, Iterator last) 
    {
        (void*)&policy;
        std::stable_sort(first, last);
    }

    // Sort the input range `[first, last)` in ascending order.
    //
    // This function is stable, meaning that the relative order of elements that are
    // incomparable remains unchanged.
    //
    // Only trivially destructable types are supported.
    template <typename Iterator,typename T = typename std::iterator_traits<Iterator>::value_type>
    void    stable_sort(Iterator first, Iterator last) 
    {
        stable_sort(autoPolicy(first, last, (size_t)1e4), first, last);
    }

    // Sort the input range `[first, last)` in ascending order using the comparison
    // function `comp`.
    //
    // This function is stable, meaning that the relative order of elements that are
    // incomparable remains unchanged.
    //
    // Only trivially destructable types are supported.
    template <typename Iterator,
              typename T = typename std::iterator_traits<Iterator>::value_type,
              typename Comp = decltype(std::less<T>())>
    void    stable_sort(ExecutionPolicy policy, Iterator first, Iterator last,Comp comp) 
    {
        std::stable_sort(first, last, comp);
    }

    // Sort the input range `[first, last)` in ascending order using the comparison
    // function `comp`.
    //
    // This function is stable, meaning that the relative order of elements that are
    // incomparable remains unchanged.
    //
    // Only trivially destructable types are supported.
    template <typename Iterator,
              typename T = typename std::iterator_traits<Iterator>::value_type,
              typename Comp = decltype(std::less<T>())>
    void    stable_sort(Iterator first, Iterator last, Comp comp) 
    {
        stable_sort(autoPolicy(first, last, (size_t)1e4), first, last, comp);
    }

    // `scatter` copies elements from a source range into an output array according
    // to a map. For each iterator `i` in the range `[first, last)`, the value `*i`
    // is assigned to `outputFirst[mapFirst[i - first]]`.  If the same index appears
    // more than once in the range `[mapFirst, mapFirst + (last - first))`, the
    // result is undefined.
    //
    // The map range, input range and the output range must not overlap.
    template <typename InputIterator1, typename InputIterator2,
              typename OutputIterator>
    void scatter( ExecutionPolicy policy
                , InputIterator1 first
                , InputIterator1 last
                , InputIterator2 mapFirst
                , OutputIterator outputFirst) 
    {
      for_each(policy, countAt(0),
               countAt(static_cast<size_t>(std::distance(first, last))),
               [first, mapFirst, outputFirst](size_t i) 
               {
                    using   Type                =   std::remove_reference_t<decltype(outputFirst[mapFirst[i]])>;
                    using   NonConst            =   std::remove_const_t<Type>;
                    outputFirst[mapFirst[i]]    =   (NonConst)first[i];
               });
    }

    // `scatter` copies elements from a source range into an output array according
    // to a map. For each iterator `i` in the range `[first, last)`, the value `*i`
    // is assigned to `outputFirst[mapFirst[i - first]]`. If the same index appears
    // more than once in the range `[mapFirst, mapFirst + (last - first))`,
    // the result is undefined.
    //
    // The map range, input range and the output range must not overlap.
    template <typename InputIterator1, typename InputIterator2,typename OutputIterator>
    void scatter(InputIterator1 first, InputIterator1 last, InputIterator2 mapFirst,OutputIterator outputFirst) 
    {
      scatter(autoPolicy(first, last, (size_t)1e5), first, last, mapFirst, outputFirst);
    }

    // `gather` copies elements from a source array into a destination range
    // according to a map. For each input iterator `i`
    // in the range `[mapFirst, mapLast)`, the value `inputFirst[*i]`
    // is assigned to `outputFirst[i - map_first]`.
    //
    // The map range, input range and the output range must not overlap.
    template <typename InputIterator, typename RandomAccessIterator,
              typename OutputIterator>
    void gather(  ExecutionPolicy policy
                , InputIterator mapFirst
                , InputIterator mapLast
                , RandomAccessIterator inputFirst
                , OutputIterator outputFirst) 
    {
      for_each(policy, countAt(0),
               countAt(static_cast<size_t>(std::distance(mapFirst, mapLast))),
               [mapFirst, inputFirst, outputFirst](size_t i) 
               {
                    using   Type        =   std::remove_reference_t<decltype(outputFirst[i])>;
                    using   NonConst    =   std::remove_const_t<Type>;
                    outputFirst[i]      =   (NonConst)inputFirst[mapFirst[i]];
               });
    }

    // `gather` copies elements from a source array into a destination range
    // according to a map. For each input iterator `i`
    // in the range `[mapFirst, mapLast)`, the value `inputFirst[*i]`
    // is assigned to `outputFirst[i - map_first]`.
    //
    // The map range, input range and the output range must not overlap.
    template <typename InputIterator, typename RandomAccessIterator,typename OutputIterator>
    void gather(InputIterator mapFirst, InputIterator mapLast,RandomAccessIterator inputFirst, OutputIterator outputFirst) 
    {
      gather(autoPolicy(std::distance(mapFirst, mapLast), (size_t)1e5), mapFirst, mapLast,inputFirst, outputFirst);
    }

    // Write `[0, last - first)` to the range `[first, last)`.
    template <typename Iterator>
    void sequence(ExecutionPolicy policy, Iterator first, Iterator last) 
    {
      for_each(policy, countAt(0),countAt(static_cast<size_t>(std::distance(first, last))),[first](size_t i) {  first[i] = decltype(first[i]) (i); });
    }

    // Write `[0, last - first)` to the range `[first, last)`.
    template <typename Iterator>
    void sequence(Iterator first, Iterator last) 
    {
      sequence(autoPolicy(first, last, (size_t)1e5), first, last);
    }

} 