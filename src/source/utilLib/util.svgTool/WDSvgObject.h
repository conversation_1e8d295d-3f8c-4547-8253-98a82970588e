#pragma once

#include    "WDSvgTransform.h"
#include    "WDRapidxml.h"

WD_NAMESPACE_BEGIN

class SVT_TOOL_API WDSvgObject
{
public:
    /**
     * @brief Svg对象列表
     */
    using SvgObjects    =   std::vector<WDSvgObject*>;
    /**
     * @brief 元素类型
     */
    enum    Type
    {
        T_None,
        T_Group,
        T_Rect,
        T_Circle,
        T_Ellipse,
        T_Text,
        T_Path,
        T_SvgRoot,
        T_Line,
        T_PolyLine,
        T_Polygon,
    };
    /**
     * @brief svg节点的属性（部分，之后可能添加其他属性）
     */
    class SVT_TOOL_API Attr
    {
    public:
        // 对于"none"的默认值
        static Color ColorForNone;
    public:
        std::optional<WDSvgTransform>   transform; // 变换矩阵
        std::optional<Color>            fill;// 填充色
        std::optional<Color>            stroke; // 描边色
        std::optional<float>            strokeWidth; // 线宽
    public:
        /**
        * @brief 序列化
        * @param doc 
        * @param xmlNode 
        */
        void toSvgAttrs(XMLDoc& doc, XMLNode& xmlNode) const;
        /**
        * @brief 反序列化
        * @param nd 
        */
        void fromSvgAttrs(const XMLNode& xmlNode);
        /**
        * @brief 如果当前对象没有对应属性则继承上一级下来的属性
        对于变换矩阵，必要时需要叠加上一级下来的矩阵attr
        * @param  
        */
        Attr mergeParent(const Attr& attr) const;
    };
protected:
    Type                _type; // 类型
    Attr                _attr; // 属性
    SvgObjects          _children; //子元素
    std::vector<Vec3>   _points; //顶点集
public:
    WDSvgObject(Type type = T_None);
    virtual ~WDSvgObject();
public:
    /**
    * @brief 获取类型
    * @return 
    */
    inline Type type() const
    {
        return _type;
    }
    /**
     * @brief 获取属性
     * @return 
    */
    inline const Attr& attr() const
    {
        return _attr;
    }
    /**
     * @brief 获取顶点
     * @return 
    */
    inline const std::vector<Vec3>& points() const
    {
        return _points;
    }
    /**
     * @brief 获取子
     * @return 
    */
    inline const SvgObjects& children() const
    {
        return _children;
    }
    /**
     * @brief 设置父
     * @param parent 
     * @return 
    */
    bool addChild(WDSvgObject* child);
protected:
    /**
     * @brief 移动到点
     * @param v 
    */
    inline  void    moveTo(const Vec3& v)
    {
        _points.push_back(v);
    }
    template<class T>
    inline  void    moveTo(T x, T y)
    {
        moveTo(Vec3(x, y, 0));
    }
    /**
    *   目前都按照贝塞尔曲线的方式增加点，会多出来一些点，但是计算绘制会统一
    *   会更方便
    */
    bool            lineTo(const Vec3& v);
    template<class T>
    inline  void    lineTo(double x, double y)
    {
       lineTo(Vec3(x, y, 0));
    }
    /**
     * @brief 写入三个点，用于计算贝塞尔曲线（toData接口实现贝塞尔曲线所有顶点的计算）
     * @param ctrl1 
     * @param ctrl2 
     * @param pt 
     * @return 
    */
    bool    cubicBezierTo(const Vec3& ctrl1, const Vec3& ctrl2, const Vec3& pt);
protected:
    /**
    * @brief 创建svg，包括属性等
    * @param  
    * @param  
    * @return 
    */
    XMLNode* createSvg(XMLDoc&, XMLNode*, const char* value = nullptr) const;
public:
    /**
    *   转化成可绘制数据(大概可以理解为平滑化)
    *   全部采用贝塞尔算法生成
    */
    virtual size_t  toData(std::vector<FVec3>& data, int segs) const;
    /**
    *   与计算需要的元素空间个数，用于提前分配内存，避免频繁计算
    */
    virtual size_t  computeDataLength(int segs) const;
    /**
    *   解析函数
    */
    virtual bool    fromSvg(XMLDoc&, XMLNode*) = 0;
    /**
    *   数据转换成XMLNode,返回创建的节点
    */
    virtual XMLNode*    toSvg(XMLDoc&, XMLNode*) const = 0;
public:
    /**
    * @brief 读取xml节点并构建相应类型
    * @param  
    * @param  
    */
    static WDSvgObject* ParseSvgNode(XMLDoc&, XMLNode*);
    /**
     * @brief 将类型转为字符串
     * @param s 
     * @return 
    */
    static std::string  TypeToString(Type t);
    /**
     * @brief 将字符串转为类型
    */
    static Type         TypeFromString(const std::string& s);
protected:
    /**
    *   从字符串中获取一个数字字符串，返回截取后的字符串位置，即剪掉数字后的字符串
    */
    static const char* ParseNumber(const char* s, char* it);
};

using SvgAttr = WDSvgObject::Attr;

WD_NAMESPACE_END
