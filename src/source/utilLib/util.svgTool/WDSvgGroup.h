#pragma once

#include    "WDSvgObject.h"
WD_NAMESPACE_BEGIN

class SVT_TOOL_API WDSvgGroup : public WDSvgObject
{
public:
    WDSvgGroup();
public:
    std::string name;
public:
    /**
    *   解析函数
    */
    virtual bool        fromSvg(XMLDoc&, XMLNode*) override final;
    /**
    *   数据转换成XMLNode,返回创建的节点
    */
    virtual XMLNode*    toSvg(XMLDoc&, XMLNode*) const override final;

};

WD_NAMESPACE_END
