#include    "WDSvgPolyLine.h"
#include    "WDSvgXmlCreator.h"

WD_NAMESPACE_BEGIN

WDSvgPolyLine::WDSvgPolyLine()
    :WDSvgObject(T_PolyLine)
{}

size_t WDSvgPolyLine::toData(std::vector<FVec3>& data, int) const
{
    const auto& points = this->points();
    for (size_t i = 0; i < points.size(); i++)
    {
        data.push_back(FVec3(points.at(i)));
    }
    return  points.size();
}

size_t WDSvgPolyLine::computeDataLength(int) const
{
    return  points().size();
}

bool    WDSvgPolyLine::fromSvg(XMLDoc&, XMLNode* node)
{
    _attr.fromSvgAttrs(*node);

    XMLAttr*    points  =   node->first_attribute("points");
    if (points == nullptr)
        return  false;
    const char* pData   =   points->value();
    if (pData == nullptr)
        return  false;

    for (; *pData; )
    {
        char    szX[64] =   {0};
        char    szY[64] =   {0};
        pData   =   ParseNumber(pData, szX);
        pData   =   ParseNumber(pData, szY);
        double  x   =   atof(szX);
        double  y   =   atof(szY);
        _points.push_back(Vec3(x, y, 0));

        if (pData && *pData == '\0')
            return  true;

        /// 跳过空格
        while(pData && *pData && isspace(*pData)) 
            ++pData;
    }
    return  true;
}
XMLNode*WDSvgPolyLine::toSvg(XMLDoc& doc, XMLNode* parent) const
{
    std::vector<Vec2> points;
    const auto& ps  =   this->points();

    points.reserve(ps.size());
    for (size_t i = 0; i < ps.size(); i++)
    {
        points.push_back(ps[i].xy());
    }
    auto nd = createSvg(doc, parent);
    WDSvgXmlCreator::Node(doc, nd).attr("points", points);

    return nd;
}
WD_NAMESPACE_END