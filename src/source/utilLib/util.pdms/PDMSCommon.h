#pragma once
#include "NodeAttributeConfigInfo.h"
#include "core/extension/WDPluginFormat.h"
#include "core/businessModule/WDBMBase.h"
#include <fstream>
WD_NAMESPACE_BEGIN

class PDMSCommon
{
public:
    /**
    * @brief 使用一个或多个空格分割字符串
    * @param commandLine 
    * @param outStrVec 
    */
    static void AnalysisStringBySpace(const std::string& commandLine
        , StringVector& outStrVec);

    /**
    * @brief 从string类型转换为需要属性类型的WDBMTypeAttributeValue
    *  此函数主要为了满足PDMS脚本导入时部分规则下数据类型可能有多种的情况
    */
    static WDBMAttrValue PDMSStringCastToDataType(const std::string& strValue
        , const WDBMAttrValueType& type);
    /**
    * @brief 从string类型转换为需要属性类型的WDBMTypeAttributeValue
    *  此函数主要为了满足PDMS脚本导入时部分规则下数据类型可能有多种的情况
    */
    static std::string DataTypeCastToPDMSString(const WDBMAttrValue& value
        , const WDBMAttrValueType& type);

    static const std::string AntiAntoNodeName(const WD::WDNode& node);

    /**
     * @brief 读PDMS文件
     * @param fileName 
     * @param commands 
     * @param pLog 
     * @return 
    */
    // 保存一条命令及其在脚本中的行数
    // 脚本中存在 一条命令用 $ 做为换行符占据多行的情况，这里当作多行处理
    using CommandLine = std::pair<WD::uint, std::string>;
    using CommandLineVector = std::vector<CommandLine>;

    /**
    * @brief 文件的格式
    */
    enum class EncodingFormat
    {
        EF_Utf8Bom,
        EF_Utf8,
        EF_Ansi,
        EF_GB2312
    };
    static bool ReadFile(const std::string& fileName
        , CommandLineVector& outCommands
        , const EncodingFormat& format = EncodingFormat::EF_Utf8
        , WD::PluginLog* pLog = nullptr);
};

/**
 * @brief 规则管理类
 * 保存处理规则，调用处理规则
 * 自定义属性的处理(和string的互转)
*/
class RuleMgr
{
private:
    // 保存的value处理规则
    std::map<RuleType, std::shared_ptr<RuleBase>> _rules;
public:
    /**
    * @brief 添加规则处理算法,规则算法已存在时不做处理
    */
    void addRule(const RuleType& type, std::shared_ptr<RuleBase> pRule);
    /**
    * @brief 更改规则处理算法
    规则存在时更改处理算法
    不存在时添加规则处理算法
    */
    void changeRule(const RuleType& type, std::shared_ptr<RuleBase> pRule);

    /**
    * @brief 获取指定规则处理算法
    */
    std::shared_ptr<RuleBase> getRule(const RuleType& type);

    /**
    * @brief 重载,获取const的指定规则处理算法
    */
    std::shared_ptr<RuleBase> getRule(const RuleType& type) const;

    /**
    * @brief 根据指定的规则处理命令值
    * @param ruleType 规则
    * @param commandPair 属性
    * @param dataType 属性值类型
    * @param outValue 处理后的值
    * @return 处理成功?
    */
    bool processByRule(const RuleType& ruleType
        , const CommandPair& commandPair
        , const WDBMAttrValueType& dataType
        , WDBMAttrValue& outValue) const;

    /**
    * @brief 
    * @brief 根据指定的规则反处理值
    * @param ruleType 规则
    * @param value 值
    * @param outValue 处理后的值
    * @return 处理成功?
    */
    bool antiProcessByRule(const RuleType& ruleType
        , const WDBMAttrValue& value
        , std::string& outValue) const;

    void clear();
public:
    /**
     * @brief 属性值转换为PDMS的字符串
    */
    std::string processCustomAttrToPDMSString(const WDBMAttrValue& value
        , bool* bSuccess);
    /**
     * @brief PDMS的字符串转换为属性值
    */
    WDBMAttrValue processCustomAttrFromPDMSString(const CommandPair& commandPair
        , WDBMAttrValueType type
        , bool* bSuccess);
};

// （不支持的（节点类型和属性））的管理类
class UnSupportTypeAndAttrMgr
{
public:
    struct Attrs
    {
        // 当前类型是否支持
        bool bTypeSupport;
        // 当前类型不支持的属性列表
        std::set<std::string> attrs;
        Attrs(bool bTypeSupport = true) : bTypeSupport(bTypeSupport)
        {}
    };
    using Types = std::map<std::string, Attrs>;
    Types  unSupportAttrs;
public:
    /**
    * @brief 添加不支持的类型
    */
    void addUnSupportType(const std::string& type
        , int index);
    /**
    * @brief 添加不支持的属性
    */
    void addUnSupportAttr(const std::string& type
        , const std::string& attr
        , int index);

    void clear();
public:
    UnSupportTypeAndAttrMgr(PluginLog& log);
    ~UnSupportTypeAndAttrMgr();
private:
    PluginLog& _log;
};

/**
 * @brief 更新数据汇总
*/
class UpdateDataSummary
{
public:
    // 新建节点数据
    struct NewCommndData
    {
        WD::WDNode::WeakPtr     pParent;
        WD::WDNode::SharedPtr   pChild;
    };
    using NewCommndDatas = std::vector<NewCommndData>;
    // 新建节点列表
    NewCommndDatas newDatas;

    using DeleteCommndDatas = std::set<WD::WDNode::SharedPtr>;
    // 删除节点列表
    DeleteCommndDatas deleteDatas;

    // 更改节点数据
    using ChangeCommndData = std::vector<std::pair<std::string, WDBMAttrValue>>;
    using ChangeCommndDatas = std::map<WD::WDNode::SharedPtr, ChangeCommndData>;
    // 更改节点列表
    ChangeCommndDatas changeDatas;
public:
    inline void addNewCommondData(WD::WDNode::SharedPtr pChild
        , WD::WDNode::SharedPtr pParent)
    {
        if (pChild == nullptr || pParent == nullptr)
            return ;
        newDatas.emplace_back(NewCommndData());
        newDatas.back().pChild = pChild;
        newDatas.back().pParent = pParent;
    }
    inline void addDeleteCommondData(WD::WDNode::SharedPtr pNode)
    {
        if (pNode == nullptr)
            return ;
        deleteDatas.insert(pNode);
    }
    inline void addChangeCommondData(WD::WDNode::SharedPtr pNode
        , const std::string& attrName
        , const WDBMAttrValue& attrValue)
    {
        if (pNode == nullptr)
            return ;
        if (auto itr = changeDatas.find(pNode); itr != changeDatas.end())
            itr->second.emplace_back(std::make_pair(attrName, attrValue));
        else
            changeDatas.emplace(pNode, ChangeCommndData{std::make_pair(attrName, attrValue)});
    }
    inline void addChangeCommondData(WD::WDNode::SharedPtr pNode
        , const ChangeCommndData& attrs)
    {
        if (pNode == nullptr)
            return ;
        if (auto itr = changeDatas.find(pNode); itr != changeDatas.end())
            itr->second.insert(itr->second.end(), attrs.begin(), attrs.end());
        else
            changeDatas.emplace(pNode, attrs);
    }
public:
    inline void clear()
    {
        newDatas.clear();
        deleteDatas.clear();
        changeDatas.clear();
    }
    void applyDataToNode(WDBMBase* pMgr);
    /**
     * @brief 节点是否被标记为删除
    */
    bool isNodeMarkAsDelete(WD::WDNode::SharedPtr pNode);
};
WD_NAMESPACE_END
