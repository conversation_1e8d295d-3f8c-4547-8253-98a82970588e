#include "NodeAttributeConfigInfo.h"

WD_NAMESPACE_BEGIN
// 以一个或多个空格分割命令行
static void AnalysisStringBySpace(const std::string& commandLine, StringVector& strVec)
{
    strVec.clear();
    if (commandLine.empty())
        return;
    std::smatch smatch;
    // 匹配轴规则(匹配长度至少为1的空格)
    std::regex partten("\\s{1,}");
    // 解析
    std::sregex_iterator itr(commandLine.begin(), commandLine.end(), partten);
    for (; itr != std::sregex_iterator(); ++itr)
    {
        smatch = *itr;
        const std::string& str = smatch.prefix();
        if (!str.empty())
            strVec.push_back(str);
    }
    if (smatch.empty())
        return strVec.push_back(commandLine);

    const std::string& str = smatch.suffix();
    if (!str.empty())
        strVec.push_back(str);

    if (strVec.empty())
        return strVec.push_back(commandLine);
}

const NodeAttributeConfigInfo::Attribute* NodeAttributeConfigInfo::getNodeAttributeCast(const std::string& attributeName
    , const std::string& nodeType) const
{
    if (attributeName.empty() || nodeType.empty() || _nodeAttributeCastMap.empty())
        return nullptr;
    // 从MAP中寻找对应类型节点下的属性转换数据
    auto itr = _nodeAttributeCastMap.find(nodeType);
    if (itr != _nodeAttributeCastMap.end())
        for (const auto& each : itr->second)
            // 在类型的所有属性中查找需要的属性,不区分大小写
            if (_stricmp(each.namePDMS.c_str(), attributeName.c_str()) == 0 || _stricmp(each.nameWD.c_str(), attributeName.c_str()) == 0)
                return &each;
    return nullptr;
}

const NodeAttributeConfigInfo::Attributes* NodeAttributeConfigInfo::getNodeAttributesCast(const std::string& typeName) const
{
    if (typeName.empty())
        return nullptr;
    auto itr = _nodeAttributeCastMap.find(typeName);
    if (itr != _nodeAttributeCastMap.end())
        return  &(itr->second);
    return nullptr;
}

bool NodeAttributeConfigInfo::loadXml(const std::string& fileName)
{
    if (fileName.empty())
        return  false;
    //  清空map
    _nodeAttributeCastMap.clear();
    _nodeTypeCast.clear();

    WD::WDFileReader file(fileName);
    if (file.isBad())
        return false;
    file.readAll();

    WD::XMLDoc doc;
    doc.parse<0>((char*)file.data());
    WD::XMLNode* pRoot = doc.first_node("Root");
    if (pRoot == nullptr)
    {
        assert(false && "配置文件根节点为空!");
        return  false;
    }
    // 读取全局属性
    WD::XMLNode* pGlobalAttribute = pRoot->first_node("GlobalAttribute");
    std::map<std::string, Attribute> globalAtrributes;
    if (pGlobalAttribute !=  nullptr)
    {
        // 读取所有全局属性，存入map中
        for (WD::XMLNode* pGlobalAttributeCast = pGlobalAttribute->first_node("Attribute");
            pGlobalAttributeCast != nullptr;
            pGlobalAttributeCast = pGlobalAttributeCast->next_sibling("Attribute"))
        {
            Attribute   attr;
            loadAttribute(*pGlobalAttributeCast, attr);
            if (attr.valid())
                globalAtrributes[attr.namePDMS] = attr;
        }
    }
    // 读取节点所有配置
    // 获取全局属性
    for (WD::XMLNode* pNode = pRoot->first_node("Node"); pNode != nullptr; pNode = pNode->next_sibling("Node"))
    {
        NodeTypeCast nodeData;
        {
            WD::XMLAttr* pPdmsNodeTypeAtt = pNode->first_attribute("PDMS");
            WD::XMLAttr* pBMNodeTypeAtt = pNode->first_attribute("BM");
            //  节点相关属性都不能为空
            if (pPdmsNodeTypeAtt == nullptr
                || pBMNodeTypeAtt == nullptr)
            {
                assert(false && "XML节点属性配置错误!");
                continue;
            }
            // 保存节点类型转换信息
            nodeData.typePDMS = pPdmsNodeTypeAtt->value();
            nodeData.typeWD = pBMNodeTypeAtt->value();
            _nodeTypeCast.push_back(nodeData);
        }
        
        // 节点保存的属性转换Map
        Attributes attCastMap;
        {
            // 获取配置的全局属性
            WD::XMLNode* pGLAtt = pNode->first_node("GlobalAttributeUsing");
            if (pGLAtt != nullptr)
            {
                StringVector attributeNames;
                AnalysisStringBySpace(pGLAtt->value(), attributeNames);
                // 从保存的全局属性Map中查找对应属性
                for (const std::string& name : attributeNames)
                {
                    auto gloItr = globalAtrributes.find(name);
                    if (gloItr != globalAtrributes.end())
                        attCastMap.push_back(gloItr->second);
                }
            }
            // 读取自身配置的属性转换信息
            WD::XMLNode* pSelfAttribute = pNode->first_node("Attribute");
            if (pSelfAttribute != nullptr)
            {
                Attribute attr;
                for (; pSelfAttribute != nullptr; pSelfAttribute = pSelfAttribute->next_sibling("Attribute"))
                {
                    loadAttribute(*pSelfAttribute, attr);
                    if (attr.valid())
                        attCastMap.push_back(attr);
                }
            }
        }
        if (!nodeData.typeWD.empty())
            _nodeAttributeCastMap[nodeData.typeWD] = attCastMap;
        if (!nodeData.typePDMS.empty() && nodeData.typeWD != nodeData.typePDMS)
            _nodeAttributeCastMap[nodeData.typePDMS] = attCastMap;
    }
    return true;
}
bool NodeAttributeConfigInfo::loadAttribute(const WD::XMLNode& pNodeAttrXmlNode, Attribute& attribute)
{
    WD::XMLAttr* pXmlAttPdmsTypeName = pNodeAttrXmlNode.first_attribute("PDMS");
    WD::XMLAttr* pXmlAttBMTypeName = pNodeAttrXmlNode.first_attribute("BM");
    WD::XMLAttr* pXmlAttDataType = pNodeAttrXmlNode.first_attribute("DataType");
    WD::XMLAttr* pXmlAttRuleType = pNodeAttrXmlNode.first_attribute("Rule");
    //  必须要有的属性 : PDMS中的名称, BM中的属性名称, 属性类型, 处理规则
    if (pXmlAttPdmsTypeName == nullptr
        ||  pXmlAttBMTypeName == nullptr
        ||  pXmlAttDataType == nullptr
        ||  pXmlAttRuleType == nullptr)
    {
        assert(false && "XML属性配置错误!");
        return  false;
    }
    attribute.namePDMS = pXmlAttPdmsTypeName->value();
    attribute.nameWD = pXmlAttBMTypeName->value();
    attribute.attrType = WDBMAttrValue::TypeFromStr(pXmlAttDataType->value());
    attribute.ruleType = RuleType(atoi(pXmlAttRuleType->value()));
    return true;
}

WD_NAMESPACE_END
