#include "IncrementalCommandContext.h"
#include "core/businessModule/catalog/WDBMCatalog.h"
#include "core/common/WDStringConvert.h"

WD_NAMESPACE_BEGIN


IncrementalCommandContext::IncrementalCommandContext(WDCore& core, UpdateDataSummary& dataSummary)
    : _core(core), unSupport(this->logMgr), dataSummary(dataSummary), nodeMgr(core, "")
{
    auto pCatalogRoot = _core.getBMCatalog().root();
    if (pCatalogRoot != nullptr)
    {
        WDNode::RecursionHelpter(*pCatalogRoot, [] (std::map<std::string, WD::WDNode::SharedPtr>& nodeMap, WDNode& node)
        {
            if (!node.isNamed())
                return;
            nodeMap.emplace(node.srcName(), WDNode::ToShared(&node));
        }, _cataNodeMap);
    }
}
IncrementalCommandContext::~IncrementalCommandContext()
{
    nodeMgr.clear();
    ruleMgr.clear();
}

WDBMBase& IncrementalCommandContext::getModule() const
{
    return static_cast<WDBMBase&>(_core.getBMCatalog());
}

const bool IncrementalCommandContext::bNodeIsNewNode(WD::WDNode::SharedPtr pNode)
{
    if (pNode == nullptr)
        return false;
    return newNodes.find(pNode) != newNodes.end();
}

const   WD::WDNode::SharedPtr IncrementalCommandContext::getOperateNodeByName(const std::string& PDMSNodeName)  const
{
    StringVector valueVector;
    //  处理后的名称，索引类型时使用
    auto& nodeMap = nodeMgr.nodeNameMap();
    PDMSCommon::AnalysisStringBySpace(PDMSNodeName, valueVector);

    auto getNodeByName = [&] (const std::string& name) ->WD::WDNode::SharedPtr
    {
        //  查找节点
        if (auto itr = nodeMap.find(name);      itr != nodeMap.end())
            return  itr->second;
        else if (itr = _cataNodeMap.find(name); itr != _cataNodeMap.end())
            return  itr->second;
        return  nullptr;
    };


    // 只指定了引用节点名称和直接的名称引用类型
    // size() == 1 : /flowmeter/PT          ->  flowmeter/PT
    // size() == 2 : PTSET /flowmeter/PT    ->  flowmeter/PT
    if (valueVector.size() == 1 || valueVector.size() == 2)
    {
        //  获取节点名称
        std::string& refNodeName = valueVector.back();
        if (!refNodeName.empty() && refNodeName.front() == '/')
            refNodeName.erase(refNodeName.begin());
        //  查找节点
        return getNodeByName(refNodeName);
    }
    // 引用指定节点下的第几个类型的节点类型   ->  索引引用默认祖宗节点有类型
    // BTSET 1 of CATEGORY /flowmeter
    else if ((valueVector.size() - 2) % 3 == 0
        && valueVector.size() >= 5
        && (valueVector[2] == "OF" || valueVector[2] == "of"))
    {
        //  计算索引层级
        int levelCount = (int)(valueVector.size() - 2) / 3;
        for (size_t i = 0; i <= levelCount; i++)
            //  3 * i 下标的值为第 i 层级的节点类型，这里将其转为业务中的节点类型
            valueVector[3 * i] = keyWordXMLMgr.getWDTypeByPDMSType(valueVector[3 * i]);
        //  去掉名称前的 / 
        std::string& nodeNameStr = valueVector.back();
        if (!nodeNameStr.empty() && nodeNameStr.front() == '/')
            nodeNameStr.erase(nodeNameStr.begin());
        //  查找需要节点的祖宗节点
        WDNode::SharedPtr pCurNode = getNodeByName(nodeNameStr);
        if (pCurNode == nullptr)
            return nullptr;

        size_t vecSize = valueVector.size();
        bool bOk;
        for (size_t level = 0; level < levelCount; level++)
        {
            //  计算当前层级下的节点和类型索引的下标
            auto refTypeIndex = vecSize - 2 - level * 3 - 3;
            auto refIndexIndex = vecSize - 2 - level * 3 - 2;
            //  得到引用节点类型的int值
            //  根据下标拿到索引值
            auto refNodeIndex = FromString<size_t>(valueVector[refIndexIndex], &bOk);
            if (!bOk)
            {
                assert(false && "类型转换失败!");
                return nullptr;
            }
            // 查询节点
            pCurNode = RefTypeTreat::IndexRefHandler(*pCurNode, valueVector[refTypeIndex], refNodeIndex, &dataSummary);
            if (pCurNode == nullptr)
                return nullptr;
        }
        return pCurNode;
    }
    assert(false && "未支持的引用命令!");
    return nullptr;
}
void    IncrementalCommandContext::removeOperateNodeByName(const std::string& nodeName)
{
    auto& nodeMap = nodeMgr.nodeNameMap();
    if (nodeName.empty())
    {
        assert(false);
        return;
    }
    //  查找节点
    if (auto itr = nodeMap.find(nodeName);       itr != nodeMap.end())
        nodeMap.erase(itr);
    else if (itr = _cataNodeMap.find(nodeName);  itr != _cataNodeMap.end())
        _cataNodeMap.erase(itr);
}
void    IncrementalCommandContext::changeOperateNodeName(const std::string& prevName, const std::string& newName)
{
    if (newName.empty())
    {
        removeOperateNodeByName(prevName);
    }
    auto& nodeMap = nodeMgr.nodeNameMap();
    //  查找节点
    if (auto itr = nodeMap.find(newName);       itr != nodeMap.end())
    {
        assert(false && "名称已存在!");
        return;
    }
    else if (itr = _cataNodeMap.find(newName);  itr != _cataNodeMap.end())
    {
        assert(false && "名称已存在!");
        return;
    }
    //  查找节点
    if (auto itr = nodeMap.find(prevName);       itr != nodeMap.end())
    {
        auto pNode = itr->second;
        nodeMap.erase(itr);
        if (pNode != nullptr)
            nodeMap.emplace(newName, pNode);
    }
    else if (itr = _cataNodeMap.find(prevName);  itr != _cataNodeMap.end())
    {
        auto pNode = itr->second;
        _cataNodeMap.erase(itr);
        if (pNode != nullptr)
            _cataNodeMap.emplace(newName, pNode);
    }
}
bool    IncrementalCommandContext::addOperateNode(const WD::WDNode::SharedPtr pNewNode, const std::string& name)
{
    if (pNewNode == nullptr)
        return false;

    auto key = name;
    if (key.empty())
    {
        if (!pNewNode->isNamed())
            return false;
        key = pNewNode->name();
    }

    auto& nodeMap = nodeMgr.nodeNameMap();
    //  查找节点
    if (auto itr = nodeMap.find(key);  itr != nodeMap.end())
        return false;
    else if (itr = _cataNodeMap.find(key); itr != _cataNodeMap.end())
        return false;
    nodeMap.emplace(key, pNewNode);
    return true;
}
void IncrementalCommandContext::clear()
{
    nodeMgr.clear();
    logMgr.clear();
    refTypeHandle.clear();
}
WD_NAMESPACE_END
