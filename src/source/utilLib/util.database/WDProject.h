#pragma once
#include "WDDataBaseTool.h"

WD_NAMESPACE_BEGIN

/// ��һ����Ŀ���г����������ݿ������Ҫ������ƿ����Ԫ�������Լ���Ŀ��Ϣ����

class DB_API WDProject : public WDObject
{
public:
    using Param     =   WDDataBaseTool::Param;
    using Nodes     =   WDDataBaseTool::Nodes;
    using IStream   =   WDDataBaseTool::Stream;
    using Version   =   WDDataBaseTool::Version;
    using ArrayUuid =   WDDataBaseTool::ArrayUuid;
    using Strings   =   WDDataBaseTool::Strings;
    using MapNodes  =   WDDataBaseTool::MapNodes;
public:
    WDProject(WDCore& core);
    WDProject(WDCore& core, WDDataBaseToolSPtr design, WDDataBaseToolSPtr catalog);
    ~WDProject();

public:
    // ������ƿ��Ԫ���⵽core��
    bool    loadModules();
    bool    loadDesignModule();
    bool    loadCatalogModule();

    inline WDDataBaseToolSPtr designTool()
    {
        return _design;
    }
    inline WDDataBaseToolSPtr catalogTool()
    {
        return _catalog;
    }
    /**
     * @brief ��ѯԪ���⣬������Ϊpr��
     * @param ids �ڵ�id
     * @param stream 
     * @param version pr�汾
     * @return �ֽ���
    */
    size_t  fetchFromCatalogToPr(const WDUuid& id, IStream& stream, Version version);
    /**
    * @brief ��ѯ��ƿ⣬������Ϊpr��
    * @param ids �ڵ�id
    * @param stream 
    * @param version pr�汾
    * @return �ֽ���
    */
    size_t  fetchFromDesignToPr(const WDUuid& id, IStream& stream, Version version);
    /**
     * @brief ��ѯԪ���⣬������Ϊpr��
     * @param names �ڵ���
     * @param stream 
     * @param version pr�汾
     * @return �ֽ���
    */
    size_t  fetchFromCatalogToPr(const char* name, IStream& stream, Version version);
    /**
    * @brief ��ѯ��ƿ⣬������Ϊpr��
    * @param names �ڵ���
    * @param stream 
    * @param version pr�汾
    * @return �ֽ���
    */
    size_t  fetchFromDesignToPr(const char* name, IStream& stream, Version version);

    // ������wd��
    size_t  fetchFromCatalogToStream(const WDUuid& id, IStream& stream);
    size_t  fetchFromDesignToStream(const WDUuid& id, IStream& stream);
    size_t  fetchFromCatalogToStream(const char* name, IStream& stream);
    size_t  fetchFromDesignToStream(const char* name, IStream& stream);
    /**
     * @brief ���ؽڵ��֧��refNode����˿����õ���Ϊpr�Ĺ��̲�����core��ǰ��������refNode
     * @param node �ڵ㣨��֧��
     * @param refNodes refNode��������ֹ��ǰ�ͷ��Լ������ظ�
     * @return �Ƿ�ÿ��refNode���سɹ�
    */
    bool    loadRefNodeForBranch(WDNode& node, MapNodes& refNodes);
protected:
    WDCore&             _core;
    // Ԫ����/��ƿ�ĸ��ڵ�
    WDNodeWPtr          _catalogRoot;
    WDNodeWPtr          _designRoot;
    // ��ƿ� & Ԫ����
    WDDataBaseToolSPtr  _design         =   nullptr;
    WDDataBaseToolSPtr  _catalog        =   nullptr;

    // ����֮�󻹻���һ����Ϣ�⣬��¼��Ŀ�����һЩ��Ϣ��������ע��Ȩ�޵�
};


WD_DECL_OBJECT_SMATR_POINTER(WDProject)
WD_NAMESPACE_END
