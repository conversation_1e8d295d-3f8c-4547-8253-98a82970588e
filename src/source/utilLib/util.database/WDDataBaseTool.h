#pragma once

#include "WDSession.h"
#include "WDDataBase.h"
#include "WDCore.h"
#include "common/WDFileReader.hpp"
#include "node/WDNode.h"
#include "common/WDObject.h"
#include "property/WDProperty.h"
#include "common/WDMemReader.hpp"
#include "businessModule/catalog/WDBMCatalogMgr.h"
#include "../util.pr/WDPrTool.h"
#include "node/WDComponentMaterial.h"
#include "businessModule/WDBMComponent.h"
#include "businessModule/WDComponentPLineSet.h"
#include "core/common/WDMD5.h"
#include "businessModule/WDBMRefCollector.h"
#include "../util.wd/WDWdTool.h"

#define MAX_SQL_LEN 256

WD_NAMESPACE_BEGIN

static const WDUuid GUID_MySqlTool  =   WDUuid::FromString("98083D89-E390-4CD9-A231-0ED34B859CFD");
static const WDUuid GUID_SqliteTool =   WDUuid::FromString("07B4C551-1EFE-4A66-837D-323E6D5DFFD7");

/// �������л�/�����л��ڵ���Ϣ�����ݿ⹤��
WD_DECL_CLASS_UUID(WDDataBaseTool,"2CCE51B3-5C12-477D-9A26-D02E5B978684");
class DB_API WDDataBaseTool : public WDObject
{
    WD_DECL_OBJECT(WDDataBaseTool)
public:
    // ������
    class   Stream :public std::vector<char>
    {
    public:
        int copyToBuffer(void* buf, int bufLen)
        {
            if (empty() || bufLen < size() || buf == nullptr)
                return 0;
            memcpy_s(buf, bufLen, &front(), size());
            return (int)size();
        }
        int readFromBuffer(const void* buf, int bufLen)
        {
            if (bufLen <= 0 || buf == nullptr)
                return bufLen;
            resize(bufLen);
            memcpy_s(&front(), size(), buf, bufLen);
            return (int)size();
        }
        void* dataPtr()
        {
            if(empty())
                return nullptr;
            return &front();
        }
    };

    // ����д��pr,wd���ļ�ʱ��Ϊ�ļ�ͷ
    struct  WDFormatHeader
    {
        char    _magic[4]   =   "wd";
        int     _version    =   1;
        char    _author[64] =   u8"����������Ϣ�����ɷ����޹�˾";
    };

    using   MapObject       =   std::map<WDUuid, WDObjectSPtr>;
    using   Strings         =   WDSession::Strings;
    using   BatchStrings    =   WDSession::BatchStrings;
    using   Record          =   WDSession::Record;
    using   Records         =   WDSession::Records;
    using   ArrayUuid       =   std::vector<WDUuid>;
    using   Nodes           =   std::vector<WDNodeSPtr>;
    using   StreamBuffer    =   std::vector<char>;
    using   Objects         =   std::vector<WDObjectSPtr>;
    using   Param           =   std::map<std::string, std::string>;
    using   StreamSPtr      =   std::shared_ptr<Stream>;
    using   ObjectMap       =   std::map<WDUuid, ObjectPtr>;
    using   Version         =   WDPrTool::PrVersion;
    using   MapNodes        =   std::map<WDUuid, WDNodeSPtr>;
public:
    WDDataBaseTool(WDCore& core, bool isCatalog);
    virtual ~WDDataBaseTool();
public:
    /**
     * @brief �������ݿ�
     * @param  ���ݿ����Ӳ���
     * @return �Ƿ�ɹ�����
    */
    virtual bool connectDB(const Param& param) = 0;
    /**
     * @brief ��ѯ�����нڵ�
     * @param objs �������
     * @param  nodeUuids �ڵ�id�ļ���
     * @return �ɹ������Ľڵ���
    */
    size_t  fetchNodesByNodeUuid(Objects& objs, const Strings& nodeUuids);
    /**
     * @brief ��ѯ�����нڵ�
     * @param objs ���
     * @param nodeUuids �ڵ�id�ļ���
     * @return �ɹ������Ľڵ���
    */
    size_t  fetchNodesByNodeUuid(Objects& objs, const ArrayUuid& nodeUuids)
    {
        Strings ids;
        for (const auto& id : nodeUuids)
        {
            ids.push_back(id.toString());
        }
        return fetchNodesByNodeUuid(objs, ids);
    }
    /**
    * @brief ���ݽڵ�����ѯ�ڵ�
    * @param names �ڵ���(������������ַ�����Ҫ�Ƚ��ַ���תΪutf8��ʽ)
    */
    virtual bool fetchNodesByNodeName(Objects& objs, const Strings& names);
    /**
    * @brief ͨ���ڵ�uuid��ѯ���ڵ㣬�����ڵ����뵽�ֽ���
    * @param nodeId �ڵ�uuid
    * @param stream ������
    * @return д������������ݴ�С
    */
    size_t  fetchByIdToStream(const char* nodeUuid, Stream& stream);
    /**
    * @brief ͨ���ڵ�name��ѯ���ڵ㣬�����ڵ����뵽�ֽ���
    * @param name �ڵ�name
    * @param stream ������ 
    * @return д���ֽ����е��������ֽ���
    */
    size_t  fetchByNameToStream(const char* name, Stream& stream);
    /**
     * @brief ͨ���ڵ�id��ȡ�ڵ㲢д��pr��
     * @param nodeUuid �ڵ�id
     * @param stream pr��
     * @param prVersion pr�汾
     * @return �ֽ���
    */
    size_t  fetchByIdToPr(const char* nodeUuid, Stream& stream, Version prVersion);
    /**
     * @brief ͨ���ڵ�����ȡ�ڵ㲢д��pr��
     * @param name �ڵ���
     * @param stream pr��
     * @param prVersion pr�汾
     * @return �ֽ���
    */
    size_t  fetchByNameToPr(const char* name, Stream& stream, Version prVersion);
    /**
    * @brief ���ڵ�д�����ݿ���
    * @param nodes �ڵ㼯
    * @param asRoot �Ƿ���Ϊ���ڵ�д��
    */
    bool    writeNodes(const Nodes& nodes, bool useTransaction = true, bool asRoot = false);
    /**
     * @brief ���ֽ����е�����д�����ݿ���
     * @param stream ��
    */
    bool    writeNodesFromWdStream(const Stream& stream);
    /**
    * @brief ɾ������ڵ�
    * @param nodeUuids �ڵ�uuid�ļ���
    * @return �ɹ�ɾ���ĸ���
    */
    bool    deleteNodes(const Strings& nodeUuids, bool useTransaction = true);
    /**
     * @brief ������ĳ���ڵ��滻��
     * @param nodeUuid Ŀ��ڵ�uuid
    */
    bool    replaceNode(WDNodeSPtr node);
    /**
    * @brief ������ݿ�
    */
    bool    clean();
    /**
     * @brief ��ջ���
    */
    void    flush();
    /**
    * @brief ��ѯ�ڵ��Ƿ�����ڵ�ǰ���ݿ���
    * @param nodeId Ŀ��ڵ�uuid
    */
    bool    nodeExist(const char* nodeUuid);
    /**
    * @brief ��ѯ�ڵ��Ƿ�����ڵ�ǰ���ݿ���
    * @param nodeUuid Ŀ��ڵ�uuid
    */
    bool    nodeExist(const WDUuid& nodeUuid);
    /**
     * @brief ����ĳ������Ƿ����
     * @param componentUuid ���id
    */
    bool    componentExist(const WDUuid& componentUuid);
    /**
     * @brief ����ĳ�������Ƿ����
     * @param textureUuid ����id
    */
    bool    textureExist(const WDUuid& textureUuid);
    /**
    * @brief ��ѯ���޲�����
    * @param sql sql���
    * @return ��ѯ���
    */
    inline Records query(const char* sql)
    {
        return session()->isValid() ? session()->query(sql) : Records{};
    }
    /**
    * @brief ִ��sql���
    * @param sql sql���
    * @param callback �ص��������ڴ���ÿ�����ؽ��
    * @param arg �ص������Ĵ���/��������
    */
    inline bool execSql(const char* sql, int(*callback)(void*, int, char**, char**) = nullptr, void* arg = nullptr)
    {
        return session()->isValid() ? session()->executeSql(sql, callback, arg) : false;
    }
    /**
     * @brief ��sql�ļ��м���ģ��
     * @param fileName Ŀ���ļ�·��
     * @param designModule ѡ����ƿ�/Ԫ���⣬Ĭ����ƿ�
    */
    bool    importFromSqlFile(const char* sqlFileName);
    /**
     * @brief ��wd�ļ�����ģ��
     * @param wdFileName Ŀ���ļ�·��
    */
    bool    importFromWdFile(const char* wdFileName);
    /**
     * @brief ��ģ�͵�����wd�ļ�
     * @param wdFileName Ŀ���ļ�·��
    */
    size_t  exportToWdFile(const char* wdFileName);
    /**
    * @brief ��Ч��
    */
    inline bool isValid()
    {
        return session() != nullptr && dataBase() != nullptr && session()->isValid() && dataBase()->isValid();
    }
    /**
    * @brief ��ȡ���ڵ㣨һ��������
    * @param nodes ���д��
    * @return ���ڵ����
    */
    size_t  getRoots(Nodes& nodes);
    /**
    * @brief ���������������ӵ����ݿ�󣬴���ҵ�������д��ڵ���Ϣ
    */
    virtual bool createTables() = 0;
    /**
     * @brief ҵ����Ƿ񴴽�
    */
    bool    tablesExist();
    /**
     * @brief �������ݵ��ļ���mysql->sql�ļ���sqlite->db�ļ���
     * @param fileName �ļ���
    */
    bool    saveToFile(const char* fileName);
    /**
     * @brief ��ȡ�ڵ㼰������ڵ��refNode�������ء�
     * Ԫ����/��ƿ�ֻ�ֱܷ���cRef/dRef
     * @param node Ŀ��ڵ㣨��֧��
     * @refNodes ���ö�ȡ������refNode����ֹrefNode��ǰ�ͷţ�ͬʱ����refNode�ظ������
     * return �Ƿ�ÿ��refNode���ɹ���ȡ
    */
    bool    loadRefNodeForBranch(WDNode& node, MapNodes& refNodes);
    /**
     * @brief ͨ����������ѯ����uuid�������ڵ㡢����ȣ�
     * @param name ������
     * @return ����uuid�ļ���
    */
    ArrayUuid   getObjectUuidByName(const char* name);
    /**
     * @brief ͨ������uuid��ѯ������
     * @param uuid ����uuid
     * @return �������ļ���
    */
    Strings getObjectNameByUuid(const char* uuid);
    Strings getObjectNameByUuid(const WDUuid& uuid)
    {
        return getObjectNameByUuid(uuid.toString().c_str());
    }
private:
    /**
    * @brief ͨ���ڵ�uuid��ѯ���ڵ�
    * @param nodeUuid �ڵ�uuid
    * @return Ŀ��ڵ�
    */
    WDNodeSPtr  fetchNode(const char* nodeUuid);
    /**
    * @brief ͨ���ڵ�uuid��ѯ���ڵ�
    * @param nodeUuid �ڵ�uuid
    * @return Ŀ��ڵ�
    */
    WDNodeSPtr  fetchNode(const WDUuid& nodeUuid);
    /**
    * @brief ��������uid��ѯ���ԣ����ҵ�����
    * @param propertyId ����uid
    * @param parent ������
    */
    void    fetchProperty(const WDUuid& propertyUuid, WDPropertyGroup& parent);
    /**
    * @brief ������Ӧ����ֵ������ö������飬�����uid��ѯ������
    * @param propertyId ����uid
    * @param parent ������
    * @param type, name, value ����ֵ
    */
    void    fetchProperty(const WDUuid& propertyUuid
        , WDPropertyGroup& parent
        , const std::string& type
        , const std::string& name
        , const std::string& value);
    /**
    * @brief ��ȡ���������ĸ�����
    * @param propertyUuid ����id
    * @param rootPropertyUuid ���������id
    */
    bool    fetchRootProperty(const WDUuid& propertyUuid, WDUuid& rootPropertyUuid);
    /**
    * @brief �������uid��ѯ���
    * @param componentId ���uid
    * @return �������
    */
    WDNodeComponentSPtr fetchComponent(const WDUuid& componentUuid);
    /**
    * @brief ��������uid��ѯ����
    * @param textureId ����uid
    * @param mapObj ������������Ų�����������
    */
    void    fetchTexture(const WDUuid& textureUuid, MapObject& mapObj);
    /**
    * @brief ���ڵ�д�����ݿ���
    * @param pNode Ŀ��ڵ�
    */
    bool    writeNode(WDNodeSPtr pNode);
    /**
    * @brief ��������д�����ݿ�
    * @param ptyGroup ������
    * @param parentId ������id
    */
    bool    writePropertyGroup(WDPropertyGroup* ptyGroup, const WDUuid& parentUuid);
    /**
    * @brief �ռ������Ϣ��д�����ݿ�
    * @param pCom �������ָ��
    */
    bool    writeComponent(WDNodeComponent* pCom);
    /**
    * @brief ����������󣬻�ȡ������µ��������д�����ݿ�
    * @param pCom �������
    */
    bool    writeTexture(WDNodeComponent* pCom);
    /**
    * @brief �����ݿ�ɾ�����ԣ��ݹ�ɾ�������ԣ�
    * @param uuid ����uuid
    */
    bool    deleteProperty(const WDUuid& nodeUuid);
    /**
    * @brief �����ݿ�ɾ�����
    * @param uuid ���uuid
    */
    bool    deleteComponent(const WDUuid& nodeUuid);
    /**
    * @brief �����ݿ�ɾ���ڵ㣨�ݹ�ɾ���ӽڵ㣩
    * @param uuIds �ڵ��uuid
    */
    bool    deleteNode(const char* nodeUuid)
    {
        return deleteNode(WDUuid::FromString(nodeUuid));
    }
    /**
    * @brief �����ݿ�ɾ���ڵ㣨�ݹ�ɾ���ӽڵ㣩
    * @param uuIds �ڵ��uuid
    */
    bool    deleteNode(const WDUuid& nodeUuid);
    /**
    * @brief  �����ݿ�ɾ������
    * @param uuid ����uuid
    */
    bool    deleteTexture(const WDUuid& nodeUuid);
public:
    inline static constexpr const char* NodeTableName()
    {
        return _NodeTableName;
    }
    inline static constexpr const char* ComponentTableName()
    {
        return _ComponentTableName;
    }
    inline static constexpr const char* PropertyTableName()
    {
        return _PropertyTableName;
    }
    inline static constexpr const char* TextureTableName()
    {
        return _TextureTableName;
    }
    inline static constexpr const char* Node2PropertyTableName()
    {
        return _Node2PropertyTableName;
    }
    inline static constexpr const char* Component2PropertyTableName()
    {
        return _Component2PropertyTableName;
    }
    inline static constexpr const char* Texture2PropertyTableName()
    {
        return _Texture2PropertyTableName;
    }
    inline static constexpr const char* Node2ComponentTableName()
    {
        return _Node2ComponentTableName;
    }
    inline static constexpr const char* Component2TextureTableName()
    {
        return _Component2TextureTableName;
    }
    inline static constexpr const char* BigDataTableName()
    {
        return _BigDataTableName;
    }
    inline static WDUuid DefaultUuid()
    {
        return WDUuid::FromString(_DefaultUuid);
    }
protected:
    inline std::shared_ptr<WDSession> session()
    {
        return _session;
    }
    inline std::shared_ptr<WDDataBase> dataBase()
    {
        return _dataBase;
    }
    /**
    * @brief Ԥ����sql
    * @param sql sql���
    * @return Ԥ�������
    */
    WDStmtSPtr getStmt(const char* sql)
    {
        return session()->prepareSql(sql);
    }
protected:
    // ���ڱ�־�ϴ�����ݣ������������ݣ������ﵽ����
    static constexpr const char*    _BigDataTag                     =   ":VERY BIG DATA:";
    static const  long              _BigDataSize                    =   1024;
    // ���ݿ��еı���
    static constexpr const char*    _NodeTableName                  =   "nodes";
    static constexpr const char*    _ComponentTableName             =   "components";
    static constexpr const char*    _PropertyTableName              =   "properties";
    static constexpr const char*    _Node2PropertyTableName         =   "node2Property";
    static constexpr const char*    _Component2PropertyTableName    =   "component2Property";
    static constexpr const char*    _Node2ComponentTableName        =   "node2Component";
    static constexpr const char*    _TextureTableName               =   "textures";
    static constexpr const char*    _Texture2PropertyTableName      =   "texture2Property";
    static constexpr const char*    _Component2TextureTableName     =   "component2Texture";
    static constexpr const char*    _BigDataTableName               =   "bigDatas";
    // ����
    static const ushort             _TablesCount                    =   10;

    // Ĭ��uuid�����ڱ�־���󲻴��ڵ����
    static constexpr const char*    _DefaultUuid                    =   "{00000000-0000-0000-0000-000000000000}";
    static const uint               _SizeOfUuid                     =   sizeof(WDUuid);// UUID�ĳ���

protected:  
    std::shared_ptr<WDDataBase> _dataBase   =   nullptr; // ��ƿ�
    std::shared_ptr<WDSession>  _session    =   nullptr; // ��ǰʹ�õ�����

    WDCore& _core;

    // �Ƿ�ΪԪ��/��ƿ�
    bool    _isCatalogModule =   false;

protected:
    // ���ÿ����Ĺ̶�������䣬��Ҫ����ҵ��������ã����������£����ڲ�ͬ���ݿ���һ���ģ�
    static constexpr const char*    _SqlInsertNodes         =   "INSERT INTO `nodes` VALUES(?, ?)";
    static constexpr const char*    _SqlInsertProperties    =   "INSERT INTO `properties` VALUES(? , ? , ? , ? , ? )";
    static constexpr const char*    _SqlInsertComponents    =   "INSERT INTO `components` VALUES(?, ?)";
    static constexpr const char*    _SqlInsertNode2Pty      =   "INSERT INTO `node2Property` VALUES(?, ?)";
    static constexpr const char*    _SqlInsertCpt2Pty       =   "INSERT INTO `component2Property` VALUES(?, ?)";
    static constexpr const char*    _SqlInsertNode2Cpt      =   "INSERT INTO `node2component` VALUES(?, ?)";
    static constexpr const char*    _SqlInsertTextures      =   "INSERT INTO `textures` VALUES(?, ?)";
    static constexpr const char*    _SqlInsertTex2Pty       =   "INSERT INTO `texture2Property` VALUES(?, ?)";
    static constexpr const char*    _SqlInsertCpt2Tex       =   "INSERT INTO `component2Texture` VALUES(?, ?)";
    static constexpr const char*    _SqlInsertBigData       =   "INSERT INTO `bigDatas` VALUES(?, ?)";

    // ���ÿ����Ĺ̶���ѯ��䣬��Ҫ����ҵ��������ã����������£����ڲ�ͬ���ݿ���һ���ģ�
    static constexpr const char*    _SqlSelectNodePtyId     =   "SELECT propertyId FROM `node2Property` WHERE nodeId = ? LIMIT 1";
    static constexpr const char*    _SqlSelectNodeCpnId     =   "SELECT componentId FROM `node2Component` WHERE nodeId = ?";
    static constexpr const char*    _SqlSelectNodeChildId   =   "SELECT objId FROM `nodes` WHERE pId = ?";
    static constexpr const char*    _SqlSelectPtyInfo       =   "SELECT type, name, value FROM `properties` WHERE objId = ? LIMIT 1";
    static constexpr const char*    _SqlSelectPtyChildInfo  =   "SELECT objId, type, name, value FROM `properties` WHERE pId = ?";
    static constexpr const char*    _SqlSelectCpnTypeId     =   "SELECT typeId FROM `components` WHERE objId = ? LIMIT 1";
    static constexpr const char*    _SqlSelectCpnPtyId      =   "SELECT propertyId FROM `component2Property` WHERE componentId = ? LIMIT 1";
    static constexpr const char*    _SqlSelectCpnTexId      =   "SELECT textureId FROM `component2Texture` WHERE componentId = ?";
    static constexpr const char*    _SqlSelectTexTypeId     =   "SELECT typeId FROM `textures` WHERE objId = ? LIMIT 1";
    static constexpr const char*    _SqlSelectTexPtyId      =   "SELECT propertyId FROM `texture2Property` WHERE textureId = ? LIMIT 1";
    static constexpr const char*    _SqlSelectBigData       =   "SELECT data FROM `bigDatas` WHERE propertyId = ? LIMIT 1";
    static constexpr const char*    _SqlSelectParentPty     =   "SELECT pId FROM `properties` WHERE objId = ? LIMIT 1";
    static constexpr const char*    _SqlSelectPtyByType     =   "SELECT objId FROM `properties` WHERE type = ?";
    static constexpr const char*    _SqlSelectPtyByName     =   "SELECT objId FROM `properties` WHERE name = ?";
    static constexpr const char*    _SqlSelectPtyByValue    =   "SELECT objId FROM `properties` WHERE type = ? and name = ? and value = ?";
    static constexpr const char*    _SqlSelectNodeByPtyId   =   "SELECT nodeId FROM `node2Property` WHERE propertyId = ?";
    static constexpr const char*    _SqlSelectCpnByPtyId    =   "SELECT componentId FROM `component2Property` WHERE propertyId = ?";
    static constexpr const char*    _SqlSelectTexByPtyId    =   "SELECT textureId FROM `texture2Property` WHERE propertyId = ?";

    // ɾ��
    static constexpr const char*    _SqlDelNodePtyByNodeId  =   "DELETE FROM `node2Property` WHERE nodeId = ?";
    static constexpr const char*    _SqlDelCpnPtyByCpnId    =   "DELETE FROM `component2Property` WHERE componentId = ?";
    static constexpr const char*    _SqlDelTexPtyByTexId    =   "DELETE FROM `texture2Property` WHERE textureId = ?";
    static constexpr const char*    _SqlDelNodeById         =   "DELETE FROM `nodes` WHERE objId = ?";
    static constexpr const char*    _SqlDelCpnById          =   "DELETE FROM `components` WHERE objId = ?";
    static constexpr const char*    _SqlDelPtyById          =   "DELETE FROM `properties` WHERE objId = ?";
    static constexpr const char*    _SqlDelTexById          =   "DELETE FROM `textures` WHERE objId = ?";
    static constexpr const char*    _SqlDelBigDataByPtyId   =   "DELETE FROM `bigDatas` WHERE propertyId = ?";
    static constexpr const char*    _SqlDelNodeCpnByNodeId  =   "DELETE FROM `node2Component` WHERE nodeId = ?";
    static constexpr const char*    _SqlDelCpnTexByCpnId    =   "DELETE FROM `component2Texture` WHERE componentId = ?";

    // ����
    static constexpr const char*    _SqlCountNodeById       =   "SELECT COUNT(*) FROM `nodes` WHERE objId = ?";
    static constexpr const char*    _SqlCountTexById        =   "SELECT COUNT(*) FROM `textures` WHERE objId = ?";
    static constexpr const char*    _SqlCountCpnById        =   "SELECT COUNT(*) FROM `components` WHERE objId = ?";
    static constexpr const char*    _SqlCountNodeByCpnId    =   "SELECT COUNT(*) FROM `node2Component` WHERE componentId = ?";
    static constexpr const char*    _SqlCountCpnByTexId     =   "SELECT COUNT(*) FROM `component2Texture` WHERE textureId = ?";
    static constexpr const char*    _SqlSelectRootNodes     =   "SELECT objId FROM `nodes` WHERE pId NOT IN (SELECT objId FROM `nodes`)";

public:
    // ����������д
    virtual  const char*   SqlInsertNodes()
    {
        return _SqlInsertNodes;
    }
    virtual  const char*   SqlInsertProperties()
    {
        return _SqlInsertProperties;
    }
    virtual  const char*   SqlInsertComponents()
    {
        return _SqlInsertComponents;
    }
    virtual  const char*   SqlInsertNode2Pty()
    {
        return _SqlInsertNode2Pty;
    }
    virtual  const char*   SqlInsertCpt2Pty()
    {
        return _SqlInsertCpt2Pty;
    }
    virtual  const char*   SqlInsertNode2Cpt()
    {
        return _SqlInsertNode2Cpt;
    }
    virtual  const char*   SqlInsertTextures()
    {
        return _SqlInsertTextures;
    }
    virtual  const char*   SqlInsertTex2Pty()
    {
        return _SqlInsertTex2Pty;
    }
    virtual  const char*   SqlInsertCpt2Tex()
    {
        return _SqlInsertCpt2Tex;
    }
    virtual  const char*   SqlInsertBigData()
    {
        return _SqlInsertBigData;
    }

    virtual  const char*   SqlSelectNodePtyId()
    {
        return _SqlSelectNodePtyId;
    }
    virtual  const char*   SqlSelectNodeCpnId()
    {
        return _SqlSelectNodeCpnId;
    }
    virtual  const char*   SqlSelectNodeChildId()
    {
        return _SqlSelectNodeChildId;
    }
    virtual  const char*   SqlSelectPtyInfo()
    {
        return _SqlSelectPtyInfo;
    }
    virtual  const char*   SqlSelectPtyChildInfo()
    {
        return _SqlSelectPtyChildInfo;
    }
    virtual  const char*   SqlSelectCpnTypeId()
    {
        return _SqlSelectCpnTypeId;
    }
    virtual  const char*   SqlSelectCpnPtyId()
    {
        return _SqlSelectCpnPtyId;
    }
    virtual  const char*   SqlSelectCpnTexId()
    {
        return _SqlSelectCpnTexId;
    }
    virtual  const char*   SqlSelectTexTypeId()
    {
        return _SqlSelectTexTypeId;
    }
    virtual  const char*   SqlSelectTexPtyId()
    {
        return _SqlSelectTexPtyId;
    }
    virtual  const char*   SqlSelectBigData()
    {
        return _SqlSelectBigData;
    }
    virtual  const char*   SqlSelectParentPty()
    {
        return _SqlSelectParentPty;
    }
    virtual  const char*   SqlSelectPtyByType()
    {
        return _SqlSelectPtyByType;
    }
    virtual  const char*   SqlSelectPtyByName()
    {
        return _SqlSelectPtyByName;
    }
    virtual  const char*   SqlSelectPtyByValue()
    {
        return _SqlSelectPtyByValue;
    }
    virtual  const char*   SqlSelectNodeByPtyId()
    {
        return _SqlSelectNodeByPtyId;
    }
    virtual  const char*   SqlSelectCpnByPtyId()
    {
        return _SqlSelectCpnByPtyId;
    }
    virtual  const char*   SqlSelectTexByPtyId()
    {
        return _SqlSelectTexByPtyId;
    }

    virtual  const char*   SqlDelNodePtyByNodeId()
    {
        return _SqlDelNodePtyByNodeId;
    }
    virtual  const char*   SqlDelCpnPtyByCpnId()
    {
        return _SqlDelCpnPtyByCpnId;
    }
    virtual  const char*   SqlDelTexPtyByTexId()
    {
        return _SqlDelTexPtyByTexId;
    }
    virtual  const char*   SqlDelNodeById()
    {
        return _SqlDelNodeById;
    }
    virtual  const char*   SqlDelCpnById()
    {
        return _SqlDelCpnById;
    }
    virtual  const char*   SqlDelPtyById()
    {
        return _SqlDelPtyById;
    }
    virtual  const char*   SqlDelTexById()
    {
        return _SqlDelTexById;
    }
    virtual  const char*   SqlDelBigDataByPtyId()
    {
        return _SqlDelBigDataByPtyId;
    }
    virtual  const char*   SqlDelNodeCpnByNodeId()
    {
        return _SqlDelNodeCpnByNodeId;
    }
    virtual  const char*   SqlDelCpnTexByCpnId()
    {
        return _SqlDelCpnTexByCpnId;
    }

    virtual  const char*   SqlCountNodeById()
    {
        return _SqlCountNodeById;
    }
    virtual  const char*   SqlCountNodeByCpnId()
    {
        return _SqlCountNodeByCpnId;
    }
    virtual  const char*   SqlCountCpnByTexId()
    {
        return _SqlCountCpnByTexId;
    }
    virtual  const char*   SqlSelctRootNodes()
    {
        return _SqlSelectRootNodes;
    }
    virtual  const char* SqlCountComponentById()
    {
        return _SqlCountCpnById;
    }
    virtual  const char* SqlCountTextureById()
    {
        return _SqlCountTexById;
    }
};

WD_DECL_OBJECT_SMATR_POINTER(WDDataBaseTool)

WD_NAMESPACE_END




