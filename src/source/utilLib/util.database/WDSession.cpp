#include "WDSession.h"

WD_NAMESPACE_BEGIN

size_t  WDSession::prepareSqls(const Strings& sqls)
{
    if(!isValid())
        return 0;
    int numPreCompile = 0;
    for (const auto& sql : sqls)
    {
        if(prepareSql(sql.c_str()) != nullptr)
            numPreCompile++;
    }
    return numPreCompile;
}

WDSession::Records   WDSession::query(const char* sql)
{
    Records records;
    executeSql(sql, [](void* arg, int numFields, char** fieldValues, char**)->int
        {
            Records*    pRecords    =   (Records*)arg;
            Record      record;
            record.resize(numFields);
            for (int i = 0; i < numFields; i++)
            {
                record[i] = fieldValues[i] == nullptr ? "" : fieldValues[i];
            }
            pRecords->push_back(record);
            return 0;
        }, &records);
    return records;
}

bool    WDSession::insert(const char* sql)
{
    return executeSql(sql);
}


bool    WDSession::update(const char* sql)
{
    return executeSql(sql);
}

bool    WDSession::deleteRecords(const char* sql)
{
    return executeSql(sql);
}

uint    WDSession::truncateAllTables()
{
    uint num = 0;
    // ��ȡ���б���
    auto tables = getTables();
    for (const auto& tbName : tables)
    {
        if (truncateTable(tbName.c_str()))
            num++;
    }
    return num;
}

bool    WDSession::createTable(const char* sql)
{
    return executeSql(sql);
}

uint    WDSession::dropAllTables()
{
    uint num = 0;
    auto tables = getTables();
    for (const auto& tb : tables)
    {
        if (dropTable(tb.c_str()))
            num++;
    }
    return num;
}

bool    WDSession::executeSqlFile(const char* sqlFileName)
{
    if (!isValid())
        return false;
    //��ȡsql�ļ����ַ���
    WD::WDFileReader file(sqlFileName);
    if (file.isBad())
        return false;
    file.readAll();
    const char* sqls = (char*)file.data();

    return executeSql(sqls);
}


bool    WDSession::readFromSqlFile(const char* sqlFileName)
{
    if(sqlFileName == nullptr || !isValid())
        return false;
    // ɾ�����б�
    dropAllTables();
    return executeSqlFile(sqlFileName);
}

WDSession::Strings  WDSession::getDataBaseIndex()
{
    Strings results;
    if(!isValid())
        return results;
    Strings tables = getTables();
    for (const auto& tb : tables)
    {
        Strings tableIdxs = getTableIndex(tb.c_str());
        for (const auto& idx : tableIdxs)
        {
            results.push_back(tb + "." + idx);
        }
    }
    return results;
}

uint    WDSession::deleteDataBaseIndex()
{
    if(!isValid())
        return 0;
    uint num = 0;
    Strings tables = getTables();
    for (const auto& tb : tables)
    {
        if(deleteTableIndex(tb.c_str()))
            num++;
    }
    return num;
}

uint    WDSession::deleteTableIndex(const char* tbName)
{
    if(!isValid())
        return 0;
    uint num = 0;
    // �ñ�������������
    Strings idxs = getTableIndex(tbName);
    for (const auto& idx : idxs)
    {
        if(deleteIndex(idx.c_str(), tbName))
            num++;
    }
    return num;
}

WD_NAMESPACE_END