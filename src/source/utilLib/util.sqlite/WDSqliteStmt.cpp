#include "WDSqliteStmt.h"

WD_NAMESPACE_BEGIN

SqliteStmt::SqliteStmt(sqlite3_stmt* stmt)
    : _stmt(stmt)
{
    if (isValid())
    {
        size_t  resCnt      =   columnCount(); // �����
        size_t  paramCnt    =   sqlite3_bind_parameter_count(stmt); // ������

        if (paramCnt != 0)
        {
            _param.resize(paramCnt);
            for (auto& param : _param)
            {
                param.dataPtr       =   nullptr;
                param.dataLength    =   0;
                param.dataType      =   SQLITE_NULL;
            }
        }

        if (resCnt != 0)
        {
            _result.resize(resCnt);
            for (auto& res : _result)
            {
                res.dataPtr         =   nullptr;
                res.dataLength      =   0;
                res.bufLength       =   0;
                res.isNull          =   true;
                res.dataType        =   SQLITE_NULL;
            }
        }
    }
}

SqliteStmt::~SqliteStmt()
{
    if(isValid())
    {
        sqlite3_finalize(_stmt);
        _stmt = nullptr;
    }
}

size_t          SqliteStmt::columnCount() const
{
    if (!isValid())
        return 0;
    return  sqlite3_column_count(_stmt);
}

const char*     SqliteStmt::columnName(size_t nIdx) const
{
    if (!isValid())
        return nullptr;
    return  sqlite3_column_name(_stmt, (int)nIdx);
}

WDFieldType     SqliteStmt::columnType(size_t nIdx) const
{
    if (!isValid())
        return  WD_TYPE_NULL;
    return  Sqlite2WDFiledType(sqlite3_column_type(_stmt, (int)nIdx));
}


size_t          SqliteStmt::columnDataSize(size_t index) const
{
    if (!isValid())
        return  0;

    if (index >= _result.size())
        return  0;

    return _result[index].dataLength;
}

bool            SqliteStmt::columnDataIsNull(size_t index) const
{
    if (!isValid())
        return  true;

    if (index >= _result.size())
        return  true;

    return _result[index].isNull;
}


bool            SqliteStmt::bindParam(size_t index, const void* buf, size_t len, WDFieldType type)
{
    if (!isValid() || _param.size() <= index)
        return  false;
    _param[index].dataPtr       =   buf;
    _param[index].dataLength    =   len;
    _param[index].dataType      =   WdFieldType2Sqlite(type);

    return true;
}


bool            SqliteStmt::bindResult(size_t index, void* buf, size_t len, WDFieldType type)
{
    if (!isValid() || _result.size() <= index)
        return false;
    _result[index].dataPtr      =   buf;
    _result[index].bufLength    =   len;
    _result[index].dataLength   =   0;
    _result[index].isNull       =   true;
    _result[index].dataType     =   WdFieldType2Sqlite(type);

    return true;
}

bool            SqliteStmt::store()  
{
    if (!isValid())
        return  false;
    // �������󶨵�sql�����
    for (int idx = 0; idx < _param.size(); idx++)
    {
        auto& param = _param[idx];

        if(param.dataPtr == nullptr)
            return false;

        switch (param.dataType)
        {
        case SQLITE_INTEGER:
            {
                sqlite3_bind_int(_stmt, idx + 1, *(int*)param.dataPtr);
            }
            break;
        case SQLITE_FLOAT:
            {
                sqlite3_bind_double(_stmt, idx + 1, *(double*)param.dataPtr);
            }
            break;
        case SQLITE_TEXT:
            {
                sqlite3_bind_text(_stmt, idx + 1, (char*)param.dataPtr, (int)param.dataLength, SQLITE_STATIC);
            }
            break;
        case SQLITE_BLOB:
            {
                sqlite3_bind_blob(_stmt, idx + 1, param.dataPtr, (int)param.dataLength, SQLITE_STATIC);
            }
            break;
        default:
            {
                // ��������
                return false;
            }
            break;
        }
    }
    return true;
}

bool            SqliteStmt::step()
{
    // �����
    store();
    if(sqlite3_step(_stmt) != SQLITE_DONE)
        return false;
    sqlite3_reset(_stmt);
    return true;
}

bool            SqliteStmt::fetch()
{
    // ��ȡһ�н��
    if(sqlite3_step(_stmt) != SQLITE_ROW) // ����ΪSQLITE_DONE/SQLITE_ERROR��
    {
        sqlite3_reset(_stmt);
        return false;
    }

    // ����row���д��
    for (int idx = 0; idx < _result.size(); idx++)
    {
        auto& res = _result[idx];

        switch (res.dataType)// bindResultʱ����
        {
        case SQLITE_INTEGER:
            {
                res.dataLength = sizeof(int);
                res.isNull = false;
                if(res.dataPtr != nullptr)
                    *(int*)res.dataPtr = sqlite3_column_int(_stmt, idx);
            }
            break;
        case SQLITE_FLOAT:
            {
                res.dataLength = sizeof(double);
                res.isNull = false;
                if(res.dataPtr != nullptr)
                    *(double*)res.dataPtr = sqlite3_column_double(_stmt, idx);
            }
            break;
        case SQLITE_TEXT:
            {
                const char* text = (char*)sqlite3_column_text(_stmt, idx);
                if (text != nullptr)
                {
                    res.dataLength = strlen((char*)text);
                    res.isNull = false;
                    if(res.dataPtr != nullptr && res.bufLength > strlen(text))
                        // �����ݸ��Ƶ�Ŀ���ַ
                        strcpy((char*)res.dataPtr, text);
                }
                else
                {
                    res.dataLength = 0;
                    res.isNull = true;
                }
            }
            break;
        case SQLITE_BLOB:
            {
                int dataLen         =   sqlite3_column_bytes(_stmt, idx);
                res.dataLength = dataLen;
                res.isNull = (dataLen == 0);

                if(res.dataPtr != nullptr && res.bufLength >= dataLen)
                {
                    const void* dataPtr = sqlite3_column_blob(_stmt, idx);
                    memcpy(res.dataPtr, dataPtr, dataLen);
                }
            }
            break;
        default:
            {
                return false;
            }
            break;
        }
    }
    return true;
}


bool            SqliteStmt::fetchColumn(size_t index, void* buf, size_t len, ulong offset) const
{
    WDUnused(offset);
    if (!isValid() || buf == nullptr)
        return  false;

    const void* dataPtr =   sqlite3_column_blob(_stmt, (int)index);
    int dataLen         =   sqlite3_column_bytes(_stmt, (int)index);

    if(dataPtr != nullptr && len >= dataLen)
    {
        memcpy(buf, dataPtr, dataLen);
        return true;
    }
    return false;
}


WD_NAMESPACE_END