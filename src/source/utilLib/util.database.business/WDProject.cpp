#include    "WDDatabaseBusiness.h"
#include    "../util.databaseApi/WDDatabaseApi.h"
#include    "common/WDFileReader.hpp"
#include    "common/WDFileInfo.h"
#include    "rapidxml/WDRapidxml.h"
#include    <direct.h>
#include    "WDCore.h"
#include    <fstream>
#include    "businessModule/WDBMCommon.h"
#include    <algorithm>
#include    "core/extension/WDPluginFormat.h"
#include    "core/extension/WDExtensionMgr.h"
#include    "rapidxml/WDRapidxml.h"
#include    "core/node/WDNode.h"
WD_NAMESPACE_BEGIN


using   SharedPtr   =   WDProject::SharedPtr;
SharedPtr WDProject::_curProject = nullptr;

/**
*   @brief 通过配置文件创建工程
*/
//bool    WDProject::createFromFile(const char* tempFile)
//{
//    if (tempFile == nullptr)
//        return  false;
//
//    ProjectList     prjList =   GetAllProjects();
//    for (auto& var : prjList)
//    {
//        if (_stricmp(name().c_str(),var->name().c_str()) == 0)//如果该工程已经存在，那么无法创建
//        {
//            return  false;
//        }
//    }
//    std::string     strFile =   tempFile;
//
//    WD::WDFileInfo  temInf(strFile);
//    if (!temInf.exists())
//        return  false;
//    /// 获取XML根节点
//    WDFileReader    file(tempFile);
//    if (file.isBad())
//        return  false;
//
//    file.readAll();
//    if (file.length() == 0)
//        return false;
//    try
//    {
//        XMLDoc      doc;
//        doc.parse<0>((char*)file.data());
//        XMLNode*    root    =   doc.first_node("root");
//        if (root == nullptr)
//            return false;
//        XMLNode*    dbs     =   doc.first_node("dbs");
//        if (dbs == nullptr)
//            return false;
//        for ( XMLNode* db = dbs->first_node()//每个节点为一个db
//            ; db 
//            ; db = db->next_sibling())
//        {
//            /* 需要创建的数据库（项目）的基本信息 */
//            XMLAttr*        aType   =   db->first_attribute("type");//项目类型
//            XMLAttr*        aName   =   db->first_attribute("name");//项目名
//            XMLAttr*        aSql    =   db->first_attribute("sql");//sql文件名
//            if (aType == nullptr || aSql == nullptr)
//                return  false;
//
//            /// 拼接db名称
//            std::string     dbName  =   std::string("wd_");
//                            dbName  +=  std::string(name());
//                            dbName  +=  "_";
//                            dbName  +=  std::string(aType->value());
//
//            /// 读取sql文件内容
//            /// sql文件与
//            WD::WDFileInfo  infor(tempFile);
//            std::string     path    =   infor.filePath();
//            /// sql文件在与配置文件同目录/sql
//            std::string     strFile =   path + "/sql/" + aSql->value();
//            WDFileReader    sqlFile(strFile);//文件路径
//            if (sqlFile.isBad())
//                return  false;
//
//            sqlFile.readAll();
//
//            if (sqlFile.length() == 0)
//                return false;
//
//            if(!createFromSQL(dbName.c_str(),(const char*)sqlFile.data()))
//                return  false;
//
//            WDProjectModule::SharedPtr  m   =   WDProjectModule::MakeShared();
//
//            if (aName)
//            {
//                m->setName(aName->value());
//            }
//                
//            m->_dbName      =   dbName;
//            m->_type        =   aType->value();
//            m->_sqlTemplate =   strFile;
//            _moudles.push_back(m);//每个模块相当于一个数据库
//        }
//        return  true;
//    }
//    catch(...)
//    {
//        return  false;
//    }
//}

/*
    使用sql文件进行数据库的写入
*/
//bool    WDProject::createFromSQL(const char* dbName,const char* sql)
//{
//    /// 1.检测是否存在
//
//   
//    bool    result  =   false;
//    do
//    {
//        /// 2.create db
//        auto&   syDb    =   WDDatabaseMgr::Instance().systemDatabase();
//
//        result          =   WDDatabaseMgr::Instance().createDatabase(name());
//        if (!result)
//            break;
//        /// 3. 初始化数据库中表
//        WDDatabase  db(  syDb._hostName
//                        ,syDb._usrName
//                        ,syDb._passWord
//                        ,name()//模块名（数据库名）
//                        ,syDb._port
//                        ,syDb._charSet);
//        WDDbSession conn(db);//建立临时连接
//
//        if (!conn.isValid())
//            break;
//
//        /// 开始事务
//        if(!conn.beginTrans())
//            break;
//        /// 执行语句
//        if(!conn.execSQL(sql))
//            break;
//        /// 提交事务
//        if (!conn.commit())
//        {
//            conn.rollback();
//            break;
//        }
//        result  =   true;
//
//    } while (!result);
//    /// 失败，则释放数据
//    if (!result)
//    {
//        WDDatabaseMgr::Instance().dropDatabase(dbName);
//    }
//    return  result;
//}



ProjectList WDProject::GetAllProjects()
{
    ProjectList         results;

    WD::WDDbSession     conn(WDDatabaseMgr::Instance().systemDatabase());//连接管理库

    WD::WDRecordSetSPtr records =   conn.query("select * from projects");
    if (records.get() == nullptr)
    {
        /// 注意写入日志信息
        return  ProjectList();
    }
    /// 注意与数据库中保持一直
    char    prjId[64]       =   {0};
    char    prjName[64]     =   {0};
    char    prjDateTime[64] =   {0};
    char    prjUser[64]     =   {0};
    char    prjDesc[64]     =   {0};

    records->bindResult(0,    prjId,          sizeof(prjId),          WD_TYPE_VAR_STRING);
    records->bindResult(1,    prjName,        sizeof(prjName),        WD_TYPE_VAR_STRING);
    records->bindResult(2,    prjDateTime,    sizeof(prjDateTime),    WD_TYPE_VAR_STRING);
    records->bindResult(3,    prjUser,        sizeof(prjUser),        WD_TYPE_VAR_STRING);
    records->bindResult(4,    prjDesc,        sizeof(prjDesc),        WD_TYPE_VAR_STRING);
    /// 绑定并读取
    records->store();

    while (records->fetch())
    {
       auto     ptr =   WD::WDProject::MakeShared();
       ptr->setUuid(WDUuid::FromString(prjId));
       ptr->setName(prjName);

       ptr->loadData(prjId, prjName, prjDateTime, prjUser, prjDesc);
       results.push_back(ptr);
    }
    return  results;
}



bool        WDProject::Save(const ObjectList& projects)
{
    bool tag = true;
    for (auto p : projects)
    {
        tag &= p->saveSelf();
    }
    return  tag;
}




bool    WDProject::saveSelf()
{
    //将当前项目信息写入数据库
    //如果不存在则插入
    //如果存在则修改
    WD::WDDbSession     conn(WDDatabaseMgr::Instance().systemDatabase());
    char sql[256];
    sprintf_s(sql, sizeof(sql), "select * from projects where id = '%s'", _id.c_str());
    conn.beginTrans();
    if (conn.quaryBatch(sql).size() > 0)
    {
        //如果已经存在该项目
        sprintf_s(sql, sizeof(sql)
            , "update projects set name = '%s', create_time = '%s', create_usr = '%s', description = '%s' where id = '%s'"
            , _name.c_str()
            , _createDateTime.c_str()
            , _createUser.c_str()
            , _desc.c_str()
            , _id.c_str());
        conn.execSQL(sql);
        if (conn.commit())
        {
            return true;
        }
        else
        {
            conn.rollback();
            return false;
        }
    }
    else//不存在该项目
    {
        sprintf_s(sql, sizeof(sql), "insert into projects values ('%s', '%s', '%s', '%s', '%s')"
            , _id.c_str()
            , _name.c_str()
            , _createDateTime.c_str()
            , _createUser.c_str()
            , _desc.c_str());
        conn.execSQL(sql);
        if (!conn.commit())
        {
            conn.rollback();
            return false;
        }
        auto cfg = WD::Core().cfg().get<std::string>("defaultPaths", "");
        std::string dir = cfg.get<std::string>("Manager", "").value();
        //创建该项目的管理模块
        return addNewModuleBySqlFile(
            "3"
            , u8"管理模块"
            , u8"info manager"
            , dir.c_str());
    }
}



void    WDProject::loadData(const char* id, const char* name, const char* createTime, const char* createUser, const char* description)
{
    _createDateTime = createTime;
    _createUser = createUser; 
    _id = id;
    _desc = description;
    _name = name;
}



bool    WDProject::findPrjById(const char* targetId)
{
    WD::WDDbSession     conn(WDDatabaseMgr::Instance().systemDatabase());
    std::string sql     =   "select * from projects where id = '";
    sql += targetId;
    sql += "'";
    WD::WDRecordSetSPtr records =   conn.query(sql.c_str());
    if (records.get() == nullptr)
    {
        return false;
    }
    
    char id[64]         =   {0};
    char name[64]       =   {0};
    char dateTime[64]   =   {0};
    char creator[64]    =   {0};
    char desc[64]       =   {0};

    records->bindResult(0,    id,           sizeof(id),       WD_TYPE_VAR_STRING);
    records->bindResult(1,    name,         sizeof(name),     WD_TYPE_VAR_STRING);
    records->bindResult(2,    dateTime,     sizeof(dateTime), WD_TYPE_VAR_STRING);
    records->bindResult(3,    creator,      sizeof(creator),  WD_TYPE_VAR_STRING);
    records->bindResult(4,    desc,         sizeof(desc),     WD_TYPE_VAR_STRING);

    /// 绑定并读取
    records->store();

    while (records->fetch())
    {
       loadData(id, name, dateTime, creator, desc);
       return true;
    }
    return false;//查不到则返回失败
}


Records  WDProject::getModules()
{
    Records results;

    std::string prjName = "wd_"+ _id + "_manager";
    WDDatabase sysDb=WDDatabaseMgr::Instance().systemDatabase();
    WDDbSession conn( WD::WDDatabase (
        sysDb._hostName
        ,sysDb._usrName
        ,sysDb._passWord
        ,prjName
        ,sysDb._port
        ,sysDb._charSet ));//连接项目管理库

    char sql[] = "select * from modules";
    WD::WDRecordSetSPtr records =   conn.query(sql);
    if (records.get() == nullptr)
    {
        return results;
    }

    char id[64]     =   {0};
    char name[64]   =   {0};
    char desc[64]   =   {0};
    records->bindResult(0, id, sizeof(id), WD_TYPE_STRING);
    records->bindResult(1, name, sizeof(name), WD_TYPE_STRING);
    records->bindResult(2, desc, sizeof(desc), WD_TYPE_STRING);
    records->store();
    
    while (records->fetch())
    {
        results.push_back({id, name, desc});
        //写到_modules
        auto m = WDProjectModule::MakeShared();
        std::string moduleDbName;
        std::string sqlTemplate;
        if (atoi(id) == BMMT_Unknown)
        {
            moduleDbName    = "";
            sqlTemplate     = "";
        }
        else
        {
            moduleDbName    =   "wd_" + _id + "_" + BMModuleTypeToString(WDBMModuleType(atoi(id)));
            auto    cfg     =   WD::Core().cfg().get<std::string>("defaultPaths", "");
            sqlTemplate     =   cfg.get<std::string>(BMModuleTypeToString(WDBMModuleType(atoi(id))), "").value();
            
        }
        m->loadData(id, moduleDbName.c_str(), sqlTemplate.c_str());
        _modules.push_back(m);
    }

    return results;
}

Records     WDProject::getUsers()
{
    Records results;

    std::string prjName = "wd_"+ _id + "_manager";
    WDDatabase sysDb=WDDatabaseMgr::Instance().systemDatabase();
    WDDbSession conn( WD::WDDatabase (
        sysDb._hostName
        ,sysDb._usrName
        ,sysDb._passWord
        ,prjName
        ,sysDb._port
        ,sysDb._charSet ));//连接项目管理库

    char sql[] = "select distinct(userId) from user2module";
    WD::WDRecordSetSPtr records =   conn.query(sql);
    if (records.get() == nullptr)
    {
        return results;
    }

    char id[64]     =   {0};

    records->bindResult(0, id, sizeof(id), WD_TYPE_STRING);

    records->store();
    
    while (records->fetch())
    {
        results.push_back({id});
    }

    return results;
}

Records     WDProject::findModulesByUserId(const char* userId)
{
    Records results;

    std::string prjName = "wd_"+ _id + "_manager";
    WDDatabase sysDb=WDDatabaseMgr::Instance().systemDatabase();
    WDDbSession conn( WD::WDDatabase (
        sysDb._hostName
        ,sysDb._usrName
        ,sysDb._passWord
        ,prjName
        ,sysDb._port
        ,sysDb._charSet ));//连接项目管理库

    char sql[256];
    sprintf_s(sql, sizeof(sql), "select moduleId, permissionId from user2module where userId = '%s'", userId);

    results = conn.quaryBatch(sql);
    
    return results;
}

Records     WDProject::findUsersByModuleId(const char* moduleId)
{
    Records results;
    std::string prjName = "wd_"+ _id + "_manager";
    WDDatabase sysDb=WDDatabaseMgr::Instance().systemDatabase();
    WDDbSession conn( WD::WDDatabase (
        sysDb._hostName
        ,sysDb._usrName
        ,sysDb._passWord
        ,prjName
        ,sysDb._port
        ,sysDb._charSet ));//连接项目管理库  


    char sql[256];
    sprintf_s(sql, sizeof(sql), "select userId, permissionId from user2module where moduleId = '%s'", moduleId);

    results = conn.quaryBatch(sql);
    
    return results;
}

bool    WDProject::writeUserModule(const char* userId, const char* moduleId, int permissonId, int projectRole)
{
    std::string prjName = "wd_"+ _id + "_manager";
    WDDatabase sysDb=WDDatabaseMgr::Instance().systemDatabase();
    WDDbSession conn( WD::WDDatabase (
        sysDb._hostName
        ,sysDb._usrName
        ,sysDb._passWord
        ,prjName
        ,sysDb._port
        ,sysDb._charSet ));//连接项目管理库

    char sql[256]; 
    //为保证一致性，需要检查该user以及该module是否还在系统中

    //验证user存在性
    WD::WDDbSession     connSys(sysDb);
    sprintf_s(sql, sizeof(sql), "select * from users where id = '%s'", userId);
    if (connSys.quaryBatch(sql).size() == 0)
    {
        return false;
    }
    
    //验证module存在性
    sprintf_s(sql, sizeof(sql), "select * from modules where id = '%s'", moduleId);
    if (conn.quaryBatch(sql).size() == 0)
    {
        return false;
    }

    sprintf_s(sql, sizeof(sql), "select * from user2module where userId = '%s' and moduleId = '%s'", userId, moduleId);
    if (conn.quaryBatch(sql).size() == 0)//之前不存在该关系
    {
        //插入
        sprintf_s(sql, sizeof(sql), "insert into user2module values ('%s', '%s', %d)", userId, moduleId, permissonId);
        if (conn.execSQL(sql))
        {
            sprintf_s(sql, sizeof(sql), "select * from user2projects where userId = '%s' and projectId = '%s'", userId, _id.c_str());
            if (connSys.quaryBatch(sql).size() == 0)//如果user2projects没有该userId与projectId的表项，则需要建立关系
            {
                //插入管理库
                //这里默认加入的用户为“4”，之后完善
                sprintf_s(sql, sizeof(sql), "insert into user2projects values ('%s', '%s', '%s')"
                    , userId, _id.c_str(), std::to_string(projectRole).c_str());//默认为0
                return connSys.execSQL(sql);
            }
            return true;
        }
        else
        {
            return false;
        }
    }
    else//已经存在
    {
        //更新
        if (permissonId == 0)//如果为0，表示要在该模块中删除该用户
        {
            //删除该条记录
            sprintf_s(sql, sizeof(sql), "delete from user2module where userId = '%s' and moduleId = '%s'", userId, moduleId);
            if (conn.execSQL(sql))
            {
                //如果该user2module中没有了userId,则在系统库中的user2projects中进行删除
                sprintf_s(sql, sizeof(sql), "select * from user2module where userId = '%s", userId);
                if (conn.quaryBatch(sql).size() == 0)
                {
                    sprintf_s(sql, sizeof(sql), "delete from user2projects where userId = '%s' and projectId = '%s'", userId, _id.c_str());
                    return connSys.execSQL(sql);
                }
                return true;
            }
            else
            {
                return false;
            }
        }
        sprintf_s(sql, sizeof(sql)
            , "update user2module set permissionId = %d where userId = '%s' and moduleId = '%s'"
            , permissonId,userId, moduleId);

        return conn.execSQL(sql);
    }
}


bool    WDProject::addNewModuleBySqlFile(const char* moduleId
    , const char* moduleName
    , const char* desc
    , const char* sqlFilePath)
{
    //考虑本别使用sql文件和通过其他模块来新建模块
    
    if (atoi(moduleId) == BMMT_Unknown)
        return false;
    std::string moduleTail = BMModuleTypeToString(WDBMModuleType(atoi(moduleId)));

    //需要实现sql文件的交互选择
    //或者直接通过某个项目的模块进行复制
    //应该也需要实现项目复制功能

    //创建模块数据库
    if (!WDDatabaseMgr::Instance().createDatabase( "wd_" + _id + "_" + moduleTail, sqlFilePath))
    {
        return false;
    }
    
    std::string prjName = "wd_"+ _id + "_manager";
    WDDatabase sysDb=WDDatabaseMgr::Instance().systemDatabase();
    WDDbSession conn( WD::WDDatabase (
        sysDb._hostName
        ,sysDb._usrName
        ,sysDb._passWord
        ,prjName
        ,sysDb._port
        ,sysDb._charSet ));//连接项目管理库

    char sql[256];
    sprintf_s(sql, sizeof(sql), "insert into modules values ('%s','%s','%s')", moduleId, moduleName, desc);
     
    return conn.execSQL(sql);
}

bool    WDProject::addNewModuleByAnother(const char* moduleId, const char* moduleName, const char* desc, const char* fromDb)
{
    if (atoi(moduleId) == BMMT_Unknown)
        return false;
    std::string moduleTail = BMModuleTypeToString(WDBMModuleType(atoi(moduleId)));

    //复制数据库
    if (!WDDatabaseMgr::Instance().copyDatabase( "wd_" + _id + "_" + moduleTail, fromDb))
    {
        return false;
    }


    std::string prjName = "wd_"+ _id + "_manager";
    WDDatabase sysDb=WDDatabaseMgr::Instance().systemDatabase();
    WDDbSession conn( WD::WDDatabase (
        sysDb._hostName
        ,sysDb._usrName
        ,sysDb._passWord
        ,prjName
        ,sysDb._port
        ,sysDb._charSet ));//连接项目管理库

    char sql[256];
    sprintf_s(sql, sizeof(sql), "insert into modules values ('%s','%s','%s')", moduleId, moduleName, desc);
     
    return conn.execSQL(sql);
}




bool    WDProject::deleteModule(const char* moduleId)
{    
    if (atoi(moduleId) == BMMT_Unknown || atoi(moduleId) == BMMT_Manager)//"待定"模块和“管理”模块无法删除，直接跳过
        return true;
    std::string moduleTail = BMModuleTypeToString(WDBMModuleType(atoi(moduleId)));

    //先删除模块对应的数据库
    std::string moduleName = "wd_" + _id + "_" + moduleTail;
    if (!WDDatabaseMgr::Instance().dropDatabase(moduleName))
    {
        return false;
    }

    std::string prjName = "wd_" + _id + "_manager";
    WDDatabase sysDb = WDDatabaseMgr::Instance().systemDatabase();

    WDDbSession conn( WD::WDDatabase (
        sysDb._hostName
        ,sysDb._usrName
        ,sysDb._passWord
        ,prjName
        ,sysDb._port
        ,sysDb._charSet ));//连接项目管理库
   
    char sql[256];
    sprintf_s(sql, sizeof(sql), "delete from modules where id = '%s'", moduleId);

    if (conn.execSQL(sql))
    {
        sprintf_s(sql, sizeof(sql), "delete from user2module where moduleId = '%s'", moduleId);
        if (conn.execSQL(sql))
        {
            return true;
        }
        else
        {
            return false;
        }
    }
    else
    {
        return false;
    }
}


bool    WDProject::deleteUser(const char* userId)
{
    std::string prjName = "wd_" + _id + "_manager";
    WDDatabase sysDb = WDDatabaseMgr::Instance().systemDatabase();
    WDDbSession conn( WD::WDDatabase (
        sysDb._hostName
        ,sysDb._usrName
        ,sysDb._passWord
        ,prjName
        ,sysDb._port
        ,sysDb._charSet ));//连接项目管理库

    char sql[256];
    sprintf_s(sql, sizeof(sql), "delete from user2module where userId = '%s'", userId);
    if (conn.execSQL(sql))
    {
        //如果该user2module中没有了userId,则在系统库中的user2projects中进行删除
        sprintf_s(sql, sizeof(sql), "select * from user2module where userId = '%s", userId);
        if (conn.quaryBatch(sql).size() == 0)
        {
            WDDbSession connSys(sysDb);
            sprintf_s(sql, sizeof(sql), "delete from user2projects where userId = '%s' and projectId = '%s'", userId, _id.c_str());
            return connSys.execSQL(sql);
        }
        return true;
    }
    else
    {
        return false;
    }
}


bool    WDProject::DeleteProject(const char* projectId)
{

    char sql[256];

    std::string prjName = "wd_"+ std::string(projectId) + "_manager";
    WDDatabase sysDb = WDDatabaseMgr::Instance().systemDatabase();
    WDDbSession conn( WD::WDDatabase (
        sysDb._hostName
        ,sysDb._usrName
        ,sysDb._passWord
        ,prjName
        ,sysDb._port
        ,sysDb._charSet ));//连接项目管理库

    //删除所有模块的数据库
    WD::WDProject prj;
    prj.loadData(projectId, "", "", "", "");
    WD::Records modules = prj.getModules();
    for (int i = 0; i < modules.size(); i++)
    {
        prj.deleteModule(modules[i][0].c_str());//管理模块没删掉
    }

    if(!WDDatabaseMgr::Instance().dropDatabase(prjName))//删除管理模块
        return false;


    WD::WDDbSession sysConn(sysDb);//连接系统管理库
    //在projects中删除
    sprintf_s(sql, sizeof(sql), "delete from projects where id = '%s'", projectId);
    if(!sysConn.execSQL(sql))
        return false;

    //在user2projects中删除
    sprintf_s(sql, sizeof(sql), "delete from user2projects where projectId = '%s'", projectId);
    return sysConn.execSQL(sql);
}


std::shared_ptr<WD::WDProject>    WDProject::NewProject(const char* projectId
    , const char* projectName
    , const char* createTime
    , const char *createUser
    , const char* desc)
{
    auto    ptr = MakeShared();

    ptr->loadData(projectId, projectName, createTime, createUser, desc);
    if (ptr->saveSelf())
        return ptr;
    return nullptr;
}

std::shared_ptr<WD::WDProject> WDProject::CurrentProject()
{
    return _curProject;
}

bool    WDProject::SetCurrentProject(const char* projectId)
{
     _curProject = WDProject::MakeShared();
     return _curProject->findPrjById(projectId);
}



std::shared_ptr<int> WDProjectModule::_curPrjModuleType = nullptr;

int    WDProjectModule::CurrentModuleType()
{
    return *_curPrjModuleType;
}

bool    WDProjectModule::SetCurrentModule(int moduleIntType)
{
    _curPrjModuleType = std::make_shared<int>(moduleIntType);
    return true;
}





Records WDProject::FindUsersByPrjId(const char* prjId)
{
    Records results;

    std::string prjName = "wd_"+ std::string(prjId) + "_manager";
    WDDatabase sysDb=WDDatabaseMgr::Instance().systemDatabase();
    WDDbSession conn( WD::WDDatabase (
        sysDb._hostName
        ,sysDb._usrName
        ,sysDb._passWord
        ,prjName
        ,sysDb._port
        ,sysDb._charSet ));//连接项目管理库

    char sql[] = "select distinct(userId) from user2module";
    WD::WDRecordSetSPtr records =   conn.query(sql);
    if (records.get() == nullptr)
    {
        return results;
    }

    char id[64]     =   {0};

    records->bindResult(0, id, sizeof(id), WD_TYPE_STRING);

    records->store();
    
    while (records->fetch())
    {
        results.push_back({id});
    }

    return results;
}


std::string WDProject::getModulePermission(const char* userId, const char* moduleId)
{
    std::string prjName = "wd_"+ _id + "_manager";
    WDDatabase sysDb=WDDatabaseMgr::Instance().systemDatabase();
    WDDbSession conn( WD::WDDatabase (
        sysDb._hostName
        ,sysDb._usrName
        ,sysDb._passWord
        ,prjName
        ,sysDb._port
        ,sysDb._charSet ));//连接项目管理库

    char sql[256]; 
    sprintf_s(sql, sizeof(sql), "select permissionId from user2module where userId = '%s' and moduleId = '%s'", userId, moduleId);

    Records results = conn.quaryBatch(sql);
    if(results.size() == 0)
        return "";

    return results[0][0];
}


std::string  WDProject::GetModulePermission(const char* projectId, const char* userId, const char* moduleId)
{
    std::string prjName = "wd_"+ std::string(projectId) + "_manager";
    WDDatabase sysDb=WDDatabaseMgr::Instance().systemDatabase();
    WDDbSession conn( WD::WDDatabase (
        sysDb._hostName
        ,sysDb._usrName
        ,sysDb._passWord
        ,prjName
        ,sysDb._port
        ,sysDb._charSet ));//连接项目管理库

    char sql[256]; 
    sprintf_s(sql, sizeof(sql), "select permissionId from user2module where userId = '%s' and moduleId = '%s'", userId, moduleId);

    Records results = conn.quaryBatch(sql);
    if(results.size() == 0)
        return "";

    return results[0][0];
}





bool    WDProject::clearModule(const char* moduleId)
{
    if (atoi(moduleId) == BMMT_Unknown)
        return false;
    std::string moduleTail = BMModuleTypeToString(WDBMModuleType(atoi(moduleId)));

    std::string moduleBaseName  =   "wd_" + _id + "_" + moduleTail;
    WDDatabase sysDb=WDDatabaseMgr::Instance().systemDatabase();
    WDDbSession conn( WD::WDDatabase (
        sysDb._hostName
        ,sysDb._usrName
        ,sysDb._passWord
        ,moduleBaseName
        ,sysDb._port
        ,sysDb._charSet ));//连接模块数据库

    return conn.truncateTables();
}


bool    WDProject::clearProject()
{
    //删除所有模块（除了管理模块和“待定”模块）
    Records prjModules  =   getModules();
    for (auto md : prjModules)
    {
        if(!deleteModule(md[0].c_str()))
            return false;
    }
    //删除所有用户
    Records prjUsers    =   getUsers();
    for (auto user : prjUsers)
    {
        if(!deleteUser(user[0].c_str()))
            return false;
    }
    //管理模块也被以上操作重置
    return true;
}

bool    WDProject::clearModuleTable(const char* moduleId, const char* tbName)
{
    if (atoi(moduleId) == BMMT_Unknown)
        return false;
    std::string moduleTail = BMModuleTypeToString(WDBMModuleType(atoi(moduleId)));

    std::string moduleBaseName  =   "wd_" + _id + "_" + moduleTail;
    WDDatabase sysDb=WDDatabaseMgr::Instance().systemDatabase();
    WDDbSession conn( WD::WDDatabase (
        sysDb._hostName
        ,sysDb._usrName
        ,sysDb._passWord
        ,moduleBaseName
        ,sysDb._port
        ,sysDb._charSet ));//连接模块数据库
    return conn.truncateTable(tbName);
}


bool    WDProject::loadModuleDataBySqlFile(const char* moduleId, const char* sqlFilePath)
{
    //先清空，再导入
    if (!clearModule(moduleId))
        return false;

    //将sqlFile中的数据插入
    if (atoi(moduleId) == BMMT_Unknown)
        return false;
    std::string moduleTail = BMModuleTypeToString(WDBMModuleType(atoi(moduleId)));

    return    WDDatabaseMgr::Instance().loadFromSqlFileData("wd_" + _id + "_" + moduleTail, sqlFilePath);
}




bool    WDProject::loadModuleDataByAnoter(const char* moduleId, const char* fromDb)
{

    if (!clearModule(moduleId))
    {
        return false;
    }

    //将fromDb中的数据导入
    if (atoi(moduleId) == BMMT_Unknown)
        return false;
    std::string moduleTail = BMModuleTypeToString(WDBMModuleType(atoi(moduleId)));

    return WDDatabaseMgr::Instance().loadFromAnotherDb("wd_" + _id + "_" + moduleTail, fromDb);
}


bool    WDProject::loadModuleDataByXml(const char* moduleId, const char* xmlFilePath)
{
    if (!clearModule(moduleId))
        return false;

    //从xml文件读取到内存（result）
    WD::WDPluginFormat::FormatParam paramRead;
    paramRead.fileName = xmlFilePath;

    WD::WDPluginFormat::Objects     result;
    WD::WDPluginFormat* pFormatXml = WD::Core().extensionMgr().createExtensionT<WD::WDPluginFormat>("plugin.format.design.xml");
    if (pFormatXml == nullptr)
        return false;
    pFormatXml->read(paramRead, result);
    WD::Core().extensionMgr().destroyExtension(pFormatXml);

    //从内存（result）读取到数据库
    WD::WDPluginFormat::FormatParam paramWrite;
    paramWrite.fileName = "wd_" + _curProject->_id + "_" + WD::BMModuleTypeToString(WDBMModuleType(atoi(moduleId)));
    WD::WDPluginFormat* pFormatMysql = WD::Core().extensionMgr().createExtensionT<WD::WDPluginFormat>("plugin.format.design.mysql");
    if (pFormatMysql == nullptr)
        return false;
    bool tag    =   pFormatMysql->write(paramWrite, result);
    WD::Core().extensionMgr().destroyExtension(pFormatMysql);
    return tag;
}


bool    WDProject::loadPrjDataByAnother(const char* srcProjectId)
{
    if (!clearProject())
    {
        return false;
    }

    //将源项目中的所有模块复制给本项目
    //之后清空本项目user2module
    auto     srcPrj =   WDProject::MakeShared();
    srcPrj->loadData(srcProjectId, "", "", "", "");
    Records modules =   srcPrj->getModules();
    for (auto md : modules)
    {
        //拼接数据库名
        if (atoi(md[0].c_str()) == BMMT_Unknown)
            continue;
      
        std::string moduleTail = BMModuleTypeToString(WDBMModuleType(atoi(md[0].c_str())));

        
        if (atoi(md[0].c_str()) != BMMT_Manager)//直接复制数据库
        {
            if (!WDDatabaseMgr::Instance().copyDatabase( 
                "wd_" + _id + "_" + moduleTail
                , "wd_" + std::string(srcProjectId) +"_" + moduleTail))
            {
                return false;
            }
        }
        else//管理库
        {
            if(!loadModuleDataByAnoter(md[0].c_str(), ("wd_" + std::string(srcProjectId) +"_" + moduleTail).c_str()))
                return false;
        }

    }

    //清空管理库中的user2module表
    std::string prjName = "wd_"+ _id + "_manager";
    WDDatabase sysDb=WDDatabaseMgr::Instance().systemDatabase();
    WDDbSession conn( WD::WDDatabase (
        sysDb._hostName
        ,sysDb._usrName
        ,sysDb._passWord
        ,prjName
        ,sysDb._port
        ,sysDb._charSet ));//连接项目管理库

    return conn.truncateTable("user2module");
}

bool    WDProject::exportModule(const char* moduleId, const char* exportPath, const char* exportFileName)
{
    auto dbName = "wd_" + _id + "_" + WD::BMModuleTypeToString(WDBMModuleType(atoi(moduleId)));
    
    WDDatabase sysDb = WDDatabaseMgr::Instance().systemDatabase();
    //char osScript[1024];
    //sprintf_s(osScript, sizeof(osScript)
    //    , "mysqldump -h%s -u%s -p%s %s > %s"
    //    , sysDb._hostName.c_str()
    //    , sysDb._usrName.c_str()
    //    , sysDb._passWord.c_str()
    //    , dbName.c_str()
    //    , (std::string(exportPath)  + exportFileName).c_str()
    //);
    ////需要配置mysqldump工具到系统变量
    //system(osScript);//win和linux通用

//#if     defined(_WIN32) || defined(_WIN64)
//#elif   defined(__linux__) || defined(linux) || defined(LINUX)
//#endif

    WDDatabase expDb(
        sysDb._hostName
        , sysDb._usrName
        , sysDb._passWord
        , dbName
        , sysDb._port
        , sysDb._charSet);
 
    expDb.databaseToSqlFile((std::string(exportPath) + exportFileName).c_str());
    return true;
}


//考虑返回xml文件流，不在这里进行文件交互
std::string    WDProject::ExportProject(const char* prjId, const char* exportPath)
{
    std::string sqlFileDir = exportPath + std::string(prjId) + "/";//模块sql文件的保存路径

    auto    doc     =   std::make_shared<XMLCreator>("xml version='1.0' encoding='utf-8'");
    auto    item1   =   doc->root("root").append("item");
    item1.attr("type", "Group")
        .attr("key", "projectFile")
        .attr("value", "")
        .attr("name", "")
        .attr("desc", "");

    auto    item2 = item1.append("item");
    item2.attr("type", "String")
        .attr("key", "projectId")
        .attr("value", prjId)
        .attr("name", "项目id")
        .attr("desc", "");
   
    std::vector<int> moduleTypes =
    {
        //BMT_Unknown,
        BMMT_Catalog,
        BMMT_Design,
        BMMT_Manager
    };
        
    auto    prj = WD::WDProject::GetPrj(prjId);
    auto    modules = prj->getModules();
    std::string name;
    std::string key;
    std::string value;
    std::string moduleFileName;
    //插入模块文件的路径
    for (auto m : modules)
    {
        if (atoi(m[0].c_str()) != BMMT_Unknown)
        {
            auto item = item1.append("item");
            item.attr("type", "String");

            key     =   BMModuleTypeToString(WDBMModuleType(atoi(m[0].c_str())));
            //name    =   utf8_2_gbk(BMTStringToZh(key.c_str()).c_str());//处理中文
            name    =   key;
            moduleTypes.erase(std::remove(moduleTypes.begin(), moduleTypes.end(), atoi(m[0].c_str())), moduleTypes.end());
            
            //模块sql文件名
            moduleFileName  =   std::string(prjId) + "_" + BMModuleTypeToString(WDBMModuleType(atoi(m[0].c_str()))) + ".sql";

            value = sqlFileDir + moduleFileName;//模块sql文件路径，考虑转化为相对路径
            //
            if (!prj->exportModule(m[0].c_str(), sqlFileDir.c_str(), moduleFileName.c_str()))
                return "";
            
            item.attr("key", key.c_str())
                .attr("value", value.c_str())
                .attr("name", name.c_str())
                .attr("desc", "");
        }
    }
    for (auto t : moduleTypes)//删除多余的文件
    {
        auto delFilePath  = sqlFileDir + prjId + "_" + BMModuleTypeToString(WDBMModuleType(t)) + ".sql";
        remove(delFilePath.c_str());//这个函数在linux和win上通用
    }
    return doc->toString();
}


bool WDProject::exportModuleXml(const char* moduleId, const char* exportPath, const char* exportFileName)
{
    //从数据库读取到内存（result）
    WD::WDPluginFormat::FormatParam paramRead;
    //数据库名
    paramRead.fileName = "wd_" + WD::WDProject::CurrentProject()->_id + "_" + BMModuleTypeToString(WDBMModuleType(atoi(moduleId)));

    WD::WDPluginFormat::Objects     result;
    WD::WDPluginFormat* pFormatMysql = WD::Core().extensionMgr().createExtensionT<WD::WDPluginFormat>("plugin.format.design.mysql");
    if (pFormatMysql == nullptr)
        return false;
    pFormatMysql->read(paramRead, result);
    WD::Core().extensionMgr().destroyExtension(pFormatMysql);

    //从内存写入到xml文件
    WD::WDPluginFormat::FormatParam paramWrite;
    WD::WDPluginFormat* pFormatXml = WD::Core().extensionMgr().createExtensionT<WD::WDPluginFormat>("plugin.format.design.xml");
    if (pFormatXml == nullptr)
        return false;
    paramWrite.fileName.append(exportPath).append(exportFileName);
    bool tag = pFormatXml->write(paramWrite, result);

    WD::Core().extensionMgr().destroyExtension(pFormatXml);
    return tag;
}


//======================================
//格式转换
//转换到数据库时需要保证数据库以及相应的表以及存在
//考虑根据sql模板预先建立数据库

//数据库转换为sql文件
bool    WDProjectModule::DatabaseToSqlFile(const char* databaseName, const char* sqlFileName)
{
    WDDatabase sysDb = WDDatabaseMgr::Instance().systemDatabase();
    WDDatabase expDb(
        sysDb._hostName
        , sysDb._usrName
        , sysDb._passWord
        , databaseName
        , sysDb._port
        , sysDb._charSet);

    return expDb.databaseToSqlFile(sqlFileName);
}

//sql文件转换为数据库
bool    WDProjectModule::SqlFileToDatabase(const char* sqlFileName, const char* databaseName)
{
    if (!WDDatabaseMgr::Instance().createDatabase(databaseName, sqlFileName))
    {
        return false;
    }
    return true;
}

//数据库转换为xml
bool    WDProjectModule::DatabaseToXml(const char* databaseName, const char* xmlFileName)
{
    //读
    WD::WDPluginFormat::FormatParam paramRead;
    paramRead.fileName = databaseName;
    
    WD::WDPluginFormat::Objects result;
    WD::WDPluginFormat* pFormatMysql = WD::Core().extensionMgr().createExtensionT<WD::WDPluginFormat>("plugin.format.design.mysql");
    if (pFormatMysql == nullptr)
        return false;
    
    pFormatMysql->read(paramRead, result);
    WD::Core().extensionMgr().destroyExtension(pFormatMysql);

    //写
    WD::WDPluginFormat::FormatParam paramWrite;
    WD::WDPluginFormat* pFormatXml = WD::Core().extensionMgr().createExtensionT<WD::WDPluginFormat>("plugin.format.design.xml");
    if (pFormatXml == nullptr)
        return false;
    paramWrite.fileName =   xmlFileName;
    bool tag = pFormatXml->write(paramWrite, result);
    WD::Core().extensionMgr().destroyExtension(pFormatXml);

    return tag;
}

//xml转换为数据库
bool    WDProjectModule::XmlToDatabase(const char* xmlFileName, const char* databaseName)
{
    //读
    WD::WDPluginFormat::FormatParam paramRead;
    paramRead.fileName = xmlFileName;

    WD::WDPluginFormat* pFormatXml = WD::Core().extensionMgr().createExtensionT<WD::WDPluginFormat>("plugin.format.design.xml");
    if (pFormatXml == nullptr)
        return false;
    WD::WDPluginFormat::Objects result;
    pFormatXml->read(paramRead, result);
    WD::Core().extensionMgr().destroyExtension(pFormatXml);

    //写
    //根据模板新建数据库
    auto cfg = WD::Core().cfg().get<std::string>("defaultPaths", "");
    std::string templateFileName = cfg.get<std::string>("designer", "").value();
    if (!WDDatabaseMgr::Instance().createDatabase(databaseName, templateFileName))
    {
        return false;
    }

    WD::WDPluginFormat::FormatParam paramWrite;
    paramWrite.fileName = databaseName;
    WD::WDPluginFormat* pFormatMysql = WD::Core().extensionMgr().createExtensionT<WD::WDPluginFormat>("plugin.format.design.mysql");
    if (pFormatMysql == nullptr)
        return false;

    bool tag = pFormatMysql->write(paramWrite, result);
    WD::Core().extensionMgr().destroyExtension(pFormatMysql);

    return tag;
}


WD_NAMESPACE_END