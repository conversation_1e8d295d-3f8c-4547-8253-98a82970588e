#ifdef  _WIN32
    #include    <Windows.h>
    #include    <Iphlpapi.h>
    #pragma comment(lib,"Iphlpapi.lib") 
    #endif

#ifdef  __linux__
    #include    <sys/ioctl.h>
    #include    <linux/hdreg.h>
    #include    <fcntl.h>
    #include    <unistd.h>
    #include    <netinet/in.h>
    #include    <dirent.h>
    #include    <fstream>
    #include    <net/if.h>
#endif
#include    "cmdline.h"
#include    "sdk.license.h"
#include    "DES.h"
#include    "zlib.h"
#include    "CELLMD5.hpp"


using       uint = unsigned int;

#define MAX_DAYS            (1000)

#define MAX_SESSION         (10000)

inline bool is_base64(unsigned char c)
{
    return (isalnum(c) || (c == '+') || (c == '/'));
}

String  LocalInfor::toBase64(const char* data,size_t in_len)
{
    std::string     ret;
    int     i   =   0;
    int     j   =   0;
    char   char_array_3[3];
    char   char_array_4[4];

    const char* base64Chars =   "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";

    while (in_len--)
    {
        char_array_3[i++] = *(data++);
        if (i == 3)
        {
            char_array_4[0] = (char_array_3[0] & 0xfc) >> 2;
            char_array_4[1] =
                ((char_array_3[0] & 0x03) << 4) + ((char_array_3[1] & 0xf0) >> 4);
            char_array_4[2] =
                ((char_array_3[1] & 0x0f) << 2) + ((char_array_3[2] & 0xc0) >> 6);
            char_array_4[3] = char_array_3[2] & 0x3f;

            for (i = 0; (i < 4); i++) ret += base64Chars[char_array_4[i]];
            i = 0;
        }
    }
    if (i)
    {
        for (j = i; j < 3; j++) char_array_3[j] = '\0';

        char_array_4[0] = (char_array_3[0] & 0xfc) >> 2;
        char_array_4[1] =
            ((char_array_3[0] & 0x03) << 4) + ((char_array_3[1] & 0xf0) >> 4);
        char_array_4[2] =
            ((char_array_3[1] & 0x0f) << 2) + ((char_array_3[2] & 0xc0) >> 6);

        for (j = 0; (j < i + 1); j++) ret += base64Chars[char_array_4[j]];

        while ((i++ < 3)) ret += '=';
    }

    return ret;
}

String  LocalInfor::fromBase64(const String& encode)
{
    int     in_len  =   static_cast<int>(encode.size());
    int     i       =   0;
    int     j       =   0;
    int     in_     =   0;
    char    char_array_4[4];
    char    char_array_3[3];
    std::string ret;
    const std::string base64_chars ="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";

    while (in_len-- && (encode[in_] != '=') &&is_base64(encode[in_]))
    {
        char_array_4[i++] = encode[in_];
        in_++;
        if (i == 4)
        {
            for (i = 0; i < 4; i++)
                char_array_4[i] =   static_cast<char>(base64_chars.find(char_array_4[i]));

            char_array_3[0] =   (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
            char_array_3[1] =   ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
            char_array_3[2] =   ((char_array_4[2] & 0x3) << 6) + char_array_4[3];

            for (i = 0; (i < 3); i++) ret += char_array_3[i];
            i = 0;
        }
    }

    if (i)
    {
        for (j = i; j < 4; j++) char_array_4[j] = 0;

        for (j = 0; j < 4; j++)
            char_array_4[j] =   static_cast<char>(base64_chars.find(char_array_4[j]));

        char_array_3[0] =   (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
        char_array_3[1] =   ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
        char_array_3[2] =   ((char_array_4[2] & 0x3) << 6) + char_array_4[3];

        for (j = 0; (j < i - 1); j++) ret += char_array_3[j];
    }

    return ret;
}


String  LocalInfor::encode(const String& encode,const String& key)
{
    DES     encoder;
    return  encoder.Encrypt(encode, key);
}
String  LocalInfor::decode(const String& encode,const String& key)
{
    DES     encoder;
    return  encoder.Decrypt(encode, key);
}


char*   LocalInfor::flipAndCodeBytes(const char * str, int  pos, int  flip, char*buf)
{
    int i;
    int j = 0;
    int k = 0;
    buf[0] = '\0';
    if (pos <= 0)
        return buf;

    if (!j)
    {
        char p = 0;

        // First try to gather all characters representing hex digits only.
        j = 1;
        k = 0;
        buf[k] = 0;
        for (i = pos; j && str[i] != '\0'; ++i)
        {
            char c = tolower(str[i]);

            if (isspace(c))
                c = '0';

            ++p;
            buf[k] <<= 4;

            if (c >= '0' && c <= '9')
                buf[k] |= (unsigned char)(c - '0');
            else if (c >= 'a' && c <= 'f')
                buf[k] |= (unsigned char)(c - 'a' + 10);
            else
            {
                j = 0;
                break;
            }

            if (p == 2)
            {
                if (buf[k] != '\0' && !isprint(buf[k]))
                {
                    j = 0;
                    break;
                }
                ++k;
                p = 0;
                buf[k] = 0;
            }
        }
    }
    if (!j)
    {
        // There are non-digit characters, gather them as is.
        j = 1;
        k = 0;
        for (i = pos; j && str[i] != '\0'; ++i)
        {
            char c = str[i];

            if (!isprint(c))
            {
                j = 0;
                break;
            }

            buf[k++] = c;
        }
    }

    if (!j)
    {
        // The characters are not there or are not printable.
        k = 0;
    }

    buf[k] = '\0';

    if (flip)
        // Flip adjacent characters
        for (j = 0; j < k; j += 2)
        {
            char t = buf[j];
            buf[j] = buf[j + 1];
            buf[j + 1] = t;
        }

    // Trim any beginning and end space
    i = j = -1;
    for (k = 0; buf[k] != '\0'; ++k)
    {
        if (!isspace(buf[k]))
        {
            if (i < 0)
                i = k;
            j = k;
        }
    }

    if ((i >= 0) && (j >= 0))
    {
        for (k = i; (k <= j) && (buf[k] != '\0'); ++k)
            buf[k - i] = buf[k];
        buf[k - i] = '\0';
    }
    return buf;
}

/// 源码参考了diskid32（https://www.winsim.com/diskid32/diskid32.html）

ULONG   LocalInfor::getHDSerial(char* pszIDBuff, int nBuffLen, int nDriveID)
{
#ifdef  _WIN32
    HANDLE  hPhysicalDrive = INVALID_HANDLE_VALUE;
    ULONG   ulSerialLen     = 0;
    __try
    {
        TCHAR szDriveName[32];
        wsprintf(szDriveName, TEXT("\\\\.\\PhysicalDrive%d"), nDriveID);
        hPhysicalDrive = CreateFile(
            szDriveName
            , 0
            , FILE_SHARE_READ | FILE_SHARE_WRITE
            , NULL
            , OPEN_EXISTING
            , 0
            , NULL);
        if (hPhysicalDrive == INVALID_HANDLE_VALUE)
        {
            __leave;
        }
        STORAGE_PROPERTY_QUERY  query;
        DWORD                   cbBytesReturned     =   0;
        char                    local_buffer[10240];
        memset(local_buffer, 0, sizeof(local_buffer));
        memset((void *)&query, 0, sizeof(query));
        query.PropertyId    =   StorageDeviceProperty;
        query.QueryType     =   PropertyStandardQuery;

        

        if (DeviceIoControl(hPhysicalDrive, IOCTL_STORAGE_QUERY_PROPERTY,
            &query,
            sizeof(query),
            &local_buffer[0],
            sizeof(local_buffer),
            &cbBytesReturned, NULL))
        {
            STORAGE_DEVICE_DESCRIPTOR * descrip = (STORAGE_DEVICE_DESCRIPTOR *)& local_buffer;
            char serialNumber[1024 * 64] = { 0 };
            flipAndCodeBytes(
                local_buffer
                , descrip->SerialNumberOffset
                , 1
                , serialNumber
            );
            if (isalnum(serialNumber[0]))
            {
                size_t ulSerialLenTemp = strnlen(serialNumber, nBuffLen - 1);
                memcpy(pszIDBuff, serialNumber, ulSerialLenTemp);
                pszIDBuff[ulSerialLenTemp] = NULL;
                ulSerialLen = (ULONG)ulSerialLenTemp;
                __leave;
            }
        }
    }
    __finally
    {
        if (hPhysicalDrive != INVALID_HANDLE_VALUE)
        {
            CloseHandle(hPhysicalDrive);
        }
        return ulSerialLen;
    }
#else
    return  0;
#endif
}

void    LocalInfor::fillLocalInfor(LicensSource& source)
{
    source.cpu      =   getCpuSerial();
    source.hd       =   getHDSerial();
    source.ipaddrs  =   getMacAddress();
    source.os       =   osName();
}

String  LocalInfor::osName()
{
#ifdef _WIN32
    return  "windows";
#elif defined __linux__
    return  "linux";
#else
    return  "unknow";
#endif
}

String  LocalInfor::toZip(const char* data,size_t nLen)
{
    size_t      nSrcLen         =   nLen;
    char        szDst[10240]    =   {0};
    Bytef*      pSRC            =   (Bytef*)data;
    Bytef*      pDST            =   (Bytef*)szDst;
    uLong       srcLen          =   (uLong)nSrcLen;
    uLong       dstLen          =   sizeof(szDst);
    int         res             =   compress(pDST, &dstLen, pSRC, srcLen);
    std::string key;
    for (uLong i = 0; i < dstLen ; ++ i)
    {
        char hex[16]    =   {0};
        uint data       =   pDST[i];
        sprintf(hex,"%02x",data);
        key +=  hex;
    }
    return  key;
}



String  LocalInfor::getHDSerial(void)
{
#ifdef  _WIN32
    int     MAX_IDE_DRIVES = 16;
    char    szBuff[1024];
    for (int nDriveNum = 0; nDriveNum < MAX_IDE_DRIVES; nDriveNum++)
    {
        ULONG ulLen = getHDSerial(szBuff, sizeof(szBuff), nDriveNum);
        if (ulLen > 0)
        {
            return szBuff;
        }
    }
    return  "none-disk-card-serial";
#elif defined __linux__
    Strings         fileNames;
    DIR*            dir =   opendir("/dev/");
    struct  dirent* ent =   nullptr;
    if(dir != nullptr)
    {
        while((ent = readdir(dir)) != nullptr)
        {
            std::string tmp = ent->d_name;
            if(ent->d_type == DT_BLK)
            {
                std::string fileName = ent->d_name;
                if(fileName.find("sda"))
                    continue;
                fileName    =   "/dev/" + fileName;
                fileNames.push_back(fileName);
            }
        }
    }
    ///if errno = 13， 在获取HDserial时，必须使用root限权
    for(auto& var : fileNames)
    {
        struct  hd_driveid hid  =   {};  
        int     fd              =   open(var.c_str(), O_RDONLY);
        if(fd >= 0 && ioctl(fd, HDIO_GET_IDENTITY, &hid) >= 0)
        {
            close(fd);
            return  (char*)hid.serial_no;
        }
    }
    return "none-disk-card-serial";
#else
    return "none-disk-card-serial";
#endif 
}

String  LocalInfor::getCpuSerial()
{
#ifdef  _WIN32
    INT32 dwBuf[4] = { 0 };
    __cpuidex(dwBuf, 1, 1);
    char szTmp[40] = { NULL };
    sprintf_s(szTmp, "%08X%08X", dwBuf[0], dwBuf[3]);
    return  szTmp;
#elif defined __linux__
    char            szTmp[64]   = {0};
    unsigned int    p[2]        = {0};
    
    //asm volatile(
    //    "movl $0x01, %%eax; \n\t"
    //    "xorl %%edx, %%edx; \n\t"
    //    "cpuid;\n\t"
    //    "movl %%edx, %0\n\t"
    //    "movl %%eax, %1\n\t"
    //    : "=m" (p[0]), "=m"(p[1])
    //    );
    // snprintf(szTmp, sizeof(szTmp), "%08X%08X", htonl(p[1]), htonl(p[0]));
    // return  szTmp;
    return  "CUP-NULL";
#else
    return  "CUP-NULL";
#endif
}

Strings LocalInfor::getMacAddress()
{
    Strings     addrList;
#ifdef  _WIN32
    /// PIP_ADAPTER_INFO结构体指针存储本机网卡信息
    PIP_ADAPTER_INFO pIpAdapterInfo = new IP_ADAPTER_INFO();
    /// 得到结构体大小,用于GetAdaptersInfo参数
    ULONG   stSize      =   sizeof(IP_ADAPTER_INFO);
    /// 调用GetAdaptersInfo函数,填充pIpAdapterInfo指针变量;其中stSize参数既是一个输入量也是一个输出量
    ULONG   nRel        =   GetAdaptersInfo(pIpAdapterInfo, &stSize);


    if (ERROR_BUFFER_OVERFLOW == nRel)
    {
        /// 如果函数返回的是ERROR_BUFFER_OVERFLOW
        /// 则说明GetAdaptersInfo参数传递的内存空间不够,同时其传出stSize,表示需要的空间大小
        /// 这也是说明为什么stSize既是一个输入量也是一个输出量
        /// 释放原来的内存空间
        delete pIpAdapterInfo;
        /// 重新申请内存空间用来存储所有网卡信息
        pIpAdapterInfo = (PIP_ADAPTER_INFO) new BYTE[stSize];
        /// 再次调用GetAdaptersInfo函数,填充pIpAdapterInfo指针变量
        nRel    =   GetAdaptersInfo(pIpAdapterInfo, &stSize);
    }
    if (ERROR_SUCCESS == nRel)
    {
        PIP_ADAPTER_INFO	info = pIpAdapterInfo;
        while (info)
        {
            if (info->AddressLength >= 6)
            {
                char    macAddr[128] = { 0 };

                sprintf_s(macAddr, "%02X:%02X:%02X:%02X:%02X:%02X",
                    info->Address[0],
                    info->Address[1],
                    info->Address[2],
                    info->Address[3],
                    info->Address[4],
                    info->Address[5]);

                auto itr = std::find(addrList.begin(),addrList.end(),macAddr);
                if (itr == addrList.end())
                {
                    addrList.push_back(macAddr);
                }
            }
            info = info->Next;
        }
    }
    //释放内存空间
    if (pIpAdapterInfo)
    {
        delete pIpAdapterInfo;
    }
#elif defined __linux__

    struct ifreq    ifr;
    struct ifconf   ifc;
    char    buf[2048];
    int     sock    =   socket(AF_INET, SOCK_DGRAM, IPPROTO_IP);
    if(sock == -1)
        return {""};

    ifc.ifc_len = sizeof(buf);
    ifc.ifc_buf = buf;
    if(ioctl(sock, SIOCGIFCONF, &ifc) == -1)
    {
        close(sock);
        return {""};
    }
    struct          ifreq*          it  = ifc.ifc_req;
    const struct    ifreq* const    end = it + (ifc.ifc_len / sizeof(struct ifreq));
    char            szMac[128]  =   {0};
    for(; it != end; ++it)
    {
        strcpy(ifr.ifr_name, it->ifr_name);
        if(ioctl(sock, SIOCGIFFLAGS, &ifr) == 0)
        {
            if(!(ifr.ifr_flags & IFF_LOOPBACK))
            {
                if(ioctl(sock, SIOCGIFHWADDR, &ifr) == 0)
                {
                    unsigned char* ptr;
                    ptr = (unsigned char*)&ifr.ifr_ifru.ifru_hwaddr.sa_data[0];
                    snprintf(szMac, 64, "%02X:%02X:%02X:%02X:%02X:%02X", *ptr, *(ptr + 1), *(ptr + 2), *(ptr + 3), *(ptr + 4), *(ptr + 5));
                    addrList.push_back(szMac);
                }
            }
        }
    }
    close(sock);
#else
    addrList.push_back("00:00:00:00:00:00");
#endif
    return  addrList;
}


YMD     LocalInfor::getDate(int offDay)
{
    time_t      now     =   time(NULL);
    time_t      dtime   =   now + 60 * 60 * 24 * offDay;
    struct tm*  lt      =   localtime(&dtime);
    char        date[64]=   { 0 };

    return  {lt->tm_year + 1900, lt->tm_mon + 1, lt->tm_mday};
}
YMD     LocalInfor::getDateOffYear(int offYear)
{
    time_t      now     =   time(NULL);
    struct tm*  lt      =   localtime(&now);
    char        date[64]=   { 0 };

    return  {lt->tm_year + 1900 + offYear, lt->tm_mon + 1, lt->tm_mday};
}


bool    LicenseMgr::check(const String& vt,const String& localstr,const String& license,YMD& ymd,int& maxSession)
{
    int     checkSession    =   maxSession;
    int     loop            =   maxSession == -1 ? 1 : MAX_SESSION;
    printf("2025-04-22:loop:%d\n",  loop);
    printf("2025-04-22:local:%s\n", localstr.c_str());

    for (int x = 0; x < loop ;  ++x)
    {
        /// 特殊时间值,则认为是不验证时间
        String  licen   =   create(vt,localstr,1234,56,78,x);
        if (strstr(license.c_str(),licen.c_str()) != nullptr)
        {
            maxSession  =   x;
            ymd         =    LocalInfor::getDateOffYear(100);
            return  true;
        }
    
        for (int i = 0; i < MAX_DAYS; i++)
        {
            auto    date    =   LocalInfor::getDate(i);
            String  licen   =   create(vt,localstr,date.year,date.month,date.day,maxSession != -1 ? x : 0);
            /// 字符串查找
            if (strstr(license.c_str(),licen.c_str()) != nullptr)
            {
                maxSession  =   maxSession != -1 ? x : i;
                ymd         =   date;
                return  true;
            }
        }
        if (checkSession == 0)
            break;
    }
    return  false;
}

bool    LicenseMgr::check(const String& vt,const LicensSource& src,const String& licenseSrc,YMD& ymd,int& maxSession)
{
    printf("src.product =   %s\n",  src.product.c_str());
    printf("src.version =   %s\n",  src.version.c_str());
    printf("src.hd      =   %s\n",  src.hd.c_str());
    printf("src.os      =   %s\n",  src.os.c_str());
    printf("src.ipaddrs =   %d\n",  (int)src.ipaddrs.size());
    for (auto& var : src.ipaddrs)
    {
        printf("src.ipaddrs =       %s\n",  var.c_str());
    }
    String  base    =   src.product + src.version + src.hd + src.cpu + src.os ;
    for (auto& var : src.ipaddrs)
    {
        String  locals  =   base + var;
        if (check(vt,locals,licenseSrc,ymd,maxSession))
            return  true;
    }
    return  false;
}

String  LicenseMgr::create(const String& vt,const LicensSource& src,int year,int month,int day,int maxSession)
{
    
    String  result;
    String  base    =   src.product + src.version + src.hd + src.cpu + src.os ;
    for (auto& var : src.ipaddrs)
    {
        String  locals  =   base + var;
        String  license =   create(vt,locals,year,month,day,maxSession);
        result          +=  license;
        result          +=  "\n";    
    }
    return  result;
}

String  LicenseMgr::create(const String& vt,const String& localstr,int year,int month,int day,int maxSession)
{
    char    date[64]        =   { 0 };
    char    srcKey[2048]    =   { 0 };
    if (maxSession == 0)
        sprintf(date, "%04d%02d%02d",   year, month, day);
    else
        sprintf(date, "%04d%02d%02d%4d",year, month, day,maxSession);
    
    /// 社交本只校验版本跟日期，其他不做校验
    if (VT_Community == vt)
    {
        sprintf(srcKey, "%s%s%s%s%s"
            , "PLANT RESOURCE TECHNOLOGY CO.,LTD."
            , "123456"
            , vt.c_str()
            , ""
            , date);
    }
    else
    {
        sprintf(srcKey, "%s%s%s%s%s"
            , "PLANT RESOURCE TECHNOLOGY CO.,LTD."
            , "123456"
            , vt.c_str()
            , localstr.c_str()
            , date);
    }

    CELL::CELLMD5Key md51 = CELL::CELLMD5::fromString(srcKey);

    char    key1[64] = { 0 };
    sprintf(key1, "%08X%08X%08X%08X", md51._bufferInt[3], md51._bufferInt[2], md51._bufferInt[1], md51._bufferInt[0]);

    for (size_t i = 0; i < 16; i++)
    {
        char tmp = key1[i * 2];
        key1[i * 2] = key1[i * 2 + 1];
        key1[i * 2 + 1] = tmp;
    }
    CELL::CELLMD5Key md52 = CELL::CELLMD5::fromString(key1);
    char    key2[64] = { 0 };
    sprintf(key2, "%08X%08X%08X%08X", md52._bufferInt[3], md52._bufferInt[2], md52._bufferInt[1], md52._bufferInt[0]);
    for (size_t i = 0; i < 16; i++)
    {
        char tmp = key2[i * 2];
        key2[i * 2] = key2[i * 2 + 1];
        key2[i * 2 + 1] = tmp;
    }
    return  key2;
}
extern  "C"
{
    API_LICENSE const char* paramData(LParam param)
    {
        std::string*    pStr    =   (std::string*)param;
        return  pStr->c_str();
    }
    API_LICENSE void       destroyParam(LParam param)
    {
        std::string*    pStr    =   (std::string*)param;
        delete  pStr;
    }

    API_LICENSE bool        checkLicense(const char* vt,const char* localstr,const char* license,int* y,int* m,int* d,int* maxSession)
    {
        YMD             ymd =   {0,0,0};
        int             ms  =   0;
        
        if (localstr == nullptr || strlen(localstr) == 0)
        {
            LicensSource    src;
            LocalInfor::fillLocalInfor(src);

            String          licenses        =   license;
            /// 默认版本类型
            String          versionType     =   "free";
            String          licenseVersion;
            size_t          nIndex          =   0;
            size_t          nStart          =   0;
            for (size_t i = 0; i < licenses.size(); i++)
            {
                if (licenses[i] == '@')
                {
                    switch (nIndex)
                    {
                    case 0:
                        licenseVersion  =   licenses.substr(nStart,i - nStart);
                        nStart          =   i + 1;
                        break;
                    case 1:
                        versionType     =   licenses.substr(nStart,i - nStart);
                        nStart          =   i + 1;
                        break;
                    case 2:
                        src.product     =   licenses.substr(nStart,i - nStart);
                        nStart          =   i + 1;
                        break;
                    case 3:
                        src.version     =   licenses.substr(nStart,i - nStart);
                        nStart          =   i + 1;
                        break;
                    default:
                        break;
                    }
                    ++nIndex;
                }
            }
            auto    result  =  LicenseMgr::check(vt,src,license,ymd,*maxSession);
            if (!result)
                return  false;
        }
        else
        {
            auto    result  =  LicenseMgr::check(vt,localstr,license,ymd,*maxSession);
            if (!result)
                return  false;
        }
        if(y)           *y          =   ymd.year;
        if(m)           *m          =   ymd.month;
        if(d)           *d          =   ymd.day;
        if (maxSession) *maxSession =   ms;
        return  true;
    }

    API_LICENSE LParam      createLicenseApi(  const char* product
                                                ,const char* version
                                                ,const char* base64
                                                ,const char* key
                                                ,int y
                                                ,int m
                                                ,int d
                                                ,int maxSession
                                                ,const char* type)
    {
        std::string*    pRes    =   new std::string;
                        *pRes   =   Helper::createLicense(product,version,base64,key,y,m,d,maxSession,type);
        return  pRes;
    }

    API_LICENSE const char* decodeData(const char* encode,const char* key)
    {
        static  String  res;
        res =   LocalInfor::decode(encode,key);
        return  res.c_str();
    }
}


int     main(int argc,char** argv)
{
    system("chcp 65001");

    CMDLine::Parser a;
    /// 输入，文件夹，或者文件
    a.add<std::string>( "name",     'n',    "product name",         true,       "");
    /// 输出,cpp文件
    a.add<std::string>( "type",     'v',    "type:developer,professional,personal,community,enterprise", false,       "enterprise");
    /// 默认 false,输出json,如果 true,输出xml
    a.add<std::string>( "out",      'o',    "local keyfile",        false,       "Local.LSRC");

    bool ok = a.parse(argc, argv);

    if (!ok || argc < 2 ) 
    {
        std::cout << a.usage() ;
        return 0;
    }

    std::string strName     =   a.get<std::string>("name");
    std::string strType     =   a.get<std::string>("type");
    std::string outFile     =   a.get<std::string>("out");


    String  localData       =   Helper::collectLocalData(strName,strType);
   
    FILE*   pFile           =   fopen(outFile.c_str(), "wb");
    if (pFile == 0)
    {
        printf("提示,创建授权文件失败，请检查是否具备文件创建权限!");
        return  0;
    }
    fwrite(localData.data(),    1,  localData.size(),  pFile);
    fclose(pFile);
    printf("提示,创建授权文件成功!");
    return  0;
}

