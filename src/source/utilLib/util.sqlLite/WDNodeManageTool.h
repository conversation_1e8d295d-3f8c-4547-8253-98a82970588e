#pragma     once
#include "WDCore.h"
#include "common/WDFileReader.hpp"
#include "WDSqlLiteApiDefine.h"
#include "node/WDNode.h"
#include "WDSqlLiteSession.h"
#include "common/WDObject.h"
#include "property/WDProperty.h"
#include <any>
#include "material/WDMaterial.h"

#define SQL_MAX_LEN 512

WD_NAMESPACE_BEGIN

WD_DECL_CLASS_UUID(NodesManageTool,"{ECFA1E53-02AF-41D6-91E6-EEF3ECDEAD70}");
class SQLLITE_API NodesManageTool : public WDObject
{
    WD_DECL_OBJECT(NodesManageTool)
public:
    using       Nodes           =   std::vector<WDNodeSPtr>;
    using       ArrayString     =   std::vector<std::string>;
    using       Record          =   ArrayString;
    using       Records         =   std::vector<Record>;
    using       Objects         =   std::vector<WDObjectSPtr>;
    using       MapObject       =   std::map<WDUuid, WDObjectSPtr>;
    using       DbDataType      =   DataBaseSession::DbDataType;
    using       ArrayDbData     =   DataBaseSession::ArrayDbData;
    using       BatchDbData     =   DataBaseSession::BatchDbData;
private:
    DataBaseSession* _sqliteSession = nullptr;
public:
    /**
     * @brief ����db�ļ�·����������򿪣����������½�
     * @param dbFileName �ļ�·����':memory:'��ʾ���ڴ湹�����ݿ�
    */
    NodesManageTool(const char* dbFileName = ":memory:");
    ~NodesManageTool();
    /**
     * @brief ʹ��db�ļ����г�ʼ��
     * @param dbFileName db�ļ�·��
    */
    bool    initByDbFile(const char* dbFileName);
    /**
     * @brief ʹ��sql�ļ����г�ʼ��
     * @param sqlFileName sql�ļ�·��
    */
    bool    initBySqlFile(const char* sqlFileName);
    /**
     * @brief ��ȡ��ǰ����
    */
    inline  DataBaseSession*    getSession()
    {
        return _sqliteSession;
    }
public:
    /**
     * @brief ������ڵ��Լ������Ϣд�����ݿ�
     * @param nodes ���ڵ�ļ���
     * @return д��ڵ���
    */
    size_t  createByNodes(const Nodes& nodes);
    /**
     * @brief ��ȡ�ļ���д�����ݿ�
     * @param fileName ���ļ�·��
     * @return д��ڵ���
    */
    size_t  createByFile(const char* fileName);
    /**
     * @brief �����ж�ȡ��д�����ݿ�
     * @param stream ������
     * @return �ֽ���
    */
    size_t  createByStream(WDInStream* stream);
    /**
     * @brief �����ݿ��ȡ�ڵ�
     * @return ��ȡ�ڵ���
    */
    size_t  fetchNodes(Objects& objs);
    /**
     * @brief �����ݿ��ȡ�ڵ㵽�ļ���
     * @param fileName ���ļ�·��
     * @return ��ȡ�ڵ���
    */
    size_t  fetchNodes(const char* fileName);
    /**
     * @brief �����ݿ��ȡ������
     * @param stream �����
     * @return �ֽ���
    */
    size_t  fetchNodes(WDOutStream* stream, Objects& objs);

    /**
     * @brief ��ѯ
     * @param sql ��ѯ��䣬����'?'
     * @param arrayValues ���'?'��ֵ
     * @return ��ѯ���
    */
    inline  BatchDbData query(const char* sql, BatchDbData& batchData)
    {
        auto results    =   _sqliteSession->queryRecords(sql, batchData);
        return results;
    }
    /**
     * @brief ��ѯ
     * @param sql ��ѯ��䣬����?
     * @return ��ѯ���
    */
    Records query(const char* sql);
    /**
     * @brief �����¼
     * @param sql ������䣬����'?'
     * @param arrayValues ���'?'��ֵ
     * @return �����¼����
    */
    inline  size_t  insert(const char* sql, BatchDbData& batchData)
    {
        return _sqliteSession->insertRecords(sql, batchData);
    }
    /**
     * @brief ��Ӷ���ڵ㵽���ݿ�
     * @param nodes ���ڵ㼯
     * @return ��ӵĽڵ���
    */
    size_t  addNodes(const Nodes& nodes);
    /**
     * @brief �����ݿ�ɾ���ڵ㣨�����
     * @param nodes Ҫɾ���Ľڵ�
     * @return ɾ���Ľڵ������ֻͳ�Ƹ���
    */
    size_t  deleteNodes(Nodes nodes);
    /**
     * @brief �����ݿ�ɾ���ڵ㣨�ݹ�ɾ���ӽڵ㣩
     * @param uuIds �ڵ��uuid
    */
    bool    deleteNode(const char* uuid);

    /**
     * @brief �����ݿ�ɾ�����ԣ��ݹ�ɾ�������ԣ�
     * @param uuid ����uuid
    */
    bool    deleteProperty(const char* uuid);
    /**
     * @brief �����ݿ�ɾ�����
     * @param uuid ���uuid
    */
    bool    deleteComponent(const char* uuid);
    /**
     * @brief  �����ݿ�ɾ������
     * @param uuid ����uuid
    */
    bool    deleteTexture(const char* uuid);
    /**
     * @brief �޸Ľڵ���Ϣ����д�ڵ���Ϣ��
     * @param nodes Ŀ��ڵ�
     * @return �ɹ��޸ĵĽڵ���
    */
    size_t  modifyNodesInfo(const Nodes& nodes);
    /**
     * @brief ��ѯ��ļ�¼��
     * @param tableName ����
     * @return ��¼��
    */
    size_t  tableRecordsCount(const char* tableName);
    /**
    * @brief ������ݿ��Լ���ر����������൱�����ù���״̬
    */
    bool    clean();
    /**
     * @brief ���Ƶ�ǰ���ӵ����ݵ�Ŀ�����ݿ�
     * �޷���������
     * @param dstDbName Ŀ�����ݿ�
    */
    bool    copyToDbFile(const char* dstDbFileName);
    /**
     * @brief ��Դ���ݿ�����ݸ��Ƶ���ǰ����(db�ļ���
     * �޷���ȡ������
     * @param srcDbName Դ���ݿ��ļ�·��
    */
    bool    readFromDbFile(const char* srcDbFileName);
    /**
     * @brief ��sql�ļ���ȡsql��䲢ִ��
     * ���Զ�ȡ������
     * @param srcSqlFileName �ļ�·��
    */
    bool    readFromSqlFile(const char* srcSqlFileName);
    /**
     * @brief ִ��sql��䣬ͬʱͨ���ص������Խ�����й���/����
     * @param sql sql���
     * @param callback �������δ����ص�ÿ����¼�Ļص�����
     * @param arg ����ص������ĵ�һ���������ȿ��Դ���ص��������������
     * Ҳ���Դ�Ŵ���������
    */
    inline bool execOnlie(const char* sql, int(*callback)(void*, int, char**, char**) = NULL, void* arg = NULL)
    {
        return getSession()->executeSql(sql, callback, arg);
    }
    /**
     * @brief ��ȡĳproperty�����ĸ�
     * @param propertyId �����Ե�uid
     * &return ����uid
    */
    std::string fetchRootProperty(const char* propertyId);
private:
    /**
     * @brief �ռ�node��Ϣ��д�����ݿ�
     * @param pNode �ڵ����
     * @param bRoot �Ƿ�Ϊ���ڵ㣬����ĸ��ڵ㲢�������ڵ�Ϊ��
     * ���������Ҫ��Ϊ��ǿ��һЩ�Ǹ��ڵ��Ը��ڵ㷽ʽд�����ݿ�
     ������������Ƿ�Ϊ����ĵ�һ���ڵ�
     * @return ����Ľڵ���
    */
    size_t  collectNodeInfo(WDNode* pNode, bool bRoot = false);
    /**
     * @brief �ռ������Ϣ��д�����ݿ�
     * @param pCom �������
     * @return ִ��״̬
    */
    bool    collectComponentInfo(WDNodeComponent* pCom);
    /**
     * @brief ����������󣬻�ȡ������µ��������д�����ݿ�
     * @param pCom �������
     * @return ִ��״̬
    */
    void    collectTextureInfo(WDNodeComponent* pCom);
    /**
     * @brief �ռ�������Ϣ��д�����ݿ�
     * @param ptyGroup ���Զ���
     * @param parentId ������id
     * @return ִ��״̬
    */
    bool    collectPropertyGroupInfo(WDPropertyGroup* ptyGroup, const char* parentId);
    /**
     * @brief ���ݽڵ�uid��ѯ�ڵ�
     * @param nodeId �ڵ�uid
     * @return �ڵ����
    */
    WDNodeSPtr  fetchNode(const char* nodeId);
    /**
     * @brief ��������uid��ѯ���ԣ����ҵ�����
     * @param propertyId ����uid
     * @param parent ������
    */
    void    fetchProperty(const char* propertyId, WDPropertyGroup& parent);
    /**
     * @brief ������Ӧ����ֵ������ö������飬�����uid��ѯ������
     * @param propertyId ����uid
     * @param parent ������
     * @param type, name, value ����ֵ
    */
    void    fetchProperty(const char* propertyId, WDPropertyGroup& parent, const char* type, const char* name, const char* value);
    /**
     * @brief �������uid��ѯ���
     * @param componentId ���uid
     * @return �������
    */
    WDNodeComponentSPtr fetchComponent(const char* componentId);
    /**
     * @brief ��������uid��ѯ����
     * @param textureId ����uid
     * @param mapObj ������������Ų�����������
    */
    void    fetchTexture(const char* textureId, MapObject& mapObj);
    /**
     * @brief ��װsql��䲢prepare
    */
    void    initSqlTemplates();
public:
    /**
     * @brief ж����������������д��
     * @return ж�ص�������
    */
    size_t  unloadIndex();
    /**
     * @brief ����sql�ļ���sql�������������
     * @param indexCreateSqlFile sql�ļ���������������sql���
     * @return ������������
    */
    size_t  loadIndex(const char* indexCreateSqlFile);

    inline static constexpr const char* NodeTableName()
    {
        return _NodeTableName;
    }
    inline static constexpr const char* ComponentTableName()
    {
        return _ComponentTableName;
    }
    inline static constexpr const char* PropertyTableName()
    {
        return _PropertyTableName;
    }
    inline static constexpr const char* TextureTableName()
    {
        return _TextureTableName;
    }
    inline static constexpr const char* Node2PropertyTableName()
    {
        return _Node2PropertyTableName;
    }
    inline static constexpr const char* Component2PropertyTableName()
    {
        return _Component2PropertyTableName;
    }
    inline static constexpr const char* Texture2PropertyTableName()
    {
        return _Texture2PropertyTableName;
    }
    inline static constexpr const char* Node2ComponentTableName()
    {
        return _Node2ComponentTableName;
    }
    inline static constexpr const char* Component2TextureTableName()
    {
        return _Component2TextureTableName;
    }
    inline static constexpr const char* DefaultUid()
    {
        return _DefaultUid;
    }
private:
    // ��¼�����uid, ��ֹ����ظ�д��
    std::set<WDUuid> _componentUIds;
    // ��¼texture��uid����ֹtexture�ظ�д��
    std::set<WDUuid> _textureUIds;

    // ���ݿ��еı���
    static constexpr const char*     _NodeTableName                  =   "nodes";
    static constexpr const char*     _ComponentTableName             =   "components";
    static constexpr const char*     _PropertyTableName              =   "properties";
    static constexpr const char*     _Node2PropertyTableName         =   "node2Property";
    static constexpr const char*     _Component2PropertyTableName    =   "component2Property";
    static constexpr const char*     _Node2ComponentTableName        =   "node2Component";
    static constexpr const char*     _TextureTableName               =   "textures";
    static constexpr const char*     _Texture2PropertyTableName      =   "texture2Property";
    static constexpr const char*     _Component2TextureTableName     =   "component2Texture";

    // �������ݿ��db�ļ������ڳ�ʼ���½������ݿ�
    static constexpr const char*     _InitDbFile         =   "../../../../../bin/data/sqlite/initTables.db";
    // �������ݿ��sql�ļ������ڳ�ʼ���½������ݿ�
    static constexpr const char*     _InitSqlFile        =   "../../../../../bin/data/sqlite/initTables.sql";
    // �����ļ�������������������
    static constexpr const char*     _IndexCreateSqlFile =   "../../../../../bin/data/sqlite/indexCreate.sql";
    // Ĭ��uid�����ڱ�ʾuidΪ�յ���������縸�ڵ�Ϊ�յ�
    static constexpr const char*     _DefaultUid         =   "{00000000-0000-0000-0000-000000000000}";

    // ���ÿ����Ĳ������
    char _SqlInsertNodes[SQL_MAX_LEN];
    char _SqlInsertProperties[SQL_MAX_LEN];
    char _SqlInsertComponents[SQL_MAX_LEN];
    char _SqlInsertNode2Property[SQL_MAX_LEN];
    char _SqlInsertComponent2Property[SQL_MAX_LEN];
    char _SqlInsertNode2Component[SQL_MAX_LEN];
    char _SqlInsertTextures[SQL_MAX_LEN];
    char _SqlInsertTexture2Property[SQL_MAX_LEN];
    char _SqlInsertComponent2Texture[SQL_MAX_LEN];
};

WD_NAMESPACE_END