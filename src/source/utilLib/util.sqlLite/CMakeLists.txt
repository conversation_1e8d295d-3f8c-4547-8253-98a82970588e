set(TARGET_NAME util.sqlLite)

set(HEADER_FILES

	"WDSqlLite.h"
	"WDSqlLiteApiDefine.h"
	"WDSqlLiteSession.h"
	"WDNodeManageTool.h"
)

set(SOURCE_FILES
	"WDNodeManageTool.cpp"
	"WDSqlLiteSession.cpp"
)

add_library(${TARGET_NAME} SHARED
		${HEADER_FILES}
		${SOURCE_FILES}
)

target_include_directories(${TARGET_NAME} PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})

target_compile_definitions(${TARGET_NAME} PRIVATE
	-DSQLLITE_EXPORTS
)

target_link_libraries(${TARGET_NAME} PRIVATE wizDesignerCore)

find_package(SQLite3 REQUIRED)
target_link_libraries(${TARGET_NAME} PRIVATE SQLite::SQLite3)

CopyImportedRuntimeDependency(${TARGET_NAME})