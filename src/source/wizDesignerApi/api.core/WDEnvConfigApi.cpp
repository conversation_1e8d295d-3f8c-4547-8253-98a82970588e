#include "WDEnvConfigApi.h"
#include "businessModule/design/WDBMDesign.h"
#include "businessModule/catalog/WDBMCatalog.h"
#include "extension/WDExtensionMgr.h"
#include "WDRapidjson.h"
#include "core/log/WDLoggerPort.h"
#include "businessModule/WDBMAuditObjectMgr.h"
#include "businessModule/WDBMPermissionMgr.h"
#include "log/WDLogger.h"

CORE_API Core   coreEnvInit(const char* exeDirPath, const char* dataPath)
{
    char logNameBuf[1024] = { 0 };
    sprintf_s(logNameBuf, sizeof(logNameBuf), "%s/log.txt", exeDirPath);
    // 日志
    WD::WDLogger::getInstance()->open(logNameBuf);
    // 新建core
    auto pCore = new WD::WDCore(exeDirPath, dataPath);
    //组件，项目里是在main里执行init
    pCore->getBMDesign().init();
    pCore->getBMCatalog().init();

    // 启用设计模块权限
    pCore->getBMDesign().permissionMgr().setEnabled(true);
    // 启用设计模块校审对象模式
    pCore->getBMDesign().auditObjectMgr().setEnabled(true);

    LOG_INFO << "环境已创建";

    return pCore;
}

CORE_API BOOL   releaseCoreEnv(Core core)
{
    WD::WDCore* pCore = (WD::WDCore*)core;
    if(pCore == nullptr)
        return false;
    delete pCore; //释放core
    LOG_INFO << "环境已释放";
    WD::WDLogger::getInstance()->close();
    return true;
}

CORE_API BOOL   loadNodesFromJsonToCore(Core core, const char* jsonFilePath)
{
    WD::WDCore* pCore = (WD::WDCore*)core;
    if(pCore == nullptr)
        return false;

    return pCore->getBMCatalog().load(jsonFilePath);
}
