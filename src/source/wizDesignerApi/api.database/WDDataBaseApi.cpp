#include "WDDataBaseApi.h"
#include "mysql.h"
#include "node/WDComponentMaterial.h"
#include "businessModule/design/WDBMDesignMgr.h"
#include "businessModule/catalog/WDBMCatalogMgr.h"
#include "extension/WDExtensionMgr.h"
#include "../source/extensions/pluginFormats/plugin.format.xml/plugin.format.xml.h"


DATABASE_API     DBHandle    msDbHandle( Core core
                                                , const char* host
                                                , const char* user
                                                , const char* password
                                                , const char* database
                                                , int port
                                                , const char* charSet)
{
    CoreEnv* pCoreEnv = (CoreEnv*)core;
    if(pCoreEnv == nullptr)
        return nullptr;
    //auto obj = pCore->objectCreator().create(WD::GUID_MySqlDataBase);
    auto db = WD::MysqlDataBase::MakeShared();
    db->open({{WD::MysqlDataBase::HostK<PERSON>(), host}
            , {WD::MysqlDataBase::User<PERSON><PERSON>(), user}
            , {WD::MysqlDataBase::PasswordKey(), password}
            , {WD::MysqlDataBase::DataBaseKey(), database}
            , {WD::MysqlDataBase::PortKey(), std::to_string(port).c_str()}
            , {WD::MysqlDataBase::CharSetKey(), charSet}});
    if(!db->isValid())
        return nullptr;
    pCoreEnv->g_MapObjects[db->uuid()] = db;
    return  db.get();
}


DATABASE_API BussinessHandle msBussinessHandle(Core core,DBHandle db, BOOL isCatalog)
{
    CoreEnv* pCoreEnv = (CoreEnv*)core;
    if(pCoreEnv == nullptr)
        return nullptr;
    auto pCore = pCoreEnv->core;
    if(pCore == nullptr)
        return nullptr;

    WD::WDDataBase* pDb = (WD::WDDataBase*)db;
    if(pDb == nullptr || !pDb->isValid())
        return nullptr;

    auto tool = WD::MysqlTool::MakeShared(*pCore, pDb, isCatalog);
    if(tool == nullptr || !tool->isValid())
        return nullptr;
    pCoreEnv->g_MapObjects[tool->uuid()] = tool;
    return tool.get();
}

DATABASE_API ProjectHandle   msProjectHandle(Core core, DBHandle designDB, DBHandle catalogDB)
{
    CoreEnv* pCoreEnv = (CoreEnv*)core;
    if(pCoreEnv == nullptr)
        return nullptr;
    auto pCore = pCoreEnv->core;
    if(pCore == nullptr)
        return nullptr;

    auto pd = (WD::WDDataBase*)designDB;
    auto pc = (WD::WDDataBase*)catalogDB;

    auto prj = std::make_shared<WD::ProjectUseMysql>(*pCore, pd, pc);

    pCoreEnv->g_MapObjects[prj->uuid()] = prj;
    return prj.get();
}

DATABASE_API BOOL    msProjectLoadCatalog(ProjectHandle project)
{
    auto prj = (WD::WDProject*)project;
    if(prj == nullptr)
        return false;
    return prj->loadCatalogModule();
}

DATABASE_API BOOL    msProjectLoadDesign(ProjectHandle project)
{
    auto prj = (WD::WDProject*)project;
    if(prj == nullptr)
        return false;
    return prj->loadDesignModule();
}

DATABASE_API BOOL    msProjectLoadModules(ProjectHandle project)
{
    return msProjectLoadCatalog(project) && msProjectLoadDesign(project);
}

DATABASE_API int     getByNameToStream(ProjectHandle project, const char* name, void* buf, int bufLen, BOOL queryCatalog)
{
    auto prj = (WD::WDProject*)project;
    if(prj == nullptr || bufLen == 0 || buf == nullptr)
        return 0;

    WD::MysqlTool::Stream stream;
    if (queryCatalog)
    {
        prj->fetchFromCatalogToStream(name, stream);
    }
    else
    {
        prj->fetchFromDesignToStream(name, stream);
    }
    // д��buf
    int byteCount = stream.copyToBuffer(buf, bufLen);
    return byteCount;
}


DATABASE_API int     getByIdToStream(ProjectHandle project, const char* uuid, void* buf, int bufLen, BOOL queryCatalog)
{
    auto prj = (WD::WDProject*)project;
    if(prj == nullptr || bufLen == 0 || buf == nullptr)
        return 0;

    WD::MysqlTool::Stream stream;
    if (queryCatalog)
    {
        prj->fetchFromCatalogToStream(WD::WDUuid::FromString(uuid), stream);
    }
    else
    {
        prj->fetchFromDesignToStream(WD::WDUuid::FromString(uuid), stream);
    }
    int byteCount = stream.copyToBuffer(buf, bufLen);
    return byteCount;
}


DATABASE_API int     getByNameToPR(ProjectHandle project, const char* name
                                                , void* buf, int bufLen
                                                , const char* prVersion
                                                , BOOL queryCatalog)
{
    auto prj = (WD::WDProject*)project;
    if(prj == nullptr || bufLen == 0 || buf == nullptr)
        return 0;

    WD::WDPrTool::PrVersion v = WD::WDPrTool::PrVersionFromStr(prVersion);

    WD::MysqlTool::Stream stream;
    size_t numBytes = 0;
    if (queryCatalog)
        numBytes = prj->fetchFromCatalogToPr(name, stream, v);
    else
        numBytes = prj->fetchFromDesignToPr(name, stream, v);
    if (bufLen < numBytes || stream.empty())
        return 0;
    return stream.copyToBuffer(buf, bufLen);
}

DATABASE_API int     getByIdToPR(ProjectHandle project, const char* uuid
                                                , void* buf, int bufLen
                                                , const char* prVersion
                                                , BOOL queryCatalog)
{
    auto prj = (WD::WDProject*)project;
    if(prj == nullptr || bufLen == 0 || buf == nullptr)
        return 0;

    WD::WDPrTool::PrVersion v = WD::WDPrTool::PrVersionFromStr(prVersion);

    WD::MysqlTool::Stream stream;
    size_t numBytes = 0;
    if(queryCatalog)
        numBytes = prj->fetchFromCatalogToPr(WD::WDUuid::FromString(uuid), stream, v);
    else
        numBytes = prj->fetchFromDesignToPr(WD::WDUuid::FromString(uuid), stream, v);

    if(bufLen < numBytes || stream.empty())
        return 0;
    return stream.copyToBuffer(buf, bufLen);
}


DATABASE_API BOOL    fromStreamToDataBase(BussinessHandle tool, const void* buf, int bufLen)
{
    auto pTool = (WD::WDDataBaseTool*)tool;
    if(pTool == nullptr || bufLen == 0 || buf == nullptr)
        return 0;
    WD::MysqlTool::Stream stream;
    stream.readFromBuffer(buf, bufLen);
    bool ret = pTool->writeNodesFromWdStream(stream);
    return ret;
}
