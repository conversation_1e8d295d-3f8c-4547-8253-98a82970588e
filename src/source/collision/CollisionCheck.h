#pragma once

#include "core/WDCore.h"
#include "core/geometry/WDGeometry.h"

WD_NAMESPACE_BEGIN

class CollisionCheckPrivate;
/**
* @brief 碰撞检测
*/
class CollisionCheck
{
public:
    CollisionCheck();
    ~CollisionCheck();
public:
    /**
    * @brief 两个包围盒碰撞检测
    * @param sAabb 主体包围盒
    * @param oAabb 客体包围盒
    * @param expansion 包围盒扩充(碰撞间隙/碰撞误差)
    * @return 是否碰撞
    */
    bool exec(const Aabb3& sAabb, const Aabb3& oAabb, double expansion);
    /**
    * @brief 两个包围盒碰撞检测
    * @param sAabb 主体包围盒
    * @param sTransform 主体包围盒变换矩阵
    * @param oAabb 客体包围盒
    * @param oTransform 客体包围盒变换矩阵
    * @param expansion 包围盒扩充(碰撞间隙/碰撞误差)
    * @return 是否碰撞
    */
    bool exec(const Aabb3& sAabb
        , const Mat4& sTransform
        , const Aabb3& oAabb
        , const Mat4& oTransform
        , double expansion);
    /**
     * @brief 碰撞返回代码
    */
    enum CollisionRetCode
    {
        /**
         * @brief 未知错误
        */
        CRC_Error = 0,
        /**
         * @brief 检测成功，结果可用
        */
        CRC_Success = 1,
        /**
         * @brief 无效的碰撞主体几何体对象数据
        */
        CRC_InvalidSGeo,
        /**
         * @brief 无效的碰撞客体几何体对象数据
        */
        CRC_InvalidOGeo,
        /**
         * @brief 无效数据,导致无法完成碰撞计算
        */
        CRC_InvalidData,
    };
    /**
     * @brief 碰撞结果数据
    */
    struct CollisionRetData
    {
        std::array<DVec3, 2>  witnesses;
        DVec3 normal;
        //两个几何体的距离，未嵌入时为正值，嵌入时为负值
        real distance;

        CollisionRetData() 
        {
            witnesses[0]    = DVec3::Zero();
            witnesses[1]    = DVec3::Zero();
            normal          = DVec3::Zero();
            distance        = NumLimits<double>::Max;
        }
    };
    /**
    * @brief 两个几何体对象碰撞检测
    * @param pSGeo 主体几何体
    * @param gSGeoMat 主体几何体global变换矩阵,不包含该几何体自身的Transform
    * @param pOGeo 客体几何体
    * @param gOGeoMat 客体几何体global变换矩阵,不包含该集合体自身的Transform
    * @return 测试是否成功,如果数据无效或非法时,将返回false
    */
    CollisionRetCode exec(const WDGeometry& sGeo
        , const Mat4& sTransform
        , const WDGeometry& oGeo
        , const Mat4& oTransform
        , double    minDistance
        , CollisionRetData& outRet);

    // 测试绘制
    void testRenderBegin(WD::WDCore& app);
    // 测试绘制
    void testRenderEnd(WD::WDCore& app);
private:
    /**
     * @brief 标准基本体测试
     * @param sGeo 
     * @param gSTransform 
     * @param oGeo 
     * @param gOTransform 
     * @param outCode 
     * @param outRet 
     * @return 如果几何体是标准基本提且可以使用标准基本体参数优化，则进行碰撞计算并返回true
    */
    bool geometryStdPrisCollision(const WDGeometry& sGeo
        , const Mat4& gSTransform
        , const WDGeometry& oGeo
        , const Mat4& gOTransform
        , CollisionRetCode& outCode
        , CollisionRetData& outRet);
    /**
     * @brief 凸多面体测试
     * @param pSGeo 
     * @param gSTransform 
     * @param pOGeo 
     * @param gOTransform 
     * @param outCode 
     * @param outRet 
     * @return 如果几何体是凸包，则进行碰撞计算并返回true
    */
    bool geometryConvexCollision(const WDGeometry& sGeo
        , const Mat4& gSTransform
        , const WDGeometry& oGeo
        , const Mat4& gOTransform
        , CollisionRetCode& outCode
        , CollisionRetData& outRet);
    /**
     * @brief 凹多面体测试
     * @param pSGeo 
     * @param gSTransform 
     * @param pOGeo 
     * @param gOTransform 
     * @param outCode 
     * @param outRet 
     * @return 其余情况应该是凹多面体了，只能按照
    */
    bool geometryConcaveCollision(const WDGeometry& sGeo
        , const Mat4& gSTransform
        , const WDGeometry& oGeo
        , const Mat4& gOTransform
        , CollisionRetCode& outCode
        , CollisionRetData& outRet);
private:
    CollisionCheckPrivate* _p;
    friend class CollisionCheckPrivate;
};

WD_NAMESPACE_END