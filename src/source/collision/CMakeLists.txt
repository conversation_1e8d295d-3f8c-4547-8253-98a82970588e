set(TARGET_NAME collision)

find_package(Bullet REQUIRED)

set(HEADER_FILES
    "CollisionCheck.h"
	"Private/CollisionCommon.h"
	"Private/ConcaveCollision.h"
	"Private/VHACD.h"
	"Private/GJK.h"
	"Private/EPA.h"
	"Private/GJKEPA.h"
)

set(SOURCE_FILES
	"Private/CollisionCommon.cpp"
	"Private/ConcaveCollision.cpp"
    "CollisionCheck.cpp"
	"Private/GJK.cpp"
	"Private/EPA.cpp"
	"Private/GJKEPA.cpp"
)

add_library(${TARGET_NAME} STATIC
		${HEADER_FILES}
		${SOURCE_FILES}
)

target_link_libraries(${TARGET_NAME} PUBLIC wizDesignerCore)
target_link_libraries(${TARGET_NAME} PRIVATE ${BULLET_LIBRARIES})
target_compile_definitions(${TARGET_NAME} PUBLIC -DBT_USE_DOUBLE_PRECISION)

target_include_directories(${TARGET_NAME} PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
target_include_directories(${TARGET_NAME} PRIVATE ${BULLET_INCLUDE_DIRS})

if(WIN32)
add_custom_command(
	TARGET ${TARGET_NAME}
	POST_BUILD
	COMMAND				CollisionPostBuild.bat
	WORKING_DIRECTORY	${CMAKE_CURRENT_SOURCE_DIR}
)
elseif(UNIX)
add_custom_command(
	TARGET ${TARGET_NAME}
	POST_BUILD
	COMMAND				./CollisionPostBuild.sh
	WORKING_DIRECTORY	${CMAKE_CURRENT_SOURCE_DIR}
)
endif()