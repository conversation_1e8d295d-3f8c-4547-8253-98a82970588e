#include "ConcaveCollision.h"
#include "BulletCollision/Gimpact/btClipPolygon.h"
#include "CollisionCommon.h"

#define MAX_TRI_CLIPPING 16

WD_NAMESPACE_BEGIN


TestRender* TestRender::_pSelf = nullptr;

ConcaveShape::ConcaveShape(TVec3Vector<T>&& points, const TVec3Vector<uint>& tris)
{
    this->rebuild(std::forward<TVec3Vector<T> >(points), tris);
}
void ConcaveShape::rebuild(TVec3Vector<T>&& points, const TVec3Vector<uint>& tris)
{
    _points = std::forward<TVec3Vector<T> >(points);

    Tris().swap(_tris);
    _tris.reserve(tris.size());
    for (const auto& tri : tris)
    {
        _tris.emplace_back(Tri(_points, tri));
    }
}

bool ConcaveShape::Distance(const ConcaveShape& left, const ConcaveShape& right, Ret& outRet, T minClearance)
{
    auto pRender = TestRender::Get();
    pRender->lAabbs.clear();
    pRender->rAabbs.clear();
    pRender->lTris.clear();
    pRender->rTris.clear();
    pRender->nearPoints.clear();

    using TriPair = std::pair<const Tri*, const Tri*>;
    using TriPairs = std::vector<TriPair>;

    // 遍历所有的三角面对，进行处理
    bool bRet = false;
    DTriangle3 retLTri;
    DTriangle3 retRTri;
    outRet.distance = NumLimits<T>::Max;
    for (const auto& lTri : left._tris)
    {
        for (const auto& rTri : right._tris)
        {
            const auto& la = lTri.aabb();
            const auto& ra = rTri.aabb();

            // 计算包围盒距离，如果超过指定的最小间隙，则跳过后续检查
            auto tDis = DAabb3::Distance(la, ra);
            if (tDis > minClearance)
                continue;
            // 计算最小距离
            if (!Tri::Distance(lTri, rTri, outRet, minClearance))
                continue;

            bRet    = true;
            retLTri = DTriangle3(lTri.pt(0), lTri.pt(1), lTri.pt(2));
            retRTri = DTriangle3(rTri.pt(0), rTri.pt(1), rTri.pt(2));
        }
    }
    if (bRet) 
    {
        //pRender->nearPoints.push_back(outRet.rPoints[0]);
        //pRender->nearPoints.push_back(outRet.rPoints[1]);

        pRender->lTris.push_back(retLTri);
        pRender->rTris.push_back(retRTri);
    }

    return bRet;
}

ConcaveShape  ConcaveShape::Build(const WDGeometry& geom
    , const DMat4& gTransform)
{
    auto pMesh = geom.mesh();
    if (pMesh == nullptr)
        return ConcaveShape();

    // 获取顶点
    const auto& poss = pMesh->positions();
    DMat4 tMat = gTransform * geom.transform();
    DVec3Vector positions;
    positions.reserve(poss.size());
    for (auto& pos : poss)
    {
        auto rPt = tMat * DVec3(pos);
        positions.push_back(rPt);
    }

    const auto& pris = pMesh->primitiveSets(WD::WDMesh::Solid);
    // 收集三角面的个数
    size_t triCnt = 0;
    for (const auto& pri : pris)
    {
        if (pri.empty())
            continue;
        switch (pri.primitiveType())
        {
        case WDPrimitiveSet::PT_Triangles:
        {
            WDPrimitiveSet::DrawType type = pri.drawType();
            switch (type)
            {
            case WD::WDPrimitiveSet::DrawType::DT_Array:
            {
                auto& priData = pri.drawArrayData();
                triCnt += priData.second / 3;
            }
            break;
            case WD::WDPrimitiveSet::DrawType::DT_ElementByte:
            {
                const WDPrimitiveSet::ElementByteData& indices = pri.drawElementByteData();
                triCnt += indices.size() / 3;
            }
            break;
            case WD::WDPrimitiveSet::DrawType::DT_ElementUShort:
            {
                const WDPrimitiveSet::ElementUShortData& indices = pri.drawElementUShortData();
                triCnt += indices.size() / 3;
            }
            break;
            case WD::WDPrimitiveSet::DrawType::DT_ElementUInt:
            {
                const WDPrimitiveSet::ElementUIntData& indices = pri.drawElementUIntData();
                triCnt += indices.size() / 3;
            }
            break;
            default:
                break;
            }
        }
        break;
        case WDPrimitiveSet::PT_TriangleStrip:
        {
            auto& priData = pri.drawArrayData();
            triCnt += priData.second - 2;
        }
        break;
        case WDPrimitiveSet::PT_TriangleFan:
        {
            auto& priData = pri.drawArrayData();
            triCnt += priData.second - 2;
        }
        break;
        default:
            break;
        }
    }

    if (triCnt == 0)
        return ConcaveShape();

    // 三角面的索引列表
    std::vector<UIVec3> tris;
    tris.reserve(triCnt);
    for (const auto& pri : pris)
    {
        if (pri.empty())
            continue;
        switch (pri.primitiveType())
        {
        case WDPrimitiveSet::PT_Triangles:
        {
            WDPrimitiveSet::DrawType type = pri.drawType();
            switch (type)
            {
            case WD::WDPrimitiveSet::DrawType::DT_Array:
            {
                auto& priData = pri.drawArrayData();
                uint nStart = priData.first;
                uint nEnd = priData.first + priData.second;

                for (size_t i = nStart; i < nEnd; i += 3)
                {
                    tris.push_back(UIVec3(
                        static_cast<uint>(i + 0)
                        , static_cast<uint>(i + 1)
                        , static_cast<uint>(i + 2)));
                }
            }
            break;
            case WD::WDPrimitiveSet::DrawType::DT_ElementByte:
            {
                const WDPrimitiveSet::ElementByteData& indices = pri.drawElementByteData();
                for (size_t i = 0; i < indices.size(); i += 3)
                {
                    tris.push_back(UIVec3(
                        static_cast<uint>(indices[i + 0])
                        , static_cast<uint>(indices[i + 1])
                        , static_cast<uint>(indices[i + 2])));
                }
            }
            break;
            case WD::WDPrimitiveSet::DrawType::DT_ElementUShort:
            {
                const WDPrimitiveSet::ElementUShortData& indices = pri.drawElementUShortData();
                for (size_t i = 0; i < indices.size(); i += 3)
                {
                    tris.push_back(UIVec3(
                        static_cast<uint>(indices[i + 0])
                        , static_cast<uint>(indices[i + 1])
                        , static_cast<uint>(indices[i + 2])));
                }
            }
            break;
            case WD::WDPrimitiveSet::DrawType::DT_ElementUInt:
            {
                const WDPrimitiveSet::ElementUIntData& indices = pri.drawElementUIntData();
                for (size_t i = 0; i < indices.size(); i += 3)
                {
                    tris.push_back(UIVec3(
                        static_cast<uint>(indices[i + 0])
                        , static_cast<uint>(indices[i + 1])
                        , static_cast<uint>(indices[i + 2])));
                }
            }
            break;
            default:
                break;
            }
        }
        break;
        case WDPrimitiveSet::PT_TriangleStrip:
        {
            auto& priData = pri.drawArrayData();
            uint nStart = priData.first;
            uint nCount = priData.first + priData.second;

            uint index0 = nStart + 0;
            uint index1 = nStart + 1;

            for (uint i = nStart + 2; i < nCount; ++i)
            {
                uint index2 = i;

                tris.push_back(UIVec3(index0, index1, index2));

                index0 = index1;
                index1 = index2;
            }
        }
        break;
        case WDPrimitiveSet::PT_TriangleFan:
        {
            auto& priData = pri.drawArrayData();
            uint nStart = priData.first;
            uint nCount = priData.first + priData.second;

            uint index0 = nStart + 0;
            uint index1 = nStart + 1;

            for (uint i = nStart + 2; i < nCount; ++i)
            {
                uint index2 = i;

                tris.push_back(UIVec3(index0, index1, index2));

                index1 = index2;
            }
        }
        break;
        default:
            break;
        }
    }

    if (tris.empty())
        return ConcaveShape();

    return ConcaveShape(std::move(positions), tris);
}

ConcaveShape::Tri::Tri(const TVec3Vector<T>& points, const UIVec3& indices)
    : _points(points)
    , _vIndices(indices)
{
    // 需要保证边缘不能小于等于0
    rebuild();
}

bool ConcaveShape::Tri::Distance(const Tri& left, const Tri& right, Ret& ret, T minClearance)
{
    std::array<T, 3> disL;
    disL[0] = left._plane.distance(right.pt(0));
    disL[1] = left._plane.distance(right.pt(1));
    disL[2] = left._plane.distance(right.pt(2));
    // 距离均比指定的最小间距大，不用后续处理了
    if (disL[0] > minClearance && disL[1] > minClearance && disL[2] > minClearance)
        return false;
    std::array<T, 3> disR;
    disR[0] = right._plane.distance(left.pt(0));
    disR[1] = right._plane.distance(left.pt(1));
    disR[2] = right._plane.distance(left.pt(2));
    // 距离均比指定的最小间距大，不用后续处理了
    if (disR[0] > minClearance && disR[1] > minClearance && disR[2] > minClearance)
        return false;

    // 开始检测是相交的情况还是相离的情况
    // bullet2 中使用裁剪法计算两个三角形是否碰撞(请查找函数: overlap_test_conservative和 find_triangle_collision_clip_method)
    // 这里暂时使用另一中方法(需要大量验证)

    constexpr T eps = NumLimits<T>::Epsilon;
    // 1. 两个三角形所在平面共面(接近共面)的情况
    if (ApproxEqualAll(T(0), eps, disL[0], disL[1], disL[2], disR[0], disR[1], disR[2]))
    {
        // 共面时的嵌入距离认为是0, 所以如果已得到小于0(嵌入)的距离，则直接返回
        if (ret.distance < eps)
            return false;
        // 计算两个共面三角形是否相交, 如果相交拿到交点，如果不相交拿到最短距离
        ret.distance = T(0);
        return true;
    }
    // 2. 两个三角形所在平面不共面，但是平行(接近平行)的情况
    if (ApproxEqualAll(disL[0], eps, disL[1], disL[2], disR[0], disR[1], disR[2]))
    {
        // 如果两个平面的距离大于已经得到结果的最短距离，直接返回
        if (ret.distance <= Abs(disL[0]))
            return false;
        return MinClearance(left, right, ret, minClearance);
    }
    // 3. 不共面也不平行，存在交叉
    TVec3<T> interPt;
    // 判断是否相交了,如果相交，则计算嵌入距离
    if (Intersect(left, right, interPt))
    {
        ret.distance    = 0;
        ret.normal      = left.plane().normal;
        ret.rPoints[0]  = interPt;
        ret.rPoints[1]  = interPt;
        return true;
    }
    // 未相交, 最短距离
    return MinClearance(left, right, ret, minClearance);
}

bool ConcaveShape::Tri::rebuild()
{
    // 重置包围盒
    _aabb = TAabb3<T>::Null();
    // 获取到顶点
    std::array<TVec3<T>, 3> pts = { _points[_vIndices[0]], _points[_vIndices[1]], _points[_vIndices[2]] };
    // 校验是否有效的三角面
    if (!TTriangle3<T>::IsTriangle(pts[0], pts[1], pts[2]))
        return false;
    // 计算三角面所在平面
    _plane.redefine(pts[0], pts[1], pts[2]);
    // 根据三角面所在平面法线，和边线方向，计算一个与边线相关的平面
    for (size_t i = 0; i < pts.size(); ++i)
    {
        size_t nextI = (i + 1) % pts.size();
        auto v = (pts[nextI] - pts[i]).normalize();
        auto nor = TVec3<T>::Cross(v, _plane.normal).normalize();
        _sidePlanes[i].redefine(nor, pts[i]);
        _aabb.expandByPoint(pts[i]);
    }
    // 如果三角面所在平面的法线与X/Y/Z轴平行，则计算的包围盒不是一个体，因此需要在法线方向扩充一下包围盒
    if (TVec3<T>::OnTheSameLine(TVec3<T>::AxisX(), _plane.normal, T(0.000001))
        || TVec3<T>::OnTheSameLine(TVec3<T>::AxisY(), _plane.normal, T(0.000001))
        || TVec3<T>::OnTheSameLine(TVec3<T>::AxisZ(), _plane.normal, T(0.000001)))
    {
        _aabb.expandByPoint(pts[0] + _plane.normal * T(0.000001));
        _aabb.expandByPoint(pts[0] - _plane.normal * T(0.000001));
    }
    return true;
}

bool ConcaveShape::Tri::Intersect(const Tri& left, const Tri& right, TVec3<T>& outPoint)
{
    // 计算这两个平面的交线的方向以及交线上的任意一点，来确定这条交线
    auto lineDir = TVec3<T>::Cross(left.plane().normal, right.plane().normal).normalize();
    auto linePt = left.plane().project(right.pt(0));
    auto line = TLine3<T>(linePt, lineDir);

    // 先用直线与任意一个平面计算交点, 如果交点在三角形内，表明相交
    const auto& lPlanes = left.sidePlanes();
    const auto& rPlanes = right.sidePlanes();

    T dis;
    for (const auto& plane : lPlanes)
    {
        auto ret = LinePlaneIntersect(line, plane);
        // 计算到交点
        if (!ret.first)
            continue;
        outPoint = line.at(ret.second);

        // 现在只需要判断这个点均在两个在三角形内就可以了
        dis = lPlanes[0].distance(outPoint);
        if (dis > NumLimits<T>::Epsilon)
            continue;

        dis = lPlanes[1].distance(outPoint);
        if (dis > NumLimits<T>::Epsilon)
            continue;

        dis = lPlanes[2].distance(outPoint);
        if (dis > NumLimits<T>::Epsilon)
            continue;

        dis = rPlanes[0].distance(outPoint);
        if (dis > NumLimits<T>::Epsilon)
            continue;

        dis = rPlanes[1].distance(outPoint);
        if (dis > NumLimits<T>::Epsilon)
            continue;

        dis = rPlanes[2].distance(outPoint);
        if (dis > NumLimits<T>::Epsilon)
            continue;

        return true;
    }

    for (const auto& plane : rPlanes)
    {
        auto ret = LinePlaneIntersect(line, plane);
        // 计算到交点
        if (!ret.first)
            continue;
        outPoint = line.at(ret.second);

        // 现在只需要判断这个点均在两个在三角形内就可以了
        dis = lPlanes[0].distance(outPoint);
        if (dis > NumLimits<T>::Epsilon)
            continue;

        dis = lPlanes[1].distance(outPoint);
        if (dis > NumLimits<T>::Epsilon)
            continue;

        dis = lPlanes[2].distance(outPoint);
        if (dis > NumLimits<T>::Epsilon)
            continue;

        dis = rPlanes[0].distance(outPoint);
        if (dis > NumLimits<T>::Epsilon)
            continue;

        dis = rPlanes[1].distance(outPoint);
        if (dis > NumLimits<T>::Epsilon)
            continue;

        dis = rPlanes[2].distance(outPoint);
        if (dis > NumLimits<T>::Epsilon)
            continue;

        return true;
    }
    return false;

}

bool ConcaveShape::Tri::MinClearance(const Tri& left, const Tri& right, Ret& ret, T minClearance)
{
    // 计算两点的最小距离是否满足设置给ret
    auto retFunc = [minClearance, &ret](const TVec3<T>& lPt, const TVec3<T>& rPt)->bool
        {
            auto dis = TVec3<T>::Distance(lPt, rPt);
            if (dis > minClearance)
                return false;
            if (dis >= ret.distance)
                return false;
            ret.distance = dis;
            ret.normal = (rPt - lPt).normalize();
            ret.rPoints[0] = lPt;
            ret.rPoints[1] = rPt;
            return true;
        };
    // 计算最小距离
    bool bRet = false;
    for (size_t i = 0; i < 3; ++i)
    {
        // 使用 rTri 的顶点计算到 lTri的最短距离
        {
            const auto& rPt = right.pt(i);
            auto lPt = TTriangle3<T>::ClosestPointToPoint(rPt, left.pt(0), left.pt(1), left.pt(2));
            if (retFunc(lPt, rPt))
                bRet = true;
        }
        // 使用 lTri 的顶点计算到 rTri的最短距离
        {
            const auto& lPt = left.pt(i);
            auto rPt = TTriangle3<T>::ClosestPointToPoint(lPt, right.pt(0), right.pt(1), right.pt(2));
            if (retFunc(lPt, rPt))
                bRet = true;
        }
    }

    return bRet;
}

WD_NAMESPACE_END
