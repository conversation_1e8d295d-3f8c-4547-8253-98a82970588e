#pragma once

#include "GJK.h"

WD_NAMESPACE_BEGIN

/**
 * @brief 扩展多边形算法 Epanding Polytop Algorithm
 *  用来计算两个多边形碰撞的穿透深度和方向, 可用于将两个发生碰撞的多边形分离
*/
template <typename TSimplex>
class EPA
{
public:
    using T = double;
public:
    /**
     * @brief 计算的结果状态
    */
    enum RStatus
    {
        // 计算失败，达到最大迭代次数, 可能是数据不规范
        RS_Failed = 0,
        // 计算成功
        RS_Valid,
    };
public:
    EPA(T minDistance = T(0.00001), int maxIterations = 255)
        : _minDistance(minDistance)
        , _maxIterations(maxIterations)
    {
    }
    ~EPA()
    {

    }
public:
    /**
     * @brief 执行算法
     * @param gjk 已经测试出相交的gjk算法对象
     * @return 执行结果
    */
    template <typename TShapeA, typename TShapeB>
    RStatus evaluate(GJK<TSimplex>& gjk
        , const TShapeA& shapeA
        , const TShapeB& shapeB
        , std::pair<TVec3<T>, TVec3<T>>& outSide
        , T& outT
        , TVec3<T>& outDir
        , const TVec3<T>& guess = TVec3<T>::AxisX())
    {
        auto& simplex   = gjk.simplex();
        RStatus rStatus = RStatus::RS_Valid;
        // 迭代计数器
        int itrCount = 0;
        // 获取单纯形的维度
        constexpr int dime = TSimplex::Dimensionality();
        switch (dime)
        {
        case 2:
            {
                // 多变形的边
                using Side = std::pair<TVec3<T>, TVec3<T>>;
                // 多边形
                using Polygon = std::array<Side, 128>;
                // 多边形以及其边的个数
                Polygon polygon;
                size_t sideCount = 0;
                // 如果单纯形有两个顶点则需要向垂直于这个向量的两个方向扩充
                if (simplex.count() == 2)
                {
                    auto v = (simplex.point(1) - simplex.point(0)).normalize();
                    if (!TVec3<T>::OnTheSameLine(v, TVec3<T>::AxisZ()))
                        v = TVec3<T>::Cross(TVec3<T>::AxisZ(), v).normalize();
                    else
                        v = TVec3<T>::Cross(v, TVec3<T>::AxisY()).normalize();

                    auto p0     = GJK<TSimplex>::MinkowskiDiffPoint(shapeA, shapeB, v);
                    bool bP0    = false;
                    if (TVec3<T>::Dot(p0 - simplex.point(0), v) > NumLimits<T>::Epsilon)
                    {
                        polygon[sideCount].first    = simplex.point(0);
                        polygon[sideCount].second   = p0;
                        sideCount++;

                        polygon[sideCount].first    = p0;
                        polygon[sideCount].second   = simplex.point(1);
                        sideCount++;
                        bP0                         = true;
                    }
                    auto p1 = GJK<TSimplex>::MinkowskiDiffPoint(shapeA, shapeB, -v);
                    if (TVec3<T>::Dot(p1 - simplex.point(0), -v) > NumLimits<T>::Epsilon) 
                    {
                        polygon[sideCount].first    = simplex.point(1);
                        polygon[sideCount].second   = p1;
                        sideCount++;

                        polygon[sideCount].first    = p1;
                        polygon[sideCount].second   = simplex.point(0);
                        sideCount++;
                    }
                    if (sideCount == 0) 
                    {
                        outSide.first = simplex.point(0);
                        outSide.second = simplex.point(1);
                        outT = simplex.weight(0);
                        outDir = guess;
                        // 当前边线就是最近点
                        return rStatus;
                    }
                    else if (sideCount == 2) 
                    {
                        if (bP0)
                        {
                            polygon[sideCount].first = simplex.point(1);
                            polygon[sideCount].second = simplex.point(0);
                        }
                        else
                        {
                            polygon[sideCount].first = simplex.point(0);
                            polygon[sideCount].second = simplex.point(1);
                        }
                        sideCount++;
                    }
                }
                else
                {
                    sideCount = 3;
                    polygon[0].first    = simplex.point(0);
                    polygon[0].second   = simplex.point(1);
                    polygon[1].first    = simplex.point(1);
                    polygon[1].second   = simplex.point(2);
                    polygon[2].first    = simplex.point(2);
                    polygon[2].second   = simplex.point(0);
                }
                // 最近距离
                T minDisSq = NumLimits<T>::Max;
                // 最近边的索引列表，可能会有多条边都是最近边，需要同时扩展
                std::array<size_t, 128> nearSideIndices;
                // 最近边的查询向量列表
                std::array<TVec3<T>, 128> dirs;
                // 最近边的个数
                size_t nearSideCount = 0;
                // 开始EPA算法迭代
                do
                {
                    // 找到最近的所有边
                    nearSideCount = 0;
                    for (size_t i = 0; i < sideCount; ++i)
                    {
                        const auto& side = polygon[i];
                        auto dir = TSegment3<T>::ClosestPointToPoint(TVec3<T>::Zero(), side.first, side.second, true);
                        auto disSq = dir.lengthSq();
                        auto d = disSq - minDisSq;
                        if (d < T(0))
                        {
                            // 出现了更近的边，清除之前的所有最近边
                            nearSideIndices[0]  = i;
                            dirs[0]             = dir;
                            nearSideCount       = 1;
                            minDisSq            = disSq;
                        }
                        else if (Abs(d) <= _minDistance)
                        {
                            // 说明两条边到原件的距离相等，需要同时扩展这两条边
                            nearSideIndices[nearSideCount]  = i;
                            dirs[nearSideCount]             = dir;
                            nearSideCount++;
                        }
                    }
                    // 扩展所有的最近边
                    bool bExpand = false;
                    for (size_t i = 0; i < nearSideCount; ++i) 
                    {
                        const auto& sideIdx     = nearSideIndices[i];
                        const auto& nearSide    = polygon[sideIdx];
                        const auto& dir         = dirs[i];
                        auto pt = GJK<TSimplex>::MinkowskiDiffPoint(shapeA, shapeB, dir);
                        if (TVec3<T>::Dot(pt - nearSide.first, dir) > NumLimits<T>::Epsilon)
                        {
                            // 成功扩展，先用新边A把之前的边替换掉
                            polygon[sideIdx].first      = nearSide.first;
                            polygon[sideIdx].second     = pt;
                            // 在把新边B加到最后(这里并不关心顺序，所以不用插入)
                            polygon[sideCount].first    = pt;
                            polygon[sideCount].second   = nearSide.second;
                            sideCount++;
                            bExpand = true;
                        }
                    }
                    // 所有边均未扩展, 说明已经找到的最近边
                    if (!bExpand) 
                    {
                        if (nearSideCount == 0) 
                        {
                            assert(false);
                            rStatus = RStatus::RS_Failed;
                            break;
                        }
                        outSide = polygon[nearSideIndices[0]];
                        outDir = dirs[0];
                        outT = TSegment3<T>::ClosestPointToPointParameter(TVec3<T>::Zero(), outSide.first, outSide.second, true);
                        rStatus = RStatus::RS_Valid;
                        break;
                    }
                    // 迭代次数
                    itrCount++;
                    // 是否超过最大迭代次数, 如果超过最大迭代次数，说明可能是数据不规范, 返回失败
                    if (itrCount >= _maxIterations)
                    {
                        rStatus = RStatus::RS_Failed;
                        break;
                    }
                } while (true);
            }
            break;
        case 3:
            {
                // 如果单纯形有两个顶点或三个顶点并且包含原点，证明两个形状刚好接触
                if (simplex.count() == 2 || simplex.count() == 3)
                {
                    // 包含原点，证明刚好接触
                    if (simplex.contains(TVec3<T>::Zero(), _minDistance))
                        return RStatus::RS_Valid;
                    // 否则视为错误
                    else
                        return RStatus::RS_Failed;
                }
                // 开始EPA算法迭代
                do
                {
                    // 迭代次数
                    itrCount++;
                    // 是否超过最大迭代次数, 如果超过最大迭代次数，说明可能是数据不规范, 返回失败
                    if (itrCount >= _maxIterations)
                    {
                        rStatus = RStatus::RS_Failed;
                        break;
                    }
                } while (true);
            }
            break;
        default:
            {
                assert(false && "不支持的维度!");
                return RStatus::RS_Failed;
            }
            break;
        };

        return rStatus;
    }
private:
    // 最小距离值,控制算法精度
    T _minDistance;
    // EPA算法的最大迭代次数, 超过最大迭代次数后，仍然未找到结果，将返回计算失败
    int _maxIterations;
};

WD_NAMESPACE_END