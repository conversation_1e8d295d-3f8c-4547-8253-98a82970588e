#pragma once

#include "core/WDCore.h"
#include "core/geometry/WDGeometryMgr.h"

#include "BulletCollision/NarrowPhaseCollision/btGjkEpa2.h"
#include "BulletCollision/CollisionShapes/btConvexHullShape.h"
#include "BulletCollision/CollisionShapes/btBoxShape.h"
#include "BulletCollision/CollisionShapes/btCylinderShape.h"
#include "BulletCollision/CollisionShapes/btSphereShape.h"

#include "BulletCollision/CollisionShapes/btTriangleMesh.h"
#include "BulletCollision/CollisionDispatch/btDefaultCollisionConfiguration.h"
#include "BulletCollision/Gimpact/btGImpactShape.h"
#include "BulletCollision/Gimpact/btGImpactCollisionAlgorithm.h"

//#define COLLISION_ASSERT_ENABLED

#ifdef COLLISION_ASSERT_ENABLED
#define COLLISION_ASSERT(expression) assert(expression)
#else
#define COLLISION_ASSERT(expression) ((void)0)
#endif

WD_NAMESPACE_BEGIN

/**
 * @brief WD Vec3 转换到 bullet Vec3
*/
template <class T = btScalar>
static inline btVector3 Vec3ToBt(const TVec3<T>& v)
{
    return btVector3(v.x, v.y, v.z);
}
/**
 * @brief bullet Vec3 转换到 WD Vec3
*/
template <class T = btScalar>
static inline TVec3<T> Vec3FromBt(const btVector3& v)
{
    return TVec3<T>(v.getX(), v.getY(), v.getZ());
}
/**
 * @brief WD Quat 转换到 bullet Quat
*/
template <class T = btScalar>
static inline TQuat<T> QuatToBt(const TQuat<T>& q)
{
    return btQuaternion(q.x(), q.y(), q.z(), q.w());
}
/**
 * @brief bullet Quat 转换到 WD Quat
*/
template <class T = btScalar>
static inline btQuaternion QuatFromBt(const btQuaternion& q)
{
    return TQuat<T>(q.getW(), q.getX(), q.getY(), q.getZ());
}



/**
 * @brief 形状构建器
*/
class ShapeBuilder
{
public:
    using ConvexShapes = std::vector<btConvexHullShape*>;
    class ConvexShapesWrapper
    {
    private:
        ConvexShapes _shapes;
    public:
        ConvexShapesWrapper(btConvexHullShape* pShape = nullptr)
        {
            if (pShape != nullptr)
            {
                _shapes = { pShape };
            }
        }
        ConvexShapesWrapper(ConvexShapes&& shapes)
        {
            _shapes = std::forward<std::vector<btConvexHullShape*> >(shapes);
        }
        ConvexShapesWrapper(const ConvexShapesWrapper& right) = delete;
        ConvexShapesWrapper(ConvexShapesWrapper&& right)
        {
            _shapes = std::forward<ConvexShapes>(right._shapes);
        }
        ConvexShapesWrapper& operator=(const ConvexShapesWrapper& right) = delete;
        ConvexShapesWrapper& operator=(ConvexShapesWrapper&& right)
        {
            if (this == &right)
                return *this;
            _shapes = std::forward<ConvexShapes>(right._shapes);
            return *this;
        }
        ~ConvexShapesWrapper()
        {
            for (auto pShape : _shapes)
            {
                if (pShape != nullptr)
                    delete pShape;
            }
            _shapes.clear();
        }
    public:
        inline const ConvexShapes& shapes() const
        {
            return _shapes;
        }
    };

    class GImpactMeshShapeWrapper 
    {
    private:
        btTriangleMesh* _pTriMesh;
        btGImpactMeshShape* _pShape;
    public:
        GImpactMeshShapeWrapper(btTriangleMesh* pTriMesh = nullptr)
            : _pTriMesh(pTriMesh)
        {
            if (_pTriMesh != nullptr)
            {
                _pShape = new btGImpactMeshShape(_pTriMesh);
                _pShape->updateBound();
            }
        }
        GImpactMeshShapeWrapper(const GImpactMeshShapeWrapper& right) = delete;
        GImpactMeshShapeWrapper(GImpactMeshShapeWrapper&& right)
            : _pTriMesh(nullptr)
            , _pShape(nullptr)
        {
            std::swap(_pTriMesh, right._pTriMesh);
            std::swap(_pShape, right._pShape);
        }
        GImpactMeshShapeWrapper& operator=(const GImpactMeshShapeWrapper& right) = delete;
        GImpactMeshShapeWrapper& operator=(GImpactMeshShapeWrapper&& right)
        {
            if (this == &right)
                return *this;
            std::swap(_pTriMesh, right._pTriMesh);
            std::swap(_pShape, right._pShape);
            return *this;
        }
        ~GImpactMeshShapeWrapper()
        {
            if (_pShape != nullptr)
            {
                delete _pShape;
                _pShape = nullptr;
            }
            if (_pTriMesh != nullptr)
            {
                delete _pTriMesh;
                _pTriMesh = nullptr;
            }
        }
    public:
        inline btGImpactMeshShape* shape() const
        {
            return _pShape;
        }
    };
public:
    /**
     * @brief 使用标准基本体创建凸碰撞形状
     *  部分基本体是凹的(例如旋转体)，但是可以使用参数化信息进行凸分解，来快速分解成多个凸形状
    */
    static ConvexShapesWrapper BuildConvexHullShapes(const WDGeometryStdPris& stdPrisGeom
        , const DMat4& gTransform);
    /**
     * @brief 校验几何体是否是凸多面体
     *  根据凸多面体的定义来判断: 如果多面体在他们每一面决定的平面的同一侧，则称此多面体为凸多面体
     * @return 如果为凸多面体或者顶点或者三角面个数为0，返回true,否则返回false
    */
    static bool IsConvexGeometry(const WDGeometry& geom);
    /**
     * @brief 指定一个凸多面体，来创建凸碰撞形状
     * @param convexGeom 凸多面体
    */
    static ConvexShapesWrapper BuildConvexHullShapes(const WDGeometry& convexGeom
        , const DMat4& gTransform);
    /**
     * @brief 指定一个多面体, 使用多面体的每个三角面来创建碰撞体, 用于凹多面体与凸多面体的碰撞检测计算
     * @param geom 几何体(多面体), 这里一般给的是凹多面体
     * @param gTransform 几何体变换矩阵
     * @return 创建的凸碰撞体列表
    */
    static ConvexShapesWrapper BuildTriangleShapes(const WDGeometry& geom
        , const DMat4& gTransform);
    /**
     * @brief 指定凹多面体创建GImpact Shape, 配合btGImpactCollisionAlgorithm算法,实现两个凹多面体之间的碰撞
     * @param geom 几何体(多面体), 这里一般给的是凹多面体
     * @param gTransform 几何体变换矩阵
     * @return 创建的GImpact碰撞体
    */
    static GImpactMeshShapeWrapper BuildGImpactShape(const WDGeometry& geom
        , const DMat4& gTransform);

    /**
     * @brief 两个凹多面体之间的碰撞检测
     * @param geomA 
     * @param gTransformA 
     * @param geomB 
     * @param gTransformB 
    */
    static void CTest(const WDGeometry& geomA
        , const DMat4& gTransformA
        , const WDGeometry& geomB
        , const DMat4& gTransformB);
private:
    /**
     * @brief 指定顶点列表，创建凸碰撞形状
     * @param points 顶点列表
     * @param transform 顶点的变换矩阵
     * @return 碰撞形状
    */
    static btConvexHullShape* CreateConvexHullShape(const FVec3Vector& points, const DMat4& transform);
    /**
     * @brief 指定顶点列表，创建凸碰撞形状
     * @param points 顶点列表
     * @param transform 顶点的变换矩阵
     * @return 碰撞形状
    */
    static btConvexHullShape* CreateConvexHullShape(const FVec3Vector& points0
        , const FVec3Vector& points1
        , const DMat4& transform);

    /**
     * @brief 使用标准基本体 圆环体 构建 凸碰撞体列表
     *  思路是: 采用分段分割法，将圆环分割为多个凸包(应该是斜圆柱)
    */
    static ConvexShapes CreateConvexHullShapesByCTOR(const WDGeometryCircularTorus& geom
        , const DMat4& transform
        , MeshLODSelection lod = MeshLODSelection());
    /**
     * @brief 使用标准基本体 方环体 构建 凸碰撞体列表
     *  思路是: 采用分段分割法，将方环分割为多个凸包(应该是斜棱台体)
    */
    static ConvexShapes CreateConvexHullShapesByRTOR(const WDGeometryRectangularTorus& geom
        , const DMat4& transform
        , MeshLODSelection lod = MeshLODSelection());
    /**
     * @brief 使用标准基本体 拉伸体 构建  凸碰撞体列表
     *  思路是：如果底面环为凸多边形，直接使用顶点创建凸包
     *      如果底面环为凹多边形，则先对底面环进行凸分解，再用分解出的多个凸环创建多个拉伸体的凸包
    */
    static ConvexShapes CreateConvexHullShapesByEXTR(const WDGeometryExtrusion& geom
        , const DMat4& transform
        , const MeshLODSelection& lod = MeshLODSelection());
    /**
     * @brief 使用标准基本体 旋转体 构建  凸碰撞体列表
     *  思路是：如果底面环为凸多边形,直接进行旋转分段近似凸包
     *      如果底面环为凹多边形,先对底面环进行凸分解，再用分解出的多个凸环进行旋转分段近似凸包
    */
    static ConvexShapes CreateConvexHullShapesByREVO(const WDGeometryRevolution& geom
        , const DMat4& transform
        , MeshLODSelection lod = MeshLODSelection());
    /**
     * @brief 使用标准基本提 放样体 构建  凸碰撞体列表
     *  思路是: 如果放样体的环是凹多边形，则先进行凸分解，再将放样曲线分段，从而分解成多个凸多面体
    */
    static ConvexShapes CreateConvexHullShapesByLoft(const WDGeometryLofting& geom
        , const DMat4& transform
        , MeshLODSelection lod = MeshLODSelection());
};



WD_NAMESPACE_END