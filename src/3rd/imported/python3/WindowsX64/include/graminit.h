/* Generated by Parser/pgen */

#define single_input 256
#define file_input 257
#define eval_input 258
#define decorator 259
#define decorators 260
#define decorated 261
#define async_funcdef 262
#define funcdef 263
#define parameters 264
#define typedargslist 265
#define tfpdef 266
#define varargslist 267
#define vfpdef 268
#define stmt 269
#define simple_stmt 270
#define small_stmt 271
#define expr_stmt 272
#define annassign 273
#define testlist_star_expr 274
#define augassign 275
#define del_stmt 276
#define pass_stmt 277
#define flow_stmt 278
#define break_stmt 279
#define continue_stmt 280
#define return_stmt 281
#define yield_stmt 282
#define raise_stmt 283
#define import_stmt 284
#define import_name 285
#define import_from 286
#define import_as_name 287
#define dotted_as_name 288
#define import_as_names 289
#define dotted_as_names 290
#define dotted_name 291
#define global_stmt 292
#define nonlocal_stmt 293
#define assert_stmt 294
#define compound_stmt 295
#define async_stmt 296
#define if_stmt 297
#define while_stmt 298
#define for_stmt 299
#define try_stmt 300
#define with_stmt 301
#define with_item 302
#define except_clause 303
#define suite 304
#define namedexpr_test 305
#define test 306
#define test_nocond 307
#define lambdef 308
#define lambdef_nocond 309
#define or_test 310
#define and_test 311
#define not_test 312
#define comparison 313
#define comp_op 314
#define star_expr 315
#define expr 316
#define xor_expr 317
#define and_expr 318
#define shift_expr 319
#define arith_expr 320
#define term 321
#define factor 322
#define power 323
#define atom_expr 324
#define atom 325
#define testlist_comp 326
#define trailer 327
#define subscriptlist 328
#define subscript 329
#define sliceop 330
#define exprlist 331
#define testlist 332
#define dictorsetmaker 333
#define classdef 334
#define arglist 335
#define argument 336
#define comp_iter 337
#define sync_comp_for 338
#define comp_for 339
#define comp_if 340
#define encoding_decl 341
#define yield_expr 342
#define yield_arg 343
#define func_body_suite 344
#define func_type_input 345
#define func_type 346
#define typelist 347
