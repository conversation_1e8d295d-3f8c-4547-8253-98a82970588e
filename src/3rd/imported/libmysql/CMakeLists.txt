if(WIN32)
	set(MYSQL_LIB_DIR "${CMAKE_CURRENT_SOURCE_DIR}/WindowsX64/lib")
	set(MYSQL_LIB_NAME libmysql.lib)
	set(MYSQL_DLL_NAME libmysql.dll)
	set(MYSQL_HEADER  "${CMAKE_CURRENT_SOURCE_DIR}/WindowsX64/include")
elseif(CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "aarch64")
	set(MYSQL_LIB_DIR "${CMAKE_CURRENT_SOURCE_DIR}/LinuxARM64/lib")
	set(MYSQL_LIB_NAME libmysqlclient.a)
	set(MYSQL_DLL_NAME libmysqlclient.so.21.2.31)
	set(MYSQL_HEADER  "${CMAKE_CURRENT_SOURCE_DIR}/LinuxARM64/include")
else()
	set(MYSQL_LIB_DIR "${CMAKE_CURRENT_SOURCE_DIR}/Linux64/lib")
	set(MYSQL_LIB_NAME libmysqlclient.so.21.2.35)
	set(MYSQL_DLL_NAME libmysqlclient.so.21.2.35)
	set(MYSQL_HEADER  "${CMAKE_CURRENT_SOURCE_DIR}/Linux64/include")
endif()

add_library(libmysql SHARED IMPORTED GLOBAL)
set_target_properties(libmysql PROPERTIES 
	IMPORTED_LOCATION ${MYSQL_LIB_DIR}/${MYSQL_DLL_NAME}
	IMPORTED_IMPLIB ${MYSQL_LIB_DIR}/${MYSQL_LIB_NAME}
	INTERFACE_INCLUDE_DIRECTORIES ${MYSQL_HEADER}
)

if(WIN32)
	add_custom_target(CopySSLRuntimeFiles
		VERBATIM
		COMMAND ${CMAKE_COMMAND} -E copy_if_different ${MYSQL_LIB_DIR}/libcrypto-1_1-x64.dll "${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/libcrypto-1_1-x64.dll"
		COMMAND ${CMAKE_COMMAND} -E copy_if_different ${MYSQL_LIB_DIR}/libssl-1_1-x64.dll "${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/libssl-1_1-x64.dll"
	)
	set_target_properties(CopySSLRuntimeFiles PROPERTIES FOLDER "copy_taskes")

	add_dependencies(libmysql CopySSLRuntimeFiles)
endif()
