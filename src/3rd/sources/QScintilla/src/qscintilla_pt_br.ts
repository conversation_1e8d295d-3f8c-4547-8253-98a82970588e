<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1">
<context>
    <name>QsciCommand</name>
    <message>
        <location filename="qscicommandset.cpp" line="44"/>
        <source>Move down one line</source>
        <translation>Mover uma linha para baixo</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="54"/>
        <source>Extend selection down one line</source>
        <translation>Extender a seleção uma linha para baixo</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="71"/>
        <source>Scroll view down one line</source>
        <translation>Descer a visão uma linha para baixo</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="64"/>
        <source>Extend rectangular selection down one line</source>
        <translation>Extender a seleção retangular uma linha para baixo</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="81"/>
        <source>Move up one line</source>
        <translation>Mover uma linha para cima</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="91"/>
        <source>Extend selection up one line</source>
        <translation>Extender a seleção uma linha para cima</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="108"/>
        <source>Scroll view up one line</source>
        <translation>Subir a visão uma linha para cima</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="101"/>
        <source>Extend rectangular selection up one line</source>
        <translation>Extender a seleção retangular uma linha para cima</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="158"/>
        <source>Move up one paragraph</source>
        <translation>Mover um paragrafo para cima</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="164"/>
        <source>Extend selection up one paragraph</source>
        <translation>Extender a seleção um paragrafo para cima</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="145"/>
        <source>Move down one paragraph</source>
        <translation>Mover um paragrafo para baixo</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="118"/>
        <source>Scroll to start of document</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="128"/>
        <source>Scroll to end of document</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="138"/>
        <source>Scroll vertically to centre current line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="151"/>
        <source>Extend selection down one paragraph</source>
        <translation>Extender a seleção  um paragrafo para baixo</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="175"/>
        <source>Move left one character</source>
        <translation>Mover um caractere para a esquerda</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="185"/>
        <source>Extend selection left one character</source>
        <translation>Extender a seleção um caractere para esquerda</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="239"/>
        <source>Move left one word</source>
        <translation>Mover uma palavra para esquerda</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="249"/>
        <source>Extend selection left one word</source>
        <translation>Extender a seleção uma palavra para esquerda</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="196"/>
        <source>Extend rectangular selection left one character</source>
        <translation>Extender a seleção retangular um caractere para esquerda</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="207"/>
        <source>Move right one character</source>
        <translation>Mover um caractere para direita</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="217"/>
        <source>Extend selection right one character</source>
        <translation>Extender a seleção um caractere para direita</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="259"/>
        <source>Move right one word</source>
        <translation>Mover uma palavra para direita</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="265"/>
        <source>Extend selection right one word</source>
        <translation>Extender a seleção uma palavra para direita</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="228"/>
        <source>Extend rectangular selection right one character</source>
        <translation>Extender a seleção retangular um caractere para direita</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="271"/>
        <source>Move to end of previous word</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="277"/>
        <source>Extend selection to end of previous word</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="288"/>
        <source>Move to end of next word</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="298"/>
        <source>Extend selection to end of next word</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="305"/>
        <source>Move left one word part</source>
        <translation>Mover uma parte da palavra para esquerda</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="311"/>
        <source>Extend selection left one word part</source>
        <translation>Extender a seleção uma parte de palavra para esquerda</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="318"/>
        <source>Move right one word part</source>
        <translation>Mover uma parte da palavra para direita</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="324"/>
        <source>Extend selection right one word part</source>
        <translation>Extender a seleção uma parte de palavra para direita</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="554"/>
        <source>Move up one page</source>
        <translation>Mover uma página para cima</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="560"/>
        <source>Extend selection up one page</source>
        <translation>Extender a seleção uma página para cima</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="566"/>
        <source>Extend rectangular selection up one page</source>
        <translation>Extender a seleção retangular uma página para cima</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="577"/>
        <source>Move down one page</source>
        <translation>Mover uma página para baixo</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="587"/>
        <source>Extend selection down one page</source>
        <translation>Extender a seleção uma página para baixo</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="597"/>
        <source>Extend rectangular selection down one page</source>
        <translation>Extender a seleção retangular uma página para baixo</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="634"/>
        <source>Delete current character</source>
        <translation>Excluir caractere atual</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="764"/>
        <source>Cut selection</source>
        <translation>Recortar seleção</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="663"/>
        <source>Delete word to right</source>
        <translation>Excluir palavra para direita</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="335"/>
        <source>Move to start of document line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="345"/>
        <source>Extend selection to start of document line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="356"/>
        <source>Extend rectangular selection to start of document line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="367"/>
        <source>Move to start of display line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="377"/>
        <source>Extend selection to start of display line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="384"/>
        <source>Move to start of display or document line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="391"/>
        <source>Extend selection to start of display or document line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="402"/>
        <source>Move to first visible character in document line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="413"/>
        <source>Extend selection to first visible character in document line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="424"/>
        <source>Extend rectangular selection to first visible character in document line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="431"/>
        <source>Move to first visible character of display in document line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="438"/>
        <source>Extend selection to first visible character in display or document line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="449"/>
        <source>Move to end of document line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="459"/>
        <source>Extend selection to end of document line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="470"/>
        <source>Extend rectangular selection to end of document line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="481"/>
        <source>Move to end of display line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="491"/>
        <source>Extend selection to end of display line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="498"/>
        <source>Move to end of display or document line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="505"/>
        <source>Extend selection to end of display or document line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="516"/>
        <source>Move to start of document</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="526"/>
        <source>Extend selection to start of document</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="537"/>
        <source>Move to end of document</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="547"/>
        <source>Extend selection to end of document</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="604"/>
        <source>Stuttered move up one page</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="610"/>
        <source>Stuttered extend selection up one page</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="617"/>
        <source>Stuttered move down one page</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="623"/>
        <source>Stuttered extend selection down one page</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="650"/>
        <source>Delete previous character if not at start of line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="673"/>
        <source>Delete right to end of next word</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="690"/>
        <source>Delete line to right</source>
        <translation>Excluir linha para direita</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="714"/>
        <source>Transpose current and previous lines</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="721"/>
        <source>Duplicate the current line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="727"/>
        <source>Select all</source>
        <oldsource>Select document</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="733"/>
        <source>Move selected lines up one line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="739"/>
        <source>Move selected lines down one line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="782"/>
        <source>Toggle insert/overtype</source>
        <translation>Alternar entre modo de inserir/sobreescrever</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="776"/>
        <source>Paste</source>
        <translation>Copiar</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="770"/>
        <source>Copy selection</source>
        <translation>Copiar seleção</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="788"/>
        <source>Insert newline</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="806"/>
        <source>De-indent one level</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="812"/>
        <source>Cancel</source>
        <translation>Cancelar</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="644"/>
        <source>Delete previous character</source>
        <translation>Excluir caractere anterior</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="657"/>
        <source>Delete word to left</source>
        <translation>Excluir palavra a esquerda</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="680"/>
        <source>Delete line to left</source>
        <translation>Excluir linha a esquerda</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="818"/>
        <source>Undo last command</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="828"/>
        <source>Redo last command</source>
        <translation>Refazer último comando</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="800"/>
        <source>Indent one level</source>
        <translation>Indentar um nível</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="834"/>
        <source>Zoom in</source>
        <translation>Aumentar zoom</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="840"/>
        <source>Zoom out</source>
        <translation>Diminuir zoom</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="794"/>
        <source>Formfeed</source>
        <translation>Alimentação da Página</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="702"/>
        <source>Cut current line</source>
        <translation>Configurar linha atual</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="696"/>
        <source>Delete current line</source>
        <translation>Excluir linha atual</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="708"/>
        <source>Copy current line</source>
        <translation>Copiar linha atual</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="752"/>
        <source>Convert selection to lower case</source>
        <translation>Converter a seleção para minúscula</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="758"/>
        <source>Convert selection to upper case</source>
        <translation>Converter a seleção para maiúscula</translation>
    </message>
    <message>
        <location filename="qscicommandset.cpp" line="746"/>
        <source>Duplicate selection</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>QsciLexerAVS</name>
    <message>
        <location filename="qscilexeravs.cpp" line="275"/>
        <source>Default</source>
        <translation type="unfinished">Padrão</translation>
    </message>
    <message>
        <location filename="qscilexeravs.cpp" line="278"/>
        <source>Block comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexeravs.cpp" line="281"/>
        <source>Nested block comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexeravs.cpp" line="284"/>
        <source>Line comment</source>
        <translation type="unfinished">Comentar Linha</translation>
    </message>
    <message>
        <location filename="qscilexeravs.cpp" line="287"/>
        <source>Number</source>
        <translation type="unfinished">Número</translation>
    </message>
    <message>
        <location filename="qscilexeravs.cpp" line="290"/>
        <source>Operator</source>
        <translation type="unfinished">Operador</translation>
    </message>
    <message>
        <location filename="qscilexeravs.cpp" line="293"/>
        <source>Identifier</source>
        <translation type="unfinished">Identificador</translation>
    </message>
    <message>
        <location filename="qscilexeravs.cpp" line="296"/>
        <source>Double-quoted string</source>
        <translation type="unfinished">Cadeia de caracteres envolvida por aspas duplas</translation>
    </message>
    <message>
        <location filename="qscilexeravs.cpp" line="299"/>
        <source>Triple double-quoted string</source>
        <translation type="unfinished">Cadeia de caracteres envolvida por três aspas duplas</translation>
    </message>
    <message>
        <location filename="qscilexeravs.cpp" line="302"/>
        <source>Keyword</source>
        <translation type="unfinished">Palavra Chave</translation>
    </message>
    <message>
        <location filename="qscilexeravs.cpp" line="305"/>
        <source>Filter</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexeravs.cpp" line="308"/>
        <source>Plugin</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexeravs.cpp" line="311"/>
        <source>Function</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexeravs.cpp" line="314"/>
        <source>Clip property</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexeravs.cpp" line="317"/>
        <source>User defined</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>QsciLexerBash</name>
    <message>
        <location filename="qscilexerbash.cpp" line="188"/>
        <source>Default</source>
        <translation>Padrão</translation>
    </message>
    <message>
        <location filename="qscilexerbash.cpp" line="191"/>
        <source>Error</source>
        <translation>Número</translation>
    </message>
    <message>
        <location filename="qscilexerbash.cpp" line="194"/>
        <source>Comment</source>
        <translation>Comentário</translation>
    </message>
    <message>
        <location filename="qscilexerbash.cpp" line="197"/>
        <source>Number</source>
        <translation>Número</translation>
    </message>
    <message>
        <location filename="qscilexerbash.cpp" line="200"/>
        <source>Keyword</source>
        <translation>Palavra Chave</translation>
    </message>
    <message>
        <location filename="qscilexerbash.cpp" line="203"/>
        <source>Double-quoted string</source>
        <translation>Cadeia de caracteres envolvida por aspas duplas</translation>
    </message>
    <message>
        <location filename="qscilexerbash.cpp" line="206"/>
        <source>Single-quoted string</source>
        <translation>Cadeia de caracteres envolvida por aspas simples</translation>
    </message>
    <message>
        <location filename="qscilexerbash.cpp" line="209"/>
        <source>Operator</source>
        <translation>Operador</translation>
    </message>
    <message>
        <location filename="qscilexerbash.cpp" line="212"/>
        <source>Identifier</source>
        <translation>Identificador</translation>
    </message>
    <message>
        <location filename="qscilexerbash.cpp" line="215"/>
        <source>Scalar</source>
        <translation>Escalar</translation>
    </message>
    <message>
        <location filename="qscilexerbash.cpp" line="218"/>
        <source>Parameter expansion</source>
        <translation>Parâmetro de Expansão</translation>
    </message>
    <message>
        <location filename="qscilexerbash.cpp" line="221"/>
        <source>Backticks</source>
        <translation>Aspas Invertidas</translation>
    </message>
    <message>
        <location filename="qscilexerbash.cpp" line="224"/>
        <source>Here document delimiter</source>
        <translation>Delimitador de &quot;here documents&quot;</translation>
    </message>
    <message>
        <location filename="qscilexerbash.cpp" line="227"/>
        <source>Single-quoted here document</source>
        <translation>&quot;here document&quot; envolvido por aspas simples</translation>
    </message>
</context>
<context>
    <name>QsciLexerBatch</name>
    <message>
        <location filename="qscilexerbatch.cpp" line="159"/>
        <source>Default</source>
        <translation>Padrão</translation>
    </message>
    <message>
        <location filename="qscilexerbatch.cpp" line="162"/>
        <source>Comment</source>
        <translation>Comentário</translation>
    </message>
    <message>
        <location filename="qscilexerbatch.cpp" line="165"/>
        <source>Keyword</source>
        <translation>Palavra Chave</translation>
    </message>
    <message>
        <location filename="qscilexerbatch.cpp" line="168"/>
        <source>Label</source>
        <translation>Rótulo</translation>
    </message>
    <message>
        <location filename="qscilexerbatch.cpp" line="171"/>
        <source>Hide command character</source>
        <translation>Esconder caractere de comando</translation>
    </message>
    <message>
        <location filename="qscilexerbatch.cpp" line="174"/>
        <source>External command</source>
        <translation>Comando externo</translation>
    </message>
    <message>
        <location filename="qscilexerbatch.cpp" line="177"/>
        <source>Variable</source>
        <translation>Variável</translation>
    </message>
    <message>
        <location filename="qscilexerbatch.cpp" line="180"/>
        <source>Operator</source>
        <translation>Operador</translation>
    </message>
</context>
<context>
    <name>QsciLexerCMake</name>
    <message>
        <location filename="qscilexercmake.cpp" line="175"/>
        <source>Default</source>
        <translation type="unfinished">Padrão</translation>
    </message>
    <message>
        <location filename="qscilexercmake.cpp" line="178"/>
        <source>Comment</source>
        <translation type="unfinished">Comentário</translation>
    </message>
    <message>
        <location filename="qscilexercmake.cpp" line="181"/>
        <source>String</source>
        <translation type="unfinished">Cadeia de Caracteres</translation>
    </message>
    <message>
        <location filename="qscilexercmake.cpp" line="184"/>
        <source>Left quoted string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercmake.cpp" line="187"/>
        <source>Right quoted string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercmake.cpp" line="190"/>
        <source>Function</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercmake.cpp" line="193"/>
        <source>Variable</source>
        <translation type="unfinished">Variável</translation>
    </message>
    <message>
        <location filename="qscilexercmake.cpp" line="196"/>
        <source>Label</source>
        <translation type="unfinished">Rótulo</translation>
    </message>
    <message>
        <location filename="qscilexercmake.cpp" line="199"/>
        <source>User defined</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercmake.cpp" line="202"/>
        <source>WHILE block</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercmake.cpp" line="205"/>
        <source>FOREACH block</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercmake.cpp" line="208"/>
        <source>IF block</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercmake.cpp" line="211"/>
        <source>MACRO block</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercmake.cpp" line="214"/>
        <source>Variable within a string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercmake.cpp" line="217"/>
        <source>Number</source>
        <translation type="unfinished">Número</translation>
    </message>
</context>
<context>
    <name>QsciLexerCPP</name>
    <message>
        <location filename="qscilexercpp.cpp" line="349"/>
        <source>Default</source>
        <translation>Padrão</translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="352"/>
        <source>Inactive default</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="355"/>
        <source>C comment</source>
        <translation>Comentário C</translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="358"/>
        <source>Inactive C comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="361"/>
        <source>C++ comment</source>
        <translation>Comentário C++</translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="364"/>
        <source>Inactive C++ comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="367"/>
        <source>JavaDoc style C comment</source>
        <translation>Comentário JavaDoc estilo C</translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="370"/>
        <source>Inactive JavaDoc style C comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="373"/>
        <source>Number</source>
        <translation>Número</translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="376"/>
        <source>Inactive number</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="379"/>
        <source>Keyword</source>
        <translation>Palavra Chave</translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="382"/>
        <source>Inactive keyword</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="385"/>
        <source>Double-quoted string</source>
        <translation>Cadeia de caracteres envolvida por aspas duplas</translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="388"/>
        <source>Inactive double-quoted string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="391"/>
        <source>Single-quoted string</source>
        <translation>Cadeia de caracteres envolvida por aspas simples</translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="394"/>
        <source>Inactive single-quoted string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="397"/>
        <source>IDL UUID</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="400"/>
        <source>Inactive IDL UUID</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="403"/>
        <source>Pre-processor block</source>
        <translation>Instruções de pré-processamento</translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="406"/>
        <source>Inactive pre-processor block</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="409"/>
        <source>Operator</source>
        <translation>Operador</translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="412"/>
        <source>Inactive operator</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="415"/>
        <source>Identifier</source>
        <translation>Identificador</translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="418"/>
        <source>Inactive identifier</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="421"/>
        <source>Unclosed string</source>
        <translation>Cadeia de caracteres não fechada</translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="424"/>
        <source>Inactive unclosed string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="427"/>
        <source>C# verbatim string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="430"/>
        <source>Inactive C# verbatim string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="433"/>
        <source>JavaScript regular expression</source>
        <translation type="unfinished">Expressão regular JavaScript</translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="436"/>
        <source>Inactive JavaScript regular expression</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="439"/>
        <source>JavaDoc style C++ comment</source>
        <translation>Comentário JavaDoc estilo C++</translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="442"/>
        <source>Inactive JavaDoc style C++ comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="445"/>
        <source>Secondary keywords and identifiers</source>
        <translation>Identificadores e palavras chave secundárias</translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="448"/>
        <source>Inactive secondary keywords and identifiers</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="451"/>
        <source>JavaDoc keyword</source>
        <translation>Palavra chave JavaDoc</translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="454"/>
        <source>Inactive JavaDoc keyword</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="457"/>
        <source>JavaDoc keyword error</source>
        <translation>Erro de palavra chave do JavaDoc</translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="460"/>
        <source>Inactive JavaDoc keyword error</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="463"/>
        <source>Global classes and typedefs</source>
        <translation>Classes e definições de tipo globais</translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="466"/>
        <source>Inactive global classes and typedefs</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="469"/>
        <source>C++ raw string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="472"/>
        <source>Inactive C++ raw string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="475"/>
        <source>Vala triple-quoted verbatim string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="478"/>
        <source>Inactive Vala triple-quoted verbatim string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="481"/>
        <source>Pike hash-quoted string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="484"/>
        <source>Inactive Pike hash-quoted string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="487"/>
        <source>Pre-processor C comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="490"/>
        <source>Inactive pre-processor C comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="493"/>
        <source>JavaDoc style pre-processor comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="496"/>
        <source>Inactive JavaDoc style pre-processor comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="499"/>
        <source>User-defined literal</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="502"/>
        <source>Inactive user-defined literal</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="505"/>
        <source>Task marker</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="508"/>
        <source>Inactive task marker</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="511"/>
        <source>Escape sequence</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercpp.cpp" line="514"/>
        <source>Inactive escape sequence</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>QsciLexerCSS</name>
    <message>
        <location filename="qscilexercss.cpp" line="217"/>
        <source>Default</source>
        <translation>Padrão</translation>
    </message>
    <message>
        <location filename="qscilexercss.cpp" line="220"/>
        <source>Tag</source>
        <translation>Marcador</translation>
    </message>
    <message>
        <location filename="qscilexercss.cpp" line="223"/>
        <source>Class selector</source>
        <translation>Seletor de classe</translation>
    </message>
    <message>
        <location filename="qscilexercss.cpp" line="226"/>
        <source>Pseudo-class</source>
        <translation>Pseudo-classe</translation>
    </message>
    <message>
        <location filename="qscilexercss.cpp" line="229"/>
        <source>Unknown pseudo-class</source>
        <translation>Pseudo-classe desconhecida</translation>
    </message>
    <message>
        <location filename="qscilexercss.cpp" line="232"/>
        <source>Operator</source>
        <translation>Operador</translation>
    </message>
    <message>
        <location filename="qscilexercss.cpp" line="235"/>
        <source>CSS1 property</source>
        <translation>Propriedade CSS1</translation>
    </message>
    <message>
        <location filename="qscilexercss.cpp" line="238"/>
        <source>Unknown property</source>
        <translation>Propriedade desconhecida</translation>
    </message>
    <message>
        <location filename="qscilexercss.cpp" line="241"/>
        <source>Value</source>
        <translation>Valor</translation>
    </message>
    <message>
        <location filename="qscilexercss.cpp" line="244"/>
        <source>ID selector</source>
        <translation>Seletor de ID</translation>
    </message>
    <message>
        <location filename="qscilexercss.cpp" line="247"/>
        <source>Important</source>
        <translation>Importante</translation>
    </message>
    <message>
        <location filename="qscilexercss.cpp" line="250"/>
        <source>@-rule</source>
        <translation>regra-@</translation>
    </message>
    <message>
        <location filename="qscilexercss.cpp" line="253"/>
        <source>Double-quoted string</source>
        <translation>Cadeia de caracteres envolvida por aspas duplas</translation>
    </message>
    <message>
        <location filename="qscilexercss.cpp" line="256"/>
        <source>Single-quoted string</source>
        <translation>Cadeia de caracteres envolvida por aspas simples</translation>
    </message>
    <message>
        <location filename="qscilexercss.cpp" line="259"/>
        <source>CSS2 property</source>
        <translation>Propriedade CSS2</translation>
    </message>
    <message>
        <location filename="qscilexercss.cpp" line="262"/>
        <source>Attribute</source>
        <translation>Atributo</translation>
    </message>
    <message>
        <location filename="qscilexercss.cpp" line="265"/>
        <source>CSS3 property</source>
        <translation type="unfinished">Propriedade CSS2 {3 ?}</translation>
    </message>
    <message>
        <location filename="qscilexercss.cpp" line="268"/>
        <source>Pseudo-element</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercss.cpp" line="271"/>
        <source>Extended CSS property</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercss.cpp" line="274"/>
        <source>Extended pseudo-class</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercss.cpp" line="277"/>
        <source>Extended pseudo-element</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercss.cpp" line="280"/>
        <source>Media rule</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercss.cpp" line="283"/>
        <source>Variable</source>
        <translation type="unfinished">Variável</translation>
    </message>
</context>
<context>
    <name>QsciLexerCSharp</name>
    <message>
        <location filename="qscilexercsharp.cpp" line="90"/>
        <source>Verbatim string</source>
        <translation>Cadeia de caracteres no formato verbatim</translation>
    </message>
</context>
<context>
    <name>QsciLexerCoffeeScript</name>
    <message>
        <location filename="qscilexercoffeescript.cpp" line="243"/>
        <source>Default</source>
        <translation type="unfinished">Padrão</translation>
    </message>
    <message>
        <location filename="qscilexercoffeescript.cpp" line="246"/>
        <source>C-style comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercoffeescript.cpp" line="249"/>
        <source>C++-style comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercoffeescript.cpp" line="252"/>
        <source>JavaDoc C-style comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercoffeescript.cpp" line="255"/>
        <source>Number</source>
        <translation type="unfinished">Número</translation>
    </message>
    <message>
        <location filename="qscilexercoffeescript.cpp" line="258"/>
        <source>Keyword</source>
        <translation type="unfinished">Palavra Chave</translation>
    </message>
    <message>
        <location filename="qscilexercoffeescript.cpp" line="261"/>
        <source>Double-quoted string</source>
        <translation type="unfinished">Cadeia de caracteres envolvida por aspas duplas</translation>
    </message>
    <message>
        <location filename="qscilexercoffeescript.cpp" line="264"/>
        <source>Single-quoted string</source>
        <translation type="unfinished">Cadeia de caracteres envolvida por aspas simples</translation>
    </message>
    <message>
        <location filename="qscilexercoffeescript.cpp" line="267"/>
        <source>IDL UUID</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercoffeescript.cpp" line="270"/>
        <source>Pre-processor block</source>
        <translation type="unfinished">Instruções de pré-processamento</translation>
    </message>
    <message>
        <location filename="qscilexercoffeescript.cpp" line="273"/>
        <source>Operator</source>
        <translation type="unfinished">Operador</translation>
    </message>
    <message>
        <location filename="qscilexercoffeescript.cpp" line="276"/>
        <source>Identifier</source>
        <translation type="unfinished">Identificador</translation>
    </message>
    <message>
        <location filename="qscilexercoffeescript.cpp" line="279"/>
        <source>Unclosed string</source>
        <translation type="unfinished">Cadeia de caracteres não fechada</translation>
    </message>
    <message>
        <location filename="qscilexercoffeescript.cpp" line="282"/>
        <source>C# verbatim string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercoffeescript.cpp" line="285"/>
        <source>Regular expression</source>
        <translation type="unfinished">Expressão Regular</translation>
    </message>
    <message>
        <location filename="qscilexercoffeescript.cpp" line="288"/>
        <source>JavaDoc C++-style comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercoffeescript.cpp" line="291"/>
        <source>Secondary keywords and identifiers</source>
        <translation type="unfinished">Identificadores e palavras chave secundárias</translation>
    </message>
    <message>
        <location filename="qscilexercoffeescript.cpp" line="294"/>
        <source>JavaDoc keyword</source>
        <translation type="unfinished">Palavra chave JavaDoc</translation>
    </message>
    <message>
        <location filename="qscilexercoffeescript.cpp" line="297"/>
        <source>JavaDoc keyword error</source>
        <translation type="unfinished">Erro de palavra chave do JavaDoc</translation>
    </message>
    <message>
        <location filename="qscilexercoffeescript.cpp" line="300"/>
        <source>Global classes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercoffeescript.cpp" line="303"/>
        <source>Block comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercoffeescript.cpp" line="306"/>
        <source>Block regular expression</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercoffeescript.cpp" line="309"/>
        <source>Block regular expression comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexercoffeescript.cpp" line="312"/>
        <source>Instance property</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>QsciLexerD</name>
    <message>
        <location filename="qscilexerd.cpp" line="251"/>
        <source>Default</source>
        <translation type="unfinished">Padrão</translation>
    </message>
    <message>
        <location filename="qscilexerd.cpp" line="254"/>
        <source>Block comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerd.cpp" line="257"/>
        <source>Line comment</source>
        <translation type="unfinished">Comentar Linha</translation>
    </message>
    <message>
        <location filename="qscilexerd.cpp" line="260"/>
        <source>DDoc style block comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerd.cpp" line="263"/>
        <source>Nesting comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerd.cpp" line="266"/>
        <source>Number</source>
        <translation type="unfinished">Número</translation>
    </message>
    <message>
        <location filename="qscilexerd.cpp" line="269"/>
        <source>Keyword</source>
        <translation type="unfinished">Palavra Chave</translation>
    </message>
    <message>
        <location filename="qscilexerd.cpp" line="272"/>
        <source>Secondary keyword</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerd.cpp" line="275"/>
        <source>Documentation keyword</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerd.cpp" line="278"/>
        <source>Type definition</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerd.cpp" line="281"/>
        <source>String</source>
        <translation type="unfinished">Cadeia de Caracteres</translation>
    </message>
    <message>
        <location filename="qscilexerd.cpp" line="284"/>
        <source>Unclosed string</source>
        <translation type="unfinished">Cadeia de caracteres não fechada</translation>
    </message>
    <message>
        <location filename="qscilexerd.cpp" line="287"/>
        <source>Character</source>
        <translation type="unfinished">Caractere</translation>
    </message>
    <message>
        <location filename="qscilexerd.cpp" line="290"/>
        <source>Operator</source>
        <translation type="unfinished">Operador</translation>
    </message>
    <message>
        <location filename="qscilexerd.cpp" line="293"/>
        <source>Identifier</source>
        <translation type="unfinished">Identificador</translation>
    </message>
    <message>
        <location filename="qscilexerd.cpp" line="296"/>
        <source>DDoc style line comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerd.cpp" line="299"/>
        <source>DDoc keyword</source>
        <translation type="unfinished">Palavra chave JavaDoc</translation>
    </message>
    <message>
        <location filename="qscilexerd.cpp" line="302"/>
        <source>DDoc keyword error</source>
        <translation type="unfinished">Erro de palavra chave do JavaDoc</translation>
    </message>
    <message>
        <location filename="qscilexerd.cpp" line="305"/>
        <source>Backquoted string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerd.cpp" line="308"/>
        <source>Raw string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerd.cpp" line="311"/>
        <source>User defined 1</source>
        <translation type="unfinished">Definição de usuário 1</translation>
    </message>
    <message>
        <location filename="qscilexerd.cpp" line="314"/>
        <source>User defined 2</source>
        <translation type="unfinished">Definição de usuário 2</translation>
    </message>
    <message>
        <location filename="qscilexerd.cpp" line="317"/>
        <source>User defined 3</source>
        <translation type="unfinished">Definição de usuário 3</translation>
    </message>
</context>
<context>
    <name>QsciLexerDiff</name>
    <message>
        <location filename="qscilexerdiff.cpp" line="91"/>
        <source>Default</source>
        <translation>Padrão</translation>
    </message>
    <message>
        <location filename="qscilexerdiff.cpp" line="94"/>
        <source>Comment</source>
        <translation>Comentário</translation>
    </message>
    <message>
        <location filename="qscilexerdiff.cpp" line="97"/>
        <source>Command</source>
        <translation>Comando</translation>
    </message>
    <message>
        <location filename="qscilexerdiff.cpp" line="100"/>
        <source>Header</source>
        <translation>Cabeçalho</translation>
    </message>
    <message>
        <location filename="qscilexerdiff.cpp" line="103"/>
        <source>Position</source>
        <translation>Posição</translation>
    </message>
    <message>
        <location filename="qscilexerdiff.cpp" line="106"/>
        <source>Removed line</source>
        <translation>Linha Removida</translation>
    </message>
    <message>
        <location filename="qscilexerdiff.cpp" line="109"/>
        <source>Added line</source>
        <translation>Linha Adicionada</translation>
    </message>
    <message>
        <location filename="qscilexerdiff.cpp" line="112"/>
        <source>Changed line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerdiff.cpp" line="115"/>
        <source>Added adding patch</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerdiff.cpp" line="118"/>
        <source>Removed adding patch</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerdiff.cpp" line="121"/>
        <source>Added removing patch</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerdiff.cpp" line="124"/>
        <source>Removed removing patch</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>QsciLexerEDIFACT</name>
    <message>
        <location filename="qscilexeredifact.cpp" line="79"/>
        <source>Default</source>
        <translation type="unfinished">Padrão</translation>
    </message>
    <message>
        <location filename="qscilexeredifact.cpp" line="82"/>
        <source>Segment start</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexeredifact.cpp" line="85"/>
        <source>Segment end</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexeredifact.cpp" line="88"/>
        <source>Element separator</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexeredifact.cpp" line="91"/>
        <source>Composite separator</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexeredifact.cpp" line="94"/>
        <source>Release separator</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexeredifact.cpp" line="97"/>
        <source>UNA segment header</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexeredifact.cpp" line="100"/>
        <source>UNH segment header</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexeredifact.cpp" line="103"/>
        <source>Badly formed segment</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>QsciLexerFortran77</name>
    <message>
        <location filename="qscilexerfortran77.cpp" line="170"/>
        <source>Default</source>
        <translation type="unfinished">Padrão</translation>
    </message>
    <message>
        <location filename="qscilexerfortran77.cpp" line="173"/>
        <source>Comment</source>
        <translation type="unfinished">Comentário</translation>
    </message>
    <message>
        <location filename="qscilexerfortran77.cpp" line="176"/>
        <source>Number</source>
        <translation type="unfinished">Número</translation>
    </message>
    <message>
        <location filename="qscilexerfortran77.cpp" line="179"/>
        <source>Single-quoted string</source>
        <translation type="unfinished">Cadeia de caracteres envolvida por aspas simples</translation>
    </message>
    <message>
        <location filename="qscilexerfortran77.cpp" line="182"/>
        <source>Double-quoted string</source>
        <translation type="unfinished">Cadeia de caracteres envolvida por aspas duplas</translation>
    </message>
    <message>
        <location filename="qscilexerfortran77.cpp" line="185"/>
        <source>Unclosed string</source>
        <translation type="unfinished">Cadeia de caracteres não fechada</translation>
    </message>
    <message>
        <location filename="qscilexerfortran77.cpp" line="188"/>
        <source>Operator</source>
        <translation type="unfinished">Operador</translation>
    </message>
    <message>
        <location filename="qscilexerfortran77.cpp" line="191"/>
        <source>Identifier</source>
        <translation type="unfinished">Identificador</translation>
    </message>
    <message>
        <location filename="qscilexerfortran77.cpp" line="194"/>
        <source>Keyword</source>
        <translation type="unfinished">Palavra Chave</translation>
    </message>
    <message>
        <location filename="qscilexerfortran77.cpp" line="197"/>
        <source>Intrinsic function</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerfortran77.cpp" line="200"/>
        <source>Extended function</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerfortran77.cpp" line="203"/>
        <source>Pre-processor block</source>
        <translation type="unfinished">Instruções de pré-processamento</translation>
    </message>
    <message>
        <location filename="qscilexerfortran77.cpp" line="206"/>
        <source>Dotted operator</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerfortran77.cpp" line="209"/>
        <source>Label</source>
        <translation type="unfinished">Rótulo</translation>
    </message>
    <message>
        <location filename="qscilexerfortran77.cpp" line="212"/>
        <source>Continuation</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>QsciLexerHTML</name>
    <message>
        <location filename="qscilexerhtml.cpp" line="548"/>
        <source>HTML default</source>
        <translation>HTML por padrão</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="551"/>
        <source>Tag</source>
        <translation>Marcador</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="554"/>
        <source>Unknown tag</source>
        <translation>Marcador desconhecido</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="557"/>
        <source>Attribute</source>
        <translation>Atributo</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="560"/>
        <source>Unknown attribute</source>
        <translation>Atributo desconhecido</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="563"/>
        <source>HTML number</source>
        <translation>Número HTML</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="566"/>
        <source>HTML double-quoted string</source>
        <translation>Cadeia de caracteres HTML envolvida por aspas duplas</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="569"/>
        <source>HTML single-quoted string</source>
        <translation>Cadeia de caracteres HTML envolvida por aspas simples</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="572"/>
        <source>Other text in a tag</source>
        <translation>Outro texto em um marcador</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="575"/>
        <source>HTML comment</source>
        <translation>Comentário HTML</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="578"/>
        <source>Entity</source>
        <translation>Entidade</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="581"/>
        <source>End of a tag</source>
        <translation>Final de um marcador</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="584"/>
        <source>Start of an XML fragment</source>
        <translation>Início de um bloco XML</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="587"/>
        <source>End of an XML fragment</source>
        <translation>Final de um bloco XML</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="590"/>
        <source>Script tag</source>
        <translation>Marcador de script</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="593"/>
        <source>Start of an ASP fragment with @</source>
        <translation>Início de um bloco ASP com @</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="596"/>
        <source>Start of an ASP fragment</source>
        <translation>Início de um bloco ASP</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="599"/>
        <source>CDATA</source>
        <translation>CDATA</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="602"/>
        <source>Start of a PHP fragment</source>
        <translation>Início de um bloco PHP</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="605"/>
        <source>Unquoted HTML value</source>
        <translation>Valor HTML não envolvido por aspas</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="608"/>
        <source>ASP X-Code comment</source>
        <translation>Comentário ASP X-Code</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="611"/>
        <source>SGML default</source>
        <translation>SGML por padrão</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="614"/>
        <source>SGML command</source>
        <translation>Comando SGML</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="617"/>
        <source>First parameter of an SGML command</source>
        <translation>Primeiro parâmetro em um comando SGML</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="620"/>
        <source>SGML double-quoted string</source>
        <translation>Cadeia de caracteres SGML envolvida por aspas duplas</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="623"/>
        <source>SGML single-quoted string</source>
        <translation>Cadeia de caracteres SGML envolvida por aspas simples</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="626"/>
        <source>SGML error</source>
        <translation>Erro SGML</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="629"/>
        <source>SGML special entity</source>
        <translation>Entidade especial SGML</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="632"/>
        <source>SGML comment</source>
        <translation>Comando SGML</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="635"/>
        <source>First parameter comment of an SGML command</source>
        <translation>Primeiro comentário de parâmetro de uma comando SGML</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="638"/>
        <source>SGML block default</source>
        <translation>Bloco SGML por padrão</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="641"/>
        <source>Start of a JavaScript fragment</source>
        <translation>Início de um bloco Javascript</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="644"/>
        <source>JavaScript default</source>
        <translation>JavaScript por padrão</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="647"/>
        <source>JavaScript comment</source>
        <translation>Comentário JavaScript</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="650"/>
        <source>JavaScript line comment</source>
        <translation>Comentário de linha JavaScript</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="653"/>
        <source>JavaDoc style JavaScript comment</source>
        <translation>Comentário JavaScript no estilo JavaDoc</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="656"/>
        <source>JavaScript number</source>
        <translation>Número JavaScript</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="659"/>
        <source>JavaScript word</source>
        <translation>Palavra JavaScript</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="662"/>
        <source>JavaScript keyword</source>
        <translation>Palavra chave JavaScript</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="665"/>
        <source>JavaScript double-quoted string</source>
        <translation>Cadeia de caracteres JavaScript envolvida por aspas duplas</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="668"/>
        <source>JavaScript single-quoted string</source>
        <translation>Cadeia de caracteres JavaScript envolvida por aspas simples</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="671"/>
        <source>JavaScript symbol</source>
        <translation>Símbolo JavaScript</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="674"/>
        <source>JavaScript unclosed string</source>
        <translation>Cadeia de caracteres JavaScript não fechada</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="677"/>
        <source>JavaScript regular expression</source>
        <translation>Expressão regular JavaScript</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="680"/>
        <source>Start of an ASP JavaScript fragment</source>
        <translation>Início de um bloco Javascript ASP</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="683"/>
        <source>ASP JavaScript default</source>
        <translation>JavaScript ASP por padrão</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="686"/>
        <source>ASP JavaScript comment</source>
        <translation>Comentário JavaScript ASP</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="689"/>
        <source>ASP JavaScript line comment</source>
        <translation>Comentário de linha JavaScript ASP</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="692"/>
        <source>JavaDoc style ASP JavaScript comment</source>
        <translation>Comentário JavaScript ASP no estilo JavaDoc</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="695"/>
        <source>ASP JavaScript number</source>
        <translation>Número JavaScript ASP</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="698"/>
        <source>ASP JavaScript word</source>
        <translation>Palavra chave JavaScript ASP</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="701"/>
        <source>ASP JavaScript keyword</source>
        <translation>Palavra chave JavaScript ASP</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="704"/>
        <source>ASP JavaScript double-quoted string</source>
        <translation>Cadeia de caracteres JavaScript ASP envolvida por aspas duplas</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="707"/>
        <source>ASP JavaScript single-quoted string</source>
        <translation>Cadeia de caracteres JavaScript ASP envolvida por aspas simples</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="710"/>
        <source>ASP JavaScript symbol</source>
        <translation>Símbolo JavaScript ASP</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="713"/>
        <source>ASP JavaScript unclosed string</source>
        <translation>Cadeia de caracteres JavaScript ASP não fechada</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="716"/>
        <source>ASP JavaScript regular expression</source>
        <translation>Expressão regular JavaScript ASP</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="719"/>
        <source>Start of a VBScript fragment</source>
        <translation>Início de um bloco VBScript</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="722"/>
        <source>VBScript default</source>
        <translation>VBScript por padrão</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="725"/>
        <source>VBScript comment</source>
        <translation>Comentário VBScript</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="728"/>
        <source>VBScript number</source>
        <translation>Número VBScript</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="731"/>
        <source>VBScript keyword</source>
        <translation>Palavra chave VBScript</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="734"/>
        <source>VBScript string</source>
        <translation>Cadeia de caracteres VBScript</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="737"/>
        <source>VBScript identifier</source>
        <translation>Identificador VBScript</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="740"/>
        <source>VBScript unclosed string</source>
        <translation>Cadeia de caracteres VBScript não fechada</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="743"/>
        <source>Start of an ASP VBScript fragment</source>
        <translation>Início de um bloco VBScript ASP</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="746"/>
        <source>ASP VBScript default</source>
        <translation>VBScript ASP por padrão</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="749"/>
        <source>ASP VBScript comment</source>
        <translation>Comentário VBScript ASP</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="752"/>
        <source>ASP VBScript number</source>
        <translation>Número VBScript ASP</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="755"/>
        <source>ASP VBScript keyword</source>
        <translation>Palavra chave VBScript ASP</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="758"/>
        <source>ASP VBScript string</source>
        <translation>Cadeia de caracteres VBScript ASP</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="761"/>
        <source>ASP VBScript identifier</source>
        <translation>Identificador VBScript ASP</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="764"/>
        <source>ASP VBScript unclosed string</source>
        <translation>Cadeia de caracteres VBScript ASP não fechada</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="767"/>
        <source>Start of a Python fragment</source>
        <translation>Início de um bloco Python</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="770"/>
        <source>Python default</source>
        <translation>Python por padrão</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="773"/>
        <source>Python comment</source>
        <translation>Comentário Python</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="776"/>
        <source>Python number</source>
        <translation>Número Python</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="779"/>
        <source>Python double-quoted string</source>
        <translation>Cadeia de caracteres Python envolvida por aspas duplas</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="782"/>
        <source>Python single-quoted string</source>
        <translation>Cadeia de caracteres Python envolvida por aspas simples</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="785"/>
        <source>Python keyword</source>
        <translation>Palavra chave Python</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="788"/>
        <source>Python triple double-quoted string</source>
        <translation>Cadeia de caracteres Python envolvida por aspas triplas duplas</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="791"/>
        <source>Python triple single-quoted string</source>
        <translation>Cadeia de caracteres Python envolvida por aspas triplas simples</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="794"/>
        <source>Python class name</source>
        <translation>Nome de classe Python</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="797"/>
        <source>Python function or method name</source>
        <translation>Nome de método ou função Python</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="800"/>
        <source>Python operator</source>
        <translation>Operador Python</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="803"/>
        <source>Python identifier</source>
        <translation>Identificador Python</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="806"/>
        <source>Start of an ASP Python fragment</source>
        <translation>Início de um bloco Python ASP</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="809"/>
        <source>ASP Python default</source>
        <translation>Python ASP por padrão</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="812"/>
        <source>ASP Python comment</source>
        <translation>Comentário Python ASP</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="815"/>
        <source>ASP Python number</source>
        <translation>Número Python ASP</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="818"/>
        <source>ASP Python double-quoted string</source>
        <translation>Cadeia de caracteres Python ASP envolvida por aspas duplas</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="821"/>
        <source>ASP Python single-quoted string</source>
        <translation>Cadeia de caracteres Python ASP envolvida por aspas simples</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="824"/>
        <source>ASP Python keyword</source>
        <translation>Palavra chave Python ASP</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="827"/>
        <source>ASP Python triple double-quoted string</source>
        <translation>Cadeia de caracteres Python ASP envolvida por aspas triplas duplas</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="830"/>
        <source>ASP Python triple single-quoted string</source>
        <translation>Cadeia de caracteres Python ASP envolvida por aspas triplas simples</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="833"/>
        <source>ASP Python class name</source>
        <translation>Nome de classe Python ASP</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="836"/>
        <source>ASP Python function or method name</source>
        <translation>Nome de método ou função Python ASP</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="839"/>
        <source>ASP Python operator</source>
        <translation>Operador Python ASP</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="842"/>
        <source>ASP Python identifier</source>
        <translation>Identificador Python ASP</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="845"/>
        <source>PHP default</source>
        <translation>PHP por padrão</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="848"/>
        <source>PHP double-quoted string</source>
        <translation>Cadeia de caracteres PHP envolvida por aspas duplas</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="851"/>
        <source>PHP single-quoted string</source>
        <translation>Cadeia de caracteres PHP envolvida por aspas simples</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="854"/>
        <source>PHP keyword</source>
        <translation>Palavra chave PHP</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="857"/>
        <source>PHP number</source>
        <translation>Número PHP</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="860"/>
        <source>PHP variable</source>
        <translation>Variável PHP</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="863"/>
        <source>PHP comment</source>
        <translation>Comentário PHP</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="866"/>
        <source>PHP line comment</source>
        <translation>Comentário de linha PHP</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="869"/>
        <source>PHP double-quoted variable</source>
        <translation>Variável PHP envolvida por aspas duplas</translation>
    </message>
    <message>
        <location filename="qscilexerhtml.cpp" line="872"/>
        <source>PHP operator</source>
        <translation>Operador PHP</translation>
    </message>
</context>
<context>
    <name>QsciLexerIDL</name>
    <message>
        <location filename="qscilexeridl.cpp" line="82"/>
        <source>UUID</source>
        <translation>UUID</translation>
    </message>
</context>
<context>
    <name>QsciLexerJSON</name>
    <message>
        <location filename="qscilexerjson.cpp" line="145"/>
        <source>Default</source>
        <translation type="unfinished">Padrão</translation>
    </message>
    <message>
        <location filename="qscilexerjson.cpp" line="148"/>
        <source>Number</source>
        <translation type="unfinished">Número</translation>
    </message>
    <message>
        <location filename="qscilexerjson.cpp" line="151"/>
        <source>String</source>
        <translation type="unfinished">Cadeia de Caracteres</translation>
    </message>
    <message>
        <location filename="qscilexerjson.cpp" line="154"/>
        <source>Unclosed string</source>
        <translation type="unfinished">Cadeia de caracteres não fechada</translation>
    </message>
    <message>
        <location filename="qscilexerjson.cpp" line="157"/>
        <source>Property</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerjson.cpp" line="160"/>
        <source>Escape sequence</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerjson.cpp" line="163"/>
        <source>Line comment</source>
        <translation type="unfinished">Comentar Linha</translation>
    </message>
    <message>
        <location filename="qscilexerjson.cpp" line="166"/>
        <source>Block comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerjson.cpp" line="169"/>
        <source>Operator</source>
        <translation type="unfinished">Operador</translation>
    </message>
    <message>
        <location filename="qscilexerjson.cpp" line="172"/>
        <source>IRI</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerjson.cpp" line="175"/>
        <source>JSON-LD compact IRI</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerjson.cpp" line="178"/>
        <source>JSON keyword</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerjson.cpp" line="181"/>
        <source>JSON-LD keyword</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerjson.cpp" line="184"/>
        <source>Parsing error</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>QsciLexerJavaScript</name>
    <message>
        <location filename="qscilexerjavascript.cpp" line="92"/>
        <source>Regular expression</source>
        <translation>Expressão Regular</translation>
    </message>
</context>
<context>
    <name>QsciLexerLua</name>
    <message>
        <location filename="qscilexerlua.cpp" line="212"/>
        <source>Default</source>
        <translation>Padrão</translation>
    </message>
    <message>
        <location filename="qscilexerlua.cpp" line="215"/>
        <source>Comment</source>
        <translation>Comentário</translation>
    </message>
    <message>
        <location filename="qscilexerlua.cpp" line="218"/>
        <source>Line comment</source>
        <translation>Comentar Linha</translation>
    </message>
    <message>
        <location filename="qscilexerlua.cpp" line="221"/>
        <source>Number</source>
        <translation>Número</translation>
    </message>
    <message>
        <location filename="qscilexerlua.cpp" line="224"/>
        <source>Keyword</source>
        <translation>Palavra Chave</translation>
    </message>
    <message>
        <location filename="qscilexerlua.cpp" line="227"/>
        <source>String</source>
        <translation>Cadeia de Caracteres</translation>
    </message>
    <message>
        <location filename="qscilexerlua.cpp" line="230"/>
        <source>Character</source>
        <translation>Caractere</translation>
    </message>
    <message>
        <location filename="qscilexerlua.cpp" line="233"/>
        <source>Literal string</source>
        <translation>Cadeia de caracteres literal</translation>
    </message>
    <message>
        <location filename="qscilexerlua.cpp" line="236"/>
        <source>Preprocessor</source>
        <translation>Preprocessador</translation>
    </message>
    <message>
        <location filename="qscilexerlua.cpp" line="239"/>
        <source>Operator</source>
        <translation>Operador</translation>
    </message>
    <message>
        <location filename="qscilexerlua.cpp" line="242"/>
        <source>Identifier</source>
        <translation>Identificador</translation>
    </message>
    <message>
        <location filename="qscilexerlua.cpp" line="245"/>
        <source>Unclosed string</source>
        <translation>Cadeia de caracteres não fechada</translation>
    </message>
    <message>
        <location filename="qscilexerlua.cpp" line="248"/>
        <source>Basic functions</source>
        <translation>Funções básicas</translation>
    </message>
    <message>
        <location filename="qscilexerlua.cpp" line="251"/>
        <source>String, table and maths functions</source>
        <translation>Funções de cadeia de caracteres e de tabelas matemáticas</translation>
    </message>
    <message>
        <location filename="qscilexerlua.cpp" line="254"/>
        <source>Coroutines, i/o and system facilities</source>
        <translation>Funções auxiiares, e/s e funções de sistema</translation>
    </message>
    <message>
        <location filename="qscilexerlua.cpp" line="257"/>
        <source>User defined 1</source>
        <translation type="unfinished">Definição de usuário 1</translation>
    </message>
    <message>
        <location filename="qscilexerlua.cpp" line="260"/>
        <source>User defined 2</source>
        <translation type="unfinished">Definição de usuário 2</translation>
    </message>
    <message>
        <location filename="qscilexerlua.cpp" line="263"/>
        <source>User defined 3</source>
        <translation type="unfinished">Definição de usuário 3</translation>
    </message>
    <message>
        <location filename="qscilexerlua.cpp" line="266"/>
        <source>User defined 4</source>
        <translation type="unfinished">Definição de usuário 4</translation>
    </message>
    <message>
        <location filename="qscilexerlua.cpp" line="269"/>
        <source>Label</source>
        <translation type="unfinished">Rótulo</translation>
    </message>
</context>
<context>
    <name>QsciLexerMakefile</name>
    <message>
        <location filename="qscilexermakefile.cpp" line="111"/>
        <source>Default</source>
        <translation>Padrão</translation>
    </message>
    <message>
        <location filename="qscilexermakefile.cpp" line="114"/>
        <source>Comment</source>
        <translation>Comentário</translation>
    </message>
    <message>
        <location filename="qscilexermakefile.cpp" line="117"/>
        <source>Preprocessor</source>
        <translation>Preprocessador</translation>
    </message>
    <message>
        <location filename="qscilexermakefile.cpp" line="120"/>
        <source>Variable</source>
        <translation>Variável</translation>
    </message>
    <message>
        <location filename="qscilexermakefile.cpp" line="123"/>
        <source>Operator</source>
        <translation>Operador</translation>
    </message>
    <message>
        <location filename="qscilexermakefile.cpp" line="126"/>
        <source>Target</source>
        <translation>Destino</translation>
    </message>
    <message>
        <location filename="qscilexermakefile.cpp" line="129"/>
        <source>Error</source>
        <translation>Erro</translation>
    </message>
</context>
<context>
    <name>QsciLexerMarkdown</name>
    <message>
        <location filename="qscilexermarkdown.cpp" line="207"/>
        <source>Default</source>
        <translation type="unfinished">Padrão</translation>
    </message>
    <message>
        <location filename="qscilexermarkdown.cpp" line="210"/>
        <source>Special</source>
        <translation type="unfinished">Especial</translation>
    </message>
    <message>
        <location filename="qscilexermarkdown.cpp" line="213"/>
        <source>Strong emphasis using double asterisks</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexermarkdown.cpp" line="216"/>
        <source>Strong emphasis using double underscores</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexermarkdown.cpp" line="219"/>
        <source>Emphasis using single asterisks</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexermarkdown.cpp" line="222"/>
        <source>Emphasis using single underscores</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexermarkdown.cpp" line="225"/>
        <source>Level 1 header</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexermarkdown.cpp" line="228"/>
        <source>Level 2 header</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexermarkdown.cpp" line="231"/>
        <source>Level 3 header</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexermarkdown.cpp" line="234"/>
        <source>Level 4 header</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexermarkdown.cpp" line="237"/>
        <source>Level 5 header</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexermarkdown.cpp" line="240"/>
        <source>Level 6 header</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexermarkdown.cpp" line="243"/>
        <source>Pre-char</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexermarkdown.cpp" line="246"/>
        <source>Unordered list item</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexermarkdown.cpp" line="249"/>
        <source>Ordered list item</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexermarkdown.cpp" line="252"/>
        <source>Block quote</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexermarkdown.cpp" line="255"/>
        <source>Strike out</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexermarkdown.cpp" line="258"/>
        <source>Horizontal rule</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexermarkdown.cpp" line="261"/>
        <source>Link</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexermarkdown.cpp" line="264"/>
        <source>Code between backticks</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexermarkdown.cpp" line="267"/>
        <source>Code between double backticks</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexermarkdown.cpp" line="270"/>
        <source>Code block</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>QsciLexerMatlab</name>
    <message>
        <location filename="qscilexermatlab.cpp" line="118"/>
        <source>Default</source>
        <translation type="unfinished">Padrão</translation>
    </message>
    <message>
        <location filename="qscilexermatlab.cpp" line="121"/>
        <source>Comment</source>
        <translation type="unfinished">Comentário</translation>
    </message>
    <message>
        <location filename="qscilexermatlab.cpp" line="124"/>
        <source>Command</source>
        <translation type="unfinished">Comando</translation>
    </message>
    <message>
        <location filename="qscilexermatlab.cpp" line="127"/>
        <source>Number</source>
        <translation type="unfinished">Número</translation>
    </message>
    <message>
        <location filename="qscilexermatlab.cpp" line="130"/>
        <source>Keyword</source>
        <translation type="unfinished">Palavra Chave</translation>
    </message>
    <message>
        <location filename="qscilexermatlab.cpp" line="133"/>
        <source>Single-quoted string</source>
        <translation type="unfinished">Cadeia de caracteres envolvida por aspas simples</translation>
    </message>
    <message>
        <location filename="qscilexermatlab.cpp" line="136"/>
        <source>Operator</source>
        <translation type="unfinished">Operador</translation>
    </message>
    <message>
        <location filename="qscilexermatlab.cpp" line="139"/>
        <source>Identifier</source>
        <translation type="unfinished">Identificador</translation>
    </message>
    <message>
        <location filename="qscilexermatlab.cpp" line="142"/>
        <source>Double-quoted string</source>
        <translation type="unfinished">Cadeia de caracteres envolvida por aspas duplas</translation>
    </message>
</context>
<context>
    <name>QsciLexerPO</name>
    <message>
        <location filename="qscilexerpo.cpp" line="84"/>
        <source>Default</source>
        <translation type="unfinished">Padrão</translation>
    </message>
    <message>
        <location filename="qscilexerpo.cpp" line="87"/>
        <source>Comment</source>
        <translation type="unfinished">Comentário</translation>
    </message>
    <message>
        <location filename="qscilexerpo.cpp" line="90"/>
        <source>Message identifier</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpo.cpp" line="93"/>
        <source>Message identifier text</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpo.cpp" line="96"/>
        <source>Message string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpo.cpp" line="99"/>
        <source>Message string text</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpo.cpp" line="102"/>
        <source>Message context</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpo.cpp" line="105"/>
        <source>Message context text</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpo.cpp" line="108"/>
        <source>Fuzzy flag</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpo.cpp" line="111"/>
        <source>Programmer comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpo.cpp" line="114"/>
        <source>Reference</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpo.cpp" line="117"/>
        <source>Flags</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpo.cpp" line="120"/>
        <source>Message identifier text end-of-line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpo.cpp" line="123"/>
        <source>Message string text end-of-line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpo.cpp" line="126"/>
        <source>Message context text end-of-line</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>QsciLexerPOV</name>
    <message>
        <location filename="qscilexerpov.cpp" line="262"/>
        <source>Default</source>
        <translation>Padrão</translation>
    </message>
    <message>
        <location filename="qscilexerpov.cpp" line="265"/>
        <source>Comment</source>
        <translation>Comentário</translation>
    </message>
    <message>
        <location filename="qscilexerpov.cpp" line="268"/>
        <source>Comment line</source>
        <translation>Comentar Linha</translation>
    </message>
    <message>
        <location filename="qscilexerpov.cpp" line="271"/>
        <source>Number</source>
        <translation>Número</translation>
    </message>
    <message>
        <location filename="qscilexerpov.cpp" line="274"/>
        <source>Operator</source>
        <translation>Operador</translation>
    </message>
    <message>
        <location filename="qscilexerpov.cpp" line="277"/>
        <source>Identifier</source>
        <translation>Identificador</translation>
    </message>
    <message>
        <location filename="qscilexerpov.cpp" line="280"/>
        <source>String</source>
        <translation>Cadeia de Caracteres</translation>
    </message>
    <message>
        <location filename="qscilexerpov.cpp" line="283"/>
        <source>Unclosed string</source>
        <translation>Cadeia de caracteres não fechada</translation>
    </message>
    <message>
        <location filename="qscilexerpov.cpp" line="286"/>
        <source>Directive</source>
        <translation>Diretiva</translation>
    </message>
    <message>
        <location filename="qscilexerpov.cpp" line="289"/>
        <source>Bad directive</source>
        <translation>Diretiva ruim</translation>
    </message>
    <message>
        <location filename="qscilexerpov.cpp" line="292"/>
        <source>Objects, CSG and appearance</source>
        <translation>Objetos, CSG e aparência</translation>
    </message>
    <message>
        <location filename="qscilexerpov.cpp" line="295"/>
        <source>Types, modifiers and items</source>
        <translation>Tipos, modificadores e itens</translation>
    </message>
    <message>
        <location filename="qscilexerpov.cpp" line="298"/>
        <source>Predefined identifiers</source>
        <translation>Identificadores predefinidos</translation>
    </message>
    <message>
        <location filename="qscilexerpov.cpp" line="301"/>
        <source>Predefined functions</source>
        <translation>Funções predefinidas</translation>
    </message>
    <message>
        <location filename="qscilexerpov.cpp" line="304"/>
        <source>User defined 1</source>
        <translation>Definição de usuário 1</translation>
    </message>
    <message>
        <location filename="qscilexerpov.cpp" line="307"/>
        <source>User defined 2</source>
        <translation>Definição de usuário 2</translation>
    </message>
    <message>
        <location filename="qscilexerpov.cpp" line="310"/>
        <source>User defined 3</source>
        <translation>Definição de usuário 3</translation>
    </message>
</context>
<context>
    <name>QsciLexerPascal</name>
    <message>
        <location filename="qscilexerpascal.cpp" line="241"/>
        <source>Default</source>
        <translation type="unfinished">Padrão</translation>
    </message>
    <message>
        <location filename="qscilexerpascal.cpp" line="253"/>
        <source>Line comment</source>
        <translation type="unfinished">Comentar Linha</translation>
    </message>
    <message>
        <location filename="qscilexerpascal.cpp" line="262"/>
        <source>Number</source>
        <translation type="unfinished">Número</translation>
    </message>
    <message>
        <location filename="qscilexerpascal.cpp" line="268"/>
        <source>Keyword</source>
        <translation type="unfinished">Palavra Chave</translation>
    </message>
    <message>
        <location filename="qscilexerpascal.cpp" line="271"/>
        <source>Single-quoted string</source>
        <translation type="unfinished">Cadeia de caracteres envolvida por aspas simples</translation>
    </message>
    <message>
        <location filename="qscilexerpascal.cpp" line="280"/>
        <source>Operator</source>
        <translation type="unfinished">Operador</translation>
    </message>
    <message>
        <location filename="qscilexerpascal.cpp" line="244"/>
        <source>Identifier</source>
        <translation type="unfinished">Identificador</translation>
    </message>
    <message>
        <location filename="qscilexerpascal.cpp" line="247"/>
        <source>&apos;{ ... }&apos; style comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpascal.cpp" line="250"/>
        <source>&apos;(* ... *)&apos; style comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpascal.cpp" line="256"/>
        <source>&apos;{$ ... }&apos; style pre-processor block</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpascal.cpp" line="259"/>
        <source>&apos;(*$ ... *)&apos; style pre-processor block</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpascal.cpp" line="265"/>
        <source>Hexadecimal number</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpascal.cpp" line="274"/>
        <source>Unclosed string</source>
        <translation type="unfinished">Cadeia de caracteres não fechada</translation>
    </message>
    <message>
        <location filename="qscilexerpascal.cpp" line="277"/>
        <source>Character</source>
        <translation type="unfinished">Caractere</translation>
    </message>
    <message>
        <location filename="qscilexerpascal.cpp" line="283"/>
        <source>Inline asm</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>QsciLexerPerl</name>
    <message>
        <location filename="qscilexerperl.cpp" line="313"/>
        <source>Default</source>
        <translation>Padrão</translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="316"/>
        <source>Error</source>
        <translation>Erro</translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="319"/>
        <source>Comment</source>
        <translation>Comentário</translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="322"/>
        <source>POD</source>
        <translation>POD</translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="325"/>
        <source>Number</source>
        <translation>Número</translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="328"/>
        <source>Keyword</source>
        <translation>Palavra Chave</translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="331"/>
        <source>Double-quoted string</source>
        <translation>Cadeia de caracteres envolvida por aspas duplas</translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="334"/>
        <source>Single-quoted string</source>
        <translation>Cadeia de caracteres envolvida por aspas simples</translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="337"/>
        <source>Operator</source>
        <translation>Operador</translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="340"/>
        <source>Identifier</source>
        <translation>Identificador</translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="343"/>
        <source>Scalar</source>
        <translation>Escalar</translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="346"/>
        <source>Array</source>
        <translation>Vetor</translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="349"/>
        <source>Hash</source>
        <translation>Hash</translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="352"/>
        <source>Symbol table</source>
        <translation>Tabela de Símbolos</translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="355"/>
        <source>Regular expression</source>
        <translation>Expressão Regular</translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="358"/>
        <source>Substitution</source>
        <translation>Substituição</translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="361"/>
        <source>Backticks</source>
        <translation>Aspas Invertidas</translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="364"/>
        <source>Data section</source>
        <translation>Seção de dados</translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="367"/>
        <source>Here document delimiter</source>
        <translation>Delimitador de documentos criados através de redicionadores (&gt;&gt; e &gt;)</translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="370"/>
        <source>Single-quoted here document</source>
        <translation>&quot;here document&quot; envolvido por aspas simples</translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="373"/>
        <source>Double-quoted here document</source>
        <translation>&quot;here document&quot; envolvido por aspas duplas</translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="376"/>
        <source>Backtick here document</source>
        <translation>&quot;here document&quot; envolvido por aspas invertidas</translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="379"/>
        <source>Quoted string (q)</source>
        <translation>Cadeia de caracteres envolvida por aspas (q)</translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="382"/>
        <source>Quoted string (qq)</source>
        <translation>Cadeia de caracteres envolvida por aspas (qq)</translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="385"/>
        <source>Quoted string (qx)</source>
        <translation>Cadeia de caracteres envolvida por aspas (qx)</translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="388"/>
        <source>Quoted string (qr)</source>
        <translation>Cadeia de caracteres envolvida por aspas (qr)</translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="391"/>
        <source>Quoted string (qw)</source>
        <translation>Cadeia de caracteres envolvida por aspas (qw)</translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="394"/>
        <source>POD verbatim</source>
        <translation>POD em formato verbatim</translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="397"/>
        <source>Subroutine prototype</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="400"/>
        <source>Format identifier</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="403"/>
        <source>Format body</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="406"/>
        <source>Double-quoted string (interpolated variable)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="409"/>
        <source>Translation</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="412"/>
        <source>Regular expression (interpolated variable)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="415"/>
        <source>Substitution (interpolated variable)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="418"/>
        <source>Backticks (interpolated variable)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="421"/>
        <source>Double-quoted here document (interpolated variable)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="424"/>
        <source>Backtick here document (interpolated variable)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="427"/>
        <source>Quoted string (qq, interpolated variable)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="430"/>
        <source>Quoted string (qx, interpolated variable)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerperl.cpp" line="433"/>
        <source>Quoted string (qr, interpolated variable)</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>QsciLexerPostScript</name>
    <message>
        <location filename="qscilexerpostscript.cpp" line="244"/>
        <source>Default</source>
        <translation type="unfinished">Padrão</translation>
    </message>
    <message>
        <location filename="qscilexerpostscript.cpp" line="247"/>
        <source>Comment</source>
        <translation type="unfinished">Comentário</translation>
    </message>
    <message>
        <location filename="qscilexerpostscript.cpp" line="250"/>
        <source>DSC comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpostscript.cpp" line="253"/>
        <source>DSC comment value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpostscript.cpp" line="256"/>
        <source>Number</source>
        <translation type="unfinished">Número</translation>
    </message>
    <message>
        <location filename="qscilexerpostscript.cpp" line="259"/>
        <source>Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpostscript.cpp" line="262"/>
        <source>Keyword</source>
        <translation type="unfinished">Palavra Chave</translation>
    </message>
    <message>
        <location filename="qscilexerpostscript.cpp" line="265"/>
        <source>Literal</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpostscript.cpp" line="268"/>
        <source>Immediately evaluated literal</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpostscript.cpp" line="271"/>
        <source>Array parenthesis</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpostscript.cpp" line="274"/>
        <source>Dictionary parenthesis</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpostscript.cpp" line="277"/>
        <source>Procedure parenthesis</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpostscript.cpp" line="280"/>
        <source>Text</source>
        <translation type="unfinished">Texto</translation>
    </message>
    <message>
        <location filename="qscilexerpostscript.cpp" line="283"/>
        <source>Hexadecimal string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpostscript.cpp" line="286"/>
        <source>Base85 string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpostscript.cpp" line="289"/>
        <source>Bad string character</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>QsciLexerProperties</name>
    <message>
        <location filename="qscilexerproperties.cpp" line="105"/>
        <source>Default</source>
        <translation>Padrão</translation>
    </message>
    <message>
        <location filename="qscilexerproperties.cpp" line="108"/>
        <source>Comment</source>
        <translation>Comentário</translation>
    </message>
    <message>
        <location filename="qscilexerproperties.cpp" line="111"/>
        <source>Section</source>
        <translation>Seção</translation>
    </message>
    <message>
        <location filename="qscilexerproperties.cpp" line="114"/>
        <source>Assignment</source>
        <translation>Atribuição</translation>
    </message>
    <message>
        <location filename="qscilexerproperties.cpp" line="117"/>
        <source>Default value</source>
        <translation>Valor Padrão</translation>
    </message>
    <message>
        <location filename="qscilexerproperties.cpp" line="120"/>
        <source>Key</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>QsciLexerPython</name>
    <message>
        <location filename="qscilexerpython.cpp" line="223"/>
        <source>Default</source>
        <translation>Padrão</translation>
    </message>
    <message>
        <location filename="qscilexerpython.cpp" line="226"/>
        <source>Comment</source>
        <translation>Comentário</translation>
    </message>
    <message>
        <location filename="qscilexerpython.cpp" line="229"/>
        <source>Number</source>
        <translation>Número</translation>
    </message>
    <message>
        <location filename="qscilexerpython.cpp" line="232"/>
        <source>Double-quoted string</source>
        <translation>Cadeia de caracteres envolvida por aspas duplas</translation>
    </message>
    <message>
        <location filename="qscilexerpython.cpp" line="235"/>
        <source>Single-quoted string</source>
        <translation>Cadeia de caracteres envolvida por aspas simples</translation>
    </message>
    <message>
        <location filename="qscilexerpython.cpp" line="238"/>
        <source>Keyword</source>
        <translation>Palavra Chave</translation>
    </message>
    <message>
        <location filename="qscilexerpython.cpp" line="241"/>
        <source>Triple single-quoted string</source>
        <translation>Cadeia de caracteres envolvida por três aspas simples</translation>
    </message>
    <message>
        <location filename="qscilexerpython.cpp" line="244"/>
        <source>Triple double-quoted string</source>
        <translation>Cadeia de caracteres envolvida por três aspas duplas</translation>
    </message>
    <message>
        <location filename="qscilexerpython.cpp" line="247"/>
        <source>Class name</source>
        <translation>Nome da classe</translation>
    </message>
    <message>
        <location filename="qscilexerpython.cpp" line="250"/>
        <source>Function or method name</source>
        <translation>Nome da função ou método</translation>
    </message>
    <message>
        <location filename="qscilexerpython.cpp" line="253"/>
        <source>Operator</source>
        <translation>Operador</translation>
    </message>
    <message>
        <location filename="qscilexerpython.cpp" line="256"/>
        <source>Identifier</source>
        <translation>Identificador</translation>
    </message>
    <message>
        <location filename="qscilexerpython.cpp" line="259"/>
        <source>Comment block</source>
        <translation>Bloco de comentários</translation>
    </message>
    <message>
        <location filename="qscilexerpython.cpp" line="262"/>
        <source>Unclosed string</source>
        <translation>Cadeia de caracteres não fechada</translation>
    </message>
    <message>
        <location filename="qscilexerpython.cpp" line="265"/>
        <source>Highlighted identifier</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpython.cpp" line="268"/>
        <source>Decorator</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpython.cpp" line="271"/>
        <source>Double-quoted f-string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpython.cpp" line="274"/>
        <source>Single-quoted f-string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpython.cpp" line="277"/>
        <source>Triple single-quoted f-string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerpython.cpp" line="280"/>
        <source>Triple double-quoted f-string</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>QsciLexerRuby</name>
    <message>
        <location filename="qscilexerruby.cpp" line="233"/>
        <source>Default</source>
        <translation>Padrão</translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="239"/>
        <source>Comment</source>
        <translation>Comentário</translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="245"/>
        <source>Number</source>
        <translation>Número</translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="251"/>
        <source>Double-quoted string</source>
        <translation>Cadeia de caracteres envolvida por aspas duplas</translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="254"/>
        <source>Single-quoted string</source>
        <translation>Cadeia de caracteres envolvida por aspas simples</translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="248"/>
        <source>Keyword</source>
        <translation>Palavra Chave</translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="257"/>
        <source>Class name</source>
        <translation>Nome da classe</translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="260"/>
        <source>Function or method name</source>
        <translation>Nome da função ou método</translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="263"/>
        <source>Operator</source>
        <translation>Operador</translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="266"/>
        <source>Identifier</source>
        <translation>Identificador</translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="236"/>
        <source>Error</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="242"/>
        <source>POD</source>
        <translation type="unfinished">POD</translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="269"/>
        <source>Regular expression</source>
        <translation type="unfinished">Expressão Regular</translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="272"/>
        <source>Global</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="275"/>
        <source>Symbol</source>
        <translation type="unfinished">Símbolo</translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="278"/>
        <source>Module name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="281"/>
        <source>Instance variable</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="284"/>
        <source>Class variable</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="287"/>
        <source>Backticks</source>
        <translation type="unfinished">Aspas Invertidas</translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="290"/>
        <source>Data section</source>
        <translation type="unfinished">Seção de dados</translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="293"/>
        <source>Here document delimiter</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="296"/>
        <source>Here document</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="299"/>
        <source>%q string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="302"/>
        <source>%Q string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="305"/>
        <source>%x string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="308"/>
        <source>%r string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="311"/>
        <source>%w string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="314"/>
        <source>Demoted keyword</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="317"/>
        <source>stdin</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="320"/>
        <source>stdout</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerruby.cpp" line="323"/>
        <source>stderr</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>QsciLexerSQL</name>
    <message>
        <location filename="qscilexersql.cpp" line="251"/>
        <source>Default</source>
        <translation>Padrão</translation>
    </message>
    <message>
        <location filename="qscilexersql.cpp" line="254"/>
        <source>Comment</source>
        <translation>Comentário</translation>
    </message>
    <message>
        <location filename="qscilexersql.cpp" line="263"/>
        <source>Number</source>
        <translation>Número</translation>
    </message>
    <message>
        <location filename="qscilexersql.cpp" line="266"/>
        <source>Keyword</source>
        <translation>Palavra Chave</translation>
    </message>
    <message>
        <location filename="qscilexersql.cpp" line="272"/>
        <source>Single-quoted string</source>
        <translation>Cadeia de caracteres envolvida por aspas simples</translation>
    </message>
    <message>
        <location filename="qscilexersql.cpp" line="281"/>
        <source>Operator</source>
        <translation>Operador</translation>
    </message>
    <message>
        <location filename="qscilexersql.cpp" line="284"/>
        <source>Identifier</source>
        <translation>Identificador</translation>
    </message>
    <message>
        <location filename="qscilexersql.cpp" line="257"/>
        <source>Comment line</source>
        <translation>Comentário de Linha</translation>
    </message>
    <message>
        <location filename="qscilexersql.cpp" line="260"/>
        <source>JavaDoc style comment</source>
        <translation>Comentário estilo JavaDoc</translation>
    </message>
    <message>
        <location filename="qscilexersql.cpp" line="269"/>
        <source>Double-quoted string</source>
        <translation>Cadeia de caracteres envolvida por aspas duplas</translation>
    </message>
    <message>
        <location filename="qscilexersql.cpp" line="275"/>
        <source>SQL*Plus keyword</source>
        <translation>Palavra chave do SQL*Plus</translation>
    </message>
    <message>
        <location filename="qscilexersql.cpp" line="278"/>
        <source>SQL*Plus prompt</source>
        <translation>Prompt do SQL*Plus</translation>
    </message>
    <message>
        <location filename="qscilexersql.cpp" line="287"/>
        <source>SQL*Plus comment</source>
        <translation>Comentário do SQL*Plus</translation>
    </message>
    <message>
        <location filename="qscilexersql.cpp" line="290"/>
        <source># comment line</source>
        <translation>Comentário de linha usando #</translation>
    </message>
    <message>
        <location filename="qscilexersql.cpp" line="293"/>
        <source>JavaDoc keyword</source>
        <translation>Palavra chave JavaDoc</translation>
    </message>
    <message>
        <location filename="qscilexersql.cpp" line="296"/>
        <source>JavaDoc keyword error</source>
        <translation>Erro de palavra chave do JavaDoc</translation>
    </message>
    <message>
        <location filename="qscilexersql.cpp" line="299"/>
        <source>User defined 1</source>
        <translation>Definição de usuário 1</translation>
    </message>
    <message>
        <location filename="qscilexersql.cpp" line="302"/>
        <source>User defined 2</source>
        <translation>Definição de usuário 2</translation>
    </message>
    <message>
        <location filename="qscilexersql.cpp" line="305"/>
        <source>User defined 3</source>
        <translation>Definição de usuário 3</translation>
    </message>
    <message>
        <location filename="qscilexersql.cpp" line="308"/>
        <source>User defined 4</source>
        <translation>Definição de usuário 4</translation>
    </message>
    <message>
        <location filename="qscilexersql.cpp" line="311"/>
        <source>Quoted identifier</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexersql.cpp" line="314"/>
        <source>Quoted operator</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>QsciLexerSpice</name>
    <message>
        <location filename="qscilexerspice.cpp" line="151"/>
        <source>Default</source>
        <translation type="unfinished">Padrão</translation>
    </message>
    <message>
        <location filename="qscilexerspice.cpp" line="154"/>
        <source>Identifier</source>
        <translation type="unfinished">Identificador</translation>
    </message>
    <message>
        <location filename="qscilexerspice.cpp" line="157"/>
        <source>Command</source>
        <translation type="unfinished">Comando</translation>
    </message>
    <message>
        <location filename="qscilexerspice.cpp" line="160"/>
        <source>Function</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerspice.cpp" line="163"/>
        <source>Parameter</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerspice.cpp" line="166"/>
        <source>Number</source>
        <translation type="unfinished">Número</translation>
    </message>
    <message>
        <location filename="qscilexerspice.cpp" line="169"/>
        <source>Delimiter</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerspice.cpp" line="172"/>
        <source>Value</source>
        <translation type="unfinished">Valor</translation>
    </message>
    <message>
        <location filename="qscilexerspice.cpp" line="175"/>
        <source>Comment</source>
        <translation type="unfinished">Comentário</translation>
    </message>
</context>
<context>
    <name>QsciLexerTCL</name>
    <message>
        <location filename="qscilexertcl.cpp" line="277"/>
        <source>Default</source>
        <translation type="unfinished">Padrão</translation>
    </message>
    <message>
        <location filename="qscilexertcl.cpp" line="280"/>
        <source>Comment</source>
        <translation type="unfinished">Comentário</translation>
    </message>
    <message>
        <location filename="qscilexertcl.cpp" line="283"/>
        <source>Comment line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexertcl.cpp" line="286"/>
        <source>Number</source>
        <translation type="unfinished">Número</translation>
    </message>
    <message>
        <location filename="qscilexertcl.cpp" line="289"/>
        <source>Quoted keyword</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexertcl.cpp" line="292"/>
        <source>Quoted string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexertcl.cpp" line="295"/>
        <source>Operator</source>
        <translation type="unfinished">Operador</translation>
    </message>
    <message>
        <location filename="qscilexertcl.cpp" line="298"/>
        <source>Identifier</source>
        <translation type="unfinished">Identificador</translation>
    </message>
    <message>
        <location filename="qscilexertcl.cpp" line="301"/>
        <source>Substitution</source>
        <translation type="unfinished">Substituição</translation>
    </message>
    <message>
        <location filename="qscilexertcl.cpp" line="304"/>
        <source>Brace substitution</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexertcl.cpp" line="307"/>
        <source>Modifier</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexertcl.cpp" line="310"/>
        <source>Expand keyword</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexertcl.cpp" line="313"/>
        <source>TCL keyword</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexertcl.cpp" line="316"/>
        <source>Tk keyword</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexertcl.cpp" line="319"/>
        <source>iTCL keyword</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexertcl.cpp" line="322"/>
        <source>Tk command</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexertcl.cpp" line="325"/>
        <source>User defined 1</source>
        <translation type="unfinished">Definição de usuário 1</translation>
    </message>
    <message>
        <location filename="qscilexertcl.cpp" line="328"/>
        <source>User defined 2</source>
        <translation type="unfinished">Definição de usuário 2</translation>
    </message>
    <message>
        <location filename="qscilexertcl.cpp" line="331"/>
        <source>User defined 3</source>
        <translation type="unfinished">Definição de usuário 3</translation>
    </message>
    <message>
        <location filename="qscilexertcl.cpp" line="334"/>
        <source>User defined 4</source>
        <translation type="unfinished">Definição de usuário 4</translation>
    </message>
    <message>
        <location filename="qscilexertcl.cpp" line="337"/>
        <source>Comment box</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexertcl.cpp" line="340"/>
        <source>Comment block</source>
        <translation type="unfinished">Bloco de comentários</translation>
    </message>
</context>
<context>
    <name>QsciLexerTeX</name>
    <message>
        <location filename="qscilexertex.cpp" line="172"/>
        <source>Default</source>
        <translation>Padrão</translation>
    </message>
    <message>
        <location filename="qscilexertex.cpp" line="175"/>
        <source>Special</source>
        <translation>Especial</translation>
    </message>
    <message>
        <location filename="qscilexertex.cpp" line="178"/>
        <source>Group</source>
        <translation>Grupo</translation>
    </message>
    <message>
        <location filename="qscilexertex.cpp" line="181"/>
        <source>Symbol</source>
        <translation>Símbolo</translation>
    </message>
    <message>
        <location filename="qscilexertex.cpp" line="184"/>
        <source>Command</source>
        <translation>Comando</translation>
    </message>
    <message>
        <location filename="qscilexertex.cpp" line="187"/>
        <source>Text</source>
        <translation>Texto</translation>
    </message>
</context>
<context>
    <name>QsciLexerVHDL</name>
    <message>
        <location filename="qscilexervhdl.cpp" line="192"/>
        <source>Default</source>
        <translation type="unfinished">Padrão</translation>
    </message>
    <message>
        <location filename="qscilexervhdl.cpp" line="195"/>
        <source>Comment</source>
        <translation type="unfinished">Comentário</translation>
    </message>
    <message>
        <location filename="qscilexervhdl.cpp" line="198"/>
        <source>Comment line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexervhdl.cpp" line="201"/>
        <source>Number</source>
        <translation type="unfinished">Número</translation>
    </message>
    <message>
        <location filename="qscilexervhdl.cpp" line="204"/>
        <source>String</source>
        <translation type="unfinished">Cadeia de Caracteres</translation>
    </message>
    <message>
        <location filename="qscilexervhdl.cpp" line="207"/>
        <source>Operator</source>
        <translation type="unfinished">Operador</translation>
    </message>
    <message>
        <location filename="qscilexervhdl.cpp" line="210"/>
        <source>Identifier</source>
        <translation type="unfinished">Identificador</translation>
    </message>
    <message>
        <location filename="qscilexervhdl.cpp" line="213"/>
        <source>Unclosed string</source>
        <translation type="unfinished">Cadeia de caracteres não fechada</translation>
    </message>
    <message>
        <location filename="qscilexervhdl.cpp" line="216"/>
        <source>Keyword</source>
        <translation type="unfinished">Palavra Chave</translation>
    </message>
    <message>
        <location filename="qscilexervhdl.cpp" line="219"/>
        <source>Standard operator</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexervhdl.cpp" line="222"/>
        <source>Attribute</source>
        <translation type="unfinished">Atributo</translation>
    </message>
    <message>
        <location filename="qscilexervhdl.cpp" line="225"/>
        <source>Standard function</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexervhdl.cpp" line="228"/>
        <source>Standard package</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexervhdl.cpp" line="231"/>
        <source>Standard type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexervhdl.cpp" line="234"/>
        <source>User defined</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexervhdl.cpp" line="237"/>
        <source>Comment block</source>
        <translation type="unfinished">Bloco de comentários</translation>
    </message>
</context>
<context>
    <name>QsciLexerVerilog</name>
    <message>
        <location filename="qscilexerverilog.cpp" line="281"/>
        <source>Default</source>
        <translation type="unfinished">Padrão</translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="284"/>
        <source>Inactive default</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="287"/>
        <source>Comment</source>
        <translation type="unfinished">Comentário</translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="290"/>
        <source>Inactive comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="293"/>
        <source>Line comment</source>
        <translation type="unfinished">Comentar Linha</translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="296"/>
        <source>Inactive line comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="299"/>
        <source>Bang comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="302"/>
        <source>Inactive bang comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="305"/>
        <source>Number</source>
        <translation type="unfinished">Número</translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="308"/>
        <source>Inactive number</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="311"/>
        <source>Primary keywords and identifiers</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="314"/>
        <source>Inactive primary keywords and identifiers</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="317"/>
        <source>String</source>
        <translation type="unfinished">Cadeia de Caracteres</translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="320"/>
        <source>Inactive string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="323"/>
        <source>Secondary keywords and identifiers</source>
        <translation type="unfinished">Identificadores e palavras chave secundárias</translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="326"/>
        <source>Inactive secondary keywords and identifiers</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="329"/>
        <source>System task</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="332"/>
        <source>Inactive system task</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="335"/>
        <source>Preprocessor block</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="338"/>
        <source>Inactive preprocessor block</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="341"/>
        <source>Operator</source>
        <translation type="unfinished">Operador</translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="344"/>
        <source>Inactive operator</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="347"/>
        <source>Identifier</source>
        <translation type="unfinished">Identificador</translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="350"/>
        <source>Inactive identifier</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="353"/>
        <source>Unclosed string</source>
        <translation type="unfinished">Cadeia de caracteres não fechada</translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="356"/>
        <source>Inactive unclosed string</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="359"/>
        <source>User defined tasks and identifiers</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="362"/>
        <source>Inactive user defined tasks and identifiers</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="365"/>
        <source>Keyword comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="368"/>
        <source>Inactive keyword comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="371"/>
        <source>Input port declaration</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="374"/>
        <source>Inactive input port declaration</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="377"/>
        <source>Output port declaration</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="380"/>
        <source>Inactive output port declaration</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="383"/>
        <source>Input/output port declaration</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="386"/>
        <source>Inactive input/output port declaration</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="389"/>
        <source>Port connection</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexerverilog.cpp" line="392"/>
        <source>Inactive port connection</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>QsciLexerYAML</name>
    <message>
        <location filename="qscilexeryaml.cpp" line="155"/>
        <source>Default</source>
        <translation type="unfinished">Padrão</translation>
    </message>
    <message>
        <location filename="qscilexeryaml.cpp" line="158"/>
        <source>Comment</source>
        <translation type="unfinished">Comentário</translation>
    </message>
    <message>
        <location filename="qscilexeryaml.cpp" line="161"/>
        <source>Identifier</source>
        <translation type="unfinished">Identificador</translation>
    </message>
    <message>
        <location filename="qscilexeryaml.cpp" line="164"/>
        <source>Keyword</source>
        <translation type="unfinished">Palavra Chave</translation>
    </message>
    <message>
        <location filename="qscilexeryaml.cpp" line="167"/>
        <source>Number</source>
        <translation type="unfinished">Número</translation>
    </message>
    <message>
        <location filename="qscilexeryaml.cpp" line="170"/>
        <source>Reference</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexeryaml.cpp" line="173"/>
        <source>Document delimiter</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexeryaml.cpp" line="176"/>
        <source>Text block marker</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexeryaml.cpp" line="179"/>
        <source>Syntax error marker</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qscilexeryaml.cpp" line="182"/>
        <source>Operator</source>
        <translation type="unfinished">Operador</translation>
    </message>
</context>
<context>
    <name>QsciScintilla</name>
    <message>
        <location filename="qsciscintilla.cpp" line="4455"/>
        <source>&amp;Undo</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qsciscintilla.cpp" line="4459"/>
        <source>&amp;Redo</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qsciscintilla.cpp" line="4465"/>
        <source>Cu&amp;t</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qsciscintilla.cpp" line="4470"/>
        <source>&amp;Copy</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qsciscintilla.cpp" line="4476"/>
        <source>&amp;Paste</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qsciscintilla.cpp" line="4480"/>
        <source>Delete</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="qsciscintilla.cpp" line="4487"/>
        <source>Select All</source>
        <translation type="unfinished"></translation>
    </message>
</context>
</TS>
