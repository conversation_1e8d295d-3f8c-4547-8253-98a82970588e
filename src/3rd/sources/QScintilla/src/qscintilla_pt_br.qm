<�d��!�`���B  P  U4  ��  U4  ��  Zw  /  Zw  lb �  � ��  py ��  �� ��  �� )[�  n 9  V; G��  O� H��  �� L�b   L�b  y� L�b  �� N��  �� Rx�  !� Rx�  # Rx�  >' Rx�  t� V��  � \��  0 \��  �, ^
t  h� o��  O4 s��  g� {�  A4 ���  W� ���  D� �ڴ  T� ��  �� �ۅ  ~� �o�  v �o�  z, �e�  o3&  �e��  �{��  {�  ��t  aO�%�  ���  \��m�  6�m�  �����  *���  ���  !����  (F���  3���  7����  =����  t����  |����  �����  �:���  � ���  �e���  �	���  �A���  ����  ����  +����  ,���  +u���  %U�@U   6��   �	��  E�Nh�  h2  ��_�  _��9U  )�p�  �u  �O�  T7�  6�<9t  �W��  P���U  <�$  M`��  ]����  ��o�  ���  ��?�  `����  �  R�4�%  +[��  �[�u  8f��  /�l&�  ]��I�    ��~  �Ű�  P��	4  dl��  m��z�  ;��  g��  4S�  rT�  �5�  ��
��  Y�vx  
�6�  ��6�  ��ct  H� T�  @�$<�  U�,_�  G @�A  9�@�A  w�@�A  �@�A  ��@�A  �@�B  9�@�B  x0@�B  �c@�B  ��@�B  �u@�C  :=@�C  x�@�C  ��@�C  �N@�C  ��@�D  x�@�D  ��@�D  �+A�  .@[��  6�\8�  �\8�  I\8�  #C\8�  (�\8�  3Z\8�  8h\8�  >_\8�  q%\8�  u�\8�  }:\8�  �2\8�  �h\8�  �}\8�  �I\8�  ��\8�  �L\8�  ��\8�  ��\8�  �i\8�  �I\8�  ��\8�  �[r�  )fr�  4�r�  ��vB  �r���  ���  �����  zz�5  ��5  ��5  %��5  1��5  7��5  =��5  tF�5  |��5  ��5  ���5  ���5  ��5  ���5  ���5  ���5  �$�5  ���5  ���5  ����G  #x��G  8���G  q���G  vd��G  ����G  ����G  ���9\  ���9\  �X��  TS*��  �g?^t  Q�_p�  �}�  (�}�  3�}�  >���d  ��('  l���.  ;����  K]���  b�Ȑ  �Ȑ  }Ȑ  "%Ȑ  (�Ȑ  .Ȑ  3�Ȑ  8�Ȑ  >�Ȑ  qYȐ  u�Ȑ  y�Ȑ  }pȐ  ��Ȑ  ��Ȑ  ��Ȑ  ��Ȑ  ��Ȑ  ��Ȑ  ��Ȑ  �}Ȑ  ��Ȑ  ��ѽ8  ԇ�   ���  f��  uH��  	�  
�"  Sk)6%  �)�  N6|�  ��_u�  ]^d*  ��d*i  ��d*�  �d*�  ��i�  -�܌�  U���  g(��  @` ��  i�	_�  L�&�  \%*  X�3g�  {j��  2|�^  7��  N%��"  6l��"  r���"  ��lT  e�~�  "`�~�  #��~�  0J�~�  z����  Be�2c  �
��2  nt���  ����  k�  C��2  L����  C		~�  �	&0'  !	&0'  ��	?3�  k�	@|U  mK	o��  k�	�@t  ��	���  N�	���  o�	��  !:	�^�  b?	��  R	��  �	��  $�	��  ,�	��  1C	��  <�	��  |	��  �B	��  �	��  ��	��  �1	��  ��	�X$  �	��=  �
 i�  
�
��  _{
�4  Z�
'�4  $F
*�S  �
*�S  ��
*�S  �w
Iq>  �
S�  �
S�  ��
d8  :�
d8  {h
d8  �
d8  ��
d<  
d<   u
d<  "�
d<  ;
d<  <
d<  s:
d<  yA
d<  {�
d<  ~D
d<  ~�
d<  ��
d<  ��
d<  ��
d<  �M
d<  �

d<  �n
d<  �<
d<  �^
d<  ��
d<  �x
d<  �`
d4  $
k�,  {+
k�,  � 
qe�  M�
��$  WT
��d  ��
��R  ,k
��B  I
��t  
��t  M
��t   �
��t  "�
��t  $�
��t  ,�
��t  1
��t  7l
��t  ;V
��t  <G
��t  <�
��t  p�
��t  t
��t  y�
��t  z�
��t  {�
��t  ~
��t  
��t  �X
��t  �|
��t  ��
��t  �B
��t  ��
��t  ��
��t  ��
��t  �z
��t  ��
��t  ��
��t  ��
��t  ��
��t  ��
�z�  PT
�ZT  [<
��  �
�Z5  w
_�  _)?  Z5"��  
(��  j!;2�  ;^�  I�A�\  �E��  i&_��  bcV�  :�d�  v�e�  n��4$  hS��  +;��  O���  ���y  w�|  &x�|  2��|  �c�Bu  �0ӕ�  �e�O  ���O  �Z��4  L��4  ��ؗ  Z�	�E  ^�
)�  ��
)�  ��
)�  �4>�  a�W��  D�f��  -�o�  Io�  �3�'  O���  �}��  S��  '\���  L+�'�  [��x�  CG

.  �2

.  �G
�E  l�
943  sv
@&4  e
@*$  d�
_�  r�
t}4  l
t}4  8!
t}4  p�
t}4  t�
t}4  �
t}4  �9
���  &�
�?D  E�
�Ig  *�
�Ig  5�
�Ig  9
�Ig  ?�
�Ig  q�
�Ig  we
�Ig  ��
�Ig  �r
�Ig  �
�Ig  �
�Ig  ��
��t  &
��t  2-
��t  �	
�`�  ^�
�.�  j�
�y  H_�W  o�
c�  k�  /P'��  
9�  G�=�e  `vV��  c�W�\  YL]�g  J�e�T  R|hw�  V�i�r  fEld  A�w�g  e�{�  F��&4  R0��  ���  *��  .���  5U��  ?O��  }���  ����  �T��  ���  �;��  ���B  B����  Fd�)w  �[Ʀ  �L�n  C�L�  ��kC  �%�j�  ;��2  '��2  X@�  I*#}w  0�4+4  �RΒ  a��2  \��)t  ��nE  ���T  eV��  _��  ��i  ��    C a n c e l a r       Cancel   QsciCommand   D C o n v e r t e r   a   s e l e � � o   p a r a   m i n � s c u l a       Convert selection to lower case   QsciCommand   D C o n v e r t e r   a   s e l e � � o   p a r a   m a i � s c u l a       Convert selection to upper case   QsciCommand   $ C o p i a r   l i n h a   a t u a l       Copy current line   QsciCommand    C o p i a r   s e l e � � o       Copy selection   QsciCommand   , C o n f i g u r a r   l i n h a   a t u a l       Cut current line   QsciCommand     R e c o r t a r   s e l e � � o       
Cut selection   QsciCommand   . E x c l u i r   c a r a c t e r e   a t u a l       Delete current character   QsciCommand   & E x c l u i r   l i n h a   a t u a l       Delete current line   QsciCommand   0 E x c l u i r   l i n h a   a   e s q u e r d a       Delete line to left   QsciCommand   4 E x c l u i r   l i n h a   p a r a   d i r e i t a       Delete line to right   QsciCommand   4 E x c l u i r   c a r a c t e r e   a n t e r i o r       Delete previous character   QsciCommand   4 E x c l u i r   p a l a v r a   a   e s q u e r d a       Delete word to left   QsciCommand   8 E x c l u i r   p a l a v r a   p a r a   d i r e i t a       Delete word to right   QsciCommand   d E x t e n d e r   a   s e l e � � o   r e t a n g u l a r   u m a   l i n h a   p a r a   b a i x o       *Extend rectangular selection down one line   QsciCommand   f E x t e n d e r   a   s e l e � � o   r e t a n g u l a r   u m a   p � g i n a   p a r a   b a i x o       *Extend rectangular selection down one page   QsciCommand   p E x t e n d e r   a   s e l e � � o   r e t a n g u l a r   u m   c a r a c t e r e   p a r a   e s q u e r d a       /Extend rectangular selection left one character   QsciCommand   n E x t e n d e r   a   s e l e � � o   r e t a n g u l a r   u m   c a r a c t e r e   p a r a   d i r e i t a       0Extend rectangular selection right one character   QsciCommand   b E x t e n d e r   a   s e l e � � o   r e t a n g u l a r   u m a   l i n h a   p a r a   c i m a       (Extend rectangular selection up one line   QsciCommand   d E x t e n d e r   a   s e l e � � o   r e t a n g u l a r   u m a   p � g i n a   p a r a   c i m a       (Extend rectangular selection up one page   QsciCommand   N E x t e n d e r   a   s e l e � � o   u m a   l i n h a   p a r a   b a i x o       Extend selection down one line   QsciCommand   P E x t e n d e r   a   s e l e � � o   u m a   p � g i n a   p a r a   b a i x o       Extend selection down one page   QsciCommand   V E x t e n d e r   a   s e l e � � o     u m   p a r a g r a f o   p a r a   b a i x o       #Extend selection down one paragraph   QsciCommand   Z E x t e n d e r   a   s e l e � � o   u m   c a r a c t e r e   p a r a   e s q u e r d a       #Extend selection left one character   QsciCommand   X E x t e n d e r   a   s e l e � � o   u m a   p a l a v r a   p a r a   e s q u e r d a       Extend selection left one word   QsciCommand   j E x t e n d e r   a   s e l e � � o   u m a   p a r t e   d e   p a l a v r a   p a r a   e s q u e r d a       #Extend selection left one word part   QsciCommand   X E x t e n d e r   a   s e l e � � o   u m   c a r a c t e r e   p a r a   d i r e i t a       $Extend selection right one character   QsciCommand   V E x t e n d e r   a   s e l e � � o   u m a   p a l a v r a   p a r a   d i r e i t a       Extend selection right one word   QsciCommand   h E x t e n d e r   a   s e l e � � o   u m a   p a r t e   d e   p a l a v r a   p a r a   d i r e i t a       $Extend selection right one word part   QsciCommand   L E x t e n d e r   a   s e l e � � o   u m a   l i n h a   p a r a   c i m a       Extend selection up one line   QsciCommand   N E x t e n d e r   a   s e l e � � o   u m a   p � g i n a   p a r a   c i m a       Extend selection up one page   QsciCommand   R E x t e n d e r   a   s e l e � � o   u m   p a r a g r a f o   p a r a   c i m a       !Extend selection up one paragraph   QsciCommand   * A l i m e n t a � � o   d a   P � g i n a       Formfeed   QsciCommand   " I n d e n t a r   u m   n � v e l       Indent one level   QsciCommand   4 M o v e r   u m a   l i n h a   p a r a   b a i x o       Move down one line   QsciCommand   6 M o v e r   u m a   p � g i n a   p a r a   b a i x o       Move down one page   QsciCommand   : M o v e r   u m   p a r a g r a f o   p a r a   b a i x o       Move down one paragraph   QsciCommand   D M o v e r   u m   c a r a c t e r e   p a r a   a   e s q u e r d a       Move left one character   QsciCommand   > M o v e r   u m a   p a l a v r a   p a r a   e s q u e r d a       Move left one word   QsciCommand   P M o v e r   u m a   p a r t e   d a   p a l a v r a   p a r a   e s q u e r d a       Move left one word part   QsciCommand   > M o v e r   u m   c a r a c t e r e   p a r a   d i r e i t a       Move right one character   QsciCommand   < M o v e r   u m a   p a l a v r a   p a r a   d i r e i t a       Move right one word   QsciCommand   N M o v e r   u m a   p a r t e   d a   p a l a v r a   p a r a   d i r e i t a       Move right one word part   QsciCommand   2 M o v e r   u m a   l i n h a   p a r a   c i m a       Move up one line   QsciCommand   4 M o v e r   u m a   p � g i n a   p a r a   c i m a       Move up one page   QsciCommand   8 M o v e r   u m   p a r a g r a f o   p a r a   c i m a       Move up one paragraph   QsciCommand    C o p i a r       Paste   QsciCommand   , R e f a z e r   � l t i m o   c o m a n d o       Redo last command   QsciCommand   F D e s c e r   a   v i s � o   u m a   l i n h a   p a r a   b a i x o       Scroll view down one line   QsciCommand   B S u b i r   a   v i s � o   u m a   l i n h a   p a r a   c i m a       Scroll view up one line   QsciCommand   X A l t e r n a r   e n t r e   m o d o   d e   i n s e r i r / s o b r e e s c r e v e r       Toggle insert/overtype   QsciCommand    A u m e n t a r   z o o m       Zoom in   QsciCommand    D i m i n u i r   z o o m       Zoom out   QsciCommand    P a d r � o       Default   QsciLexerAVS   ^ C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   a s p a s   d u p l a s       Double-quoted string   QsciLexerAVS    I d e n t i f i c a d o r       
Identifier   QsciLexerAVS    P a l a v r a   C h a v e       Keyword   QsciLexerAVS    C o m e n t a r   L i n h a       Line comment   QsciLexerAVS    N � m e r o       Number   QsciLexerAVS    O p e r a d o r       Operator   QsciLexerAVS   h C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   t r � s   a s p a s   d u p l a s       Triple double-quoted string   QsciLexerAVS     A s p a s   I n v e r t i d a s       	Backticks   
QsciLexerBash    C o m e n t � r i o       Comment   
QsciLexerBash    P a d r � o       Default   
QsciLexerBash   ^ C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   a s p a s   d u p l a s       Double-quoted string   
QsciLexerBash    N � m e r o       Error   
QsciLexerBash   > D e l i m i t a d o r   d e   " h e r e   d o c u m e n t s "       Here document delimiter   
QsciLexerBash    I d e n t i f i c a d o r       
Identifier   
QsciLexerBash    P a l a v r a   C h a v e       Keyword   
QsciLexerBash    N � m e r o       Number   
QsciLexerBash    O p e r a d o r       Operator   
QsciLexerBash   * P a r � m e t r o   d e   E x p a n s � o       Parameter expansion   
QsciLexerBash    E s c a l a r       Scalar   
QsciLexerBash   V " h e r e   d o c u m e n t "   e n v o l v i d o   p o r   a s p a s   s i m p l e s       Single-quoted here document   
QsciLexerBash   ` C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   a s p a s   s i m p l e s       Single-quoted string   
QsciLexerBash    C o m e n t � r i o       Comment   QsciLexerBatch    P a d r � o       Default   QsciLexerBatch    C o m a n d o   e x t e r n o       External command   QsciLexerBatch   : E s c o n d e r   c a r a c t e r e   d e   c o m a n d o       Hide command character   QsciLexerBatch    P a l a v r a   C h a v e       Keyword   QsciLexerBatch    R � t u l o       Label   QsciLexerBatch    O p e r a d o r       Operator   QsciLexerBatch    V a r i � v e l       Variable   QsciLexerBatch    C o m e n t � r i o       Comment   QsciLexerCMake    P a d r � o       Default   QsciLexerCMake    R � t u l o       Label   QsciLexerCMake    N � m e r o       Number   QsciLexerCMake   ( C a d e i a   d e   C a r a c t e r e s       String   QsciLexerCMake    V a r i � v e l       Variable   QsciLexerCMake    C o m e n t � r i o   C       	C comment   QsciLexerCPP    C o m e n t � r i o   C + +       C++ comment   QsciLexerCPP    P a d r � o       Default   QsciLexerCPP   ^ C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   a s p a s   d u p l a s       Double-quoted string   QsciLexerCPP   H C l a s s e s   e   d e f i n i � � e s   d e   t i p o   g l o b a i s       Global classes and typedefs   QsciLexerCPP    I d e n t i f i c a d o r       
Identifier   QsciLexerCPP   * P a l a v r a   c h a v e   J a v a D o c       JavaDoc keyword   QsciLexerCPP   @ E r r o   d e   p a l a v r a   c h a v e   d o   J a v a D o c       JavaDoc keyword error   QsciLexerCPP   6 C o m e n t � r i o   J a v a D o c   e s t i l o   C       JavaDoc style C comment   QsciLexerCPP   : C o m e n t � r i o   J a v a D o c   e s t i l o   C + +       JavaDoc style C++ comment   QsciLexerCPP   8 E x p r e s s � o   r e g u l a r   J a v a S c r i p t       JavaScript regular expression   QsciLexerCPP    P a l a v r a   C h a v e       Keyword   QsciLexerCPP    N � m e r o       Number   QsciLexerCPP    O p e r a d o r       Operator   QsciLexerCPP   > I n s t r u � � e s   d e   p r � - p r o c e s s a m e n t o       Pre-processor block   QsciLexerCPP   X I d e n t i f i c a d o r e s   e   p a l a v r a s   c h a v e   s e c u n d � r i a s       "Secondary keywords and identifiers   QsciLexerCPP   ` C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   a s p a s   s i m p l e s       Single-quoted string   QsciLexerCPP   @ C a d e i a   d e   c a r a c t e r e s   n � o   f e c h a d a       Unclosed string   QsciLexerCPP    r e g r a - @       @-rule   QsciLexerCSS    A t r i b u t o       	Attribute   QsciLexerCSS     P r o p r i e d a d e   C S S 1       
CSS1 property   QsciLexerCSS     P r o p r i e d a d e   C S S 2       
CSS2 property   QsciLexerCSS   , P r o p r i e d a d e   C S S 2   { 3   ? }       
CSS3 property   QsciLexerCSS   " S e l e t o r   d e   c l a s s e       Class selector   QsciLexerCSS    P a d r � o       Default   QsciLexerCSS   ^ C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   a s p a s   d u p l a s       Double-quoted string   QsciLexerCSS    S e l e t o r   d e   I D       ID selector   QsciLexerCSS    I m p o r t a n t e       	Important   QsciLexerCSS    O p e r a d o r       Operator   QsciLexerCSS    P s e u d o - c l a s s e       Pseudo-class   QsciLexerCSS   ` C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   a s p a s   s i m p l e s       Single-quoted string   QsciLexerCSS    M a r c a d o r       Tag   QsciLexerCSS   0 P r o p r i e d a d e   d e s c o n h e c i d a       Unknown property   QsciLexerCSS   4 P s e u d o - c l a s s e   d e s c o n h e c i d a       Unknown pseudo-class   QsciLexerCSS   
 V a l o r       Value   QsciLexerCSS    V a r i � v e l       Variable   QsciLexerCSS   P C a d e i a   d e   c a r a c t e r e s   n o   f o r m a t o   v e r b a t i m       Verbatim string   QsciLexerCSharp    P a d r � o       Default   QsciLexerCoffeeScript   ^ C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   a s p a s   d u p l a s       Double-quoted string   QsciLexerCoffeeScript    I d e n t i f i c a d o r       
Identifier   QsciLexerCoffeeScript   * P a l a v r a   c h a v e   J a v a D o c       JavaDoc keyword   QsciLexerCoffeeScript   @ E r r o   d e   p a l a v r a   c h a v e   d o   J a v a D o c       JavaDoc keyword error   QsciLexerCoffeeScript    P a l a v r a   C h a v e       Keyword   QsciLexerCoffeeScript    N � m e r o       Number   QsciLexerCoffeeScript    O p e r a d o r       Operator   QsciLexerCoffeeScript   > I n s t r u � � e s   d e   p r � - p r o c e s s a m e n t o       Pre-processor block   QsciLexerCoffeeScript   " E x p r e s s � o   R e g u l a r       Regular expression   QsciLexerCoffeeScript   X I d e n t i f i c a d o r e s   e   p a l a v r a s   c h a v e   s e c u n d � r i a s       "Secondary keywords and identifiers   QsciLexerCoffeeScript   ` C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   a s p a s   s i m p l e s       Single-quoted string   QsciLexerCoffeeScript   @ C a d e i a   d e   c a r a c t e r e s   n � o   f e c h a d a       Unclosed string   QsciLexerCoffeeScript    C a r a c t e r e       	Character   
QsciLexerD   * P a l a v r a   c h a v e   J a v a D o c       DDoc keyword   
QsciLexerD   @ E r r o   d e   p a l a v r a   c h a v e   d o   J a v a D o c       DDoc keyword error   
QsciLexerD    P a d r � o       Default   
QsciLexerD    I d e n t i f i c a d o r       
Identifier   
QsciLexerD    P a l a v r a   C h a v e       Keyword   
QsciLexerD    C o m e n t a r   L i n h a       Line comment   
QsciLexerD    N � m e r o       Number   
QsciLexerD    O p e r a d o r       Operator   
QsciLexerD   ( C a d e i a   d e   C a r a c t e r e s       String   
QsciLexerD   @ C a d e i a   d e   c a r a c t e r e s   n � o   f e c h a d a       Unclosed string   
QsciLexerD   , D e f i n i � � o   d e   u s u � r i o   1       User defined 1   
QsciLexerD   , D e f i n i � � o   d e   u s u � r i o   2       User defined 2   
QsciLexerD   , D e f i n i � � o   d e   u s u � r i o   3       User defined 3   
QsciLexerD     L i n h a   A d i c i o n a d a       
Added line   
QsciLexerDiff    C o m a n d o       Command   
QsciLexerDiff    C o m e n t � r i o       Comment   
QsciLexerDiff    P a d r � o       Default   
QsciLexerDiff    C a b e � a l h o       Header   
QsciLexerDiff    P o s i � � o       Position   
QsciLexerDiff    L i n h a   R e m o v i d a       Removed line   
QsciLexerDiff    P a d r � o       Default   QsciLexerEDIFACT    C o m e n t � r i o       Comment   QsciLexerFortran77    P a d r � o       Default   QsciLexerFortran77   ^ C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   a s p a s   d u p l a s       Double-quoted string   QsciLexerFortran77    I d e n t i f i c a d o r       
Identifier   QsciLexerFortran77    P a l a v r a   C h a v e       Keyword   QsciLexerFortran77    R � t u l o       Label   QsciLexerFortran77    N � m e r o       Number   QsciLexerFortran77    O p e r a d o r       Operator   QsciLexerFortran77   > I n s t r u � � e s   d e   p r � - p r o c e s s a m e n t o       Pre-processor block   QsciLexerFortran77   ` C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   a s p a s   s i m p l e s       Single-quoted string   QsciLexerFortran77   @ C a d e i a   d e   c a r a c t e r e s   n � o   f e c h a d a       Unclosed string   QsciLexerFortran77   2 C o m e n t � r i o   J a v a S c r i p t   A S P       ASP JavaScript comment   
QsciLexerHTML   2 J a v a S c r i p t   A S P   p o r   p a d r � o       ASP JavaScript default   
QsciLexerHTML   | C a d e i a   d e   c a r a c t e r e s   J a v a S c r i p t   A S P   e n v o l v i d a   p o r   a s p a s   d u p l a s       #ASP JavaScript double-quoted string   
QsciLexerHTML   8 P a l a v r a   c h a v e   J a v a S c r i p t   A S P       ASP JavaScript keyword   
QsciLexerHTML   D C o m e n t � r i o   d e   l i n h a   J a v a S c r i p t   A S P       ASP JavaScript line comment   
QsciLexerHTML   * N � m e r o   J a v a S c r i p t   A S P       ASP JavaScript number   
QsciLexerHTML   @ E x p r e s s � o   r e g u l a r   J a v a S c r i p t   A S P       !ASP JavaScript regular expression   
QsciLexerHTML   ~ C a d e i a   d e   c a r a c t e r e s   J a v a S c r i p t   A S P   e n v o l v i d a   p o r   a s p a s   s i m p l e s       #ASP JavaScript single-quoted string   
QsciLexerHTML   , S � m b o l o   J a v a S c r i p t   A S P       ASP JavaScript symbol   
QsciLexerHTML   ^ C a d e i a   d e   c a r a c t e r e s   J a v a S c r i p t   A S P   n � o   f e c h a d a       ASP JavaScript unclosed string   
QsciLexerHTML   8 P a l a v r a   c h a v e   J a v a S c r i p t   A S P       ASP JavaScript word   
QsciLexerHTML   2 N o m e   d e   c l a s s e   P y t h o n   A S P       ASP Python class name   
QsciLexerHTML   * C o m e n t � r i o   P y t h o n   A S P       ASP Python comment   
QsciLexerHTML   * P y t h o n   A S P   p o r   p a d r � o       ASP Python default   
QsciLexerHTML   t C a d e i a   d e   c a r a c t e r e s   P y t h o n   A S P   e n v o l v i d a   p o r   a s p a s   d u p l a s       ASP Python double-quoted string   
QsciLexerHTML   F N o m e   d e   m � t o d o   o u   f u n � � o   P y t h o n   A S P       "ASP Python function or method name   
QsciLexerHTML   0 I d e n t i f i c a d o r   P y t h o n   A S P       ASP Python identifier   
QsciLexerHTML   0 P a l a v r a   c h a v e   P y t h o n   A S P       ASP Python keyword   
QsciLexerHTML   " N � m e r o   P y t h o n   A S P       ASP Python number   
QsciLexerHTML   & O p e r a d o r   P y t h o n   A S P       ASP Python operator   
QsciLexerHTML   v C a d e i a   d e   c a r a c t e r e s   P y t h o n   A S P   e n v o l v i d a   p o r   a s p a s   s i m p l e s       ASP Python single-quoted string   
QsciLexerHTML   � C a d e i a   d e   c a r a c t e r e s   P y t h o n   A S P   e n v o l v i d a   p o r   a s p a s   t r i p l a s   d u p l a s       &ASP Python triple double-quoted string   
QsciLexerHTML   � C a d e i a   d e   c a r a c t e r e s   P y t h o n   A S P   e n v o l v i d a   p o r   a s p a s   t r i p l a s   s i m p l e s       &ASP Python triple single-quoted string   
QsciLexerHTML   . C o m e n t � r i o   V B S c r i p t   A S P       ASP VBScript comment   
QsciLexerHTML   . V B S c r i p t   A S P   p o r   p a d r � o       ASP VBScript default   
QsciLexerHTML   4 I d e n t i f i c a d o r   V B S c r i p t   A S P       ASP VBScript identifier   
QsciLexerHTML   4 P a l a v r a   c h a v e   V B S c r i p t   A S P       ASP VBScript keyword   
QsciLexerHTML   & N � m e r o   V B S c r i p t   A S P       ASP VBScript number   
QsciLexerHTML   B C a d e i a   d e   c a r a c t e r e s   V B S c r i p t   A S P       ASP VBScript string   
QsciLexerHTML   Z C a d e i a   d e   c a r a c t e r e s   V B S c r i p t   A S P   n � o   f e c h a d a       ASP VBScript unclosed string   
QsciLexerHTML   * C o m e n t � r i o   A S P   X - C o d e       ASP X-Code comment   
QsciLexerHTML    A t r i b u t o       	Attribute   
QsciLexerHTML   
 C D A T A       CDATA   
QsciLexerHTML   ( F i n a l   d e   u m   m a r c a d o r       End of a tag   
QsciLexerHTML   * F i n a l   d e   u m   b l o c o   X M L       End of an XML fragment   
QsciLexerHTML    E n t i d a d e       Entity   
QsciLexerHTML   h P r i m e i r o   c o m e n t � r i o   d e   p a r � m e t r o   d e   u m a   c o m a n d o   S G M L       *First parameter comment of an SGML command   
QsciLexerHTML   J P r i m e i r o   p a r � m e t r o   e m   u m   c o m a n d o   S G M L       "First parameter of an SGML command   
QsciLexerHTML    C o m e n t � r i o   H T M L       HTML comment   
QsciLexerHTML    H T M L   p o r   p a d r � o       HTML default   
QsciLexerHTML   h C a d e i a   d e   c a r a c t e r e s   H T M L   e n v o l v i d a   p o r   a s p a s   d u p l a s       HTML double-quoted string   
QsciLexerHTML    N � m e r o   H T M L       HTML number   
QsciLexerHTML   j C a d e i a   d e   c a r a c t e r e s   H T M L   e n v o l v i d a   p o r   a s p a s   s i m p l e s       HTML single-quoted string   
QsciLexerHTML   V C o m e n t � r i o   J a v a S c r i p t   A S P   n o   e s t i l o   J a v a D o c       $JavaDoc style ASP JavaScript comment   
QsciLexerHTML   N C o m e n t � r i o   J a v a S c r i p t   n o   e s t i l o   J a v a D o c        JavaDoc style JavaScript comment   
QsciLexerHTML   * C o m e n t � r i o   J a v a S c r i p t       JavaScript comment   
QsciLexerHTML   * J a v a S c r i p t   p o r   p a d r � o       JavaScript default   
QsciLexerHTML   t C a d e i a   d e   c a r a c t e r e s   J a v a S c r i p t   e n v o l v i d a   p o r   a s p a s   d u p l a s       JavaScript double-quoted string   
QsciLexerHTML   0 P a l a v r a   c h a v e   J a v a S c r i p t       JavaScript keyword   
QsciLexerHTML   < C o m e n t � r i o   d e   l i n h a   J a v a S c r i p t       JavaScript line comment   
QsciLexerHTML   " N � m e r o   J a v a S c r i p t       JavaScript number   
QsciLexerHTML   8 E x p r e s s � o   r e g u l a r   J a v a S c r i p t       JavaScript regular expression   
QsciLexerHTML   v C a d e i a   d e   c a r a c t e r e s   J a v a S c r i p t   e n v o l v i d a   p o r   a s p a s   s i m p l e s       JavaScript single-quoted string   
QsciLexerHTML   $ S � m b o l o   J a v a S c r i p t       JavaScript symbol   
QsciLexerHTML   V C a d e i a   d e   c a r a c t e r e s   J a v a S c r i p t   n � o   f e c h a d a       JavaScript unclosed string   
QsciLexerHTML   $ P a l a v r a   J a v a S c r i p t       JavaScript word   
QsciLexerHTML   4 O u t r o   t e x t o   e m   u m   m a r c a d o r       Other text in a tag   
QsciLexerHTML    C o m e n t � r i o   P H P       PHP comment   
QsciLexerHTML    P H P   p o r   p a d r � o       PHP default   
QsciLexerHTML   f C a d e i a   d e   c a r a c t e r e s   P H P   e n v o l v i d a   p o r   a s p a s   d u p l a s       PHP double-quoted string   
QsciLexerHTML   N V a r i � v e l   P H P   e n v o l v i d a   p o r   a s p a s   d u p l a s       PHP double-quoted variable   
QsciLexerHTML   " P a l a v r a   c h a v e   P H P       PHP keyword   
QsciLexerHTML   . C o m e n t � r i o   d e   l i n h a   P H P       PHP line comment   
QsciLexerHTML    N � m e r o   P H P       
PHP number   
QsciLexerHTML    O p e r a d o r   P H P       PHP operator   
QsciLexerHTML   h C a d e i a   d e   c a r a c t e r e s   P H P   e n v o l v i d a   p o r   a s p a s   s i m p l e s       PHP single-quoted string   
QsciLexerHTML    V a r i � v e l   P H P       PHP variable   
QsciLexerHTML   * N o m e   d e   c l a s s e   P y t h o n       Python class name   
QsciLexerHTML   " C o m e n t � r i o   P y t h o n       Python comment   
QsciLexerHTML   " P y t h o n   p o r   p a d r � o       Python default   
QsciLexerHTML   l C a d e i a   d e   c a r a c t e r e s   P y t h o n   e n v o l v i d a   p o r   a s p a s   d u p l a s       Python double-quoted string   
QsciLexerHTML   > N o m e   d e   m � t o d o   o u   f u n � � o   P y t h o n       Python function or method name   
QsciLexerHTML   ( I d e n t i f i c a d o r   P y t h o n       Python identifier   
QsciLexerHTML   ( P a l a v r a   c h a v e   P y t h o n       Python keyword   
QsciLexerHTML    N � m e r o   P y t h o n       
Python number   
QsciLexerHTML    O p e r a d o r   P y t h o n       Python operator   
QsciLexerHTML   n C a d e i a   d e   c a r a c t e r e s   P y t h o n   e n v o l v i d a   p o r   a s p a s   s i m p l e s       Python single-quoted string   
QsciLexerHTML   | C a d e i a   d e   c a r a c t e r e s   P y t h o n   e n v o l v i d a   p o r   a s p a s   t r i p l a s   d u p l a s       "Python triple double-quoted string   
QsciLexerHTML   ~ C a d e i a   d e   c a r a c t e r e s   P y t h o n   e n v o l v i d a   p o r   a s p a s   t r i p l a s   s i m p l e s       "Python triple single-quoted string   
QsciLexerHTML   * B l o c o   S G M L   p o r   p a d r � o       SGML block default   
QsciLexerHTML    C o m a n d o   S G M L       SGML command   
QsciLexerHTML    C o m a n d o   S G M L       SGML comment   
QsciLexerHTML    S G M L   p o r   p a d r � o       SGML default   
QsciLexerHTML   h C a d e i a   d e   c a r a c t e r e s   S G M L   e n v o l v i d a   p o r   a s p a s   d u p l a s       SGML double-quoted string   
QsciLexerHTML    E r r o   S G M L       
SGML error   
QsciLexerHTML   j C a d e i a   d e   c a r a c t e r e s   S G M L   e n v o l v i d a   p o r   a s p a s   s i m p l e s       SGML single-quoted string   
QsciLexerHTML   , E n t i d a d e   e s p e c i a l   S G M L       SGML special entity   
QsciLexerHTML   $ M a r c a d o r   d e   s c r i p t       
Script tag   
QsciLexerHTML   : I n � c i o   d e   u m   b l o c o   J a v a s c r i p t       Start of a JavaScript fragment   
QsciLexerHTML   , I n � c i o   d e   u m   b l o c o   P H P       Start of a PHP fragment   
QsciLexerHTML   2 I n � c i o   d e   u m   b l o c o   P y t h o n       Start of a Python fragment   
QsciLexerHTML   6 I n � c i o   d e   u m   b l o c o   V B S c r i p t       Start of a VBScript fragment   
QsciLexerHTML   B I n � c i o   d e   u m   b l o c o   J a v a s c r i p t   A S P       #Start of an ASP JavaScript fragment   
QsciLexerHTML   : I n � c i o   d e   u m   b l o c o   P y t h o n   A S P       Start of an ASP Python fragment   
QsciLexerHTML   > I n � c i o   d e   u m   b l o c o   V B S c r i p t   A S P       !Start of an ASP VBScript fragment   
QsciLexerHTML   , I n � c i o   d e   u m   b l o c o   A S P       Start of an ASP fragment   
QsciLexerHTML   8 I n � c i o   d e   u m   b l o c o   A S P   c o m   @       Start of an ASP fragment with @   
QsciLexerHTML   , I n � c i o   d e   u m   b l o c o   X M L       Start of an XML fragment   
QsciLexerHTML    M a r c a d o r       Tag   
QsciLexerHTML   * A t r i b u t o   d e s c o n h e c i d o       Unknown attribute   
QsciLexerHTML   * M a r c a d o r   d e s c o n h e c i d o       Unknown tag   
QsciLexerHTML   D V a l o r   H T M L   n � o   e n v o l v i d o   p o r   a s p a s       Unquoted HTML value   
QsciLexerHTML   & C o m e n t � r i o   V B S c r i p t       VBScript comment   
QsciLexerHTML   & V B S c r i p t   p o r   p a d r � o       VBScript default   
QsciLexerHTML   , I d e n t i f i c a d o r   V B S c r i p t       VBScript identifier   
QsciLexerHTML   , P a l a v r a   c h a v e   V B S c r i p t       VBScript keyword   
QsciLexerHTML    N � m e r o   V B S c r i p t       VBScript number   
QsciLexerHTML   : C a d e i a   d e   c a r a c t e r e s   V B S c r i p t       VBScript string   
QsciLexerHTML   R C a d e i a   d e   c a r a c t e r e s   V B S c r i p t   n � o   f e c h a d a       VBScript unclosed string   
QsciLexerHTML    U U I D       UUID   QsciLexerIDL    P a d r � o       Default   
QsciLexerJSON    C o m e n t a r   L i n h a       Line comment   
QsciLexerJSON    N � m e r o       Number   
QsciLexerJSON    O p e r a d o r       Operator   
QsciLexerJSON   ( C a d e i a   d e   C a r a c t e r e s       String   
QsciLexerJSON   @ C a d e i a   d e   c a r a c t e r e s   n � o   f e c h a d a       Unclosed string   
QsciLexerJSON   " E x p r e s s � o   R e g u l a r       Regular expression   QsciLexerJavaScript    F u n � � e s   b � s i c a s       Basic functions   QsciLexerLua    C a r a c t e r e       	Character   QsciLexerLua    C o m e n t � r i o       Comment   QsciLexerLua   V F u n � � e s   a u x i i a r e s ,   e / s   e   f u n � � e s   d e   s i s t e m a       %Coroutines, i/o and system facilities   QsciLexerLua    P a d r � o       Default   QsciLexerLua    I d e n t i f i c a d o r       
Identifier   QsciLexerLua    P a l a v r a   C h a v e       Keyword   QsciLexerLua    R � t u l o       Label   QsciLexerLua    C o m e n t a r   L i n h a       Line comment   QsciLexerLua   8 C a d e i a   d e   c a r a c t e r e s   l i t e r a l       Literal string   QsciLexerLua    N � m e r o       Number   QsciLexerLua    O p e r a d o r       Operator   QsciLexerLua    P r e p r o c e s s a d o r       Preprocessor   QsciLexerLua   ( C a d e i a   d e   C a r a c t e r e s       String   QsciLexerLua   p F u n � � e s   d e   c a d e i a   d e   c a r a c t e r e s   e   d e   t a b e l a s   m a t e m � t i c a s       !String, table and maths functions   QsciLexerLua   @ C a d e i a   d e   c a r a c t e r e s   n � o   f e c h a d a       Unclosed string   QsciLexerLua   , D e f i n i � � o   d e   u s u � r i o   1       User defined 1   QsciLexerLua   , D e f i n i � � o   d e   u s u � r i o   2       User defined 2   QsciLexerLua   , D e f i n i � � o   d e   u s u � r i o   3       User defined 3   QsciLexerLua   , D e f i n i � � o   d e   u s u � r i o   4       User defined 4   QsciLexerLua    C o m e n t � r i o       Comment   QsciLexerMakefile    P a d r � o       Default   QsciLexerMakefile    E r r o       Error   QsciLexerMakefile    O p e r a d o r       Operator   QsciLexerMakefile    P r e p r o c e s s a d o r       Preprocessor   QsciLexerMakefile    D e s t i n o       Target   QsciLexerMakefile    V a r i � v e l       Variable   QsciLexerMakefile    P a d r � o       Default   QsciLexerMarkdown    E s p e c i a l       Special   QsciLexerMarkdown    C o m a n d o       Command   QsciLexerMatlab    C o m e n t � r i o       Comment   QsciLexerMatlab    P a d r � o       Default   QsciLexerMatlab   ^ C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   a s p a s   d u p l a s       Double-quoted string   QsciLexerMatlab    I d e n t i f i c a d o r       
Identifier   QsciLexerMatlab    P a l a v r a   C h a v e       Keyword   QsciLexerMatlab    N � m e r o       Number   QsciLexerMatlab    O p e r a d o r       Operator   QsciLexerMatlab   ` C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   a s p a s   s i m p l e s       Single-quoted string   QsciLexerMatlab    C o m e n t � r i o       Comment   QsciLexerPO    P a d r � o       Default   QsciLexerPO    D i r e t i v a   r u i m       
Bad directive   QsciLexerPOV    C o m e n t � r i o       Comment   QsciLexerPOV    C o m e n t a r   L i n h a       Comment line   QsciLexerPOV    P a d r � o       Default   QsciLexerPOV    D i r e t i v a       	Directive   QsciLexerPOV    I d e n t i f i c a d o r       
Identifier   QsciLexerPOV    N � m e r o       Number   QsciLexerPOV   0 O b j e t o s ,   C S G   e   a p a r � n c i a       Objects, CSG and appearance   QsciLexerPOV    O p e r a d o r       Operator   QsciLexerPOV   ( F u n � � e s   p r e d e f i n i d a s       Predefined functions   QsciLexerPOV   8 I d e n t i f i c a d o r e s   p r e d e f i n i d o s       Predefined identifiers   QsciLexerPOV   ( C a d e i a   d e   C a r a c t e r e s       String   QsciLexerPOV   8 T i p o s ,   m o d i f i c a d o r e s   e   i t e n s       Types, modifiers and items   QsciLexerPOV   @ C a d e i a   d e   c a r a c t e r e s   n � o   f e c h a d a       Unclosed string   QsciLexerPOV   , D e f i n i � � o   d e   u s u � r i o   1       User defined 1   QsciLexerPOV   , D e f i n i � � o   d e   u s u � r i o   2       User defined 2   QsciLexerPOV   , D e f i n i � � o   d e   u s u � r i o   3       User defined 3   QsciLexerPOV    C a r a c t e r e       	Character   QsciLexerPascal    P a d r � o       Default   QsciLexerPascal    I d e n t i f i c a d o r       
Identifier   QsciLexerPascal    P a l a v r a   C h a v e       Keyword   QsciLexerPascal    C o m e n t a r   L i n h a       Line comment   QsciLexerPascal    N � m e r o       Number   QsciLexerPascal    O p e r a d o r       Operator   QsciLexerPascal   ` C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   a s p a s   s i m p l e s       Single-quoted string   QsciLexerPascal   @ C a d e i a   d e   c a r a c t e r e s   n � o   f e c h a d a       Unclosed string   QsciLexerPascal   
 V e t o r       Array   
QsciLexerPerl   \ " h e r e   d o c u m e n t "   e n v o l v i d o   p o r   a s p a s   i n v e r t i d a s       Backtick here document   
QsciLexerPerl     A s p a s   I n v e r t i d a s       	Backticks   
QsciLexerPerl    C o m e n t � r i o       Comment   
QsciLexerPerl    S e � � o   d e   d a d o s       Data section   
QsciLexerPerl    P a d r � o       Default   
QsciLexerPerl   T " h e r e   d o c u m e n t "   e n v o l v i d o   p o r   a s p a s   d u p l a s       Double-quoted here document   
QsciLexerPerl   ^ C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   a s p a s   d u p l a s       Double-quoted string   
QsciLexerPerl    E r r o       Error   
QsciLexerPerl    H a s h       Hash   
QsciLexerPerl   � D e l i m i t a d o r   d e   d o c u m e n t o s   c r i a d o s   a t r a v � s   d e   r e d i c i o n a d o r e s   ( > >   e   > )       Here document delimiter   
QsciLexerPerl    I d e n t i f i c a d o r       
Identifier   
QsciLexerPerl    P a l a v r a   C h a v e       Keyword   
QsciLexerPerl    N � m e r o       Number   
QsciLexerPerl    O p e r a d o r       Operator   
QsciLexerPerl    P O D       POD   
QsciLexerPerl   . P O D   e m   f o r m a t o   v e r b a t i m       POD verbatim   
QsciLexerPerl   X C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   a s p a s   ( q )       Quoted string (q)   
QsciLexerPerl   Z C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   a s p a s   ( q q )       Quoted string (qq)   
QsciLexerPerl   Z C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   a s p a s   ( q r )       Quoted string (qr)   
QsciLexerPerl   Z C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   a s p a s   ( q w )       Quoted string (qw)   
QsciLexerPerl   Z C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   a s p a s   ( q x )       Quoted string (qx)   
QsciLexerPerl   " E x p r e s s � o   R e g u l a r       Regular expression   
QsciLexerPerl    E s c a l a r       Scalar   
QsciLexerPerl   V " h e r e   d o c u m e n t "   e n v o l v i d o   p o r   a s p a s   s i m p l e s       Single-quoted here document   
QsciLexerPerl   ` C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   a s p a s   s i m p l e s       Single-quoted string   
QsciLexerPerl    S u b s t i t u i � � o       Substitution   
QsciLexerPerl   $ T a b e l a   d e   S � m b o l o s       Symbol table   
QsciLexerPerl    C o m e n t � r i o       Comment   QsciLexerPostScript    P a d r � o       Default   QsciLexerPostScript    P a l a v r a   C h a v e       Keyword   QsciLexerPostScript    N � m e r o       Number   QsciLexerPostScript   
 T e x t o       Text   QsciLexerPostScript    A t r i b u i � � o       
Assignment   QsciLexerProperties    C o m e n t � r i o       Comment   QsciLexerProperties    P a d r � o       Default   QsciLexerProperties    V a l o r   P a d r � o       
Default value   QsciLexerProperties   
 S e � � o       Section   QsciLexerProperties    N o m e   d a   c l a s s e       
Class name   QsciLexerPython    C o m e n t � r i o       Comment   QsciLexerPython   ( B l o c o   d e   c o m e n t � r i o s       
Comment block   QsciLexerPython    P a d r � o       Default   QsciLexerPython   ^ C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   a s p a s   d u p l a s       Double-quoted string   QsciLexerPython   0 N o m e   d a   f u n � � o   o u   m � t o d o       Function or method name   QsciLexerPython    I d e n t i f i c a d o r       
Identifier   QsciLexerPython    P a l a v r a   C h a v e       Keyword   QsciLexerPython    N � m e r o       Number   QsciLexerPython    O p e r a d o r       Operator   QsciLexerPython   ` C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   a s p a s   s i m p l e s       Single-quoted string   QsciLexerPython   h C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   t r � s   a s p a s   d u p l a s       Triple double-quoted string   QsciLexerPython   j C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   t r � s   a s p a s   s i m p l e s       Triple single-quoted string   QsciLexerPython   @ C a d e i a   d e   c a r a c t e r e s   n � o   f e c h a d a       Unclosed string   QsciLexerPython     A s p a s   I n v e r t i d a s       	Backticks   
QsciLexerRuby    N o m e   d a   c l a s s e       
Class name   
QsciLexerRuby    C o m e n t � r i o       Comment   
QsciLexerRuby    S e � � o   d e   d a d o s       Data section   
QsciLexerRuby    P a d r � o       Default   
QsciLexerRuby   ^ C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   a s p a s   d u p l a s       Double-quoted string   
QsciLexerRuby   0 N o m e   d a   f u n � � o   o u   m � t o d o       Function or method name   
QsciLexerRuby    I d e n t i f i c a d o r       
Identifier   
QsciLexerRuby    P a l a v r a   C h a v e       Keyword   
QsciLexerRuby    N � m e r o       Number   
QsciLexerRuby    O p e r a d o r       Operator   
QsciLexerRuby    P O D       POD   
QsciLexerRuby   " E x p r e s s � o   R e g u l a r       Regular expression   
QsciLexerRuby   ` C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   a s p a s   s i m p l e s       Single-quoted string   
QsciLexerRuby    S � m b o l o       Symbol   
QsciLexerRuby   8 C o m e n t � r i o   d e   l i n h a   u s a n d o   #       # comment line   QsciLexerSQL    C o m e n t � r i o       Comment   QsciLexerSQL   & C o m e n t � r i o   d e   L i n h a       Comment line   QsciLexerSQL    P a d r � o       Default   QsciLexerSQL   ^ C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   a s p a s   d u p l a s       Double-quoted string   QsciLexerSQL    I d e n t i f i c a d o r       
Identifier   QsciLexerSQL   * P a l a v r a   c h a v e   J a v a D o c       JavaDoc keyword   QsciLexerSQL   @ E r r o   d e   p a l a v r a   c h a v e   d o   J a v a D o c       JavaDoc keyword error   QsciLexerSQL   2 C o m e n t � r i o   e s t i l o   J a v a D o c       JavaDoc style comment   QsciLexerSQL    P a l a v r a   C h a v e       Keyword   QsciLexerSQL    N � m e r o       Number   QsciLexerSQL    O p e r a d o r       Operator   QsciLexerSQL   , C o m e n t � r i o   d o   S Q L * P l u s       SQL*Plus comment   QsciLexerSQL   2 P a l a v r a   c h a v e   d o   S Q L * P l u s       SQL*Plus keyword   QsciLexerSQL   $ P r o m p t   d o   S Q L * P l u s       SQL*Plus prompt   QsciLexerSQL   ` C a d e i a   d e   c a r a c t e r e s   e n v o l v i d a   p o r   a s p a s   s i m p l e s       Single-quoted string   QsciLexerSQL   , D e f i n i � � o   d e   u s u � r i o   1       User defined 1   QsciLexerSQL   , D e f i n i � � o   d e   u s u � r i o   2       User defined 2   QsciLexerSQL   , D e f i n i � � o   d e   u s u � r i o   3       User defined 3   QsciLexerSQL   , D e f i n i � � o   d e   u s u � r i o   4       User defined 4   QsciLexerSQL    C o m a n d o       Command   QsciLexerSpice    C o m e n t � r i o       Comment   QsciLexerSpice    P a d r � o       Default   QsciLexerSpice    I d e n t i f i c a d o r       
Identifier   QsciLexerSpice    N � m e r o       Number   QsciLexerSpice   
 V a l o r       Value   QsciLexerSpice    C o m e n t � r i o       Comment   QsciLexerTCL   ( B l o c o   d e   c o m e n t � r i o s       
Comment block   QsciLexerTCL    P a d r � o       Default   QsciLexerTCL    I d e n t i f i c a d o r       
Identifier   QsciLexerTCL    N � m e r o       Number   QsciLexerTCL    O p e r a d o r       Operator   QsciLexerTCL    S u b s t i t u i � � o       Substitution   QsciLexerTCL   , D e f i n i � � o   d e   u s u � r i o   1       User defined 1   QsciLexerTCL   , D e f i n i � � o   d e   u s u � r i o   2       User defined 2   QsciLexerTCL   , D e f i n i � � o   d e   u s u � r i o   3       User defined 3   QsciLexerTCL   , D e f i n i � � o   d e   u s u � r i o   4       User defined 4   QsciLexerTCL    C o m a n d o       Command   QsciLexerTeX    P a d r � o       Default   QsciLexerTeX   
 G r u p o       Group   QsciLexerTeX    E s p e c i a l       Special   QsciLexerTeX    S � m b o l o       Symbol   QsciLexerTeX   
 T e x t o       Text   QsciLexerTeX    A t r i b u t o       	Attribute   
QsciLexerVHDL    C o m e n t � r i o       Comment   
QsciLexerVHDL   ( B l o c o   d e   c o m e n t � r i o s       
Comment block   
QsciLexerVHDL    P a d r � o       Default   
QsciLexerVHDL    I d e n t i f i c a d o r       
Identifier   
QsciLexerVHDL    P a l a v r a   C h a v e       Keyword   
QsciLexerVHDL    N � m e r o       Number   
QsciLexerVHDL    O p e r a d o r       Operator   
QsciLexerVHDL   ( C a d e i a   d e   C a r a c t e r e s       String   
QsciLexerVHDL   @ C a d e i a   d e   c a r a c t e r e s   n � o   f e c h a d a       Unclosed string   
QsciLexerVHDL    C o m e n t � r i o       Comment   QsciLexerVerilog    P a d r � o       Default   QsciLexerVerilog    I d e n t i f i c a d o r       
Identifier   QsciLexerVerilog    C o m e n t a r   L i n h a       Line comment   QsciLexerVerilog    N � m e r o       Number   QsciLexerVerilog    O p e r a d o r       Operator   QsciLexerVerilog   X I d e n t i f i c a d o r e s   e   p a l a v r a s   c h a v e   s e c u n d � r i a s       "Secondary keywords and identifiers   QsciLexerVerilog   ( C a d e i a   d e   C a r a c t e r e s       String   QsciLexerVerilog   @ C a d e i a   d e   c a r a c t e r e s   n � o   f e c h a d a       Unclosed string   QsciLexerVerilog    C o m e n t � r i o       Comment   
QsciLexerYAML    P a d r � o       Default   
QsciLexerYAML    I d e n t i f i c a d o r       
Identifier   
QsciLexerYAML    P a l a v r a   C h a v e       Keyword   
QsciLexerYAML    N � m e r o       Number   
QsciLexerYAML    O p e r a d o r       Operator   
QsciLexerYAML