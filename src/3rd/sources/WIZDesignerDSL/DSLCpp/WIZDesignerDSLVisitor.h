
// Generated from WIZDesignerDSL.g4 by ANTLR 4.13.2

#pragma once


#include "antlr4-runtime.h"
#include "WIZDesignerDSLParser.h"



/**
 * This class defines an abstract visitor for a parse tree
 * produced by WIZDesignerDSLParser.
 */
class  WIZDesignerDSLVisitor : public antlr4::tree::AbstractParseTreeVisitor {
public:

  /**
   * Visit parse trees produced by WIZDesignerDSLParser.
   */
    virtual std::any visitProgram(WIZDesignerDSLParser::ProgramContext *context) = 0;

    virtual std::any visitStatementList(WIZDesignerDSLParser::StatementListContext *context) = 0;

    virtual std::any visitStatementListWithContinueBreak(WIZDesignerDSLParser::StatementListWithContinueBreakContext *context) = 0;

    virtual std::any visitStatement(WIZDesignerDSLParser::StatementContext *context) = 0;

    virtual std::any visitBreakWord(WIZDesignerDSLParser::BreakWordContext *context) = 0;

    virtual std::any visitContinueWord(WIZDesignerDSLParser::ContinueWordContext *context) = 0;

    virtual std::any visitNormalStatement(WIZDesignerDSLParser::NormalStatementContext *context) = 0;

    virtual std::any visitCommand(WIZDesignerDSLParser::CommandContext *context) = 0;

    virtual std::any visitGlobalVariableAssign(WIZDesignerDSLParser::GlobalVariableAssignContext *context) = 0;

    virtual std::any visitGlobalVariableDefine(WIZDesignerDSLParser::GlobalVariableDefineContext *context) = 0;

    virtual std::any visitLocalVariableAssign(WIZDesignerDSLParser::LocalVariableAssignContext *context) = 0;

    virtual std::any visitLocalVariableDefine(WIZDesignerDSLParser::LocalVariableDefineContext *context) = 0;

    virtual std::any visitFunctionDefine(WIZDesignerDSLParser::FunctionDefineContext *context) = 0;

    virtual std::any visitIfExpr(WIZDesignerDSLParser::IfExprContext *context) = 0;

    virtual std::any visitForExpr(WIZDesignerDSLParser::ForExprContext *context) = 0;

    virtual std::any visitForInExpr(WIZDesignerDSLParser::ForInExprContext *context) = 0;

    virtual std::any visitWhileExpr(WIZDesignerDSLParser::WhileExprContext *context) = 0;

    virtual std::any visitElseIfExpr(WIZDesignerDSLParser::ElseIfExprContext *context) = 0;

    virtual std::any visitExpressionToAtom(WIZDesignerDSLParser::ExpressionToAtomContext *context) = 0;

    virtual std::any visitComparisonExpr(WIZDesignerDSLParser::ComparisonExprContext *context) = 0;

    virtual std::any visitLogicalNotExpr(WIZDesignerDSLParser::LogicalNotExprContext *context) = 0;

    virtual std::any visitLogicalAndOrExpr(WIZDesignerDSLParser::LogicalAndOrExprContext *context) = 0;

    virtual std::any visitConditionalOperatorExpr(WIZDesignerDSLParser::ConditionalOperatorExprContext *context) = 0;

    virtual std::any visitActionTypeWith2Expr(WIZDesignerDSLParser::ActionTypeWith2ExprContext *context) = 0;

    virtual std::any visitMathPlusMinusExpr(WIZDesignerDSLParser::MathPlusMinusExprContext *context) = 0;

    virtual std::any visitRangeTakeBracketsExpr(WIZDesignerDSLParser::RangeTakeBracketsExprContext *context) = 0;

    virtual std::any visitTakeBracketsExpr(WIZDesignerDSLParser::TakeBracketsExprContext *context) = 0;

    virtual std::any visitBracketsExpr(WIZDesignerDSLParser::BracketsExprContext *context) = 0;

    virtual std::any visitAttrIndexOfExpr(WIZDesignerDSLParser::AttrIndexOfExprContext *context) = 0;

    virtual std::any visitGlobalVariableExpr(WIZDesignerDSLParser::GlobalVariableExprContext *context) = 0;

    virtual std::any visitUnitConvertExpr(WIZDesignerDSLParser::UnitConvertExprContext *context) = 0;

    virtual std::any visitNegateExpr(WIZDesignerDSLParser::NegateExprContext *context) = 0;

    virtual std::any visitAttrNumOfExpr(WIZDesignerDSLParser::AttrNumOfExprContext *context) = 0;

    virtual std::any visitParamAttrIndexOfExpr(WIZDesignerDSLParser::ParamAttrIndexOfExprContext *context) = 0;

    virtual std::any visitActionTypeWith1Expr(WIZDesignerDSLParser::ActionTypeWith1ExprContext *context) = 0;

    virtual std::any visitSpecialVariableExpr(WIZDesignerDSLParser::SpecialVariableExprContext *context) = 0;

    virtual std::any visitMathRemExpr(WIZDesignerDSLParser::MathRemExprContext *context) = 0;

    virtual std::any visitAttrOfExpr(WIZDesignerDSLParser::AttrOfExprContext *context) = 0;

    virtual std::any visitMathMulDivExpr(WIZDesignerDSLParser::MathMulDivExprContext *context) = 0;

    virtual std::any visitParenthesesExpr(WIZDesignerDSLParser::ParenthesesExprContext *context) = 0;

    virtual std::any visitFactorExpr(WIZDesignerDSLParser::FactorExprContext *context) = 0;

    virtual std::any visitFactor(WIZDesignerDSLParser::FactorContext *context) = 0;

    virtual std::any visitFunctionCall(WIZDesignerDSLParser::FunctionCallContext *context) = 0;

    virtual std::any visitParamList(WIZDesignerDSLParser::ParamListContext *context) = 0;

    virtual std::any visitArgumentList(WIZDesignerDSLParser::ArgumentListContext *context) = 0;

    virtual std::any visitVariable(WIZDesignerDSLParser::VariableContext *context) = 0;

    virtual std::any visitConstant(WIZDesignerDSLParser::ConstantContext *context) = 0;

    virtual std::any visitStringLiteral(WIZDesignerDSLParser::StringLiteralContext *context) = 0;

    virtual std::any visitFloatLiteral(WIZDesignerDSLParser::FloatLiteralContext *context) = 0;

    virtual std::any visitDecimalLiteral(WIZDesignerDSLParser::DecimalLiteralContext *context) = 0;

    virtual std::any visitBooleanLiteral(WIZDesignerDSLParser::BooleanLiteralContext *context) = 0;

    virtual std::any visitSpecialVariable(WIZDesignerDSLParser::SpecialVariableContext *context) = 0;

    virtual std::any visitAttrRPRO(WIZDesignerDSLParser::AttrRPROContext *context) = 0;

    virtual std::any visitArrayTypeName(WIZDesignerDSLParser::ArrayTypeNameContext *context) = 0;

    virtual std::any visitActionTypeWith1(WIZDesignerDSLParser::ActionTypeWith1Context *context) = 0;

    virtual std::any visitActionTWICE(WIZDesignerDSLParser::ActionTWICEContext *context) = 0;

    virtual std::any visitActionTypeWith2(WIZDesignerDSLParser::ActionTypeWith2Context *context) = 0;

    virtual std::any visitActionDIFFERENCE(WIZDesignerDSLParser::ActionDIFFERENCEContext *context) = 0;

    virtual std::any visitActionTANF(WIZDesignerDSLParser::ActionTANFContext *context) = 0;

    virtual std::any visitActionSUM(WIZDesignerDSLParser::ActionSUMContext *context) = 0;

    virtual std::any visitAttrName(WIZDesignerDSLParser::AttrNameContext *context) = 0;


};

