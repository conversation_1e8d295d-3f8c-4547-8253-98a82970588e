if defined CI (
    if not defined CI_COMMIT_TAG (
        echo "No CI_COMMIT_TAG"
        set CI_COMMIT_TAG=%CI_COMMIT_REF_SLUG%-%CI_COMMIT_SHORT_SHA%
    )
    set PackageName=WIZDesigner-%CI_COMMIT_TAG%-Windows-x64
) else (
    set PackageName=WIZDesigner-CustomBuild-Windows-x64
)

if not defined Qt5_DIR (
    echo "No Qt5_DIR"
    set Qt5_DIR=D:/Qt/Qt5.14.2/5.14.2/msvc2017_64
)

set BaseDir=%~dp0
set BaseDir=%BaseDir:\=/%
set QTIFWDIR=%BaseDir%/bin/IFW/Windows-x64

cd %BaseDir%

echo cmake -G "Visual Studio 17 2022" -A x64 -S. -Bbuild/Win64 -DCMAKE_SYSTEM_VERSION=10.0 -DCHECK_DEPENDS_BUILD=OFF -DPACKAGE_NAME=%PackageName% -DCMAKE_PREFIX_PATH="%Qt5_DIR%"
cmake -G "Visual Studio 17 2022" -A x64 -S. -Bbuild/Win64 -DCMAKE_SYSTEM_VERSION=10.0 -DCHECK_DEPENDS_BUILD=OFF -DPACKAGE_NAME=%PackageName% -DCMAKE_PREFIX_PATH="%Qt5_DIR%"

echo cmake --build build/Win64 --config Release --parallel 4
cmake --build build/Win64 --config Release --parallel 4

cd build/Win64
echo cpack -G ZIP -C Release
cpack -G ZIP -C Release
cd ../..

if defined CI (
    cd package
    echo copy /Y %PackageName%.zip "D:/wiz_player/WIZDesigner"
    copy /Y %PackageName%.zip "D:/wiz_player/WIZDesigner"
    cd ..
)
